## isoss 集成 siem

### isoss-ui 为主应用，菜单管理中添加 siem 的菜单，需要设置路由地址为 siem 菜单对应的路由地址；组件路径设置“siem/index" 详细引入和配置查阅 isoss-ui 中的说明

### outpost-web 为子应用，使用 wujie 微前端框架。在项目中使用 window.$wujie 和vue中的全局变量$wujie 区分是否为 isoss 内置的子应用。

### siem 独立部署版/isoss 集成的 siem 不同点如下

- 集成的自应用，在 isoss 菜单对应的页面，路由的 path 需要改造，需要适配主应用的路由数据结构，因此，修改路由配置后要检查有关路由地址的判断和跳转是否有影响
- 集成的子应用不需要登陆页面，不需要查询授权接口
- 集成的子应用内 layout.vue 不同，没有菜单和页签等，只有内容区
- ui 样式和细节，根据$wujie 进行区分,并根据运行环境传入的变量，应用 style3 样式
- 子应用的表格组件未使用 tooltip，定位有问题
- 子应用未引入全局 echarts
- 子应用的路由前缀和打包路径为‘/isoss-siem/’
- 子应用的运行和打包命令，查看 package.json 的 isoss 相关命令
- 子应用的改造大部分是屏蔽了全局接口的调用，可在全项目中搜索$wujie，筛选差异
- 子应用的环境配置文件为.evn.isossSiem

### 开发注意

- 全局的功能改造，需要注意区分子应用是否需要，添加适当的判断
- 作为子应用，目前只提供给 isoss 使用，未扩展其他主应用，变量等未提取，后续有另外的主应用再扩展
- 发布部署时，子应用的打包文件应在主应用文件夹内，并配置合理的 nginx

### 20230327 新增 soss3.0 的 ui//todo 需要修改

- 新增 soss3.0 的 ui，根据$wujie进行区分，获取isoss主应用设置的全局变量window.parent.$websiteStyle == '2' 为 soss3.0 的 ui
- soss3.0 风格为前哨，但作为 isoss 的子应用和前哨有细微调整，为前哨+isoss 的混合版，先应用 isoss 集成的样式，再使用前哨样式进行覆盖
- 主要修改的地方有两个 1.app.vue 中设置 outpostStyle4,用来覆盖样式 2.main.js 中设置主题色和引入不同的 element-ui 样式文件
- 主应用中替换了$color的值，因为$color 是 vite.config.js 中预设值的，无法再次覆盖，因此在主应用中进行了替换

### todo 上线完成后合并代码，其他的需要保持一致，也就是前哨版本需要添加一些冗余代码

### siem 子应用差异化的文件（14（全局查询$wujie 的文件）+3（scss）+ 1(md)）

1. 基本文件（11）

- index.html 不现实 loading
- main.js 处理子应用，添加全局变量$wujie，加载适配 isoss 的样式，echarts 不注册为全局变量
- App.vue 不需要授权
- request.js 登陆信息过期后通知父页面跳转到登陆页
- router/index.js 路由不需要认证权限，不需要进度条
- router/modules/index.js 筛选出 siem 相关的路由模块 （注：要求 siem 相关模块以 siem 开头命名文件名）
- store/index.js changeAllRouters 不需要,直接返回，因为菜单权限在父应用中控制
- getToken.js 获取父应用在缓存中的 token
- .env.isossSiemDev 开发环境变量配置
- .env.isossSiem 生产环境变量配置
- layout/index.vue 布局文件，只有内容区
- xelSelectTree.vue 内部处理 click 事件

2. ui 文件（3）

- coverStyle3.scss 子应用的样式，覆盖默认样式
- element-variables-style3.scss 子应用的 element-ui 样式
- variable-style3.scss 子应用的变量

3. 业务文件（5）

- commonSearch.vue 通用搜索组件，修改文字显示和 label 位置
- xelTable.vue 表格组件不显示 tooltip
- siemSearch/components/search.vue 处理 popover 的位置
- editCondition.vue 处理 popover 的位置
- conditionResult.vue 处理 popover 的位置

### 合并新功能代码的详细步骤

1. 运行 isoss-ui , 运行 outpost-web：npm run devIsossSiem, outpost-web 需要在 3000 端口
2. isoss-ui 需要有对应的菜单，菜单配置规则： 组件路径：siem/index 路由地址：outpost-web 项目内真实的路由地址。这里需要注意二级菜单的路由地址，受到主应用一级菜单的影响，可以修改 outpost-web 内的路由 path 为绝对路径以匹配。
3. 合并代码，原则上 isoss 相关的分支和主分支没有差异，内部通过$wujie 进行区分，做了兼容处理，不会影响主版本处理。因此理想状态下可以直接合并。（暂时不采用）
4. 合并代码，如果特殊情况，两个分支的代码差异化很大，可以使用主版本的代码，拉取合并用的新分支，再参考 siem 子应用差异化的文件,手动进行修改。可借助代码比较工具。合并完成后比较新分支和旧版isoss集成的分支，对比是否有不同。一定注意不要遗漏。
5. 合并完成后，点击菜单查看是否正常显示，需要特别注意页面右边缘的 popover 的位置是否正确
6. 测试功能是否正常
