module.exports = {
  env: {
    browser: true,
    es2021: true,
    commonjs: true,
    es6: true,
    node: true,
  },
  extends: ["eslint:recommended", "plugin:vue/vue3-essential", "prettier"],
  plugins: ["vue", "prettier"],
  parser: "vue-eslint-parser",
  globals: {
    defineProps: "readonly",
    defineEmits: "readonly",
    defineExpose: "readonly",
    withDefaults: "readonly",
    $globalWindowSize:'writable'
  },
  rules: {
    "no-console": "off",
    "no-debugger": "off",
    "no-unused-vars": ["off", { vars: "local", args: "none" }],
    "vue/no-v-html": "off",
    "vue/require-component-is": "off",
    "vue/no-unused-components": "off",
  },
};
