export default {
  mounted(el, binding, vnode) {
    el.addEventListener("click", () => {
      if (el.disabled) return;
      window.$clickBtn = el;

      let clearTime = setTimeout(() => {
        if (!window.$requesting) {
          window.$clickBtn = null;
        }
      }, 200);
    });
  },
};

window.$requesting = false;

export function requestAddBtnDisabled() {
  if (window.$clickBtn) {
    if (window.$clickBtn.querySelector(".el-icon-loading")) return;
    window.$clickBtn.disabled = true;
    window.$clickBtn.classList.add("is-loading");
    window.$clickBtn.insertAdjacentHTML("afterbegin", '<i class="el-icon-loading"></i>');
    window.$requesting = true;
  }
}

export function responseRemoveBtnDisabled() {
  if (window.$clickBtn) {
    window.$clickBtn.disabled = false;
    window.$clickBtn.classList.remove("is-loading");

    let iDom = window.$clickBtn.querySelector(".el-icon-loading");
    if (iDom) {
      iDom.remove();
    }
    window.$requesting = false;
  }
}
