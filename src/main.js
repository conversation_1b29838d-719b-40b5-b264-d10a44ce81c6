if (window.location.href.includes("screen.html")) {
  window.location.href = window.location.origin + "/screen.html?name=" + window.location.href.split("screen.html/")[1];
}
// import './assets/style/variable-style3.scss'
import "minireset.css";
import "./assets/style/public.scss";
import "./assets/style/media.scss";
import "nprogress/nprogress.css";

import "vue3-treeselect/dist/vue3-treeselect.css";
import "cropperjs/dist/cropper.css";
import "./assets/fonts/aliFont.js";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { createApp } from "vue";
import App from "./App.vue";

import "./assets/style/vueDraggableResizable.scss";
//router
import router from "./router";

//vuex
import store from "./store";

//element-ui
import ElementPlus from "element-plus";

if (import.meta.env.VITE_STYLE2) {
  import("./assets/style/element-variables-style2.scss");
} else if (import.meta.env.VITE_STYLE3) {
  if (window.$wujie) {
    if (window.parent.$websiteStyle == "2") {
      import("./assets/style/element-variables.scss");
    } else {
      import("./assets/style/element-variables-style3.scss");
    }
  }
} else {
  import("./assets/style/element-variables.scss");
}

const app = createApp(App);
// siem字段值显示\\n
app.config.globalProperties.$globalShowOriginStr = function (s) {
  const str = typeof s == "string" ? s.replace(/\\/g, "\\\\") : s;

  return str;
};
//组全局件注册
import { setGloabelComponents } from "@/components";
setGloabelComponents(app);
import { setXelComponents } from "@/xelComponents";
setXelComponents(app);

import * as echarts from "echarts";
import "echarts-gl";
import "echarts-liquidfill"; // 引入liquidfill

//自定义指令
import setDirective from "@/directive";
setDirective(app);

//element-plus 字体图标注册为全局组件
import icons from "@/components/plusIcon.js";
Object.values(icons).forEach((item, index) => {
  app.component(item.name, item); // 读取出文件中的default模块
});

//全局主题色
app.config.globalProperties.$themeColor = import.meta.env.VITE_COLOR;

if (window.$wujie) {
  if (window.parent.$websiteStyle == "2") {
    app.config.globalProperties.$themeColor = "#ef8936";
  } else {
    document.body.classList.add("isoss-child-app-theme");
  }
}

//取消el-button的选中状态
import "@/utils/blurFocusBtn";

//响应式windowWidth
import "@/utils/windowSize";
app.config.globalProperties.$globalWindowSize = window.$globalWindowSize;
//条件判断import sime独立，系统管理需要引入的全局变量
if (import.meta.env.VITE_IS_SIME) {
  import("./simeGlobal").then((simeGlobal) => {
    simeGlobal.default(app);
  });
}

//图表库组件
import starsoComponents from "starso/setEchartOptions";
import "starso-components-ui/lib/style.css";
import EchartComponent from "starso/echartComponent";
import editEchartList from "starso/editEchartList";
import echartOptions from "@/config/echartOptions";

//不同样式版本 覆盖样式
import "./assets/style/coverStyle2.scss";
import "./assets/style/coverStyle3.scss";
import "./assets/style/coverStyle4.scss";

// app.use(store).use(ElementPlus, { size: "small", locale: zhCn }).use(VueDraggableResizable);
app.use(store).use(ElementPlus, { size: "small", locale: zhCn });

// if (!window.$wujie) {
app.config.globalProperties.$echarts = echarts;

app.use(starsoComponents, echartOptions);
app.component("echartComponent", EchartComponent);
app.component("editEchartList", editEchartList);
// }

import { getLicenseInfo } from "@/api/system/license";

//验证是否有授权
//前哨，siem都展示
if (!window.$wujie) {
  getLicenseInfo().then((res) => {
    window.versionNo = res.data.versionNo;
    if (res.data.login_code) window.licenseInfo = res.data;
    if (!res.data.login_code && window.location.href.includes("licensePage")) {
      window.location.href = window.location.origin + "/login";
    }

    app.use(router).mount("#app");
  });
} else {
  app.use(router).mount("#app");
}
// 子应用body添加class
ifChildApp();
function ifChildApp() {
  if (window.$wujie) {
    app.config.globalProperties.$wujie = true; //全局的子应用标识
    document.body.classList.add("wujie-child-app");
  }
}

adjustDatePopperPos();
//isoss集成的规则编辑器调整位置
function adjustDatePopperPos() {
  if (window.$wujie) {
    document.querySelector(".wujie-child-app").addEventListener("mouseup", (e) => {
      setTimeout(() => {
        const elPickerPoppers = document.querySelectorAll(".wujie-child-app .el-picker__popper");
        for (const elPickerPopper of elPickerPoppers) {
          if (window.getComputedStyle(elPickerPopper).display === "block") {
            const bodyDom = document.querySelector(".wujie-child-app");

            //获取elPickerPopper距离bodyDom的右侧和上部距离
            let { left, top, width } = elPickerPopper.getBoundingClientRect();
            top = top - 100;
            if (left + width > bodyDom.clientWidth) {
              elPickerPopper.style.left = bodyDom.clientWidth - width + "px";
            }
            if (top < 0) {
              elPickerPopper.style.top = "0px";
            }

            break;
          }
        }
      }, 200);
    });
  }
}
