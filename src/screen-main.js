import "minireset.css";
import "./assets/style/screen.scss";
import "./assets/style/public.scss";

import zhCn from "element-plus/es/locale/lang/zh-cn";
import { createApp } from "vue";
import App from "./screen-App.vue";

import * as echarts from "echarts"; //引入echarts
import "echarts-gl";
import "echarts-liquidfill"; // 引入liquidfill
import Emitter from "tiny-emitter"; //替换vue2 eventBus
// 大屏登录存取token
import { refreshToken } from "@/api/login";
setInterval(() => {
  refreshToken();
}, 40000000);
//router
import router from "./router/screen";

import starsoComponents from "starso/setEchartOptions";
import "starso-components-ui/lib/style.css";
import echartOptions from "@/config/screen-echartOptions";

//element-ui
import ElementPlus from "element-plus";
import "./assets/style/element-variables.scss";

const app = createApp(App);
// console.info(app)
app.config.globalProperties.$echarts = echarts; //全局使用

const emitter = new Emitter();
app.config.globalProperties.$emitter = emitter;

app.use(router).use(ElementPlus, { size: "small", locale: zhCn }).use(starsoComponents, echartOptions).mount("#appScreen");
