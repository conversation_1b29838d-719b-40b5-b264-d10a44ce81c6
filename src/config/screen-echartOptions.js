// 调色板
// let colors = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"];

// 标题是否显示，目前都不可见
let title = {
  show: false,
};

let xAxis = {
  type: "category",
  axisLine: {
    show: true,
    lineStyle: {
      color: "#163e47",
    },
  },
  axisLabel: {
    show: true,
    textStyle: {
      color: "#fff",
    },
  },
};

let yAxis = {
  nameGap: 20,

  axisLabel: {
    show: true,
    padding: [0, 10, 0, 0],
    textStyle: {
      color: "#fff",
    },
  },
  splitLine: {
    show: true, // X轴线 颜色类型的修改
    lineStyle: {
      color: "#163e47",
    },
  },
};
//series相关
// 分为3个对象：barItemStyle,lineItemStyle.pieItemStyle
let barItemStyle = {
  barWidth: 10, //粗细
};

// 线图
let lineItemStyle = {
  smooth: true,
  symbol: "none",
};
let grid = {
  left: "35px",
  top: "10px",
  right: "0",
  bottom: "20px",
};

//无数据显示
let noDataInfo = {
  text: "暂无数据",
};

export default {
  // colors,
  title,
  grid,
  barItemStyle,
  lineItemStyle,
  // mapItemStyle,
  noDataInfo,
  xAxis,
  yAxis,
};
