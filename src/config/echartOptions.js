// 调色板
// let colors = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"];

// 标题是否显示，目前都不可见
let title = {
  show: false,
};

//series相关
// 分为3个对象：barItemStyle,lineItemStyle.pieItemStyle
let barItemStyle = {
  barWidth: 10, //粗细
};

// 线图
let lineItemStyle = {};

let grid = {
  left: "15%",
  top: "15%",
  right: "5%",
  bottom: "15%",
};

//无数据显示
let noDataInfo = {
  text: "暂无数据",
};

export default {
  // colors,
  title,
  grid,
  barItemStyle,
  lineItemStyle,
  // mapItemStyle,
  noDataInfo,
};
