<template>
  <router-view />
</template>
<script setup>
import { onMounted } from "vue";
import firstToUpper from "@/utils/firstToUpper.js";
onMounted(() => {
  document.getElementById("pageLoading").remove();
});

import { useRouter, useRoute } from "vue-router";

const router = useRouter();

const route = useRoute();
let routeName = window.location.href.split("=")[1];
console.info(window.location.href.split("="));
if (routeName) {
  router.push({ name: firstToUpper(routeName) });
}
</script>

<style scoped lang="scss"></style>
