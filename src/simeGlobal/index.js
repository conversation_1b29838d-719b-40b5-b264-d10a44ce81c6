import "@/assets/style/sime/index.scss"; // global css
import { download } from "@/plugins/request";
import { useDict } from "@/utils/dict";
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel } from "@/utils/ruoyi2";
// 分页组件
import Pagination from "@/components/sime/Pagination/index.vue";
// 自定义表格工具组件
import RightToolbar from "@/components/sime/RightToolbar/index.vue";
// 文件上传组件
import FileUpload from "@/components/sime/FileUpload/index.vue";
// 图片上传组件
import ImageUpload from "@/components/sime/ImageUpload/index.vue";
// 图片预览组件
import ImagePreview from "@/components/sime/ImagePreview/index.vue";
// 自定义树选择组件
import TreeSelect from "@/components/sime/TreeSelect/index.vue";
// 字典标签组件
import DictTag from "@/components/sime/DictTag/index.vue";
import modal from "@/simePlugins/modal";
export default function (app) {
  // 全局方法挂载
  app.config.globalProperties.$modal = modal;

  app.config.globalProperties.useDict = useDict;
  app.config.globalProperties.download = download;
  app.config.globalProperties.parseTime = parseTime;
  app.config.globalProperties.resetForm = resetForm;
  app.config.globalProperties.handleTree = handleTree;
  app.config.globalProperties.addDateRange = addDateRange;
  app.config.globalProperties.selectDictLabel = selectDictLabel;
  // 全局组件挂载
  app.component("DictTag", DictTag);
  app.component("Pagination", Pagination);
  app.component("TreeSelect", TreeSelect);
  app.component("FileUpload", FileUpload);
  app.component("ImageUpload", ImageUpload);
  app.component("ImagePreview", ImagePreview);
  app.component("RightToolbar", RightToolbar);
}
