#outpostStyle2 {
  .action-btns-ul {
    li {
      width: 38px;
      background: #fff;
      color: #c3c5c8;
      border: 1px solid #eae9ec;
      border-radius: 50%;
      margin-left: 6px;
      &:last-child {
        border: 1px solid #eae9ec;
      }
    }
  }
  .action-btns-ul li:not(.disabled):hover {
    background: #556ff6;
    color: #fff;
  }
  .tap-tubiao {
    background-color: #e8effd;
    color: #546ef5;
  }
  .layout-wrapper {
    &::before {
      display: none;
    }
    .aside-menu {
      background: $leftBarColor;
      border-radius: 0;
    }
    .main-content {
      .main-content-overflow {
        & > .el-card.is-always-shadow:first-child {
          border: 1px solid #eff3f5;
        }
      }
    }
    .breadcrumb {
      border: 1px solid #f1f2f5;
      background-color: #fff;
      box-shadow: none;
    }
  }
  .tabs-wrapper {
    a {
      border-radius: 6px;
      background-color: #fff;
      border: 1px solid #f0f1f3;
    }
  }
  .tabs-wrapper a.active,
  .tabs-wrapper a:hover {
    background-color: #556ff6;
    color: #f8f6f7;
  }
  .el-menu.asideMenu,
  .el-sub-menu__title {
    background: #11143e !important;
    color: #737595 !important;
  }
  .el-menu-item:hover,
  .el-sub-menu__title:hover {
    background: #11143e;
    color: #ddd !important;
  }
  ul.el-menu.asideMenu > li.is-active {
    // background: #292b4e;
    color: #ddd !important;
  }
  .el-menu-item {
    color: #737595;
  }
  .el-menu.asideMenu .el-menu .el-menu-item {
    background: #292b4e;
    &:hover {
      color: #ddd;
    }
    &.is-active {
      color: #fff !important;
      background: #3e4066;
    }
  }
  .asideMenu:not(.el-menu--collapse) {
    .el-sub-menu .el-menu {
      background: #292b4e;
    }
  }
  .sidebar-logo-container {
    background-color: #11143e;
  }
  .asset-top-ul {
    .top li {
      border: 1px solid #eff3f5;
    }
    .card {
      border: 1px solid #eff3f5;
    }
  }
}
