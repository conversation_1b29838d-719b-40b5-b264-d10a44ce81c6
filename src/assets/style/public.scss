@import "./base.scss";
//取消按钮，靠右
.but-right {
  float: right;
  margin: 16px 0 16px !important;
}
// 突出标题
.font-color {
  font-size: 16px;
  font-weight: 500;
}
// 模块标题
.modularTitle {
  font-size: 16px;
  font-weight: 400;
}
//分页样式
.xel-table-pagination {
  margin-top: 20px;
  text-align: right;
}
// 标注信息
.color-soft {
  color: #848484;
}
.color-rev {
  color: $colorRev;
}
//列表上按钮样式
.list-button-str {
  float: right;
  margin-bottom: 20px;
}
// 每页的标题
.conH3Tit {
  margin-top: 0;
  color: #262f3d;
  font-size: 16px;
  margin-bottom: 10px;
  &.bottom-line {
    border-bottom: 1px solid #ededed;
    padding-bottom: 22px;
    margin-bottom: 20px;
  }
}
// 搜索上的按钮
.table-handler-btns {
  text-align: right;
  margin-bottom: 28px;

  .el-button {
    &:focus,
    &:active,
    &:hover {
      color: #63729d;
      border-color: #c5daff;
      background-color: #eef5ff;
    }
    .el-icon {
      vertical-align: top;
    }
  }
}
/*表格操作列按钮组*/
.action-btns-ul {
  display: flex;
  flex-wrap: nowrap;
  border-radius: 4px;
  overflow: hidden;
  width: fit-content;
  li {
    width: 54px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #eaeaea;
    color: #858585;
    cursor: pointer;
    border-right: 1px solid #fff;
    transition: all 0.1s;
    flex-shrink: 0;
    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }
    &:last-child {
      border: none;
    }
    &:not(.disabled):hover {
      background: $color;
      color: #fff;
    }
  }
}
//一行展示多个的form表单
.flex-form {
  display: flex;
  flex-wrap: wrap;
}
.no-data {
  text-align: center;
  // margin: 10px 0;
  color: $fontColorSoft;
}

// 灰色背景的表格
.gray-table {
  background-color: #f8f6f7;
  .el-table__row {
    background: #f8f6f7;
  }
}
// 设置图标
.tap-tubiao {
  margin-top: 10px;
  width: 4.375rem;
  height: 4.375rem;
  background-color: $color;
  color: #fff;
  border-radius: $raduisM;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tap-title {
  margin-top: 10px;
  margin-bottom: 10px;
  color: $fontColorSoft;
  font-size: 14px;
  font-weight: 500;
}

.tap-number {
  color: #28334f;
  font-size: 36px;
  font-weight: 600;
}
body .el-tag--blue {
  --el-tag-font-color: #409eff;
  --el-tag-hover-color: #409eff;
  background-color: #ecf5ff;
  border-color: #ecf5ff;
  color: #409eff;
}

//页面内带下划线的的标题
.title-bottom-line {
  padding-bottom: 10px;
  border-bottom: 2px solid #ebedf1;
  margin-bottom: 15px;
  color: #000;
  font-size: 16px;
  & + section {
    margin-bottom: 40px;
  }
}
.dynamic-add {
  height: 36px;
  line-height: 36px;
  background: #f1f1f1;
  border-radius: 8px;
  color: #b4b4b4;
  text-align: center;
  transition: all 0.5s;
  .el-icon {
    vertical-align: text-top;
  }
  &:hover {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary-light-7);
    background-color: var(--el-color-primary-light-9);
  }
}

.el-button .el-icon {
  vertical-align: text-bottom;
  margin-right: 2px;
}

// 等级颜色sapn
.level-color {
  display: inline-block;
  width: 5px;
  height: 13px;
  border-radius: 5px;
}
//表格可单击的列
.xel-clickable {
  color: #3aa0ff;
  cursor: pointer;
}
/*合并事件任务列表*/
.event-task-list {
  li {
    margin-top: 20px;
  }
}
.event-task-title {
  background: #f8f6f7;
  line-height: 46px;
  color: #262f3d;
  padding: 0 20px;
  & + .event-task-title {
    margin-top: 10px;
  }
}
.event-task-item,
.el-checkbox.event-task-item {
  display: block;
  line-height: 60px;
  height: 60px !important;
  padding: 0 30px;
  border-bottom: 1px solid #ebedf1;
}
//弹框内引用的组件el-card样式清空
.no-border-card {
  border: none !important;
  .el-card__body {
    padding: 0 10px;
  }
}
// 工作台数量样式
.giveAn {
  font-size: 14px;
  font-weight: 300;
  color: #848484;
}
// 工作台数量
.quantity {
  font-size: 52px;
  font-weight: 600;
  color: #28334f;
}
.ishidden {
  .el-button {
    display: none;
  }
  .action-btns-ul {
    display: none;
  }
  .search-form-wrapper .el-button {
    display: inline-block;
  }
  // *:not(.search-form-wrapper) .el-button{
  //   display:none;
  // }
}
body {
  .base-info-box {
    .el-col-12 {
      display: flex;
      align-items: flex-end;
      .el-form-item {
        width: 100%;
        .el-form-item__content {
          word-break: break-all;
        }
      }
    }
  }
}
.inline-line {
  display: inline-block;
}
.break {
  word-break: break-all;
}
.el-popper {
  word-break: break-all;
}
.el-form-item__content {
  word-break: break-all;
}
.el-dialog .el-card__body {
  padding-top: 0;
}
.login-wrapper {
  width: 100vw;
  height: 100vh;
  background: url(@/assets/imgs/loginBg.png);
  padding-top: 23vh;
  padding-left: 10vw;
  background-size: 100% 100%;
  font-size: 34px;
  .system-title {
    color: #fff;
    font-size: 46px;
    transform: scale(1, 0.8);
    padding-left: 8px;
    margin-top: -20px;
    letter-spacing: 1px;
    font-weight: 500;
  }
}
.version-box {
  position: absolute;
  bottom: 20px;
  right: 20px;
  color: #eee;
  font-size: 14px;
}
.w-e-menu:nth-of-type(10) {
  display: none !important; //富文本编辑器隐藏图片图标
}

/*编辑图表样式start*/
.components-demo-wrapper {
  .type-menu {
    li {
      &.active {
        color: $colorRev;
        background: $bgColor;
        font-weight: bold;
      }
    }
  }
  &.is-com-wrapper {
    .demo-list li {
      &.active {
        box-shadow: 0 0 4px 1px $color;
      }
    }
  }
}
/*编辑图表样式end*/
