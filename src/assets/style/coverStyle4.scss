#isossApp3 {
  .p0-new {
    padding: 0 !important;
  }
  .p-12-16-new {
    padding: 12px 16px !important;
  }
  .pt0-new {
    padding-top: 0 !important;
  }
  .pb15-new {
    padding-bottom: 15px !important;
  }
  .px0-new {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .m0-new {
    margin: 0 !important;
  }

  .mt-10-new {
    margin-top: -10px !important;
  }

  .ml-10-new {
    margin-left: -10px !important;
  }

  .ml-15-new {
    margin-left: -15px !important;
  }

  .mr-15-new {
    margin-right: -15px !important;
  }

  .ml15-new {
    margin-left: 15px !important;
  }

  .mr-30-new {
    margin-right: -30px !important;
  }

  .mt15-new {
    margin-top: 15px !important;
  }

  .mb15-new {
    margin-bottom: 15px !important;
  }

  .mr20-new {
    margin-right: 20px !important;
  }

  /* el-tree */
  .tree-search-new {
    .el-tree {
      padding: 12px 16px;
      height: calc(-119px + 100vh);
      border-radius: 4px;
      box-shadow: var(--el-box-shadow-light);
    }
  }
  /* tab-pane 首个child样式调整 */
  .el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
  .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--top .el-tabs__item.is-top:nth-child(2),
  .el-tabs__item:nth-child(1) {
    padding-left: 20px !important;
  }
  .el-tabs {
    margin-top: 0;
    .el-tabs__header {
      background-color: #fff;
      padding: 5px 12px 5px;
      border-radius: 4px;
      box-shadow: var(--el-box-shadow-light);
      .el-tabs__active-bar:before {
        width: 12px;
        left: -12px;
      }
      .el-tabs__active-bar:after {
        width: 12px;
        right: -12px;
      }
      //区分两种
      .el-tabs__nav-wrap::after {
        width: 0px;
      }
    }
    &.show-border {
      .el-tabs__nav-wrap::after {
        width: 100%;
      }
    }
    &.el-tabs--left {
      .el-tabs__header.is-left {
        width: 100%;
      }
    }
  }

  /* 暂无数据 */
  .no-data {
    background: #fff;
    display: grid;
    place-items: center;
    min-height: 300px;
    margin: 0 -20px;
  }
  /* 背景 */
  .bg-new {
    background: #fff !important;
  }
  /* 卡片--背景色 */
  .el-card {
    background-color: #f6f7fb;
  }

  /* 查询 */
  .bg-p-border-new {
    background: #fff;
    padding: 16px 16px !important;
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
    .table-pagination-new {
      padding: 0 !important;
      margin: 0 !important;
    }
    .sime-layout-wrapper {
      .bg-p-border-new {
        padding: 0 0 0 10px !important;
      }
      .tree-search-new {
        .el-tree {
          padding: 0 !important;
        }
      }
      .table-pagination-new {
        padding: 0 0 0 10px !important;
        margin-top: 12px !important;
      }
    }
  }
  .siem-search-conH3Tit {
    padding: 0 !important;
  }
  /* 表格--分页 */
  .table-pagination-new {
    background: #fff;
    padding: 12px 16px 20px;
    margin-top: 12px;
  }
  // 操作列按钮
  .action-btns-ul {
    width: fit-content;

    li {
      width: 30px;
      height: 24px;
      cursor: pointer;
      color: rgb(255, 200, 20);
      display: flex;
      justify-content: center;
      align-items: center;
      white-space: nowrap;
      background: transparent;
      padding: 2px 6px;
      border-right: 1px solid #fff;

      &:last-child {
        border-right: none;
      }
    }

    &.action-btns-ul-text {
      li {
        background: transparent;
        padding: 0;
        border-right: none;

        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }

  .action-btns-ul {
    display: flex;
    border-radius: 80px;
    overflow: hidden;
  }

  /* simeLayout--条件 图标 */
  .filter-show-change {
    top: 15px;
  }

  .tap-tubiao {
    background-color: #e8effd;
    color: #546ef5;
  }

  .layout-wrapper {
    &::before {
      display: none;
    }

    .aside-menu {
      background: $leftBarColor;
      border-radius: 0;
    }

    .main-content {
      .main-content-overflow {
        & > .el-card.is-always-shadow:first-child {
          border: 1px solid #eff3f5;
        }
      }
    }

    .breadcrumb {
      border: 1px solid #f1f2f5;
      background-color: #fff;
      box-shadow: none;
    }
  }

  .tabs-wrapper {
    a {
      border-radius: 6px;
      background-color: #fff;
      border: 1px solid #f0f1f3;
    }
  }

  .tabs-wrapper a.active,
  .tabs-wrapper a:hover {
    background-color: var(--primary-color-sub);
    color: #f8f6f7;
    border-color: rgba(20, 78, 193, 1);
  }

  .el-menu.asideMenu,
  .el-sub-menu__title {
    background: #11143e !important;
    color: #737595 !important;
  }

  .el-menu-item:hover,
  .el-sub-menu__title:hover {
    background: #11143e;
    color: #ddd !important;
  }

  ul.el-menu.asideMenu > li.is-active {
    // background: #292b4e;
    color: #ddd !important;
  }

  .el-menu-item {
    color: #737595;
  }

  .el-menu.asideMenu .el-menu .el-menu-item {
    background: #292b4e;

    &:hover {
      color: #ddd;
    }

    &.is-active {
      color: #fff !important;
      background: #3e4066;
    }
  }

  .asideMenu:not(.el-menu--collapse) {
    .el-sub-menu .el-menu {
      background: #292b4e;
    }
  }

  .sidebar-logo-container {
    background-color: #11143e;
  }

  .asset-top-ul {
    .top li {
      border: 1px solid #eff3f5;
    }

    .card {
      border: 1px solid #eff3f5;
    }
  }
}

/*作为子应用嵌套的样式，style2的基础上再覆盖*/
body.wujie-child-app {
  width: 100%;
  min-height: calc(100vh - 120px);
  overflow-x: hidden;
  min-width: auto;
  position: relative;
}

body.wujie-child-app #isossApp3 {
  .el-picker__popper.el-popper {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12) !important;
  }

  .el-card {
    border: none;
  }

  // 页面布局
  & > .el-card.is-always-shadow {
    border: 0;
  }

  .el-table {
    --el-table-font-color: #262824;
  }
}

body #isossApp3 {
  .el-input__inner,
  .el-textarea__inner {
    background-color: #fff;
    // --el-color-white: #f6f7f6;
    --el-border-color-hover: #436fcc;
    box-shadow: none;
  }

  /*分页*/
  .el-pagination {
    height: 36px;
    color: var(--primary-color-sub);
    --el-pagination-font-size: 12px;
    padding-right: 10px;
  }

  .el-pagination .btn-next .el-icon,
  .el-pagination .btn-prev .el-icon {
    font-size: 16px;
  }

  .el-pagination .el-input__inner {
    --el-input-bg-color: #fff;
    background: #fff;
    color: var(--primary-color-sub);
    border: 1px solid var(--primary-color-sub);
    height: 32px;
    min-height: 32px;
  }

  .el-pagination .el-pagination__total {
    color: var(--primary-color-sub);
  }

  .el-pagination .btn-next,
  .el-pagination .btn-prev {
    color: var(--primary-color-sub);
    position: relative;
    z-index: 1;
    transform: scale(0.9);
    background: transparent;
  }

  .el-pagination .el-pager {
    position: relative;
    --el-pagination-button-width: 48px;
  }

  .el-pagination .el-pager li {
    position: relative;
    z-index: 1;
    height: 32px;
    line-height: 32px;
    border-radius: 0;
  }

  .el-pagination.is-background .el-pager li:not(.active) {
    background: transparent;
    color: var(--primary-color-sub);
  }

  .el-pagination .el-pager:before {
    display: block;
    content: "";
    position: absolute;
    width: calc(100% + 80px);
    left: -40px;
    top: -1px;
    bottom: -1px;
    background: transparent;
    border: 1px solid var(--primary-color-sub);
    border-radius: 18px;
  }

  .el-pager li.is-active {
    background: var(--primary-color-sub);
    color: #fff;
  }

  /*tab*/
  .el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
  .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--top .el-tabs__item.is-top:nth-child(2),
  .el-tabs__item {
    padding-left: 30px;
  }

  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:before,
  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:after {
    background: var(--primary-color-sub);
  }

  /*表单*/
  .el-form-item--default {
    --font-size: 12px;
  }

  .el-form-item__label {
    font-size: 12px;
    color: #4b4b4b;
  }

  .two_filter_list > .two_filter {
    background: #fff;
    border: 1px solid #eee;
  }

  .el-table .cell {
    min-width: 60px;
    // padding: 0 12px;
  }
  /* 表格上部操作按钮  */
  .table-handler-btns {
    .el-button {
      background: #ecf5ff7a;
      border: 1px solid #a0cfffc2 !important;
      color: #1d5bcc;
      margin-left: 0;
      margin-right: 10px;

      &:hover {
        box-shadow: 0px 0px 2px 2px #0f46a910;
        border-color: #a0cfff !important;
      }

      &:not(.filter-btn) {
        &.is-disabled {
          opacity: 0.5;
        }
      }

      &.filter-search-btn {
        background-color: #2b82e0;
        border-color: #2b82e0;
        color: #fff !important;

        &:hover {
          background: #3f9afc !important;
          border-color: #b8daff;
        }
      }
    }
  }
}

/* isoss两种风格都需要的改动 */
body.wujie-child-app #isossApp3 {
  .conH3Tit {
    display: none;
  }
  //星梭详情的详情标签页面
  .starshot-tab,
  .starshot-tab-40 {
    margin-top: 0;
    & > .el-card {
      padding-top: 10px;
    }
  }

  .siem-search-conH3Tit {
    display: block;

    & > .pull-left {
      display: none;
    }
  }

  .main-content-overflow {
    & .el-card > .el-card__body {
      padding: 0 0px !important;
    }
  }

  .el-form-item__label {
    font-size: 13px;
  }

  /*tab*/

  .el-tabs__nav:not(.is-left) .el-tabs__active-bar {
    bottom: 3px;
  }

  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:before,
  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:after {
    content: "";
    display: block;
    width: 30px;
    height: 2px;
    position: absolute;
    top: 0;

    left: -30px;
  }

  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:after {
    right: -30px;
    left: auto;
  }

  .el-checkbox__label {
    font-size: 12px;
  }

  .dialog-footer {
    justify-content: flex-end;
    text-align: right;
  }

  .search-form-wrapper .el-form-item--small .el-form-item__label {
    line-height: 22px;
    padding-bottom: 0px;
    display: inline-block;
    position: relative;

    &:after {
      content: "";
      display: inline-block;
      position: absolute;
      width: 6px;
      height: 14px;
      background: #fff;
      top: 4px;
      right: -3px;
    }
  }
  // 搜索表单：全部结果
  .formCon .el-form-item--small .el-form-item__label {
    &:after {
      right: -6px !important;
    }
  }
  .common-search-btns-item {
    display: flex;
    align-items: flex-end;
  }

  .siem-search-content-wrapper {
    padding: 0 10px;
  }

  .table-handler-btns {
    text-align: left;
    margin-bottom: 15px;
    overflow: hidden;

    .el-button {
      .el-icon {
        vertical-align: top;
      }

      &.filter-search-btn {
        float: right;
      }
    }
  }

  .jh {
    .el-input__inner {
      font-size: 12px;
    }
  }
  /* */
  .add_filter {
    background: none;
  }
  /* 二次筛选 */
  .newConComDiv {
    background: #fff !important;
  }
  .tree-search-new {
    max-width: 21.8333333333%;
    flex: 0 0 21.8333333333%;
  }
  .tree-wrapper.el-col-1 {
    max-width: 3.1666666667%;
    flex: 0 0 3.1666666667%;
    &:after {
      display: none;
    }
  }
  .card-new-gray {
    background: transparent;
    .el-tabs__item {
      padding-left: 20px !important;
    }
    // .split-area-new {
    // }
  }
  // .split-area-new {
  //   max-width: 1.5%;
  //   flex: 0 0 1.5%;
  //   & + .el-col-21 {
  //     padding-left: 10px !important;
  //     padding-right: 10px !important;
  //     max-width: 86% !important;
  //     flex: 0 0 86% !important;
  //     .action-btns-ul {
  //       min-width: 100px;
  //     }
  //   }
  // }
}
