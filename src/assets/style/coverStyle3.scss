.wujie-child-app {
  .el-picker__popper.el-popper[role="tooltip"] {
    border: 1px solid #eee !important;
  }
}
#outpostStyle3 {
  .condition-wrapper {
    background: #fdfdfd;
  }
  .action-btns-ul {
    padding-right: 18px;
    li {
      width: 28px;
      height: 28px;
      background: transparent;
      color: #ffb907;
      border: 1px solid transparent;
      border-radius: 6px;
      margin-left: 6px;
      &:first-child {
        margin-left: 0;
      }
      &:last-child {
        border: 1px solid transparent;
      }
    }
  }
  .action-btns-ul li:not(.disabled):hover {
    background: #fff7db;
    color: #ffd439;
  }
  .tap-tubiao {
    background-color: #e8effd;
    color: #546ef5;
  }
  .layout-wrapper {
    &::before {
      display: none;
    }
    .aside-menu {
      background: $leftBarColor;
      border-radius: 0;
    }
    .main-content {
      .main-content-overflow {
        & > .el-card.is-always-shadow:first-child {
          border: 1px solid #eff3f5;
        }
      }
    }
    .breadcrumb {
      border: 1px solid #f1f2f5;
      background-color: #fff;
      box-shadow: none;
    }
  }
  .tabs-wrapper {
    a {
      border-radius: 6px;
      background-color: #fff;
      border: 1px solid #f0f1f3;
    }
  }
  .tabs-wrapper a.active,
  .tabs-wrapper a:hover {
    background-color: var(--primary-color-sub);
    color: #f8f6f7;
  }
  .el-menu.asideMenu,
  .el-sub-menu__title {
    background: #11143e !important;
    color: #737595 !important;
  }
  .el-menu-item:hover,
  .el-sub-menu__title:hover {
    background: #11143e;
    color: #ddd !important;
  }
  ul.el-menu.asideMenu > li.is-active {
    // background: #292b4e;
    color: #ddd !important;
  }
  .el-menu-item {
    color: #737595;
  }
  .el-menu.asideMenu .el-menu .el-menu-item {
    background: #292b4e;
    &:hover {
      color: #ddd;
    }
    &.is-active {
      color: #fff !important;
      background: #3e4066;
    }
  }
  .asideMenu:not(.el-menu--collapse) {
    .el-sub-menu .el-menu {
      background: #292b4e;
    }
  }
  .sidebar-logo-container {
    background-color: #11143e;
  }
  .asset-top-ul {
    .top li {
      border: 1px solid #eff3f5;
    }
    .card {
      border: 1px solid #eff3f5;
    }
  }
}

/*作为子应用嵌套的样式，style2的基础上再覆盖*/
body.wujie-child-app {
  width: 100%;
  min-height: calc(100vh - 120px);
  overflow-x: hidden;
  min-width: auto;
  position: relative;
}
body.wujie-child-app #outpostStyle3 {
  .el-picker__popper.el-popper {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12) !important;
  }
  .el-card {
    border: none;
  }
  // 页面布局
  & > .el-card.is-always-shadow {
    border: 0;
  }

  .el-table {
    --el-table-font-color: #262824;
  }
}

body #outpostStyle3 {
  //box-shadow

  .el-input__inner,
  .el-textarea__inner {
    --el-input-bg-color: #f6f7f6;
    --el-color-white: #f6f7f6;

    --el-border-color-hover: #436fcc;
    box-shadow: none;
    --el-input-border: 1px solid transparent;
  }
  .el-input.is-disabled {
    .el-input__inner,
    .el-textarea__inner {
      background-color: #f5f7fa;
    }
  }

  /*分页*/
  .el-pagination {
    height: 36px;
    color: var(--primary-color-sub);
    --el-pagination-font-size: 12px;
    padding-right: 10px;
  }
  .el-pagination .btn-next .el-icon,
  .el-pagination .btn-prev .el-icon {
    font-size: 16px;
  }
  .el-pagination .el-input__inner {
    --el-input-bg-color: #fff;
    background: #fff;
    color: var(--primary-color-sub);
    border: 1px solid var(--primary-color-sub);
    height: 32px;
    min-height: 32px;
  }

  .el-pagination .el-pagination__total {
    color: var(--primary-color-sub);
  }
  .el-pagination .btn-next,
  .el-pagination .btn-prev {
    color: var(--primary-color-sub);
    position: relative;
    z-index: 1;
    transform: scale(0.9);
    background: transparent;
  }
  .el-pagination .el-pager {
    position: relative;
    --el-pagination-button-width: 48px;
  }
  .el-pagination .el-pager li {
    position: relative;
    z-index: 1;
    height: 32px;
    line-height: 32px;
    border-radius: 0;
  }
  .el-pagination.is-background .el-pager li:not(.active) {
    background: transparent;
    color: var(--primary-color-sub);
  }
  .el-pagination .el-pager:before {
    display: block;
    content: "";
    position: absolute;
    width: calc(100% + 80px);
    left: -40px;
    top: -1px;
    bottom: -1px;
    background: transparent;
    border: 1px solid var(--primary-color-sub);
    border-radius: 18px;
  }
  .el-pager li.is-active {
    background: var(--primary-color-sub);
    color: #fff;
  }
  /*tab*/
  .el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
  .el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
  .el-tabs--top .el-tabs__item.is-top:nth-child(2),
  .el-tabs__item {
    padding-left: 30px;
  }
  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:before,
  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:after {
    background: #144ec1;
  }
  /*表单*/
  .el-form-item--default {
    --font-size: 12px;
  }
  .el-checkbox,
  .el-radio,
  .el-input {
    --el-color-white: #f6f7f6;
  }
  .el-input {
  }
  .custom-select,
  .el-textarea__inner {
    background: #f6f7f6;
  }

  input,
  .vue-treeselect__control {
    background-color: #f6f7f6;
  }
  .el-form-item__label {
    font-size: 12px;
    color: #4b4b4b;
  }
  .two_filter_list > .two_filter {
    background: #fff;
    border: 1px solid #eee;
  }

  //过滤器编辑
  .condition-wrapper {
    // background: #fdfbfe;
  }

  .el-table .cell {
    min-width: 60px;
  }

  .table-handler-btns {
    .el-button {
      &:not(.filter-btn) {
        color: #222;
        border: 1px solid #f0f0f0;
        background: #f0f0f0;

        &.is-disabled {
          opacity: 0.5;
        }
        &:not(.is-disabled) {
          &:focus,
          &:active,
          &:hover {
            background: #fff;
            border-color: #eaeaea;
          }
        }
      }

      &.filter-search-btn {
        background-color: #2b82e0;
        border-color: #2b82e0;
        color: #fff !important;
        &:hover {
          background: #3f9afc !important;
          border-color: #b8daff;
        }
      }
    }
  }
}

/* isoss两种风格都需要的改动 */
body.wujie-child-app #outpostStyle3,
body.wujie-child-app #outpostStyle4 {
  .conH3Tit {
    display: none;
  }
  //资产测绘结果详情页面
  .details-starshot-title {
    justify-content: end;
    section {
      margin-top: 0;
    }
  }
  //星梭详情的详情标签页面
  .starshot-tab,
  .starshot-tab-40 {
    margin-top: 0;
    & > .el-card {
      padding-top: 10px;
    }
  }

  .siem-search-conH3Tit {
    display: block;
    & > .pull-left {
      display: none;
    }
  }
  .main-content-overflow {
    & .el-card > .el-card__body {
      padding: 0 0px !important;
    }
  }
  .el-form-item__label {
    font-size: 13px;
  }

  /*tab*/

  .el-tabs__nav:not(.is-left) .el-tabs__active-bar {
    bottom: 3px;
  }

  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:before,
  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:after {
    content: "";
    display: block;
    width: 30px;
    height: 2px;
    position: absolute;
    top: 0;

    left: -30px;
  }
  .el-tabs__nav:not(.is-left) .el-tabs__active-bar:after {
    right: -30px;
    left: auto;
  }
  .el-checkbox__label {
    font-size: 12px;
  }
  .dialog-footer {
    justify-content: flex-end;
  }
  .search-form-wrapper .el-form-item--small .el-form-item__label {
    line-height: 22px;
    padding-bottom: 0px;
    display: inline-block;
    position: relative;
    &:after {
      content: "";
      display: inline-block;
      position: absolute;
      width: 6px;
      height: 14px;
      background: #fff;
      top: 4px;
      right: -3px;
    }
  }
  // 搜索表单：全部结果
  .search-form-wrapper .formCon .el-form-item__label {
    &:after {
      right: -6px !important;
    }
  }
  .common-search-btns-item {
    display: flex;
    align-items: flex-end;
  }
  .siem-search-content-wrapper {
    padding: 0 10px;
  }
  .table-handler-btns {
    text-align: left;
    margin-bottom: 15px;
    overflow: hidden;

    .el-button {
      .el-icon {
        vertical-align: top;
      }
      &.filter-search-btn {
        float: right;
      }
    }
  }
  .jh {
    .el-input__inner {
      font-size: 12px;
    }
  }
}

body.wujie-child-app #outpostStyle4 {
  .main-content {
    border-radius: 0;
  }
  .main-content-overflow .el-card {
    border-radius: 0;
  }
  //节点管理--星梭详情
  .starshot-wrapper {
    .el-card.is-always-shadow {
      box-shadow: none !important;
    }
  }
  .el-card.bg-p-border-new.is-always-shadow {
    box-shadow: none !important;
  }
}
