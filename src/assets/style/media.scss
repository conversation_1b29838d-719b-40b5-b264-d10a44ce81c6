@media screen and (max-width: 1900px) {
  body .layout-wrapper {
    .content-wrapper {
      padding: 0 24px 0;
    }
    .breadcrumb {
      width: calc(100% + 24px);
      .avatar {
        margin-right: 24px;
      }
    }
    //工作台
    .workbench-wrapper {
      .heAdd:not(.user-section) {
        height: 227px;
      }
      .user-section {
        .user-identity {
          height: 80px;
          margin-bottom: 30px;
        }
        .avatar {
          width: 80px;
          height: 80px;
        }
        .onNane {
          margin-top: 0;
        }
      }
    }
    // 首页概览
    .home-wrapper {
      .home-middle {
        .situation {
          li {
            width: 33.3%;
            margin-bottom: 20px;
          }
        }
      }
      .home-card {
        .el-card__body {
          padding: 20px;
          .el-col.el-col-17.is-guttered {
            padding-left: 40px !important;
            padding-right: 0 !important;
          }
          .number p {
            margin-right: 0px;
          }
        }
      }
      .spanlist {
        .echart-box {
          height: 380px !important;
        }
      }
    }
    // 漏洞总览
    .home-loophole {
      .head {
        height: 280px;
        margin-bottom: 40px;
        .el-col-17 {
          padding-left: 40px !important;
        }
        .el-col-19 {
          .level-data li {
            margin-right: 5px;
          }
        }
        .el-col-5 {
          .level-data li {
            margin-bottom: 13px;
            margin-right: 0;
            .fontstyle {
              margin-right: 10px;
            }
            .fontcolo {
              font-size: 19px;
            }
          }
        }

        .onnote {
          height: 290px;
        }
      }
      .chart {
        .autc {
          height: 380px !important;
        }
      }
      .pictures {
        .autc {
          height: 380px !important;
        }
      }
    }
    // 事件总览
    .home-event {
      .inevent {
        .el-col-17 {
          padding-left: 9px !important;
          padding-right: 0px !important;
        }
      }
      .middle {
        .autc {
          height: 380px !important;
        }
      }
      .bottom {
        .autc {
          height: 380px !important;
        }
      }
    }
    //漏洞
    .level-data li {
      margin-right: 15px;
    }
    //报告
    .report-wrapper {
      .report-items {
        .el-col.el-col-4.is-guttered:last-child {
          padding-right: 0 !important;
          padding-left: 0px !important;
        }
      }
    }
    //资产总览
    .asset-top-ul {
      margin-bottom: 15px;

      li {
        height: 120px;
      }
    }
    //资产详情
    .dynamic-item-wrapper {
      .el-form-item--small .el-form-item__label {
        width: fit-content !important;
        margin-left: 10px;
      }

      .el-input--suffix .el-input__inner {
        padding-right: 10px;
      }
      .el-input--suffix:not(.el-input--prefix) .el-input__inner {
        padding-left: 10px;
      }
    }
  }
}

@media screen and (max-width: 1500px) {
  body .layout-wrapper {
    .el-input--suffix .el-input__inner {
      padding-right: 10px;
    }
    .el-input--suffix:not(.el-input--prefix) .el-input__inner {
      padding-left: 10px;
    }
    .el-form-item__label {
      margin-right: 2px;
    }
    .workbench-wrapper {
      .user-section {
        .onadd .el-card__body,
        &.heAdd .el-card__body {
          padding-left: 10px;
          padding-right: 10px;
        }
        .avatar {
          margin-right: 10px;
        }
      }
      .count-ul {
        padding-left: 20px;
        li {
          margin-right: 30px;
        }
      }
    }
    //首页总览
    .home-wrapper {
      .home-card {
        & > li {
          margin-right: 10px;
          .el-card__body {
            padding: 14px;
            height: 207px;
          }
        }
      }
    }
    //事件总览
    .home-event {
      .wid {
        .el-card__body {
          padding-right: 10px;
          padding-left: 16px;
        }
      }
      .inevent .el-col-17 {
        padding-left: 13px !important;
      }
    }
    //事件详情
    .timeline-wrapper .el-card__body {
      padding-left: 15px;
      padding-right: 15px;
      .iconClose i {
        right: -5px;
      }
    }
    .create-data li {
      margin-right: 40px;
    }
    //漏洞总览
    .home-loophole {
      .head {
        .el-col.el-col-5.is-guttered.onnote {
          max-width: 25%;
          flex: 0 0 25%;
        }
        .el-col.el-col-19.is-guttered.onnote {
          max-width: 75%;
          flex: 0 0 75%;
          .el-col.el-col-6.is-guttered {
            padding-right: 10px !important;
            &:last-child {
              padding-left: 0 !important;
            }
          }
        }
      }
    }
    //报告
    .report-wrapper {
      .report-items {
        transform: translateY(10px);
        .el-col.is-guttered {
          .spanName {
            display: block;
          }
        }
        &:last-child {
          margin-bottom: 5px;
        }
      }
    }
    //资产详情
    .dynamic-item-wrapper {
      .el-form-item--small .el-form-item__label {
        width: fit-content !important;
        margin-left: 10px;
      }
      .el-form-item.el-form-item--small.xelItem.xel-form-item {
        width: 33% !important;
        margin-right: 0;
      }

      .empty-line {
        display: block;
        margin-top: -12px;
        margin-bottom: 15px;
        text-align: right;
        padding-right: 10px;
      }
    }
  }
}
