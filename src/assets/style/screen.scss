@font-face {
  font-family: "yejing";
  src: url("../fonts/yejingshuzi.TTF");
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url("../fonts/YouSheBiaoTiHei-2.ttf");
}

@media only screen and (max-width: 1500px) {
  .ScaleBox {
    height: 1080px !important;
    width: 1920px !important;
  }
}

// 大屏 大于 1600px 宽度屏幕 媒体查询
@media only screen and (min-width: 1500px) and (min-aspect-ratio: 192/108) {
  // 如果宽高比小于16：9的话，显示这个内容
  html {
    font-size: calc(100vh / 1080 * 16);
  }
}

@media only screen and (min-width: 1500px) and (max-aspect-ratio: 192/108) {
  // 如果宽高比大于16：9的话，显示这个内容
  html {
    font-size: calc(100vw / 1920 * 16);
  }
}

.el-select__popper {
  filter: invert(90%);
}

//
