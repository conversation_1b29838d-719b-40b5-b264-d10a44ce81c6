.draggable-resizable {
  .vdr {
    touch-action: none;
    position: absolute;
    box-sizing: border-box;
    /* border: 1px dashed black; */
  }
  .handle {
    box-sizing: border-box;
    position: absolute;
    width: 10px;
    height: 10px;
    /* background: #EEE; */
    /* border: 1px solid #333; */
    z-index: 9;
  }
  .handle-tl {
    top: -10px;
    left: -10px;
    cursor: nw-resize;
    display: none !important;
  }
  .handle-tm {
    width: 100%;
    top: 0;
    left: 0;
    cursor: n-resize;
  }
  .handle-tr {
    top: -10px;
    right: -10px;
    cursor: ne-resize;
    display: none !important;
  }
  .handle-ml {
    height: 100%;
    top: 0;
    left: -6px;
    cursor: w-resize;
  }
  .handle-mr {
    height: 100%;
    top: 0;
    right: -6px;
    cursor: e-resize;
  }
  .handle-bl {
    bottom: -10px;
    left: -10px;
    cursor: sw-resize;
    display: none !important;
  }
  .handle-bm {
    width: 100%;
    bottom: -7px;
    left: 0;
    cursor: s-resize;
  }
  .handle-br {
    bottom: -10px;
    right: -10px;
    cursor: se-resize;
    display: none !important;
  }
  @media only screen and (max-width: 768px) {
    [class*="handle-"]:before {
      content: "";
      left: -10px;
      right: -10px;
      bottom: -10px;
      top: -10px;
      position: absolute;
    }
  }
}
