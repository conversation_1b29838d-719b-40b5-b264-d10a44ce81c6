/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  border-radius: 25px;
  background-color: #eee;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  background-color: #ddd;
}
html,
body {
  height: 100%;
}

body {
  font-size: 14px;
  font-weight: 400;
  color: $fontColor;
  font-family: "PingFangSC-Regular", "PingFang SC", "Microsoft Yahei";
  -webkit-font-smoothing: antialiased; /*chrome、safari*/
  -moz-osx-font-smoothing: grayscale; /*firefox*/
  outline: none;
  min-width: 1336px;
  overflow-x: auto;
}
a {
  text-decoration: none;
  -webkit-transition: all 0.25s ease;
  -moz-transition: all 0.25s ease;
  -ms-transition: all 0.25s ease;
  -o-transition: all 0.25s ease;
  transition: all 0.25s ease;
  &:hover {
    color: #2d4590;
    text-decoration: underline;
  }
}
img {
  border: none;
}
// 清除浮动
.clearfix {
  // display: inline-block;
}
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
// 左浮动
.pull-left {
  float: left;
}
// 右浮动
.pull-right {
  float: right;
}
// 文字溢出显示...
.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
// 弹性布局
.flex {
  display: -moz-box;
  // Firefox
  display: -ms-flexbox;
  // IE10
  display: -webkit-box;
  // Safari
  display: -webkit-flex;
  // Chrome, WebKit
  display: box;
  display: flexbox;
  display: flex;
}
// 纵向布局
.flex-col {
  flex-direction: column;
}
// 横向翻转布局
.flex-row-r {
  flex-direction: row-reverse;
}
// 两边对齐弹性布局
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
// 间隔相等弹性布局
.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
// 水平居中
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
// 弹性布局从头排
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
// 弹性布局从尾排
.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
// 弹性布局换行
.flex-wrap {
  flex-wrap: wrap;
}
// 弹性布局文字不换行，溢出显示...
.flex-1 {
  flex: 1;
  overflow: hidden;
}
// 顶部对齐
.align-start {
  align-self: flex-start;
}
// 尾部对齐
.align-end {
  align-self: flex-end;
}
// 上外边距
.margin-top0 {
  margin-top: 0px;
}
.margin-top5 {
  margin-top: 5px !important;
}
.margin-top10 {
  margin-top: 10px;
}
.margin-top20 {
  margin-top: 20px;
}
.margin-top30 {
  margin-top: 30px;
}
// 下外边距
.margin-bottom0 {
  margin-bottom: 0px;
}
.margin-bottom5 {
  margin-bottom: 5px;
}
.margin-bottom10 {
  margin-bottom: 10px;
}
.margin-bottom20 {
  margin-bottom: 20px;
}
.margin-bottom30 {
  margin-bottom: 30px;
}
// 左外边距
.margin-left0 {
  margin-left: 0px;
}
.margin-left5 {
  margin-left: 5px;
}
.margin-left10 {
  margin-left: 10px;
}
.margin-left20 {
  margin-left: 20px;
}
.margin-left30 {
  margin-left: 30px;
}
// 右外边距
.margin-right0 {
  margin-right: 0px;
}
.margin-right5 {
  margin-right: 5px;
}
.margin-right10 {
  margin-right: 10px;
}
.margin-right20 {
  margin-right: 20px;
}
.margin-right30 {
  margin-right: 30px;
}
//定位
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.pointer {
  cursor: pointer;
}
// 图标
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}
.magin-auto {
  margin-left: auto;
  margin-right: auto;
}
// 弹窗内滚动条去除
.el-dialog__body {
  .el-row {
    margin-left: 0px !important;
    margin-right: 0px !important;
  }
}
// 日历样式调整
.fc-direction-ltr .fc-daygrid-event .fc-event-time {
  display: none;
}
.fc-daygrid-event-dot {
  display: none;
}
.fc-daygrid-dot-event .fc-event-title {
  overflow: auto;
  white-space: break-spaces;
}
