

// styles/element/index.scss
/* just override what you need */
$color: #144ec1; //颜色

@forward "element-plus/theme-chalk/src/common/var.scss" with (
  $colors: (
    "primary": (
      "base": $color,
    ),
  ),

$border-radius: (
  "base": 18px,
  "small": 2px,
  "round": 20px,
  "circle": 100%,
),
// input 圆角大小及触发后颜色
$input: (
  "border-radius": 6px,
  "focus-border": $color,
),
// select 圆角大小及触发后颜色
$select: (
  "border-radius": 8px,
  "input-focus-border-color": $color,
),
// messageBox 圆角及字体大小
$message: (
  "font-size": 14px,
),
$menu: (
  "item-font-size": 14px,
),
$font-path: "element-plus/theme-chalk/fonts",
$card: (
  "border-radius": 6px,
  "padding": 32px 28px,
  
),
$box-shadow: (
  "light": 0px 0px 0px 0px rgba(0, 0, 0, 0.05),
),

$table: (
  "header-font-color": #262f3d,
  "header-background-color": #f8f6f7,
  "border": 1px solid #ebedf1,
  "row-hover-background-color": #eef5ff,
),
$dialog:(
  "border-radius": var(--el-border-radius-round),
  "title-font-size":18px,
),
$switch:(
    "on-color": var(--el-color-success),
  ),

);


@import "element-plus/theme-chalk/src/index.scss";



.el-table--small .el-table__cell {
  padding: 3px 0;
}
.el-table::before {
  height: 0;
}
.el-input-group__append {

  color: #fff;
  border-color: $color;
  background: $color;
}

.el-dialog{
  border-radius: 6px;
  .el-dialog__title{
  }
}
.el-table .cell{
  line-height: 22px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.el-form-item__label{
// text-align: left default;
color: #848484;
}
.el-input-number--small{
  width: 100%;
}
.el-card{
  border: 1px solid #EFF3F5;
}
.el-message-box__message p{
  word-wrap: break-all;
}
.el-table__body {
  width: 100%;
  // 使表格兼容safari，不错位
  table-layout: fixed !important;
}
 .el-button--primary:focus{

}
.el-input-number.is-controls-right .el-input-number__increase{
  border-radius: 0 6px 0 0;
}
.el-input-number.is-controls-right .el-input-number__decrease{
  border-radius: 0 0 6px 0;

}