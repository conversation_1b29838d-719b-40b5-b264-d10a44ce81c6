<template>
  <div :id="appId">
    <template v-if="permissionsRequested">
      <router-view />
    </template>
    <editEchartList></editEchartList>
  </div>
</template>
<script setup>
import { useStore } from "vuex";
import { ref, watch, reactive, toRefs, computed, onMounted } from "vue";

// import editEchartList from "./components/editEchart.vue";
const store = useStore();
import { getChangeLicense, geterrorInfo } from "@/api/login";

import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";

const permissionsRequested = computed(() => {
  if (import.meta.env.VITE_IS_ISOSS) {
    return store.state.permissionsRequested;
  }
  return true;
});
const appId = getAppStyleId();
function getAppStyleId() {
  if (import.meta.env.VITE_STYLE2) {
    return "outpostStyle2";
  } else if (import.meta.env.VITE_STYLE3) {
    if (window.$wujie) {
      if (window.parent.$websiteStyle == "2") {
        return "outpostStyle4";
      }
      if (window.parent.$websiteStyle == "3") {
        return "isossApp3";
      }
    }
    return "outpostStyle3";
  }
  return "";
}

const router = useRouter();

const route = useRoute();
//判断当前系统授权问题;
// if (import.meta.env.VITE_IS_SIME) {
//   success();
// } else {
removeLoading();

//! isoss集成的子应用不需要授权
if (!window.$wujie) {
  if (!window.location.href.includes("/login") && !window.location.href.includes("/licensePage")) {
    getChangeLicense().then(({ data }) => {
      if (data.changeLicense == "0") {
        success();
      } else {
        //授权异常  判断是否有权限进行系统授权
        if (data.hasLicensePermission === true) {
          router.push({ path: "/system/license" });
        } else {
          //没有权限-提醒联系管理员
          geterrorInfo().then(({ data }) => {
            router.push({ path: "/login" });

            ElMessage.info(data.licenseError);
          });
        }
      }
    });
  }
} else {
  success();
}

// }
function success() {
  store.dispatch("loginSuccess");
  removeLoading();
}
function removeLoading() {
  document.getElementById("pageLoading") && document.getElementById("pageLoading").remove();
}
</script>

<style scoped lang="scss">
.saveEditEcharts {
  position: fixed;
  right: 0px;
  bottom: 40px;
}
</style>
