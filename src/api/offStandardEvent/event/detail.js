import Axios from "@/plugins/request";
import router from "@/router";
/* 删除文件 */
export const updateEventFile = (query) => {
  return Axios.put("/system/osEvent/updateEventFile?id=" + query);
};

//事件id
function _getId() {
  return sessionStorage.getItem("eventId");
}
//事件详情
export const getEventDetail = (params) => {
  return Axios.get("/system/osEvent/eventdetailmain", { params: { id: params.id || _getId() } });
};

//是否有事件审核、事件合并、事件升级权限
export const getEventAuditFlg = (params) => {
  return Axios.get("system/osEvent/getEventAuditFlg", { params });
};

//查询事件流
export const getEventLogList = (params) => {
  return Axios.get("/system/osEvent/getEventLogList", { params });
};
// 修改事件信息
export function saveEditEvent(params) {
  return Axios.put(`/system/osEvent/saveEditEvent`, params);
}
// 查询附件列表
export const getEventFilePage = (params) => {
  return Axios.get("/system/osEvent/getEventFilePage", { params });
};
// 查询报告列表不分页
export const selectAlarmReport = (params) => {
  return Axios.get("/system/osEvent/selectAlarmReport", { params });
};
// 驳回事件
export const postRejectEvent = (query) => {
  return Axios.post("/system/osEvent/rejectEvent", query);
};
// 关闭
export const postCloseEvent = (query) => {
  return Axios.post("/system/osEvent/closeEvent", query);
};
// 审核事件
export const saveEventAudit = (query) => {
  return Axios.post("/system/osEvent/saveEventAudit", query);
};
// 删除事件
export const deleteEvent = (params) => {
  return Axios.delete("/system/osEvent/deleteEvent", { data: params });
};
// 查询两个合并事件的数据
export const getMergeEventData = (params) => {
  return Axios.get("/system/osEvent/mergeEvent", { params });
};

// 事件合并/变更查询选择的模板信息
export const getTemplateAndTasks = (params) => {
  return Axios.get("/system/osEvent/getTemplateAndTasks", { params });
};

//事件合并
export const saveEventMerge = (params) => {
  return Axios.post("/system/osEvent/saveEventMerge", params);
};

//查询事件升级信息
export const initChangeEvent = (params) => {
  return Axios.get("/system/osEvent/initChangeEvent", { params });
};
//查询事件升级模板
export const selectChangeTemplates = (params) => {
  return Axios.get("/system/osEvent/selectChangeTemplates", { params });
};

/* 新事件变更信息初始化 */
export const initAlterationEvent = (params) => {
  return Axios.get("/system/osEvent/initAlterationEvent", { params });
};
/* 新事件变更模板列表查询 */
export const selectAlterationTemplates = (params) => {
  return Axios.get("/system/osEvent/selectAlterationTemplates", { params });
};
/* 新事件变更保存方法 */
export const saveEventAlteration = (params) => {
  return Axios.post("/system/osEvent/saveEventAlteration", params);
};

//事件升级
export const saveEventChange = (params) => {
  return Axios.post("/system/osEvent/saveEventChange", params);
};

//保存，修改超级模板
export const saveEventSuperTemplate = (params) => {
  return Axios.post("/system/osEventTemplate/saveEventSuperTemplate", params);
};

// 查询超级模板任务列表
export const selectSuperTemplateTaskList = (params) => {
  return Axios.get("/system/osEventTemplate/selectSuperTemplateTaskList", { params });
};

// 删除超级模板任务
export const delSuperTemplateTaskById = (params) => {
  return Axios.delete("/system/osEventTemplate/delSuperTemplateTaskById", { data: params });
};

//新增/修改超级模板任务
export const saveSuperEventTemplateTask = (params) => {
  return Axios.post("/system/osEventTemplate/saveSuperEventTemplateTask", params);
};

//查询模板详情和任务
export const getSuperTemplateAndTasks = (params) => {
  return Axios.get("/system/osEvent/getSuperTemplateAndTasks", { params });
};

//查询超级模板任务详情
export const selectSuperTemplateTask = (params) => {
  return Axios.get("/system/osEventTemplate/selectSuperTemplateTask", { params });
};

//关联漏洞
export const relatedVuln = (params) => {
  return Axios.post("/system/osEvent/relatedVuln", params);
};
//取消关联漏洞
export const cancelVuln = (params) => {
  return Axios.delete("/system/osEvent/cancelVuln", { data: params });
};
// 事件-查询关联事件列表
export const getEventRelevanceList = (params) => {
  return Axios.get("/system/osEvent/getEventRelevanceList", { params });
};
// 事件-查询关联事件关联要素信息
export const getRelativeList = (params) => {
  return Axios.get("/system/osEvent/getRelativeList", { params });
};
