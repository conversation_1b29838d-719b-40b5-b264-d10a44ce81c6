import Axios from "@/plugins/request";
//查询任务列表
export function initTaskList(param) {
  return Axios({
    url: "/system/osEventTask/initTaskList",
    method: "get",
    params: param,
  });
}
// 任务详情中执行日志查询方法
export function getLogList(param) {
  return Axios({
    url: "/system/osEventTask/getLogList",
    method: "get",
    params: param,
  });
}
// 审核结果列表
export function getAuditList(eventTaskId) {
  return Axios({
    url: "/system/osEventTask/getAuditList?eventTaskId=" + eventTaskId,
    method: "get",
  });
}

//查询批量审核任务
export function selectNotAuditedTask(params) {
  return Axios({
    url: "/system/osEventTask/selectNotAuditedTask",
    method: "get",
    params,
  });
}

//批量审核
export function saveEventTaskAudit(data) {
  return Axios({
    url: "/system/osEventTask/saveEventTaskAudit",
    method: "post",
    data,
  });
}

//新建任务
export function saveEventTask(data) {
  return Axios({
    url: "/system/osEventTask/saveEventTask",
    method: "post",
    data,
  });
}

//查询任务详情
export function viewEventTaskDetail(id) {
  return Axios({
    url: "/system/osEventTask/viewEventTaskDetail?id=" + id,
    method: "get",
  });
}

//修改任务
export function updateEventTask(data) {
  return Axios({
    url: "/system/osEventTask/updateEventTask",
    method: "put",
    data,
  });
}

//保存任务日志
export function saveTaskLog(data) {
  return Axios({
    url: "/system/osEventTask/saveTaskLog",
    method: "post",
    data,
  });
}

//删除任务日志
export function deleteTaskLog(data) {
  return Axios({
    url: "/system/osEventTask/deleteTaskLog",
    method: "delete",
    data,
  });
}
//删除任务日志文件
export function removeFile(data) {
  return Axios({
    url: "/system/osEventTask/removeFile",
    method: "delete",
    data,
  });
}

//开始任务
export function startTask(data) {
  return Axios({
    url: "/system/osEventTask/startTask",
    method: "post",
    data,
  });
}

//关闭任务
export function closeTask(data) {
  return Axios({
    url: "/system/osEventTask/closeTask",
    method: "post",
    data,
  });
}

//提报任务
export function submitTask(data) {
  return Axios({
    url: "/system/osEventTask/submitTask",
    method: "post",
    data,
  });
}

//删除任务
export function deleteTask(data) {
  return Axios({
    url: "/system/osEventTask/deleteTask",
    method: "delete",
    data,
  });
}

//审核任务
export function saveAuditEvent(data) {
  return Axios({
    url: "/system/osEventTask/saveAudit",
    method: "post",
    data,
  });
}

//重做任务
export function redoTask(data) {
  return Axios({
    url: "/system/osEventTask/redoTask",
    method: "put",
    data,
  });
}

//任务日志汇总
export function initEventTaskLogList(params) {
  return Axios({
    url: "/system/osEventTask/initEventTaskLogList",
    method: "get",
    params,
  });
}
