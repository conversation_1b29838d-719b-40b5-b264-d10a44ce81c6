import Axios from "@/plugins/request";
import router from "@/router";
// 查询已处置告警
export const getAlertDealCount = (params) => {
  return Axios.get("/system/threat/analysis/getAlertDealCount", { params });
};
//  查询已分析安全事件数
export const getEventAnalysisCount = (params) => {
  return Axios.get("/system/threat/analysis/getEventAnalysisCount", { params });
};
// 查询资产数量
export const getEventAssetCount = (params) => {
  return Axios.get("/system/threat/analysis/getEventAssetCount", { params });
};
// 查询事件等级分布状态
export const getEventLevelCount = (params) => {
  return Axios.get("/system/threat/analysis/getEventLevelCount", { params });
};
//   攻击告警次数
export const getAlertLevelCount = (params) => {
  return Axios.get("/system/threat/analysis/getAlertLevelCount", { params });
};
// 告警源生成告警量
export const getAlertDevCount = (params) => {
  return Axios.get("/system/threat/analysis/getAlertDevCount", { params });
};
// 事件信息展示
export const getEventCount = (params) => {
  return Axios.get("/system/threat/analysis/getEventCount", { params });
};
//   攻击事件类型
export const getEventTypeCount = (params) => {
  return Axios.get("/system/threat/analysis/getEventTypeCount", { params });
};
