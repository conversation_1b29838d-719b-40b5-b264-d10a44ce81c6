import Axios from "@/plugins/request";
import router from "@/router";
// 查询可疑对象列表
export const getSuspiciousList = (params) => {
  return Axios.get("/system/eventSuspiciousObject/getSuspiciousList", { params });
};
// 新增可疑对象
export function saveSuspiciousForm(query) {
  return Axios({
    url: "/system/eventSuspiciousObject/saveSuspiciousForm",
    method: "post",
    data: query,
  });
}
// 删除可疑对象
export function deleteEventSuspicious(query) {
  return Axios({
    url: "/system/eventSuspiciousObject/deleteEventSuspicious",
    method: "DELETE",
    data: query,
  });
}
//批量打开可疑对象
export function openSuspicious(query) {
  return Axios({
    url: "/system/eventSuspiciousObject/openSuspicious",
    method: "post",
    data: query,
  });
}
// 批量关闭可疑对象
export function closeSuspicious(query) {
  return Axios({
    url: "/system/eventSuspiciousObject/closeSuspicious",
    method: "post",
    data: query,
  });
}
// 修改可疑对象
export function saveEventSuspicious(params) {
  return Axios({
    url: "/system/eventSuspiciousObject/saveEventSuspicious",
    method: "put",
    data: params,
  });
}
// 查看详情
export const getDetail = (params) => {
  return Axios.get("/system/eventSuspiciousObject/detail", { params });
};
