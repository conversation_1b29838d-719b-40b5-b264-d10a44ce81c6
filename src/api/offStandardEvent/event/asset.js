import Axios from "@/plugins/request";
//业务系统对象 --- 查列表
export function selectBussiness(param) {
  return Axios({
    url: "/system/osAffected/selectBussiness",
    method: "get",
    params: param,
  });
}
// 业务系统对象 --- 查详情
export function selectBusinessDetail(param) {
  return Axios({
    url: "/system/osAffected/selectBusinessDetail",
    method: "get",
    params: param,
  });
}

//计算设备对象 --- 查列表
export function getRelResourceList(param) {
  return Axios({
    url: "/system/osAffected/getRelResourceList",
    method: "get",
    params: param,
  });
}
//计算设备对象 --- 查详情
export function getRelResourceDetail(param) {
  return Axios({
    url: "/system/osAffected/getRelResourceDetail",
    method: "get",
    params: param,
  });
}
//终端资产对象 --- 列表
export function selectEventAssetsTerminal(param) {
  return Axios({
    url: "/system/osAffected/selectEventAssetsTerminal",
    method: "get",
    params: param,
  });
}
//终端资产对象 --- 详情
export function selectTerminalDetail(param) {
  return Axios({
    url: "/system/osAffected/selectTerminalDetail",
    method: "get",
    params: param,
  });
}

//域名待确认资产 --- 列表
export function getDomainConfirmList(param) {
  return Axios({
    url: "/system/osAffected/getDomainConfirmList",
    method: "get",
    params: param,
  });
}
//   ip待确认资产 --- 列表
export function selectIpConfirm(param) {
  return Axios({
    url: "/system/osAffected/selectIpConfirm",
    method: "get",
    params: param,
  });
}
// 业务系统对象关联

export function addRelevancy(param) {
  return Axios({
    url: "/system/osAffected/addRelevancy",
    method: "post",
    data: param,
  });
}
// 计算设备对象 --- 添加关联要素

export function addResourceRelContent(param) {
  return Axios({
    url: "/system/osAffected/addResourceRelContent",
    method: "post",
    data: param,
  });
}
// 业务取消关联

export function businessDisassociate(param) {
  return Axios({
    url: "/system/osAffected/businessDisassociate",
    method: "delete",
    data: param,
  });
}
// 计算设备对象 --- 取消关联
export function cancelEventResourceRel(param) {
  return Axios({
    url: "/system/osAffected/cancelEventResourceRel",
    method: "delete",
    data: param,
  });
}
// 终端资产对象 --- 取消关联
export function terminalDisassociate(param) {
  return Axios({
    url: "/system/osAffected/terminalDisassociate",
    method: "delete",
    data: param,
  });
}
// 域名待确认资产 --- 取消关联
export function cancelEventDomainConfirmRel(param) {
  return Axios({
    url: "/system/osAffected/cancelEventDomainConfirmRel",
    method: "delete",
    data: param,
  });
}
// ip类待确认资产 --- 取消关联
export function deleteEventIpConfirmRel(param) {
  return Axios({
    url: "/system/osAffected/deleteEventIpConfirmRel",
    method: "delete",
    data: param,
  });
}
// 业务系统对象-关联
export function saveBusinessRelevancy(param) {
  return Axios({
    url: "/system/osAffected/saveBusinessRelevancy",
    method: "post",
    data: param,
  });
}
// 计算设备对象 --- 关联
export function saveConnectResource(param) {
  return Axios({
    url: "/system/osAffected/saveConnectResource",
    method: "post",
    data: param,
  });
}
// 终端资产对象 --- 关联
export function saveEventAssetsTerminal(param) {
  return Axios({
    url: "/system/osAffected/saveEventAssetsTerminal",
    method: "post",
    data: param,
  });
}
// 域名待确认资产 --- 关联
export function saveConnectDomainConfirm(param) {
  return Axios({
    url: "/system/osAffected/saveConnectDomainConfirm",
    method: "post",
    data: param,
  });
}
// ip类待确认资产 --- 关联
export function saveEventIpConfirmRel(param) {
  return Axios({
    url: "/system/osAffected/saveEventIpConfirmRel",
    method: "post",
    data: param,
  });
}
// 公用方法 --- 修改关联要素
export function updateRelContent(param) {
  return Axios({
    url: "/system/osAffected/updateRelContent",
    method: "put",
    data: param,
  });
}
// 公用方法 --- 删除关联要素
export function delRelContent(param) {
  return Axios({
    url: "/system/osAffected/delRelContent",
    method: "delete",
    data: param,
  });
}
// 计算设备对象 --- 关联要素列表
export function getContentList(param) {
  return Axios({
    url: " /system/osAffected/getContentList",
    method: "get",
    params: param,
  });
}
