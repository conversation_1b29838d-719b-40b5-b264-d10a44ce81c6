import request from "@/plugins/request";
// 事件 – 获取列表
export function getEventList(query) {
  return request({
    url: "/system/osEvent/getList",
    method: "get",
    params: query,
  });
}
//导出事件
export function exportEvent(query) {
  return request({
    url: "/system/osEvent/exportEvent",
    method: "post",
    data: query,
  });
}
// 驳回-列表
export function getRejectEventPage(query) {
  return request({
    url: "/system/osEvent/getRejectEventPage",
    method: "get",
    params: query,
  });
}
//   事件-事件新建选择模板列表
export function getEventTemplate(query) {
  return request({
    url: "/system/osEvent/getEventTemplate",
    method: "get",
    params: query,
  });
}
//   业务系统--查询业务系统列表
export function assetsBusiness(query) {
  return request({
    url: "/system/assetsBusiness/list",
    method: "get",
    params: query,
  });
}
//   计算设备资产-查询列表
export function getAssetsResourcesList(query) {
  return request({
    url: "/system/assetsBasic/getAssetsResourcesList",
    method: "get",
    params: query,
  });
}
//   待确认资产-查询域名类待确认资产列表
export function getAssetsConfirmedList(query) {
  return request({
    url: "/system/assetsConfirm/getAssetsConfirmedList",
    method: "get",
    params: query,
  });
}
//   终端资产--查询列表
export function assetsTerminal(query) {
  return request({
    url: "/system/assetsTerminal/list",
    method: "get",
    params: query,
  });
}
export function selectUserTree(query) {
  return request({
    url: "/system/user/selectUserTree",
    method: "get",
    params: query,
  });
}
export function getAssigneeRoles(query) {
  return request({
    url: "/system/role/getAssigneeRoles",
    method: "get",
    params: query,
  });
}

// 校验可疑对象
export function checkSuspiciousForm(query) {
  return request({
    url: "/system/osEvent/checkSuspiciousForm",
    method: "post",
    data: query,
  });
}
// 创建事件
export function saveEvent(query) {
  return request({
    url: "/system/osEvent/saveEvent",
    method: "post",
    data: query,
  });
}
// 查询要关联的业务系统资产信息
export function getRelBusinessAssetById(query) {
  return request({
    url: "/system/osEvent/getRelBusinessAssetById",
    method: "get",
    params: query,
  });
}
// 查询要关联的计算设备资产信息
export function getRelBasicAssetById(query) {
  return request({
    url: "/system/osEvent/getRelBasicAssetById",
    method: "get",
    params: query,
  });
}
// 查询要关联的终端资产信息
export function selectAssetsTerminalById(query) {
  return request({
    url: "/system/osEvent/selectAssetsTerminalById",
    method: "get",
    params: query,
  });
}
// 查询要关联的域名类待确认资产
export function selectDomainNotConfirmById(query) {
  return request({
    url: "/system/osEvent/selectDomainNotConfirmById",
    method: "get",
    params: query,
  });
}
// 查询要关联的IP类待确认资产
export function selectIpNotConfirmById(query) {
  return request({
    url: "/system/osEvent/selectIpNotConfirmById",
    method: "get",
    params: query,
  });
}

//新增待确认域名类资产
export function saveDomainNotConfirm(query) {
  return request({
    url: "/system/assetsConfirm/saveDomainNotConfirm",
    method: "post",
    data: query,
  });
}

//新增待确认ip类资产
export function saveIpNotConfirm(query) {
  return request({
    url: "/system/assetsConfirm/saveIpNotConfirm",
    method: "post",
    data: query,
  });
}
