import request from "@/plugins/request";
// 运营脚本 – 获取列表
export function selectPage(query) {
  return request({
    url: "/system/osEventTemplate/selectPage",
    method: "get",
    params: query,
  });
}
// 运营脚本-根据模板id查询信息
export function getTemplateById(query) {
  return request({
    url: "/system/osEventTemplate/getTemplateById",
    method: "get",
    params: query,
  });
}
// 运营脚本-查询关联模板列表
export function getRelTemplate(query) {
  return request({
    url: "/system/osEventTemplate/getRelTemplate",
    method: "get",
    params: query,
  });
}
// 运营脚本-查询模板任务列表
export function selectTaskListByTemplateId(query) {
  return request({
    url: "/system/osEventTemplate/selectTaskListByTemplateId",
    method: "get",
    params: query,
  });
}
// 运营脚本-查询可关联列表
export function getRelTemplateList(query) {
  return request({
    url: "/system/osEventTemplate/getRelTemplateList",
    method: "get",
    params: query,
  });
}
// 运营脚本-关联模板
export function relManageTemplate(query) {
  return request({
    url: "/system/osEventTemplate/relManageTemplate",
    method: "post",
    data: query,
  });
}
// 运营脚本-根据id判断模板是否已经存在
export function checkTemplateById(query) {
  return request({
    url: "/system/osEventTemplate/checkTemplateById",
    method: "get",
    params: query,
  });
}
// 运营脚本-查询新建模板任务信息

export function getTempTaskParam(query) {
  return request({
    url: "/system/osEventTemplate/getTempTaskParam",
    method: "get",
    params: query,
  });
}
// 运营脚本-获取模板id
export function createTemplateId(query) {
  return request({
    url: "/system/osEventTemplate/createTemplateId",
    method: "post",
    data: query,
  });
}
// 运营脚本-删除关联关系
export function delRelManageTemplate(query) {
  return request({
    url: "/system/osEventTemplate/delRelManageTemplate",
    method: "DELETE",
    data: query,
  });
}
// 运营脚本-导入脚本
export function importEventTemplate(query) {
  return request({
    url: "/system/osEventTemplate/importEventTemplate",
    method: "post",
    data: query,
  });
}
// 运营脚本-模板下载
export function downloadEventTemplate(query) {
  return request({
    url: "/system/osEventTemplate/downloadEventTemplate",
    method: "get",
    params: query,
  });
}
// 运营脚本-导出模板
export function exportEventTemplate(query) {
  return request({
    url: "/system/osEventTemplate/exportEventTemplate",
    method: "post",
    params: query,
  });
}
// 运营脚本-保存模板任务
export function saveEventTemplateTask(query) {
  return request({
    url: "/system/osEventTemplate/saveEventTemplateTask",
    method: "put",
    data: query,
  });
}
// 运营脚本-删除模板任务
export function delEventTemplateTaskById(query) {
  return request({
    url: "/system/osEventTemplate/delEventTemplateTaskById",
    method: "delete",
    data: query,
  });
}
// 运营脚本-校验运营脚本名称是否已存在
export function checkTemplateName(query) {
  return request({
    url: "/system/osEventTemplate/checkTemplateName",
    method: "get",
    params: query,
  });
}
// 运营脚本-删除模板任务
export function saveEventTemplate(query) {
  return request({
    url: "/system/osEventTemplate/saveEventTemplate",
    method: "put",
    data: query,
  });
}
// 运营脚本-初始化任务要求
export function initTask(query) {
  return request({
    url: "/system/osEventTemplate/initTask",
    method: "post",
    data: query,
  });
}
//标签-新增标签
export function saveTagByTagName(query) {
  return request({
    url: "/system/tag/saveTagByTagName",
    method: "put",
    data: query,
  });
}
//运营脚本-删除运营脚本
export function deleteEventTemplate(query) {
  return request({
    url: "/system/osEventTemplate/deleteEventTemplate",
    method: "delete",
    data: query,
  });
}
