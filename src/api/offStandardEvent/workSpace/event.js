import Axios from "@/plugins/request";
// 待处理事件任务
export function getNotFinishedTaskListOs(query) {
  return Axios({
    url: "/system/osEventTask/getNotFinishedTaskList",
    method: "get",
    params: query,
  });
}
// 待认领事件任务
export function selectUnclaimedTaskPageOs(query) {
  return Axios({
    url: "/system/osEventTask/selectUnclaimedTaskPage",
    method: "get",
    params: query,
  });
}
// 事件审核 前五条
export function selectAuditTaskListOs(params) {
  return Axios({
    /*url: "/system/workbench/selectAuditTaskList",*/
    url: "/system/osEventTask/selectAuditTaskList",
    method: "get",
    params,
  });
}
// 抽检事件
export function selectAuditSamplingTask() {
  return Axios({
    url: "/system/workbench/selectAuditSamplingTask",
    method: "get",
  });
}
// 工作台告警提报事件  获取模板任务数
export function getEventTemplateTaskSize(templateId) {
  return Axios({
    url: "/system/workbench/getEventTemplateTaskSize?templateId=" + templateId,
    method: "get",
  });
}
//工作台告警提报事件
export function alertsaveEvent(params) {
  return Axios({
    url: "/system/workbench/saveEvent",
    method: "post",
    data: params,
  });
}
// 工作台告警提报事件 并添加事件任务
export function saveEventAndTask(params) {
  return Axios({
    url: "/system/workbench/saveEventAndTask",
    method: "post",
    data: params,
  });
}
// 事件审核分页数据
export function selectAuditTaskPageOs(query) {
  return Axios({
    url: "/system/osEventTask/selectAuditTaskPage",
    method: "get",
    params: query,
  });
}
