import request from "@/plugins/request";
//  获取列表
export function operationParam(query) {
  return request({
    url: "/system/operationParam/list",
    method: "get",
    params: query,
  });
}
//获取子列表
export function operationParamChild(query) {
  return request({
    url: "/system/operationParam/paramList",
    method: "get",
    params: query,
  });
}

//  运营参数--新增/修改父类
export function saveParent(query) {
  return request({
    url: "/system/operationParam/saveParent",
    method: "post",
    data: query,
  });
}
// 运营参数--删除父类
export function deleteParentValue(query) {
  return request({
    url: "/system/operationParam/deleteParentValue/" + query.paramId,
    method: "DELETE",
  });
}

//   运营参数--校验运营参数id
export function checkParamId(query) {
  return request({
    url: "/system/operationParam/checkParamId",
    method: "get",
    params: query,
  });
}
//   运营参数--新增/修改运营参数
export function saveParamValue(query) {
  return request({
    url: "/system/operationParam/saveParamValue",
    method: "post",
    data: query,
  });
}
//   运营参数--删除运营参数
export function deleteParamValue(query) {
  return request({
    url: "/system/operationParam/deleteParamValue",
    method: "DELETE",
    data: query,
  });
}
//   运营参数--查询父类下拉列表
export function listParent(query) {
  return request({
    url: "/system/operationParam/listParent",
    method: "get",
    data: query,
  });
}
// 查询具体
export function searchOperationParam(id) {
  return request({
    url: "/system/operationParam/value/" + id,
    method: "get",
    data: id,
  });
}
