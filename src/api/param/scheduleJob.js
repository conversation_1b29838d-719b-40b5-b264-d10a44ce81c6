import Axios from "@/plugins/request";

export const getTableData = (params) => {
  return Axios.get("/schedule/job/list", { params });
};
//查详情
export const getDetail = (params) => {
  return Axios.get(`/schedule/job/${params.jobId}`);
};

//新增 修改
export const addItem = (params) => {
  return Axios.post("/schedule/job", params);
};

export const updateItem = (params) => {
  return Axios.put("/schedule/job", params);
};

//删除
export const delItem = (params) => {
  return Axios.delete(`/schedule/job/${params.jobId}`);
};

//执行一次
export const runJob = (params) => {
  return Axios.put("/schedule/job/run", params);
};

//启用  关闭
export const changeStatus = (params) => {
  return Axios.put("/schedule/job/changeStatus", params);
};

//查询日志列表
export const getLogList = (params) => {
  return Axios.get("/schedule/job/log/list", { params });
};
//删除日志
export const delLogs = (params) => {
  return Axios.delete(`/schedule/job/log/${params.ids}`);
};
//清空日志
export const cleanLogs = (params) => {
  return Axios.delete(`/schedule/job/log/clean`);
};
