import request from "@/plugins/request";
// 获取列表
export function selectParamValue(query) {
  return request({
    url: "/system/operationParam/selectParamValue",
    method: "get",
    params: query,
  });
}

// 设置
export function updateParamValue(query) {
  return request({
    url: "/system/operationParam/updateParamValue",
    method: "post",
    data: query,
  });
}

// 上传自定义图片
export function uploadWatermarkImage(query) {
  return request({
    url: "/system/file/uploadWatermarkImage",
    method: "post",
    data: query,
  });
}
