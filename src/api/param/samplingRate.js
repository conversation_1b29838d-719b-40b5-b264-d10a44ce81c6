import request from "@/plugins/request";
// 获取抽检率  /samplingRate/getList.do
export function getSamplingRate(query) {
  return request({
    url: "/system/samplingRate/getList",
    method: "get",
    params: query,
  });
}
// 获取人员树  /samplingRate/getSamplingRateParam.do
export function getSamplingRateParam() {
  return request({
    url: "/system/samplingRate/getSamplingRateParam",
    method: "get",
  });
}
// 新增与修改  /samplingRate/saveSamplingRate.do
export function saveSamplingParam(query) {
  return request({
    url: "/system/samplingRate/saveSamplingRate",
    method: "put",
    data: query,
  });
}
// 删除 /samplingRate/deleteRate.do
export function dellingRate(deptId) {
  return request({
    url: "/system/samplingRate/deleteRate",
    method: "delete",
    data: deptId,
  });
}
