import request from "@/plugins/request";
// 列表
export function getDbPropertiesList(query) {
  return request({
    url: "/system/dbProperties/getList",
    method: "get",
    params: query,
  });
}

// 新增数据源 - 编辑数据源
export function createDbProperties(query) {
  return request({
    url: "/system/dbProperties/saveForm",
    method: "post",
    data: query,
  });
}

// 编辑 - 获取详情
export function getDbPropertiesDetail(query) {
  return request({
    url: "/system/dbProperties/getDbPropertiesById",
    method: "get",
    params: query,
  });
}

// 删除
export function deleteDbProperties(query) {
  return request({
    url: "/system/dbProperties/delete",
    method: "delete",
    data: query,
  });
}
