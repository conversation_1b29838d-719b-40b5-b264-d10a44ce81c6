import request from "@/plugins/request";
// 告警过滤 – 获取列表
export function getFilterData(query) {
  return request({
    url: "/system/alertConfig/filter/list",
    method: "get",
    params: query,
  });
}

// 告警过滤 - 开启/关闭规则
export function updateStatus(query) {
  return request({
    url: "/system/alertConfig/filter",
    method: "put",
    data: query,
  });
}

// 告警过滤 – 删除规则
export function deleteFilter(query) {
  return request({
    url: "/system/alertConfig/filter",
    method: "delete",
    data: query,
  });
}

// 告警过滤 – 新增
export function createFilter(query) {
  return request({
    url: "/system/alertConfig/filter",
    method: "post",
    data: query,
  });
}

/*=== 紧急告警 ===*/
// 紧急告警 – 获取列表
export function getEnmergencyData(query) {
  return request({
    url: "/system/alertConfig/enmergency/list",
    method: "get",
    params: query,
  });
}

// 紧急告警 - 开启/关闭规则
export function updateEnmergencyStatus(query) {
  return request({
    url: "/system/alertConfig/enmergency",
    method: "put",
    data: query,
  });
}

// 紧急告警 – 删除规则
export function deleteEnmergencyFilter(query) {
  return request({
    url: "/system/alertConfig/enmergency",
    method: "delete",
    data: query,
  });
}

// 紧急告警 – 新增
export function createEnmergencyFilter(query) {
  return request({
    url: "/system/alertConfig/enmergency",
    method: "post",
    data: query,
  });
}
// 告警流查询-查询列表
export function getAlertOperationList(query) {
  return request({
    url: "/system/alertConfig/getAlertOperationList",
    method: "get",
    params: query,
  });
}
