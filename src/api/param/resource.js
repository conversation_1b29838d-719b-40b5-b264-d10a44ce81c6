import Axios from "@/plugins/request";

export const getTableData = (params) => {
  return Axios.get("/system/monitor/getList", { params });
};
//查详情

export const getDetail = (params) => {
  return Axios.get("/system/monitor/selectMonitorById", { params });
};
//新增 修改
export const addItem = (params) => {
  return Axios.put("/system/monitor/saveForm", params);
};
export const updateItem = (params) => {
  return Axios.put("/system/monitor/saveForm", params);
};

//删除
export const delItem = (params) => {
  return Axios.delete("/system/monitor/deleteMonitor", { data: params });
};

//检验是否已存在
export const checkFlag = (params) => {
  return Axios.post("/system/monitor/checkFlag", params);
};

//启用  关闭
export const updateIsLock = (params) => {
  return Axios.put("/system/monitor/updateIsLock", params);
};
