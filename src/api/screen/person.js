import request from "@/plugins/request";
// 威胁态势大屏

// 平均响应时间完成比例统计
export function selectAcargeCompletionRatio(query) {
  return request({
    url: "/screen/personStatus/selectAcargeCompletionRatio",
    method: "get",
    params: query,
  });
}

// 个人平均响应时间统计
export function selectPersonalAcargeCompletionRatio(query) {
  return request({
    url: "/screen/personStatus/selectPersonalAcargeCompletionRatio",
    method: "get",
    params: query,
  });
}

//任务执行及时率
export function selectTaskTimelinessRate(query) {
  return request({
    url: "/screen/personStatus/selectTaskTimelinessRate",
    method: "get",
    params: query,
  });
}

//任务执行率
export function selectTaskExecutionRate(query) {
  return request({
    url: "/screen/personStatus/selectTaskExecutionRate",
    method: "get",
    params: query,
  });
}

// 平均监测时间完成比例统计
export function selectAvgTestimeStatistics(query) {
  return request({
    url: "/screen/personStatus/selectAvgTestimeStatistics",
    method: "get",
    params: query,
  });
}

// 个人监测时间完成比例统计
export function selectPersonAvgTestimeStatistics(query) {
  return request({
    url: "/screen/personStatus/selectPersonAvgTestimeStatistics",
    method: "get",
    params: query,
  });
}

// 准确率统计
export function selectAlertAndEventAccuracy(query) {
  return request({
    url: "/screen/personStatus/selectAlertAndEventAccuracy",
    method: "get",
    params: query,
  });
}

//事件处置率
export function selectUnCloseEvent(query) {
  return request({
    url: "/screen/personStatus/selectUnCloseEvent",
    method: "get",
    params: query,
  });
}

// 运营质量雷达图
export function selectLeiDaTuData(query) {
  return request({
    url: "/screen/personStatus/selectLeiDaTuData",
    method: "get",
    params: query,
  });
}
