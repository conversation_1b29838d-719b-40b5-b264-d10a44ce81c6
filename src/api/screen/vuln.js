import request from "@/plugins/request";
// 威胁态势大屏

// 漏洞数量与时间分布-业务系统漏洞
export function selectResourceList(query) {
  return request({
    url: "/screen/vulnerability/selectResourceVulnNumAndTimeDistributionList",
    method: "get",
    params: query,
  });
}
// 漏洞数量与时间分布-业务系统漏洞
export function selectBusinessList(query) {
  return request({
    url: "/screen/vulnerability/selectBusinessVulnNumAndTimeDistributionList",
    method: "get",
    params: query,
  });
}
// 漏洞数量与时间分布-全部漏洞
export function selectAllList(query) {
  return request({
    url: "/screen/vulnerability/selectVulnNumAndTimeDistributionList",
    method: "get",
    params: query,
  });
}
// 资产漏洞分布分析
export function selectAnalysisList(query) {
  return request({
    url: "/screen/vulnerability/selectAssetVulnDistributionAnalysisList",
    method: "get",
    params: query,
  });
}
// 业务系统漏洞实时状态
export function selectBusinessVulnRealTimeStatusList(query) {
  return request({
    url: "/screen/vulnerability/selectBusinessVulnRealTimeStatusList",
    method: "get",
    params: query,
  });
}
// 基础资源漏洞实时状态
export function selectResourceVulnRealTimeStatusList(query) {
  return request({
    url: "/screen/vulnerability/selectResourceVulnRealTimeStatusList",
    method: "get",
    params: query,
  });
}
// 高频监测状态-监测系统数量
export function selectMonitorSysNumber(query) {
  return request({
    url: "/screen/vulnerability/selectMonitorSysNumber",
    method: "get",
    params: query,
  });
}
// 高频监测状态-最近监测结果时间
export function selectLatestMonitoringResultTime(query) {
  return request({
    url: "/screen/vulnerability/selectLatestMonitoringResultTime",
    method: "get",
    params: query,
  });
}
// 高频监测状态-最新监测成果
export function selectLatestMonitoringResultAchievements(query) {
  return request({
    url: "/screen/vulnerability/selectLatestMonitoringResultAchievements",
    method: "get",
    params: query,
  });
}
// 低频扫描状态-监测对象数量
export function selectJianCeDuixNum(query) {
  return request({
    url: "/screen/vulnerability/selectJianCeDuixNum",
    method: "get",
    params: query,
  });
}
// 低频扫描状态-最近扫描时间
export function selectLastScanTime(query) {
  return request({
    url: "/screen/vulnerability/selectLastScanTime",
    method: "get",
    params: query,
  });
}
// 低频扫描状态-最新扫描成果
export function selectLastScanResult(query) {
  return request({
    url: "/screen/vulnerability/selectLastScanResult",
    method: "get",
    params: query,
  });
}
// 低频扫描状态-下次扫描计划
export function selectLastScanPlan(query) {
  return request({
    url: "/screen/vulnerability/selectLastScanPlan",
    method: "get",
    params: query,
  });
}
// 深度测试状态-监测系统数量
export function selectMonitorSysTemNum(query) {
  return request({
    url: "/screen/vulnerability/selectMonitorSysTemNum",
    method: "get",
    params: query,
  });
}
// 深度测试状态-最新测试时间
export function selectLastTestTime(query) {
  return request({
    url: "/screen/vulnerability/selectLastTestTime",
    method: "get",
    params: query,
  });
}
// 深度测试状态-下次测试计划
export function selectNextTestPlan(query) {
  return request({
    url: "/screen/vulnerability/selectNextTestPlan",
    method: "get",
    params: query,
  });
}
// 深度测试状态-最新测试成果
export function selectLastTestResults(query) {
  return request({
    url: "/screen/vulnerability/selectLastTestResults",
    method: "get",
    params: query,
  });
}
// 漏洞类型排名-业务漏洞
export function selectBusinessTypeRankingList(query) {
  return request({
    url: "/screen/vulnerability/selectBusinessTypeRankingList",
    method: "get",
    params: query,
  });
}
// 漏洞类型排名-基础漏洞
export function selectResourceTypeRankingList(query) {
  return request({
    url: "/screen/vulnerability/selectResourceTypeRankingList",
    method: "get",
    params: query,
  });
}
// 待处置漏洞分布-高频监测
export function selectBusinessVulnStatisticst(query) {
  return request({
    url: "/screen/vulnerability/selectBusinessVulnStatistics",
    method: "get",
    params: query,
  });
}
// 待处置漏洞分布-低频监测
export function selectResourceVulnStatistics(query) {
  return request({
    url: "/screen/vulnerability/selectResourceVulnStatistics",
    method: "get",
    params: query,
  });
}
// 无主漏洞分布
export function selectNoPersonVuln(query) {
  return request({
    url: "/screen/vulnerability/selectNoPersonVulnDistributionAnalysisList",
    method: "get",
    params: query,
  });
}
// 资产漏洞整改率
export function selectRectificationRateList(query) {
  return request({
    url: "/screen/vulnerability/selectRectificationRateList",
    method: "get",
    params: query,
  });
}
// 资产漏洞整改率
export function selectAssetsVulnConstituteList(query) {
  return request({
    url: "/screen/vulnerability/selectAssetsVulnConstituteList",
    method: "get",
    params: query,
  });
}
