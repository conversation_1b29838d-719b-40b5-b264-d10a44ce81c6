import request from "@/plugins/request";
// 威胁态势大屏

// CPU性能监测
export function selectCpuList(query) {
  return request({
    url: "/screen/infrastructure/selectCpuPerforManceList",
    method: "get",
    params: query,
  });
}

// 内存性能监测
export function selectMemoryList(query) {
  return request({
    url: "/screen/infrastructure/selectMemoryPerforManceList",
    method: "get",
    params: query,
  });
}

// 存储资源用量监控
export function selectStorageList(query) {
  return request({
    url: "/screen/infrastructure/selectStorageResourcesList",
    method: "get",
    params: query,
  });
}
// 日志入库状态监控
export function selectLogWareList(query) {
  return request({
    url: "/screen/infrastructure/selectLogWarehousingStatusMonitoringList",
    method: "get",
    params: query,
  });
}
// 30天日均安全日志数量
export function getThirtyDayNum(query) {
  return request({
    url: "/screen/infrastructure/selectThirtyDayRiJunLogNum",
    method: "get",
    params: query,
  });
}
// 近24小时日志总量
export function selecTtwentyFourLogNum(query) {
  return request({
    url: "/screen/infrastructure/selecTtwentyFourLogNum",
    method: "get",
    params: query,
  });
}
// 近24小时告警数量
export function selecTtwentyFourAlertNum(query) {
  return request({
    url: "/screen/infrastructure/selecTtwentyFourAlertNum",
    method: "get",
    params: query,
  });
}
// 安全设备引擎/引擎运行情况
export function selectLeiDaTuData(query) {
  return request({
    url: "/screen/infrastructure/selectLeiDaTuData",
    method: "get",
    params: query,
  });
}
// 本周提报事件挨着的多条数据
export function selectDataList(query) {
  return request({
    url: "/screen/infrastructure/selectDataList",
    method: "get",
    params: query,
  });
}
// 日志源状态监控
export function selectLogList(query) {
  return request({
    url: "/screen/infrastructure/selectLogWareHousingStatusList",
    method: "get",
    params: query,
  });
}
// 查询fw、waf、ids、ips、蜜罐、edr、其他类型运行情况
export function selectEngineEquipmentRunStatus(query) {
  return request({
    url: "/screen/infrastructure/selectEngineEquipmentRunStatus",
    method: "get",
    params: query,
  });
}
// SIEM
export function selectSiemTotalNumList(query) {
  return request({
    url: "/screen/infrastructure/selectSiemTotalNumList",
    method: "get",
    params: query,
  });
}
// 根据传入的数据类型返回异常的设备名称
export function deviceStatus(query) {
  return request({
    url: "/screen/infrastructure/selectEngineEquipmentYiChangeRunStatus",
    method: "get",
    params: query,
  });
}
