import request from "@/plugins/request";
/* 运营基础设施架构 */
/* CPU性能监控 */
export function getSelectCpuPerformanceList(data) {
  return request({
    url: "/newScreen/infrastructure/selectCpuPerformanceList",
    method: "get",
    params: data,
  });
}
/* 内存性能监控 */
export function getSelectMemoryPerformanceList(data) {
  return request({
    url: "/newScreen/infrastructure/selectMemoryPerformanceList",
    method: "get",
    params: data,
  });
}
/* 存储资源用量监控 */
export function getSelectStorageResourcesList(data) {
  return request({
    url: "/newScreen/infrastructure/selectStorageResourcesList",
    method: "get",
    params: data,
  });
}
/* 24小时告警量 */
export function getSelectTwentyFourAlertNum(data) {
  return request({
    url: "/newScreen/infrastructure/selectTwentyFourAlertNum",
    method: "get",
    params: data,
  });
}
/* 安全设备引擎/引擎运行情况 */
export function getSelectEngineOperation(data) {
  return request({
    url: "/newScreen/infrastructure/selectEngineOperation",
    method: "get",
    params: data,
  });
}
/* 30天日均安全日志数量 */
export function getSelectThirtyDayRiJunLogNum(data) {
  return request({
    url: "/newScreen/infrastructure/selectThirtyDayRiJunLogNum",
    method: "get",
    params: data,
  });
}
/* 近24小时日志总量 */
export function getSelectTwentyFourLogNum(data) {
  return request({
    url: "/newScreen/infrastructure/selectTwentyFourLogNum",
    method: "get",
    params: data,
  });
}
/* 日志源状态 */
export function getSelectLogWareHousingStatusList(data) {
  return request({
    url: "/newScreen/infrastructure/selectLogWareHousingStatusList",
    method: "get",
    params: data,
  });
}
/* 日志入库状态监控 */
export function getSelectLogWarehousingStatusMonitoringList(data) {
  return request({
    url: "/newScreen/infrastructure/selectLogWarehousingStatusMonitoringList",
    method: "get",
    params: data,
  });
}
/* 查询fw、ids、蜜罐、edr运行情况 */
export function getSelectEngineEquipmentRunStatus(data) {
  return request({
    url: "/newScreen/infrastructure/selectEngineEquipmentRunStatus",
    method: "get",
    params: data,
  });
}
/* 根据传入的数据类型返回异常的设备名称 */
export function getSelectEngineEquipmentYiChangeRunStatus(data) {
  return request({
    url: "/newScreen/infrastructure/selectEngineEquipmentYiChangeRunStatus",
    method: "get",
    params: data,
  });
}
/* seim数量 */
export function getSelectSiemTotalNumList(data) {
  return request({
    url: "/newScreen/infrastructure/selectSiemTotalNumList",
    method: "get",
    params: data,
  });
}
/* 本周提报事件挨着的多条数据 */
export function getSelectDataList(data) {
  return request({
    url: "/newScreen/infrastructure/selectDataList",
    method: "get",
    params: data,
  });
}
