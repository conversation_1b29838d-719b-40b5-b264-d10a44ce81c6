import request from "@/plugins/request";
// 脆弱性大屏
// 漏洞修复率以及漏洞总数
export function selectVulnRepairRateAndVulnSum() {
  return request({
    url: "/screen/vulnerability/selectVulnRepairRateAndVulnSum",
    method: "get",
  });
}
// 查询中高紧急漏洞关系
export function selectAssetsVulnRelationData() {
  return request({
    url: "/screen/vulnerability/selectAssetsVulnRelationData",
    method: "get",
  });
}
// 漏洞整改率以及漏洞类型分布
export function selectAssetsVulnConstituteData(assetsId) {
  return request({
    url: "/screen/vulnerability/selectAssetsVulnConstituteData?assetsId=" + assetsId,
    method: "get",
  });
}
// 应用漏洞个数，系统漏洞个数等
export function selectStatisticsVulnData() {
  return request({
    url: "/screen/vulnerability/selectStatisticsVulnData",
    method: "get",
  });
}
// 漏洞实时监测
export function selectVulnRealTimeMonitoring() {
  return request({
    url: "/screen/vulnerability/selectVulnRealTimeMonitoring",
    method: "get",
  });
}
// 漏洞类型top5

export function selectVulnTypeTop5() {
  return request({
    url: "/screen/vulnerability/selectVulnTypeTop5",
    method: "get",
  });
}
// 紧急漏洞top5
export function selectUrgentVulnTop5() {
  return request({
    url: "/screen/vulnerability/selectUrgentVulnTop5",
    method: "get",
  });
}
// 近30天告警
export function selectUrgentVulnCount() {
  return request({
    url: "/screen/vulnerability/selectUrgentVulnCount",
    method: "get",
  });
}
