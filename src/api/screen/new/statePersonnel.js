import request from "@/plugins/request";
/* 运营质量态势 */
/* 查询雷达图 */
export function getSelectLeiDaTuData(data) {
  return request({
    url: "/newScreen/personStatus/selectLeiDaTuData",
    method: "get",
    params: data,
  });
}

/* 分析师任务执行及时率列表 */
export function getSelectTaskExecutionRate(data) {
  return request({
    url: "/newScreen/personStatus/selectTaskExecutionRate",
    method: "get",
    params: data,
  });
}

/* 分析师MTTA统计 */
export function getSelectAnalystsMttaStatistics(data) {
  return request({
    url: "/newScreen/personStatus/selectAnalystsMttaStatistics",
    method: "get",
    params: data,
  });
}

/* 分析师MTTD统计 */
export function getSelectAnalystsMttdStatistics(data) {
  return request({
    url: "/newScreen/personStatus/selectAnalystsMttdStatistics",
    method: "get",
    params: data,
  });
}

/* 查询一二线分析师及服务该客户的其他分析师 */
export function getSelectAnalysts(data) {
  return request({
    url: "/newScreen/personStatus/selectAnalysts",
    method: "get",
    params: data,
  });
}

/* 查询分析师数据 */
export function getSelectAnalystsData(data) {
  return request({
    url: "/newScreen/personStatus/selectAnalystsData",
    method: "get",
    params: data,
  });
}
