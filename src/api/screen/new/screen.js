import request from "@/plugins/request";
// 本地告警排名
export function selectAlertByTitleCount() {
  return request({
    url: "/newScreen/event/alert/selectAlertByTitleCount",
    method: "get",
  });
}
// 近30天告警量
export function selectNearlyThirtyDaysAlert() {
  return request({
    url: "/newScreen/event/alert/selectNearlyThirtyDaysAlert",
    method: "get",
  });
}
// 告警监测走势图
export function selectAlertTrend() {
  return request({
    url: "/newScreen/event/alert/selectAlertTrend",
    method: "get",
  });
}
// 告警监测走势图
export function selectAnalyzedAlert() {
  return request({
    url: "/newScreen/event/alert/selectAnalyzedAlert",
    method: "get",
  });
}
// 攻防对抗
export function selectNearlyTenAlert() {
  return request({
    url: "/newScreen/event/alert/selectNearlyTenAlert",
    method: "get",
  });
}
// 近30天安全事件总数
export function selectNearlyThirtyDaysEvent() {
  return request({
    url: "/newScreen/event/alert/selectNearlyThirtyDaysEvent",
    method: "get",
  });
}
// 事件处置率
export function selectEventHandlingRate() {
  return request({
    url: "/newScreen/event/alert/selectEventHandlingRate",
    method: "get",
  });
}
// 事件信息
export function selectEventHandDetail() {
  return request({
    url: "/newScreen/event/alert/selectEventHandDetail",
    method: "get",
  });
}
// 24H攻击流量趋势图
export function getAttackedEsCount() {
  return request({
    url: "/newScreen/event/alert/getAttackedEsCount",
    method: "get",
  });
}
//攻击次数、攻击者、受攻击服务器
export function getSeimEsCount() {
  return request({
    url: "/newScreen/event/alert/getSeimEsCount",
    method: "get",
  });
}
//近30天日志类型列表（攻击日志列表）

export function getSeimEsNameTop() {
  return request({
    url: "/newScreen/event/alert/getSeimEsNameTop",
    method: "get",
  });
}
//根据天数查询日志数量（攻击曲线图）
export function getSeimTimeCount(params) {
  return request({
    url: "/newScreen/event/alert/getSeimTimeCount",
    method: "get",
    params: params,
  });
}
// 中央地图ES日志攻击数据
export function getThreatOperationMapAttacked() {
  return request({
    url: "/newScreen/event/alert/getThreatOperationMapAttacked",
    method: "get",
  });
}
// 攻击类型TOP5
export function getCeventNameCountTop() {
  return request({
    url: "/newScreen/event/alert/getCeventNameCountTop",
    method: "get",
  });
}
// 受影响业务系统TOP5
export function getCdstIpCountTop() {
  return request({
    url: "/newScreen/event/alert/getCdstIpCountTop",
    method: "get",
  });
}
