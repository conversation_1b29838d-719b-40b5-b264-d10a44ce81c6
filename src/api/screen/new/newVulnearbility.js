import request from "@/plugins/request";
// 安全漏洞
export function selectSafetyVuln() {
  return request({
    url: "/newScreen/vulnerability/selectSafetyVuln",
    method: "get",
  });
}
// 资产列表
export function selectAssetVulnDistributionAnalysisList() {
  return request({
    url: "/newScreen/vulnerability/selectAssetVulnDistributionAnalysisList",
    method: "get",
  });
}
// 漏洞整改率
export function selectRectificationRateList(params) {
  return request({
    url: "/newScreen/vulnerability/selectRectificationRateList",
    method: "get",
    params: params,
  });
}
// 资产下漏洞数据统计
export function selectAssetsVulnConstituteList(params) {
  return request({
    url: "/newScreen/vulnerability/selectAssetsVulnConstituteList",
    method: "get",
    params: params,
  });
}
// 业务系统漏洞实时监测
export function selectBusinessVulnRealTimeStatusList() {
  return request({
    url: "/newScreen/vulnerability/selectBusinessVulnRealTimeStatusList",
    method: "get",
  });
}
// 漏洞数量
export function selectVulnCategoryCount() {
  return request({
    url: "/newScreen/vulnerability/selectVulnCategoryCount",
    method: "get",
  });
}
// 漏洞类型TOP5
export function selectVulnTypeRankingList() {
  return request({
    url: "/newScreen/vulnerability/selectVulnTypeRankingList",
    method: "get",
  });
}
// 紧急漏洞TOP5
export function selectEmergencyVulnList() {
  return request({
    url: "/newScreen/vulnerability/selectEmergencyVulnList",
    method: "get",
  });
}
// 紧急漏洞数量
export function selectEmergencyVulnCount() {
  return request({
    url: "/newScreen/vulnerability/selectEmergencyVulnCount",
    method: "get",
  });
}
