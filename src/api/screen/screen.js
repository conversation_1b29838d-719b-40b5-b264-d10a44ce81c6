import request from "@/plugins/request";
// 威胁态势大屏

// 查询地图数据
export function getThreatOperationMap(query) {
  return request({
    url: "/screen/event/alert/getThreatOperationMap",
    method: "get",
    params: query,
  });
}

// 中央地图攻击情况
export function getThreatOperationMapAttacked(query) {
  return request({
    url: "/screen/event/alert/getThreatOperationMapAttacked",
    method: "get",
    params: query,
  });
}

//本日告警排名
export function alarmRankingToday(query) {
  return request({
    url: "/screen/event/alert/selectAlertByTitleCount",
    method: "get",
    params: query,
  });
}

// 告警监测走势图
export function larmMonitoringTrendChart(query) {
  return request({
    url: "/screen/event/alert/selectAlertTrend",
    method: "get",
    params: query,
  });
}
// 攻击源IP地址排名
export function attackSourceIPaddressRanking(query) {
  return request({
    url: "/screen/event/alert/getCsrcipCountTop",
    method: "get",
    params: query,
  });
}

// 近30天日志量
export function logVolume(query) {
  return request({
    url: "/screen/event/alert/getSeimEsCount",
    method: "get",
    params: query,
  });
}

// 近30天告警量
export function alarmQuantity(query) {
  return request({
    url: "/screen/event/alert/selectNearlyThirtyDaysAlert",
    method: "get",
    params: query,
  });
}

// 近30天事件量
export function eventQuantity(query) {
  return request({
    url: "/screen/event/alert/selectNearlyThirtyDaysEvent",
    method: "get",
    params: query,
  });
}

// 事件处置率
export function eventHandlingRate(query) {
  return request({
    url: "/screen/event/alert/selectEventHandlingRate",
    method: "get",
    params: query,
  });
}

// 受影响业务系统排名
export function rankingOfAffected(query) {
  return request({
    url: "/screen/event/alert/getCdstIpCountTop",
    method: "get",
    params: query,
  });
}

// 攻击类型排名
export function attackTypeRanking(query) {
  return request({
    url: "/screen/event/alert/getCeventNameCountTop",
    method: "get",
    params: query,
  });
}

// 待分析告警表
export function alarmTableToBeAnalyzed(query) {
  return request({
    url: "/screen/event/alert/selectAnalyzedAlert",
    method: "get",
    params: query,
  });
}

// 事件处置详情
export function selectEventHandDetail(query) {
  return request({
    url: "/screen/event/alert/selectEventHandDetail",
    method: "get",
    params: query,
  });
}

// 近一日受攻击态势
export function getAttackedEsCount(query) {
  return request({
    url: "/screen/event/alert/getAttackedEsCount",
    method: "get",
    params: query,
  });
}

// 实时日志状态
export function getRealTimeLogStatus(query) {
  return request({
    url: "/screen/event/alert/getRealTimeLogStatus",
    method: "get",
    params: query,
  });
}

export function checkssp(query) {
  return request({
    url: "/screen/getVerifyRedis",
    method: "get",
    params: query,
  });
}

//攻防对抗
export function getCustomerEventsNewestData(query) {
  return request({
    url: "/screen/event/alert/getCustomerEventsNewestData",
    method: "get",
    params: query,
  });
}

//事件相关信息
export function getEventsNewestData(query) {
  return request({
    url: "/screen/event/alert/getEventsNewestData",
    method: "get",
    params: query,
  });
}

//攻击类型排名
export function getCeventTypeTop(query) {
  return request({
    url: "/screen/event/alert/getCeventTypeTop",
    method: "get",
    params: query,
  });
}

//攻击相关数据
export function getAttackNewData(query) {
  return request({
    url: "/screen/event/alert/getAttackNewData",
    method: "get",
    params: query,
  });
}

//获取本日告警排名下拉
export function getViewAlertNum(query) {
  return request({
    url: "/screen/event/alert/getViewAlertNum",
    method: "get",
    params: query,
  });
}
