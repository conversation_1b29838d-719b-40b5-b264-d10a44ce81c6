import Axios from "@/plugins/request";

// 登录方法

export function login(params) {
  return Axios.post("/auth/login", params);
}
// 刷新方法
export function refreshToken() {
  return Axios({
    url: "/auth/refresh",
    method: "post",
  });
}

// 获取用户详细信息
export function getInfo() {
  return Axios({
    url: "/system/user/getInfo",
    method: "get",
  });
}

// 退出方法
export function logout() {
  return Axios({
    url: "/auth/logout",
    method: "delete",
  });
}

// 获取验证码
export function getCodeImg() {
  return Axios({
    url: "/code",
    method: "get",
  });
}

//获取授权状态（在login方法后调用）
export function getChangeLicense() {
  return Axios({
    url: `/${import.meta.env.VITE_IS_SIME ? "auth" : "system"}/license/getChangeLicense`,
    method: "get",
  });
}

//获取登陆页面的错误提示信息
export function geterrorInfo() {
  return Axios({
    url: `/${import.meta.env.VITE_IS_SIME ? "auth" : "system"}/license/errorInfo`,
    method: "get",
  });
}

//密码修改
export function changePassword(params) {
  return Axios({
    url: "/system/user/changePassword",
    method: "put",
    data: params,
  });
}

//logo获取
export function getLogo(params) {
  return Axios({
    url: "/system/logo",
    method: "get",
    params: params,
  });
}

/* 前哨 - 获取title */
export function getSysTitle() {
  return Axios({
    url: "/system/sysTitle/getSysTitle",
    method: "get",
  });
}
