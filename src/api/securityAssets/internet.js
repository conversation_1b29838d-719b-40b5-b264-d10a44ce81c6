import request from "@/plugins/request";
// 互联网暴露信息-查询ip组
export function selectIpGroups(query) {
  return request({
    url: "/system/assetsExposeIp/selectIpGroups",
    method: "get",
    params: query,
  });
}
// 互联网暴露信息-以IP为单位查询左侧列表
export function selectAllIps(query) {
  return request({
    url: "/system/assetsExposeIp/selectAllIps",
    method: "get",
    params: query,
  });
}
// 互联网暴露信息-根据ip组查询ip
export function selectIpByGroup(query) {
  return request({
    url: "/system/assetsExposeIp/selectIpByGroup",
    method: "get",
    params: query,
  });
}
// 互联网暴露信息-根据ip查询端口列表
export function selectPortByIp(query) {
  return request({
    url: "/system/assetsExposeIp/selectPortByIp",
    method: "get",
    params: query,
  });
}
// 互联网暴露信息-查询域名组
export function selectDomainGroups(query) {
  return request({
    url: "/system/assetsExposeDomain/selectDomainGroups",
    method: "get",
    params: query,
  });
}
// 互联网暴露信息-跟组域名组查询域名列表
export function selectDomainByGroup(query) {
  return request({
    url: "/system/assetsExposeDomain/selectDomainByGroup",
    method: "get",
    params: query,
  });
}
// 互联网暴露资产
export function selectAssetCount(query) {
  return request({
    url: "/system/assetsExposeDomain/selectAssetCount",
    method: "get",
    params: query,
  });
}
