//终端资产
import request from "@/plugins/request";

// 查询终端资产列表
export function getTerminallist(params) {
  return request.get(`/system/assetsTerminal/list`, { params });
}

//新增
export function addTerminal(params) {
  return request.post(`/system/assetsTerminal/add`, params);
}

//查询详情
export function getTerminalDetail(params) {
  return request.get(`/system/assetsTerminal/detail/${params.id}`);
}

//删除资产
export function deleteTerminal(params) {
  return request.delete(`/system/assetsTerminal/deleteTerminal`, { data: params });
}

//修改IP
export function saveAssetsTerminalIp(params) {
  return request.put(`/system/assetsTerminal/saveAssetsTerminalIp`, params);
}
//添加IP
export function saveAddTerminalIp(params) {
  return request.post(`/system/assetsTerminal/saveAddTerminalIp`, params);
}

//删除IP
export function deleteTerminalIp(params) {
  return request.delete(`/system/assetsTerminal/deleteTerminalIp`, { params: {}, data: params });
}
//转移IP
export function transferTerminalIp(params) {
  return request.put(`/system/assetsTerminal/transferTerminalIp`, params);
}

//修改终端资产信息
export function updateTerminal(params) {
  return request.put(`/system/assetsTerminal/update`, params);
}
// 查询关联和未关联事件列表
export function getTerminalEventList(params) {
  return request.get(`/system/assetsTerminal/getTerminalEventList`, { params });
}
