import request from "@/plugins/request";
// 查询待确认资产-查询域名类待确认资产列表
export function getAssetsConfirmedList(query) {
  return request({
    url: "/system/assetsConfirm/getAssetsConfirmedList",
    method: "get",
    params: query,
  });
}
// 查询待确认资产-查询IP类待确认资产列表
export function getAssetsIpConfirmedList(query) {
  return request({
    url: "/system/assetsConfirm/getAssetsIpConfirmedList",
    method: "get",
    params: query,
  });
}
// 查询ip类详情
export function openIpNotConfirm(query) {
  return request({
    url: "/system/assetsConfirm/openIpNotConfirm",
    method: "get",
    params: query,
  });
}
// 查询URL类详情
export function openDomainNotConfirm(query) {
  return request({
    url: "/system/assetsConfirm/openDomainNotConfirm",
    method: "get",
    params: query,
  });
}

//忽略URl类待确认资产
export function ignoreDomainNotConfirmAsset(query) {
  return request({
    url: "/system/assetsConfirm/ignoreDomainNotConfirmAsset",
    method: "DELETE",
    data: query,
  });
}
//忽略URl类待确认资产
export function ignoreIpNotConfirmAsset(query) {
  return request({
    url: "/system/assetsConfirm/ignoreIpNotConfirmAsset",
    method: "DELETE",
    data: query,
  });
}
// 与现有业务系统资产合并
export function changeDomainNotConfirmAssets(query) {
  return request({
    url: "/system/assetsConfirm/changeDomainNotConfirmAssets",
    method: "post",
    data: query,
  });
}
// 与现有与计算设备资产合并
export function changeIpNotConfirmAssets(query) {
  return request({
    url: "/system/assetsConfirm/changeIpNotConfirmAssets",
    method: "post",
    data: query,
  });
}
// 与终端资产合并
export function changeIpNotConfirmTerminalAssets(query) {
  return request({
    url: "/system/assetsConfirm/changeIpNotConfirmTerminalAssets",
    method: "post",
    data: query,
  });
}
// 统计总数
export function selectNotConfirmCount() {
  return request({
    url: "/system/assetsConfirm/selectNotConfirmCount",
    method: "get",
  });
}
