//计算资产
import request from "@/plugins/request";

//获取基础软件列表
export function selectSoftListByPage(params) {
  return request.get(`/system/basicResourceSoftware/selectSoftListByPage`, { params });
}

//获取基础软件类型列表
export function selectSoftTypeListByPage(params) {
  return request.get(`/system/assetsBasic/selectSoftType`, { params });
}

// 列表查询
export function getAssetsResourcesList(query) {
  return request({
    url: "/system/assetsBasic/getAssetsResourcesList",
    method: "get",
    params: query,
  });
}
// 查询详情
export function assetsDetail(params) {
  return request.get(`/system/assetsBasic/assetsDetail`, { params });
}

//修改详情
export function saveEditAssetsResource(params) {
  return request.put(`/system/assetsBasic/saveEditAssetsResource`, params);
}

//新增-计算设备资产

export function saveBasic(params) {
  return request.post(`/system/assetsBasic/saveBasic`, params);
}

//保存组件
export function saveAssemblyByAssemblyName(params) {
  return request.put(`/system/assembly/saveAssemblyByAssemblyName`, params);
}

//保存组件关联关系
export function insertAssemblys(params) {
  return request.put(`/system/assetsBasic/insertAssemblys`, params);
}
// 查询已整改漏洞和已处置事件
export function eventAndVuln(params) {
  return request.get(`/system/assetsBasic/selectResourceDoneEventAndVuln`, { params });
}
// 查询查询关联的业务系统资产
export function getRalationBusiness(params) {
  return request.get(`/system/assetsBasic/getRalationBusiness`, { params });
}
// 查询左侧ip端口服务协议
export function getLeftResources(params) {
  return request.get(`/system/assetsBasic/getLeftResources`, { params });
}
// 查询未关联的业务系统资产
export function getRightBusiness(params) {
  return request.get(`/system/assetsBusiness/getRightBusiness`, { params });
}
// 关联业务系统资产
export function saveRalationBusinesse(data) {
  return request({
    url: "/system/assetsBasic/saveRalationBusiness",
    method: "PUT",
    data: data,
  });
}

//修改ip端口
export function saveAssetsResourceServer(params) {
  return request.put(`/system/assetsBasic/saveAssetsResourceServer`, params);
}
//添加ip端口
export function saveBasicPortServer(params) {
  return request.put(`/system/assetsBasic/saveBasicPortServer`, params);
}

//删除ip端口
export function deleteAssetsResourceServer(params) {
  return request.delete(`/system/assetsBasic/deleteAssetsResourceServer`, { params: {}, data: params });
}

//转移ip
export function transferIpApi(params) {
  return request.put(`/system/assetsBasic/transferIp`, params);
}

// 取消关联业务系统资产
export function deleteRalationBusiness(query) {
  return request({
    url: "/system/assetsBasic/deleteRalationBusiness",
    method: "DELETE",
    data: query,
  });
}

//添加，修改基础资源软件
export function saveSoftware(params) {
  return request.put(`/system/assetsBasic/saveSoftware`, params);
}

//删除基础资源软件
export function deleteSoftware(params) {
  return request.delete(`/system/assetsBasic/deleteSoftware`, { params: {}, data: params });
}

//删除组件
export function deleteAssemblys(params) {
  return request.delete(`/system/assetsBasic/deleteAssemblys`, { params: {}, data: params });
}

//删除资产
export function deleteAssetsResources(params) {
  return request.delete(`/system/assetsBasic/deleteResource`, { data: params });
}
// 查询关联业务系统资产分页
export function getRelevantBusiness(params) {
  return request.get(`/system/assetsBasic/getRelevantBusiness`, { params });
}
// 计算设备关系图
export function getResourcesEchartsInfo(query) {
  return request({
    url: "/system/assetsBasic/getResourcesEchartsInfo",
    method: "get",
    params: query,
  });
}
