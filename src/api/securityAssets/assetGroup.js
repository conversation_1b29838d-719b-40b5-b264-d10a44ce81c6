import request from "@/plugins/request";

// 查列表
export function selectPage(query) {
  return request({
    url: "/system/assetsGroup/selectPage",
    method: "get",
    params: query,
  });
}
// 资产组-校验资产组名称是否存在
export function checkName(query) {
  return request({
    url: "/system/assetsGroup/checkName",
    method: "post",
    data: query,
  });
}
// 新增资产组
export function saveForm(params) {
  return request({
    url: "/system/assetsGroup/saveForm",
    method: "put",
    data: params,
  });
}
// 关联列表
// 新建资产组查关联业务系统对象列表
export function getGroupAssetsList(query) {
  return request({
    url: "/system/assetsGroup/getGroupAssetsBusinessList",
    method: "get",
    params: query,
  });
}
// 新建资产组查询关联计算设备资产列表
export function getGroupAssetsBasicList(query) {
  return request({
    url: "/system/assetsGroup/getGroupAssetsBasicList",
    method: "get",
    params: query,
  });
}
// 新建资产组查询关联的终端资产列表
export function getGroupAssetsTerminalList(query) {
  return request({
    url: "/system/assetsGroup/getGroupAssetsTerminalList",
    method: "get",
    params: query,
  });
}

// 未关联列表
// 新建资产组查未关联业务系统对象列表
export function getRightGroupBusiness(query) {
  return request({
    url: "/system/assetsGroup/getRightGroupBusiness",
    method: "get",
    params: query,
  });
}
// 新建资产组查询未关联计算设备资产列表
export function getRightGroupResource(query) {
  return request({
    url: "/system/assetsGroup/getRightGroupResource",
    method: "get",
    params: query,
  });
}
// 新建资产组查询未关联终端资产列表
export function getRightGroupTerminal(query) {
  return request({
    url: "/system/assetsGroup/getRightGroupTerminal",
    method: "get",
    params: query,
  });
}
// 资产组关联业务系统资产
export function saveAddBusiness(params) {
  return request({
    url: "/system/assetsGroup/saveAddBusiness",
    method: "put",
    data: params,
  });
}
// 关联计算设备资产
export function saveAddResource(params) {
  return request({
    url: "/system/assetsGroup/saveAddResource",
    method: "put",
    data: params,
  });
}
// 关联终端资产
export function saveAddTerminal(params) {
  return request({
    url: "/system/assetsGroup/saveAddTerminal",
    method: "put",
    data: params,
  });
}
// 删除资产组
export function delAssetsGroup(query) {
  return request({
    url: "/system/assetsGroup/delAssetsGroup",
    method: "DELETE",
    data: query,
  });
}
// 删除业务系统对象
export function delAssetsGroupBusinessRel(query) {
  return request({
    url: "/system/assetsGroup/delAssetsGroupBusinessRel",
    method: "DELETE",
    data: query,
  });
}
// 删除计算设备资产
export function delAssetsGroupResourceRel(query) {
  return request({
    url: "/system/assetsGroup/delAssetsGroupResourceRel",
    method: "DELETE",
    data: query,
  });
}
// 删除终端资产
export function delAssetsGroupTerminalRel(query) {
  return request({
    url: "/system/assetsGroup/delAssetsGroupTerminalRel",
    method: "DELETE",
    data: query,
  });
}
// 回显接口
export function selectGroupById(query) {
  return request({
    url: "/system/assetsGroup/selectGroupById",
    method: "get",
    params: query,
  });
}
// 查询已整改和未整改漏洞列表
export function getResourceVulnList(query) {
  return request({
    url: "/system/assetsBasic/getResourceVulnList",
    method: "get",
    params: query,
  });
}
// 查询已整改和未整改事件列表
export function getResourceEventList(query) {
  return request({
    url: "/system/assetsBasic/getResourceEventList",
    method: "get",
    params: query,
  });
}
