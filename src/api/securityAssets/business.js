import request from "@/plugins/request";

// 根据责任主体查询责任人
export function getCanSelectPersonByDept(params) {
  return request.get(`/system/assetsBusiness/getCanSelectPersonByDept`, { params });
}

// 列表查询
export function queryList(query) {
  return request({
    url: "/system/assetsBusiness/list",
    method: "get",
    params: query,
  });
}

//新增
export function addAssetsBusiness(params) {
  return request.post(`/system/assetsBusiness/add`, params);
}
// 查看详情
export function getdetail(query) {
  return request({
    url: "/system/assetsBusiness/detail/" + query.id,
    method: "get",
  });
}

//获取详情
export function getDetailBusiness(params) {
  return request.get(`/system/assetsBusiness/detail/${params.id}`, { params });
}

//修改基本信息
export function updateBusiness(params) {
  return request.put(`/system/assetsBusiness/update`, params);
}

//修改ip端口
export function saveEditPortServer(params) {
  return request.put(`/system/assetsBusiness/saveEditPortServer`, params);
}
//添加ip端口
export function savePortServer(params) {
  return request.post(`/system/assetsBusiness/savePortServer`, params);
}

//删除ip端口
export function deletePortServer(params) {
  return request.delete(`/system/assetsBusiness/deletePortServer`, { params: {}, data: params });
}

//修改域名
export function saveEditDomain(params) {
  return request.put(`/system/assetsBusiness/saveEditDomain`, params);
}
//添加域名
export function saveDomain(params) {
  return request.post(`/system/assetsBusiness/saveDomain`, params);
}

//删除域名
export function deleteDomain(params) {
  return request.delete(`/system/assetsBusiness/deleteDomain`, { params: {}, data: params });
}
//转移域名
export function transferDomain(params) {
  return request.put(`/system/assetsBusiness/transferDomain`, params);
}
// 关联基础资源的弹窗左边数据
export function getBusinessLeftList(query) {
  return request({
    url: "/system/assetsBusiness/getBusinessLeftList",
    method: "get",
    params: query,
  });
}
// 关联基础资源的弹窗右边数据
export function getRightResources(query) {
  return request({
    url: "/system/assetsBusiness/getRightResources",
    method: "get",
    params: query,
  });
}
// 保存业务系统关联基础资源
export function saveRalationResources(params) {
  return request.post(`/system/assetsBusiness/saveRalationResources`, params);
}
// 删除计算设备资产关联
export function deleteRalationResource(query) {
  return request({
    url: "/system/assetsBusiness/deleteRalationResource",
    method: "delete",
    data: query,
  });
}
// 查询未整改漏洞列表
export function selectAssetsUnCorrectVulnList(query) {
  return request({
    url: "/system/assetsBusiness/selectAssetsUnCorrectVulnList",
    method: "get",
    params: query,
  });
}
// 查询已整改漏洞列表
export function selectAssetsCorrectVulnList(query) {
  return request({
    url: "/system/assetsBusiness/selectAssetsCorrectVulnList",
    method: "get",
    params: query,
  });
}
// 查询处置事件列表
export function selectBusinessEventList(query) {
  return request({
    url: "/system/assetsBusiness/selectBusinessEventList",
    method: "get",
    params: query,
  });
}
// 查询已关联基础资源列表
export function selectRalationResourcesList(query) {
  return request({
    url: "/system/assetsBusiness/selectRalationResourcesList",
    method: "get",
    params: query,
  });
}

//删除资产
export function deleteAssetsBusiness(params) {
  return request.delete(`/system/assetsBusiness/deleteAssetsBusiness`, { data: params });
}
// 资产关系图
export function getBusinessEchartsInfo(query) {
  return request({
    url: "/system/assetsBusiness/getBusinessEchartsInfo",
    method: "get",
    params: query,
  });
}
