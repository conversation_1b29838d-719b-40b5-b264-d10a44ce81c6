import request from "@/plugins/request";
// 互联网IP资产数量
export function selectIpAssetNum(query) {
  return request({
    url: "/system/overview/selectIpAssetNum",
    method: "get",
    params: query,
  });
}
// 互联网域名资产数量
export function selectDomainAssetNum(query) {
  return request({
    url: "/system/overview/selectDomainAssetNum",
    method: "get",
    params: query,
  });
}
// 业务系统资产数量
export function selectBusinessSystemAssetNum(query) {
  return request({
    url: "/system/overview/selectBusinessSystemAssetNum",
    method: "get",
    params: query,
  });
}
// 计算设备资产
export function selectComputingEquipmentAssetNum(query) {
  return request({
    url: "/system/overview/selectComputingEquipmentAssetNum",
    method: "get",
    params: query,
  });
}
// 存在风险的资产
export function selectExistRiskAssetNum(query) {
  return request({
    url: "/system/overview/selectExistRiskAssetNum",
    method: "get",
    params: query,
  });
}
// IP资产风险汇总
export function selectIpAssetRiskList(query) {
  return request({
    url: "/system/overview/selectIpAssetRiskList",
    method: "get",
    params: query,
  });
}
// 风险资产分布
export function selectIpAssetRiskPieChart(query) {
  return request({
    url: "/system/overview/selectIpAssetRiskPieChart",
    method: "get",
    params: query,
  });
}
