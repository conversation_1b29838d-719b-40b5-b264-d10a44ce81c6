import Axios from "@/plugins/request";
export function selectarningAwaitingConfirmatio(id) {
  return Axios({
    url: "/system/workbench/selectarningAwaitingConfirmatio?id=" + id,
    method: "get",
  });
}
// 删除告警
export function deleteWaitingConfirm(params) {
  return Axios({
    url: "/system/workbench/deleteWaitingConfirm",
    method: "post",
    data: params,
  });
}
// 申请删除告警
export function applyWaitingConfirm(param) {
  return Axios({
    url: "/system/workbench/applyWaitingConfirm",
    method: "post",
    data: param,
  });
}

//根据时间范围删除告警
export function applyWaitingConfirmByTime(params) {
  return Axios({
    url: "system/workbench/applyWaitingConfirmByTime",
    method: "post",
    data: params,
  });
}

// 告警误报保存
export function misWarning(param) {
  return Axios({
    url: "/system/workbench/misWarning",
    method: "post",
    data: param,
  });
}
// 追加告警  获取可追加的事件列表 /system/event/getMergeList
export function getMergeList(query) {
  return Axios({
    url: "/system/event/getMergeList",
    method: "get",
    params: query,
  });
}
// 追加告警保存方法
export function saveEventAlertTask(params) {
  return Axios({
    url: "/system/workbench/saveEventAlertTask",
    method: "post",
    data: params,
  });
}
// 工作台获取所有告警列表
export function selectAllPendingWarnings(query) {
  return Axios({
    url: "/system/workbench/selectAllPendingWarnings",
    method: "get",
    params: query,
  });
}
// 驳回告警
export function rejectApplyWorkbenchAlertInfo(params) {
  return Axios({
    url: "/system/workbench/rejectApplyWorkbenchAlertInfo",
    method: "post",
    data: params,
  });
}
// 告警追溯接口
export function alertTrace(params) {
  return Axios({
    url: "/search/search/external/alert/trace",
    method: "post",
    data: params,
  });
}
// 清空接口
export function deleteAllWaitingConfirm(query) {
  return Axios({
    url: "/system/workbench/deleteAllWaitingConfirm",
    method: "post",
    data: query,
  });
}
