import Axios from "@/plugins/request";
// 服务开通情况
export function listServices() {
  return Axios({
    url: "/system/homePage/listServices",
    method: "get",
  });
}
// 已处理告警
export function getAlertDealCount(query) {
  return Axios({
    url: "/system/homePage/getAlertDealCount",
    method: "get",
    params: query,
  });
}
// 已分析安全事件
export function getEventAnalysisCount(query) {
  return Axios({
    url: "/system/homePage/getEventAnalysisCount",
    method: "get",
    params: query,
  });
}
// 高危资产
export function getIpAssetRiskNum(query) {
  return Axios({
    url: "/system/homePage/getIpAssetRiskNum",
    method: "get",
    params: query,
  });
}
// 已发现安全漏洞
export function getVulnLevelStatistics() {
  return Axios({
    url: "/system/homePage/getVulnLevelStatistics",
    method: "get",
  });
}
// 服务得分
export function getServiceScore() {
  return Axios({
    url: "/system/homePage/getServiceScore",
    method: "get",
  });
}
// 已提交报告数量
export function getReportCount() {
  return Axios({
    url: "/system/homePage/getReportCount",
    method: "get",
  });
}
// 查询客户服务配置信息
export function selectServiceStatus() {
  return Axios({
    url: "/system/homePage/selectServiceStatus",
    method: "get",
  });
}
// 折线图
export function getVulnHighOrAboveSummary() {
  return Axios({
    url: "/system/homePage/getVulnHighOrAboveSummary",
    method: "get",
  });
}
// 饼图
export function getEventTypeCount() {
  return Axios({
    url: "/system/homePage/getEventTypeCount",
    method: "get",
  });
}
// 年月日饼图
export function getAlertTypeCount(query) {
  return Axios({
    url: "/system/homePage/getAlertTypeCount",
    method: "get",
    params: query,
  });
}

//告警管理接口
export function getworkbenchList(query) {
  console.log(query);
  return Axios({
    url: "/system/workbench/list",
    method: "get",
    params: query,
  });
}
// 查看详情
export function alertDetail(query) {
  return Axios({
    url: "/system/workbench/alertDetail",
    method: "get",
    params: query,
  });
}
//查看误报原因、
export function goMisreportAlertDetail(query) {
  return Axios({
    url: "/system/event/goMisreportAlertDetail",
    method: "get",
    params: query,
  });
}
//查看合并告警
export function getMergedList(query) {
  return Axios({
    url: "/system/alert/getMergedList",
    method: "get",
    params: query,
  });
}
/* 查询分析--追溯按钮--获取告警id */
export function alertDetailByAlertNo(query) {
  return Axios({
    url: "/system/alert/getMergedIdByAlertNo",
    method: "get",
    params: query,
  });
}
/* 查询分析--追溯按钮 isoss */
export function alertDetailByIsossAlertNo(query) {
  return Axios({
    url: "/alertEvent/alert/getMergedIdByAlertNo",
    method: "get",
    params: query,
  });
}
