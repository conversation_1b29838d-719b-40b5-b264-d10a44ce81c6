import Axios from "@/plugins/request";
//保存编辑后的echart设置
export function editEchartOption(params) {
  return Axios({
    url: "/system/homePage/editEchartOption",
    method: "post",
    data: params,
  });
}
//保存编辑后的echart设置
export function getEchartOption(id) {
  return Axios({
    url: "/system/homePage/getEchartOption?echartId=" + id,
    method: "get",
  });
}
//  还原配置
export function resetEditChart(id) {
  return Axios({
    url: "/system/homePage/delEchartOption/" + id,
    method: "delete",
  });
}
