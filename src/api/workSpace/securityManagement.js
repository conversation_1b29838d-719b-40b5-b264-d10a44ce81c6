//计算资产
import request from "@/plugins/request";

// 获取数据
export function selectWorkBenchData(params) {
  return request.get(`/system/workbench/selectWorkBenchData`, { params });
}

//   漏洞统计
export function assetsAndVulnStatistics(params) {
  return request.get(`/system/workbench/assetsAndVulnStatistics`, { params });
}
//   线形图数据
export function noPersonAssets(params) {
  return request.get(`/system/workbench/noPersonAssets`, { params });
}
//  待处置事件任务
export function getEventTaskStay(params) {
  return request.get(`/system/workbench/getEventTaskStay`, { params });
}
// 分析中相关事件
export function getAnalysisingEvent(params) {
  return request.get(`/system/workbench/getAnalysisingEvent`, { params });
}
// 待处置漏洞
export function selectStayVulnList(params) {
  return request.get(`/system/workbench/selectStayVulnList`, { params });
}
