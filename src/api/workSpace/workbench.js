import Axios from "@/plugins/request";
// 统计数据
export function selectWorkBenchData() {
  return Axios({
    url: "/system/workbench/selectWorkBenchData",
    method: "get",
  });
}
// 一线 二线待确认告警
export function getWarningList() {
  return Axios({
    url: "/system/workbench/getWarningList",
    method: "get",
  });
}
// 获取时间间隔 /system/workbench/getNoticeiIterval
export function getNoticeiIterval(type) {
  return Axios({
    url: "/system/workbench/getNoticeiIterval?type=" + type,
    method: "get",
  });
}
// 一线二线待确认资产
export function selectAssets() {
  return Axios({
    url: "/system/workbench/selectAssets",
    method: "get",
  });
}
// 三线工作台 风险处置任务
export function selectMoreTask() {
  return Axios({
    url: "/system/workbench/selectMoreTask",
    method: "get",
  });
}
// 三线抽检事件  详情
export function selectEventById(eventId) {
  return Axios({
    url: "/system/workbench/selectEventById?eventId=" + eventId,
    method: "get",
  });
}
// 抽检事件中的任务
export function getAuditedTaskList(params) {
  return Axios({
    url: "/system/workbench/getAuditedTaskList",
    method: "get",
    params: params,
  });
}
// 抽检事件中的任务详情 三线工作台
export function selectOpenSamplingTask(taskId) {
  return Axios({
    url: "/system/workbench/selectOpenSamplingTask?taskId=" + taskId,
    method: "get",
  });
}
// 已申请删除的告警
export function selectApplyAlertList(taskId) {
  return Axios({
    url: "/system/workbench/selectApplyAlertList",
    method: "get",
  });
}
// 分页查看申请删除的告警
export function selectApplyAlertPage(param) {
  return Axios({
    url: "/system/workbench/selectApplyAlertPage",
    method: "get",
    params: param,
  });
}
// 抽检事件  审核任务
export function saveSamplingTaskAudit(param) {
  return Axios({
    url: "/system/workbench/saveSamplingTaskAudit",
    method: "post",
    params: param,
  });
}
// 交付经理资产审核列表
export function selectAuditAssetList() {
  return Axios({
    url: "/system/workbench/selectAuditAssetList",
    method: "get",
  });
}
//交付经理资产确认列表
export function selectNotConfirmAssetsList() {
  return Axios({
    url: "/system/workbench/selectNotConfirmAssetsList",
    method: "get",
  });
}
// 交付经理  获取执行人和参与人
export function selectWorkBenchUserTree() {
  return Axios({
    url: "/system/workbench/selectWorkBenchUserTree",
    method: "get",
  });
}
// 交付经理 查询执行人
export function loadScheduleTaskExecutorData() {
  return Axios({
    url: "/system/workbench/loadScheduleTaskExecutorData",
    method: "get",
  });
}
// 交付经理工作台 保存计划任务
export function saveScheduleTask(params) {
  return Axios.post(`/system/workbench/saveScheduleTask`, params);
}
// 交付经理工作台查询任务详情
export function selectScheduleTaskDetail(id) {
  return Axios({
    url: "/system/workbench/selectScheduleTaskDetail?id=" + id,
    method: "get",
  });
}
// 交付经理工作台删除任务
export function deleteScheduleTask(params) {
  return Axios({
    url: "/system/workbench/deleteScheduleTask",
    method: "post",
    params: params,
  });
}
// 校验任务是否能删除
export function checkCyclicNewTask(params) {
  return Axios({
    url: "/system/workbench/checkCyclicNewTask?id=" + params.id,
    method: "get",
  });
}
