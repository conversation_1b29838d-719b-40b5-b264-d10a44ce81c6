import Axios from "@/plugins/request";
//查询资产详情
export function selectAssetsByIpUuid(params) {
  return Axios({
    url: "/system/workbench/selectAssetsByIpUuid",
    method: "get",
    params,
  });
}
//新建计算设备资产
export function saveBasicAsset(params) {
  return Axios({
    url: "/system/workbench/saveBasicAsset",
    method: "post",
    data: params,
  });
}
//新建终端资产对象
export function saveWorkbenchTerminalAsset(params) {
  return Axios({
    url: "/system/workbench/saveTerminalAsset",
    method: "post",
    data: params,
  });
}
// 工作台资产忽略
export function ignoreAsset(IpUuid) {
  let param = {
    ipUuid: IpUuid,
  };
  return Axios({
    url: "/system/workbench/ignoreAsset",
    method: "post",
    data: param,
  });
}
// 合并资产获取详情内容
export function selectServiceAssetsConfirmatio(params) {
  return Axios({
    url: "/system/workbench/selectServiceAssetsConfirmatio",
    method: "get",
    params,
  });
}
//合并资产时获取 基础资源端口服务
export function getBasicPortsServic(params) {
  return Axios({
    url: "/system/assetsBasic/getBasicPortsServic",
    method: "get",
    params,
  });
}
// 工作台待确认资产 与终端资产合并
export function changeTerminalAssets(params) {
  return Axios({
    url: "/system/workbench/changeTerminalAssets",
    method: "post",
    data: params,
  });
}
// 工作台待确认资产  与计算设备资产合并
export function changeAssets(params) {
  return Axios({
    url: "/system/workbench/changeAssets",
    method: "post",
    data: params,
  });
}
// 资产审核列表
export function getAuditAsset(query) {
  return Axios({
    url: "/system/workbench/getAuditAsset",
    method: "get",
    params: query,
  });
}
//交付经理  资产审核
export function saveAssetsAudit(params) {
  return Axios({
    url: "/system/workbench/saveAssetsAudit",
    method: "post",
    data: params,
  });
}
