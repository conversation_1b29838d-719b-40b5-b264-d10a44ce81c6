import Axios from "@/plugins/request";
// 待处理事件任务
export function getNotFinishedTaskList(query) {
  return Axios({
    url: "/system/eventTask/getNotFinishedTaskList",
    method: "get",
    params: query,
  });
}
// 待认领事件任务
export function selectUnclaimedTaskPage(query) {
  return Axios({
    url: "/system/eventTask/selectUnclaimedTaskPage",
    method: "get",
    params: query,
  });
}
// 事件审核 前五条
export function selectAuditTaskList(params) {
  return Axios({
    url: "/system/workbench/selectAuditTaskList",
    method: "get",
    params,
  });
}
// 抽检事件
export function selectAuditSamplingTask() {
  return Axios({
    url: "/system/workbench/selectAuditSamplingTask",
    method: "get",
  });
}
// 工作台告警提报事件  获取模板任务数
export function getEventTemplateTaskSize(templateId) {
  return Axios({
    url: "/system/workbench/getEventTemplateTaskSize?templateId=" + templateId,
    method: "get",
  });
}
//工作台告警提报事件
export function alertsaveEvent(params) {
  return Axios({
    url: "/system/workbench/saveEvent",
    method: "post",
    data: params,
  });
}
// 工作台告警提报事件 并添加事件任务
export function saveEventAndTask(params) {
  return Axios({
    url: "/system/workbench/saveEventAndTask",
    method: "post",
    data: params,
  });
}
// 事件审核分页数据
export function selectAuditTaskPage(query) {
  return Axios({
    url: "/system/eventTask/selectAuditTaskPage",
    method: "get",
    params: query,
  });
}
// 分配的事件审核任务
export function getPreAuditList(params) {
  return Axios({
    url: "/system/event/getPreAuditList",
    method: "get",
    params: params,
  });
}

//可处理的事件审核任务
export function getUnAssignAuditList(params) {
  return Axios({
    url: "/system/event/getUnAssignAuditList",
    method: "get",
    params: params,
  });
}

//认领事件
export function claimEventAudit(eventId) {
  return Axios({
    url: "/system/event/claimEventAudit",
    method: "put",
    params: { eventId: eventId },
  });
}
