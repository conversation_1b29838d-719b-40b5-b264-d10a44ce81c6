import request from "@/plugins/request";
import { praseStrEmpty } from "@/utils/ruoyi";
/*授权管理*/
//获取系统是否有授权

export function getLicenseInfo() {
  return request({
    url: `/auth/getLicenseInfo`,
    method: "get",
  });
}
// 获取授权管理页面的相关信息
export function getCode() {
  return request({
    url: `/${import.meta.env.VITE_IS_SIME ? "auth" : "system"}/license/submitInfo`,
    method: "get",
  });
}

// 修改保存接口
export function saveCode(query) {
  return request({
    url: `/${import.meta.env.VITE_IS_SIME ? "auth" : "system"}/license/saveLicenseCode`,
    method: "post",
    data: query,
  });
}

/*通知发送方式*/
//获取列表
export function getSendTypePage(query) {
  return request({
    url: "/system/user/selectSendTypePage",
    method: "get",
    params: query,
  });
}
//改变通知发送类型
export function changeSend(query) {
  return request({
    url: "/system/user/changeSendStatus",
    method: "POST",
    data: query,
  });
}

/*漏洞知识库*/
//获取列表
export function getvulnTypemain(query) {
  return request({
    url: "/system/vulnType/getVulnTypeList",
    method: "get",
    params: query,
  });
}
// 添加  /system/vulnType/saveVulnTypeForm
export function saveVulnType(query) {
  return request({
    url: "/system/vulnType/saveVulnTypeForm",
    method: "post",
    data: query,
  });
}
// 添加  /system/vulnType/deleteVulnType
export function deleteVulnType(query) {
  return request({
    url: "/system/vulnType/deleteVulnType",
    method: "post",
    data: query,
  });
}

/* siem 获取通知 列表 */
export function getSysUserSendTypes(query) {
  return request({
    url: "/warning/user/sendType/getSysUserSendTypes",
    method: "get",
    params: query,
  });
}
/* siem 修改通知发送方式 */
export function changeSendStatusSiem(query) {
  return request({
    url: "/warning/user/sendType/changeSendStatus",
    method: "POST",
    data: query,
  });
}
