import request from "@/plugins/request";
// 查询报告总览
export function reportCount(query) {
  return request({
    url: "/system/report/reportCount",
    method: "get",
    params: query,
  });
}
//   查询报告
export function reportList(query) {
  return request({
    url: "/system/report/list",
    method: "get",
    params: query,
  });
}
// 查询事件报告
export function getEvent(query) {
  return request({
    url: "/system/report/getEvent",
    method: "get",
    params: query,
  });
}
// 查询安全敏感信息泄露报告
export function getLeakage(query) {
  return request({
    url: "/system/report/getLeakage",
    method: "get",
    params: query,
  });
}

// 查询安全意识隐患报告
export function getSafety(query) {
  return request({
    url: "/system/report/getSafety",
    method: "get",
    params: query,
  });
}
// 导入
export function exportReport(params) {
  return request.post(`/system/report/exportReport`, params);
}
// 判断是否可以下载
export function canDownLoad(query) {
  return request({
    url: "/system/report/canDownLoad",
    method: "get",
    params: query,
  });
}
