import request from "@/plugins/request";
import { praseStrEmpty } from "@/utils/ruoyi";

// 查询列表
export function getJczyList(query) {
  return request({
    url: "/system/basicResourceSoftware/selectPage",
    method: "get",
    params: query,
  });
}

// 添加基础数据  /basicResourceSoftware/saveForm.do
export function addJczyList(query) {
  return request({
    url: "/system/basicResourceSoftware/saveForm",
    method: "post",
    data: query,
  });
}

// 删除基础数据  /basicResourceSoftware/deleteSoftware/{id}
export function deleteSoft(query) {
  return request({
    url: "/system/basicResourceSoftware/deleteSoftware/" + query,
    method: "delete",
  });
}

//基础资源软件-查询子集列表
export function selectChildPage(query) {
  return request({
    url: "/system/basicResourceSoftware/selectChildPage",
    method: "get",
    params: query,
  });
}
