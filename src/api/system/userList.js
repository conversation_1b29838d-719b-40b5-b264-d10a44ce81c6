import request from "@/plugins/request";
import { praseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function getUserList(query) {
  return request({
    url: "/system/user/list",
    method: "get",
    params: query,
  });
}

// 删除用户  /userCii/{userIds}
export function deleteUser(data) {
  return request({
    url: `/system/user/delete`,
    method: "put",
    data,
  });
}

// 用户密码重置 /userCii/resetPwd
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password,
  };
  return request({
    url: "/system/user/userCii/resetPwd",
    method: "put",
    data: data,
  });
}

// 获取备案单位
export function getTreeselect() {
  return request({
    url: "/system/user/userCii/treeselect",
    method: "get",
  });
}

// 获取支撑单位
export function getZhddList() {
  return request({
    url: "/system/user/userCii/getZhdd",
    method: "get",
  });
}

// 获取网安专家
export function getWazjList() {
  return request({
    url: "/system/user/userCii/getWazj",
    method: "get",
  });
}

//校验客户类型账号之前是否创建过
export function checkMerge(query) {
  return request({
    url: "/system/user/checkMerge",
    method: "post",
    data: query,
  });
}
// 添加用户  /userCii/addUser
export function addUserList(query) {
  return request({
    url: "/system/user/add",
    method: "post",
    data: query,
  });
}

// 编辑用户  /userCii/editUser
export function editUserList(query) {
  return request({
    url: "/system/user/update",
    method: "put",
    data: query,
  });
}

// 编辑用户 获取用户信息  /userCii/{userId}
export function getuserForm(data) {
  return request({
    url: "/system/user/userCii/" + praseStrEmpty(data),
    method: "get",
  });
}
// 获取用户角色
export function getuserRole(data) {
  return request({
    url: "/system/user/userCii",
    method: "get",
  });
}
/*=======================*/
export function getCode() {
  return request({
    url: "/system/license/submitmain",
    method: "get",
  });
}
//获取全部角色和用户id
export function getRoleInfoById(params) {
  return request.get(`/system/user/getRoleInfo/${params.userId || 0}`);
}

//用户锁定
export function changeStatus(params) {
  return request.put(`/system/user/changeStatus`, params);
}

//用户解锁
export function resetWrongPasswordCount(params) {
  return request.put(`/system/user/resetWrongPasswordCount`, {}, { params });
}

export const avatarUrl = `/outpost-api/system/user/avatar`; //上传头像

//获取部门树
export function getDeptInfoUrl(params) {
  return request.get(`/system/user/getDeptInfo/${params.userId || 0}`);
}
//重置密码
export function resetPwd(params) {
  return request.put(`/system/user/resetPwd`, params);
}
