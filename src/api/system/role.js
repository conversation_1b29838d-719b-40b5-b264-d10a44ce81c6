import request from "@/plugins/request";

// 查询角色列表
export function listRole(query) {
  return request({
    url: "/system/role/list",
    method: "get",
    params: query,
  });
}

// 查询角色详细
export function getRole(roleId) {
  return request({
    url: "/system/role/" + roleId,
    method: "get",
  });
}

// 新增角色
export function addRole(data) {
  return request({
    url: "/system/role/add",
    method: "post",
    data: data,
  });
}

// 修改角色
export function updateRole(data) {
  return request({
    url: "/system/role/edit",
    method: "post",
    data: data,
  });
}

// 角色数据权限
export function dataScope(data) {
  return request({
    url: "/system/role/dataScope",
    method: "put",
    data: data,
  });
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status,
  };
  return request({
    url: "/system/role/changeStatus",
    method: "put",
    data: data,
  });
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: "/system/role/" + roleId,
    method: "delete",
  });
}

// 1)	获取要配置角色的可选角色和已选角色
export function selectAuditRole(query) {
  return request({
    url: "/system/role/selectAuditRole",
    method: "get",
    params: query,
  });
}

// 2)	保存角色的审核范围
export function saveRoleAuditRel(query) {
  return request({
    url: "/system/role/saveRoleAuditRel",
    method: "post",
    data: query,
  });
}
// 版本信息的列表查询
export function getVersion(query) {
  return request({
    url: "/system/version/getList",
    method: "get",
    params: query,
  });
}
