import request from "@/plugins/request";
// 列表
export function listRole(query) {
  return request({
    url: "/system/customer/list",
    method: "get",
    params: query,
  });
}
//   客户管理--校验客户编号
export function checkCustomerNo(query) {
  return request({
    url: "/system/customer/checkCustomerNo",
    method: "get",
    params: query,
  });
}
// 新增客户
export function addDept(data) {
  return request({
    url: "/system/customer/add",
    method: "post",
    data: data,
  });
}
//   客户管理--删除客户
export function delDept(deptId) {
  return request({
    url: "/system/customer/delete/" + deptId,
    method: "delete",
  });
}
//   客户管理--修改客户
export function update(data) {
  return request({
    url: "/system/customer/update",
    method: "PUT",
    data: data,
  });
}

// 查询详情
export function viewDetails(deptId) {
  return request({
    url: "/system/customer/getCustomerInfo/" + deptId,
    method: "get",
  });
}
