import request from "@/plugins/request";

// 查询部门列表
export function listDept(query) {
  return request({
    url: "/system/dept/getDeptList",
    method: "get",
    params: { ...query, rows: query.pageSize, page: query.pageNum },
  });
}
// 查询客户列表
export function selectAllCustomer(query) {
  return request({
    url: "/system/businessSystem/selectAllCustomer",
    method: "get",
    params: query,
  });
}
// 新增
export function saveDeptForm(data) {
  return request({
    url: "/system/dept/saveDeptForm",
    method: "post",
    data: data,
  });
}
// 删除部门
export function deleteDept(data) {
  return request({
    url: "/system/dept/deleteDept",
    method: "post",
    data: data,
  });
}
