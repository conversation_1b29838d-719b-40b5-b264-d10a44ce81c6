import Axios from "@/plugins/request";

//查询综合服务标签列表
export function getTagList(query) {
  return Axios({
    url: "/system/osEvent/getEventTagList",
    method: "get",
    params: query,
  });
}
//查询综合服务项
export function getSelectList(query) {
  return Axios({
    url: "/system/osEventTemplate/selectList",
    method: "get",
    params: query,
  });
}

//查询脚本标签列表
export function getEventTemplateTagList(query) {
  return Axios({
    url: "/system/osEventTemplate/getEventTemplateTagList",
    method: "get",
    params: query,
  });
}
