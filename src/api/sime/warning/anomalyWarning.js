/* 异常警告 - API */
import request from "@/plugins/request";
/* 获取列表 */
export function getWaringList(query) {
  return request({
    url: "/warning/config/list",
    method: "get",
    params: query,
  });
}

/* 查看 */
export function getWaringDetail(id) {
  return request({
    url: "/warning/config/" + id,
    method: "get",
  });
}

/* 编辑 */
export function updateWaring(data) {
  return request({
    url: "/warning/config",
    method: "put",
    data,
  });
}

/* 修改可用状态 */
export function updateWaringAvailable(data) {
  return request({
    url: "/warning/config/isAvailable",
    method: "put",
    data,
  });
}

/* 获取列表 - 历史记录 */
export function getMessageList(query) {
  return request({
    url: "/warning/message/list",
    method: "get",
    params: query,
  });
}
