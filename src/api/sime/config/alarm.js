import request from "@/plugins/request";

// 数据源分组-获取分组树
export function getTreeData(query) {
  return request({
    url: "/config/alertRelay/datasource/group/tree",
    method: "get",
    params: query,
  });
}
// 数据源分组-新增分组树
export function addAlertTree(query) {
  return request({
    url: "/config/alertRelay/datasource/group",
    method: "post",
    data: query,
  });
}
// 数据源分组-编辑分组树
export function editAlertTree(query) {
  return request({
    url: "/config/alertRelay/datasource/group",
    method: "put",
    data: query,
  });
}
// 数据源分组-查看分组树
export function getAlertTree(query) {
  return request({
    url: "/config/alertRelay/datasource/group/" + query,
    method: "get",
  });
}
// 数据源分组-删除分组树
export function delAlertTree(query) {
  return request({
    url: "/config/alertRelay/datasource/group/" + query,
    method: "DELETE",
  });
}

// 数据源分组-列表获取
export function getTableAlert(query) {
  return request({
    url: "/config/alertRelay/datasource/list",
    method: "get",
    params: query,
  });
}
// 数据源-列表单个信息查看
export function getAlertInfo(query) {
  return request({
    url: "/config/alertRelay/datasource/" + query,
    method: "get",
  });
}
// 数据源-列表删除
export function delAlertInfo(query) {
  return request({
    url: "/config/alertRelay/datasource/" + query,
    method: "DELETE",
  });
}
// 数据源-列表编辑
export function editAlertInfo(query) {
  return request({
    url: "/config/alertRelay/datasource",
    method: "put",
    data: query,
  });
}
// 数据源-列表新增
export function addAlertInfo(query) {
  return request({
    url: "/config/alertRelay/datasource",
    method: "post",
    data: query,
  });
}
export function testAlertInfo(query) {
  return request({
    url: "/config/alertRelay/datasource/test",
    method: "post",
    data: query,
  });
}

// 数据表

// 数据表分组-新增
export function addTableGroup(query) {
  return request({
    url: "/config/alertRelay/table/group",
    method: "post",
    data: query,
  });
}
// 数据表分组-编辑
export function editTableGroup(query) {
  return request({
    url: "/config/alertRelay/table/group",
    method: "put",
    data: query,
  });
}
// 数据表分组-删除
export function deleteTableGroup(query) {
  return request({
    url: "/config/alertRelay/table/group/" + query,
    method: "delete",
  });
}
// 数据表分组-查看
export function getTableGroup(query) {
  return request({
    url: "/config/alertRelay/table/group/" + query,
    method: "get",
    params: query,
  });
}
// 数据表-获取分组树
export function getTableTreeData(query) {
  return request({
    url: "/config/alertRelay/table/group/tree",
    method: "get",
    params: query,
  });
}

// 数据表--获取列表
export function getTableListData(query) {
  return request({
    url: "/config/alertRelay/table/list",
    method: "get",
    params: query,
  });
}
// 数据表--删除
export function delAlertRelay(query) {
  return request({
    url: "/config/alertRelay/table/" + query,
    method: "DELETE",
  });
}
// 数据表-获取索引树
export function getIndexTree(query) {
  return request({
    url: "/config/alertRelay/table/index/tree",
    method: "get",
    params: query,
  });
}
// 数据表-获取可用数据源的下拉树
export function getAlertRelayDataTree(query) {
  return request({
    url: "/config/alertRelay/table/datasource/tree",
    method: "get",
    params: query,
  });
}
// 数据表-新增
export function addlertRelay(query) {
  return request({
    url: "/config/alertRelay/table",
    method: "post",
    data: query,
  });
}
// 数据表-编辑
export function editAlertRelay(query) {
  return request({
    url: "/config/alertRelay/table",
    method: "put",
    data: query,
  });
}
// 数据表-查看
export function getAlertRelay(query) {
  return request({
    url: "/config/alertRelay/table/" + query,
    method: "get",
  });
}
// 数据表-拉取字段
export function getRelayData(query) {
  return request({
    url: "/config/alertRelay/table/loadFields",
    method: "get",
    params: query,
  });
}

// 消息列表

// 消息列表-获取分组树
export function getMqTreeData(query) {
  return request({
    url: "/config/alertRelay/mq/group/tree",
    method: "get",
    params: query,
  });
}
// 消息列表-新增分组
export function addMqGroup(query) {
  return request({
    url: "/config/alertRelay/mq/group",
    method: "post",
    data: query,
  });
}
// 消息列表-编辑分组
export function editMqroup(query) {
  return request({
    url: "/config/alertRelay/mq/group",
    method: "put",
    data: query,
  });
}
// 消息列表-删除分组
export function deletteMqGroup(query) {
  return request({
    url: "/config/alertRelay/mq/group/" + query,
    method: "delete",
  });
}
// 消息列表-查看分组
export function getMqGroup(query) {
  return request({
    url: "/config/alertRelay/mq/group/" + query,
    method: "get",
    params: query,
  });
}
// 消息列表-查获取可用索引的下拉树
export function getMqTreeIndex(query) {
  return request({
    url: "/config/alertRelay/mq/index/tree",
    method: "get",
    params: query,
  });
}
// 消息队列-新增
export function addMqData(query) {
  return request({
    url: "/config/alertRelay/mq",
    method: "post",
    data: query,
  });
}
// 消息队列-编辑
export function editMqData(query) {
  return request({
    url: "/config/alertRelay/mq",
    method: "put",
    data: query,
  });
}
// 消息队列-查看
export function getInfoMqData(query) {
  return request({
    url: "/config/alertRelay/mq/" + query,
    method: "get",
  });
}
// 消息队列-删除
export function delInfoMqData(query) {
  return request({
    url: "/config/alertRelay/mq/" + query,
    method: "delete",
  });
}
// 消息队列-拉取字段
export function loadMqData(query) {
  return request({
    url: "/config/alertRelay/mq/loadFields",
    method: "get",
    params: query,
  });
}
// 消息队列-获取列表
export function getMqList(query) {
  return request({
    url: "/config/alertRelay/mq/list",
    method: "get",
    params: query,
  });
}

// 索引-获取数据字典
export function getFilesDicts(query) {
  return request({
    url: "/config/logStore/index/fields/" + query,
    method: "get",
  });
}
