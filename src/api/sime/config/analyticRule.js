import request from "@/plugins/request";
// 字段列表
export function getParseRuleGroup(query) {
  return request({
    url: "/config/parseRuleGroup/tree",
    method: "get",
  });
}
// 新增 解析规则组
export function addParseRuleGroup(query) {
  return request({
    url: "/config/parseRuleGroup",
    method: "post",
    data: query,
  });
}
// 修改 解析规则组
export function putParseRuleGroup(query) {
  return request({
    url: "/config/parseRuleGroup",
    method: "put",
    data: query,
  });
}
// 删除 解析规则组
export function delParseRuleGroup(query) {
  return request({
    url: "/config/parseRuleGroup/" + query,
    method: "delete",
  });
}
// 查询 解析规则组详情
export function detailParseRuleGroup(query) {
  return request({
    url: "/config/parseRuleGroup/" + query,
    method: "get",
  });
}

// 解析规则列表

export function getParseRuleList(query) {
  return request({
    url: "/config/parseRule/list",
    method: "get",
    params: query,
  });
}

// 添加解析规则
export function addParseRule(query) {
  return request({
    url: "/config/parseRule",
    method: "post",
    data: query,
  });
}
// 修改解析规则
export function editParseRule(query) {
  return request({
    url: "/config/parseRule",
    method: "put",
    data: query,
  });
}

// 移动解析规则到别的分组

export function moveParseRule(query) {
  return request({
    url: "/config/parseRule/move",
    method: "put",
    params: query,
  });
}

// 删除解析规则

export function delParseRule(query) {
  return request({
    url: "/config/parseRule/" + query,
    method: "delete",
    data: query,
  });
}

// 解析json返回map

export function parseJson(query) {
  return request({
    url: "/config/parseRule/parseJson",
    method: "post",
    data: query,
  });
}

// 修改解析规则启停状态

export function updateStatus(query) {
  return request({
    url: "/config/parseRule/status",
    method: "put",
    data: query,
  });
}

// 获取详情数据

export function getDetailRule(query) {
  return request({
    url: "/config/parseRule/" + query,
    method: "get",
  });
}

// 查询版本列表
export function getVersionList(query) {
  return request({
    url: "/config/parseRule/version/list",
    method: "get",
    params: query,
  });
}

// \查询详情副本
export function getVersionDetail(id) {
  return request({
    url: `/config/parseRule/version/${id}`,
    method: "get",
  });
}

// 将解析规则回滚到指定版本
export function rollBackVersion(query) {
  return request({
    url: "/config/parseRule/version/rollback",
    method: "put",
    data: query,
  });
}

/* 日志数据类型校验 */
export function postLogTypeValid(query) {
  return request({
    url: "/config/parseRule/logTypeValid",
    method: "post",
    data: query,
  });
}

/* 解析日志样本 */
export function postParseLog(query) {
  return request({
    url: "/config/parseRule/parseLog",
    method: "post",
    data: query,
  });
}
