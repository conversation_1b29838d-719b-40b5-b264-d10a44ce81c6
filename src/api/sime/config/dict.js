import request from "@/plugins/request";

// 查询字典类型列表
export function listType(query) {
  return request({
    url: "/config/dictionary/type/list",
    method: "get",
    params: query,
  });
}

// 查询字典类型详细
export function getType(dictId) {
  return request({
    url: "/config/dictionary/type/" + dictId,
    method: "get",
  });
}

// 新增字典类型
export function addType(data) {
  return request({
    url: "/config/dictionary/type",
    method: "post",
    data: data,
  });
}

// 修改字典类型
export function updateType(data) {
  return request({
    url: "/config/dictionary/type",
    method: "put",
    data: data,
  });
}

// 删除字典类型
export function delType(dictId) {
  return request({
    url: "/config/dictionary/type/" + dictId,
    method: "delete",
  });
}

// 清理参数缓存
export function clearCache() {
  return request({
    url: "/config/dictionary/type/refreshCache",
    method: "delete",
  });
}

//========字典项===========
// 查询字典数据列表
export function listData(query) {
  return request({
    url: "/config/dictionary/data/list",
    method: "get",
    params: query,
  });
}

// 查询字典数据详细
export function getData(dictCode) {
  return request({
    url: "/config/dictionary/data/" + dictCode,
    method: "get",
  });
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: "/config/dictionary/data/type/" + dictType,
    method: "get",
  });
}

// 新增字典数据
export function addData(data) {
  return request({
    url: "/config/dictionary/data",
    method: "post",
    data: data,
  });
}

// 修改字典数据
export function updateData(data) {
  return request({
    url: "/config/dictionary/data",
    method: "put",
    data: data,
  });
}

// 删除字典数据
export function delData(dictCode) {
  return request({
    url: "/config/dictionary/data/" + dictCode,
    method: "delete",
  });
}
