import request from "@/plugins/request";

// 查询字典类型列表
export function getTreeData(query) {
  return request({
    url: "/config/queryFilter/group/tree",
    method: "get",
    params: query,
  });
}
// 新增
export function addNodeGroup(query) {
  return request({
    url: "/config/queryFilter/group",
    method: "post",
    data: query,
  });
}
// 过滤器分组-编辑
export function editNodeGroup(query) {
  return request({
    url: "/config/queryFilter/group",
    method: "put",
    data: query,
  });
}
// 过滤器分组-查看
export function detailNodeGroup(query) {
  return request({
    url: "/config/queryFilter/group/" + query,
    method: "get",
  });
}
// 过滤器分组-删除
export function deleteNodeGroup(query) {
  return request({
    url: "/config/queryFilter/group/" + query,
    method: "Delete",
  });
}
// 过滤器分组-重名校验
export function checkName(query) {
  return request({
    url: "/config/queryFilter/group/checkName",
    method: "get",
    params: query,
  });
}
// 过滤器分组-分组下的列表
export function getDataSourceList(query) {
  return request({
    url: "/config/queryFilter/list",
    method: "get",
    params: query,
  });
}

// 过滤器-新增
export function addTableDataSource(query) {
  return request({
    url: "/config/queryFilter",
    method: "post",
    data: query,
  });
}

// 过滤器-编辑
export function editTableDataSource(query) {
  return request({
    url: "/config/queryFilter",
    method: "put",
    data: query,
  });
}
//  过滤器-获取当前数据
export function getSourceInfo(query) {
  return request({
    url: "/config/queryFilter/" + query,
    method: "get",
  });
}
// 过滤器-删除
export function delSourceInfo(query) {
  return request({
    url: "/config/queryFilter/" + query,
    method: "delete",
  });
}

//过滤器 移动
export function moveItem(query) {
  return request({
    url: "/config/queryFilter/move",
    method: "put",
    params: query,
  });
}

//查询相同索引的过滤器
export function queryFilterSameIndex(query) {
  return request({
    url: "/config/queryFilter/group/tree/index",
    method: "get",
    params: query,
  });
}

//修改过滤器是否常用
export function editStatus(query) {
  return request({
    url: "/config/queryFilter/editStatus",
    method: "put",
    params: query,
  });
}

//分页查询版本列表
export function getVersionList(query) {
  return request({
    url: "/config/queryFilter/version/list",
    method: "get",
    params: query,
  });
}

//查询版本详情
export function getVersionDetail(id) {
  return request({
    url: `/config/queryFilter/version/${id}`,
    method: "get",
  });
}

//回滚版本
export function rollBackVersion(query) {
  return request({
    url: `/config/queryFilter/rollback`,
    method: "put",
    params: query,
  });
}
//过滤器拓扑关系
export function getFilterRefInfo(query) {
  return request({
    url: "/config/queryFilter/getRefInfo",
    method: "get",
    params: query,
  });
}
