/* syslog配置 相关*/
import request from "@/plugins/request";
/* syslog分组-获取分组树 */
export function getSyslogTreeData(query) {
  return request({
    url: "/config/alertRelay/syslog/group/tree",
    method: "get",
    params: query,
  });
}

/* syslog分组-新增 */
export function addSyslogGroup(query) {
  return request({
    url: "/config/alertRelay/syslog/group",
    method: "post",
    data: query,
  });
}

/* syslog分组-编辑 */
export function editSyslogroup(query) {
  return request({
    url: "/config/alertRelay/syslog/group",
    method: "put",
    data: query,
  });
}

/* syslog分组-删除 */
export function deleteSyslogGroup(query) {
  return request({
    url: "/config/alertRelay/syslog/group/" + query,
    method: "delete",
  });
}

/* syslog分组-查看 */
export function getSyslogGroup(query) {
  return request({
    url: "/config/alertRelay/syslog/group/" + query,
    method: "get",
    params: query,
  });
}

/* syslog获取可用索引的下拉树 */
export function getSyslogTreeIndex(query) {
  return request({
    url: "/config/alertRelay/syslog/index/tree",
    method: "get",
    params: query,
  });
}

/* syslog-新增 */
export function addSyslogData(query) {
  return request({
    url: "/config/alertRelay/syslog",
    method: "post",
    data: query,
  });
}

/* syslog-编辑 */
export function editSyslogData(query) {
  return request({
    url: "/config/alertRelay/syslog",
    method: "put",
    data: query,
  });
}

/* syslog-查看 */
export function getSyslogData(query) {
  return request({
    url: "/config/alertRelay/syslog/" + query,
    method: "get",
  });
}

/* syslog-删除 */
export function delInfoSyslogData(query) {
  return request({
    url: "/config/alertRelay/syslog/" + query,
    method: "delete",
  });
}

/* syslog-拉取字段 */
export function loadSyslogData(query) {
  return request({
    url: "/config/alertRelay/syslog/loadFields",
    method: "get",
    params: query,
  });
}

/* syslog-获取列表 */
export function getSyslogList(query) {
  return request({
    url: "/config/alertRelay/syslog/list",
    method: "get",
    params: query,
  });
}

/* 索引-获取数据字典 */
export function getFilesDicts(query) {
  return request({
    url: "/config/logStore/index/fields/" + query,
    method: "get",
  });
}
