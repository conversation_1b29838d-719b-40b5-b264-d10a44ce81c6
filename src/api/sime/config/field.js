import request from "@/plugins/request";
// 字段列表
export function getLogFieldList(query) {
  return request({
    url: "/config/logField/list",
    method: "get",
  });
}
// 保存字段配置
export function saveLogFieldList(query) {
  return request({
    url: "/config/logField",
    method: "post",
    data: query,
  });
}

//获取所有可被引用的字典类型对应的字典数据
export function fieldAvailable(query) {
  return request({
    url: "/config/dictionary/data/type/fieldAvailable",
    method: "get",
    params: query,
  });
}
