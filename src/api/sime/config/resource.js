import request from "@/plugins/request";

// 地址资源-获取列表
export function getIpList(query) {
  return request({
    url: "/config/analysisResource/ip/list",
    method: "get",
    params: query,
  });
}
// 地址资源-新增

export function addIp(query) {
  return request({
    url: "/config/analysisResource/ip",
    method: "post",
    data: query,
  });
}

// 地址资源-查看
export function viewIP(id) {
  return request({
    url: "/config/analysisResource/ip/" + id,
    method: "get",
  });
}
// 地址资源-更新
export function updateIp(query) {
  return request({
    url: "/config/analysisResource/ip",
    method: "put",
    data: query,
  });
}
// 地址资源-删除

export function delIp(ids) {
  return request({
    url: "/config/analysisResource/ip/" + ids,
    method: "delete",
  });
}
// 端口相关接口

// 端口资源-获取列表
export function getPortList(query) {
  return request({
    url: "/config/analysisResource/port/list",
    method: "get",
    params: query,
  });
}
// 端口-新增
export function addPort(query) {
  return request({
    url: "/config/analysisResource/port",
    method: "post",
    data: query,
  });
}

//-
//  端口资源-查看
export function viewPort(id) {
  return request({
    url: "/config/analysisResource/port/" + id,
    method: "get",
  });
}
//  端口资源-更新
export function updatePort(query) {
  return request({
    url: "/config/analysisResource/port",
    method: "put",
    data: query,
  });
}
//  端口资源-删除

export function delPort(ids) {
  return request({
    url: "/config/analysisResource/port/" + ids,
    method: "delete",
  });
}

//自定义相关接口

// 自定义资源-获取列表
export function getCustomList(query) {
  return request({
    url: "/config/analysisResource/custom/list",
    method: "get",
    params: query,
  });
}
// 自定义-新增
export function addCustom(query) {
  return request({
    url: "/config/analysisResource/custom",
    method: "post",
    data: query,
  });
}

//-
//  自定义-查看
export function viewCustom(id) {
  return request({
    url: "/config/analysisResource/custom/" + id,
    method: "get",
  });
}
// 自定义-更新
export function updateCustom(query) {
  return request({
    url: "/config/analysisResource/custom",
    method: "put",
    data: query,
  });
}
//  自定义-删除

export function delCustom(ids) {
  return request({
    url: "/config/analysisResource/custom/" + ids,
    method: "delete",
  });
}

// 时间模块相关
//  时间资源-获取列表
export function getTimeList(query) {
  return request({
    url: "/config/analysisResource/time/list",
    method: "get",
    params: query,
  });
}
//  时间-新增
export function addTime(query) {
  return request({
    url: "/config/analysisResource/time",
    method: "post",
    data: query,
  });
}

//-
//  时间-查看
export function viewTime(id) {
  return request({
    url: "/config/analysisResource/time/" + id,
    method: "get",
  });
}
//  时间-更新
export function updateTime(query) {
  return request({
    url: "/config/analysisResource/time",
    method: "put",
    data: query,
  });
}
//   时间-删除

export function delTime(ids) {
  return request({
    url: "/config/analysisResource/time/" + ids,
    method: "delete",
  });
}
