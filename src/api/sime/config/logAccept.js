import request from "@/plugins/request";
// 查询列表
export function logList(query) {
  return request({
    url: "/receiver/server/list",
    method: "get",
    params: query,
  });
}
// 日志接收器-启动
export function logStart(query) {
  return request({
    url: "/receiver/server/start/" + query.id,
    method: "get",
  });
}
// 日志接收器-关闭
export function logClose(query) {
  return request({
    url: "/receiver/server/close/" + query.id,
    method: "get",
  });
}
// 日志接收器-删除
export function remove(query) {
  return request({
    url: "/receiver/server/remove/" + query.ids,
    method: "DELETE",
  });
}
// 获取系统内置的日志处理器
export function getMapping(query) {
  return request({
    url: "/receiver/handler/getMapping",
    method: "get",
    params: query,
  });
}
// 日志接收器-添加监听器
export function relManagAdd(query) {
  return request({
    url: "/receiver/server/add",
    method: "post",
    data: query,
  });
}
// 日志接收器-获取详细信息
export function getDetails(query) {
  return request({
    url: "/receiver/server/get/" + query.id,
    method: "get",
  });
}
//日志接收器-编辑
export function saveEdit(data) {
  return request({
    url: "/receiver/server/edit",
    method: "PUT",
    data: data,
  });
}
// 获取监听器采集状态信息
export function getListenInfo(query) {
  return request({
    url: "/receiver/server/getListenInfo/" + query.id,
    method: "get",
  });
}
//
export function transfer(query) {
  return request({
    url: "/receiver/server/indexSet",
    method: "get",
    params: query,
  });
}
// 新增监听器查询默认日志字段
export function defaultFieldsList(query) {
  return request({
    url: "/receiver/server/defaultFieldsList",
    method: "get",
    params: query,
  });
}

// 日志接收器-添加监听器-v2
export function addReceiver(query) {
  return request({
    url: "/receiver/server/add",
    method: "post",
    data: query,
  });
}

// 日志接收器-获取详情信息-v2
export function getReceiver(query) {
  return request({
    url: "receiver/server/get/" + query,
    method: "get",
  });
}

// 日志接收器-编辑-v2
export function updateReceiver(query) {
  return request({
    url: "receiver/server/edit",
    method: "put",
    data: query,
  });
}

// 重启监听器

export function restartReceiver(query) {
  return request({
    url: "receiver/server/restart",
    method: "post",
    data: query,
  });
}
