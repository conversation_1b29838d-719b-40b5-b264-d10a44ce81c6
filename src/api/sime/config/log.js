import request from "@/plugins/request";

// 查询字典类型列表
export function getTreeData(query) {
  return request({
    url: "/config/logStore/datasource/group/tree",
    method: "get",
    params: query,
  });
}
// 新增
export function addNodeGroup(query) {
  return request({
    url: "/config/logStore/datasource/group",
    method: "post",
    data: query,
  });
}
// 数据源分组-编辑
export function editNodeGroup(query) {
  return request({
    url: "/config/logStore/datasource/group",
    method: "put",
    data: query,
  });
}
// 数据源分组-查看
export function detailNodeGroup(query) {
  return request({
    url: "/config/logStore/datasource/group/" + query,
    method: "get",
  });
}
// 数据源分组-删除
export function deleteNodeGroup(query) {
  return request({
    url: "/config/logStore/datasource/group/" + query,
    method: "Delete",
  });
}
// 数据源分组-编辑
export function checkName(query) {
  return request({
    url: "/config/logStore/datasource/group/checkName",
    method: "get",
    params: query,
  });
}
// 数据源分组-编辑
export function getDataSourceList(query) {
  return request({
    url: "/config/logStore/datasource/list",
    method: "get",
    params: query,
  });
}

// 数据源-新增
export function getTableDataSource(query) {
  return request({
    url: "/config/logStore/datasource",
    method: "post",
    data: query,
  });
}

// 数据源-编辑
export function editTableDataSource(query) {
  return request({
    url: "/config/logStore/datasource",
    method: "put",
    data: query,
  });
}
// 数据源-获取当前数据
export function getSourceInfo(query) {
  return request({
    url: "/config/logStore/datasource/" + query,
    method: "get",
  });
}
// 数据源-删除
export function delSourceInfo(query) {
  return request({
    url: "/config/logStore/datasource/" + query,
    method: "Delete",
  });
}
// 数据源-删除
export function test(query) {
  return request({
    url: "/config/logStore/datasource/test",
    method: "post",
    data: query,
  });
}
// 索引

// 索引分组-新增
export function addIndexGroup(query) {
  return request({
    url: "/config/logStore/index/group",
    method: "post",
    data: query,
  });
}
// 索引分组-编辑
export function editIndexGroup(query) {
  return request({
    url: "/config/logStore/index/group",
    method: "put",
    data: query,
  });
}
// 索引分组-查看
export function detailIndexGroup(query) {
  return request({
    url: "/config/logStore/index/group/" + query,
    method: "get",
  });
}

// 索引分组-删除
export function delIndexGroup(query) {
  return request({
    url: "/config/logStore/index/group/" + query,
    method: "Delete",
  });
}
// 索引分组-获取分组树
export function getIndexGroupTree(query) {
  return request({
    url: "/config/logStore/index/group/tree",
    method: "get",
    params: query,
  });
}
// 索引-获取列表

export function getIndexList(query) {
  return request({
    url: "/config/logStore/index/list",
    method: "get",
    params: query,
  });
}
// 索引-获取分组树
export function getIndexStoreTree(query) {
  return request({
    url: "/config/logStore/index/datasource/tree",
    method: "get",
    params: query,
  });
}
// 索引-索引-拉取字段
export function getIndexLoadFields(query) {
  return request({
    url: "/config/logStore/index/loadFields",
    method: "post",
    params: query,
  });
}
// 索引-查看
export function getIndexData(query) {
  return request({
    url: "/config/logStore/index/" + query,
    method: "get",
  });
}
// 索引-删除
export function delIndexData(query) {
  return request({
    url: "/config/logStore/index/" + query,
    method: "delete",
  });
}
// 索引-新增
export function addIndexData(query) {
  return request({
    url: "/config/logStore/index",
    method: "post",
    data: query,
  });
}
// 索引-新增
export function updateIndexData(query) {
  return request({
    url: "/config/logStore/index",
    method: "put",
    data: query,
  });
}
// 索引-获取数据字典
export function getDicts(query) {
  return request({
    url: "/config/dictionary/type/select/fieldAvailable",
    method: "get",
    params: query,
  });
}

//获取可用索引的下拉树

export function getLogTree(query) {
  return request({
    url: "/config/logStore/index/tree",
    method: "get",
    params: query,
  });
}
export function existsIndex(query) {
  return request({
    url: `/config/logStore/index/existsIndex`,
    method: "get",
    params: query,
  });
}
