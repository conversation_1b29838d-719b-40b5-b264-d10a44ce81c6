import request from "@/plugins/request";
/* 采集器管理 - 动态接口 */
/* 获取采集器 */
export function getReceiverRoute() {
  return request({
    url: "/route/getReceiverRoute",
    method: "get",
  });
}

/* 查询列表 */
export function getLogList(query, url) {
  return request({
    url: url + "/server/list",
    method: "get",
    params: query,
  });
}

/* 日志接收器-启动 */
export function logStart(query, url) {
  return request({
    url: url + "/server/start/" + query.id,
    method: "get",
  });
}
/* 日志接收器-关闭 */
export function logClose(query, url) {
  return request({
    url: url + "/server/close/" + query.id,
    method: "get",
  });
}
/* 日志接收器-删除 */
export function remove(query, url) {
  return request({
    url: url + "/server/remove/" + query.ids,
    method: "DELETE",
  });
}
/* 获取监听器采集状态信息 */
export function getListenInfo(query, url) {
  return request({
    url: url + "/server/getListenInfo/" + query.id,
    method: "get",
  });
}
/* 日志接收器-添加监听器-v2 */
export function addReceiver(query, url) {
  return request({
    url: url + "/server/add",
    method: "post",
    data: query,
  });
}
/* 日志接收器-编辑-v2 */
export function updateReceiver(query, url) {
  return request({
    url: url + "/server/edit",
    method: "put",
    data: query,
  });
}
/* 日志接收器-获取详情信息-v2 */
export function getReceiver(query, url) {
  return request({
    url: url + "/server/get/" + query,
    method: "get",
  });
}
/* 获取索引格式 */
export function transfer(query, url) {
  return request({
    url: url + "/server/indexSet",
    method: "get",
    params: query,
  });
}
/* 日志接收器-获取详细信息 */
export function getDetails(query, url) {
  return request({
    url: url + "/server/get/" + query.id,
    method: "get",
  });
}
/* 重启监听器 */
export function restartReceiver(query, url) {
  return request({
    url: url + "/server/restart",
    method: "post",
    data: query,
  });
}

/* 统计日志数量 */
export function getStatisticLogNum(query) {
  return request({
    url: "/receiver/server/statistic/logNum",
    method: "get",
    params: query,
  });
}

/* 修改 - 以下暂未发现使用 */
// 获取系统内置的日志处理器
export function getMapping(query) {
  return request({
    url: "/receiver/handler/getMapping",
    method: "get",
    params: query,
  });
}
// 日志接收器-添加监听器
export function relManagAdd(query) {
  return request({
    url: "/receiver/server/add",
    method: "post",
    data: query,
  });
}

//日志接收器-编辑
export function saveEdit(data) {
  return request({
    url: "/receiver/server/edit",
    method: "PUT",
    data: data,
  });
}

// 新增监听器查询默认日志字段
export function defaultFieldsList(query) {
  return request({
    url: "/receiver/server/defaultFieldsList",
    method: "get",
    params: query,
  });
}
