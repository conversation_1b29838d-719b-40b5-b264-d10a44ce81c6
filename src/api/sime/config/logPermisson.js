import request from "@/plugins/request";
/* 日志权限过滤器  -API */
/* 日志权限过滤器分组-获取分组树 */
export function getTreeData(query) {
  return request({
    url: "/config/logPermission/group/tree",
    method: "get",
    params: query,
  });
}
/* 日志权限过滤器分组-新增 */
export function addNodeGroup(query) {
  return request({
    url: "/config/logPermission/group",
    method: "post",
    data: query,
  });
}
/* 日志权限过滤器分组-编辑 */
export function editNodeGroup(query) {
  return request({
    url: "/config/logPermission/group",
    method: "put",
    data: query,
  });
}
/* 日志权限过滤器分组-删除 */
export function deleteNodeGroup(query) {
  return request({
    url: "/config/logPermission/group/" + query,
    method: "Delete",
  });
}
/* 日志权限过滤器分组-查看 */
export function detailNodeGroup(query) {
  return request({
    url: "/config/logPermission/group/" + query,
    method: "get",
  });
}

/* 列表查询 */
export function getDataSourceList(query) {
  return request({
    url: "/config/logPermission/listOfPage",
    method: "get",
    params: query,
  });
}
/* 根据ID查询 */
export function getSourceInfo(query) {
  return request({
    url: "/config/logPermission/" + query,
    method: "get",
  });
}
/* 新增日志权限过滤器 */
export function addTableDataSource(query) {
  return request({
    url: "/config/logPermission",
    method: "post",
    data: query,
  });
}
/* 修改日志权限过滤器 */
export function editTableDataSource(query) {
  return request({
    url: "/config/logPermission",
    method: "put",
    data: query,
  });
}
/* 删除日志权限过滤器 */
export function delSourceInfo(query) {
  return request({
    url: "/config/logPermission/" + query,
    method: "delete",
  });
}
/* 移动日志权限过滤器 */
export function moveItem(query) {
  return request({
    url: "/config/logPermission/move",
    method: "put",
    params: query,
  });
}
/* 修改日志权限过滤器状态 */
export function editStatus(query) {
  return request({
    url: "/config/logPermission/editStatus",
    method: "put",
    params: query,
  });
}

/* 配置用户的日志权限过滤器 */
export function postUserRelation(query) {
  return request({
    url: "/config/logPermission/userRelation",
    method: "post",
    data: query,
  });
}
/* 获取用户配置的日志权限过滤器 */
export function postGetListByUserId(query) {
  return request({
    url: "/config/logPermission/userRelation/getListByUserId",
    method: "get",
    params: query,
  });
}

// 过滤器分组-重名校验
export function checkName(query) {
  return request({
    url: "/config/queryFilter/group/checkName",
    method: "get",
    params: query,
  });
}

//查询相同索引的过滤器
export function queryFilterSameIndex(query) {
  return request({
    url: "/config/queryFilter/group/tree/index",
    method: "get",
    params: query,
  });
}
//分页查询版本列表
export function getVersionList(query) {
  return request({
    url: "/config/queryFilter/version/list",
    method: "get",
    params: query,
  });
}
//查询版本详情
export function getVersionDetail(id) {
  return request({
    url: `/config/queryFilter/version/${id}`,
    method: "get",
  });
}

//回滚版本
export function rollBackVersion(query) {
  return request({
    url: `/config/queryFilter/rollback`,
    method: "put",
    params: query,
  });
}
