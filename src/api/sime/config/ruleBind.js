import request from "@/plugins/request";
// 列表
export function getIpBindingList(query) {
  return request({
    url: "/config/parseRule/ipBinding/list",
    method: "get",
  });
}

// 列表
export function getIpBindingTree(query) {
  return request({
    url: "/config/parseRule/tree",
    method: "get",
    params: query,
  });
}

// 新增

export function addIpBinding(query) {
  return request({
    url: "/config/parseRule/ipBinding",
    method: "post",
    data: query,
  });
}

// 详情

export function detailIpBinding(query) {
  return request({
    url: "/config/parseRule/ipBinding/" + query,
    method: "get",
  });
}

// 编辑

export function updateIpBinding(query) {
  return request({
    url: "/config/parseRule/ipBinding",
    method: "put",
    data: query,
  });
}
// 删除
export function delIpBinding(query) {
  return request({
    url: "/config/parseRule/ipBinding/" + query,
    method: "delete",
  });
}
