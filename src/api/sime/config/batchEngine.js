import request from "@/plugins/request";

// 查询列表
export function getListData(query) {
  return request({
    url: "/config/analysisRule/list",
    method: "get",
    params: query,
  });
}
// 根据Id查询
export function getRuleInfo(query) {
  return request({
    url: "/config/analysisRule/" + query,
    method: "get",
  });
}
// 新增批处理规则
export function addBatchRule(query) {
  return request({
    url: "/config/analysisRule",
    method: "post",
    data: query,
  });
}
// 修改批处理规则
export function editBatchRule(query) {
  return request({
    url: "/config/analysisRule",
    method: "put",
    data: query,
  });
}
// 删除批处理规则
export function delBatchRule(query) {
  return request({
    url: "/config/analysisRule/" + query,
    method: "delete",
  });
}
// 移动批处理规则
export function moveBatchRule(query) {
  return request({
    url: "/config/analysisRule/move",
    method: "put",
    params: query,
  });
}

// 分组-获取分组树
export function getTreeData(query) {
  return request({
    url: "/config/analysisRule/group/tree",
    method: "get",
    params: query,
  });
}
// 分组-新增
export function addNodeGroup(query) {
  return request({
    url: "/config/analysisRule/group",
    method: "post",
    data: query,
  });
}
// 分组-编辑
export function editNodeGroup(query) {
  return request({
    url: "/config/analysisRule/group",
    method: "put",
    data: query,
  });
}
// 分组-删除
export function deleteNodeGroup(query) {
  return request({
    url: "/config/analysisRule/group/" + query,
    method: "delete",
  });
}
// 分组-查看
export function detailNodeGroup(query) {
  return request({
    url: "/config/analysisRule/group/" + query,
    method: "get",
  });
}
// 分页查询运行记录信息
export function batchRuleList(query) {
  return request({
    url: "/batchengine/batchRule/history/list",
    method: "get",
    params: query,
  });
}
//
export function batchRuleCount(query) {
  return request({
    url: "/batchengine/batchRule/history/count",
    method: "get",
    params: query,
  });
}

// 获取对应消息队列数据
export function getMessageList(url, query) {
  return request({
    url: url,
    method: "get",
    params: query,
  });
}
// 人工运行、测试运行
export function testRunRule(query) {
  return request({
    url: "/batchengine/batchRule/run",
    method: "post",
    params: query,
    timeout: 1200000,
  });
}
// 人工运行、测试运行新增，编辑，复制
export function runSingle(query, data) {
  return request({
    url: "config/analysisRule/run/single",
    method: "post",
    params: query,
    data: data,
  });
}
// 修改批处理规则状态
export function editStatus(query) {
  return request({
    url: "/config/analysisRule/editStatus",
    method: "put",
    params: query,
  });
}

//分页查询版本列表
export function getVersionList(query) {
  return request({
    url: "/config/analysisRule/version/list",
    method: "get",
    params: query,
  });
}

//查询版本详情
export function getVersionDetail(id) {
  return request({
    url: `/config/analysisRule/version/${id}`,
    method: "get",
  });
}

//回滚版本
export function rollBackVersion(query) {
  return request({
    url: `/config/analysisRule/rollback`,
    method: "put",
    params: query,
  });
}

// 获取根据索引规则列表
export function getAnalysisRuleList(query) {
  return request({
    url: `/config/analysisRule/getList`,
    method: "get",
    params: query,
  });
}
//拓扑关系
export function getRefInfo(query) {
  return request({
    url: "/config/analysisRule/getRefInfo",
    method: "get",
    params: query,
  });
}
