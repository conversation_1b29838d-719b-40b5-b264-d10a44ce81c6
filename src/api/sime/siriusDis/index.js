import request from "@/plugins/request";
/* 获取引擎规则数量 */
export function getRuleCount(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/getRuleCount",
    method: "post",
    data,
  });
}
/* 获取告警外发配置 */
export function getAlertRelay(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/getAlertRelay",
    method: "post",
    data,
  });
}
/* 告警外发配置 */
export function setAlertRelay(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/setAlertRelay",
    method: "post",
    data,
  });
}

/* 上传规则文件 */
export function saveFile(data) {
  return request({
    url: "/nodemanager/file/save",
    method: "post",
    data,
  });
}
/* 执行规则文件 */
export function uploadRules(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/uploadRules",
    method: "post",
    data,
  });
}

/* ===== 白名单 ====== */
/* 新增白名单 */
export function addWhiteList(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/addWhiteList",
    method: "post",
    data,
  });
}
/* 编辑白名单 */
export function editWhiteList(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/editWhiteList",
    method: "post",
    data,
  });
}
/* 删除白名单 */
export function deleteWhiteList(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/deleteWhiteList",
    method: "post",
    data,
  });
}
/* 获取白名单列表 */
export function getWhiteLists(id, data) {
  return request({
    url: "/nodemanager/proxy/" + id + "/getWhiteLists",
    method: "post",
    data,
  });
}
