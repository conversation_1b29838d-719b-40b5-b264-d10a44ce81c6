import request from "@/plugins/request";
// 查询默认展示的过滤器
export function defaultList() {
  return request({
    url: "/config/queryFilter/defaultList",
    method: "get",
  });
}
// 查询所有过滤器
export function filterListAll() {
  return request({
    url: "/config/queryFilter/list/all",
    method: "get",
  });
}
// 通过过滤器indexId 获取字段
export function filterIndexIdNames(indexId) {
  return request({
    url: "/config/logStore/index/fields/" + indexId,
    method: "get",
  });
}
// 添加二次筛选条件
export function saveFilterTemplate(params) {
  return request({
    url: "/search/search/template",
    method: "post",
    data: params,
  });
}
// 获取过滤器内容
export function getFilterDetail(queryFilterId) {
  return request({
    url: "/config/queryFilter/" + queryFilterId,
    method: "get",
  });
}
// 获取所有快速查询模板
export function searchTemplateList() {
  return request({
    url: "/search/search/template/list",
    method: "get",
  });
}
// 删除快速查询模板
export function deleteSearchTemplate(id) {
  return request({
    url: "/search/search/template/" + id,
    method: "DELETE",
  });
}
// 修改快速查询模板
export function updateSearchTemplate(params) {
  return request({
    url: "/search/search/template",
    method: "put",
    data: params,
  });
}
