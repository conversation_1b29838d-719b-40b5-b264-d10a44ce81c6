import request from "@/plugins/request";
// 查询列表
export function search_list(params) {
  return request({
    url: "/search/search/list",
    method: "post",
    data: params,
    cancelToken: true, // 启用自动取消重复请求
  });
}
// 查询汇总信息-有分组信息
export function search_sum(params) {
  return request({
    url: "/search/search/sum",
    method: "post",
    data: params,
  });
}
// 查询时间段汇总信息
export function search_aggs_sum(params) {
  return request({
    url: "/search/search/aggs/sum",
    method: "post",
    data: params,
  });
}
// 追溯
export function search_trace(params) {
  return request({
    url: "/search/search/trace",
    method: "post",
    data: params,
  });
}
