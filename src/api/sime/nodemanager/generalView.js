/* 节点管理 - 节点总览 API */
import request from "@/plugins/request";
/* 获取节点类型，用于TAB页显示 */
export function getNodeType() {
  return request({
    url: "/nodemanager/node/nodeType",
    method: "get",
  });
}

/* 获取某个类型下的全部节点，用于扑克牌页面 */
export function getNodeList(query) {
  return request({
    url: "/nodemanager/node/list/" + query,
    method: "get",
  });
}

/* 获取节点详情（可用于删除前的判断） */
export function getNodeDetail(id) {
  return request({
    url: "/nodemanager/node/" + id,
    method: "get",
  });
}

/* 删除节点（离线状态时） */
export function deleteNode(id) {
  return request({
    url: "/nodemanager/node/" + id,
    method: "delete",
  });
}

/* 节点退出 */
export function nodeExit(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/nodeExit",
    method: "POST",
    data,
  });
}

/* 编辑节点名称、描述 */
export function updateNode(data) {
  return request({
    url: "/nodemanager/node",
    method: "put",
    data,
  });
}

/* 节点监控-折线图 */
export function getNodeMonitoringLine(id, data) {
  return request({
    url: "/nodemanager/nodeMonitoring/line/" + id,
    method: "get",
    params: data,
  });
}
/* 节点监控-仪表盘-cpu使用率 */
export function getGaugeCpu(id) {
  return request({
    url: "/nodemanager/nodeMonitoring/gauge/cpu/" + id,
    method: "get",
  });
}
/* 节点监控-仪表盘-内存使用率 */
export function getGaugeRam(id) {
  return request({
    url: "/nodemanager/nodeMonitoring/gauge/ram/" + id,
    method: "get",
  });
}
/* 节点监控-仪表盘-硬盘使用率 */
export function getGaugeHardDisk(id) {
  return request({
    url: "/nodemanager/nodeMonitoring/gauge/hardDisk/" + id,
    method: "get",
  });
}
