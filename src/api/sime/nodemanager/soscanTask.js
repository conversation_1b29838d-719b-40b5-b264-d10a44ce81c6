/* soscan - soscan API */
import request from "@/plugins/request";
/* 获取扫描任务列表 */
export function getTaskList(data) {
  return request({
    url: "/nodemanager/soscan/task/list",
    method: "get",
    params: data,
  });
}
/* 获取任务的扫描进度 */
export function getTaskListStatus(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.GetProgress1",
    method: "post",
    data,
  });
}
/* 获取策略下拉列表 */
export function postGetAllProfile(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.GetAllProfile",
    method: "post",
    data,
  });
}
/* 根据策略key获取脚本列表 */
export function getProfilesByName(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.GetProfilesByName",
    method: "post",
    data: data,
  });
}
/* 新增扫描任务 */
export function addTask(data) {
  return request({
    url: "/nodemanager/soscan/task",
    method: "post",
    data,
  });
}
/* 删除扫描任务 */
export function delTask(ids) {
  return request({
    url: "/nodemanager/soscan/task/" + ids,
    method: "delete",
  });
}

/* 创建URL扫描任务（使用默认策略） */
export function psotScanUrls(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.ScanUrls",
    method: "post",
    data,
  });
}
/* 创建URL扫描任务（使用指定策略） */
export function psotScanUrlsWithProfile(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.ScanUrlsWithProfile",
    method: "post",
    data,
  });
}
/* 创建URL扫描任务（使用指定脚本） */
export function psotScanUrlsWithScripts(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.ScanUrlsWithScripts",
    method: "post",
    data,
  });
}

/* =============专项相关===========*/
/* 创建专项扫描任务（使用指定策略） */
export function psotScanTopicWithProfile(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.ScanTopicWithProfile",
    method: "post",
    data,
  });
}
/* 创建专项扫描任务（使用指定脚本） */
export function psotScanTopicWithScripts(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/BrokerServices.ScanTopicWithScripts",
    method: "post",
    data,
  });
}

/* 根据任务id获取扫描结果 */
export function getScanResult(data) {
  return request({
    url: "/nodemanager/soscan/task/scanResult",
    method: "get",
    params: data,
  });
}
/* 根据任务id获取关联资产 */
export function getGatherAssets(data) {
  return request({
    url: "/nodemanager/soscan/task/gatherAssets",
    method: "get",
    params: data,
  });
}
