/* 空间资产测绘 - 字段管理 - API */
import request from "@/plugins/request";
/* 查询所有数据字段 */
export function getDataFieldList(query) {
  return request({
    url: "/assetdetect/dataField/list",
    method: "get",
    params: query,
  });
}
/* 保存字段 */
export function postDataFieldSave(query) {
  return request({
    url: "/assetdetect/dataField/save",
    method: "post",
    data: query,
  });
}
/* 查询扩展字段 */
export function getDataFieldExtraList(query) {
  return request({
    url: "/assetdetect/dataField/extra/list",
    method: "get",
    params: query,
  });
}
/* 保存扩展字段 */
export function postDataFieldExtraSave(query) {
  return request({
    url: "/assetdetect/dataField/extra/save",
    method: "post",
    data: query,
  });
}
/*获取字典列表*/
export function getFieldAvailableByCnd() {
  return request({
    url: "/config/dictionary/type/select/fieldAvailableByCnd?fieldAvailable=2",
    method: "get",
  });
}
