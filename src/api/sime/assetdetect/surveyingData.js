/* 空间资产测绘 - 测绘数据管理 - API */
import request from "@/plugins/request";

/* 获取列表 */
export function getIndexRecordList(query) {
  return request({
    url: "/assetdetect/indexRecord/list",
    method: "get",
    params: query,
  });
}

/* 索引记录 - 新增 */
export function postIndexRecord(query) {
  return request({
    url: "/assetdetect/indexRecord",
    method: "post",
    data: query,
    timeout: 60000 * 30, // 五分钟超时
  });
}
/* 索引记录 - 删除 */
export function deleteIndexRecord(query) {
  return request({
    url: "/assetdetect/indexRecord",
    method: "delete",
    params: query,
  });
}

/* 网络区域-获取全部区域列表 */
export function getNetworkAreaList() {
  return request({
    url: "/assetdetect/networkArea/list",
    method: "get",
  });
}

/* 网络区域-新增 */
export function postNetworkArea(query) {
  return request({
    url: "/assetdetect/networkArea",
    method: "post",
    data: query,
  });
}
/* 网络区域-删除 */
export function deleteNetworkArea(id) {
  return request({
    url: "/assetdetect/networkArea/" + id,
    method: "delete",
  });
}
// 标签修改
export function getEditNetworkArea(param) {
  return request({
    url: "/assetdetect/networkArea",
    method: "put",
    data: param,
  });
}
// 标签排序
export function getEditNetworkAreaSort(data) {
  return request({
    url: "/assetdetect/networkArea/sort",
    method: "put",
    data,
  });
}
