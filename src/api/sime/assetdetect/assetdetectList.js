/* 空间资产测绘 - 空间资产测绘 - API */
import request from "@/plugins/request";
/* 获取表格字段 */
export function getDataMapList(query) {
  return request({
    url: "/assetdetect/dataField/mapList",
    method: "get",
    params: query,
  });
}

// 查询列表
export function search_list(params) {
  return request({
    url: "/assetdetect/search/list",
    method: "post",
    data: params,
  });
}

// 查询时间段汇总信息
export function search_aggs_sum(params) {
  return request({
    url: "/assetdetect/search/sum",
    method: "post",
    data: params,
  });
}

// 查询
export function getIndexRecord() {
  return request({
    url: "/assetdetect/indexRecord/mapList",
    method: "get",
  });
}

/* 模板列表 */
// 获取所有快速查询模板
export function searchTemplateList() {
  return request({
    url: "/assetdetect/search/template/list",
    method: "get",
  });
}
// 添加二次筛选条件
export function saveFilterTemplate(params) {
  return request({
    url: "/assetdetect/search/template",
    method: "post",
    data: params,
  });
}
// 修改快速查询模板
export function updateSearchTemplate(params) {
  return request({
    url: "/assetdetect/search/template",
    method: "put",
    data: params,
  });
}
/* 删除模板 */
export function deleteSearchTemplate(id) {
  return request({
    url: "/assetdetect/search/template/" + id,
    method: "DELETE",
  });
}
