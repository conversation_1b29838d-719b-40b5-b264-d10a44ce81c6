import request from "@/plugins/request";
/* 专项策略列表分页 */
export function getTopicInfoList(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/TopicInfo.list",
    method: "post",
    data: data,
  });
}
/* 新增专项策略 */
export function topicInfoAdd(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/TopicInfo.add",
    method: "post",
    data: data,
  });
}
/* 编辑专项策略 */
export function topicInfoEdit(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/TopicInfo.edit",
    method: "post",
    data: data,
  });
}
/* 详情专项策略 */
export function topicInfoDetail(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/TopicInfo.getTopicInfoById",
    method: "post",
    data: data,
  });
}
/* 专项策略删除 */
export function deleteTopicInfo(data) {
  return request({
    url: "/nodemanager/starshot/topic/deleteTopicInfo",
    method: "delete",
    data: data,
  });
}
/* 专项策略启用禁用 */
export function topicInfoEnable(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/TopicInfo.updateTopicInfoEnable",
    method: "post",
    data: data,
  });
}
/* 专项任务列表分页 */
export function getTopicList(data) {
  return request({
    url: "/nodemanager/starshot/topic/list",
    method: "get",
    params: data,
  });
}
/* 新增专项任务 */
export function topicAdd(data) {
  return request({
    url: "/nodemanager/starshot/topic",
    method: "post",
    data: data,
  });
}
/* 专项任务修改(任务状态进行中不可编辑) */
export function topicEdit(data) {
  return request({
    url: "/nodemanager/starshot/topic",
    method: "put",
    data: data,
  });
}
/* 专项任务删除 */
export function topicDelete(ids) {
  return request({
    url: "/nodemanager/starshot/topic/" + ids,
    method: "delete",
    // data: {
    //   ids,
    // },
  });
}
/* 专项任务详情(任务状态为进行中不可删除) */
export function topicDetail(data) {
  return request({
    url: "/nodemanager/starshot/topic/getById",
    method: "get",
    params: data,
  });
}
/* 专项任务启动 */
export function startTopic(data) {
  return request({
    url: "/nodemanager/starshot/topic/startTopic",
    method: "post",
    data: data,
  });
}
/* 专项任务终止 */
export function stopTopic(data) {
  return request({
    url: "/nodemanager/starshot/topic/stopTopic",
    method: "post",
    data: data,
  });
}
/* 专项任务启用停用接口 */
export function updateTopicEnable(data) {
  return request({
    url: "/nodemanager/starshot/topic/updateTopicEnable",
    method: "put",
    data: data,
  });
}
