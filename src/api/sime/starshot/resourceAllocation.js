import request from "@/plugins/request";
// 配置端口组:
/* 端口组配置查询 */
export function getPortConfigList(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/PortConfig.list",
    method: "post",
    data: data,
  });
}
/* 新增端口组 */
export function portConfigAdd(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/PortConfig.add",
    method: "post",
    data: data,
  });
}
/* 编辑端口组 */
export function portConfigEdit(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/PortConfig.edit",
    method: "post",
    data: data,
  });
}
/* 端口组删除 */
export function deletePortConfig(data) {
  return request({
    url: "/nodemanager/starshot/task/deletePort",
    method: "post",
    data: data,
  });
}
// 升级
export function postUpgrade(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/upgrade.upgrade",
    method: "post",
    data: data,
  });
}
