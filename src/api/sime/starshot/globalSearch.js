import request from "@/plugins/request";
/* 资产指纹查询列表 */
export function getAssetFingersList(data) {
  return request({
    url: "/nodemanager/starshot/assetFingers/list",
    method: "get",
    params: data,
  });
}
/* 存活资产列表 */
export function getAssetHostsList(data) {
  return request({
    url: "/nodemanager/starshot/assetHosts/list",
    method: "get",
    params: data,
  });
}
/* 开放端口列表 */
export function getAssetPortsList(data) {
  return request({
    url: "/nodemanager/starshot/assetPorts/list",
    method: "get",
    params: data,
  });
}
/* 资产漏洞列表 */
export function getVulnsList(data) {
  return request({
    url: "/nodemanager/starshot/vulns/list",
    method: "get",
    params: data,
  });
}
/* 资产漏洞详情 */
export function getVulnsDetail(data) {
  return request({
    url: "/nodemanager/starshot/vulns/getById/" + data.id,
    method: "get",
  });
}
