import request from "@/plugins/request";
/* poc管理列表 */
export function pocDataInfoList(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/pocDataInfo.list",
    method: "post",
    data: data,
  });
}
//poc管理详情
export function detailsPocDataInfo(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/pocDataInfo.getPocDataInfoByPath",
    method: "post",
    data: data,
  });
}
// poc管理移动组
export function pocDataInfo(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/pocDataInfo.move",
    method: "post",
    data: data,
  });
}
// poc管理左侧树
export function getPocTree(nodeId) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/pocTree.tree",
    method: "post",
    data: {},
  });
}
// poc管理左侧树新增节点
export function addPocTree(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/pocTree.add",
    method: "post",
    data: data,
  });
}

//poc管理左侧树编辑节点
export function detailsPocTree(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/pocTree.edit",
    method: "post",
    data: data,
  });
}
//poc管理左侧树删除节点
export function delPocTree(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/pocTree.delete",
    method: "post",
    data: data,
  });
}
