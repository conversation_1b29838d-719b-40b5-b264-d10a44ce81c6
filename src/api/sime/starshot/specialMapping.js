import request from "@/plugins/request";
/* 专项测绘列表 */
export function getSpecialTaskList(data) {
  return request({
    url: "/nodemanager/starshot/task/list",
    method: "get",
    params: data,
  });
}
/* 专项测绘新增 */
export function specialTaskAdd(data) {
  return request({
    url: "/nodemanager/starshot/task",
    method: "post",
    data: data,
  });
}
/* 专项测绘修改 */
export function specialTaskEdit(data) {
  return request({
    url: "/nodemanager/starshot/task",
    method: "put",
    data: data,
  });
}
/* 专项测绘详情 */
export function specialTaskDetail(data) {
  return request({
    url: "/nodemanager/starshot/task/getById",
    method: "get",
    params: data,
  });
}
/* 专项测绘删除 */
export function specialTaskDelete(ids) {
  return request({
    url: "/nodemanager/starshot/task/" + ids,
    method: "delete",
    data: {
      ids,
    },
  });
}
/* 专项测绘启动 */
export function startTask(data) {
  return request({
    url: "/nodemanager/starshot/task/startTask",
    method: "post",
    data: data,
  });
}
/* 专项测绘终止 */
export function stopTask(data) {
  return request({
    url: "/nodemanager/starshot/task/stopTask",
    method: "post",
    data: data,
  });
}
/* 专项测绘启用停用接口 */
export function updateTaskEnable(data) {
  return request({
    url: "/nodemanager/starshot/task/updateTaskEnable",
    method: "put",
    data: data,
  });
}
/* 获取soss任务列表 */
export function getStarshotTestTask(data) {
  return request({
    url: "/nodemanager/starshot/SyncSoss/getStarshotTestTask",
    method: "get",
    params: data,
  });
}
/* 绑定soss任务 */
export function bindingSossTask(data) {
  return request({
    url: "/nodemanager/starshot/SyncSoss/bindingSossTask",
    method: "post",
    data: data,
  });
}
/* 获取测试矩阵列表 */
export function getStarshotTestMatrix() {
  return request({
    url: "/nodemanager/starshot/SyncSoss/getStarshotTestMatrix",
    method: "get",
  });
}
/* 获取交付经理 */
export function getStarshotDeliveryManager() {
  return request({
    url: "/nodemanager/starshot/SyncSoss/getStarshotDeliveryManager",
    method: "get",
  });
}
/* 获取查询测试资产 -单位 */
export function getDeptId() {
  return request({
    url: "/nodemanager/starshot/SyncSoss/getDeptId",
    method: "get",
  });
}
/* 新建并绑定此soss任务 */
export function saveStarshotTestTask(data) {
  return request({
    url: "/nodemanager/starshot/SyncSoss/saveStarshotTestTask",
    method: "post",
    data: data,
  });
}

//绑定soss任务详情
export function getStarshotTestTaskById(data) {
  return request({
    url: "/nodemanager/starshot/SyncSoss/getStarshotTestTaskById",
    method: "get",
    params: data,
  });
}
// task任务，Vulns漏洞 Topic 资产测绘 Assets资产
// 专项任务同步soss 同步漏洞
export function syncTopicVulnsResults(data, isMsgHide) {
  return request({
    url: "/nodemanager/starshot/task/syncVulnsResults/" + data.taskid,
    method: "post",
    isMsgHide: isMsgHide,
  });
}
// 专项任务同步soss 同步资产
export function syncTopicAssetsResults(data, isMsgHide) {
  return request({
    url: "/nodemanager/starshot/task/syncAssetsResults/" + data.taskid,
    method: "post",
    isMsgHide: isMsgHide,
  });
}
//资产测绘 资产
export function syncTaskAssetsResults(data, isMsgHide) {
  return request({
    url: "/nodemanager/starshot/topic/syncAssetsResults/" + data.taskid,
    method: "post",
    isMsgHide: isMsgHide,
  });
}
//资产测绘 漏洞
export function syncTaskVulnsResults(data, isMsgHide) {
  return request({
    url: "/nodemanager/starshot/topic/syncVulnsResults/" + data.taskid,
    method: "post",
    isMsgHide: isMsgHide,
  });
}
