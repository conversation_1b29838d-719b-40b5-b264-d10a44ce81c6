import request from "@/plugins/request";
/* 服务指纹列表 */
export function getServiceFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceFinger.list",
    method: "post",
    data: data,
  });
}
// 服务指纹删除
export function delServiceFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceFinger.delete",
    method: "post",
    data: data,
  });
}
// 服务指纹新增
export function addServiceFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceFinger.add",
    method: "post",
    data: data,
  });
}
// 服务指纹编辑
export function editServiceFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceFinger.edit",
    method: "post",
    data: data,
  });
}
//服务指纹详情
export function detailsServiceFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceFinger.getServiceFingerById",
    method: "post",
    data: data,
  });
}
//服务指纹是否启用
export function updateServiceFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceFinger.updateServiceFingerEnable",
    method: "post",
    data: data,
  });
}
/* web指纹列表 */
export function getWebFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webFinger.list",
    method: "post",
    data,
  });
}
// web指纹删除
export function delWebFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webFinger.delete",
    method: "post",
    data: data,
  });
}
// Web指纹新增
export function addWebFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webFinger.add",
    method: "post",
    data: data,
  });
}
// Web指纹编辑
export function editWebFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webFinger.edit",
    method: "post",
    data: data,
  });
}
//Web指纹详情
export function detailsWebFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webFinger.getWebFingerById",
    method: "post",
    data: data,
  });
}
//Web指纹是否启用
export function updateWebFinger(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webFinger.updateWebFingerEnable",
    method: "post",
    data: data,
  });
}
