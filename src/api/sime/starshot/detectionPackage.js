import request from "@/plugins/request";
/* 服务探测包列表 */
export function getServiceProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceProbe.list",
    method: "post",
    data: data,
  });
}
// 服务探测包删除
export function delServiceProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceProbe.delete",
    method: "post",
    data: data,
  });
}
// 服务探测包新增
export function addServiceProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceProbe.add",
    method: "post",
    data: data,
  });
}
// 服务探测包编辑
export function editServiceProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceProbe.edit",
    method: "post",
    data: data,
  });
}
//服务探测包详情
export function detailsServiceProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceProbe.getServiceProbeById",
    method: "post",
    data: data,
  });
}
//服务探测包是否启用
export function updateServiceProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/serviceProbe.updateServiceProbeEnable",
    method: "post",
    data: data,
  });
}
/* Web探测包列表 */
export function getWebProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webProbe.list",
    method: "post",
    data,
  });
}
// Web探测包删除
export function delWebProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webProbe.delete",
    method: "post",
    data: data,
  });
}
// Web探测包新增
export function addWebProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webProbe.add",
    method: "post",
    data: data,
  });
}
// Web探测包编辑
export function editWebProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webProbe.edit",
    method: "post",
    data: data,
  });
}
//Web探测包详情
export function detailsWebProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webProbe.getWebProbeById",
    method: "post",
    data: data,
  });
}
//Web探测包是否启用
export function updateWebProbe(nodeId, data) {
  return request({
    url: "/nodemanager/proxy/" + nodeId + "/webProbe.updateWebProbeEnable",
    method: "post",
    data: data,
  });
}
