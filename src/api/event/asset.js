import Axios from "@/plugins/request";
//业务系统对象 --- 查列表
export function selectBussiness(param) {
  return Axios({
    url: "/system/affected/selectBussiness",
    method: "get",
    params: param,
  });
}
// 业务系统对象 --- 查详情
export function selectBusinessDetail(param) {
  return Axios({
    url: "/system/affected/selectBusinessDetail",
    method: "get",
    params: param,
  });
}

//计算设备对象 --- 查列表
export function getRelResourceList(param) {
  return Axios({
    url: "/system/affected/getRelResourceList",
    method: "get",
    params: param,
  });
}
//计算设备对象 --- 查详情
export function getRelResourceDetail(param) {
  return Axios({
    url: "/system/affected/getRelResourceDetail",
    method: "get",
    params: param,
  });
}
//终端资产对象 --- 列表
export function selectEventAssetsTerminal(param) {
  return Axios({
    url: "/system/affected/selectEventAssetsTerminal",
    method: "get",
    params: param,
  });
}
//终端资产对象 --- 详情
export function selectTerminalDetail(param) {
  return Axios({
    url: "/system/affected/selectTerminalDetail",
    method: "get",
    params: param,
  });
}

//域名待确认资产 --- 列表
export function getDomainConfirmList(param) {
  return Axios({
    url: "/system/affected/getDomainConfirmList",
    method: "get",
    params: param,
  });
}
//   ip待确认资产 --- 列表
export function selectIpConfirm(param) {
  return Axios({
    url: "/system/affected/selectIpConfirm",
    method: "get",
    params: param,
  });
}
// 业务系统对象关联

export function addRelevancy(param) {
  return Axios({
    url: "/system/affected/addRelevancy",
    method: "post",
    data: param,
  });
}
// 计算设备对象 --- 添加关联要素

export function addResourceRelContent(param) {
  return Axios({
    url: "/system/affected/addResourceRelContent",
    method: "post",
    data: param,
  });
}
// 业务取消关联

export function businessDisassociate(param) {
  return Axios({
    url: "/system/affected/businessDisassociate",
    method: "delete",
    data: param,
  });
}
// 计算设备对象 --- 取消关联
export function cancelEventResourceRel(param) {
  return Axios({
    url: "/system/affected/cancelEventResourceRel",
    method: "delete",
    data: param,
  });
}
// 终端资产对象 --- 取消关联
export function terminalDisassociate(param) {
  return Axios({
    url: "/system/affected/terminalDisassociate",
    method: "delete",
    data: param,
  });
}
// 域名待确认资产 --- 取消关联
export function cancelEventDomainConfirmRel(param) {
  return Axios({
    url: "/system/affected/cancelEventDomainConfirmRel",
    method: "delete",
    data: param,
  });
}
// ip类待确认资产 --- 取消关联
export function deleteEventIpConfirmRel(param) {
  return Axios({
    url: "/system/affected/deleteEventIpConfirmRel",
    method: "delete",
    data: param,
  });
}
// 业务系统对象-关联
export function saveBusinessRelevancy(param) {
  return Axios({
    url: "/system/affected/saveBusinessRelevancy",
    method: "post",
    data: param,
  });
}
// 计算设备对象 --- 关联
export function saveConnectResource(param) {
  return Axios({
    url: "/system/affected/saveConnectResource",
    method: "post",
    data: param,
  });
}
// 终端资产对象 --- 关联
export function saveEventAssetsTerminal(param) {
  return Axios({
    url: "/system/affected/saveEventAssetsTerminal",
    method: "post",
    data: param,
  });
}
// 域名待确认资产 --- 关联
export function saveConnectDomainConfirm(param) {
  return Axios({
    url: "/system/affected/saveConnectDomainConfirm",
    method: "post",
    data: param,
  });
}
// ip类待确认资产 --- 关联
export function saveEventIpConfirmRel(param) {
  return Axios({
    url: "/system/affected/saveEventIpConfirmRel",
    method: "post",
    data: param,
  });
}
// 公用方法 --- 修改关联要素
export function updateRelContent(param) {
  return Axios({
    url: "/system/affected/updateRelContent",
    method: "put",
    data: param,
  });
}
// 公用方法 --- 删除关联要素
export function delRelContent(param) {
  return Axios({
    url: "/system/affected/delRelContent",
    method: "delete",
    data: param,
  });
}
// 计算设备对象 --- 关联要素列表
export function getContentList(param) {
  return Axios({
    url: " /system/affected/getContentList",
    method: "get",
    params: param,
  });
}
