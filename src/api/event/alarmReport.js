import Axios from "@/plugins/request";
import router from "@/router";
//已删除对象列表
export const selectAlreadyDelAlert = (params) => {
  return Axios.get("/system/workbench/selectAlreadyDelAlert", { params });
};

//告警与报告
// 查询告警与报告列表
export const selectAlarmReportPage = (params) => {
  return Axios.get("/system/event/selectAlarmReportPage", { params });
};
// 新增告警与报告
export function saveAlarm(params) {
  return Axios.post(`/system/event/saveAlarm`, params);
}
// 删除任务
export function deleteAlarm(query) {
  return Axios({
    url: "/system/event/deleteAlarm",
    method: "DELETE",
    data: query,
  });
}
// 查询告警列表
export const selectEventAlertPage = (params) => {
  return Axios.get("/system/event/selectEventAlertPage", { params });
};
// 查看告警详情
export const selectarningAwaitingConfirmatio = (params) => {
  return Axios.get("/system/workbench/selectarningAwaitingConfirmatio", { params });
};
