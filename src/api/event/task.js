import Axios from "@/plugins/request";
//查询任务列表
export function initTaskList(param) {
  return Axios({
    url: "/system/eventTask/initTaskList",
    method: "get",
    params: param,
  });
}
// 任务详情中执行日志查询方法
export function getLogList(param) {
  return Axios({
    url: "/system/eventTask/getLogList",
    method: "get",
    params: param,
  });
}
// 审核结果列表
export function getAuditList(eventTaskId) {
  return Axios({
    url: "/system/eventTask/getAuditList?eventTaskId=" + eventTaskId,
    method: "get",
  });
}

//查询批量审核任务
export function selectNotAuditedTask(params) {
  return Axios({
    url: "/system/eventTask/selectNotAuditedTask",
    method: "get",
    params,
  });
}

//批量审核
export function saveEventTaskAudit(data) {
  return Axios({
    url: "/system/eventTask/saveEventTaskAudit",
    method: "post",
    data,
  });
}

//新建任务
export function saveEventTask(data) {
  return Axios({
    url: "/system/eventTask/saveEventTask",
    method: "post",
    data,
  });
}

//查询任务详情
export function viewEventTaskDetail(id) {
  return Axios({
    url: "/system/eventTask/viewEventTaskDetail?id=" + id,
    method: "get",
  });
}

//修改任务
export function updateEventTask(data) {
  return Axios({
    url: "/system/eventTask/updateEventTask",
    method: "put",
    data,
  });
}

//保存任务日志
export function saveTaskLog(data) {
  return Axios({
    url: "/system/eventTask/saveTaskLog",
    method: "post",
    data,
  });
}

//删除任务日志
export function deleteTaskLog(data) {
  return Axios({
    url: "/system/eventTask/deleteTaskLog",
    method: "delete",
    data,
  });
}
//删除任务日志文件
export function removeFile(data) {
  return Axios({
    url: "/system/eventTask/removeFile",
    method: "delete",
    data,
  });
}

//开始任务
export function startTask(data) {
  return Axios({
    url: "/system/eventTask/startTask",
    method: "post",
    data,
  });
}

//关闭任务
export function closeTask(data) {
  return Axios({
    url: "/system/eventTask/closeTask",
    method: "post",
    data,
  });
}

//提报任务
export function submitTask(data) {
  return Axios({
    url: "/system/eventTask/submitTask",
    method: "post",
    data,
  });
}

//删除任务
export function deleteTask(data) {
  return Axios({
    url: "/system/eventTask/deleteTask",
    method: "delete",
    data,
  });
}

//审核任务
export function saveAuditEvent(data) {
  return Axios({
    url: "/system/eventTask/saveAudit",
    method: "post",
    data,
  });
}

//重做任务
export function redoTask(data) {
  return Axios({
    url: "/system/eventTask/redoTask",
    method: "put",
    data,
  });
}

//任务日志汇总
export function initEventTaskLogList(params) {
  return Axios({
    url: "/system/eventTask/initEventTaskLogList",
    method: "get",
    params,
  });
}

/* 新增 - （202404）保存事件任务ES原始日志 */
export function saveEventTaskEsLog(data) {
  return Axios({
    url: "/system/eventTask/saveEventTaskEsLog",
    method: "post",
    data,
  });
}
/* 新增 - （202404）根据事件任务id查询ES日志 */
export function getEventTaskEsLog(params) {
  return Axios({
    url: "/system/eventTask/getEventTaskEsLog",
    method: "get",
    params,
  });
}
/* 新增 - （202404）查询ES日志组集合 */
export function getEventTaskEsLogGroupIdList(params) {
  return Axios({
    url: "/system/eventTask/getEventTaskEsLogGroupIdList",
    method: "get",
    params,
  });
}
/* 新增 - （202404）根据ES日志组id删除 */
export function deleteEventTaskEsLog(id) {
  return Axios({
    url: "/system/eventTask/deleteEventTaskEsLog/" + id,
    method: "delete",
  });
}
