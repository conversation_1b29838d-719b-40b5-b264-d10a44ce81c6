import request from "@/plugins/request";

// 查询评估任务
export function selectSafetyTask(params) {
  return request.get(`/system/leakTask/selectSafetyTask`, { params });
}
// 测试邮箱域
export function selectEmailDomain(params) {
  return request.get(`/system/leakTask/selectEmailDomain`, { params });
}
// 点击提交行为时间分布
export function selectTimeResult(params) {
  return request.get(`/system/leakTask/selectTimeResult`, { params });
}
// 点击运行附件行为占比
export function getRightPercent(params) {
  return request.get(`/system/leakTask/getRightPercent`, { params });
}
// 测试邮箱清单
export function selectTestEmail(params) {
  return request.get(`/system/leakTask/selectTestEmail`, { params });
}
// 伪造域选择记录
export function selectForgeDomain(params) {
  return request.get(`/system/leakTask/selectForgeDomain`, { params });
}
// 测试记录
export function selectTestRecord(params) {
  return request.get(`/system/leakTask/selectTestRecord`, { params });
}
// 结果清单
export function selectTestResult(params) {
  return request.get(`/system/leakTask/selectTestResult`, { params });
}
// 查询服务成果统计
export function selectSafetyTaskById(params) {
  return request.get(`/system/leakTask/selectSafetyTaskById`, { params });
}
// 导入安全意识隐患
export function insertSafetyTask(params) {
  return request.post(`/system/leakTask/insertSafetyTask`, params);
}
// 伪造邮件文案
export function selectTaskFile(params) {
  return request.get(`/system/leakTask/selectTaskFile`, { params });
}
// 安全预警
// 列表查询
export function earlywarning(params) {
  return request.get(`/system/earlywarning/getList`, { params });
}

// 新安全预警--漏洞预警
// 列表查询
export function getWarningList(params) {
  return request.get(`/system/earlywarning/getWarningList`, { params });
}
