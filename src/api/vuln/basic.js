import request from "@/plugins/request";

// 查询父级列表
export function getVulnParentList_2(params) {
  return request.get(`/system/vulnBasic/getVulnParentList`, { params });
}
//   根据资产id查询漏洞列表
export function getVulnList_2(params) {
  return request.get(`/system/vulnBasic/getVulnList`, { params });
}

//查询漏洞详情
export function selectVulnDetail_2(params) {
  return request.get(`/system/vulnBasic/selectVulnDetail`, { params });
}

//反馈整改
export function saveBackRectified_2(params) {
  return request.put(`/system/vulnBasic/saveBackRectified`, params);
}

//反馈误报
export function saveBackMisreport_2(params) {
  return request.put(`/system/vulnBasic/saveBackMisreport`, params);
}

//忽略
export function saveBackIgnore_2(params) {
  return request.put(`/system/vulnBasic/saveBackIgnore`, params);
}

//批量反馈整改
export function saveBatchRectified_2(params) {
  return request.put(`/system/vulnBasic/saveBatchRectified`, params);
}

//批量反馈误报
export function saveBatchMisreport_2(params) {
  return request.put(`/system/vulnBasic/saveBatchMisreport`, params);
}

//批量忽略
export function saveBatchIgnore_2(params) {
  return request.put(`/system/vulnBasic/saveBatchIgnore`, params);
}

//批量手动过滤
export function saveBatchFiltered_2(params) {
  return request.put(`/system/vulnBasic/saveBatchFiltered`, params);
}

//导出漏洞
export function exportVuln_2(params) {
  return request.put(`/system/vulnBasic/exportVuln`, params);
}
// 统计总数
export function selectBasicResourceVulnCount() {
  return request({
    url: "/system/vulnBasic/selectBasicResourceVulnCount",
    method: "get",
  });
}

//查询基础资源漏洞类型
export function selectVulnType_2(params) {
  return request.get(`/system/vulnBasic/selectBasicType`, { params });
}
