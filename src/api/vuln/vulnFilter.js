import request from "@/plugins/request";
export function getVulnTypeTree(vulnType) {
  return request({
    url: "/system/vulnFilter/getVulnTypeTree?vulnType=" + vulnType,
    method: "get",
  });
}
// 获取选择的漏洞列表
export function getVulnResourceList(params) {
  return request({
    url: "/system/vulnBasic/getVulnResourceList",
    method: "get",
    params: params,
  });
}
// 获取资产组
export function getAssetsGroup() {
  return request({
    url: "/system/assetsGroup/selectGroupList",
    method: "get",
  });
}
// 获取业务系统
export function getBusinessList(params) {
  return request({
    url: "/system/assetsBusiness/list",
    method: "get",
    params: params,
  });
}
///漏洞忽略规则列表查询 /system/vulnFilter/getGlobalList
export function getGlobalList(params) {
  return request({
    url: "/system/vulnFilter/getGlobalList",
    method: "get",
    params,
  });
}
// 添加漏洞忽略规则
export function saveGlobalList(params) {
  return request({
    url: "/system/vulnFilter/saveGlobalList",
    method: "post",
    params: params,
  });
}
// 修改漏洞忽略规则状态
export function updateGlobalList(params) {
  return request({
    url: "/system/vulnFilter/deleteGlobalList",
    method: "post",
    params: params,
  });
}
// 业务系统误报忽略清单
export function selectPageMisreportBusiness(params) {
  params.screeningType = 2;
  return request({
    url: "/system/vulnFilter/selectPageMisreportBusiness",
    method: "get",
    params,
  });
}
// 基础资源误报忽略列表
export function selectPageMisreportResource(params) {
  params.screeningType = 2;
  return request({
    url: "/system/vulnFilter/selectPageMisreportResource",
    method: "get",
    params,
  });
}
// 业务系统主动忽略清单
export function selectPageIgnoreBusiness(params) {
  params.screeningType = 3;
  return request({
    url: "/system/vulnFilter/selectPageIgnoreBusiness",
    method: "get",
    params,
  });
}
// 基础资源主动忽略清单
export function selectPageIgnoreResource(params) {
  params.screeningType = 3;
  return request({
    url: "/system/vulnFilter/selectPageIgnoreResource",
    method: "get",
    params,
  });
}
// 漏洞误报忽略清单和主动忽略清单删除接口
export function deleteIngroeVuln(ids) {
  return request({
    url: "/system/vulnFilter/deleteIngroeVuln?ids=" + ids,
    method: "post",
  });
}
// 误报清单中误报证明
export function viewMisReport(id) {
  return request({
    url: "/system/vulnFilter/viewMisReport?id=" + id,
    method: "GET",
  });
}
