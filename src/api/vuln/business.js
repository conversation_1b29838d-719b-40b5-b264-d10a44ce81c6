import request from "@/plugins/request";

// 查询父级列表
export function getVulnParentList_1(params) {
  return request.get(`/system/vulnBusiness/getVulnParentList`, { params });
}
//   根据资产id查询漏洞列表
export function getVulnList_1(params) {
  return request.get(`/system/vulnBusiness/getVulnList`, { params });
}

//查询漏洞详情
export function selectVulnDetail_1(params) {
  return request.get(`/system/vulnBusiness/selectVulnDetail`, { params });
}

//反馈整改
export function saveBackRectified_1(params) {
  return request.put(`/system/vulnBusiness/saveBackRectified`, params);
}

//反馈误报
export function saveBackMisreport_1(params) {
  return request.put(`/system/vulnBusiness/saveBackMisreport`, params);
}

//忽略
export function saveBackIgnore_1(params) {
  return request.put(`/system/vulnBusiness/saveBackIgnore`, params);
}

//批量反馈整改
export function saveBatchRectified_1(params) {
  return request.put(`/system/vulnBusiness/saveBatchRectified`, params);
}

//批量反馈误报
export function saveBatchMisreport_1(params) {
  return request.put(`/system/vulnBusiness/saveBatchMisreport`, params);
}

//批量忽略
export function saveBatchIgnore_1(params) {
  return request.put(`/system/vulnBusiness/saveBatchIgnore`, params);
}

//批量手动过滤
export function saveBatchFiltered_1(params) {
  return request.put(`/system/vulnBusiness/saveBatchFiltered`, params);
}

//导出漏洞
export function exportVuln_1(params) {
  return request.put(`/system/vulnBusiness/exportVuln`, params);
}

//查询漏洞反馈详情
export function selectVulnFeedBackDetail_1(params) {
  return request.get(`/system/vulnBusiness/selectVulnFeedBackDetail`, { params });
}

//查询漏洞类型
export function selectVulnType_1(params) {
  return request.get(`/system/vulnBusiness/selectVulnType`, { params });
}

// 统计总数
export function getBusinessVulnCount() {
  return request({
    url: "/system/vulnBusiness/getBusinessVulnCount",
    method: "get",
  });
}
