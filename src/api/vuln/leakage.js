import request from "@/plugins/request";

// 监测任务列表
export function ParentList(params) {
  return request.get(`/system/leakTask/list`, { params });
}
//关键词清单
export function keywordsDetail(params) {
  return request.get(`/system/leakTask/keywordsDetail`, { params });
}
// 关键词站点
export function keywordsSiteDetail(params) {
  return request.get(`/system/leakTask/keywordsSiteDetail`, { params });
}
// GitHub敏感信息
export function gitHubDetail(params) {
  return request.get(`/system/leakTask/gitHubDetail`, { params });
}
// 网盘敏感信息
export function networkDiskDetail(params) {
  return request.get(`/system/leakTask/networkDiskDetail`, { params });
}
// 互联网暴露邮箱
export function exposeEmailDetail(params) {
  return request.get(`/system/leakTask/exposeEmailDetail`, { params });
}
// 公众账号
export function wechatDetail(params) {
  return request.get(`/system/leakTask/wechatDetail`, { params });
}
// 导入监测任务
export function uploadLeakTask(params) {
  return request.post(`/system/leakTask/uploadLeakTask`, params);
}
