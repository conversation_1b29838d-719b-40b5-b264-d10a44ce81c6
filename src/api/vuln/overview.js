import request from "@/plugins/request";

// 监测任务列表
export function getVulnLevelSummary(params) {
  return request.get(`/system/summary/getVulnLevelSummary`, { params });
}
// 统计近6个月高危及以上漏洞的个数
export function getVulnHighOrAboveSummary(params) {
  return request.get(`/system/summary/getVulnHighOrAboveSummary`, { params });
}
// 漏洞发现/整改态势图
export function getVulnFindOrCorrectSummary(params) {
  return request.get(`/system/summary/getVulnFindOrCorrectSummary`, { params });
}
// 已发漏洞（Top10）整改情况分布
export function getVulnCorrectRateSummary(params) {
  return request.get(`/system/summary/getVulnCorrectRateSummary`, { params });
}
// 已完成敏感信息泄露测试
export function getLeakageSummary(params) {
  return request.get(`/system/summary/getLeakageSummary`, { params });
}
// 已完成安全意识评估
export function getSafeTySummary(params) {
  return request.get(`/system/summary/getSafeTySummary`, { params });
}
