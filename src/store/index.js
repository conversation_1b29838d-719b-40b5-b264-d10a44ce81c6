import Vuex from "vuex";
import setCustomChart from "starso-components-ui/src/store/customEchart";
import { getInfo, getLogo, getSysTitle } from "@/api/login";
import modules from "./modules";
import { echartConfig } from "../config/echartStore";

import Cookies from "js-cookie";
import { getRouters } from "@/api/menu";
import router from "@/router";
import { routes } from "@/router";

import { TOKEN_NAME } from "@/config/constant";
import { editEchartOption, getEchartOption, resetEditChart } from "@/api/workSpace/echartApi";
let customEchart = setCustomChart(echartConfig, editEchartOption, getEchartOption, resetEditChart);
const store = new Vuex.Store({
  modules: { customEchart, ...modules },
  state: {
    userInfo: null, //用户信息
    roles: [], //角色，如一线分析师
    permissions: [], //权限按钮列表
    permissionsRequested: false,
    allRouters: [], //权限路由
    tabsList: [], //页签
    logo: {
      big: "",
      small: "",
    },
    needChangePassword: false, //登录用户是否需要修改密码
  },
  getters: {
    sidebar: (state) => {
      return state.app.sidebar;
    },
  },
  mutations: {
    //退出登录
    loginout(state) {
      store.commit("clearWebinfo");
      router.push("/login");
    },
    clearWebinfo(state) {
      localStorage.removeItem(TOKEN_NAME);
      state.userInfo = null;
      state.roles = []; //角色，如一线分析师
      state.permissions = []; //权限按钮列表
      state.allRouters = []; //权限路由
      state.tabsList = []; //页签
      state.logo = {
        big: "",
        small: "",
      };
      state.needChangePassword = false;
    },
    changePassword(state, flag) {
      state.needChangePassword = flag == "1";
    },

    changeAllRouters(state, list) {
      //子应用
      if (window.$wujie) {
        return;
      }
      state.allRouters = list;
      let realTitle = _getRouteLable(list, router.currentRoute.value.name);

      if (state.tabsList.length > 0) {
        state.tabsList[0].meta.title = realTitle || state.tabsList[0].meta.title;
      }
      let firstRoute = list[0].children[0];

      if (firstRoute.name != "Home" && (window.location.href.includes("/login") || window.location.href.includes("/licensePage"))) {
        store.commit("closeCurrentTab");
        router.push({ name: firstRoute.name });
      }
    },
    pushTab(state, route) {
      if (!state.tabsList.find((item) => item.name == route.name)) {
        if (route.meta && route.meta.title) {
          let realTitle = _getRouteLable(state.allRouters, route.name);
          let routeData = { ...route };
          routeData.meta.title = realTitle || routeData.meta.title;
          state.tabsList.push(routeData);
        }
      } else {
        let realTitle = _getRouteLable(state.allRouters, route.name);
        let routeData = { ...route };
        routeData.meta.title = realTitle || routeData.meta.title;
        let _index = state.tabsList.findIndex((item) => item.name == route.name);
        state.tabsList[_index] = routeData;
      }
    },
    closeTab(state, index) {
      state.tabsList.splice(index, 1);
    },
    closeCurrentTab(state) {
      let index = state.tabsList.findIndex((item) => item.fullPath == router.currentRoute.value.fullPath);

      state.tabsList.splice(index, 1);
    },
    closeTabById(state, id, paramKey = "id") {
      let index = state.tabsList.findIndex((item) => item.params[paramKey] == id);
      if (index > -1) {
        state.tabsList.splice(index, 1);
      }
    },
    closeOtherTabs(state, item) {
      let current = state.tabsList.find((tab) => tab.name == item.name);

      state.tabsList.splice(0, state.tabsList.length);
      state.tabsList.push(current);
    },
    setUserInfo(state, user) {
      state.userInfo = user;
    },
    setLogo(state, logo) {
      state.logo = {
        big: logo.big,
        small: logo.small,
      };
    },
    setRoles(state, roles) {
      state.roles = roles;
    },
    setPermissions(state, permissions) {
      state.permissions = permissions;
      state.permissionsRequested = true;
    },
    TOGGLE_SIDEBAR: (state) => {
      state.app.sidebar.opened = !state.app.sidebar.opened;
      state.app.sidebar.withoutAnimation = false;
      if (state.app.sidebar.opened) {
        Cookies.set("sidebarStatus", 1);
      } else {
        Cookies.set("sidebarStatus", 0);
      }
    },
  },
  actions: {
    //有权限访问页面后获取数据
    loginSuccess(context) {
      getInfoFn(context);

      /* 解决接口404问题，判断是否为 siem, 否时执行该接口 */
      if (!import.meta.env.VITE_IS_SIME) getLogoFn(context);
    },
    updateUserInfo(context) {
      getInfo().then((res) => {
        let { user } = res;
        context.commit("setUserInfo", user);
      });
    },
    toggleSideBar({ commit }) {
      commit("TOGGLE_SIDEBAR");
    },
  },
  // modules,
});

//获取用户信息
function getInfoFn(context) {
  getInfo().then((res) => {
    let { changePassword, user, roles, permissions } = res;
    context.commit("setUserInfo", user);
    context.commit("setRoles", roles);
    context.commit("setPermissions", permissions);

    if (changePassword != "1") {
      getRoutersFn(context);
    }
    context.commit("changePassword", changePassword);
  });
}

//获取路由信息
function getRoutersFn(context) {
  getRouters().then((res) => {
    context.commit("changeAllRouters", res.data);
  });
}

//获取logo
function getLogoFn(context) {
  /* 获取标题 - 前哨  */
  getSysTitle().then((res1) => {
    if (res1 && res1.data && res1.data.data) {
      document.title = res1.data.data;
    }
  });

  getLogo().then((res) => {
    let data = res.data.logoList;

    if (data && data.length > 0) {
      let bigItem = data.find((item) => item.type == "bigLogo");
      let big = bigItem ? bigItem.fileUrl : "";
      let smallItem = data.find((item) => item.type == "smallLogo");

      let small = smallItem ? smallItem.fileUrl : "";
      context.commit("setLogo", {
        big,
        small,
      });
    }
  });
}
function _getRouteLable(routes, name) {
  for (let item of routes) {
    if (item.children) {
      let selected = item.children.find((item) => item.name == name);
      if (selected) {
        return selected.meta.title;
      }
    }
  }
}

//动态添加路由
// function addRoutes(routes, paraneName) {
//   routes.forEach((item) => {
//     router.addRoute(item);
//   });
//   //刷新动态路由页面

//   router.push({ path: router.currentRoute.value.fullPath });
// }

export default store;
