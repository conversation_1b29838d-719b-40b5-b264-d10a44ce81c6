import { getDictsData } from "@/utils/getDicts";
const state = {
  numberOperators: null,
  stringOperators: null,
  ruleOperators: null,
  ruleOperatorObj: {
    ruleString: null,
    ruleNum: null,
  },
  numberFlag: false, //是否是第一次加载
  stringFlag: false, //是否是第一次加载
  ruleFlag: false, //是否是第一次加载
};
const mutations = {
  setNumberOperators(state, operators) {
    state.numberOperators = operators;
    state.numberFlag = true;
  },
  setStringOperators(state, operators) {
    state.stringOperators = operators;
    state.stringFlag = true;
  },
  setRuleOperators(state, operators) {
    state.ruleOperators = operators;
    state.ruleFlag = true;
  },
  setRuleOperatorObj(state, operators) {
    state.ruleOperatorObj = operators;
    state.ruleFlag = true;
  },
};
const actions = {
  async getFilterOperatorNumRes() {
    if (state.numberFlag) {
      return;
    }
    const numberRes = await getDictsData("config_filterOperator_number");
    mutations.setNumberOperators(state, numberRes);
  },
  async getFilterOperatorStrRes() {
    if (state.stringFlag) {
      return;
    }
    const stringRes = await getDictsData("config_filterOperator_string");
    mutations.setStringOperators(state, stringRes);
  },
  async getRuleOperatorRes() {
    if (state.ruleFlag) {
      return;
    }
    // const ruleOperatorRes = await getDictsData("config_refRule_operator");
    // mutations.setRuleOperators(state, ruleOperatorRes);
    const ruleOperatorString = await getDictsData("config_refRuleOperator_string");
    const ruleOperatorNum = await getDictsData("config_refRuleOperator_num");
    mutations.setRuleOperatorObj(state, {
      ruleString: ruleOperatorString, // 字段是c开头的字符串时使用
      ruleNum: ruleOperatorNum, //字段是i\l\d开头或者time结尾时使用
    });
  },
};
export default {
  state,
  mutations,
  actions,
};
