import { getEventDetail, getEventAuditFlg, getEventLogList } from "@/api/event/detail";
import { getAuditResults } from "@/api/event/eventList";

import { searchOperationParam } from "@/api/param/config";
import router from "@/router";

const state = {
  inToEventPage: false, //请求权限接口标志
  pageStatus: true, //页面刷新标识
  eventInfo: {},
  //当前的tab
  activeTab: "0",
  //动态tab列表
  //? type，同时是组件名:taskDetail 任务详情 ||  attachment 附件 || suspiciousDetail 可疑对象详情 ||  logList 任务日志汇总
  trendsTabList: [
    // {
    //   name: "111", //任务id或者其他唯一值 唯一！！！ 任务详情为任务id,其他为type
    //   label: "动态tab示例",
    //   type: "taskDetail", //确定组件
    // },
  ],
  eventFindings: [], //事件审核结果 列表数据
  //事件按钮
  eventBtns: {
    closeFlag: false, //关闭
    rejectFlag: false, //驳回
    flg: false, //审核
    changeFlg: false, //变更
    mergeFlg: false, //合并
    isReject: false, //是否已驳回
    eventDelete: false, //删除
  },
  //事件提报审核按钮
  eventAuditBtns: {
    audit: false, //提报审核
    findings: false, //审核结果
    submitFindings: false, //提交审核结果
  },
  // 综述按钮
  overviewBtns: {
    eventUpdate: false, //摘要
    eventDepiction: false, //描述和结论
  },
  //事件tabs上的数字
  tabNums: {
    1: 0,
    2: 0,
    3: 0,
    4: 0,
    5: 0,
    6: 0,
  },

  //任务列表id对应的按钮组
  taskListBtns: {
    taskId: {
      startFlag: false, //开始
      submitFlag: false, //提报
      deleteFalg: false, //删除
      reauditFlag: false, //重审
      closeFlag: false, //关闭
      redoFlag: false, //重做
      auditFlag: false, //审核
    },
  },

  //状态流 列表
  eventLogList: [],
  //任务更新标识
  taskUpdate: {
    status: true,
    id: "",
  },
  eventDetail: {}, //事件详情信息
  eventTaskButs: {
    eventTaskButShow: false, //事件详情内任务按钮,auditResultStatus != 4 ,且event_task_editable_before_adopt 为0 时不能编辑
    auditResultStatus: false, //事件状态标识：审核通过为true auditResultStatus == 4 ,
    editable: false, //是否可编辑
  },
};

const mutations = {
  //修改任务按钮
  changeEventTaskButs(state, flag) {
    state.eventTaskButs = flag;
  },
  //修改事件详情
  changeEventInfo(state, info) {
    state.eventInfo = info;
  },
  changeInToEventPage(state) {
    state.inToEventPage = true;
  },
  //修改事件审核记录
  changeEventFindings(state, info) {
    state.eventFindings = info;
  },
  //刷新页面
  reload(state) {
    state.pageStatus = false;
    state.inToEventPage = false;
    setTimeout(() => {
      state.pageStatus = true;
    }, 200);
    setTimeout(() => {
      state.inToEventPage = true;
    }, 2000);
  },
  //修改事件按钮
  changeEventFlag(state, o) {
    state.eventBtns = o;
  },
  //修改事件提报审核按钮
  changeEventAuditFlag(state, o) {
    state.eventAuditBtns = o;
  },
  // 修改综述
  revisionOverview(state, val) {
    state.overviewBtns = val;
  },
  //修改事件状态流
  changeEventLogList(state, list) {
    state.eventLogList = list;
  },
  //修改tab上的数字
  changeTabNums(state, obj) {
    state.tabNums = obj;
  },
  //根据传入的name值修改
  changeTabNumsByName(state, payload) {
    state.tabNums[payload.name] = payload.num;
  },
  //修改当前打开的tab
  changeActiveTab(state, name) {
    state.activeTab = name + "";
  },
  //!新打开动态tab ,其中任务的tab页，name是taskId
  pushEventTab(state, tab) {
    if (!state.trendsTabList.find((item) => item.name == tab.name)) {
      state.trendsTabList.push(tab);
    }
    state.activeTab = tab.name;
  },
  //更新tab
  updateEventActiveTab(state, data) {
    let selectedIndex = state.trendsTabList.findIndex((item) => item.name == state.activeTab);

    if (selectedIndex > -1) {
      state.trendsTabList[selectedIndex] = { ...state.trendsTabList[selectedIndex], ...data };
    }
  },
  //删除tab
  delEventTab(state, index) {
    let removedTab = state.trendsTabList[index];

    state.trendsTabList.splice(index, 1);
    if (state.activeTab == removedTab.name) {
      _handlerTab(state, index);
    }
  },
  //根据任务id关闭打开的任务tab
  delTabByName(state, name) {
    let taskTabIndex = state.trendsTabList.findIndex((item) => item.name == name);
    if (taskTabIndex > -1) {
      mutations.delEventTab(state, taskTabIndex);
    }
  },
  changeTaskListBtns(state, obj) {
    state.taskListBtns = obj;
  },
  //更新任务列表和任务详情
  updateTaskById(state, id) {
    state.taskUpdate.status = !state.taskUpdate.status;
    state.taskUpdate.id = id;
  },
  initEventDetail(state) {
    //当前的tab
    state.activeTab = "0";
    //动态tab列表
    state.trendsTabList = [];
  },
  changeEventDetail(state, data) {
    state.eventDetail = data;
  },
};

const actions = {
  //刷新事件按钮
  async updateEventFlag(context) {
    if (!window.location.href.includes("/event/eventDetail/")) return;
    let oldCloseFlag = context.state.eventBtns.closeFlag;

    let res1 = await getEventDetail({ id: _getId() });
    let res2 = { data: { flg: "N", changeFlg: "N", mergeFlg: "N" } };
    let eventList = await getAuditResults({ eventId: _getId() }); //审核结果记录列表,
    let operationList = await searchOperationParam("event_task_editable_before_adopt"); //获取参数列表，判断能不能编辑
    context.commit("changeEventFindings", eventList.data);
    //提审记录按钮 都展示
    let isSubmitFindings = res1.data.event.auditUserId == res1.data.userId; //审核人auditUserId=当前用户id
    let isCreateBy = res1.data.event.createBy == res1.data.userId; //判断当前用户是否是创建人
    //添加条件：未关闭的事件 isClose == 0
    context.commit("changeEventAuditFlag", {
      audit: isCreateBy && res1.data.event.isClose == 0 && res1.data.event.auditResultStatus == 3, //提报审核
      findings: true, //审核结果
      submitFindings: isSubmitFindings ? res1.data.event.auditResultStatus == 2 && res1.data.event.isClose == 0 : false, //提交审核结果
    });

    /* 新增 - 修改综述 */
    context.commit("revisionOverview", {
      eventUpdate: res1.data.eventUpdate == "Y", //摘要,
      eventDescription: res1.data.eventDescription == "Y", //描述和结论
    });

    if (res1.data.closeFlag == "Y") {
      res2 = await getEventAuditFlg({ id: _getId() });
    }
    context.commit("changeEventInfo", res1.data.event);
    context.commit("changeEventTaskButs", {
      eventTaskButShow: operationList.msg == "1" ? true : res1.data.event.auditResultStatus != 4 ? false : true, //事件详情内任务按钮,event_task_editable_before_adopt 为1 时能编辑。为0 且auditResultStatus != 4 ,不能编辑
      auditResultStatus: res1.data.event.auditResultStatus, //事件状态
      editable: operationList.msg == "1" ? true : false, //是否可编辑
    });
    let { closeFlag, eventDelete, rejectFlag, noManageCount, manageCount, assetsCount, vulnCount, suspiciousCount, alertCount, isReject } = res1.data;
    let { flg, changeFlg, mergeFlg } = res2.data;

    context.commit("changeEventFlag", {
      closeFlag: closeFlag == "Y",
      rejectFlag: rejectFlag == "Y",
      flg: flg == "Y",
      changeFlg: changeFlg == "Y",
      mergeFlg: mergeFlg == "Y",
      isReject: isReject == "Y",
      eventDelete: eventDelete == "Y",
    });
    //事件激活后刷新任务详情
    if (oldCloseFlag != context.state.eventBtns.closeFlag) {
      context.commit("updateTaskById", _getId());
    }

    context.commit("changeEventDetail", res1.data);

    //修改tabnums
    context.commit("changeTabNums", {
      1: noManageCount, //事件分析数量
      2: manageCount, //事件处置
      3: assetsCount, //资产
      4: vulnCount, //漏洞
      5: suspiciousCount, //可疑对象
      6: alertCount, //告警与报告
    });
    // });
  },
  //刷新事件状态流
  updateEventLogList(context) {
    getEventLogList({ eventId: _getId() }).then(({ data }) => {
      context.commit("changeEventLogList", data.eventLogList);
    });
  },
  //更新任务列表的按钮
  updateTaskListBtns(context) {
    let data = {};
    context.commit("changeTaskListBtns", data);
  },
};

function _getId() {
  return router.currentRoute.value.params.id;
}
function _handlerTab(state, index) {
  if (index == state.trendsTabList.length) {
    if (index == 0) {
      state.activeTab = "6";
    } else {
      state.activeTab = state.trendsTabList[index - 1].name;
    }
  }
}

export default {
  state,
  mutations,
  actions,
};
