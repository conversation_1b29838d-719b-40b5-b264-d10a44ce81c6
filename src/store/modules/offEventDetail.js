import { getEventDetail, getEventAuditFlg, getEventLogList } from "@/api/offStandardEvent/event/detail";

import router from "@/router";

const state = {
  inToEventPage: false, //请求权限接口标志
  pageStatus: true, //页面刷新标识
  eventInfo: {},
  //当前的tab
  activeTab: "0",
  //动态tab列表
  //? type，同时是组件名:taskDetail 任务详情 ||  attachment 附件 || suspiciousDetail 可疑对象详情 ||  logList 任务日志汇总
  trendsTabList: [
    // {
    //   name: "111", //任务id或者其他唯一值 唯一！！！ 任务详情为任务id,其他为type
    //   label: "动态tab示例",
    //   type: "taskDetail", //确定组件
    // },
  ],
  //事件按钮
  eventBtns: {
    closeFlag: false, //关闭
    rejectFlag: false, //驳回
    flg: false, //审核
    changeFlg: false, //变更
    mergeFlg: false, //合并
    isReject: false, //是否已驳回
    eventDelete: false, //删除
  },
  // 综述按钮
  overviewBtns: {
    eventUpdate: false, //摘要
    eventDepiction: false, //描述和结论
  },
  //事件tabs上的数字
  tabNums: {
    1: 0,
    2: 0,
    3: 0,
    4: 0,
    5: 0,
    6: 0,
  },

  //任务列表id对应的按钮组
  taskListBtns: {
    taskId: {
      startFlag: false, //开始
      submitFlag: false, //提报
      deleteFalg: false, //删除
      reauditFlag: false, //重审
      closeFlag: false, //关闭
      redoFlag: false, //重做
      auditFlag: false, //审核
    },
  },

  //状态流 列表
  eventLogList: [],
  //任务更新标识
  taskUpdate: {
    status: true,
    id: "",
  },
  eventDetail: {}, //事件详情信息
};

const mutations = {
  //修改事件详情
  offChangeEventInfo(state, info) {
    state.eventInfo = info;
  },
  offChangeInToEventPage(state) {
    state.inToEventPage = true;
  },
  //刷新页面
  offReload(state) {
    state.pageStatus = false;
    state.inToEventPage = false;
    setTimeout(() => {
      state.pageStatus = true;
    }, 200);
    setTimeout(() => {
      state.inToEventPage = true;
    }, 2000);
  },
  //修改时间按钮
  offchangeEventFlag(state, o) {
    state.eventBtns = o;
  },
  // 修改综述
  offrevisionOverview(state, val) {
    state.overviewBtns = val;
  },
  //修改事件状态流
  offchangeEventLogList(state, list) {
    state.eventLogList = list;
  },
  //修改tab上的数字
  offchangeTabNums(state, obj) {
    state.tabNums = obj;
  },
  //根据传入的name值修改
  offchangeTabNumsByName(state, payload) {
    state.tabNums[payload.name] = payload.num;
  },
  //修改当前打开的tab
  offChangeActiveTab(state, name) {
    state.activeTab = name + "";
  },
  //!新打开动态tab ,其中任务的tab页，name是taskId
  offPushEventTab(state, tab) {
    if (!state.trendsTabList.find((item) => item.name == tab.name)) {
      state.trendsTabList.push(tab);
    }
    state.activeTab = tab.name;
  },
  //更新tab
  offupdateEventActiveTab(state, data) {
    let selectedIndex = state.trendsTabList.findIndex((item) => item.name == state.activeTab);

    if (selectedIndex > -1) {
      state.trendsTabList[selectedIndex] = { ...state.trendsTabList[selectedIndex], ...data };
    }
  },
  //删除tab
  offDelEventTab(state, index) {
    let removedTab = state.trendsTabList[index];

    state.trendsTabList.splice(index, 1);
    if (state.activeTab == removedTab.name) {
      _handlerTab(state, index);
    }
  },
  //根据任务id关闭打开的任务tab
  offdelTabByName(state, name) {
    let taskTabIndex = state.trendsTabList.findIndex((item) => item.name == name);
    if (taskTabIndex > -1) {
      mutations.delEventTab(state, taskTabIndex);
    }
  },
  offchangeTaskListBtns(state, obj) {
    state.taskListBtns = obj;
  },
  //更新任务列表和任务详情
  offupdateTaskById(state, id) {
    state.taskUpdate.status = !state.taskUpdate.status;
    state.taskUpdate.id = id;
  },
  offInitEventDetail(state) {
    //当前的tab
    state.activeTab = "0";
    //动态tab列表
    state.trendsTabList = [];
  },
  offchangeEventDetail(state, data) {
    state.eventDetail = data;
  },
};

const actions = {
  //刷新事件按钮
  async offUpdateEventFlag(context) {
    if (!window.location.href.includes("/offStandardEvent/offEventDetail")) return;
    let oldCloseFlag = context.state.eventBtns.closeFlag;

    let res1 = await getEventDetail({ id: _getId() });
    let res2 = { data: { flg: "N", changeFlg: "N", mergeFlg: "N" } };

    /* 新增 - 修改综述 */
    context.commit("offrevisionOverview", {
      eventUpdate: res1.data.eventUpdate == "Y", //摘要,
      eventDescription: res1.data.eventDescription == "Y", //描述和结论
    });

    if (res1.data.closeFlag == "Y") {
      res2 = await getEventAuditFlg({ id: _getId() });
    }

    // Promise.all([getEventDetail({ id: _getId() }), getEventAuditFlg({ id: _getId() })]).then(([res1, res2]) => {

    context.commit("offChangeEventInfo", res1.data.event);
    let { closeFlag, eventDelete, rejectFlag, noManageCount, manageCount, assetsCount, vulnCount, suspiciousCount, alertCount, isReject } = res1.data;
    let { flg, changeFlg, mergeFlg } = res2.data;

    context.commit("offchangeEventFlag", {
      closeFlag: closeFlag == "Y",
      rejectFlag: rejectFlag == "Y",
      flg: flg == "Y",
      changeFlg: changeFlg == "Y",
      mergeFlg: mergeFlg == "Y",
      isReject: isReject == "Y",
      eventDelete: eventDelete == "Y",
    });
    //事件激活后刷新任务详情
    if (oldCloseFlag != context.state.eventBtns.closeFlag) {
      context.commit("offupdateTaskById", _getId());
    }

    context.commit("offchangeEventDetail", res1.data);

    //修改tabnums
    context.commit("offchangeTabNums", {
      1: noManageCount, //事件分析数量
      2: manageCount, //事件处置
      3: assetsCount, //资产
      4: vulnCount, //漏洞
      5: suspiciousCount, //可疑对象
      6: alertCount, //告警与报告
    });
    // });
  },
  //刷新事件状态流
  offUpdateEventLogList(context) {
    getEventLogList({ eventId: _getId() }).then(({ data }) => {
      context.commit("offchangeEventLogList", data.eventLogList);
    });
  },
  //更新任务列表的按钮
  offupdateTaskListBtns(context) {
    let data = {};
    context.commit("offchangeTaskListBtns", data);
  },
};

function _getId() {
  return router.currentRoute.value.params.id;
}
function _handlerTab(state, index) {
  if (index == state.trendsTabList.length) {
    if (index == 0) {
      state.activeTab = "6";
    } else {
      state.activeTab = state.trendsTabList[index - 1].name;
    }
  }
}

export default {
  state,
  mutations,
  actions,
};
