/* siem 相关 状态 */
import { getNodeList } from "@/api/sime/nodemanager/generalView";
export default {
  state: {
    /* 分析查询 - 按钮状态 */
    btnDis: false,

    /* 天狼星 - 节点list */
    activeName: "",
    tabsList: [],

    /* 查询分析记录 - 事件引用相关 */
    eventSearch: {},

    /* 默认查询条件结构 */
    saveData: {
      filtersType: 1,
      queryFilter: {
        indexId: "10000",
        conditions:
          '{"notetype":"and","children":[{"name":"istandby1","operator":"equal","value":"3","nameText":"日志类型","operatorText":"等于","valueText":"原始日志","valueType":"select","kong":false,"notetype":"condition"}],"name":"事件"}',
      },
      configsObject: {
        timeType: "0",
        time: "",
        timeSwitch: "0",
      },

      startTime: "",
      endTime: "",

      filterId: "20000",
      filters: [],
    },

    /* 查询来源 - 用于判断是否刷新查询项
     * btn - 按钮点击
     * tab - tab切换
     * */
    tabType: "btn",
  },
  mutations: {
    setBtnDis(state, context) {
      state.btnDis = context;
    },

    /* 天狼星 - 节点list */
    setTabsList(state) {
      getNodeList("sirius").then((res) => {
        state.activeName = res.data.rows[0].id;
        state.tabsList = res.data.rows;
      });
    },

    /* 查询分析记录 - 事件引用相关 */
    setEventSearch(state, context) {
      state.eventSearch = context;
    },

    /* 调整查询来源 */
    setTabType(state, context) {
      state.tabType = context;
    },
  },
};
