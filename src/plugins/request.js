import axios from "axios";
import { getToken } from "@/utils";
import { getScreenToken as getScreenTokenApi } from "@/api/screen/login";
import { ElMessage } from "element-plus";
import htmlTransfer from "@/utils/htmlTransfer";
import store from "@/store/index";
import router from "@/router/index";
import { storeKey, useStore } from "vuex";
import { SCREEN_TOKEN_NAME } from "@/config/constant";

import { requestAddBtnDisabled, responseRemoveBtnDisabled } from "@/directive/btnDisabled";

let Axios = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  // 超时
  timeout: 60000,
});

const pendingRequests = new Map();
const generateRequestKey = (config) => {
  const { url, method, params, data } = config;
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join("&");
  // return [JSON.stringify(params), JSON.stringify(data)].join("&");
};
//请求
Axios.interceptors.request.use((config) => {
  requestAddBtnDisabled();
  if (config.cancelToken) {
    const requestKey = generateRequestKey(config);
    if (pendingRequests.has(requestKey)) {
      const source = pendingRequests.get(requestKey);
      source.cancel("重复请求已被取消");
      pendingRequests.delete(requestKey);
    }
    const source = axios.CancelToken.source();
    config.cancelToken = source.token;
    pendingRequests.set(requestKey, source);
  }
  if (getToken()) {
    config.headers["Cache-Control"] = "no-cache"; // 所有api请求不缓存
    config.headers["Authorization"] = getToken();
  }

  //删除空字符串或空数组的参数
  config.params = _delEmptyData(config.params);

  // get请求映射params参数
  if (config.method === "get" && config.params) {
    let url = config.url + "?";
    for (const propName of Object.keys(config.params)) {
      const value = config.params[propName];
      var part = encodeURIComponent(propName) + "=";
      if (value !== null && typeof value !== "undefined") {
        if (typeof value === "object") {
          for (const key of Object.keys(value)) {
            let params = propName + "[" + key + "]";
            var subPart = encodeURIComponent(params) + "=";
            url += subPart + encodeURIComponent(value[key]) + "&";
          }
        } else {
          url += part + encodeURIComponent(value) + "&";
        }
      }
    }
    url = url.slice(0, -1);
    config.params = {};
    config.url = url;
  }

  return config;
});
let isVisit = false;
//响应
Axios.interceptors.response.use(
  (res) => {
    responseRemoveBtnDisabled();
    if (res.config.cancelToken) {
      const requestKey = generateRequestKey(res.config);
      pendingRequests.delete(requestKey);
    }
    let { code, data, msg } = res.data;
    if (!code) {
      return data;
    }
    if (code == 401) {
      //作为子应用，向父应用发送消息
      if (window.$wujie) {
        window.parent.location.href = window.location.origin + "/login";
        return;
      }
      if (window.location.href.includes("screen.html")) {
        window.location.href = window.location.origin + "/login";
        localStorage.removeItem(SCREEN_TOKEN_NAME);
      } else if (!window.location.href.includes("licensePage")) {
        router.push("/login");
      }
      // window.location.reload();
      return Promise.reject(res.data);
    } else if (code == 204) {
      ElMessage.warning(msg || "数据为空");
      return Promise.reject(res.data);
    } else if (code == 200) {
      // 判断是否是事件模块

      if (window.location.href.includes("event/eventDetail/")) {
        if (!isVisit && store.state.eventDetail.inToEventPage) {
          store.dispatch("updateEventLogList");
          store.dispatch("updateEventFlag");
          isVisit = true;
          setTimeout(() => {
            isVisit = false;
          }, 1000);
        }
      } else if (window.location.href.includes("offStandardEvent/offEventDetail/")) {
        if (!isVisit && store.state.offEventDetail.inToEventPage) {
          store.dispatch("offUpdateEventLogList");
          store.dispatch("offUpdateEventFlag");
          isVisit = true;
          setTimeout(() => {
            isVisit = false;
          }, 1000);
        }
      }

      //判断是否是sime单独的系统管理，需要将数据返回到外层res
      if (import.meta.env.VITE_IS_SIME && res.config.url.startsWith("/system/")) {
        if (res.data.data && typeof res.data.data == "object" && !Array.isArray(res.data.data)) {
          for (let attr in res.data.data) {
            if (!(attr in res.data)) {
              res.data[attr] = res.data.data[attr];
            }
          }
        }
      }

      if (!import.meta.env.VITE_IS_SIME && !localStorage.getItem(SCREEN_TOKEN_NAME)) {
        getScreenTokenApi().then((tokenRes) => {
          if (tokenRes.code == 200) {
            localStorage.setItem(SCREEN_TOKEN_NAME, tokenRes.data.access_token);
          }
        });
      }
      return Promise.resolve(res.data || {});
    } else {
      if (erroeCodes[code]) {
        let config = res.config;
        if (code == 500 && config.url != "/auth/getScreenToken") {
          if (!config.isMsgHide) {
            ElMessage.error(msg || `${config.url}接口${code} : ` + erroeCodes[code]);
          }
        }
      } else {
        ElMessage.error(msg || "操作失败");
      }
      return Promise.reject(res.data);
    }
  },
  (error) => {
    if (axios.isCancel(error)) {
      return Promise.reject(error);
    } else {
      import.meta.env.MODE == "development" && ElMessage.error("接口错误：请通过Network检查接口");
      return Promise.reject(error.response.data);
    }
  }
);

const erroeCodes = {
  404: "接口不存在，请确认接口地址",
  500: "服务端错误",
};
export default Axios;

// 通用下载方法
export function download(url, filename, params, type, data, timeout = 60000) {
  return axios({
    method: type ? type : "get",
    url: import.meta.env.VITE_BASE_API + url,
    timeout: timeout,
    params,
    headers: {
      Authorization: getToken(),
    },
    data: data,
    responseType: "blob",
  })
    .then((res) => {
      if (res.data.type == "application/json") {
        let reader = new FileReader();
        reader.readAsText(res.data, "utf-8");
        reader.onload = function () {
          let result = JSON.parse(reader.result);
          if (result.code == 500) {
            ElMessage.error(result.msg || "下载失败");
          }
        };
        return false;
      } else {
        let data = res.data;
        let resFileName = res.headers["content-disposition"];

        let url = window.URL.createObjectURL(data);
        var a = document.createElement("a");
        document.body.appendChild(a);
        a.href = url;

        a.download = resFileName
          ? decodeURI(resFileName.split("filename=")[1])
              .replace(/%26/g, "&")
              .replace(/%2B/g, "+")
              .replace(/%20/g, "s")
              .replace(/%23/g, "#")
              .replaceAll('"', "")
          : filename;

        a.click();
        window.URL.revokeObjectURL(url);
        return true;
      }
    })
    .catch((err) => {});
}

function _delEmptyData(obj) {
  if (obj) {
    for (let attr in obj) {
      if (obj[attr] === "" || (Array.isArray(obj[attr]) && obj[attr].length == 0)) {
        delete obj[attr];
      }
    }

    return obj;
  } else {
    return null;
  }
}
