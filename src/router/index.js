import Home from "@/views/home/<USER>";
import * as VueRouter from "vue-router";
import { getToken } from "@/utils";
import store from "@/store";
import NProgress from "nprogress";
import moduleRoutes from "./modules";
import firstToUpper from "@/utils/firstToUpper";
moduleRoutes.forEach((item) => {
  item.name = firstToUpper(item.name);
  if (item.children) {
    item.children.forEach((child) => {
      child.name = firstToUpper(child.name);
    });
  }
});

//加载各模块路由
import Layout from "@/views/layout/index.vue";
let routes = [
  {
    name: "login",
    path: "/login",
    hidden: true,
    component: () => import("@/views/login/index.vue"),
    meta: {
      name: "登录",
      keepAlive: false,
    },
  },
  {
    name: "licensePage",
    path: "/licensePage",
    hidden: true,
    component: () => import("@/views/system/license/licensePage.vue"),
    meta: {
      name: "授权",
      keepAlive: false,
    },
  },
  {
    name: "homeLayout",
    path: "/",
    component: Layout,
    meta: {
      icon: "icon-shouye",
      title: "首页",
      keepAlive: true,
    },
    children: [
      {
        path: "/outpost/home",
        component: Home,
        name: "Home",
        meta: {
          icon: "icon-shouye",
          title: "概览", //todo 密码修改
          keepAlive: true,
        },
        // redirect: "/outpost/home",
      },
      {
        path: "",
        redirect: import.meta.env.VITE_IS_SIME ? (import.meta.env.VITE_IS_ISOSS ? "" : "/simeSearch/analysis") : "/outpost/home",
      },
    ],
  },
  {
    name: "System",
    path: "/system",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "系统设置",
      icon: "icon-nav_system",
      noCache: false,
    },
    children: [
      {
        name: "License",
        path: "license",
        hidden: true,
        component: () => import("@/views/system/license/submitmain.vue"),
        meta: {
          title: "授权管理",
          icon: "shouquanguanli",
          noCache: false,
        },
      },
    ],
  },
  ...moduleRoutes,
];

if (window.$wujie) {
  routes = [...moduleRoutes];
}

const router = VueRouter.createRouter({
  history: VueRouter.createWebHistory(import.meta.env.VITE_BASE_URL),

  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
  routes,
});

router.beforeEach(async (to, from) => {
  if (window.$wujie) {
    return true;
  }
  //验证token
  let token = getToken();

  //首次登录需要跳转到授权
  if (window.licenseInfo) {
    if (to.name == "licensePage") {
      return true;
    }
    return {
      path: "/licensePage",
    };
  }

  if (!token && to.name != "login") {
    return {
      path: "/login",
    };
  } else {
    if (store.state.allRouters.length > 0 && !to.name) {
      //无权限路由跳转到首页
      return { path: "" };
    } else {
      store.commit("pushTab", to);
      // 开启进度条
      NProgress.start();
      return true;
    }
  }
});
router.afterEach(() => {
  // 关闭进度条
  if (!window.$wujie) {
    NProgress.done();
  }
});
export default router;

export { routes };
