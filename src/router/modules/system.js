import Layout from "@/views/layout/index.vue";

export default [
  {
    name: "System",
    path: "/system",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "系统设置",
      icon: "icon-nav_system",
      noCache: false,
    },
    children: [
      {
        name: "UserList",
        path: "userList",
        hidden: false,
        component: () => import("@/views/system/user/userList.vue"),
        meta: {
          title: "账号管理",
          icon: "yonghuguanli",
          noCache: false,
        },
      },

      {
        name: "Role",
        path: "role",
        hidden: false,
        component: () => import("@/views/system/role/index.vue"),
        meta: {
          title: "角色管理",
          icon: "jueseguan<PERSON>",
          noCache: false,
        },
      },
      {
        name: "customer",
        path: "customer",
        component: () => import("@/views/system/customer/index.vue"),
        meta: {
          title: "客户管理",
          icon: "kehuguanli",
          noCache: false,
        },
      },
      {
        name: "department",
        path: "department",
        component: () => import("@/views/system/department/index.vue"),
        meta: {
          title: "部门管理",
          icon: "bumenguanli",
          noCache: false,
        },
      },

      {
        name: "Menu",
        path: "menu",
        hidden: false,
        component: () => import("@/views/system/menu/index.vue"),
        meta: {
          title: "菜单管理",
          icon: "caidanguanli",
          noCache: false,
        },
      },
      {
        name: "Dict",
        path: "dict",
        hidden: false,
        component: () => import("@/views/system/dict/index.vue"),
        meta: {
          title: "字典管理",
          icon: "zidianguanli",
          noCache: false,
        },
      },
      {
        name: "Log",
        path: "log",
        hidden: false,
        component: () => import("@/views/system/log/index.vue"),
        alwaysShow: true,
        meta: {
          title: "日志管理",
          icon: "rizhiguanli",
          noCache: false,
        },
      },
      {
        name: "BasicSoftware",
        path: "basicSoftware",
        hidden: false,
        component: () => import("@/views/system/basicSoftware/index.vue"),
        meta: {
          title: "基础资源软件",
          icon: "jichuziyuanruanjian",
          noCache: false,
        },
      },
      {
        name: "License",
        path: "license",
        hidden: false,
        component: () => import("@/views/system/license/submitmain.vue"),
        meta: {
          title: "授权管理",
          icon: "shouquanguanli",
          noCache: false,
        },
      },
      {
        name: "SendTypemain",
        path: "sendTypemain",
        hidden: false,
        component: () => import("@/views/system/sendTypemain/index.vue"),
        meta: {
          title: "通知发送方式",
          icon: "tongzhifasongfangshi",
          noCache: false,
        },
      },
      {
        name: "ServiceRestart",
        path: "serviceRestart",
        hidden: false,
        component: () => import("@/views/system/serviceRestart/index.vue"),
        meta: {
          title: "服务重启",
        },
      },
      {
        name: "versionInformation",
        path: "versionInformation",
        hidden: false,
        component: () => import("@/views/system/versionInformation/index.vue"),
        meta: {
          title: "版本信息",
          icon: "tongzhifasongfangshi",
          noCache: false,
        },
      },

      {
        name: "logPermisson",
        path: import.meta.env.VITE_IS_ISOSS ? "/simeConfig/logPermisson" : "logPermisson",
        hidden: false,
        component: () => import("@/views/sime/config/logPermisson/index.vue"),
        meta: {
          title: "日志权限过滤器",
          icon: "tongzhifasongfangshi",
          noCache: false,
        },
      },
      {
        name: "resetPsd",
        path: "resetPsd",
        hidden: false,
        component: () => import("@/views/system/resetPsd.vue"),
        meta: {
          title: "密码修改",
          icon: "yewuxitong",
          noCache: false,
        },
      },
    ],
  },
];
