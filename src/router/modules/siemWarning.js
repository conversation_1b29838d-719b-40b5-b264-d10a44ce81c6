/* 异常警告 - 路由 */
import Layout from "@/views/layout/index.vue";

const pathMap = import.meta.env.VITE_IS_ISOSS
  ? {
      Warning: "/warning",
      WarningList: "/simeConfig/warningList",
      UpdateWarning: "/simeConfig/updateWarning/:id",
      HistoryRecords: "/simeConfig/historyRecords/:warningType",
      SiriusDis: "/simeConfig/siriusDis",
    }
  : {
      Warning: "/warning",
      WarningList: "/warning/warningList",
      UpdateWarning: "/warning/updateWarning/:id",
      HistoryRecords: "/warning/historyRecords/:warningType",
      SiriusDis: "/warning/siriusDis",
    };

export default [
  {
    name: "Warning",
    path: pathMap.Warning,
    component: Layout,
    meta: {
      title: "异常警告",
      icon: "icon-nav_aqzc",
    },
    children: [
      {
        name: "WarningList",
        path: pathMap.WarningList,
        hidden: false,
        component: () => import("@/views/sime/warning/list.vue"),
        meta: {
          title: "异常警告",
          icon: "z<PERSON><PERSON><PERSON><PERSON>",
        },
      },

      {
        name: "UpdateWarning",
        path: pathMap.UpdateWarning,
        hidden: true,
        component: () => import("@/views/sime/warning/anomalyWarningCom/updateWarning.vue"),
        meta: {
          title: "警告配置",
        },
      },
      {
        name: "HistoryRecords",
        path: pathMap.HistoryRecords,
        hidden: true,
        component: () => import("@/views/sime/warning/anomalyWarningCom/historyRecords.vue"),
        meta: {
          title: "历史记录",
        },
      },

      {
        name: "SiriusDis",
        path: pathMap.SiriusDis,
        hidden: true,
        component: () => import("@/views/sime/siriusDis/index.vue"),
        meta: {
          title: "天狼星",
        },
      },
    ],
  },
];
