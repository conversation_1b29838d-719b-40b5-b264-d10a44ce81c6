/* 星梭 - 路由 */
import Layout from "@/views/layout/index.vue";

export default [
  {
    name: "Starshot",
    path: "/starshot",
    component: Layout,
    meta: {
      title: "资产测绘与测试",
      icon: "icon-nav_aqzc",
    },
    children: [
      {
        name: "SpecialMapping",
        path: "specialMapping",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialMapping/index.vue"),
        meta: {
          title: "资产测绘",
        },
      },
      {
        name: "SpecialTesting",
        path: "specialTesting",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialTesting/index.vue"),
        meta: {
          title: "专项检测",
        },
      },
      {
        name: "GlobalSearch",
        path: "globalSearch",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/globalSearch/index.vue"),
        meta: {
          title: "全局搜索",
        },
      },
      {
        name: "FingerprintMaintenance",
        path: "fingerprintMaintenance",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/fingerprintMaintenance/index.vue"),
        meta: {
          title: "服务规则维护",
        },
      },
      {
        name: "DetectionPackageMaintenance",
        path: "detectionPackageMaintenance",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/detectionPackageMaintenance/index.vue"),
        meta: {
          title: "Web规则维护",
        },
      },
      {
        name: "PocAdministration",
        path: "PocAdministration",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/pocAdministration/index.vue"),
        meta: {
          title: "Poc管理",
        },
      },
      {
        name: "ResourceAllocation",
        path: "resourceAllocation",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/resourceAllocation/index.vue"),
        meta: {
          title: "资源配置",
        },
      },
      {
        name: "SpecialStrategyAdd",
        path: "specialStrategyAdd/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialTesting/specialStrategyAdd.vue"),
        meta: {
          title: "新建专项策略",
          breadParent: "SpecialTaskDetails",
        },
      },
      {
        name: "SpecialStrategyEdit",
        path: "specialStrategyEdit/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialTesting/specialStrategyEdit.vue"),
        meta: {
          title: "编辑专项策略",
          breadParent: "SpecialTaskDetails",
        },
      },
      {
        name: "SpecialTaskDetails",
        path: "specialTaskDetails/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialTesting/specialTaskDetails.vue"),
        meta: {
          title: "专项任务结果详情",
        },
      },
      {
        name: "SpecialMappingDetails ",
        path: "specialMappingDetails/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialMapping/specialMappingDetails.vue"),
        meta: {
          title: "资产测绘结果详情",
        },
      },
      {
        name: "SpecialTaskAdd",
        path: "specialTaskAdd/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialMapping/specialTaskAdd.vue"),
        meta: {
          title: "新建资产测绘任务",
          breadParent: "SpecialTaskDetails",
        },
      },
      {
        name: "SpecialTaskEdit",
        path: "specialTaskEdit/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialMapping/specialTaskEdit.vue"),
        meta: {
          title: "编辑资产测绘任务",
          breadParent: "SpecialTaskDetails",
        },
      },
    ],
  },
];
