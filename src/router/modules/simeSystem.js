import Layout from "@/views/layout/index.vue";

export default [
  {
    name: "Siem-System",
    path: "/system",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "系统设置",
      icon: "icon-nav_system",
      noCache: false,
    },
    children: [
      {
        name: "Siem-user",
        path: "/simeuser",
        hidden: false,
        component: () => import("@/views/sime/system/user/index.vue"),
        meta: {
          title: "账号管理",
          icon: "yonghuguanli",
          noCache: false,
        },
      },
      {
        name: "Siem-role",
        path: "/simerole",
        hidden: false,
        component: () => import("@/views/sime/system/role/index.vue"),
        meta: {
          title: "角色管理",
          icon: "yonghuguanli",
          noCache: false,
        },
      },
      {
        name: "Siem-dept",
        path: "/simedept",
        hidden: false,
        component: () => import("@/views/sime/system/dept/index.vue"),
        meta: {
          title: "部门管理",
          icon: "yonghuguanli",
          noCache: false,
        },
      },
      {
        name: "Siem-post",
        path: "/simepost",
        hidden: false,
        component: () => import("@/views/sime/system/post/index.vue"),
        meta: {
          title: "岗位管理",
          icon: "yonghuguanli",
          noCache: false,
        },
      },
      {
        name: "Siem-config",
        path: "/simeconfig",
        hidden: false,
        component: () => import("@/views/sime/system/config/index.vue"),
        meta: {
          title: "参数设置",
          icon: "yonghuguanli",
          noCache: false,
        },
      },
      {
        name: "Siem-log",
        path: "/simelog",
        hidden: false,
        component: () => import("@/views/sime/system/log/index.vue"),
        meta: {
          title: "日志管理",
          icon: "yonghuguanli",
          noCache: false,
        },
      },
      {
        name: "Siem-online",
        path: "/simeonline",
        hidden: false,
        component: () => import("@/views/sime/system/online/index.vue"),
        meta: {
          title: "在线用户",
          icon: "yonghuguanli",
          noCache: false,
        },
      },
      {
        path: "/simeuser/:roleId(\\d+)",
        component: () => import("@/views/sime/system/role/authUser.vue"),
        name: "SimeAuthUser",

        meta: { title: "分配用户", parentName: "Siem-role", breadParent: "Siem-System" },
      },
      {
        path: "/simeuser/:userId(\\d+)",
        component: () => import("@/views/sime/system/user/authRole.vue"),
        name: "SimeAuthRole",

        meta: { title: "分配角色", parentName: "Siem-user", breadParent: "Siem-System" },
      },
    ],
  },
];
