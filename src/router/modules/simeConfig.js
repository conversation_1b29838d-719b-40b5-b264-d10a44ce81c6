import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "simeConfigLayout",
    path: "/simeConfig",
    component: Layout,
    meta: {
      title: "分析配置",
      icon: "icon-nav_sjy",
    },
    children: [
      {
        name: "Si<PERSON><PERSON><PERSON>",
        path: import.meta.env.VITE_IS_ISOSS ? "simeLogStore" : "SimeLog",
        hidden: false,
        component: () => import("@/views/sime/config/logStore/log.vue"),
        meta: {
          title: "日志存储",
        },
      },
      {
        name: "addEditIndexes",
        path: "addEditIndexes/:id",
        hidden: true,
        component: () => import("@/views/sime/config/logStore/components/addEditIndexes.vue"),
        meta: { breadParent: "SimeConfig", title: "新增索引配置", icon: "gaojingpeizhi", noCache: false, parentName: "SimeLog" },
      },
      {
        name: "editIndexes",
        path: "editIndexes/:id",
        hidden: true,
        component: () => import("@/views/sime/config/logStore/components/addEditIndexes.vue"),
        meta: {
          breadParent: "SimeConfig",
          title: "修改索引配置",
          icon: "gaojingpeizhi",
          permission: "SimeLog",
          keepAliveName: "AddEditIndexes",
          parentName: "SimeLog",
        },
      },
      {
        name: "extendedField",
        path: "extendedField/:id",
        hidden: true,
        component: () => import("@/views/sime/config/logStore/components/extendedField.vue"),
        meta: {
          breadParent: "SimeConfig",
          title: "扩展字段配置",
          icon: "gaojingpeizhi",
          permission: "SimeLog",
          keepAliveName: "ExtendedField",
          parentName: "SimeLog",
        },
      },
      {
        name: "Alarm",
        path: "alarm",
        hidden: false,
        component: () => import("@/views/sime/config/alarm/index.vue"),

        meta: { breadParent: "SimeConfig", title: "告警输出" },
      },
      {
        name: "addEditAlarm",
        path: "addEditAlarm/:id",
        hidden: true,
        component: () => import("@/views/sime/config/alarm/components/addEditAlarm.vue"),
        meta: { breadParent: "SimeConfig", title: "新增数据表配置", icon: "gaojingpeizhi", noCache: false, permission: "alarm", parentName: "Alarm" },
      },
      {
        name: "editAlarm",
        path: "editAlarm/:id",
        hidden: true,
        component: () => import("@/views/sime/config/alarm/components/addEditAlarm.vue"),
        meta: {
          breadParent: "SimeConfig",
          title: "修改数据表配置",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "Alarm",
          keepAliveName: "AddEditAlarm",
        },
      },
      {
        name: "addEditMessage",
        path: "addEditMessage/:id",
        hidden: true,
        component: () => import("@/views/sime/config/alarm/components/addEditMessage.vue"),
        meta: { breadParent: "SimeConfig", title: "新增消息队列配置", icon: "gaojingpeizhi", noCache: false, parentName: "Alarm" },
      },
      {
        name: "editMessage",
        path: "editMessage/:id",
        hidden: true,
        component: () => import("@/views/sime/config/alarm/components/addEditMessage.vue"),
        meta: {
          breadParent: "SimeConfig",
          title: "修改消息队列配置",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "Alarm",
          keepAliveName: "AddEditMessage",
        },
      },

      /* Syslog转发配置 - 路由 */
      {
        name: "addSyslog",
        path: "addSyslog/:id",
        hidden: true,
        component: () => import("@/views/sime/config/alarm/components/addEditSyslog.vue"),
        meta: { breadParent: "SimeConfig", title: "新增syslog转发配置", icon: "gaojingpeizhi", noCache: false, parentName: "Alarm" },
      },
      {
        name: "editSyslog",
        path: "editSyslog/:id",
        hidden: true,
        component: () => import("@/views/sime/config/alarm/components/addEditSyslog.vue"),
        meta: {
          breadParent: "SimeConfig",
          title: "修改syslog转发配置",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "Alarm",
          keepAliveName: "AddEditSyslog",
        },
      },
      {
        name: "filter",
        path: "filter",
        hidden: false,
        component: () => import("@/views/sime/config/filter/index.vue"),

        meta: {
          title: "过滤器管理",
        },
      },
      {
        name: "filterVersion",
        path: "filterVersion/:id",
        hidden: true,
        component: () => import("@/views/sime/config/filter/version.vue"),

        meta: { breadParent: "SimeConfig", title: "过滤器版本", parentName: "Filter" },
      },
      {
        name: "filterVersionDetail",
        path: "filterVersionDetail/:id",
        hidden: true,
        component: () => import("@/views/sime/config/filter/versionDetail.vue"),

        meta: { breadParent: "SimeConfig", title: "版本详情", parentName: "Filter" },
      },
      {
        name: "logAccept",
        path: "logAccept",
        hidden: false,
        component: () => import("@/views/sime/config/logAccept.vue"),

        meta: {
          title: "监听器配置",
        },
      },
      {
        name: "Management",
        path: "management/:urlPath?",
        hidden: true,
        component: () => import("@/views/sime/config/logAccept/index.vue"),

        meta: { breadParent: "SimeConfig", title: "新增监听器", parentName: "LogAccept" },
      },
      {
        name: "editListener",
        path: "editListener/:id/:urlPath?",
        hidden: true,
        component: () => import("@/views/sime/config/logAccept/index.vue"),

        meta: { breadParent: "SimeConfig", title: "编辑监听器", parentName: "LogAccept", keepAliveName: "Management" },
      },
      {
        name: "monitoring",
        path: "monitoring/:id/:name/:urlPath?",
        hidden: true,
        component: () => import("@/views/sime/config/logAccept/monitoring.vue"),

        meta: { breadParent: "SimeConfig", title: "状态监控", parentName: "LogAccept" },
      },
      {
        name: "SiemDict",
        path: import.meta.env.VITE_IS_ISOSS ? "siemDict" : "dict",
        hidden: false,
        component: () => import("@/views/system/dict/index.vue"),

        meta: { breadParent: "SimeConfig", title: "字典管理", keepAliveName: "Dict" },
      },
      {
        name: "analyseResource",
        path: "analyseResource",
        hidden: false,
        component: () => import("@/views/sime/config/resource/index.vue"),
        meta: {
          title: "分析资源",
        },
      },
      {
        name: "field",
        path: "field",
        hidden: false,
        component: () => import("@/views/sime/config/field/index.vue"),
        meta: {
          title: "字段配置",
        },
      },
      {
        name: "AnalyticRule",
        path: "analyticRule",
        hidden: false,
        component: () => import("@/views/sime/config/analyticRule/index.vue"),

        meta: { title: "解析规则" },
      },
      {
        name: "AddAnalyticRule",
        path: "addAnalyticRule",
        hidden: false,
        component: () => import("@/views/sime/config/analyticRule/addRule.vue"),

        meta: { breadParent: "SimeConfig", title: "添加解析规则", icon: "gaojingpeizhi", noCache: false, parentName: "AnalyticRule" },
      },
      {
        name: "EditAnalyticRule",
        path: "EditAnalyticRule/:id",
        hidden: false,
        component: () => import("@/views/sime/config/analyticRule/addRule.vue"),

        meta: {
          breadParent: "SimeConfig",
          title: "编辑解析规则",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "AnalyticRule",
          keepAliveName: "AddAnalyticRule",
        },
      },
      {
        name: "ParseVersion",
        path: "parseVersion/:id",
        hidden: true,
        component: () => import("@/views/sime/config/analyticRule/version.vue"),

        meta: {
          breadParent: "SimeConfig",
          title: "解析规则版本",
          parentName: "AnalyticRule",
        },
      },
      {
        name: "parseVersionDetail",
        path: "parseVersionDetail/:id",
        hidden: true,
        component: () => import("@/views/sime/config/analyticRule/versionDetail.vue"),

        meta: {
          breadParent: "SimeConfig",
          title: "版本详情",
          parentName: "AnalyticRule",
        },
      },
      {
        name: "ruleBind",
        path: "ruleBind",
        hidden: false,
        component: () => import("@/views/sime/config/ruleBind/index.vue"),
        meta: {
          title: "规则绑定",
        },
      },
    ],
  },
];
