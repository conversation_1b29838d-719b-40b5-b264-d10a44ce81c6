import screenLayout from "@/views/screenLayout/index.vue";
export default [
  // {
  //   name: "screen",
  //   path: "/screen",
  //   component: 'screenLayout',
  //   meta: {
  //     title: "态势大屏",
  //     icon: "icon-nav_tsgz",
  //   },
  //   children: [
  //     {
  //       name: "threatOperation",
  //       path: "http://10.110.101.119/screen/threatOperation",
  //       meta: {
  //         title: "威胁运营大屏",
  //         icon: "icon-nav_quit",
  //         noCache: false,
  //         isLink: true,
  //       },
  //     },
  //     {
  //       name: "vulnerability",
  //       path: "http://10.110.101.119/screen/vulnerability",
  //       meta: {
  //         title: "脆弱性运营",
  //         icon: "icon-nav_quit",
  //         noCache: false,
  //         isLink: true,
  //       },
  //     },
  //     {
  //       name: "infrastructure",
  //       path: "http://10.110.101.119/screen/infrastructure",
  //       meta: {
  //         title: "运营基础设施架构",
  //         icon: "icon-nav_quit",
  //         noCache: false,
  //         isLink: true,
  //       },
  //     },
  //   ],
  // },
];
