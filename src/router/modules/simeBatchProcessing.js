import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "simeBatchProcessingLayout",
    path: "/simeBatchProcessing",
    component: Layout,
    meta: {
      title: "分析规则",
      icon: "icon-nav_fxgz",
    },
    children: [
      {
        name: "rule",
        path: "rule",
        hidden: false,
        component: () => import("@/views/sime/simeBatchProcessing/index.vue"),

        meta: {
          title: "分析规则",
          icon: "icon-nav_fxgz",
        },
      },
      {
        name: "ruleVersion",
        path: "ruleVersion/:id",
        hidden: true,
        component: () => import("@/views/sime/simeBatchProcessing/version.vue"),

        meta: {
          breadParent: "SimeBatchProcessing",
          title: "分析规则版本",
          parentName: "Rule",
        },
      },
      {
        name: "ruleVersionDetail",
        path: "ruleVersionDetail/:id",
        hidden: true,
        component: () => import("@/views/sime/simeBatchProcessing/versionDetail.vue"),

        meta: {
          breadParent: "SimeBatchProcessing",

          title: "版本详情",
          parentName: "Rule",
        },
      },
    ],
  },
];
