import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "AnalyticalDisposal",
    path: "/analyticalDisposal",

    component: Layout,
    meta: {
      title: "运营脚本",
      icon: "icon-zhuzhuangtu",
    },
    children: [
      {
        name: "RunscriptList",
        path: "runscriptList",
        hidden: false,
        component: () => import("@/views/analyticalDisposal/runscript/runscriptList.vue"),
        meta: {
          title: "运营脚本",
          icon: "icon-zhuzhuangtu",
          noCache: false,
          permission: "AnalyticalDisposal",
        },
      },
      {
        name: "DetailTemplate",
        path: "DetailTemplate/:id",
        hidden: true,
        component: () => import("@/views/analyticalDisposal/runscript/components/detailTemplate.vue"),
        meta: {
          breadParent: "AnalyticalDisposal",
          title: "模板详情",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "RunscriptList",
        },
      },
      {
        name: "addEditTemplate",
        path: "addEditTemplate/:id",
        hidden: true,
        component: () => import("@/views/analyticalDisposal/runscript/components/addEditTemplate.vue"),
        meta: {
          breadParent: "AnalyticalDisposal",
          title: "新增模板",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "RunscriptList",
        },
      },
      {
        name: "editTemplate",
        path: "editTemplate/:id",
        hidden: true,
        component: () => import("@/views/analyticalDisposal/runscript/components/addEditTemplate.vue"),
        meta: {
          breadParent: "AnalyticalDisposal",
          title: "编辑模板",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "RunscriptList",
          keepAliveName: "AddEditTemplate",
        },
      },
    ],
  },
];
