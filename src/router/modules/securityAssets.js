import Layout from "@/views/layout/index.vue";

export default [
  {
    name: "SecurityAssets",
    path: "/securityAssets",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "安全资产",
      icon: "icon-nav_aqzc",
      noCache: false,
    },
    children: [
      {
        name: "assetOverview",
        path: "assetOverview",
        hidden: false,
        component: () => import("@/views/securityAssets/assetOverview/index.vue"),
        meta: {
          title: "资产总览",
          icon: "zichanzonglan",
          noCache: false,
        },
      },
      {
        name: "assetGroup",
        path: "assetGroup",
        hidden: false,
        component: () => import("@/views/securityAssets/assetGroup/index.vue"),
        meta: {
          title: "资产组",
          icon: "zichanzu",
          noCache: false,
        },
      },
      {
        name: "businessAssets",
        path: "businessAssets",
        hidden: false,
        component: () => import("@/views/securityAssets/businessAssets/index.vue"),
        meta: {
          title: "业务系统资产",
          icon: "yewuxitongzichan",
          noCache: false,
        },
      },
      {
        name: "underlying",
        path: "underlying",
        hidden: false,
        component: () => import("@/views/securityAssets/underlying/index.vue"),
        meta: {
          title: "计算设备资产",
          icon: "jichuzichanduixiang",
          noCache: false,
        },
      },
      {
        name: "terminal",
        path: "terminal",
        hidden: false,
        component: () => import("@/views/securityAssets/terminal/index.vue"),
        meta: {
          title: "终端资产对象",
          icon: "jichuzichanduixiang",
          noCache: false,
        },
      },
      {
        name: "internet",
        path: "internet",
        hidden: false,
        component: () => import("@/views/securityAssets/internet/index.vue"),
        meta: {
          title: "互联网暴露资产",
          icon: "jichuzichanduixiang",
          noCache: false,
        },
      },
      {
        name: "confirm",
        path: "confirm",
        hidden: false,
        component: () => import("@/views/securityAssets/confirm/index.vue"),
        meta: {
          title: "待确认资产",
          icon: "jichuzichanduixiang",
          noCache: false,
        },
      },

      {
        name: "addAsset",
        path: "addAsset",
        hidden: true,
        component: () => import("@/views/securityAssets/businessAssets/addAsset.vue"),
        meta: { breadParent: "SecurityAssets", title: "添加资产", icon: "yewuxitongzichan", noCache: false, permission: "businessAssets" },
      },
      {
        name: "changeAsset",
        path: "changeAsset/:terminalId",
        hidden: true,
        component: () => import("@/views/securityAssets/businessAssets/addAsset.vue"),
        meta: {
          breadParent: "SecurityAssets",
          title: "变更资产",
          icon: "yewuxitongzichan",
          noCache: false,
          permission: "businessAssets",
          keepAliveName: "AddAsset",
        },
      },
      //待确认资产的新建
      {
        name: "confirmAdd",
        path: "confirmAdd/:type/:confirmId",
        hidden: true,
        component: () => import("@/views/securityAssets/businessAssets/addAsset.vue"),
        meta: {
          breadParent: "SecurityAssets",
          title: "新建资产",
          icon: "yewuxitongzichan",
          noCache: false,
          permission: "businessAssets",
          keepAliveName: "AddAsset",
        },
      },
      //工作台待确认资产的新建
      {
        name: "WorkBenchConfirmAdd",
        path: "WorkBenchConfirmAdd/:type/:confirmId/:assetType/:id",
        hidden: true,
        component: () => import("@/views/securityAssets/businessAssets/addAsset.vue"),
        meta: {
          breadParent: "SecurityAssets",
          title: "新建资产",
          icon: "yewuxitongzichan",
          noCache: false,
          permission: "businessAssets",
          keepAliveName: "AddAsset",
        },
      },
      {
        name: "assetGroupDetail",
        path: "assetGroupDetail/:id",
        hidden: true,
        component: () => import("@/views/securityAssets/assetGroup/newAssetGroup.vue"),
        meta: {
          breadParent: "SecurityAssets",
          title: "资产组详情",
          icon: "xinjianzichanzu",
          noCache: false,
          parentName: "AssetGroup",
          keepAliveName: "NewAssetGroup",
        },
      },
      {
        name: "newAssetGroup",
        path: "newAssetGroup/:id",
        hidden: true,
        component: () => import("@/views/securityAssets/assetGroup/newAssetGroup.vue"),
        meta: {
          breadParent: "SecurityAssets",
          title: "新建资产组",
          icon: "xinjianzichanzu",
          noCache: false,
          parentName: "AssetGroup",
          keepAliveName: "NewAssetGroup",
        },
      },
      {
        name: "assetDetails",
        path: "assetDetails/:id/:type",
        hidden: true,
        component: () => import("@/views/securityAssets/businessAssets/assetDetails.vue"),
        meta: { breadParent: "SecurityAssets", title: "资产详情", icon: "yewuxitongzican", noCache: false, permission: "businessAssets" },
      },
      {
        name: "jumpPage",
        //id:资产id  pageType:0 漏洞 1：事件 assetType: 0|| 1 ||2  spare1:标识 1:已处置 0未处置
        path: "jumpPage/:id/:pageType/:assetType/:spare1",
        hidden: true,
        component: () => import("@/views/securityAssets/businessAssets/components/jumpPage.vue"),
        meta: { breadParent: "SecurityAssets", title: "列表", icon: "liebiao", noCache: false, permission: "businessAssets" },
      },
      {
        name: "jumpService",
        path: "jumpService/:id/:assetType",
        hidden: true,
        component: () => import("@/views/securityAssets/businessAssets/business/jumpService.vue"),
        meta: { breadParent: "SecurityAssets", title: "列表", icon: "liebiao1", noCache: false, permission: "businessAssets" },
      },
    ],
  },
];
