/* soscan - 路由 */
import Layout from "@/views/layout/index.vue";

export default [
  {
    name: "Nodemanager-soscan",
    path: "/nodemanager-soscan",
    component: Layout,
    meta: {
      title: "SOSCAN",
      icon: "icon-nav_aqzc",
    },
    children: [
      {
        name: "Soscan-task",
        path: "soscan-task",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/soscan-task.vue"),
        meta: {
          title: "扫描任务",
        },
      },
      {
        name: "ScanResult",
        path: "scanResult/:taskID",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/soscanCom/scanResult.vue"),
        meta: {
          title: "扫描结果",
        },
      },
      {
        name: "AssociatedAssets",
        path: "associatedAssets/:taskID",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/soscanCom/associatedAssets.vue"),
        meta: {
          title: "关联资产",
        },
      },
    ],
  },
];
