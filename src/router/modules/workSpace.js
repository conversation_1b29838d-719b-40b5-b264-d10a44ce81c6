import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "WorkbenchLayout",
    path: "/workbenchLayout",

    component: Layout,
    meta: {
      title: "首页",
      icon: "icon-zhuzhuangtu",
    },
    children: [
      {
        name: "Workbench",
        path: "/workbench",
        hidden: false,
        component: () => import("@/views/workSpace/index.vue"),
        meta: {
          title: "工作台",
          icon: "icon-zhuzhuangtu",
          noCache: false,
        },
      },
      // 工作台待确认告警总列表
      {
        name: "PendingWarnings",
        path: "/pendingWarnings",
        hidden: true,
        component: () => import("@/views/workSpace/components/pendingWarnings.vue"),
        meta: { breadParent: "Outpost", title: "待处理告警", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
      // 三线  交付经理
      {
        name: "PendingWarningsDelete",
        path: "/pendingWarningsDelete",
        hidden: true,
        component: () => import("@/views/workSpace/components/pendingWarnings.vue"),
        meta: { breadParent: "Outpost", title: "已申请删除的告警", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
      {
        name: "WorkbenchList",
        path: "/workbenchList/:id",
        hidden: true,
        component: () => import("@/views/workSpace/components/workbenchList.vue"),
        meta: { breadParent: "Outpost", title: "待处置事件任务", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
      {
        name: "AnalysisList",
        path: "/analysisList/:id",
        hidden: true,
        component: () => import("@/views/workSpace/components/workbenchList.vue"),
        meta: {
          breadParent: "Outpost",
          title: "分析中相关事件",
          icon: "icon-zhuzhuangtu",
          noCache: false,
          parentName: "Workbench",
          keepAliveName: "WorkbenchList",
        },
      },
      // {
      //   name: "VulnerabilitiesList",
      //   path: "/vulnerabilitiesList",
      //   hidden: true,
      //   component: () => import("@/views/workSpace/components/workbenchList.vue"),
      //  meta: {breadParent:'Outpost',
      //     title: "待处置漏洞",
      //     icon: "icon-zhuzhuangtu",
      //     noCache: false,
      //     parentName: "Workbench",
      //   },
      // },
      {
        name: "BasicResources",
        path: "/basicResources/:id",
        hidden: true,
        component: () => import("@/views/workSpace/components/basicResources.vue"),
        meta: { breadParent: "Outpost", title: "待处置漏洞", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
      {
        name: "Tasksreviewed",
        path: "/tasksreviewed/:type?",
        hidden: true,
        component: () => import("@/views/workSpace/components/tasksreviewed.vue"),
        meta: { breadParent: "Outpost", title: "待处理任务", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
      {
        name: "PendingTasks",
        path: "/pendingTasks/:type?",
        hidden: true,
        component: () => import("@/views/workSpace/components/pendingTasks.vue"),
        meta: { breadParent: "Outpost", title: "待认领任务", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
      {
        name: "EventAudit",
        path: "/eventAudit/:type?",
        hidden: true,
        component: () => import("@/views/workSpace/components/eventAudit.vue"),
        meta: { breadParent: "Outpost", title: "待审核任务", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
      {
        name: "EventSubmitAudit",
        path: "/eventSubmitAudit/:type?",
        hidden: true,
        component: () => import("@/views/workSpace/components/eventSubmitAudit.vue"),
        meta: { breadParent: "Outpost", title: "待审核事件", icon: "icon-zhuzhuangtu", noCache: false, parentName: "Workbench" },
      },
    ],
  },
];
