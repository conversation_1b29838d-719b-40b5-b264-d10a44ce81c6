import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "OperationParam",
    path: "/operationParam",
    hidden: false,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "运营管理",
      icon: "icon-nav_yunying",
      noCache: false,
    },
    children: [
      {
        name: "config",
        path: "config",
        hidden: false,
        component: () => import("@/views/operationParam/config/index.vue"),
        meta: {
          title: "参数配置",
          icon: "canshupeizhi",
          noCache: false,
        },
      },
      {
        name: "samplingRate",
        path: "samplingRate",
        hidden: false,
        component: () => import("@/views/operationParam/samplingRate/index.vue"),
        meta: {
          title: "抽检率",
          icon: "choujianlv",
          noCache: false,
        },
      },
      {
        name: "alertConfig",
        path: "alertConfig",
        hidden: false,
        component: () => import("@/views/operationParam/alertConfig/index.vue"),
        meta: {
          title: "告警配置",
          icon: "gaojingpeizhi",
          noCache: false,
        },
      },
      {
        name: "dataSource",
        path: "dataSource",
        hidden: false,
        component: () => import("@/views/operationParam/dataSource/sourceList.vue"),
        meta: {
          title: "数据源配置",
          icon: "gaojingpeizhi",
          noCache: false,
        },
      },
      {
        name: "resource",
        path: "resource",
        hidden: false,
        component: () => import("@/views/operationParam/resource/resourceList.vue"),
        meta: {
          title: "资源监控管理",
          icon: "gaojingpeizhi",
          noCache: false,
        },
      },
      {
        name: "timeTask",
        path: "timeTask",
        hidden: false,
        component: () => import("@/views/operationParam/timeTask/timeTaskList.vue"),
        meta: {
          title: "定时任务配置",
          icon: "gaojingpeizhi",
          noCache: false,
        },
      },
      {
        name: "timeTaskLog",
        path: "timeTaskLog",
        hidden: true,
        component: () => import("@/views/operationParam/timeTask/timeTaskLog.vue"),
        meta: {
          title: "定时任务日志",
          icon: "gaojingpeizhi",
          noCache: false,
          permission: "timeTask",
        },
      },
      {
        name: "logoConfig",
        path: "logoConfig",
        hidden: false,
        component: () => import("@/views/operationParam/logoConfig.vue"),
        meta: {
          title: "LOGO配置",
          icon: "gaojingpeizhi",
          noCache: false,
        },
      },

      {
        name: "largeScreen",
        path: "largeScreen",
        hidden: false,
        component: () => import("@/views/operationParam/largeScreen/screenList.vue"),
        meta: {
          title: "大屏数据导入",
          icon: "gaojingpeizhi",
          noCache: false,
        },
      },
      {
        name: "timedTask",
        path: "timedTask",
        component: () => import("@/views/system/timedTask/index.vue"),
        meta: {
          title: "MTTD/MTTA定时",
          icon: "dingshi",
          noCache: false,
        },
      },
      {
        name: "alarmflowQuery",
        path: "alarmflowQuery",
        component: () => import("@/views/operationParam/alarmflowQuery/index.vue"),
        meta: {
          title: "告警流查询",
          icon: "dingshi",
          noCache: false,
        },
      },
      {
        name: "exportSsp",
        path: "exportSsp",
        component: () => import("@/views/system/dataExport/index.vue"),
        meta: {
          title: "导出SSP数据",
          icon: "dingshi",
          noCache: false,
        },
      },
      {
        name: "waterImage",
        path: "waterImage",
        component: () => import("@/views/operationParam/waterImage/index.vue"),
        meta: {
          title: "导出SSP数据",
          icon: "dingshi",
          noCache: false,
        },
      },
    ],
  },
];
