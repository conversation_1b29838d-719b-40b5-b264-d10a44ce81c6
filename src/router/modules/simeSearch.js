import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "simeSearchLayout",
    path: "/simeSearch",
    component: Layout,
    meta: {
      title: "查询分析",
      icon: "icon-nav_cxfx",
    },
    children: [
      {
        name: "analysis",
        path: "analysis/:alert?",
        hidden: false,
        component: () => import("@/views/sime/simeSearch/index.vue"),

        meta: {
          title: "查询分析",
          icon: "icon-nav_cxfx",
          // keepAlive: true,
        },
      },
    ],
  },
];
