/* 天狼星 - 路由 */
import Layout from "@/views/layout/index.vue";

export default [
  {
    name: "Nodemanager-sirius",
    path: "/nodemanager-sirius",
    component: Layout,
    meta: {
      title: "天狼星",
      icon: "icon-nav_aqzc",
    },
    children: [
      {
        name: "Sirius-config",
        path: "Sirius-config",
        hidden: true,
        component: () => import("@/views/sime/siriusDis/index.vue"),
        meta: {
          title: "分析配置",
        },
      },
      {
        name: "Sirius-whiteList",
        path: "sirius-whiteList",
        hidden: true,
        component: () => import("@/views/sime/siriusDis/whiteList/index.vue"),
        meta: {
          title: "白名单",
        },
      },

      {
        name: "SiriusDetail",
        path: "siriusDetail/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/siriusDis/siriusDetail.vue"),
        meta: {
          title: "节点详情",
        },
      },
      {
        name: "SoScanDetail",
        path: "soScanDetail/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/soscanCom/soscanDetail.vue"),
        meta: {
          title: "扫描详情",
        },
      },
      {
        name: "StarshotDetail",
        path: "starshotDetail/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/soscanCom/starshotDetail.vue"),
        meta: {
          title: "星梭详情",
        },
      },
      {
        name: "NodeSpecialStrategyAdd",
        path: "nodeSpecialStrategyAdd/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialTesting/specialStrategyAdd.vue"),
        meta: {
          title: "新建专项策略",
        },
      },
      {
        name: "NodeSpecialStrategyEdit",
        path: "nodeSpecialStrategyEdit/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialTesting/specialStrategyEdit.vue"),
        meta: {
          title: "编辑专项策略",
        },
      },
      {
        name: "NodeSpecialTaskDetails",
        path: "nodeSpecialTaskDetails/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialTesting/specialTaskDetails.vue"),
        meta: {
          title: "专项任务结果详情",
        },
      },
      {
        name: "NodeSpecialMappingDetails ",
        path: "nodeSpecialMappingDetails/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialMapping/specialMappingDetails.vue"),
        meta: {
          title: "资产测绘结果详情",
        },
      },
      {
        name: "NodeSpecialTaskAdd",
        path: "nodeSpecialTaskAdd/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialMapping/specialTaskAdd.vue"),
        meta: {
          title: "新建资产测绘任务",
        },
      },
      {
        name: "NodeSpecialTaskEdit",
        path: "nodeSpecialTaskEdit/:nodeId",
        hidden: true,
        component: () => import("@/views/sime/nodemanager/starshot/specialMapping/specialTaskEdit.vue"),
        meta: {
          title: "编辑资产测绘任务",
        },
      },
    ],
  },
];
