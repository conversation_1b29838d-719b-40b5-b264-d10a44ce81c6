import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "eventLayout",
    path: "/event",
    component: Layout,
    meta: {
      title: "事件",
      noCache: false,
      icon: "icon-nav_wxjcfx",
    },
    children: [
      {
        name: "eventOverview",
        path: "eventOverview",
        // hidden: true,
        component: () => import("@/views/event/overview.vue"),
        meta: {
          title: "总览",
          noCache: false,
          icon: "icon-nav_wxjcfx",
        },
      },
      {
        name: "AlarmDeleted",
        path: "alarmDeleted",
        // hidden: true,
        component: () => import("@/views/event/alarmDeleted.vue"),
        meta: { breadParent: "Event", title: "已删除告警", noCache: false, icon: "icon-nav_wxjcfx" },
      },
      {
        name: "alarmManagement",
        path: "alarmManagement",
        // hidden: true,
        component: () => import("@/views/event/alarmManagement.vue"),
        meta: {
          title: "告警管理",
          noCache: false,
          icon: "icon-nav_wxjcfx",
        },
      },
      {
        name: "AlarmDetails",
        path: "alarmDetails/:id",
        hidden: true,
        component: () => import("@/views/event/alarmDetails.vue"),
        meta: { breadParent: "Event", title: "告警详情", noCache: false, icon: "icon-nav_wxjcfx", parentName: "AlarmManagement" },
      },
      {
        name: "threatEvent",
        path: "threatEvent",
        // hidden: true,
        component: () => import("@/views/event/threatEvent/index.vue"),
        meta: {
          title: "威胁事件",
          noCache: false,
          icon: "icon-nav_wxjcfx",
        },
      },
      {
        name: "eventDetail",
        path: "eventDetail/:id",
        hidden: true,
        component: () => import("@/views/event/threatEvent/detail.vue"),
        meta: {
          breadParent: "Event",
          title: "事件详情",
          noCache: false,
          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
          parentName: "ThreatEvent",
        },
      },
      {
        name: "AddEvent",
        path: "addEvent",
        hidden: true,
        component: () => import("@/views/event/threatEvent/components/addEvent.vue"),
        meta: {
          breadParent: "Event",
          title: "添加事件",
          noCache: false,
          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
          parentName: "ThreatEvent",
        },
      },
      {
        name: "MergeEvent",
        path: "mergeEvent/:id",
        hidden: true,
        component: () => import("@/views/event/threatEvent/mergeEvent.vue"),
        meta: {
          breadParent: "Event",
          title: "事件合并",
          noCache: false,
          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
          parentName: "ThreatEvent",
        },
      },
      {
        name: "UpgradesEvent",
        path: "changeEvent/:id",
        hidden: true,
        component: () => import("@/views/event/threatEvent/changeEvent.vue"),
        meta: {
          breadParent: "Event",
          title: "事件升级",
          noCache: false,
          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
          parentName: "ThreatEvent",
        },
      },

      {
        name: "AlterationEvent",
        path: "alterationEvent/:id",
        hidden: true,
        component: () => import("@/views/event/threatEvent/changeEvent.vue"),
        meta: {
          breadParent: "Event",
          title: "事件变更",
          noCache: false,
          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
          parentName: "ThreatEvent",
        },
      },
    ],
  },
];
