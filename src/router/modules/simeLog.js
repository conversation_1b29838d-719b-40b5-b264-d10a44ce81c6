import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "simeLogLayout",
    path: "/simeLog",
    component: Layout,
    meta: {
      title: "日志采集",
      icon: "icon-nav_sjy",
    },
    children: [
      {
        name: "logAccept",
        path: "logAccept",
        hidden: false,
        component: () => import("@/views/sime/config/logAccept.vue"),

        meta: {
          title: "监听器配置",
        },
      },
      {
        name: "AnalyticRule",
        path: "analyticRule",
        hidden: false,
        component: () => import("@/views/sime/config/analyticRule/index.vue"),

        meta: { title: "解析规则" },
      },
      {
        name: "field",
        path: "field",
        hidden: false,
        component: () => import("@/views/sime/config/field/index.vue"),
        meta: {
          title: "字段配置",
        },
      },
      {
        name: "ruleBind",
        path: "ruleBind",
        hidden: false,
        component: () => import("@/views/sime/config/ruleBind/index.vue"),
        meta: {
          title: "规则绑定",
        },
      },
    ],
  },
];
