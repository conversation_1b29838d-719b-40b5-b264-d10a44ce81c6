import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "vulnLayout",
    path: "/vuln",
    component: Layout,
    meta: {
      title: "漏洞隐患",
      noCache: false,
      icon: "icon-nav_loudong",
    },
    children: [
      {
        name: "vulnOverview",
        path: "vulnOverview",
        component: () => import("@/views/vuln/overview/index.vue"),
        meta: {
          title: "漏洞总览",
        },
      },
      {
        name: "vulnBusiness",
        path: "vulnBusiness",
        component: () => import("@/views/vuln/business/index.vue"),
        meta: {
          title: "业务系统漏洞",
        },
      },
      {
        name: "vulnBusinessDetail",
        path: "vulnBusinessDetail/:id",
        hidden: true,
        component: () => import("@/views/vuln/business/detail.vue"),
        meta: { breadParent: "Vuln", title: "业务系统漏洞详情", permission: "vulnBusiness", parentName: "VulnBusiness" },
      },
      {
        name: "vulnBaisc",
        path: "vulnBaisc",
        component: () => import("@/views/vuln/basic/index.vue"),
        meta: {
          title: "基础资源漏洞",
        },
      },
      {
        name: "vulnBasicDetail",
        path: "vulnBasicDetail/:id",
        hidden: true,
        component: () => import("@/views/vuln/business/detail.vue"),
        meta: { breadParent: "Vuln", title: "基础资源漏洞详情", permission: "vulnBaisc", keepAliveName: "vulnBasicDetail", parentName: "VulnBaisc" },
      },
      {
        name: "leakage",
        path: "leakage",
        component: () => import("@/views/vuln/leakage/index.vue"),
        meta: {
          title: "安全敏感信息泄露",
        },
      },
      {
        name: "sensitiveDetails",
        path: "sensitiveDetails/:id",
        hidden: true,

        component: () => import("@/views/vuln/leakage/sensitiveDetails.vue"),
        meta: { breadParent: "Vuln", title: "安全敏感信息漏洞详情", permission: "leakage", parentName: "Leakage" },
      },
      {
        name: "hiddenDanger",
        path: "hiddenDanger",
        component: () => import("@/views/vuln/hiddenDanger/index.vue"),
        meta: {
          title: "安全意识隐患",
        },
      },
      {
        name: "Consciousness",
        path: "consciousness/:id",
        hidden: true,
        component: () => import("@/views/vuln/hiddenDanger/consciousness.vue"),
        meta: { breadParent: "Vuln", title: "安全意识评估详情", permission: "hiddenDanger", parentName: "HiddenDanger" },
      },
      {
        name: "safetyWarning",
        path: "safetyWarning",
        component: () => import("@/views/vuln/safetyWarning/index.vue"),
        meta: {
          title: "安全预警",
        },
      },
      {
        name: "ignoremanagement",
        path: "ignoremanagement",
        component: () => import("@/views/vuln/ignoremanagement/index.vue"),
        meta: {
          title: "漏洞忽略管理",
          parentName: "SafetyWarning",
        },
      },
    ],
  },
];
