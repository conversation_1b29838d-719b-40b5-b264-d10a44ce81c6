/* 节点管理 - 相关 */
import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "Nodemanager",
    path: "/nodemanager",
    component: Layout,
    meta: {
      title: "节点管理",
      icon: "icon-nav_sjy",
    },
    children: [
      {
        name: "NodeList",
        path: "generalView",
        hidden: false,
        component: () => import("@/views/sime/nodemanager/generalView.vue"),
        meta: {
          title: "节点总览",
        },
      },
      {
        name: "MethodList",
        path: "nodeMethod",
        hidden: false,
        component: () => import("@/views/sime/nodemanager/nodeMethod.vue"),
        meta: {
          title: "节点方法",
        },
      },
    ],
  },
];
