/* 综合服务管理 - 路由 */
import Layout from "@/views/layout/index.vue";
export default [
  {
    name: "OffStandardEvent",
    path: "/offStandardEvent",
    component: Layout,
    meta: {
      title: "综合服务管理",
      noCache: false,
      icon: "icon-nav_wxjcfx",
    },
    children: [
      /* 综合服务脚本 - 相关 */
      {
        name: "OffRunscriptList",
        path: "offRunscriptList",
        hidden: true,
        component: () => import("@/views/offStandardEvent/analyticalDisposal/runscript/runscriptList.vue"),
        meta: {
          title: "综合服务脚本",
        },
      },
      /* 新增、修改、详情 */
      {
        name: "AddOffEditTemplate",
        path: "addOffEditTemplate/:id",
        hidden: true,
        component: () => import("@/views/offStandardEvent/analyticalDisposal/runscript/components/addEditTemplate.vue"),
        meta: {
          breadParent: "OffStandardEvent",
          title: "新增模板",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "OffRunscriptList",
        },
      },
      {
        name: "OffEditTemplate",
        path: "offEditTemplate/:id",
        hidden: true,
        component: () => import("@/views/offStandardEvent/analyticalDisposal/runscript/components/addEditTemplate.vue"),
        meta: {
          breadParent: "OffStandardEvent",
          title: "编辑模板",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "OffRunscriptList",
          keepAliveName: "OffEditTemplate",
        },
      },
      {
        name: "OffDetailTemplate",
        path: "OffDetailTemplate/:id",
        hidden: true,
        component: () => import("@/views/offStandardEvent/analyticalDisposal/runscript/components/detailTemplate.vue"),
        meta: {
          breadParent: "OffStandardEvent",
          title: "模板详情",
          icon: "gaojingpeizhi",
          noCache: false,
          parentName: "OffRunscriptList",
        },
      },

      /* 综合服务脚本 - 综合服务管理 - 事件*/
      {
        name: "OffThreatEvent",
        path: "offThreatEvent",
        hidden: true,
        component: () => import("@/views/offStandardEvent/event/threatEvent/index.vue"),
        meta: {
          title: "综合服务管理",
        },
      },

      {
        name: "OffAddEvent",
        path: "offAddEvent",
        hidden: true,
        component: () => import("@/views/offStandardEvent/event/threatEvent/components/addEvent.vue"),
        meta: {
          breadParent: "OffStandardEvent",
          title: "添加综合服务",
          noCache: false,
          icon: "icon-nav_wxjcfx",
          permission: "OffThreatEvent",
          parentName: "OffThreatEvent",
        },
      },

      {
        name: "OffEventDetail",
        path: "offEventDetail/:id",
        hidden: true,
        component: () => import("@/views/offStandardEvent/event/threatEvent/detail.vue"),
        meta: {
          breadParent: "OffStandardEvent",
          title: "综合服务详情",
          noCache: false,
          icon: "icon-nav_wxjcfx",
          permission: "OffThreatEvent",
          parentName: "OffThreatEvent",
        },
      },
    ],
  },
];
