import * as VueRouter from "vue-router";

let routes = [
  {
    name: "ThreatOperation",
    path: "/threatOperation",
    hidden: false,
    component: () => import("@/views/screen/index.vue"),
    meta: {
      title: "威胁运营态势",
      icon: "icon-nav_quit",
      noCache: false,
      link: "/screen/threatOperation",
      isLink: true,
    },
  },
  {
    name: "Vulnerability",
    path: "/vulnerability",
    hidden: false,
    component: () => import("@/views/screen/index.vue"),
    meta: {
      title: "脆弱性运营态势",
      icon: "icon-nav_quit",
      noCache: false,
      link: "/screen/vulnerability",
      isLink: true,
    },
  },
  {
    name: "StatePersonnel",
    path: "/statePersonnel",
    hidden: false,
    component: () => import("@/views/screen/index.vue"),
    meta: {
      title: "运营质量态势",
      icon: "icon-nav_quit",
      noCache: false,
      link: "/screen/vulnerability",
      isLink: true,
    },
  },
  {
    name: "Infrastructure",
    path: "/infrastructure",
    hidden: false,
    component: () => import("@/views/screen/index.vue"),
    meta: {
      title: "运营基础设施架构",
      icon: "icon-nav_quit",
      noCache: false,
      link: "/screen/vulnerability",
      isLink: true,
    },
  },
];

const router = VueRouter.createRouter({
  history: VueRouter.createWebHistory("/screen.html"),
  scrollBehavior(to, from, savedPosition) {
    // console.log(111111);
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
  routes,
});

export default router;
