<template>
  <VueDraggableNext v-if="isDraggable" class="dragArea list-group choseFiled draggable-tag-box mr10" :list="dynamicTags" group="site" :sort="true">
    <el-tag class="draggable-tag mb10" v-for="tag in dynamicTags" :key="tag.id" closable :disable-transitions="false" @close="handleClose(tag)">
      {{ tag.name }}
      <el-icon class="tag-edit-icon" @click="getTagEdit(tag, false)"><Edit /></el-icon>
    </el-tag>
    <!-- 保存排序 -->
  </VueDraggableNext>

  <el-tag class="mr10 mb10" v-else v-for="tag in dynamicTags" :key="tag.id" closable :disable-transitions="false" @close="handleClose(tag)">
    {{ tag.name }}
  </el-tag>
  <el-input
    v-if="inputVisible"
    ref="saveTagInput"
    v-model="inputValue"
    class="input-new-tag"
    size="mini"
    @keyup.enter="handleInputConfirm"
    @blur="handleInputConfirm"
  >
  </el-input>
  <el-button v-else class="button-new-tag" size="small" @click="showInput">
    <el-icon :size="12">
      <plus />
    </el-icon>
    添加{{ btnText }}</el-button
  >
  <!-- 改变后显示，无改变不显示 -->
  <el-button v-if="isDraggable && isSort" type="primary" class="button-new-tag" size="small" @click="handleSortTags"> 保存排序</el-button>
  <!-- 空间资产测绘：编辑标签弹框 -->
  <xelDialog title="编辑区域标签" ref="dialogRef" size="small" @submit="submitEditTag" @close="closeDialog">
    <section class="dialog-tag-section">
      <span>区域标签:</span>
      <el-input class="tag-input" v-model="tagText" />
    </section>
  </xelDialog>
</template>
<script setup>
import { ref, reactive, onMounted, toRefs, nextTick, watch, computed, watchEffect } from "vue";
import { VueDraggableNext } from "vue-draggable-next";

import { saveTagByTagName } from "@/api/analyticalDisposal/runscript";
import { ElMessageBox, ElMessage } from "element-plus";
import { getEditNetworkArea, getEditNetworkAreaSort } from "@/api/sime/assetdetect/surveyingData";
const emit = defineEmits(["update:modelValue", "change", "del", "reload"]); //reload 刷新 新增加的，空间资产测绘中用于编辑/排序区域标签后刷新

//定义props属性
const props = defineProps({
  modelValue: {
    type: [Array, String, Number],
  },
  btnText: {
    type: String,
    default: "标签",
  },
  saveApi: {
    type: Function,
    default: saveTagByTagName,
  },
  nameKey: {
    type: String,
    default: "tagName",
  },
  msgText: {
    type: String,
    default: "标签",
  },
  //是否接口删除
  asyncDel: {
    type: Boolean,
    default: false,
  },
  //添加通用通用模板
  addSuperTag: {
    type: Boolean,
    default: false,
  },
  //是否可拖拽
  isDraggable: {
    type: Boolean,
    default: false,
  },
});
//根据选中id数组或值 回显输入框内容
onMounted(() => {
  state.dynamicTags = props.modelValue;
});
const tagsList = ref([]); //空间资产测绘：存储区域标签列表
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      nextTick(() => {
        state.dynamicTags = props.modelValue;
        tagsList.value = JSON.parse(JSON.stringify(props.modelValue)); //空间资产测绘：存储区域标签列表，与改变位置后的做比较
      });
    }
  }
);

let state = reactive({
  dynamicTags: [],
  inputVisible: false,
  inputValue: "",
});

// 关闭
function handleClose(tag) {
  if (!props.asyncDel) {
    delTag(tag);
  } else {
    emit("del", tag, delTag);
  }
}

function delTag(tag) {
  state.dynamicTags.splice(
    state.dynamicTags.findIndex((item) => item.id === tag.id),
    1
  );
}

let saveTagInput = ref();
function showInput() {
  state.inputVisible = true;
  nextTick((_) => {
    saveTagInput.value.focus();
  });
}
// 确定添加
function handleInputConfirm(name) {
  let tagName = state.inputValue;
  if (name == "通用模板") {
    tagName = name;
    if (state.dynamicTags.find((item) => item.dynamicTags == name)) {
      return;
    }
  }

  if (tagName) {
    let saveFn = props.saveApi({ [props.nameKey]: tagName }).then((res) => {
      if (res.code === 200) {
        //判断 当前标签是否存在
        var index = state.dynamicTags.findIndex((item) => item.id === res.data || item.id == res.data.id);
        if (index === -1) {
          let newItem = {
            id: res.data.id,
            name: tagName,
          };
          emit("update:modelValue", state.dynamicTags);
          emit("change", newItem, addTag);
          if (!props.asyncDel) {
            if (name != "通用模板") {
              ElMessage.success(props.msgText + "添加成功");
            }
            addTag(newItem);
          }

          state.inputVisible = false;
          state.inputValue = "";
        } else {
          ElMessage.warning(props.btnText + "已存在，请重新输入");
        }
      }
    });
  } else {
    state.inputVisible = false;
  }
}

// 添加通用通用模板
function addSuperTempTag() {
  const tagName = "通用模板";
  handleInputConfirm(tagName);
}
watch(
  () => props.addSuperTag,
  (val) => {
    if (val) {
      addSuperTempTag();
    }
  }
);

function addTag(newItem) {
  state.dynamicTags.push(newItem);
}
let { dynamicTags, inputVisible, inputValue } = toRefs(state);

//空间资产测绘：编辑标签弹框
const tagText = ref("");
const tagId = ref("");
const dialogRef = ref();
// 关闭编辑标签弹框
function closeDialog() {
  dialogRef.value.close();
  tagText.value = "";
  tagId.value = "";
}
// 打开编辑标签弹框
function getTagEdit(tag) {
  tagText.value = JSON.parse(JSON.stringify(tag.name));
  tagId.value = tag.id;
  nextTick(() => {
    dialogRef.value.open();
  });
}
function submitEditTag() {
  if (tagText.value && tagId.value) {
    const param = { id: tagId.value, name: tagText.value };
    getEditNetworkArea(param).then((res) => {
      closeDialog();
      ElMessage.success("编辑成功");
      emit("reload");
    });
  } else {
    ElMessage.warning("请输入标签名称");
  }
}
// 空间资产测绘： 排序方法
function handleSortTags() {
  const formData = new FormData();
  let idsArr = [];
  for (let i = 0; i < state.dynamicTags.length; i++) {
    idsArr.push(state.dynamicTags[i].id);
  }
  const ids = idsArr.join(",");
  formData.append("ids", ids);
  getEditNetworkAreaSort(formData).then((res) => {
    ElMessage.success("保存排序成功");
    emit("reload");
  });
}
const isSort = ref(false); //空间资产测绘 判断是否显示排序按钮
//空间资产测绘：  判断数组是否顺序相同 是否改变过顺序
function areArraysInOrder(arr1, arr2) {
  if (arr1.length == arr2.length && arr1.length != 0 && arr2.length != 0) {
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i].id !== arr2[i].id) {
        return true; // 发现不匹配的id，顺序不同
      }
    }
    return false; // 所有元素都匹配，顺序相同
  } else {
    return false; // 长度不同或为空
  }
}
watchEffect(() => {
  if (state.dynamicTags.length == tagsList.value.length && state.dynamicTags.length != 0) {
    isSort.value = areArraysInOrder(tagsList.value, state.dynamicTags);
  }
});
</script>

<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}
.mr10 {
  margin-right: 10px;
}
.el-tag + .el-tag {
  margin-right: 10px;
}
.button-new-tag {
  height: 30px;
  line-height: 30px;
  min-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
  transform: scale(0.9);
  transform-origin: center;
  position: relative;
  left: -6px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
.tag-edit-icon {
  margin-left: 4px;
  cursor: pointer;
}
.draggable-tag-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.draggable-tag {
  margin-right: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
::v-deep .sortable-ghost {
  background-color: var(--el-tag-background-color) !important;
}
.dialog-tag-section {
  span {
    margin-bottom: 15px;
    line-height: 32px;
    color: #848484;
  }
}
</style>
