<template>
  <el-pagination
    v-if="normalPage"
    v-model:currentPage="pageNumSelf"
    background
    :page-sizes="pageSizes"
    v-model:pageSize="pageSizeSelf"
    layout="prev, pager, next"
    :total="total"
    v-bind="$attrs"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
  </el-pagination>
  <el-pagination
    v-else
    v-model:currentPage="pageNumSelf"
    background
    :page-sizes="pageSizes"
    v-model:pageSize="pageSizeSelf"
    layout="total, sizes, prev, pager, next, jumper"
    :total="total"
    v-bind="$attrs"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
  </el-pagination>
</template>

<script setup>
import { ref, computed } from "vue";

//定义props属性
const props = defineProps({
  total: {
    type: Number,
    default: 0,
  },
  pageNum: {
    type: Number,
    default: 1,
  },
  //每页的条数
  pageSize: {
    type: Number,
    default: 10,
  },
  //每页条数列表
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 50, 100];
    },
  },
  // 普通页码
  normalPage: {
    type: Boolean,
    default: false,
  },
  init: {
    type: Boolean,
    default: true,
  },
});
//访问props属性

//定义emit事件
const emits = defineEmits(["change"]);

let pageNumSelf = ref(Number(props.pageNum));
let pageSizeSelf = ref(Number(props.pageSize));

function handleSizeChange(val) {
  //emit事件 change
  emits("change", { pageNum: pageNumSelf.value, pageSize: val });
}
function handleCurrentChange(val) {
  //emit事件 change
  emits("change", { pageNum: val, pageSize: pageSizeSelf.value });
}

if (props.init) {
  emits("change", { pageNum: pageNumSelf.value, pageSize: pageSizeSelf.value });
}

function resetPageNum() {
  pageNumSelf.value = 1;
}
function pageNumCut() {
  pageNumSelf.value--;
}
defineExpose({
  resetPageNum,
  pageNumCut,
  pageData: computed(() => {
    return {
      pageNum: pageNumSelf.value,
      pageSize: pageSizeSelf.value,
    };
  }),
});
</script>

<style scoped lang="scss"></style>
