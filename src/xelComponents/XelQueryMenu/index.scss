.formCon {
  width: 100%;
  border-bottom: 1px solid #f6f6f6;
  margin-bottom: 10px !important;
  padding-bottom: 10px;
  .el-button.is-plain:hover,
  .el-button.is-plain:focus {
    color: #1682e6 !important;
    border-color: #1682e6 !important;
  }
  .formDiv {
    width: calc(100% - 100px);
    // height: 36px;
    overflow: hidden;
  }
  .formDiv.formDivChose {
    width: auto;
    height: auto;
    overflow: inherit;
  }
  .menuBtnShow {
    position: absolute;
    top: 0;
    right: -20px;
  }
}
:deep() {
  .activeBtn {
    background: #fff;
    border-color: #1682e6;
    border-color: transparent;
    color: #1682e6;
  }
}
:deep() {
  .el-button.is-plain:focus {
    background: #fff;
    border-color: transparent;
  }
}
:deep() {
  .formCon .el-form-item__content {
    font-size: 12px;
    /*width: 90% !important;*/
    width: calc(100% - 100px);
  }
}
.formCon .el-button {
  margin-left: 0;
  margin-right: 10px;
  border-color: transparent;
}
.qbBtn {
  border-color: #1682e6 !important;
  color: #1682e6;
  border-radius: 3px;
}
.qbBtn1 {
  border-color: #dcdfe6 !important;
  color: #606266;
  border-radius: 3px;
}

.btnClass {
  margin-right: 8px;
  border-color: #dcdfe6 !important;
  color: #606266;
}

// :deep(){
//   .formCon .el-form-item__label {
//     color: #999999 !important;
//     text-align: left;
//   }
// }
