<template>
  <el-form-item v-if="isShow" :key="status" :label="lable" class="formCon">
    <div class="formDiv" v-if="changeShow" :style="{ 'max-height': heightAuto || !menuBtnShow ? 'inherit' : '65px' }">
      <div ref="buttonBody" id="buttonBody">
        <!-- 默认全部按钮  -->
        <el-button plain size="mini" :class="activeIndex == -1 ? 'qbBtn' : 'qbBtn1'" @click="menuBtn(-1, lable)"> 全部 </el-button>

        <!-- 按钮区  -->
        <el-button
          plain
          size="mini"
          v-for="item in menuData"
          :key="item.value"
          :class="activeIndex == item.value ? 'activeBtn' : ''"
          @click="menuBtn(item.value, item.label)"
        >
          {{ item.label }}
        </el-button>
      </div>
    </div>

    <!-- 是否显示更多按钮  -->
    <div class="menuBtnShow" v-show="menuBtnShow && heightAll > 65">
      <el-button plain size="mini" @click="packUpBtn">
        {{ btnName }}
        <i :class="btnName === '更多' ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" />
      </el-button>
    </div>
  </el-form-item>
</template>
<script setup>
import { ref, reactive, onMounted, onUpdated, onUnmounted, watch, nextTick } from "vue";
let props = defineProps({
  data: {
    type: [Number, String, undefined],
    default: () => {
      return undefined;
    },
  },
  /*lable - 查询条件名称*/
  lable: {
    type: String,
    default: "",
  },
  prop: {
    type: String,
    default: "",
  },
  /* 要展示的查询条件 关键字 dictLable-字段名   value-关键字 */
  menuData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  /*是否显示 更多按钮*/
  menuBtnShow: {
    type: Boolean,
    default: true,
  },
  isShow: {
    type: Boolean,
    default: true,
  },
  searchShow: {
    type: Boolean,
    default: true,
  },
});
let activeIndex = ref(-1);
let deptTradeList = reactive({
  deptTradeList: [],
});
let heightAuto = ref(false);
let btnName = ref("更多");
const emits = defineEmits(["itemClick"]);
let heightAll = ref(0);
let buttonBody = ref();
/*显示更多按钮事件*/
function packUpBtn() {
  heightAuto.value = !heightAuto.value;
  btnName.value === "收起" ? (btnName.value = "更多") : (btnName.value = "收起");
}
let changeShow = ref(true);

let status = ref(true);
function menuBtn(id, lable) {
  activeIndex.value = id;
  status.value = !status.value;
  let data = {
    type: props.lable,
    prop: props.prop,
    lable: lable,
    id: id,
  };
  emits("itemClick", data);
}
onMounted(() => {
  setTimeout(() => {
    setSelectData();
    heightAll.value = buttonBody.value && buttonBody.value.offsetHeight;
  }, 200);
});

watch(
  () => props.searchShow,
  (val) => {
    if (val) {
      nextTick(() => {
        heightAll.value = buttonBody.value && buttonBody.value.offsetHeight;
      });
    }
  }
);

watch(
  () => props.menuData.length,
  (val) => {
    setSelectData();
  }
);
function setSelectData() {
  if (props.data) {
    let selected = props.menuData.find((item) => item.value == props.data);
    if (selected) {
      menuBtn(selected.value, selected.label);
    }
  }
}
defineExpose({
  menuBtn,
  activeIndex,
});
</script>
<style scoped lang="scss">
@import "./index";
</style>
