<template>
  <!-- 菜单组件 - 仿京东 -->
  <div class="home" style="width: 95%; margin: auto">
    <el-row>
      <el-form class="formCon" ref="queryParamsRef" :model="queryParams" :inline="true" label-width="100px">
        <xelQueryMenu ref="XelQueryMenuRef" :menuData="menuData.menuData" @selectValue="selectValue" :showAll="true" />
        <div class="clearfix"></div>
      </el-form>
    </el-row>
  </div>
</template>
<script setup>
import { ref, reactive } from "vue";
let queryParams = reactive({});
let menuData = reactive({
  menuData: [],
});
// 测试数据
function ceshi() {
  for (var i = 0; i <= 4; i++) {
    menuData.menuData.push({
      lable: "测试" + i,
      prop: "ceshi" + i,
      menuBtnShow: true,
      options: [],
    });
    for (var y = 0; y < 40; y++) {
      menuData.menuData[i].options.push({
        label: "dictLabel" + y,
        value: "dictValue" + y,
      });
    }
  }
}
function selectValue(data) {
  if (data.id === -1) {
    queryParams[data.prop] = "";
  } else {
    queryParams[data.prop] = data.id;
  }
}
let XelQueryMenuRef = ref();
let resetQuery = () => {
  XelQueryMenuRef.value.closeAll();
  queryParams = {};
};
ceshi();
</script>

<style scoped></style>
