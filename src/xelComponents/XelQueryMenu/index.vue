<template>
  <!-- 点击子组件后 菜单选项上部显示  -->
  <el-form-item v-if="menuAllData.menuAllData.length > 0 && props.showAll === true" label="全部结果" class="formCon">
    <div class="formDiv formDivChose">
      <el-tag
        v-for="(item, index) in menuAllData.menuAllData"
        :key="item.type"
        effect="plain"
        style=""
        class="btnClass"
        @close="closeTag(index)"
        closable
      >
        {{ item.type }}{{ item.type.includes(":") || item.type.includes("：") ? "" : "：" }} {{ item.lable }}
      </el-tag>
    </div>
  </el-form-item>
  <!-- 菜单查询 - 子组件  -->
  <QueryMenuItem
    v-for="ii in menuList"
    :ref="setItemRef"
    :key="ii.lable"
    :is-show="ii.isShow"
    :menuBtnShow="ii.menuBtnShow"
    :menuData="ii.options"
    :lable="ii.lable"
    :data="data[ii.prop]"
    :prop="ii.prop"
    :searchShow="searchShow"
    @itemClick="itemClick"
  />
</template>
<script setup>
import { ref, reactive, onBeforeUpdate, onUpdated } from "vue";
import QueryMenuItem from "./queryMenuItem.vue";
import { getSelectList } from "@/xelComponents/utils/common";

let props = defineProps({
  data: {
    type: [Array, null],
    default: () => {
      return null;
    },
  },
  /* 要展示的查询条件 关键字 dictLable-字段名   dictValue-关键字 */
  menuData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  showAll: {
    type: Boolean,
    default: true,
  },
  searchShow: {
    type: Boolean,
    default: true,
  },
});
let menuList = reactive(props.menuData);
for (let i = 0; i < menuList.length; i++) {
  getSelectList(menuList[i].options, menuList[i].dictName, menuList[i].seleteCode, menuList[i].sime).then((res) => {
    menuList[i].options = res;
  });
}
let menuAllData = reactive({
  menuAllData: [],
});
let itemRefs = [];
let setItemRef = (el) => {
  if (el) {
    itemRefs.push(el);
  }
};
onBeforeUpdate(() => {
  itemRefs = [];
});
onUpdated(() => {
  //
});
const emits = defineEmits(["selectValue"]);
function itemClick(data) {
  var i = menuAllData.menuAllData.findIndex((value) => value.prop === data.prop);
  if (data.id === -1) {
    if (i !== -1) {
      menuAllData.menuAllData.splice(i, 1);
    }
  } else {
    if (i === -1) {
      menuAllData.menuAllData.push(data);
    } else {
      menuAllData.menuAllData.splice(i, 1);
      menuAllData.menuAllData.push(data);
    }
  }
  emits("selectValue", data);
}
// 所有子项集合
function closeTag(index) {
  var data = menuAllData.menuAllData[index];
  let num = ref(0);
  for (let i = 0; i < props.menuData.length; i++) {
    if (props.menuData[i].prop === data.prop) {
      num = i;
      break;
    }
  }
  // 通过子组件方法删除数组内数据
  itemRefs[num].menuBtn(-1, "");
}
/*清除所有选择事件*/
function closeAll() {
  let length = menuAllData.menuAllData.length;
  for (let i = 0; i < itemRefs.length; i++) {
    itemRefs[i].activeIndex = -1; // 循环修改状态值，改变选中样式
  }
  menuAllData.menuAllData = [];
}

defineExpose({
  closeAll,
});
</script>

<style scoped lang="scss">
@import "./index";
</style>
