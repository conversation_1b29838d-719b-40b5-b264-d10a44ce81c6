<template>
  <el-tag
    class="tag"
    v-for="item in data"
    :key="item.value + selLevel"
    :effect="selLevel == item.value ? '' : 'plain'"
    :type="selLevel == item.value ? levelData[selLevel] : 'info'"
    @click="changeLevel(item.value)"
    >{{ item.label }}</el-tag
  >
</template>
<script setup>
import { ref, reactive, onMounted, toRefs, watch, nextTick } from "vue";
import { Level_Data } from "@/config/constant";
const emit = defineEmits(["update:modelValue"]);
//定义props属性
const props = defineProps({
  modelValue: {
    type: [Array, String, Number],
  },
  data: {
    type: Array,
    default() {
      return [];
    },
  },
});
//根据选中id数组或值 回显输入框内容
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      nextTick(() => {
        state.selLevel = props.modelValue;
      });
    }
  },
  { immediate: true }
);
let state = reactive({ levelData: Level_Data, selLevel: null });
function changeLevel(val) {
  emit("update:modelValue", val);
  state.selLevel = val;
}
let { levelData, selLevel } = toRefs(state);
</script>

<style lang="scss" scoped>
.tag {
  cursor: pointer;
  margin: 0 10px;
}
.tag:first-child {
  margin-left: 0;
}
</style>
