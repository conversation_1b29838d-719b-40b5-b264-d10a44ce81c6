<template>
  <div class="upload-file">
    <el-button icon="el-icon-upload2" @click="handleImport">
      {{ props.btnName }}
    </el-button>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body @close="closeDia">
      <slot></slot>

      <el-upload
        ref="uploadRef"
        :limit="props.limit"
        :accept="props.accept"
        :headers="upload.headers"
        :action="VITE_BASE_API + upload.url"
        :disabled="upload.isUploading"
        :on-error="handleFileUploadError"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-exceed="handleExceedFile"
        :auto-upload="false"
        :on-change="changeFileList"
        :file-list="fileListSelf"
        :data="data"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" v-if="showDownLoadBtn">
            <el-link type="info" style="font-size: 14px" @click="importTemplate"> 下载模板 </el-link>
          </div>
        </template>
        <template #tip2>
          <div class="el-upload__tip" style="color: red">提示：仅允许导入{{ props.accept }}格式文件！</div>
        </template>
      </el-upload>
      <div class="dialog-footer">
        <el-button @click="closeDia">取 消</el-button>
        <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, reactive } from "vue";
import { getToken } from "@/utils";
import { ElMessageBox, ElMessage } from "element-plus";
import Axios from "@/plugins/request";
import handleBlob from "@/utils/handleBlob";
import { download } from "@/plugins/request";
const VITE_BASE_API = import.meta.env.VITE_BASE_API;
let props = defineProps({
  /* 新增 - 模板名称 - 默认 template */
  templateName: {
    type: String,
    default: () => {
      return "template";
    },
  },

  /*窗口标题名称*/
  diaName: {
    type: String,
    default: "导入数据",
  },
  /*按钮名称*/
  btnName: {
    type: String,
    default: "导入",
  },
  /*文件个数*/
  limit: {
    type: Number,
    default: 1,
  },
  /*文件格式*/
  accept: {
    type: String,
    default: ".xlsx, .xls",
  },
  /*上传模板地址*/
  importUrl: {
    type: String,
    default: "",
  },
  /*下载模板地址*/
  exportUrl: {
    type: String,
    default: "",
  },
  params: {
    type: Object,
    default: () => {
      return {};
    },
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  showDownLoadBtn: {
    type: Boolean,
    default: true,
  },
});
let upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  /*url: process.env.VUE_APP_BASE_API + "/system/eventTemplate/importEventTemplate.do"*/
  // url: process.env.VUE_APP_BASE_API + this.importUrl,
  url: props.importUrl,
});
const emits = defineEmits(["updateData", "clickBtn"]);
/** 导入按钮操作 */
function handleImport() {
  /*this.upload.title = "事件模板导入";*/
  upload.title = props.diaName;
  upload.open = true;
  emits("clickBtn");
}
/** 下载模板操作 */
function importTemplate() {
  download(props.exportUrl, props.templateName + ".xlsx", props.params);
}
// 文件上传中处理
function handleFileUploadProgress() {
  upload.isUploading = true;
}
// 文件上传失败处理
function handleFileUploadError() {
  upload.isUploading = false;
}
// 文件上传成功处理
let uploadRef = ref();
function handleFileSuccess(response) {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value.clearFiles();
  fileListSelf.value = [];
  let msg = "";
  let title = "";

  if (response.code == 200) {
    if ("data" in response) {
      msg = response.data.msg + "<br/>" + response.data.message;
    } else {
      msg = response.msg;
    }
    if (response.data && !response.data.msg && !response.data.message) {
      msg = response.msg;
    }
    title = "导入结果";
  } else {
    if (typeof response.data == "object" && "message" in response.data) {
      msg = response.data.message;
    } else {
      msg = response.data || response.msg;
    }
    title = "导入失败";
  }

  ElMessageBox.alert("<div style='max-height:400px;overflow:auto;'>" + msg.split("您好").join("<br/>您好") + "</div>", title, {
    confirmButtonText: "关闭",
    dangerouslyUseHTMLString: true,
  });

  // this.getList();
  emits("updateData", response.data);
}
// 提交上传文件
function submitFileForm() {
  if (fileListSelf.value.length == 0) {
    ElMessage.warning("请选择上传文件");
    return;
  }
  // 处理接受的文件类型
  const acceptedTypes = props.accept
    .toLowerCase()
    .replace(/\s+/g, "")
    .split(",")
    .map((type) => type.trim());
  console.log("fileListSelf ", fileListSelf.value, acceptedTypes);
  for (let file of fileListSelf.value) {
    const fileExt = "." + file.name.toLowerCase().split(".").pop();
    // const fileType = `.${i.name.split(".").pop()}`; // 获取文件类型
    console.log("fileType: ", fileExt);
    if (!acceptedTypes.some((type) => fileExt === type)) {
      ElMessage.warning(`文件格式错误,仅支持 ${props.accept} 格式`);
      return;
    }
  }
  uploadRef.value.submit();
}
function closeDia() {
  upload.open = false;
  uploadRef.value && uploadRef.value.clearFiles();
  fileListSelf.value = [];
}
/*文件数量 限制文件*/
function handleExceedFile() {
  ElMessage(`当前限制选择 ${props.limit} 个文件，请移除当前文件，重新上传!`);
}

let fileListSelf = ref([]);
function changeFileList(file, fileList) {
  fileListSelf.value = fileList;
}
</script>

<style scoped>
.upload-file {
  display: inline-block;
  margin-left: 10px;
}
.dialog-footer {
  text-align: center;
  margin-top: 20px;
}
</style>
