import { getDicts } from "@/api/system/dict/data";
import { getDicts as getDictsSime } from "@/api/sime/config/dict";

export async function getSelectList(data, dictName, selectCode, sime = false) {
  // debugger
  let list = [];
  if (data && data.length > 0) {
    list = data;
  } else if (dictName) {
    let getDictsFn = sime ? getDictsSime : getDicts;
    let res = await getDictsFn(dictName); // console.info(res);
    let arrL = res.data;
    arrL.forEach((item) => {
      list.push({
        label: item.dictLabel,
        value: item.dictValue,
      });
    });
  } else if (selectCode) {
    let fun = selectCode.code;
    let res = await fun(selectCode.params);
    let arrL = selectCode.resKey ? res.data[selectCode.resKey] : res.data;
    arrL.forEach((item) => {
      list.push({
        label: item[selectCode.label],
        value: item[selectCode.value],
      });
    });
  }
  if (dictName == "recevice_transfer_type") {
    console.log(list);
  }

  return list;
}

/**
 * 省略多余字符，用...显示
 * @param {String} value
 * @param {number} len
 */
export function ellipsis(value, len) {
  if (!value) return "";
  if (value.length > len) {
    return value.slice(0, len) + "...";
  }
  return value;
}

// 比较ip
export function compareIP(ipBegin, ipEnd) {
  var temp1;
  var temp2;
  temp1 = ipBegin.split(".");
  temp2 = ipEnd.split(".");
  for (var i = 0; i < 4; i++) {
    let tmp1 = parseInt(temp1[i]);
    let tmp2 = parseInt(temp2[i]);
    if (tmp1 > tmp2) {
      return 1;
    } else if (temp1 < temp2) {
      return -1;
    }
  }
  return 0;
}

// 比较端口
export function comparePort(portBegin, portEnd) {
  let tmp1 = parseInt(portBegin);
  let tmp2 = parseInt(portEnd);

  if (tmp1 > tmp2) {
    return 1;
  } else if (tmp1 < tmp1) {
    return -1;
  }
  return 0;
}
