<template>
  <el-popover
    ref="popoverContainer"
    :width="customSelectWidth"
    :placement="placement"
    trigger="click"
    :disabled="disabled"
    @show="showPopoverShow"
    @hide="popoverShow = false"
  >
    <template #reference>
      <slot>
        <div
          class="custom-select el-input el-input--small"
          ref="popselectRef"
          :class="{ active: popoverShow, disabled: disabled }"
          @click.native="popClickFn"
        >
          <!-- 默认 -->
          <span class="defalutInfo" v-if="!length">{{ placeholder == "请选择" ? placeholder + label : placeholder }}</span>
          <!-- 选中 多选 -->
          <template v-if="multiple && length">
            <template v-if="collapseTag">
              <el-tag
                class="select-tag"
                v-for="item in state.checkedList.slice(0, 1)"
                :key="item.id"
                type="info"
                size="mini"
                closable
                @close="removeTag(item)"
                >{{ item[treeProps.label].length > 6 ? item[treeProps.label].slice(0, 5) + "..." : item[treeProps.label] }}</el-tag
              >
              <el-tag v-if="state.checkedList.length > 1" class="select-tag" type="info" size="mini">+{{ state.checkedList.length - 1 }}</el-tag>
            </template>
            <template v-else>
              <el-tag class="select-tag" v-for="item in state.checkedList" :key="item.id" type="info" size="mini" closable @close="removeTag(item)">{{
                item[treeProps.label]
              }}</el-tag>
            </template>
          </template>
          <!-- 单选 -->
          <span v-else class="radio-label" v-for="item in state.checkedList" :key="item.id">{{ item[treeProps.label] }}</span>
          <el-icon v-if="!multiple && state.checkedList.length > 0" class="clear-icon" @click.stop="clearCheckedList"><circle-close /></el-icon>

          <i class="icon el-icon-arrow-down"></i>
        </div>
      </slot>
    </template>

    <slot name="remark"></slot>
    <el-input v-show="showSearch" size="mini" class="selectInput" placeholder="检索关键字" v-model="filterText"> </el-input>
    <div class="custom-option" style="position: relative">
      <!-- @check 多选    @node-click单选-->
      <el-tree-v2
        ref="selectTreeRef"
        :data="dataSelf"
        :node-key="idKey"
        :filter-method="filterNode"
        :show-checkbox="multiple"
        @check="checkChange"
        @node-click="nodeClick"
        :expand-on-click-node="false"
        :props="{ disabled: disabledKey, ...treeProps }"
        v-bind="$attrs"
      >
        <template #default="{ data, node }">
          <span v-if="data.isDataNode === false" class="el-tree-node_label ellipsis" :title="node.label">
            <el-icon class="folder" v-if="showIcon"><folder-opened /></el-icon>{{ node.label }}
          </span>
          <span v-else-if="data.isDataNode === true" class="el-tree-node_label datalabel ellipsis" :title="node.label">
            <el-icon class="folder" v-if="showIcon"><Coin /></el-icon>{{ node.label }}
          </span>
          <span v-else class="el-tree-node_label ellipsis" :title="node.label">
            {{ node.label }}
          </span>
          <span class="el-tree-node__label" v-if="data.isDataNode === false">
            <el-button v-if="multiple" plain size="mini" :class="node.checked ? 'activeBtn' : ''" @click="handleCheckAllChange(data)">全选</el-button>
            <el-button v-if="multiple" plain size="mini" :class="node.checked ? 'activeBtn' : ''" @click="handleCheckReverseChange(data)"
              >反选</el-button
            >
          </span>
          <div v-if="data.isDataNode === false" class="cover"></div>
        </template>
      </el-tree-v2>
    </div>
  </el-popover>
</template>
<script>
export default {
  inheritAttrs: false,
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, watch, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
import { ellipsis } from "@/xelComponents/utils/common";

let props = defineProps({
  modelValue: {
    type: [Array, String, Number],
  },
  text: {
    type: String,
  },
  idKey: {
    type: String,
    default: "id",
  },
  data: {
    type: Array,
    default: () => {
      return [];
    },
  },
  showSearch: {
    type: Boolean,
    default: true,
  },
  label: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  disabledKey: {
    type: String,
    default: "disabled",
  },
  collapseTag: {
    type: Boolean,
    default: false, //是否折叠tag，只有分析规则一个场景用到,如果其他地方要用，改一下关联的地方
  },
  treeProps: {
    type: Object,
    default() {
      return {
        id: "id",
        label: "label",
        children: "children",
      };
    },
  },
  // 树的接口 :{loadData:fn,params:{},resKey:''}
  treeOptions: {
    type: [Object, null],
    default: null,
  },
  //传入的下拉框宽度
  popWidth: {
    type: [String],
    default: "",
  },
});

const emit = defineEmits(["update:modelValue", "update:text", "nodeClick", "existIndexs", "checkChange"]);

let customSelectWidth = ref(0);
let selectTreeRef = ref();

let popoverContainer = ref();

//动态判断弹框位置
let placement = ref("bottom");
// 提示信息
function open() {
  // ElMessage({
  //   offset: 200,
  //   message: "此节点不能选中",
  //   type: "warning",
  // });
}
//关键字搜索
let filterText = ref("");
watch(
  () => filterText.value,
  (newVal, old) => {
    selectTreeRef.value.filter(newVal);
  }
);
function filterNode(value, data) {
  if (!value) return true;
  return data[props.treeProps.label].indexOf(value) !== -1;
}

let state = reactive({
  checkedList: [],
  dataSelf: props.data,
});

let { checkedList, dataSelf } = toRefs(state);

/* 临时测试位置问题 - 获取length */
const length = computed(() => {
  return state.checkedList && state.checkedList.length > 0;
});

//节点选中变化
function checkChange(data, info) {
  let { checkedKeys, checkedNodes } = info;

  emit("update:modelValue", checkedKeys);
  // 只有分析规则一个场景用到,如果其他地方要用，改一下关联的地方
  emit("checkChange", checkedNodes);
  state.checkedList = JSON.parse(JSON.stringify(checkedNodes));
}
//取消选中
function removeTag(data) {
  if (props.collapseTag) {
    // 只有分析规则一个场景用到,如果其他地方要用，改一下关联的地方
    if (selectTreeRef.value.getCheckedKeys().length == 0 && state.checkedList.length > 0) {
      setCheckedKeysFun(); //赋值
      nextTick(() => {
        removeTagFn(data);
      });
    } else {
      removeTagFn(data);
    }
  } else {
    if (selectTreeRef.value.getCheckedKeys().length == 0) {
      popoverContainer.value.show();
      popoverContainer.value.hide();

      setTimeout(() => {
        removeTagFn(data);
      }, 400);
    } else {
      removeTagFn(data);
    }
  }
}
function removeTagFn(data) {
  selectTreeRef.value.setChecked(data.id, false);
  let checkedKeys = selectTreeRef.value.getCheckedKeys();

  let checkedNodes = selectTreeRef.value.getCheckedNodes();

  checkChange(data, { checkedKeys, checkedNodes });
}

//设置下拉框宽度，显示隐藏
let popoverShow = ref(false);
let popselectRef = ref();
let lastCheckId = ref(""); //记录上一个有效节点的选择
onMounted(() => {
  nextTick(() => {
    // customSelectWidth.value = props.popWidth || popselectRef.value.getBoundingClientRect().width;
    setTimeout(() => {
      customSelectWidth.value = props.popWidth || popselectRef.value.getBoundingClientRect().width;
    }, 1000);
  });
});

//根据选中id数组或值 回显输入框内容
watch(
  () => props.modelValue,
  (val) => {
    nextTick(() => {
      initNodeList(val);
      popoverContainer.value.update(); //刷新弹出框位置
    });

    /* 临时新增 - 增加一层延时，验证回显问题 */
    setTimeout(() => {
      initNodeList(val);
    }, 300);
  }
);
onMounted(() => {
  nextTick(() => {
    initNodeList();
  });
});
function initNodeList(value) {
  if (props.multiple) {
    state.checkedList = getDataByIds(state.dataSelf, value || props.modelValue);
  } else {
    state.checkedList = getDataByIds(state.dataSelf, [value || props.modelValue]);
  }
}

//根据id筛选数组
function getDataByIds(arr, ids = []) {
  if (ids == "") {
    ids = [];
  }
  let list = arr.filter((item) => ids.includes(item[props.idKey]));
  for (let item of arr) {
    if (item.children && item.children.length > 0) {
      list = list.concat(getDataByIds(item.children, ids));
    }
  }
  return list;
}

//兼容子应用
let windowEvent = null;
function popClickFn($event) {
  windowEvent = $event;
}
//树选择器显示时回填数据
function showPopoverShow() {
  if (!windowEvent) return;
  // debugger;
  //
  //
  // selectTreeRef.value.getNode(props.modelValue);

  popoverShow.value = true;
  filterText.value = "";

  //设置弹出框位置
  if (windowEvent.clientY > document.body.offsetHeight * 0.65) {
    placement.value = "top";
  } else {
    placement.value = "bottom";
  }
  nextTick(() => {
    setCheckedKeysFun();
    if (props.multiple) {
      let checkedKeys = selectTreeRef.value.getCheckedKeys();
      let checkedNodes = selectTreeRef.value.getCheckedNodes();
      checkChange(null, { checkedKeys, checkedNodes });
    } else {
      selectTreeRef.value.setCurrentKey(props.modelValue);
    }
  });
}
//多选数据 回显
function setCheckedKeysFun() {
  if (props.multiple) {
    let ids = [];
    if (props.collapseTag) {
      // 只有分析规则一个场景用到,如果其他地方要用，改一下关联的地方
      if (Array.isArray(props.modelValue)) {
        ids = props.modelValue;
      } else {
        ids = props.modelValue.split(",");
      }
    } else {
      ids = props.modelValue;
    }
    selectTreeRef.value.setCheckedKeys(ids);
  } else {
    selectTreeRef.value.setCurrentKey(props.modelValue);
  }
}

//单选
function nodeClick(data, node) {
  if (!props.multiple) {
    if (data.isDataNode === false) {
      open();

      // 如果本次选中的是无效节点，就把选中的节点置为上次选择的
      selectTreeRef.value.setCurrentKey(lastCheckId);
    } else {
      // 记录上一个有效选中的节点  默认情况(undefin) 或者true
      lastCheckId = data[props.idKey];
      emit("update:modelValue", data[props.idKey]);
      emit("update:text", data[props.treeProps.label]);
      emit("existIndexs", data[props.treeProps.label]);
      state.checkedList = JSON.parse(JSON.stringify([data]));
      popoverContainer.value.hide(); //单选选中后关闭弹出框
    }
  }
  emit("nodeClick", data);
}

//获取数据
watch(
  () => props.data,
  (val) => {
    state.dataSelf = val;
  },
  { deep: true }
);
//异步获取树 数据
if (props.treeOptions) {
  if (!props.data || props.data.length == 0) {
    props.treeOptions.loadData(props.treeOptions.params).then(({ data }) => {
      state.dataSelf = data[props.treeOptions.resKey] || data;
      initNodeList();
    });
  }
}

//删除单选
function clearCheckedList() {
  state.checkedList.pop();
  emit("update:modelValue", "");
  emit("update:text", "");
}

// 全选

function handleCheckAllChange(data) {
  updateCheckedChildren(data.children, "allCheck");
}
// 父节点选中，子节点也选中；父节点取消，子节点也取消
function updateCheckedChildren(children, type, initCheckList) {
  if (!children || children.length === 0) return;
  children.forEach((child) => {
    if (initCheckList && initCheckList.includes(child.id)) {
      child.isChecked = true;
    } else {
      child.isChecked = false;
    }

    child.isChecked = type == "allCheck" ? true : !child.isChecked;

    if (type == "allCheck") {
      // 获取之前选中的
      let oldCheckedKeys = selectTreeRef.value.getCheckedKeys();

      if (child.isDataNode) {
        oldCheckedKeys.push(child.id);
      }
      let newCheckList = Array.from(new Set(oldCheckedKeys));
      // // 新增
      selectTreeRef.value.setCheckedKeys(newCheckList);
    } else {
      if (child.isDataNode) {
        selectTreeRef.value.setChecked(child.id, child.isChecked);
      }
    }

    let checkedKeys = selectTreeRef.value.getCheckedKeys();

    let checkedNodes = selectTreeRef.value.getCheckedNodes();
    checkChange(child, { checkedKeys, checkedNodes });
    updateCheckedChildren(child.children, type, initCheckList);
  });
}
// 反选
function handleCheckReverseChange(data) {
  //  通过点击复选框选中
  let oldCheckedKeys = selectTreeRef.value.getCheckedKeys();
  updateCheckedChildren(data.children, "reverse", oldCheckedKeys);
}

defineExpose({
  treeData: computed(() => {
    return state.dataSelf;
  }),
  // isExisNode,
});
</script>

<style lang="scss" scoped>
.custom-select {
  // margin-top: 6px;
  min-height: 32px;
  max-height: 80px;
  width: 100%;
  margin-bottom: -10px;
  // line-height: 32px;
  box-sizing: border-box;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  overflow: auto;
  position: relative;
  &.disabled:after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0.5;
    background: #f5f7fa;
    cursor: not-allowed;
  }
  &:hover {
    .clear-icon {
      display: inline-block;
    }
  }
}
.custom-select.active {
  border-color: $color;
}

.custom-option {
  max-height: 200px;
  // overflow-y: auto;
}
:deep(.el-tree-virtual-list) {
  // height: auto !important;
  margin-top: 10px;
}
.el-tree-node > .el-tree-node__children {
  overflow: visible;
}
.custom-select {
  position: relative;
}
.clear-icon {
  cursor: pointer;
  position: absolute;
  right: 40px;
  top: 30%;
  color: #c0c4cc;
  display: none;
}
.icon {
  cursor: pointer;
  display: inline-block;
  position: absolute;
  right: 10px;
  top: 30%;
  color: #c0c4cc;
}
.radio-label {
  margin-left: 15px;
}
:deep .el-tree-node__content > span,
:deep .el-checkbox__label {
  font-size: 12px !important;
}
:deep .el-tag.el-tag--info {
  margin-left: 4px;
}
.defalutInfo {
  margin-left: 10px;
  color: #cfd0d4;
}
:deep(.el-checkbox__input.is-disabled) {
  transform: scale(0);
  display: none;
}
:deep .el-tree-node_label + .cover {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  // cursor: not-allowed;
  background: none;
}
:deep .el-tree-node_label {
  width: calc(100% - 32px);
  padding-right: 16px;
  display: inline-block;
}
.folder {
  margin-top: 10px;
  margin-right: 4px;
}
.datalabel {
  color: #1890ff;
}
.el-tree-node__label {
  .el-button.is-plain:active {
    color: #1682e6 !important;
    border-color: #1682e6 !important;
  }

  :deep(.el-button) {
    transform: translateY(2px);
    margin-right: 8px;
    font-size: 8px;
    padding: 2px 4px;
    min-height: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// :deep() {
//   .el-button.is-plain:visited {
//     color: #ef8936 !important;
//     background-color: #fdf3eb;
//   }
// }
</style>
