<template>
  <!-- input框 text textarea email password-->

  <el-form-item
    v-if="isShow"
    ref="formItemRef"
    v-bind="formItemAttrsKey"
    :label="formItemAttrsKey.label ? formItemAttrsKey.label + ':' : ''"
    :rules="rules"
    class="xel-form-item"
    :style="itemStyle"
    :title="$attrs.type == 'datetimerange' || $attrs.type == 'daterange' ? (modelValue ? modelValue.toString().replace(',', ' - ') : '') : ''"
  >
    <el-input
      v-if="formType == 'input'"
      :clearable="true"
      v-bind="elattrs"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      @change="$emit('update:modelValue', reserveTrim ? $event : $event.trim())"
      :style="inputStyle"
      :disabled="disabled"
      :placeholder="elattrs.placeholder || '请输入' + formItemAttrsKey.label"
    ></el-input>
    <!-- select下拉 单选 多选-->
    <el-select
      v-else-if="formType == 'select'"
      :disabled="disabled"
      clearable
      v-bind="elattrs"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      :placeholder="elattrs.placeholder || '请选择' + formItemAttrsKey.label"
      :multiple="elattrs.multiple"
      :style="inputStyle"
      :filterable="elattrs.filterable"
      @change="getSelectLabel($event, optionsSelf)"
    >
      <el-option
        v-for="item in optionsSelf"
        :key="item.value"
        :label="item.label"
        :value="props.isNumber ? Number(item.value) : item.value"
        @click="$emit('optionClick', item.value)"
      ></el-option>
    </el-select>
    <!-- 数字输入框 -->
    <el-input-number
      v-else-if="formType == 'number'"
      :clearable="true"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      controls-position="right"
      :max="elattrs.max"
      :min="elattrs.min"
      v-bind="elattrs"
      :style="inputStyle"
    />
    <!-- 单选框 -->
    <el-radio-group v-else-if="formType == 'radio'" v-bind="elattrs" :modelValue="modelValue" @update:modelValue="$emit('update:modelValue', $event)">
      <el-radio :disabled="disabled" v-for="item in optionsSelf" :key="item.value" :label="props.isNumber ? Number(item.value) : item.value">{{
        item.label
      }}</el-radio>
    </el-radio-group>
    <!-- 多选 -->
    <el-checkbox-group
      v-else-if="formType == 'checkbox'"
      v-bind="elattrs"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
    >
      <el-checkbox v-for="item in optionsSelf" :key="item.value" :label="props.isNumber ? Number(item.value) : item.value" name="type">
        {{ item.label }}
      </el-checkbox>
    </el-checkbox-group>

    <!-- 时间选择器 -->
    <el-time-picker
      v-else-if="formType == 'time'"
      :clearable="true"
      :disabled="disabled"
      v-bind="elattrs"
      :value-format="'HH:mm:ss'"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :placeholder="'请选择' + formItemAttrsKey.label"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      :style="inputStyle"
    ></el-time-picker>
    <el-date-picker
      v-else-if="formType == 'date'"
      :clearable="true"
      :disabled="disabled"
      :value-format="elattrs.day ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
      :format="elattrs.day ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
      v-bind="elattrs"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :placeholder="'请选择' + formItemAttrsKey.label"
      :modelValue="modelValue"
      :default-time="
        elattrs.type == 'daterange' || elattrs.type == 'datetimerange' ? [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)] : null
      "
      @update:modelValue="$emit('update:modelValue', $event)"
      :style="inputStyle"
    ></el-date-picker>
    <!-- 时间范围选择器 -->

    <el-date-picker
      v-else-if="formType == 'daterange'"
      :clearable="true"
      :type="elattrs.type || 'daterange'"
      :disabled="disabled"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :value-format="elattrs.day ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
      :format="elattrs.day ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss'"
      v-bind="elattrs"
      v-model="timeRange"
      :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
      :style="inputStyle"
      @change="changeTimeRange"
    ></el-date-picker>
    <!-- tree -->
    <xel-select-tree
      ref="treeRef"
      v-else-if="formType == 'tree'"
      :data="treeData"
      :disabled="disabled"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      v-bind="elattrs"
      :label="formItemAttrsKey.label"
      :style="inputStyle"
    />

    <!-- deptTree 部门树-->
    <xel-select-tree
      ref="treeDeptRef"
      v-else-if="formType == 'deptTree' && showDeptTree"
      :data="treeDataDept"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      :label="formItemAttrsKey.label"
      v-bind="elattrs"
      :style="inputStyle"
      :disabled="disabled"
    />
    <!-- 上传 -->
    <upload-btn v-else-if="formType == 'upload'" prompt="" btn-type="" v-bind="$attrs" :disabled="disabled" @fileList="changeFileList"></upload-btn>
    <!-- editor -->
    <Editor
      v-else-if="formType == 'editor'"
      :editorClass="elattrs.editorClass"
      :desc="editorData"
      ref="editorRef"
      v-bind="elattrs"
      :is-clear="isClear"
      @change="reproNewHtmls"
    />
    <!-- 标签 -->
    <xel-tag
      v-else-if="formType == 'tag'"
      :addSuperTag="addSuperTag"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      v-bind="elattrs"
    />
    <xel-tag-new
      v-else-if="formType == 'tagNew'"
      :addSuperTag="addSuperTag"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      v-bind="elattrs"
    />
    <!-- 级别 -->
    <xel-level
      v-else-if="formType == 'level'"
      :data="optionsSelf"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      v-bind="elattrs"
    ></xel-level>
    <!-- switch -->
    <el-switch
      v-else-if="formType == 'switch'"
      :modelValue="modelValue"
      @update:modelValue="$emit('update:modelValue', $event)"
      v-bind="elattrs"
      :active-value="1"
      :inactive-value="0"
    >
    </el-switch>
    <slot></slot>
  </el-form-item>
</template>
<script>
// 声明额外的选项
export default {
  inheritAttrs: false,
};
</script>
<script setup>
//editor 全局组件
import Editor from "./xelEdit.vue";
import uploadBtn from "./xelUploadBtn.vue";
// 词典
import { getSelectList } from "./utils/common";
// 验证
import { vxRule } from "./utils/formValidator";
import { ref, reactive, onMounted, computed, useAttrs, onUpdated, watch, nextTick, watchEffect } from "vue";

let radio = ref("");
const emit = defineEmits([
  "delete",
  "update:modelValue",
  "update:text",
  "changeTree",
  "update:start",
  "update:end",
  "changeOptions",
  "optionClick",
  "FileList",
]);
let selectWrapper = ref();

let formItemRef = ref();
onUpdated(() => {
  nextTick(() => {
    formItemRef.value && formItemRef.value.clearValidate && formItemRef.value.clearValidate();
  });
});
/**
 * formType:String,//表单项类型
 * required:Boolean,//是否必填
 * label:String,//表单名称
 * prop:String //prop 属性设置为需校验的字段名
 */

//定义props属性
const props = defineProps({
  /* 是否位移一个 el-form-item
   * 默认false 不影响原使用逻辑
   * */
  isDisplacement: {
    type: Boolean,
    default: false,
  },
  text: [String],
  modelValue: [Number, String, Array, Date, null, undefined],
  start: [String],
  end: [String],
  value: [Number, String, Array, Date, Boolean],
  formType: {
    type: String,
    default: "input",
  },
  isShow: {
    type: Boolean,
    default: true,
  },
  // 宽度
  width: {
    type: String,
    default: () => {
      return "100%";
    },
  },
  itemWidth: {
    type: String,
    default: () => {
      return "";
    },
  },
  // 数据字典
  options: {
    type: Array,
    default: () => {
      return [];
    },
  },
  // 查询关键字
  dictName: {
    type: String,
    default: "",
  },
  seleteCode: {
    type: [Object, null],
    default: () => {
      return null;
    },
  },
  //是否是sime字典项
  sime: {
    type: Boolean,
    default: false,
  },
  // 树
  treeData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  // 富文本编辑器
  editorData: {
    type: String,
    default: "",
  },
  isClear: {
    type: Boolean,
    default: false,
  },
  isNumber: {
    type: Boolean,
    default: false,
  },
  switchOptions: {
    type: [Object, null],
    default: () => {
      return null;
    },
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  //添加通用超级模板
  addSuperTag: {
    type: Boolean,
    default: false,
  },
  // 自定义label
  showDefinedLabel: {
    type: Boolean,
    default: false,
  },
  // input 保留空格
  reserveTrim: {
    type: Boolean,
    default: false,
  },
});
/**
 * 取差集 计算formItem属性和表单项属性
 * @param {Object}  $attrs 全集
 * @param {Array}formItemAttrsKey 子集
 * form-item是固定的，所以筛出来  剩下的都给表单项
 * @param {Array} elattrs 子集
 * 不同的表单元素，要的属性不一样
 */
let formItemAttrsKey = reactive({});
let formItemAttrs = reactive(["prop", "label", "label-width", "required", "rules", "error", "show-message", "inline-message", "size", "id", "class"]); //formItem拥有的全部属性
let elattrs = reactive({}); //表单项
let attrs = useAttrs();
//
Subtraction();
function Subtraction() {
  Object.keys(attrs).forEach((key) => {
    if (formItemAttrs.includes(key)) {
      formItemAttrsKey[key] = attrs[key];
    } else {
      elattrs[key] = attrs[key];
    }
  });
}

let itemStyle = computed(() => {
  /* 临时新增 - 增加 marginLeft，后续优化
  默认false 不影响原使用逻辑
  */
  // 样式
  return props.itemWidth ? (props.isDisplacement ? { width: props.itemWidth, marginLeft: props.itemWidth } : { width: props.itemWidth }) : {};
});
// 计算样式
let inputStyle = computed(() => {
  // 样式
  return { width: props.width };
});
// 计算验证
let rules = computed(() => {
  // 验证规则
  let rules = reactive([]);
  // 验证提示语
  let topName = ref("");

  // 后期如果还有其它类型
  switch (props.formType) {
    case "select":
    case "tree":
    case "radio":
    case "checkbox":
    case "daterange":
    case "deptTree":
      topName.value = "请选择";
      break;
    case "upload":
      topName.value = "请上传";
      break;
    case "tag":
    case "level":
      topName.value = "请设置";
      break;

    default:
      topName.value = "请输入";
      break;
  }
  if (props.formType.includes("date") || props.formType.includes("time")) {
    topName.value = "请选择";
  }
  // 必填验证
  if (attrs.required) {
    rules.push({
      required: attrs.required,
      message: topName.value + attrs.label,
      trigger: ["blur", "change"],
    });
  }
  // element 自带验证
  if (attrs.exRule) {
    rules.push({
      type: "email",
      message: "请输入正确的邮箱地址",
      trigger: ["blur", "change"],
    });
  }
  // 自定义验证
  if (attrs.vxRule) {
    rules.push(vxRule(attrs.required || false, attrs.vxRule));
  }
  if (attrs.rules) {
    rules.push(...attrs.rules);
  }
  return rules;
});
let pickerOptions = computed(() => {
  if (attrs.pickOptions == "start") {
    return startDatePicker;
  } else {
    return endDatePicker;
  }
});
// 修改tree父组件内容
function changeTree(data) {
  emit("changeTree", data, formItemAttrsKey.prop);
}
// 开始日期不能大于结束日期判断
let startDatePicker = reactive({});
startDatePicker = {
  disableDate(time) {
    let beginDateVal = new Date(attrs.endTime).getTime();
    if (beginDateVal) {
      return time.getTime() > beginDateVal || time.getTime() < Date.now() - 8.64e7;
    } else {
      return time.getTime() < Date.now() - 8.64e7;
    }
  },
};
let endDatePicker = reactive({});
endDatePicker = {
  disableDate(time) {
    let beginDateVal = new Date(attrs.startTime).getTime();
    return time.getTime() < beginDateVal - 24 * 60 * 60 * 1000;
  },
};
let optionsSelf = ref([]);
getSelectList(props.options, props.dictName, props.seleteCode, props.sime).then((res) => {
  if (props.options.length > 0) {
    optionsSelf.value = props.options;
  } else if (props.dictName) {
    optionsSelf.value = res;
  } else if (props.seleteCode) {
    optionsSelf.value = res;
  }
  emit("changeOptions", optionsSelf.value);
});
watch(
  () => props.options,
  (val) => {
    optionsSelf.value = props.options;
  },
  { deep: true }
);
watch(
  () => props.showDefinedLabel,
  (val) => {
    Subtraction();
  },
  { deep: true }
);
// 获取slect label
function getSelectLabel(val, options) {
  if (typeof val === "string") {
    let name = val ? options.find((ele) => ele.value === val).label : "";
    emit("update:text", name);
  }
}

let editorRef = ref();

// 编辑器 子传父
function reproNewHtmls(value) {
  emit("editorValue", value);
}

//select

function selectChange(val) {
  elattrs.onchange && elattrs.onchange(val);
}

//修改时间范围选择器的v-mdoel值

let timeRange = ref([props.start || "", props.end || ""]);
if (props.modelValue) {
  if (props.formType == "daterange") {
    timeRange.value = JSON.parse(JSON.stringify(props.modelValue));
  }
}
watch(
  () => props.modelValue,
  (val) => {
    if (props.formType == "daterange") {
      timeRange.value = val;
    }
  },
  {
    deep: true,
  }
);
watch(
  () => props.start,
  (val) => {
    if (timeRange.value) {
      timeRange.value[0] = val;
    }
  }
);
watch(
  () => props.end,
  (val) => {
    if (timeRange.value) {
      timeRange.value[1] = val;
    }
  }
);
function changeTimeRange(val) {
  if (val) {
    emit("update:start", val[0]);
    emit("update:end", val[1]);
  } else {
    emit("update:start", "");
    emit("update:end", "");
  }

  emit("update::modelValue", val);
}

//获取部门树
import { getDeptTree } from "@/api/system/dept";
let treeDataDept = ref([]);
let showDeptTree = ref(false);
if (props.formType == "deptTree") {
  getDeptTree().then(({ data }) => {
    treeDataDept.value = data.deptTree;
    showDeptTree.value = true;
    emit("getTreeData", data.deptTree);
  });
}

//暴露树组件的数据
let treeDeptRef = ref();
let treeRef = ref();
if (props.formType == "tree") {
  watch(
    () => treeRef.value,
    (val) => {
      if (val) {
        setTimeout(() => {
          emit("getTreeData", treeRef.value ? treeRef.value.treeData : []);
        }, 300);
      }
    },
    { immediate: true }
  );
}

defineExpose({
  treeData: computed(() => {
    if (treeRef.value) {
      return treeRef.value.treeData;
    } else if (treeDeptRef.value) {
      return treeDeptRef.value.treeData;
    } else {
      return [];
    }
  }),
});

//上传
function changeFileList(list) {
  formItemRef.value && formItemRef.value.clearValidate && formItemRef.value.clearValidate();
  emit("fileList", list);
}
</script>

<style lang="scss" scoped>
:deep(.el-input-number.is-controls-right) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
