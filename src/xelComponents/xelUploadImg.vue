<template>
  <el-upload
    :class="{ 'avatar-uploader': true, round: round }"
    :show-file-list="false"
    accept=".png,.jpg,.jpeg,.gif"
    :on-success="handleAvatarSuccess"
    :before-upload="beforeAvatarUpload"
    :on-error="handleError"
    :auto-upload="cropper ? false : true"
    v-bind="$attrs"
    :on-change="changeFile"
    :headers="{ Authorization: getToken() }"
    :style="avatarStyle"
  >
    <img v-if="modelValue" :src="modelValue" class="avatar" :style="avatarStyle" />
    <img v-else-if="defaultSrc" :src="defaultSrc" class="avatar" :style="avatarStyle" />

    <i v-else class="el-icon-plus avatar-uploader-icon" :style="avatarStyle"></i>
  </el-upload>
  <el-dialog v-model="visible" title="裁剪图片" width="60%">
    <vue-cropper class="cropper-wrapper" ref="cropRef" :img="cropperImg" :autoCrop="true" :centerBox="true"></vue-cropper>
    <el-button @click="cropSubmit">裁剪</el-button>
  </el-dialog>
</template>
<script setup>
import { VueCropper } from "vue-cropper";
import { ref, nextTick, useAttrs, computed } from "vue";
import { ElMessage } from "element-plus";
import Axios from "../plugins/request";
import getToken from "@/utils/getToken";

const attrs = useAttrs();

let props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  defaultSrc: {
    type: Object,
    default: () => {
      return null;
    },
  },
  limitSize: {
    type: Number,
    default: 2,
  },
  //单个图片样式
  size: {
    type: String,
    default: "100px",
  },

  //图片上传是否圆形
  round: {
    type: Boolean,
    default: false,
  },
  cropper: {
    type: Boolean,
    default: false,
  },
  //自定义上传成功事件
  success: {
    type: [Function, null],
    default() {
      return null;
    },
  },
});
let emits = defineEmits(["update:modelValue"]);

//样式
let avatarStyle = computed(() => {
  return {
    width: props.size,
    height: props.size,
    lineHeight: props.size,
  };
});

function beforeAvatarUpload(file) {
  const isLt2M = file.size / 1024 / 1024 < props.limitSize;

  if (!isLt2M) {
    ElMessage.warning(`文件大小不能超过${props.limitSize}M`);
  }
  return isLt2M;
}

function handleAvatarSuccess(res) {
  if (res.code == 200) {
    if (props.success) {
      props.success();
      // emits("update:modelValue", res);
    } else {
      emits("update:modelValue", import.meta.env.VITE_BASE_API + res.data.imgUrl);

      ElMessage.success(`上传成功`);
    }
  } else {
    ElMessage.error(`上传失败，请重试！`);
  }
}

let cropperImg = ref();
let imgFile = null;
let visible = ref(false);

function changeFile(file) {
  if (props.cropper) {
    imgFile = file;
    let URL = window.URL || window.webkitURL;
    cropperImg.value = URL.createObjectURL(file.raw);

    visible.value = true;
  }
}

let cropRef = ref();
function cropSubmit() {
  cropRef.value
    .getCropBlob((data) => {
      const file = new window.File([data], imgFile.name, { type: imgFile.type });
      if (!beforeAvatarUpload()) return;
      Axios.post(attrs.action, {
        [attrs.name || "file"]: file,
        ...attrs.data,
      });
    })
    .then((res) => {
      handleAvatarSuccess(res);
    });
}
</script>

<style lang="scss" scoped>
.avatar-uploader :deep(.el-upload) {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  & :hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 20px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    line-height: 100%;
    text-align: center;
  }
  .avatar {
    width: 100%;
    height: 100%;
    display: block;
  }
}
.avatar-uploader.round :deep(.el-upload) {
  border-radius: 100%;
}
.cropper-wrapper {
  width: 100%;
  height: 400px;
}
</style>
