<template>
  <!-- vueCropper 剪裁图片实现-->
  <el-dialog title="图片剪裁" v-model="dialogVisible" append-to-body :width="dialogWidth">
    <vueCropper
      ref="cropperRef"
      :src="option.img"
      :container-style="{ width: '100%', height: '400px' }"
      :img-style="{ 'max-width': '100%', 'max-height': '100%' }"
      v-bind="option"
    ></vueCropper>
    <div class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="finish">确认</el-button>
    </div>
  </el-dialog>
</template>
<script>
import VueCropper from "vue-cropperjs";

export default {
  components: {
    VueCropper,
  },
  props: {
    dialogWidth: {
      type: String,
      default: "600px",
    },
    src: {
      type: String,
      default: "",
    },
    width: {
      type: Number,
      default: 500,
    },
    height: {
      type: Number,
      default: 400,
    },
    containerStyle: {
      type: Object,
      default: () => {
        return {
          width: "500px",
          height: "400px",
        };
      },
    },
  },
  emits: ["crop"],
  data() {
    return {
      dialogVisible: false,
      // 裁剪组件的基础配置option
      option: {
        img: "https://img2.baidu.com/it/u=2627524963,4243200686&fm=26&fmt=auto", // 裁剪图片的地址

        zoomOnWheel: true,
        centerBox: true,
      },
    };
  },
  watch: {},
  methods: {
    // 上传按钮   限制图片大小
    changeUpload(file, fileList) {
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error("上传文件大小不能超过 5MB!");
        return false;
      }
      this.fileinfo = file;

      // 上传成功后将图片地址赋值给裁剪框显示图片
      this.$nextTick(() => {
        this.option.img = file.url;
        this.dialogVisible = true;
      });
    },
    // 点击裁剪，这一步是可以拿到处理后的地址
    finish() {
      let that = this;
      let cropImg = this.$refs.cropperRef
        .getCroppedCanvas({
          imageSmoothingQuality: "high",
        })
        .toDataURL();
      //base64转文件
      var imgFile = that.dataURLtoFile(cropImg, "img");
      this.$emit("crop", imgFile);
    },
    //将图片Base64 转成文件
    dataURLtoFile(dataurl, filename) {
      // console.log("转文件")
      // 获取文件扩展名称
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1];
      var extension = mime.split("/")[1];

      var bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename + "." + extension, { type: mime });
    },

    open() {
      this.dialogVisible = true;
    },
    close() {
      this.dialogVisible = false;
    },
  },
  expose: ["open", "close"],
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  margin-top: 20px;
  text-align: center;
}
</style>
