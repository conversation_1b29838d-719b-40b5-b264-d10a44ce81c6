<template>
  <ul class="action-btns-ul">
    <li v-for="btn in btnListSelf" :key="btn.title" :class="{ disabled: ifDiabled(btn) }" @click="clickFn(btn)">
      <el-tooltip :content="btn.title" placement="top" effect="light">
        <el-icon v-if="btn.icon">
          <component :is="btn.icon" />
        </el-icon>
        <el-icon v-else>
          <svg class="icon" aria-hidden="true">
            <use :xlink:href="'#' + btn.isFont"></use>
          </svg>
        </el-icon>
      </el-tooltip>
    </li>
  </ul>
</template>
<script setup>
import { computed } from "vue";
import { useStore } from "vuex";
import usePermissionFlag from "@/utils/permissionFlag";
const store = useStore();

const permissions = computed(() => {
  return store.state && store.state.permissions;
});
const all_permission = "*:*:*";

let props = defineProps({
  btnList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  scope: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

//按钮权限  hasPermi
let btnListSelf = computed(() => {
  return props.btnList
    .filter((item) => !item.hide)
    .filter((item) => {
      let showFlag = usePermissionFlag(item.hasPermi);

      //表格内的操作按钮，表格每行数据控制显示隐藏
      if (item && item.hideByProp && props.scope) {
        let hideValueArr = [];

        if (Array.isArray(item.hideByPropValue)) {
          hideValueArr = [...item.hideByPropValue];
        } else if ("hideByPropValue" in item) {
          hideValueArr = [item.hideByPropValue];
        }

        if (hideValueArr.includes(props.scope.row[item.hideByProp])) {
          showFlag = false;
        } else {
          showFlag = true;
        }
      }

      return showFlag;
    });
});

function ifDiabled(btn) {
  let scope = props.scope;
  return btn.disabled || (scope.row && scope.row.id && btn.disabledId == scope.row.id);
}

function clickFn(btn) {
  let disabled = ifDiabled(btn);
  if (disabled) return;

  btn.onClick(props.scope);
}
</script>

<style lang="scss" scoped></style>
