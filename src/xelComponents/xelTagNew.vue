<!--目前只用于综合服务设置标签-->

<template>
  <div class="event-set-tag-box">
    <div class="tag-content">
      <ul class="tag-list" ref="tagRef">
        <li v-for="item in checkedData" :key="item.name" class="hasPermi">
          <span class="tag">{{ item.name }}</span>
          <el-icon @click="toDelTag(item.id)"><CircleCloseFilled /></el-icon>
        </li>
        <li class="add-tag-li">
          <el-icon class="add-tag" :size="12" @click.stop="showPopBox">
            <plus />
          </el-icon>
        </li>

        <div ref="tagDialog" class="select-list" v-show="visible" @click.stop :style="{ top: topVal + 'px' }">
          <div class="search-input">
            <el-input v-model="tagName" placeholder="搜索标签" />
            <el-icon class="add-tag-btn" :size="12" @click="toAddTag()">
              <Plus />
            </el-icon>
          </div>
          <ul class="select-list-ul" :class="showSelect ? 'is-show' : ''">
            <li v-for="item in optionList" :key="item.id" @click="addOrDel(item)">
              <span> {{ item.name }}</span>
              <el-icon v-if="isCheck(item.id)"><Check /></el-icon>
            </li>
          </ul>
        </div>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, ref, onUnmounted, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { saveTagByTagName } from "@/api/analyticalDisposal/runscript";

interface IStringObject {
  [index: string]: any;
}
interface TagItem {
  name: string;
  id: string;
}
interface Props {
  modelValue: TagItem[];
  getAllListApi: (params?: IStringObject) => Promise<any>; //通过调用接口获取标签下拉内容
}
const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits(["update:modelValue"]);

const checkedData = ref<TagItem[]>([]);
watch(
  () => props.modelValue,
  (val, oldVal) => {
    if (oldVal) {
      const valStr = JSON.stringify(val);
      const oldValStr = JSON.stringify(oldVal);
      if (valStr != oldValStr) {
        checkedData.value = JSON.parse(JSON.stringify(val));
      }
    } else {
      checkedData.value = JSON.parse(JSON.stringify(val));
    }
  },
  { immediate: true }
);

const visible = ref(false);
const showSelect = ref(false);
const allList = ref<TagItem[]>([]);

//获取全部标签，下拉列表显示
function getAllList() {
  props.getAllListApi().then(({ data }) => {
    allList.value = data.tagList;
  });
}

//判断是否选中
function isCheck(id: string) {
  return checkedData.value.some((item) => item.id === id);
}

//添加或删除标签
function addOrDel(item: TagItem) {
  if (isCheck(item.id)) {
    toDelTag(item.id);
  } else {
    toAddTag(item);
  }
}

//添加标签

function toAddTag(item?: TagItem) {
  if (item) {
    checkedData.value.push(item);
  } else {
    if (tagName.value) {
      saveTagByTagName({
        tagName: tagName.value,
      }).then(({ data }) => {
        const newTag = {
          name: tagName.value,
          id: data.id,
        };
        checkedData.value.push(newTag);
        allList.value.push(newTag);
        tagName.value = "";
      });
    } else {
      ElMessage.warning("请输入标签名称");
    }
  }
}

//删除标签
function toDelTag(id: string) {
  const index = checkedData.value.findIndex((item) => item.id === id);
  checkedData.value.splice(index, 1);
}

// 打开选择下拉
function showPopBox() {
  visible.value = true;

  if (allList.value.length === 0) {
    getAllList();
  }
}

// 输入框
const tagName = ref("");

const optionList = computed(() => {
  return tagName.value.trim() ? allList.value.filter((item) => item.name.includes(tagName.value)) : allList.value;
});

/* 修改 - 动态top */
const tagRef = ref();
const topVal = ref(40);
watch(
  () => checkedData.value,
  (val) => {
    emit("update:modelValue", val);
    setTimeout(() => {
      topVal.value = tagRef.value.offsetHeight + 10;
    }, 200);
  },
  {
    deep: true,
  }
);

function hidePopBox() {
  visible.value = false;
  tagName.value = "";
}
onMounted(() => {
  window.addEventListener("click", hidePopBox);
});
onUnmounted(() => {
  window.removeEventListener("click", hidePopBox);
});
</script>

<style scoped lang="scss">
$--color-primary: #ef8936;
.event-set-tag-box {
  font-size: 12px;
}
.add-tag {
  font-size: 14px;

  cursor: pointer;
  color: #bbb;
  .el-icon {
    opacity: 1 !important;
  }
}
.tag-content {
  display: flex;
  .tag-list {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    margin-top: -8px;
    & > li {
      margin: 8px 0 0 8px;
    }
    & > li:not(.add-tag-li) {
      margin-right: 10px;
      height: 24px;
      line-height: 24px;

      overflow: hidden;
      background-color: mix(#fff, $--color-primary, 90%);
      color: $--color-primary;

      padding: 0px 12px;
      border-radius: 24px;
      align-items: center;
      position: relative;
      display: flex;
      cursor: pointer;
      &.hasPermi:hover {
        .el-icon {
          opacity: 1;
          z-index: 2;
        }
        .tag {
          padding-right: 18px;
        }
      }
      & .el-icon {
        font-size: 16px;
        opacity: 0;
        position: absolute;
        right: 4px;
        top: 4px;
        -webkit-transition: opacity 0.5s ease;
        -o-transition: opacity 0.5s ease;
        transition: opacity 0.5s ease;
      }
      .tag {
        -webkit-transition: padding-right 0.7s ease;
        -o-transition: padding-right 0.7s ease;
        transition: padding-right 0.7s ease;
      }
    }
    & > li.add-tag-li {
      height: 24px;
      width: 24px;
      line-height: 24px;
      border: 1px solid #ddd;
      border-radius: 50%;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.select-list {
  width: 300px;
  text-align: left;
  overflow: auto;
  background-color: #fff;
  border: 1px solid #e4e4e4;
  z-index: 999;
  border-radius: 3px;
  position: absolute;
  /*top: 30px;*/
  /*right: -20px;*/

  .search-input {
    background-color: #fff;
    border-bottom: 1px solid #e4e4e4;

    :deep(.el-input__inner) {
      background-color: #fff;
      border-radius: 0;
      padding-right: 30px;
      border: none;
      &:focus {
        box-shadow: none !important;
      }
    }

    & > .add-tag-btn {
      position: absolute;
      // font-size: 20px;
      font-size: 16px;
      color: #8c8c8c;
      opacity: 0.5;
      top: 6px;
      right: 10px;
      cursor: pointer;
      &:hover {
        opacity: 1;
      }
    }
  }

  & .select-list-ul {
    overflow: auto;
    height: 260px;
    max-height: 260px;
    min-height: auto;
    padding-top: 4px;

    & > li {
      cursor: pointer;
      padding: 8px;

      &:hover {
        color: $--color-primary;

        span {
          transition: all 0.3s ease;
          display: inline-block;
          transform: scale(1.1);
          transform-origin: left center;
        }
      }

      & > .el-icon {
        float: right;
        font-size: 16px;
      }
    }

    &.is-show {
      animation: SHOW_LIST 1s 1;
    }
  }
}

@keyframes SHOW_LIST {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
