<template>
  <el-upload
    size="small"
    ref="uploadFile"
    :class="{ 'upload-demo': true, 'avatar-uploader': uploadType == 'avatar', round: round }"
    name="files"
    :accept="acceptSelf"
    :on-change="fileChange"
    :before-remove="beforeRemove"
    :on-remove="handleRemoveFile"
    :on-preview="handOnPreview"
    :multiple="multiple"
    :limit="limit"
    :disabled="disabled"
    :auto-upload="uploadType == 'avatar' ? true : autoUpload"
    :on-exceed="handleExceedFile"
    :list-type="listType"
    :file-list="state.outFileList"
    :show-file-list="uploadType == 'avatar' ? false : showFileList"
    :headers="{ Authorization: getToken() }"
    :on-success="onSuccess || handleAvatarSuccess"
    v-bind="$attrs"
  >
    <el-button :disabled="disabled" v-if="uploadType != 'avatar'" :size="btnSize" :type="btnType">{{ btnName }}</el-button>
    <div v-else>
      <img v-if="avatarSrcSelf" :src="avatarSrcSelf" class="avatar" :style="avatarStyle" />
      <i v-else class="el-icon-plus avatar-uploader-icon" :style="avatarStyle"></i>
      <xel-cropper ref="cropperRef" :src="avatarSrcSelf" @crop="cropImg"></xel-cropper>
    </div>

    <template #tip>
      <div class="el-upload__tip" v-if="isPromptShow">
        {{ prompt }}
      </div>
    </template>
  </el-upload>
</template>
<script setup>
import { ref, reactive, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import getToken from "@/utils/getToken";
import xelCropper from "./xelCropper.vue";
/*
 * 文件上传组件
 * props 使用请查看对应字段含义
 * 方法： fileChange   文件上传对文件大小格式的限制判断
 *   beforeRemove   继承原事件
 *   handleRemoveFile   将改变后的fileList 传递
 *   handleExceedFile   超出文件数量提示
 *   handOnPreview   点击文件事件 - 继承
 * */
let props = defineProps({
  btnName: {
    type: String,
    default: "点击上传",
  },
  /*按钮大小 - 继承自带class*/
  btnSize: {
    type: String,
    default: "",
  },
  /*文件上传格式   继承原组件 text / picture / picture-card */
  listType: {
    type: String,
    default: "text",
  },
  /*文件提示信息*/
  prompt: {
    type: String,
    default: "请上传文件",
  },
  /*是否显示文件信息*/
  isPromptShow: {
    type: Boolean,
    default: false,
  },
  /*控制上传文件数量*/
  limit: {
    type: Number,
    default: 1,
  },
  /*是否可以多选*/
  multiple: {
    type: Boolean,
    default: false,
  },
  /*是否在选取文件后立即进行上传*/
  autoUpload: {
    type: Boolean,
    default: false,
  },
  /*是否禁用*/
  disabled: {
    type: Boolean,
    default: false,
  },
  /*文件大小限制： 单位 M */
  fileSize: {
    type: Number,
    default: 10,
  },
  accept: {
    type: String,
    default:
      ".bmp, .jpeg, .gif, .png, .jpg," +
      ".docx, .doc,   .xls, .xlsx," +
      ".ppt, .pptx, .html, .htm, .txt, .rar, .zip, .gz, .bz2," +
      ".mp4, .avi, .rmvb, .pdf",
  },

  /* 是否校验格式
   * 默认校验 accept 格式， isAccept: true，accept正常使用
   * 不进行校验时，设置 isAccept: false，accept为 '*'
   * */
  isAccept: {
    type: Boolean,
    default: true,
  },

  /*父组件传递的FileLIst*/
  fileListFa: {
    type: Array,
    default: () => {
      return [];
    },
  },
  //按钮类型
  btnType: {
    type: String,
    default: "primary",
  },
  //上传类型  默认按钮，avatar  图片
  uploadType: {
    type: String,
    default: "",
  },
  //单个图片上传地址
  avatarSrc: {
    type: String,
    default: "",
  },
  //单个图片样式
  avatarSize: {
    type: String,
    default: "100px",
  },
  showFileList: {
    type: Boolean,
    default: true,
  },
  onSuccess: {
    type: [Function, null],
    default: null,
  },
  //单个图片上传后是否裁剪
  cropper: {
    type: Boolean,
    default: false,
  },
  //图片上传是否原型
  round: {
    type: Boolean,
    default: false,
  },
});
let avatarStyle = computed(() => {
  return {
    width: props.avatarSize,
    height: props.avatarSize,
    lineHeight: props.avatarSize,
  };
});
let state = reactive({
  outFileList: [],
});
let acceptSelf = ref(props.accept);
if (props.uploadType == "avatar") {
  acceptSelf = ".bmp, .jpeg, .gif, .png, .jpg";
} else if (!props.isAccept) {
  acceptSelf = "*";
}
const emits = defineEmits(["fileList", "beforeRemove", "handOnPreview"]);
/*上传文件 - 文件大小与格式校验 */
function fileChange(file, fileList) {
  /*判断文件大小*/
  let size = file.size / 1024 / 1024 < props.fileSize;
  /*判断文件类型*/
  let filemsg = "." + file.name.substring(file.name.lastIndexOf(".") + 1);
  /*let isFilemsg = props.accept.indexOf(filemsg) !== -1;*/
  let isFilemsg = props.isAccept ? props.accept.indexOf(filemsg) !== -1 : true;
  if (isFilemsg) {
    if (size) {
      state.outFileList.push(file);
    } else {
      fileList.splice(-1, 1);
      ElMessage(`文件（${file.name}）过大，请重新上传！（限制大小：${props.fileSize} M）`);
    }
  } else {
    fileList.splice(-1, 1);
    ElMessage(`文件（${file.name}）格式不正确，允许上传 ${props.accept} 格式文件！`);
  }
  emits("fileList", state.outFileList);
}
/*移除 - 之前*/
function beforeRemove(file, fileList) {
  emits("beforeRemove", file, fileList);
}
/*移除 上传文件 */
function handleRemoveFile(file, fileList) {
  state.outFileList = fileList;
  emits("fileList", state.outFileList);
}
/*文件数量 限制文件*/
function handleExceedFile() {
  ElMessage(`文件个数超出限制，最多上传 ${props.limit} 个文件`);
  /*this.msgInfo(`当前限制选择 ${this.limit} 个文件，请移除当前多余文件，重新上传!`);*/
  /*this.msgInfo(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);*/
}
/*点击文件 - 事件*/
function handOnPreview(file) {
  emits("handOnPreview", file);
}
watch(
  () => props.fileListFa,
  (fileListFa, newFile) => {
    /*console.info(fileListFa, 99999);*/
    state.outFileList = fileListFa;
  },
  {
    immediate: true,
    deep: true,
  }
);
let avatarSrcSelf = ref(props.avatarSrc);
function handleAvatarSuccess(res, file) {
  avatarSrcSelf.value = res.data.imgUrl;
  props.cropper && openCropper();
}
let cropperRef = ref();

function openCropper() {
  cropperRef.value.open();
}
let uploadFile = ref();
function cropImg(file) {
  uploadFile.value.submit();
  cropperRef.value.close();
  uploadFile.value.clearFiles();
}
</script>

<style scoped lang="scss">
.avatar-uploader {
  :deep(.el-upload) {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: $color;
    }
  }
  .avatar-uploader-icon {
    font-size: 22px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }
  &.round {
    :deep(.el-upload) {
      border-radius: 100%;
    }
  }
}
</style>
