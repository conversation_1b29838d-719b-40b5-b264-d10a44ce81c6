<template>
  <el-dialog
    v-model="dialogVisible"
    :width="popupWidth"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    v-bind="$attrs"
  >
    <div class="el-dialog-div">
      <el-scrollbar ref="scrollRef" style="height: 100%">
        <slot></slot>
      </el-scrollbar>
    </div>
    <div class="dialog-footer" v-if="!ishiddenDialog">
      <slot name="button">
        <el-button v-show="showCancel" @click="close" size="mini">{{ buttonCancel }}</el-button>
        <el-button v-show="showSubmit" type="primary" :disabled="btnDisabled" :loading="loading" size="mini" @click="Save">
          {{ buttonDetermine }}
        </el-button>
        <slot name="otherButton"></slot>
      </slot>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { scrollRef, scrollToUnValid } from "@/utils/scrollToUnValid";

let dialogVisible = ref(false);
let loading = ref(false);
let props = defineProps({
  width: {
    type: String,
    default: "",
  },
  size: {
    type: String,
    default: "middle",
  },
  buttonCancel: {
    type: String,
    default: "取消",
  },
  buttonDetermine: {
    type: String,
    default: "确定",
  },
  ishiddenDialog: {
    type: Boolean,
    default: false,
  },
  showSubmit: {
    type: Boolean,
    default: true,
  },
  showCancel: {
    type: Boolean,
    default: true,
  },
});
// 定义emit事件
const emits = defineEmits(["close", "submit"]);
watch(dialogVisible, (val) => {
  if (!val) {
    loading.value = false;
    emits("close");
  }
});
function close() {
  dialogVisible.value = false;
}
let btnDisabled = ref(false);
function Save() {
  let close = (isclose = true) => {
    loading.value = false;
    if (isclose) {
      dialogVisible.value = false;
    }
  };
  let load = () => {
    loading.value = true;
  };
  scrollToUnValid();
  btnDisabled.value = true;
  emits("submit", close, load);
  setTimeout(() => {
    btnDisabled.value = false;
  }, 1000);
}
function open(next) {
  dialogVisible.value = true;
  nextTick(() => {
    next && next();
  });
}

let popupWidth = computed(() => {
  let width = "785px";
  if (props.width) {
    width = props.width;
  } else if (props.size == "small") {
    width = "557px";
  } else if (props.size == "large") {
    width = "984px";
  }
  return width;
});

defineExpose({
  open,
  close,
});
</script>
<style scoped lang="scss">
.el-dialog-div {
  max-height: calc(80vh - 180px);
  overflow-x: auto;
  padding-right: 8px;
  margin-right: -8px;
}
.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
<style>
.el-dialog-div > .el-scrollbar .el-scrollbar__bar.is-horizontal {
  display: none !important;
}
</style>
