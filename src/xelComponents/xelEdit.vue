<template>
  <!--this.$refs.editorRef.editor.txt.html()  获取富文本内容  -->
  <div className="home" ref="editor">
    <div :class="editorClass"></div>
  </div>
</template>

<script>
import wangEditor from "wangeditor";
import { ElMessage } from "element-plus";
import { getToken } from "@/utils";

export default {
  name: "Editor",
  props: {
    desc: {
      type: String,
      default: "",
    },
    //业务中我们经常会有添加操作和编辑操作，添加操作时，我们需清除上一操作留下的缓存
    isClear: {
      type: Boolean,
      default: false,
    },
    editorClass: {
      type: String,
      default: "editor",
    },
    changeTimeout: {
      type: Number,
      default: 200,
    },
  },
  watch: {
    isClear: {
      handler(newVal) {
        const self = this;
        self.$nextTick(() => {
          if (newVal) {
            self.editor.txt.clear();
            // this.info_=null
          }
        });
      },
      /*deep: true,*/
      immediate: true,
    },
    desc: {
      handler(newVal) {
        const self = this;
        self.$nextTick(() => {
          if (newVal !== self.editor.txt.html()) {
            self.editor.txt.html(newVal);
          }
          if (!newVal) {
            self.editor.txt.clear();
          }
        });
      },
      /*deep: true,*/
      immediate: true,
    },
  },
  data() {
    return {
      content: "",
      editor: null,
      info_: null,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let self = this;
      let editor = new wangEditor(`.${this.editorClass}`);

      editor.config.menus = [
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        /*'foreColor',  // 文字颜色
        'backColor',  // 背景颜色*/
        "list", // 列表
        /*'justify',  // 对齐方式*/
        "quote", // 引用
        "image", // 插入图片
        "undo", // 撤销
        "redo", // 重复
      ];
      editor.config.showLinkImg = false;
      editor.config.showFullScreen = false;
      editor.config.menuTooltipPosition = "down";
      editor.config.placeholder = "请输入";

      editor.config.onchangeTimeout = this.changeTimeout; //触发时间频率，以防操作过快有问题
      // 配置 onchange 回调函数，将数据同步到 vue 中
      editor.config.onchange = (newHtml) => {
        this.info_ = newHtml; // 绑定当前逐渐地值

        if (!this.checkEmpty(newHtml)) {
          this.$emit("change", this.info_); // 将内容同步到父组件中
        } else {
          this.$emit("change", ""); // 将内容同步到父组件中
        }
      };

      editor.config.onblur = function (newHtml) {
        /* */ // 获取最新的 html 内容
        /* */
        self.$emit("newHtml", newHtml);
      };

      editor.config.zIndex = 500;
      editor.config.uploadImgMaxSize = 10 * 1024 * 1024; // 10M
      editor.config.uploadFileName = "file";
      // editor.config.uploadImgParamsWithUrl = true;
      editor.config.uploadImgServer = import.meta.env.VITE_BASE_API + "/system/file/upload";
      // editor.config.uploadImgShowBase64 = true;
      // editor.config.uploadImgServer = process.env.VUE_APP_BASE_API + "/system/file/uploadByWang";
      editor.config.uploadImgParams = {
        x: 100,
      };
      editor.config.uploadImgHeaders = {
        Authorization: "Bearer " + getToken(),
      };
      editor.config.linkImgCheck = function (src) {
        return true; // 返回 true 表示校验成功
      };
      editor.config.uploadImgHooks = {
        // 图片上传并返回了结果，想要自己把图片插入到编辑器中
        // 例如服务器端返回的不是 { errno: 0, data: [...] } 这种格式，可使用 customInsert
        customInsert: function (insertImgFn, result) {
          if (result.code == 500) {
            ElMessage.warning(result.msg);
            return;
          }
          // result 即服务端返回的接口
          /**/
          result.data.forEach((item, i) => {
            // insertImgFn 可把图片插入到编辑器，传入图片 src ，执行函数即可
            insertImgFn("/outpost-api" + item["url"] + "&token=" + getToken());
          });
        },
      };
      // 创建编辑器
      editor.create();
      this.editor = editor;
    },
    //判断是否为空
    /**
     * 判断editor富文本域是否为空
     * str返回的值为"" 代表输入框里面有值 成功
     * str返回！="" 代表里面有空格 回车 失败
     * */
    checkEmpty(str) {
      let num = 0,
        reg = /<p>(&nbsp;|&nbsp;\s+)+<\/p>|<p>(<br>)+<\/p>/g;
      while (num < str.length && str != "") {
        num++;
        let k = str.match(reg);
        if (k) {
          str = str.replace(k[0], "");
        }
      }
      return str == "";
    },
  },
};
</script>

<style scoped></style>
