<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button @click="newlyAdded" class="search-button">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>

      <el-button @click="delFn(state.multipleSelection)" :disabled="multiples" class="search-button">
        <el-icon :size="12">
          <delete />
        </el-icon>
        批量删除
      </el-button>
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange"> </xel-table>
    <!-- 弹窗内容 -->
    <xel-dialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xel-dialog>
    <!-- 详情弹框 -->
    <detail-dialog ref="0" :list="detailList" :data="detailInfo"></detail-dialog>
  </el-card>
</template>
<script setup>
import { listRole as getTableData, getRole as getDetail, addRole as addItem, updateRole as updateItem, delRole as delItem } from "@/api/system/role";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import { batchDelete } from "@/utils/delete";
let dialogText = ref("字典类型");
let idKey = "roleId";
let tableRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    value: "",
  },
  menuData: [
    {
      lable: "状态：",
      prop: "status",
      options: [],
      dictName: "sys_common_status",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "value",
      label: "关键字",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    value: "",
  };
  search();
}
//搜索结束

let state = reactive({
  formData: {}, //新增编辑表单
  multipleSelection: [],
});
let { formData } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  state.formData = {
    name: "",
  };
}

// 列表配置项
const columns = [
  {
    prop: "name",
    label: "列名称",
  },
  //详细，用不到可以删除
  // {
  //   label: "操作",
  //   fixed: "right",
  //   formatter() {
  //     return "详细";
  //   },
  //   click({ row }) {
  //     for (let attr in row) {
  //       detailInfo[attr] = row[attr];
  //     }
  //     detailDialogRef.value.open();
  //   },
  // },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton(scope.row[idKey]);
        },
      },
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn([scope.row]);
        },
      },
    ],
  },
];
//删除，批量删除
function delFn(rows) {
  batchDelete().then(() => {
    let ids = rows.map((item) => item[idKey]);
    delItem({ ids: ids.join() }).then(() => {
      ElMessage.success("操作成功");
      search(false);
    });
  });
}
// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  resetFormData();
  popupBox();
}
let editId = ref("");
// 修改按钮
function modifyButton(id) {
  editId.value = id;
  getDetail(id).then(({ data }) => {
    for (let key in data) {
      state.formData[key] = data[key];
    }
    popupBox();
  });
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}

// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "字典类型",
    required: true,
  },
  {
    formType: "radio",
    prop: "resource",
    label: "状态",
    required: true,
    options: [],
    dictName: "sys_normal_disable",
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let addFn = editId.value ? updateItem : addItem;
      addFn(state.formData)
        .then((res) => {
          search(false);
          close();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
  ruleFormRef.value.resetFields();
}

//详情相关

let detailDialogRef = ref();
let detailInfo = reactive({});
let detailList = [
  {
    prop: "name",
    label: "名称",
  },
  {
    prop: "jobMessage",
    label: "日志信息",
    width: "100%", //可以自定义宽度
  },
];
</script>
<style scoped lang="scss"></style>
