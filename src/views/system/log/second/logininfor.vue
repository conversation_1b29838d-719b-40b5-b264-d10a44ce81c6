<template>
  <el-card>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button size="small" @click="newlyAdded" class="search-button" :disabled="multiples" v-hasPermi="'system:logininfor:remove'">
        <el-icon :size="12">
          <delete />
        </el-icon>
        删除
      </el-button>
      <el-button size="small" @click="empty" class="search-button" v-hasPermi="'system:logininfor:remove'">
        <el-icon :size="12">
          <delete />
        </el-icon>
        清空
      </el-button>
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="getTableData"
      @selection-change="handleSelectionChange"
      :checkbox="true"
      :row-key="idKey"
    >
    </xel-table>
  </el-card>
</template>
<script setup>
import { list as getTableData, cleanLogininfor, delLogininfor } from "@/api/system/logininfor";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let idKey = "infoId";
let tableRef = ref();
let searchState = reactive({
  data: {
    ipaddr: "",
    userName: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态：",
      prop: "status",
      options: [],
      dictName: "sys_common_status",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "ipaddr",
      label: "登录地址",
    },
    {
      formType: "input",
      prop: "userName",
      label: "用户名称",
    },
  ],
});
// 列表配置项
const columns = [
  {
    prop: "infoId",
    label: "访问编号",
  },
  {
    prop: "userName",
    label: "用户名称",
  },
  {
    prop: "ipaddr",
    label: "登录地址",
  },
  {
    prop: "statusText",
    label: "状态",
  },
  {
    prop: "msg",
    label: "描述",
  },
  {
    prop: "accessTime",
    label: "访问时间",
  },
];
//获取表格数据
let loading = ref(false);
function search(initPage = true) {
  tableRef.value.reload(searchState.data, initPage);
}
function reset() {
  searchState.data = {
    ipaddr: "",
    userName: "",
    pageNum: 1,
    pageSize: 10,
  };
  search();
}
// 清空按钮
function empty() {
  ElMessageBox.confirm("是否确认清空所有登录日志数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      cleanLogininfor().then((res) => {
        ElMessage({
          message: "清空成功",
          type: "success",
        });
      });
      search();
    })

    .catch((action) => {
      ElMessage({
        type: "info",
        message: "取消操作",
      });
    });
}
// 列表多选
let state = reactive({ form: {} });
let { form } = toRefs(state);
let formRef = ref();
let multiples = ref(true);
function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 删除按钮
function newlyAdded() {
  let rows = [];
  rows = state.multipleSelection;
  let ids = rows.map((item) => {
    return item[idKey];
  });
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delLogininfor(ids.join()).then(() => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      search(false);
      tableRef.value.table.clearSelection();
    });
  });
}
</script>
<style scoped lang="scss"></style>
