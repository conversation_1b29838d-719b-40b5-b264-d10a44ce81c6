<template>
  <el-card>
    <!-- <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3> -->
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button size="small" @click="newlyAdded" class="search-button" :disabled="multiples" v-hasPermi="'system:operlog:remove'">
        <el-icon :size="12">
          <delete />
        </el-icon>
        删除
      </el-button>
      <el-button size="small" @click="empty" class="search-button" v-hasPermi="'system:operlog:remove'">
        <el-icon :size="12">
          <delete />
        </el-icon>
        清空
      </el-button>
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="getTableData"
      @selection-change="handleSelectionChange"
      :checkbox="true"
      :row-key="idKey"
    >
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog title="操作日志详细" ref="dialog">
      <el-form ref="formRef" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作模块：">{{ form.title }} / </el-form-item>
            <el-form-item label="登录信息：">{{ form.operName }} / {{ form.operIp }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求地址：">{{ form.operUrl }}</el-form-item>
            <el-form-item label="请求方式：">{{ form.requestMethod }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="请求参数：">{{ form.operParam }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="返回参数：">{{ form.jsonResult }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作状态：">
              <div v-if="form.status === 0">成功</div>
              <div v-else-if="form.status === 1">失败</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作时间：">{{ form.operTime }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常信息：" v-if="form.status === 1">{{ form.errorMsg }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #button>
        <el-button round @click="close" size="mini">关闭</el-button>
      </template>
    </xelDialog>
  </el-card>
</template>
<script setup>
import { list as getTableData, delOperlog, cleanOperlog } from "@/api/system/operlog";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let idKey = "operId";
let dialogRef = ref();
let tableRef = ref();
let searchState = reactive({
  data: {
    title: "",
    operName: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "操作状态：",
      prop: "status",
      options: [],
      dictName: "sys_common_status",
    },
    {
      lable: "操作类型：",
      prop: "businessType",
      options: [],
      dictName: "sys_oper_type",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "系统模块",
    },
    {
      formType: "input",
      prop: "operName",
      label: "操作人员",
    },
  ],
});
// 列表配置项
const columns = [
  {
    prop: "operId",
    label: "日志编号",
  },
  {
    prop: "title",
    label: "系统模块",
  },
  {
    prop: "businessTypeText",
    label: "操作类型",
  },
  {
    prop: "requestMethod",
    label: "请求方式",
  },
  {
    prop: "operName",
    label: "操作人员",
  },
  {
    prop: "operIp",
    label: "主机",
  },
  {
    prop: "statusText",
    label: "操作状态",
  },
  {
    prop: "operTime",
    label: "操作日期",
  },
  {
    label: "操作",
    fixed: "right",
    formatter() {
      return "详细";
    },
    click(scope) {
      batchDelete(scope.row);
    },
  },
];
//获取表格数据
let loading = ref(false);
function search(initPage = true) {
  tableRef.value.reload(searchState.data, initPage);
}
function reset() {
  searchState.data = {
    title: "",
    operName: "",
    pageNum: 1,
    pageSize: 10,
  };
  tableRef.value.reload(searchState.data);
}
// 弹框
let dialog = ref();
let state = reactive({ form: {} });
let { form } = toRefs(state);
let formRef = ref();

// 弹框
function batchDelete(row) {
  state.form = row;
  dialog.value.open();
}
//关闭按钮
function close() {
  dialog.value.close();
}
// 清空按钮
function empty() {
  ElMessageBox.confirm("是否确认清空所有操作日志数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      cleanOperlog().then((res) => {
        ElMessage({
          message: "清空成功",
          type: "success",
        });
        search(false);
      });
    })

    .catch((action) => {
      ElMessage({
        type: "info",
        message: "取消操作",
      });
    });
}
// 列表多选
let multiples = ref(true);
function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 删除按钮
function newlyAdded() {
  let rows = [];
  rows = state.multipleSelection;
  let ids = rows.map((item) => {
    return item[idKey];
  });
  console.log(ids.join());
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delOperlog(ids.join()).then(() => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      search(false);
      tableRef.value.table.clearSelection();
    });
  });
}
</script>
<style scoped lang="scss"></style>
