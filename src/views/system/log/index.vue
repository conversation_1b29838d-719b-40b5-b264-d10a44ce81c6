<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="操作日志" name="first"><Operlog /></el-tab-pane>
      <el-tab-pane label="登录日志" name="second"><Logininfor /></el-tab-pane>
      <el-tab-pane label="运行日志" name="logExport"><LogExport /></el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "Log",
};
</script>
<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import Logininfor from "./second/logininfor.vue";
import Operlog from "./second/operlog.vue";
import LogExport from "@/views/sime/system/log/components/logExport.vue";
let activeName = ref("first");
function handleClick(tab, event) {
  console.log(tab, event);
}
</script>
<style scoped lang="scss"></style>
