<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="search" @reset="reset" label-width="150px">
      <el-button size="small" @click="newlyAdded" v-hasPermi="'operation:basicResource:list'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <el-table
      ref="table"
      :data="state.list"
      row-key="id"
      :tree-props="{ children: 'childSoftware' }"
      @selection-change="handleSelectionChange"
      v-loading="loading"
    >
      <el-table-column type="expand">
        <template #default="{ row, $index }">
          <div class="gray-table">
            <xel-table
              :ref="(el) => setItemRef(el, row.id)"
              :load-data="selectChildPage"
              :columns="columnsChild"
              :default-params="{ parentId: row.id, ...searchState.data }"
            >
              <template #actions="scope">
                <ul class="action-btns-ul">
                  <li @click="handleUpdate(scope.row, true, $index)">
                    <el-tooltip content="修改" placement="top" effect="light">
                      <el-icon>
                        <edit />
                      </el-icon>
                    </el-tooltip>
                  </li>
                  <li @click="handleDelete(scope.row, true, $index)" v-hasPermi="'operation:basicResource:remove'">
                    <el-tooltip content="删除" placement="top" effect="light">
                      <el-icon>
                        <delete />
                      </el-icon>
                    </el-tooltip>
                  </li>
                </ul>
              </template>
            </xel-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-for="item in columns" :key="item.id" v-bind="item"></el-table-column>
      <el-table-column prop="action" label="操作" width="200px">
        <template #default="scope">
          <ul class="action-btns-ul">
            <li @click="handleUpdate(scope.row)">
              <el-tooltip content="修改" placement="top" effect="light">
                <el-icon>
                  <edit />
                </el-icon>
              </el-tooltip>
            </li>
            <li @click="handleDelete(scope.row)" v-hasPermi="'operation:basicResource:remove'">
              <el-tooltip content="删除" placement="top" effect="light">
                <el-icon>
                  <delete />
                </el-icon>
              </el-tooltip>
            </li>
            <li v-show="scope.row.parentId === null || scope.row.parentId === '' ? true : false" @click="handleAddChild(scope.row)">
              <el-tooltip content="添加子基础资源软件" placement="top" effect="light">
                <el-icon>
                  <plus />
                </el-icon>
              </el-tooltip>
            </li>
          </ul>
        </template>
      </el-table-column>
    </el-table>
    <xel-pagination
      ref="paginationRef"
      style="text-align: right"
      class="xel-table-pagination margin-top20"
      v-model:currentPage="searchState.data.pageNum"
      :total="state.total"
      :page-size="searchState.data.pageSize"
      @change="changePage"
    ></xel-pagination>
    <xelDialog
      :custom-class="{ 'loading-dialog': dialogLoading }"
      :title="soft_title"
      ref="add_software"
      size="small"
      @submit="submit_software"
      @close="colse_edit"
    >
      <el-form ref="add_software_form_ref" :model="addForm" :rules="state.rules" label-width="160px">
        <el-form-item :label="(isChild ? '子' : '') + '基础资源软件名称'" prop="name">
          <el-input v-model.trim="addForm.name" :placeholder="`请输入${isChild ? '子' : ''}基础资源软件名称`" />
        </el-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "BasicSoftware",
};
</script>
<script setup>
import { getJczyList as getTableData, addJczyList, deleteSoft, selectChildPage } from "@/api/system/basicSoftware";
import { ref, reactive, onMounted, nextTick, toRefs } from "vue";
import { handleTree } from "@/utils/ruoyi";
import xelPagination from "@/xelComponents/xelPagination.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { batchDelete } from "@/utils/delete";
/*查询内容 */
let searchState = reactive({
  data: {
    name: "",
    softName: "",
    pageNum: 1,
    pageSize: 10,
  },
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "基础资源软件类别",
    },
    {
      formType: "input",
      prop: "softName",
      label: "基础资源软件名称",
    },
  ],
});
// 搜索
function search(initPage = true) {
  getData();
}
let table = ref();
// 重置
function reset() {
  searchState.data = {
    name: "",
    softName: "",
    pageNum: 1,
    pageSize: searchState.data.pageSize,
  };
  getData();
  // 重置 时需要子表格折叠
  closeAllTable();
}
// 全部折叠
function closeAllTable() {
  state.list.forEach((item) => {
    table.value.toggleRowExpansion(item, false);
  });
}
// 表格
const columns = [
  {
    prop: "name",
    label: "基础资源软件名称",
    "show-overflow-tooltip": true,
  },
];
let columnsChild = [
  {
    prop: "empty",
    label: "",
    width: "50px",
  },
  {
    prop: "name",
    label: "基础资源软件名称",
    "show-overflow-tooltip": true,
  },
  {
    label: "操作",
    width: "200px",
    fixed: "right",
    slotName: "actions",
  },
];
let state = reactive({
  list: [],
  total: "",
  software_data: {},

  rules: {
    name: [{ required: true, message: "名称不能为空", trigger: ["blur", "change"] }],
  },
});
let addForm = ref({
  name: "",
});

// 获取表格数据
let loading = ref(false);
getData();
function getData() {
  loading.value = true;

  getTableData(searchState.data).then((res) => {
    state.list = res.data.rows;
    state.total = res.data.total;
    loading.value = false;
  });
}
// 新增
let add_software = ref();
let soft_title = ref("");
let add_software_form_ref = ref();
let parentNameShow = ref(false);
function newlyAdded() {
  isChild.value = false;
  soft_title.value = "添加基础资源软件";

  addForm.value = {
    name: "",
  };

  open();
}
function colse_edit() {
  add_software_form_ref.value.clearValidate();
}
function changePage(obj) {
  searchState.data.pageNum = obj.pageNum;
  searchState.data.pageSize = obj.pageSize;
  getData();
}
let dialogLoading = ref(false);
// 提交
function submit_software(close, loading) {
  add_software_form_ref.value.validate((valid) => {
    if (valid) {
      loading();
      dialogLoading.value = true;
      addJczyList(addForm.value)
        .then((res) => {
          ElMessage({
            type: "success",
            message: "操作成功",
          });
          dialogLoading.value = false;

          close();
          if (!isChild.value) {
            getData();
          } else {
            tableRefList[currentChild.parentId].reload(searchState.data, true);
          }
        })
        .catch((res) => {
          dialogLoading.value = false;

          close(false);
        });
    }
  });
}
let isChild = ref(false);
let tableRefList = {};
let currentChild = {};
function setItemRef(el, id) {
  if (el) {
    tableRefList[id] = el;
  }
}

// 编辑
function handleUpdate(row, child = false) {
  currentChild = row;
  isChild.value = child;
  soft_title.value = isChild.value ? "修改子基础资源软件" : "修改基础资源软件";
  addForm.value.parentName = row.parentName;
  addForm.value.parentId = row.parentId;
  addForm.value.name = row.name;
  addForm.value.id = row.id;
  open();
}
function open() {
  add_software.value.open(() => {
    add_software_form_ref.value.clearValidate();
  });
}
// 删除
function handleDelete(scope, child = false) {
  batchDelete().then((res) => {
    isChild.value = child;

    deleteSoft(scope.id).then((res) => {
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      if (!child) {
        getData();
      } else {
        tableRefList[scope.parentId].reload(searchState.data, false);
      }
    });
  });
}
// 添加子项
function handleAddChild(scope) {
  currentChild = { parentId: scope.id };
  isChild.value = true;
  soft_title.value = "添加子基础资源软件";
  parentNameShow.value = true;
  addForm.value.parentName = scope.name;
  addForm.value.parentId = scope.id;
  addForm.value.name = "";
  addForm.value.id = "";
  open();
}
</script>

<style lang="scss" scoped>
:deep(.loading-dialog) {
  &:after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.5);
    z-index: 1;
    border-radius: inherit;
  }
}
</style>
