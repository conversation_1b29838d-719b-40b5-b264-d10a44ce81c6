<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <!-- <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button size="small" @click="newlyAdded">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search> -->

    <el-table
      ref="table"
      :data="list"
      row-key="menuId"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
      v-loading="loading"
    >
      <el-table-column v-for="item in columns" :key="item.id" v-bind="item"></el-table-column>
      <!-- <el-table-column prop="action" label="操作">
        <template #default="scope">
          <xel-handle-btns :btn-list="btnList" :scope="scope"></xel-handle-btns>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
        <el-form-item label="上级菜单">
          <treeselect
            v-if="formData.parentId > -1"
            v-model="formData.parentId"
            :options="menuOptions"
            :normalizer="normalizer"
            placeholder="选择上级菜单"
          />
        </el-form-item>
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script setup>
import { listMenu as getTableData, getMenu as getDetail, addMenu as addItem, updateMenu as updateItem, delMenu as delItem } from "@/api/system/menu";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, computed, nextTick } from "vue";
import { handleTree } from "@/utils/ruoyi";

let dialogText = ref("菜单");
let idKey = "menuId";
let treeRef = ref();

//查询相关
let searchState = reactive({
  data: {
    menuName: "",
    status: "",
  },
  menuData: [
    {
      lable: "状态：",
      prop: "status",
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "menuName",
      label: "菜单名称",
    },
  ],
});
function search(initPage = true) {
  getData();
}
function reset() {
  searchState.data = {
    menuName: "",
    status: "",
  };
  getData();
}

let state = reactive({
  formData: {},
  list: [],
  menuOptions: [],
  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
});
function resetFormData() {
  state.formData = {
    menuId: 0,
    parentId: 0,
    menuName: "",
    menuType: "M",
    orderNum: "",
    isFrame: "1",
    isCache: "1",
    visible: "0",
    status: "1",
  };
}

//获取表格数据
let loading = ref(false);
getData();
function getData() {
  loading.value = true;
  getTableData(searchState.data).then(({ data }) => {
    state.list = handleTree(data, "menuId");
    loading.value = false;
  });
}

// 列表配置项
const columns = [
  {
    prop: "menuName",
    label: "菜单名称",
    "show-overflow-tooltip": true,
  },
  // {
  //   prop: "orderNum",
  //   label: "排序",
  // },
  {
    prop: "perms",
    label: "权限标识",
    "show-overflow-tooltip": true,
  },
  // {
  //   prop: "status",
  //   label: "状态",
  // },
  // {
  //   prop: "createTime",
  //   label: "创建时间",
  // },
];
let btnList = reactive([
  {
    icon: "edit",
    title: "修改",
    onClick(scope) {
      dialogTitle.value = "修改" + dialogText.value;
      modifyButton(scope.row[idKey]);
    },
  },
  {
    icon: "plus",
    title: "新增",
    onClick(scope) {
      dialogTitle.value = "新增" + dialogText.value;
      resetFormData();
      state.formData.parentId = scope.row.menuId;
      popupBox();
    },
  },
  {
    icon: "delete",
    title: "删除",
    onClick(scope) {
      batchDelete([scope.row]);
    },
  },
]);
// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  popupBox();
  resetFormData();
}
let editId = ref("");
// 修改按钮
function modifyButton(id) {
  editId.value = id;
  getDetail(id).then(({ data }) => {
    for (let key in data) {
      state.formData[key] = data[key];
    }
    popupBox();
  });
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 批量删除
function batchDelete(arr) {
  let rows = [];
  if (Array.isArray(arr)) {
    rows = arr;
  } else {
    rows = state.multipleSelection;
  }
  let ids = rows.map((item) => {
    return item[idKey];
  });
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delItem(ids.join()).then(() => {
      getData();
      ElMessage({
        message: "删除成功",
      });
    });
  });
}

// 搜索条件
// let searchData = reactive({
//   name: "",
//   menuName: "",
//   status: "",
// });
// // 获取querMenu组件返回值
// function selectValue(data) {
//   if (data.id === -1) {
//     searchData[data.parameter] = "";
//   } else {
//     searchData[data.parameter] = data.id;
//   }
// }

// 弹框
let dialogTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  getTreeselect();

  dialog.value.open();
}
// 弹框内容
let formList = computed(() => {
  let list = [
    {
      formType: "radio",
      prop: "menuType",
      label: "菜单类型",
      required: true,
      options: [
        { label: "目录", value: "M" },
        { label: "菜单", value: "C" },
        { label: "按钮", value: "F" },
      ],
    },
    {
      formType: "input",
      prop: "menuName",
      label: "菜单名称",
      required: true,
    },
    {
      formType: "input",
      type: "number",
      prop: "orderNum",
      label: "显示排序",
      required: true,
    },
  ];
  if (state.formData.menuType == "M" || state.formData.menuType == "C") {
    list = list.concat([
      {
        formType: "input",
        prop: "path",
        label: "路由地址",
        required: true,
      },

      {
        formType: "radio",
        prop: "isFrame",
        label: "是否外链",
        options: [
          { label: "是", value: "0" },
          { label: "否", value: "1" },
        ],
      },
      {
        formType: "radio",
        prop: "visible",
        label: "显示状态",
        dictName: "sys_show_hide",
      },
      {
        formType: "radio",
        prop: "isCache",
        label: "是否缓存",
        options: [
          { label: "缓存", value: "1" },
          { label: "不缓存", value: "0" },
        ],
      },
    ]);
  } else {
    list = list.concat([
      {
        formType: "input",
        prop: "perms",
        label: "权限标识",
      },
    ]);
  }
  return list;
});
function getTreeselect() {
  getTableData().then((response) => {
    state.menuOptions = [];
    const menu = { menuId: 0, menuName: "主类目", children: [] };
    menu.children = handleTree(response.data, "menuId");
    state.menuOptions.push(menu);
  });
}
/** 转换菜单数据结构 */
function normalizer(node) {
  if (node.children && !node.children.length) {
    delete node.children;
  }
  return {
    id: node.menuId,
    label: node.menuName,
    children: node.children,
  };
}

// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading();
      let addFn = editId.value ? updateItem : addItem;
      addFn(state.formData)
        .then((res) => {
          close();
          getData();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleForm.value.resetFields();
    state.formData.parentId = -1;
  });
}
let { list, menuOptions, formData } = toRefs(state);
</script>
<style scoped lang="scss"></style>
