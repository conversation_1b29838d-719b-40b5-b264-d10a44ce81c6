<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-row class="margin-top30">
      <el-button @click="exportFn('checkEvent', '事件')">事件导出</el-button>
      <el-button @click="exportFn('checkEventTas', '事件任务')">事件任务导出</el-button>
      <el-button @click="exportFn('checkEventAlerts', '事件告警')">事件告警导出</el-button>
      <el-button @click="exportFn('checkEventMttdMtta', '事件MTTD/MTTA')">事件MTTD/MTTA导出</el-button>
      <el-button @click="exportFn('checkAlertInfo', '告警信息')">告警信息导出</el-button>
      <el-button @click="exportFn('checkAlertUser', '告警分配')">告警分配导出</el-button>
      <el-button @click="exportFn('checkParamValue', '运营参数')">运营参数导出</el-button>
    </el-row>
    <h3 class="slogan">导出的数据为上一次定时器同步之后或者手动导出的时间到现在的数据！</h3>
    <el-row style="margin-top: 80px">
      <el-button @click="playbook">Usecase更新率及定性Playbook使用率导出</el-button>
    </el-row>
    <h3 class="slogan">Usecase更新率及定性Playbook使用率为上月数据及本月1号到目前为止的数据！</h3>
  </el-card>
</template>
<script>
export default {
  name: "ExportSsp",
};
</script>
<script setup>
import Axios from "@/plugins/request";
import { download } from "@/plugins/request";

//Usecase更新率及定性Playbook使用率导出
function playbook() {
  download("/system/exportSspData/exportUsPlay", "Usecase更新率及定性Playbook使用率.xls");
}

//校验
function exportFn(name, text) {
  let urlObj = {
    //事件
    checkEvent: {
      validate: "/system/exportSspData/checkEvent",
      export: "/system/exportSspData/exportEvent",
    },
    //事件任务
    checkEventTas: {
      validate: "/system/exportSspData/checkEventTask",
      export: "/system/exportSspData/exportEventTask",
    },
    //事件告警
    checkEventAlerts: {
      validate: "/system/exportSspData/checkEventAlerts",
      export: "/system/exportSspData/exportEventAlerts",
    },
    // 事件MTTD/MTTA
    checkEventMttdMtta: {
      validate: "/system/exportSspData/checkEventMttdMtta",
      export: "/system/exportSspData/exportEventMttdMttaInfo",
    },
    //告警
    checkAlertInfo: {
      validate: "/system/exportSspData/checkAlertInfo",
      export: "/system/exportSspData/exportAlertInfo",
    },
    //告警分配
    checkAlertUser: {
      validate: "/system/exportSspData/checkAlertUser",
      export: "/system/exportSspData/exportAlertUser",
    },
    //运营参数校验
    checkParamValue: {
      validate: "/system/exportSspData/checkParamValue",
      export: "/system/exportSspData/exportParamValue",
    },
  };
  Axios.get(urlObj[name].validate).then(() => {
    download(urlObj[name].export, text + "信息表.xls");
  });
}
</script>
<style scoped lang="scss">
.slogan {
  margin-top: 30px;
  color: #f00;
}
:deep {
  .el-button {
    margin-right: 20px;
  }
}
</style>
