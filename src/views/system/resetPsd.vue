<template>
  <el-card>
    <h3 class="conH3Tit">{{ needChangePassword ? "密码修改" : $route.meta && $route.meta.title }}</h3>
    <h4 class="red-remark">您需要首先修改密码。修改完成后请重新登录系统！</h4>
    <div class="text-center content-wrapper">
      <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
        <xel-form-item
          v-for="(item, index) in psdList"
          :key="index"
          v-model="formData[item.prop]"
          v-bind="item"
          autocomplete="new-password"
        ></xel-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </div>
    </div>
  </el-card>
</template>
<script>
export default {
  name: "ResetPsd",
};
</script>
<script setup>
import { ref, reactive, toRefs, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { ciiEncrypt } from "@/utils/jsencrypt";
import { changePassword } from "@/api/login";

import { validatePass, validatePass2 } from "@/utils/passwordValid.js";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
const router = useRouter();

const store = useStore();
let userInfo = computed(() => {
  return store.state.userInfo;
});

let needChangePassword = computed(() => {
  return store.state.needChangePassword;
});

onMounted(() => {
  if (userInfo.value && userInfo.value.userName) {
    init();
    watcher();
  }
});
let watcher = watch(
  () => userInfo.value,
  () => {
    init();
  }
);
function init() {
  formData.userName = userInfo.value.userName;

  formData.email = userInfo.value.email;
}
let formData = reactive({
  userName: "",
  email: "",
  oldPassword: "",
  password: "",
  confirmPassword: "",
});

let psdList = ref([
  {
    prop: "userName",
    label: "用户名",
    required: true,
    disabled: true,
  },

  {
    prop: "email",
    label: "Email",
    required: import.meta.env.VITE_IS_SIME ? false : true,
    disabled: import.meta.env.VITE_IS_SIME ? false : true,
  },
  {
    formType: "input",
    prop: "oldPassword",
    label: "旧密码",
    required: true,
    type: "password",
  },
  {
    formType: "input",
    prop: "password",
    label: "新密码",
    required: true,
    type: "password",
    rules: [{ validator: validatePass, trigger: "blur" }],
  },
  {
    formType: "input",
    prop: "confirmPassword",
    label: "确认密码",
    required: true,
    type: "password",
    rules: [{ validator: (a, b, c) => validatePass2(formData.password, a, b, c), trigger: "blur" }],
  },
]);
let ruleForm = ref();
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      let params = {
        userId: userInfo.value.userId,
        oldPassword: ciiEncrypt(formData.oldPassword),
        password: ciiEncrypt(formData.password),
      };
      changePassword(params).then(() => {
        ElMessage.success("修改成功，请重新登录系统");
        setTimeout(() => {
          localStorage.removeItem("TOKEN_NAME");
          router.push("/login");
        }, 500);
      });
    } else {
      return false;
    }
  });
}
</script>

<style lang="scss" scoped>
.content-wrapper {
  width: 500px;
  margin: 20px auto;
}
.red-remark {
  color: red;
  margin-bottom: 20px;
  font-size: 18px;
  text-align: center;
}
</style>
