<template>
  <!-- 字典管理 - 列表  -->
  <el-card>
    <h3 class="conH3Tit">{{ "字典管理" || ($route.meta && $route.meta.name) }}</h3>
    <!-- Siem使用字典管理 -->
    <div v-if="!isSystem">
      <el-row :gutter="20" v-if="tabsList.length">
        <el-col :span="3" style="text-align: end">
          <el-tabs v-model="activeName" tab-position="left" style="height: 100%">
            <el-tab-pane v-for="item in tabsList" :key="item.dictValue" :label="item.dictLabel" :name="item.dictValue" />
          </el-tabs>
        </el-col>
        <el-col :span="21" class="ml-15-new">
          <DictItem v-if="activeName" :fieldAvailable="activeName" />
        </el-col>
      </el-row>
      <p v-else class="text-center no-data">暂无数据</p>
    </div>

    <!-- 前哨使用字典管理 -->
    <div v-else>
      <DictItem />
    </div>
  </el-card>
</template>
<script setup>
import { computed, onActivated, ref } from "vue";
import DictItem from "./dictItem.vue";
import { getDicts as getDictsSime } from "@/api/sime/config/dict";
import { useRoute } from "vue-router";
const route = useRoute();
/* true 系统字典 false  sime字典 */
let isSystem = computed(() => {
  return route.name == "Dict";
});

const activeName = ref("");
let tabsList = ref([]);

onActivated(() => {
  !isSystem.value && getData();
});
onMounted(() => {
  if (window.$wujie) {
    !isSystem.value && getData();
  }
});

/* 获取字典类型 */
const getData = () => {
  getDictsSime("config_dictionary_fieldAvailable").then(({ data }) => {
    if (!activeName.value) {
      activeName.value = data[0].dictValue;
    }
    tabsList.value = data;
  });
};
</script>
<style scoped lang="scss">
::v-deep .el-tabs--left .el-tabs__header.is-left {
  float: right;
}
</style>
