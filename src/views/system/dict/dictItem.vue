<template>
  <div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button size="small" @click="newlyAdded" class="search-button" v-hasPermi="'system:dict:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="apiObj.getTableData"
      @selection-change="handleSelectionChange"
      :default-params="{ fieldAvailable }"
    >
      <template #expand="{ row }">
        <xel-table
          ref="tableChildRef"
          class="gray-table"
          :columns="dictColumns"
          :load-data="apiObj.listData"
          :pagination="false"
          :default-params="{ pageSize: 200, pageNum: 1, dictType: row.dictType }"
          :checkbox="false"
        >
          <template #dictAction="scope">
            <xel-handle-btns :scope="scope" class="row-action" :btn-list="getDictgBtnList(row)"></xel-handle-btns>
          </template>
        </xel-table>
      </template>
      <template #action="scope">
        <xel-handle-btns :scope="scope" class="row-action" :btn-list="getBtnList(scope)"></xel-handle-btns>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
    <!-- 字典内容弹框 -->
    <xelDialog :title="dictionaryTitle" ref="dictionary" size="small" @submit="dictionarySubmit" @close="dictionaryReset">
      <el-form :model="formDictionary" ref="dictionaryRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formdictionary" :key="index" v-model="formDictionary[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </div>
</template>

<script>
export default {
  name: "dictItem",
};
</script>

<script setup>
import { useRoute } from "vue-router";
const route = useRoute();
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, computed, watch } from "vue";

/* true: 系统字典, false: 业务字典 */
let isSystem = computed(() => {
  return route.name == "Dict";
});
import {
  listType as getTableData,
  getType as getDetail,
  addType as addItem,
  updateType as updateItem,
  delType as delItem,
  clearCache,
  refreshCache,
} from "@/api/system/dict/type";
import {
  listType as getTableData2,
  getType as getDetail2,
  addType as addItem2,
  updateType as updateItem2,
  delType as delItem2,
  clearCache as clearCache2,
} from "@/api/sime/config/dict";

import { addData, listData, getData, updateData, delData } from "@/api/system/dict/data";
import {
  addData as addData2,
  listData as listData2,
  getData as getData2,
  updateData as updateData2,
  delData as delData2,
} from "@/api/sime/config/dict";

let props = defineProps({
  /* 参数 -需要默认传递的 */
  fieldAvailable: {
    type: String || Number,
    default() {
      return "";
    },
  },
});

let apiObj = {
  getTableData: isSystem.value ? getTableData : getTableData2,
  getDetail: isSystem.value ? getDetail : getDetail2,
  addItem: isSystem.value ? addItem : addItem2,
  updateItem: isSystem.value ? updateItem : updateItem2,
  delItem: isSystem.value ? delItem : delItem2,
  clearCache: isSystem.value ? clearCache : clearCache2,
  addData: isSystem.value ? addData : addData2,
  listData: isSystem.value ? listData : listData2,
  getData: isSystem.value ? getData : getData2,
  updateData: isSystem.value ? updateData : updateData2,
  delData: isSystem.value ? delData : delData2,
};

let dialogText = ref("字典类型");
let dictionaryText = ref("字典数据");
let idKey = "dictId";
let tableRef = ref();
let tableChildRef = ref();
let dictionary = ref();
let dictionaryRef = ref();
// 搜索配置
let searchState = reactive({
  data: {
    dictName: "",
    dictType: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态：",
      prop: "status",
      options: [
        { value: "1", label: "正常" },
        { value: "2", label: "停用" },
      ],
      // dictName: "sys_normal_disable",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "dictName",
      label: "字典名称",
    },
    {
      formType: "input",
      prop: "dictType",
      label: "字典类型",
    },
  ],
});
// 搜索按钮
function search(initPageNum = true) {
  let query = { ...searchState.data, fieldAvailable: props.fieldAvailable };
  tableRef.value && tableRef.value.reload(query, initPageNum);
}
// 重置按钮
function reset() {
  searchState.data = {
    dictName: "",
    dictType: "",
    pageNum: 1,
    pageSize: 10,
  };
  search();
}

let loading = ref(false);
let formData = reactive({
  dictName: "",
  dictType: "",
  status: "1",
  remark: "",
  dictId: null,
  fieldAvailable: props.fieldAvailable,
});
let formDictionary = reactive({
  dictType: "",
  dictLabel: "",
  status: "1",
  dictValue: "",
  dictSort: "0",
  remark: "",
  dictCode: "",
});
let state = reactive({
  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [
        { value: "1", label: "正常" },
        { value: "2", label: "停用" },
      ],
      // dictName: "sys_normal_disable",
    },
  ],
});
//列表配置项
const dictColumns = [
  {
    prop: "",
    label: "",
    width: "50px",
  },
  // {
  //   prop: "dictCode",
  //   label: "字典编码",
  // },
  {
    prop: "dictLabel",
    label: "字典标签",
  },
  {
    prop: "dictValue",
    label: "字典键值",
  },
  {
    prop: "dictSort",
    label: "字典排序",
  },

  {
    prop: "status",
    label: "状态",
    formatter(row, column) {
      let showVal = ref("");
      if (row.status == 1) {
        showVal = "正常";
      } else {
        showVal = "停用";
      }
      return showVal;
    },
  },
  {
    prop: "remark",
    label: "备注",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "dictAction",
  },
];
// 按钮
function getDictgBtnList(row) {
  return [
    {
      icon: "edit",
      title: "修改",
      hasPermi: "system:dict:edit",

      onClick(scope) {
        dictionaryTitle.value = "修改" + dictionaryText.value;
        change(scope.row.dictCode, row);
      },
    },
    {
      icon: "delete",
      title: "删除",
      /*hide: row.fieldAvailable == "0",*/
      hasPermi: "system:dict:remove",
      onClick(scope) {
        deleteData([scope.row.dictCode]);
      },
    },
  ];
}
// 列表配置项
const columns = [
  {
    type: "expand",
    slotName: "expand",
  },
  /*{
    prop: "dictId",
    label: "字典编号",
  },*/
  {
    prop: "dictName",
    label: "字典名称",
  },
  {
    prop: "dictType",
    label: "字典类型",
  },
  {
    prop: "status",
    label: "状态",
    formatter(row, column) {
      let showVal = ref("");
      if (row.status == 1) {
        showVal = "正常";
      } else {
        showVal = "停用";
      }
      return showVal;
    },
  },
  {
    prop: "remark",
    label: "备注",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    width: "180px",
    /*fixed: "right",*/
    slotName: "action",
    // btnList: getBtnList,
  },
];
function getBtnList(scope) {
  return [
    {
      icon: "plus",
      title: "新增",
      /*hide: scope.row.fieldAvailable == "0",*/
      onClick(scope) {
        newDictionary([scope.row.dictType]);
      },
    },
    {
      icon: "edit",
      title: "修改",
      onClick(scope) {
        dialogTitle.value = "修改" + dialogText.value;
        /*formList[0].isShow = scope.row.fieldAvailable != "0";
        formList[1].isShow = scope.row.fieldAvailable != "0";
        formList[2].isShow = scope.row.fieldAvailable != "0";
        formList[3].isShow = scope.row.fieldAvailable != "0";*/
        modifyButton(scope.row.dictId);
      },
    },
    {
      icon: "delete",
      title: "删除",
      /*hide: scope.row.fieldAvailable == "0",*/
      onClick(scope) {
        batchDelete([scope.row]);
      },
    },
  ];
}
/*if (!isSystem.value) {
  columns.splice(2, 0, {
    prop: "fieldAvailableText",
    label: "字段引用",
  });
}*/
// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  editId.value = "";
  formList[0].isShow = true;
  formList[1].isShow = true;
  formList[2].isShow = true;
  formList[3].isShow = true;
  popupBox();
}
let editId = ref("");
// 修改按钮
function modifyButton(id) {
  popupBox();
  editId.value = id;
  apiObj.getDetail(id).then((res) => {
    formData.dictName = res.data.dictName;
    formData.dictType = res.data.dictType;
    formData.status = res.data.status;
    formData.remark = res.data.remark;
    formData.dictId = res.data.dictId;
    formData.fieldAvailable = res.data.fieldAvailable;
  });
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 批量删除
function batchDelete(arr) {
  let rows = [];
  if (Array.isArray(arr)) {
    rows = arr;
  } else {
    rows = state.multipleSelection;
  }
  let ids = rows.map((item) => {
    return item[idKey];
  });
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    apiObj.delItem(ids.join(",")).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      tableRef.value.table.clearSelection();

      search(false);
    });
  });
}
// 清理缓存
function handleClearCache() {
  apiObj.clearCache().then((response) => {
    ElMessage({
      message: "清除成功",
      type: "success",
    });
  });
}
// 刷新缓存
function datarefreshCache() {
  refreshCache().then((res) => {
    console.log();
  });
}
// 弹框
let dialogTitle = ref("");
let dictionaryTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  dialog.value.open();
}

// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "dictName",
    label: "字典名称",
    required: true,
  },
  {
    formType: "input",
    prop: "dictType",
    label: "字典类型",
    required: true,
  },
  {
    formType: "radio",
    prop: "status",
    label: "状态",
    required: true,
    options: [
      { value: "1", label: "正常" },
      { value: "2", label: "停用" },
    ],
    // dictName: "sys_normal_disable",
  },
  {
    formType: "input",
    size: "mini",
    prop: "remark",
    label: "备注",
    type: "textarea",
  },
]);
/*if (!isSystem.value) {
  formList.splice(1, 0, {
    formType: "radio",
    prop: "fieldAvailable",
    label: "字段引用",
    dictName: "config_dictionary_fieldAvailable",
    sime: true,
    disabled: true,
  });
}*/

/* 监测 props.fieldAvailable 属性 */
watch(
  () => props.fieldAvailable,
  (val) => {
    if (val) {
      formData.fieldAvailable = props.fieldAvailable;
      search();
    }
  },
  {
    immediate: true,
  }
);

// 弹框确定按钮
// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading();
      let addFn = editId.value ? apiObj.updateItem : apiObj.addItem;
      addFn(formData)
        .then((res) => {
          close();

          ElMessage({
            message: "操作成功",
            type: "success",
          });
          search(false);
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  formData.dictName = "";
  formData.dictType = "";
  formData.status = "1";
  formData.remark = "";
  formData.dictId = "";
  formData.fieldAvailable = props.fieldAvailable;
  ruleForm.value.resetFields();
}
// 新增字典数据弹框
function newDictionary(ref) {
  judge.value = "";
  formDictionary.dictType = ref.join();
  dictionary.value.open();
  formdictionary[1].disabled = false;
  formdictionary[2].disabled = false;
  dictionaryTitle.value = "添加" + dictionaryText.value;
}
// 新增字典数据弹框内容
let formdictionary = reactive([
  {
    formType: "input",
    prop: "dictType",
    label: "字典类型",
    required: true,
    disabled: true,
  },
  {
    formType: "input",
    prop: "dictLabel",
    label: "字典标签",
    required: true,
  },
  {
    formType: "input",
    prop: "dictValue",
    label: "字典键值",
    required: true,
  },
  {
    formType: "number",
    size: "mini",
    required: true,
    prop: "dictSort",
    label: "显示排序",
    min: 0,
    max: 9999,
  },
  {
    formType: "radio",
    prop: "status",
    label: "状态",
    required: true,
    options: [
      { value: "1", label: "正常" },
      { value: "2", label: "停用" },
    ],
    // dictName: "sys_normal_disable",
  },
  {
    formType: "input",
    size: "mini",
    prop: "remark",
    label: "备注",
    type: "textarea",
  },
]);
// 新增字典数据提交
function dictionarySubmit(close, loading) {
  dictionaryRef.value.validate((valid) => {
    if (valid) {
      loading();
      let addFoel = judge.value ? apiObj.updateData : apiObj.addData;
      addFoel(formDictionary)
        .then((res) => {
          close();
          if (res.code === 200) {
            ElMessage({
              message: "操作成功",
              type: "success",
            });
            // tableChildRef.value.reload();
            search(false);
          } else {
            ElMessage.error("操作失败");
          }
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
// 字典数据表单重置
function dictionaryReset() {
  (formDictionary.dictType = ""),
    (formDictionary.dictLabel = ""),
    (formDictionary.status = "1"),
    (formDictionary.dictValue = ""),
    (formDictionary.dictSort = "0"),
    (formDictionary.remark = ""),
    (formDictionary.dictCode = ""),
    dictionaryRef.value.resetFields();
}
let judge = ref("");
// 修改按钮
function change(id, parentData) {
  judge.value = id;
  dictionary.value.open();
  apiObj.getData(id).then((res) => {
    /*formdictionary[1].disabled = parentData.fieldAvailable == "0";
    formdictionary[2].disabled = parentData.fieldAvailable == "0";*/
    (formDictionary.dictType = res.data.dictType),
      (formDictionary.dictLabel = res.data.dictLabel),
      (formDictionary.status = res.data.status),
      (formDictionary.dictValue = res.data.dictValue),
      (formDictionary.dictSort = res.data.dictSort),
      (formDictionary.remark = res.data.remark);
    formDictionary.dictCode = res.data.dictCode;
  });
}
// 删除按钮
function deleteData(id) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    apiObj.delData(id.join()).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      search(false);
    });
  });
}
</script>

<style scoped></style>
