<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <div class="pull-right margin-bottom20">
      <el-button size="small" @click="newlyAdded" class="search-button" v-hasPermi="'system:customer:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </div>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData"> </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "Customer",
};
</script>
<script setup>
import {
  listRole as getTableData,
  checkCustomerNo,
  viewDetails as getDetail,
  addDept as addItem,
  update as updateItem,
  delDept as delItem,
} from "@/api/system/customer";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let dialogText = ref("客户");
let idKey = "roleId";
let tableRef = ref();

let state = reactive({
  formData: {
    name: "",
    customerNo: "",
    trade: "",
    address: "",
    city: "",
    longitude: "",
    latitude: "",
    id: "",
  },

  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    customerNo: "",
    trade: "",
    address: "",
    city: "",
    longitude: "",
    latitude: "",
    id: "",
  };
}

// 列表配置项
const columns = [
  {
    prop: "name",
    label: "客户名称",
  },
  {
    prop: "customerNo",
    label: "客户编号",
  },
  {
    prop: "tradeText",
    label: "所属行业",
  },
  {
    prop: "address",
    label: "联系地址",
  },
  {
    prop: "city",
    label: "所在城市",
  },
  {
    prop: "longitude",
    label: "经度",
  },
  {
    prop: "latitude",
    label: "纬度",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        hasPermi: "system:customer:edit",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton([scope.row.id]);
        },
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "system:customer:delete",
        onClick(scope) {
          batchDelete([scope.row.id]);
        },
      },
    ],
  },
];

// 列表操作方法
// 新增按钮
function newlyAdded() {
  editId.value = "";
  dialogTitle.value = "添加" + dialogText.value;
  resetFormData();
  popupBox();
}
let editId = ref("");
// 修改按钮
function modifyButton(id) {
  editId.value = id;
  getDetail(id).then((res) => {
    state.formData.name = res.data.customer.name;
    state.formData.customerNo = Number(res.data.customer.customerNo);
    state.formData.trade = res.data.customer.trade;
    state.formData.address = res.data.customer.address;
    state.formData.city = res.data.customer.city;
    state.formData.longitude = res.data.customer.longitude;
    state.formData.latitude = res.data.customer.latitude;
    state.formData.id = res.data.customer.id;
    searchValue.id = res.data.customer.id;
    popupBox();
  });
}
// 列表多选
let multiples = ref(true);

// 删除
function batchDelete(id) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delItem(id.join()).then(() => {
      ElMessage.success("删除成功");
      tableRef.value.reload({}, false);
    });
  });
}

// 弹框
let dialogTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  dialog.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "客户名称",
    required: true,
  },
  {
    formType: "number",
    prop: "customerNo",
    label: "客户编号",
    required: true,
  },
  {
    formType: "select",
    prop: "trade",
    label: "所属行业",
    size: "mini",
    required: true,
    dictName: "custom_type",
  },
  {
    formType: "input",
    prop: "address",
    label: "联系地址",
    required: true,
  },
  {
    formType: "input",
    prop: "city",
    label: "所在城市",
    required: true,
  },
  {
    formType: "input",
    prop: "longitude",
    label: "经度",
    required: true,
  },
  {
    formType: "input",
    prop: "latitude",
    label: "纬度",
    required: true,
  },
]);
// 弹框确定按钮
// 验证条件
let searchValue = reactive({
  customerNo: "",
  id: "",
});

// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading();
      searchValue.customerNo = state.formData.customerNo;
      checkCustomerNo(searchValue).then((res) => {
        if (res.data.result) {
          let addFn = editId.value ? updateItem : addItem;
          addFn(state.formData)
            .then((res) => {
              ElMessage.success("操作成功");
              dialog.value.close();
              tableRef.value.reload();
              close();
            })
            .catch(() => {
              close(false);
            });
        } else {
          ElMessage.error("客户编号重复");
          close(false);
        }
      });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  ruleForm.value.resetFields();
}
</script>
<style scoped lang="scss"></style>
