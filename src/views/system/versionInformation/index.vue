<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <xel-table ref="table" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange" :checkbox="false"> </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "VersionInformation",
};
</script>
<script setup>
import { getVersion as getTableData } from "@/api/system/role";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";

let tableRef = ref();

let state = reactive({
  formData: {},

  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    resource: "",
  };
}
// 列表配置项
const columns = [
  {
    prop: "versionNo",
    label: "版本",
  },
  {
    prop: "upgradeTime",
    label: "升级时间",
  },
  {
    prop: "remark",
    label: "升级内容",
  },
];

let editId = ref("");
</script>
<style scoped lang="scss"></style>
