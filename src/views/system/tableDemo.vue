<template>
  <el-card>
    <el-form :model="searchData" ref="ruleForm" label-width="100px" label-position="left" size="mini" class="formWrapper">
      <!-- 普遍类型用一个就可以了，如果存在特殊表单项可单独设置 -->
      <xel-form-item
        v-for="(item, index) in formList"
        :key="index"
        v-model="searchData[item.prop]"
        v-bind="item"
        :required="item.required"
        :type="item.type"
        :formType="item.formType"
        :clearable="item.clearable"
        :multiple="item.multiple"
        :arr="item.arr"
        :prop="item.prop"
        :width="'240px'"
      ></xel-form-item>
      <xelQueryMenu ref="XelQueryMenuRef" :menuData="state.menuData" @selectValue="selectValue" :showAll="true" />
      <el-form-item>
        <el-button @click="search">查询</el-button>
        <el-button @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="">
      <div class="pull-left">功能角色管理</div>
      <div class="pull-right margin-bottom20">
        <el-tooltip content="新增" placement="top" effect="light" class="pull-left margin-top5">
          <el-button size="small">
            <el-icon :size="12">
              <plus />
            </el-icon>
            新增
          </el-button>
        </el-tooltip>
        <el-tooltip content="批量删除" placement="top" effect="light" class="pull-left margin-top5">
          <el-button size="small" @click="batchDelete">
            <el-icon :size="12">
              <delete-filled />
            </el-icon>
            批量删除
          </el-button>
        </el-tooltip>
        <el-input v-model="state.searchList.value" placeholder="请输入关键词搜索" class="pull-left margin-left20" style="width: 250px">
          <template #append>
            <el-button type="primary" icon="el-icon-search"></el-button>
          </template>
        </el-input>
        <div class="clearfix" />
      </div>
      <div class="clearfix" />
    </div>
    <xel-table
      ref="table"
      :columns="columns"
      :load-data="listMenu"
      :checkbox="false"
      @selection-change="handleSelectionChange"
      row-key="menuId"
      :pagination="false"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :handle-data="handleTree"
    >
      <template #tubiao="scope">
        {{ scope.row.icon }}
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="title" ref="dialog">
      <el-form ref="formDemo" :model="state.formDemo">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="state.formDemo.name" />
        </el-form-item>
      </el-form>
      <template v-slot:button>
        <el-button type="primary" @click="save">提交</el-button>
        <el-button type="danger" @click="closeDialog">关闭</el-button>
      </template>
    </xelDialog>
  </el-card>
</template>
<script setup>
import { listMenu } from "@/api/system/menu";
import { handleTree } from "@/utils/ruoyi";
import { getDicts } from "@/api/system/dict/data";
import { ref, reactive, onBeforeMount, onMounted, computed, toRefs } from "vue";
let title = ref("");
let dialog = ref();
function openDialog() {
  dialog.value.open();
}
function closeDialog() {
  dialog.value.cancel();
}
let state = reactive({
  formDemo: {
    name: "",
  },
  searchList: {
    value: "",
  },
  multipleSelection: [],
  menuData: [
    {
      lable: "状态：",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      // 字典方法
      dictName: "sys_normal_disable",
      // seleteCode: {
      //   code: getDicts,
      //   // 传递取值的字段名
      //   label: "",
      //   value: "",
      //   params: {},
      // },
    },
  ],
});
// 获取querMenu组件返回值
function selectValue(data) {
  if (data.id === -1) {
    searchData[data.parameter] = "";
  } else {
    searchData[data.parameter] = data.id;
  }
}
// 搜索条件
let searchData = reactive({
  menuName: "",
  status: "",
});
// 搜索表格内容
let table = ref();
let XelQueryMenuRef = ref();
function search(initPage = true) {
  table.value.reload(searchData, initPage);
}
function reset() {
  XelQueryMenuRef.value.closeAll();
  table.value.reload({});
}
let formList = reactive([
  {
    formType: "input",
    prop: "menuName",
    label: "菜单名称",
    size: "mini",
    required: false,
    type: "text",
  },
]);
function save() {
  console.info(state.formDemo);
  console.info(state.searchList);
}
// 列表多选
function handleSelectionChange(val) {
  state.multipleSelection = val;
}
function batchDelete() {
  console.info(state.multipleSelection);
}
const columns = [
  {
    prop: "menuName",
    label: "菜单名称",
    width: 160,
  },
  {
    prop: "icon",
    label: "图标",
    width: 100,
    slotName: "tubiao",
  },
  {
    prop: "orderNum",
    label: "排序",
    width: 60,
  },
  {
    prop: "perms",
    label: "权限标识",
  },
  {
    prop: "component",
    label: "组件路径",
  },
  {
    prop: "status",
    label: "状态",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    width: "270px",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick() {
          title.value = "修改";
          openDialog();
        },
      },
      {
        icon: "plus",
        title: "新增",
        onClick() {
          title.value = "新增";
          openDialog();
        },
      },
      {
        icon: "delete",
        title: "删除",
        onClick() {
          title.value = "删除";
          openDialog();
        },
      },
    ],
  },
];
function getList() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          rows: [
            {
              a: "dsadasdasdasdasdasd",
              b: 2,
              c: 3,
              d: "新建角色",
              e: "日期时间",
            },
          ],
          total: 1000,
        },
        msg: "查询成功",
      });
    });
  });
}
</script>
<style scoped lang="scss">
.tagClass {
  border-radius: $raduisM;
  width: 48px;
  height: 36px;
  background: #eef5ff;
  border-radius: $raduisM;
  border: 1px solid #c5daff;
  text-align: center;
  line-height: 36px;
  color: #63729d;
}
</style>
