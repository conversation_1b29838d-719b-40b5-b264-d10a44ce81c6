<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <div class="table-handler-btns">
      <el-button size="small" @click="newlyAdded" v-hasPermi="'system:role:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </div>

    <xel-table ref="table" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange">
      <template #status="scope">
        <el-switch v-model="scope.row.status" active-value="1" inactive-value="2" @change="handleStatusChange(scope.row)"></el-switch>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="state.formData" ref="ruleForm" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="state.formData[item.prop]" v-bind="item"></xel-form-item>
        <el-form-item label="菜单权限" class="role_menu_class">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-checkbox v-model="state.formData.menuCheckStrictly" @change="handleCheckedTreeConnect($event, 'menu')">父子联动</el-checkbox>
          <el-tree
            class="tree-border"
            :data="treeOptions.menuOptions"
            show-checkbox
            ref="menu"
            node-key="id"
            :check-strictly="!state.formData.menuCheckStrictly"
            empty-text="加载中，请稍后"
            :props="defaultProps"
          ></el-tree>
        </el-form-item>
        <xel-form-item
          label="备注"
          :required="false"
          v-model="state.formData.remark"
          prop="remark"
          formType="input"
          type="textarea"
          maxlength="500"
        ></xel-form-item>
      </el-form>
    </xelDialog>
    <!-- 分配数据权限 -->
    <xelDialog title="分配数据权限" ref="dialog_role_dept" size="small" @submit="submit_role_dept" @close="close_dept_dialog">
      <el-form :model="state.role_dept_form" ref="role_dept_form" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in role_dept_list" :key="index" v-model="state.role_dept_form[item.prop]" v-bind="item"></xel-form-item>
        <el-form-item label="数据权限" v-show="state.role_dept_form.dataScope === '2'" class="role_menu_class">
          <el-checkbox v-model="deptExpand" @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
          <el-checkbox v-model="deptNodeAll" @change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
          <el-checkbox v-model="state.role_dept_form.deptCheckStrictly" @change="handleCheckedTreeConnect($event, 'dept')">父子联动</el-checkbox>
          <el-tree
            class="tree-border"
            :data="treeOptions.deptOptions"
            show-checkbox
            ref="dept"
            node-key="id"
            :check-strictly="!state.role_dept_form.deptCheckStrictly"
            empty-text="加载中，请稍后"
            :props="defaultProps"
          ></el-tree>
        </el-form-item>
      </el-form>
    </xelDialog>
    <xelDialog title="编辑审核范围" ref="role_edit" size="large" @submit="submit_edit" @close="colse_edit">
      <roleEdit v-if="role_edit_show" ref="role_edit_menu" :roleId="state.roleId" @close="colse_edit" />
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "Role",
};
</script>
<script setup>
import {
  listRole as getTableData,
  getRole as getDetail,
  addRole as addItem,
  updateRole as updateItem,
  delRole as delItem,
  changeRoleStatus,
  dataScope,
} from "@/api/system/role";
import { treeselect as menuTreeselect, roleMenuTreeselect } from "@/api/system/menu";
import { treeselect as deptTreeselect, roleDeptTreeselect } from "@/api/system/dept";
import { download } from "@/utils/export";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, onMounted, nextTick } from "vue";
import { batchDelete } from "@/utils/delete";
import roleEdit from "./components/roleEdit.vue";
let dialogText = ref("角色");
let idKey = "roleId";
// 搜索条件
let searchState = reactive({
  data: {},
  menuData: [
    // {
    //   lable: "状态",
    //   prop: "status",
    //   options: [],
    //   dictName: "sys_normal_disable",
    // },
  ],
  formList: [
    // {
    //   formType: "input",
    //   prop: "roleName",
    //   label: "角色名称",
    // },
    // {
    //   formType: "input",
    //   prop: "roleKey",
    //   label: "权限字符",
    // },
  ],
});
let state = reactive({
  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      prop: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
  formData: {
    roleName: "",
    roleKey: "",
    roleType: "",
    roleSort: null,
    status: "1",
    menuCheckStrictly: true,
    menuIds: "",
  },
  role_dept_form: {
    roleName: "",
    roleKey: "",
    roleType: "",
    dataScope: "",
    deptCheckStrictly: true,
    deptIds: "",
  },
  roleId: "",
});
// 角色状态修改
function handleStatusChange(row) {
  let text = row.status === "2" ? "停用" : "启用";
  ElMessageBox.confirm("确认要" + text + '"' + row.roleName + '"角色吗?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(function () {
      return changeRoleStatus(row.roleId, row.status);
    })
    .then(() => {
      ElMessage({
        type: "success",
        message: text + "成功",
      });
    })
    .catch(function () {
      search(false);
    });
}
let queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  roleName: undefined,
  roleKey: undefined,
  status: undefined,
});
// 导出全部
function handleExport() {
  download("system/role/export", { ...queryParams }, `role_${new Date().getTime()}.xlsx`);
}
// 列表配置项
const columns = [
  {
    prop: "status",
    label: "启用/停用",
    slotName: "status",
  },
  {
    prop: "roleName",
    label: "角色名称",
  },
  {
    prop: "roleTypeText",
    label: "角色类型",
  },
  {
    prop: "roleKey",
    label: "权限字符",
  },
  {
    prop: "roleSort",
    label: "显示顺序",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    width: "240px",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        hasPermi: "system:role:edit",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton(scope.row.roleId);
        },
      },
      {
        icon: "circle-check-filled",
        title: "数据权限",
        onClick(scope) {
          handleDataScope(scope.row);
        },
      },
      {
        icon: "checked",
        title: "编辑审核范围",
        hasPermi: "system:role:edit",
        onClick(scope) {
          handleRoleEdit(scope.row);
        },
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "system:role:remove",
        onClick(scope) {
          batchDelete().then(() => {
            delItem(scope.row.roleId).then(() => {
              ElMessage({
                type: "success",
                message: "删除成功",
              });
              search(false);
            });
          });
        },
      },
    ],
  },
];

// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  editId.value = "";
  state.formData = {
    roleName: "",
    roleKey: "",
    roleType: "",
    roleSort: null,
    status: "1",
    menuCheckStrictly: true,
    menuIds: "",
  };
  getMenuTreeselect();
  popupBox();
}
let editId = ref("");
// 修改按钮
function modifyButton(id) {
  popupBox();
  editId.value = id;
  let roleMenu = getRoleMenuTreeselect(id);
  getDetail(id).then((res) => {
    state.formData = { ...res.data, roleSort: Number(res.data.roleSort) };
    roleMenu.then((response) => {
      let checkedKeys = response.checkedKeys;
      checkedKeys.forEach((v) => {
        menu.value.setChecked(v, true, false);
      });
    });
  });
}
// 分配数据权限
let dialog_role_dept = ref();
let role_dept_form = ref();
function handleDataScope(row) {
  let roleDeptTreeselect = getRoleDeptTreeselect(row.roleId);
  getDetail(row.roleId).then((response) => {
    state.role_dept_form = response.data;
    roleDeptTreeselect.then((res) => {
      dept.value.setCheckedKeys(res.checkedKeys);
    });
    dialog_role_dept.value.open();
    state.role_dept_form.deptCheckStrictly = true;
  });
}
/** 根据角色ID查询部门树结构 */
function getRoleDeptTreeselect(roleId) {
  return roleDeptTreeselect(roleId).then((response) => {
    treeOptions.deptOptions = response.depts;
    return response;
  });
}
// 保存分配数据
function submit_role_dept(close, loading) {
  if (state.role_dept_form.roleId != undefined) {
    loading();
    state.role_dept_form.deptIds = getDeptAllCheckedKeys();
    dataScope(state.role_dept_form)
      .then((response) => {
        close();
        ElMessage({
          type: "success",
          message: "操作成功",
        });

        search(false);
      })
      .catch((res) => {
        close(false);
      });
  }
}
// 关闭数据权限弹窗
function close_dept_dialog() {
  role_dept_form.value.resetFields();
}
// 审核范围
let role_edit = ref();
let role_edit_show = ref(false);
let role_edit_menu = ref();
function handleRoleEdit(row) {
  state.roleId = row.roleId;
  role_edit_show.value = true;
  role_edit.value.open();
  nextTick(() => {
    role_edit_menu.value.getEditList();
  });
}
function submit_edit() {
  role_edit_menu.value.submitEditForm();
}
function colse_edit() {
  role_edit.value.close();
  role_edit_show.value = false;
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 弹框
let dialogTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  menuNodeAll.value = false;
  menuExpand.value = false;
  dialog.value.open();
  state.formData.menuCheckStrictly = true;
}
// 弹框内容
// let formData = reactive();
let formList = reactive([
  {
    formType: "input",
    prop: "roleName",
    label: "角色名称",
    required: true,
  },
  {
    formType: "input",
    prop: "roleKey",
    label: "权限字符",
    required: true,
  },
  {
    formType: "select",
    prop: "roleType",
    label: "角色类型",
    required: true,
    options: [],
    dictName: "user_type",
  },
  {
    formType: "number",
    prop: "roleSort",
    label: "角色顺序",
    required: true,
    max: 999,
    vxRule: "IntPlus",
  },
  {
    formType: "radio",
    prop: "status",
    label: "状态",
    required: false,
    options: [],
    dictName: "sys_normal_disable",
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading();
      let addFn = editId.value !== "" ? updateItem : addItem;
      state.formData.menuIds = getMenuAllCheckedKeys();
      addFn(state.formData)
        .then((res) => {
          close();
          ElMessage({
            type: "success",
            message: "操作成功",
          });
          search(false);
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
// 表格内容重查
let table = ref();
function search(initPage = true) {
  table.value && table.value.reload(searchState.data, initPage);
}
// 重置
function reset() {
  table.value && table.value.reload({});
  searchState.data = {
    roleName: "",
    roleKey: "",
    status: "",
  };
}
//列表重置
function closeDialog() {
  state.formData.name = "";
  state.formData.resource = "";
  ruleForm.value.resetFields();
}
// 配置
let defaultProps = reactive({
  children: "children",
  label: "label",
});
let treeOptions = reactive({
  menuOptions: [],
  deptOptions: [],
});
let menu = ref();
let dept = ref();
let menuNodeAll = ref(false);
let menuExpand = ref(false);
let deptExpand = ref(false);
let deptNodeAll = ref(false);
// 查询树状结构
function getMenuTreeselect() {
  menuTreeselect().then((response) => {
    console.log(response.data);
    treeOptions.menuOptions = response.data;
  });
}

// 树权限（展开/折叠）
function handleCheckedTreeExpand(value, type) {
  if (type == "menu") {
    let treeList = treeOptions.menuOptions;
    for (let i = 0; i < treeList.length; i++) {
      menu.value.store.nodesMap[treeList[i].id].expanded = value;
    }
  } else if (type == "dept") {
    let treeList = treeOptions.deptOptions;
    for (let i = 0; i < treeList.length; i++) {
      dept.value.store.nodesMap[treeList[i].id].expanded = value;
    }
  }
}
// 树权限（全选/全不选）
function handleCheckedTreeNodeAll(value, type) {
  if (type == "menu") {
    menu.value.setCheckedNodes(value ? treeOptions.menuOptions : []);
  } else if (type == "dept") {
    dept.value.setCheckedNodes(value ? treeOptions.deptOptions : []);
  }
}
// 树权限（父子联动）
function handleCheckedTreeConnect(value, type) {
  if (type == "menu") {
    state.formData.menuCheckStrictly = value ? true : false;
  } else if (type == "dept") {
    state.role_dept_form.deptCheckStrictly = value ? true : false;
  }
}
// 所有菜单节点数据
// 所有菜单节点数据
function getMenuAllCheckedKeys() {
  // 目前被选中的菜单节点
  let checkedKeys = menu.value.getCheckedKeys();
  // 半选中的菜单节点
  let halfCheckedKeys = menu.value.getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  return checkedKeys;
}
function getDeptAllCheckedKeys() {
  // 目前被选中的部门节点
  let checkedKeys = dept.value.getCheckedKeys();
  // 半选中的部门节点
  let halfCheckedKeys = dept.value.getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  return checkedKeys;
}
// 根据角色ID查询树结构
function getRoleMenuTreeselect(roleId) {
  return roleMenuTreeselect(roleId).then((response) => {
    treeOptions.menuOptions = response.menus;
    return response;
  });
}
// 分配权限弹窗内容
let role_dept_list = reactive([
  {
    formType: "input",
    prop: "roleName",
    label: "角色名称",
    required: false,
    disabled: true,
  },
  {
    formType: "input",
    prop: "roleKey",
    label: "权限字符",
    required: false,
    disabled: true,
  },
  {
    formType: "select",
    prop: "roleType",
    label: "角色类型",
    required: false,
    options: [],
    dictName: "user_type",
  },
  {
    formType: "select",
    prop: "dataScope",
    label: "权限范围",
    required: false,
    options: [
      {
        value: "1",
        label: "全部数据权限",
      },
      {
        value: "2",
        label: "自定数据权限",
      },
      {
        value: "3",
        label: "本部门数据权限",
      },
      {
        value: "4",
        label: "本部门及以下数据权限",
      },
      {
        value: "5",
        label: "仅本人数据权限",
      },
    ],
  },
]);
</script>
<style scoped lang="scss">
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #ffffff none;
  border-radius: 4px;
}
</style>
