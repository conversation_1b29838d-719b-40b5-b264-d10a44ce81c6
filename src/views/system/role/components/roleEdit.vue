<template>
  <el-scrollbar style="height: 100%">
    <el-form ref="editFormRef" action="javascript:;" :model="state.editForm" :inline="true">
      <el-form-item label="关键字">
        <el-input size="mini" v-model="state.editForm.roleName" clearable style="width: 240px" placeholder="请输入关键字" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getEditList('search')">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row>
      <!-- 未选区 -->
      <el-col :span="10" class="tableScoll">
        <el-table style="width: 100%" :data="state.notinrowsData" @selection-change="leftSelect" height="60vh">
          <el-table-column type="selection" width="55" />
          <el-table-column :label="state.leftLable" prop="roleName" />
        </el-table>
      </el-col>

      <!-- 按钮区 -->
      <el-col :span="4" style="text-align: center; margin-top: 25%">
        <el-button @click="remLeftSelect" :disabled="leftBtnIsDIs" size="small" type="primary">
          <i class="el-icon-arrow-left"></i>
        </el-button>

        <el-button @click="addRightSelect" :disabled="rightBtnIsDIs" size="small" type="primary">
          <i class="el-icon-arrow-right"></i>
        </el-button>
      </el-col>

      <!-- 已选区 -->
      <el-col :span="10" class="tableScoll">
        <el-table style="width: 100%" :data="state.inrowsData" @selection-change="rightSelect" height="60vh">
          <el-table-column type="selection" width="55" />
          <el-table-column :label="state.rightLable" prop="roleName" />
          <el-table-column label="时间（h）">
            <template #default="scope">
              <el-input-number v-model="scope.row.timeScope" size="mini" controls-position="right" :min="0" :max="9999999" placeholder="请输入时间" />
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </el-scrollbar>
</template>

<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, onMounted, watch, onActivated } from "vue";
import { selectAuditRole, saveRoleAuditRel } from "@/api/system/role";
import { Delete } from "@element-plus/icons";
let props = defineProps({
  roleId: {
    type: String,
    default: "null",
  },
});
let state = reactive({
  editForm: {
    roleName: "",
  },
  notinrowsData: [],
  inrowsData: [],
  leftBtnIsDIs: true,
  rightBtnIsDIs: true,
  /*选择的数据*/
  leftData: [],
  rightData: [],
  leftLable: "已选择0条",
  rightLable: "已选择0条",
});
function getEditList(type) {
  state.editForm.roleId = props.roleId;
  selectAuditRole(state.editForm).then((res) => {
    // 符合条件的所有
    let resultAll = res.notinrows.concat(res.inrows);

    if (type !== "search") {
      state.inrowsData = res.inrows;
      state.inrowsData.forEach((item) => {
        item.timeScope = parseFloat(item.timeScope);
      });
    }
    console.log("resultAll: ", resultAll);
    state.notinrowsData = resultAll.filter((item) => !state.inrowsData.some((ele) => ele.roleId == item.roleId));
    console.log("state.inrowsData: ", state.inrowsData);
    console.log("state.notinrowsData: ", state.notinrowsData);
  });
}
/*向右 添加 */
function addRightSelect() {
  if (state.leftData.length > 0) {
    state.leftData.forEach((item) => {
      state.notinrowsData.splice(
        state.notinrowsData.findIndex((items) => items === item),
        1
      );
      item.timeScope = "";
      state.inrowsData.push(item);
    });
  } else {
    ElMessage.warning("请至少选择一条数据");
  }
}
/*向左 移除 */
function remLeftSelect() {
  if (state.rightData.length > 0) {
    state.rightData.forEach((item) => {
      state.inrowsData.splice(
        state.inrowsData.findIndex((items) => items === item),

        1
      );
      item.timeScope = "";
      state.notinrowsData.push(item);
    });
  } else {
    ElMessage.warning("请至少选择一条数据");
  }
}
function leftSelect(val) {
  val.length > 0 ? (state.rightBtnIsDIs = false) : (state.rightBtnIsDIs = true);
  state.leftData = val;
  state.leftLable = `已选择${val.length}条`;
}
function rightSelect(val) {
  val.length > 0 ? (state.leftBtnIsDIs = false) : (state.leftBtnIsDIs = true);
  state.rightData = val;
  state.rightLable = `已选择${val.length}条`;
}
// 保存
function submitEditForm() {
  let auditJson = [];
  state.inrowsData.forEach((item) => {
    auditJson.push({
      auditRoleId: item.roleId,
      timeScope: item.timeScope,
    });
  });
  let query = {
    roleId: props.roleId,
    auditJson: JSON.stringify(auditJson),
  };
  saveRoleAuditRel(query).then((res) => {
    if (res.code === 200) {
      emits("close");
      ElMessage({
        type: "success",
        message: "操作成功",
      });
    }
  });
}
defineExpose({
  submitEditForm,
  getEditList,
});
// 定义emit事件
const emits = defineEmits(["close"]);
</script>

<style lang="scss">
.tableScoll .el-table__body-wrapper::-webkit-scrollbar {
  width: 0px; // 横向滚动条
  height: 0px; // 纵向滚动条 必写
}
// 滚动条的滑块
.tableScoll .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}
.tableScoll .el-table__body-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #ddd transparent;
}
</style>
