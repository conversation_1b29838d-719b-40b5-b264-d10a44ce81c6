<template>
  <el-card>
    <!-- <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3> -->
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table ref="table" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange" :checkbox="false"> </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog title="操作日志详细" ref="dialog">
      <el-form ref="formRef" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作模块：">{{ form.title }} / </el-form-item>
            <el-form-item label="登录信息：">{{ form.operName }} / {{ form.operIp }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求地址：">{{ form.operUrl }}</el-form-item>
            <el-form-item label="请求方式：">{{ form.requestMethod }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="请求参数：">{{ form.operParam }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="返回参数：">{{ form.jsonResult }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作状态：">
              <div v-if="form.status === 0">正常</div>
              <div v-else-if="form.status === 1">失败</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="操作时间：">{{ parseTime(form.operTime) }}</el-form-item> -->
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常信息：" v-if="form.status === 1">{{ form.errorMsg }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #button>
        <el-button round @click="close" size="mini">关闭</el-button>
      </template>
    </xelDialog>
  </el-card>
</template>
<script setup>
import { list as getTableData } from "@/api/system/operlog";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let idKey = "roleId";
let tableRef = ref();
let searchState = reactive({
  data: {
    businessType: "",
    typeOptions: "",
    operName: "",
    title: "",
  },
  menuData: [
    {
      lable: "状态：",
      prop: "status",
      options: [],
      dictName: "sys_common_status",
    },
    {
      lable: "类型：",
      prop: "businessType",
      options: [],
      dictName: "sys_oper_type",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "系统模块",
    },
    {
      formType: "input",
      prop: "operName",
      label: "操作人员",
    },
  ],
});
// 列表配置项
const columns = [
  {
    prop: "operId",
    label: "日志编号",
  },
  {
    prop: "title",
    label: "系统模块",
  },
  {
    prop: "businessType",
    label: "操作类型",
  },
  {
    prop: "requestMethod",
    label: "请求方式",
  },
  {
    prop: "operName",
    label: "操作人员",
  },
  {
    prop: "operIp",
    label: "主机",
  },
  {
    prop: "statusText",
    label: "操作状态",
  },
  {
    prop: "operTime",
    label: "操作日期",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "More",
        title: "详细",
        onClick(scope) {
          batchDelete(scope.row, scope.index);
        },
      },
    ],
  },
];
//获取表格数据
let loading = ref(false);
getData();
function getData() {
  loading.value = true;
  getTableData(searchState.data).then(({ data }) => {
    // state.list = handleTree(data, "menuId");
    loading.value = false;
  });
}
function search(initPage = true) {
  getData();
}
function reset() {
  searchState.data = {
    businessType: "",
    typeOptions: "",
    operName: "",
    title: "",
  };
  getData();
}
// 弹框
let dialog = ref();
let state = reactive({ form: {} });
let { form } = toRefs(state);
let formRef = ref();
// 弹框
function batchDelete(row) {
  state.form = row;

  //   console.log(form);
  dialog.value.open();
}
//关闭按钮
function close() {
  dialog.value.close();
}
</script>
<style scoped lang="scss"></style>
