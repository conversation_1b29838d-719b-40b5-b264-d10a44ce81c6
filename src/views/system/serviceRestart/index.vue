<template>
  <el-card>
    <section class="button-top">
      <el-button :disabled="multipleSelection.length === 0" @click="getRestart(multipleSelection)" v-hasPermi="['system:services:operate']">
        重启服务
      </el-button>
    </section>
    <xel-table
      v-hasPermi="['system:services:list']"
      ref="tableRef"
      :columns="columns"
      :load-data="getServicesList"
      @selection-change="handleSelectionChange"
      :checkbox="true"
      :pagination="false"
      row-key="value"
    >
      <template #actionBtn="scope">
        <el-tooltip content="重启" placement="top">
          <el-button type="text" icon="Refresh" @click="getRestart([scope.row])" v-hasPermi="['system:services:operate']">重启</el-button>
        </el-tooltip>
      </template>
    </xel-table>
  </el-card>
</template>
<script setup lang="ts">
import { getServicesList, postServicesRestart } from "@/api/system/serviceRestart";
const { proxy } = getCurrentInstance();
const tableRef = ref<any>(null);
// 列表配置项
const columns = [
  {
    prop: "name",
    label: "服务名称",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtn",
    width: 120,
  },
];
function getRestart(rows) {
  postServicesRestart(rows).then(() => {
    proxy.$modal.msgSuccess("服务正在重启，请稍后...");
    tableRef.value.table.clearSelection();
  });
}
const multipleSelection = ref<any>([]);
function handleSelectionChange(val: any) {
  multipleSelection.value = val;
}
</script>
<style scoped lang="scss">
.button-top {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 18px;
}
</style>
