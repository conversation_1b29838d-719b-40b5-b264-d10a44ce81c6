<template>
  <!-- Form-Item 也具有size属性 -->
  <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini" class="formWrapper">
    <!-- 普遍类型用一个就可以了，如果存在特殊表单项可单独设置 -->
    <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item" :width="'240px'"></xel-form-item>
    <!-- 日期 date 单个写开始-结束 -->
    <xel-form-item prop="value1" label="时间范围选择" form-type="date" type="datetimerange" v-model="formData.value1"></xel-form-item>

    <!-- 是否显隐 -->
    <p v-show="isshow">显示和隐藏：aaa</p>

    <el-form-item>
      <el-button type="primary" @click="submitForm()"> 立即创建 </el-button>
      <el-button @click="resetForm()">重置</el-button>
    </el-form-item>
  </el-form>
  {{ formData }}
</template>
<script setup>
import { getDicts } from "@/api/system/dict/data";
import { ref, reactive, onMounted } from "vue";
import { getDepttree } from "@/api/system/dict/data";
// 每个表单项的配置，/
let formData = reactive({
  value1: [],
  name: "",
  region: "",
  resource: "",
  cType: [],
  password: "",
  desc: "",
  startDate: "", //开始日期
  endDate: "", //结束日期
  datetime: "", //type:datetime
  dateRange: "", //type:dataeRange
  datetimeRange: "",
  email: "",
  specialRadio: "", //特殊radio
  stree: "",
  mtree: [],
  txt: "",
  num: 0,
});

let ruleForm = ref();
// 提交
function submitForm() {
  ruleForm.value.validate((valid) => {
    if (valid) {
      alert("submit!");
    } else {
      console.log("error submit!!");
      return false;
    }
  });
}
function resetForm() {
  ruleForm.value.resetFields();
}
// 常规
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "姓名",
    size: "mini",
    required: true,
    type: "text",
  },
  {
    formType: "select",
    prop: "region",
    label: "区域",
    size: "mini",
    required: true,
    options: [], //字典自定义
    // 字典关键字
    dictName: "sys_normal_disable",
    // 调字典接口
    // seleteCode: {
    //   code: getDicts,
    //   // 传递取值的字段名
    //   label: "",
    //   value: "",
    //   params: {},
    // },
  },
  {
    formType: "radio",
    prop: "resource",
    label: "资源",
    size: "mini",
    required: true,
    options: [
      { value: "品牌商赞助", id: 1 },
      { value: "线下场地免费", id: 2 },
    ],
  },
  {
    formType: "checkbox",
    prop: "cType",
    label: "性质",
    size: "mini",
    required: true,
    options: [
      { value: "主题", id: 1 },
      { value: "品牌", id: 2 },
    ],
  },
  {
    formType: "input",
    prop: "card",
    label: "身份证号",
    size: "mini",
    required: true,
    vxRule: "IdCard", //自定义验证要和vaildataor.js名字相同
  },
  {
    formType: "input",
    prop: "email",
    label: "邮箱",
    size: "mini",
    required: true,
    exRule: "email", //element自带验证方法
  },
  {
    formType: "input",
    prop: "password",
    label: "密码",
    size: "mini",
    required: true,
  },
  {
    formType: "input",
    size: "mini",
    required: true,
    prop: "desc",
    label: "活动形式",
    type: "textarea",
    disabled: true,
  },
  {
    formType: "number",
    size: "mini",
    required: true,
    prop: "num",
    label: "计数器",
    max: 10,
    min: 0,
  },
]);
// 特殊表单项
let dateList = reactive([
  // 单个年月日
  {
    formType: "date",
    prop: "startDate",
    label: "开始日期",
  },
  {
    formType: "date",
    prop: "endDate",
    label: "结束日期",
  },
  // 单个日期时间
  {
    formType: "date",
    prop: "datetime",
    label: "单个日期时间",
  },
  {
    formType: "daterange",
    prop: "dateRange",
    label: "日期组合",
    size: "mini",
    required: true,
  },
  {
    formType: "daterange",
    prop: "datetimeRange",
    label: "日期时间组合",
    size: "mini",
    required: true,
  },
]);
let treeList = reactive([
  {
    formType: "tree",
    prop: "sTree",
    label: "单选树",
    size: "mini",
    required: true,
    dept: true,
  },
  {
    formType: "tree",
    prop: "mTree",
    label: "多选树",
    size: "mini",
    required: true,
    multiple: true,
    // 调接口
    treeOptions: {
      code: getDepttree,
      // 传递取值的字段名
      label: "",
      value: "",
      params: {},
    },
  },
]);
// 编缉器
let editorList = reactive({
  formType: "editor",
  prop: "txt",
  label: "编辑器",
  size: "mini",
  required: true,
  editorClass: "formEditor", //多个编辑器时，命名不同的名字
});
let radioList = reactive({
  formType: "radio",
  prop: "specialRadio",
  label: "特殊单选",
  size: "mini",
  required: true,
  options: [
    { value: "目录", id: 1 },
    { value: "菜单", id: 2 },
    { value: "按钮", id: 3 },
  ],
});
let isshow = ref(false);
function changeValue(data) {
  console.log(data);
}
// 编辑器比较特殊需要单独接收子组件传过来的值
function editorValue(val) {
  formData.txt = val;
}
</script>

<style lang="scss" scoped>
.formWrapper {
  margin-top: 200px;
}
</style>
