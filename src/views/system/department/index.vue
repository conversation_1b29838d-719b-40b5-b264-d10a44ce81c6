<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <div class="pull-right margin-bottom20">
      <el-button size="small" @click="newlyAdded" class="search-button" v-hasPermi="'system:dept:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </div>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange">
      <template #actionBtn="{ row }">
        <ul class="action-btns-ul">
          <li
            v-for="btn in columns[3].btnList"
            :key="btn.title"
            :disabled="btn.disabled ? btn.disabled : ''"
            :style="btn.icon == 'CircleCheck' ? (row.isDefault == '0' ? '' : 'background:#67c23a;color:#fff') : ''"
            @click="clickFn(btn, row)"
          >
            <el-tooltip
              :content="btn.icon == 'CircleCheck' ? (row.isDefault == '0' ? '设置默认' : '取消默认') : btn.title"
              placement="top"
              effect="light"
            >
              <el-icon v-if="btn.icon">
                <component :is="btn.icon" />
              </el-icon>
              <el-icon v-else>
                <svg class="icon" aria-hidden="true">
                  <use :xlink:href="'#' + btn.isFont"></use>
                </svg>
              </el-icon>
            </el-tooltip>
          </li>
        </ul>
      </template>
      <template #expand="{ row }">
        <xel-table class="gray-table" ref="tableChildRef" :columns="dictColumns" :data="row.children || []" :pagination="false" :checkbox="false">
          <template #actionBtn="{ row }">
            <ul class="action-btns-ul">
              <li
                v-for="btn in dictColumns[2].btnList"
                :key="btn.title"
                :disabled="btn.disabled ? btn.disabled : ''"
                :style="btn.icon == 'CircleCheck' ? (row.isDefault == '0' ? '' : 'background:#67c23a;color:#fff') : ''"
                @click="clickFn(btn, row)"
              >
                <el-tooltip
                  :content="btn.icon == 'CircleCheck' ? (row.isDefault == '0' ? '设置默认' : '取消默认') : btn.title"
                  placement="top"
                  effect="light"
                >
                  <el-icon v-if="btn.icon">
                    <component :is="btn.icon" />
                  </el-icon>
                  <el-icon v-else>
                    <svg class="icon" aria-hidden="true">
                      <use :xlink:href="'#' + btn.isFont"></use>
                    </svg>
                  </el-icon>
                </el-tooltip>
              </li>
            </ul>
          </template>
        </xel-table>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "Department",
};
</script>
<script setup>
import { listDept as getTableData, selectAllCustomer, saveDeptForm as addItem, deleteDept } from "@/api/system/department";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let dialogText = ref("部门");
let idKey = "roleId";
let tableRef = ref();
let tableChildRef = ref();
let dictionaryTitle = ref("");
function clickFn(btn, row) {
  btn.onClick(row);
}
// 重置表单项
let state = reactive({
  formData: {
    deptName: "",
    customerId: "",
    parentId: "0",
    deptId: "",
  },
  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    deptName: "",
    customerId: "",
    parentId: "0",
    deptId: "",
  };
}
let formDictionary = reactive({
  dictType: "",
});
// 列表配置项
const columns = [
  {
    type: "expand",
    slotName: "expand",
  },
  {
    prop: "deptName",
    label: "部门名称",
  },
  {
    prop: "customerName",
    label: "客户名称",
  },

  {
    label: "操作",
    fixed: "right",
    width: "250px",
    slotName: "actionBtn",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        hasPermi: "system:dept:edit",
        onClick(row) {
          dialogTitle.value = "修改" + dialogText.value;
          isChild.value = false;
          modifyButton(row);
        },
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "system:dept:remove",
        onClick(row) {
          batchDelete(row.deptId);
        },
      },
      {
        icon: "CircleCheck",
        title: "设置默认",
        onClick(row) {
          isChild.value = false;

          setDefault(row);
        },
      },
      {
        icon: "Plus",
        title: "添加子部门",
        onClick(row) {
          isChild.value = true;
          newDictionary(row);
        },
      },
    ],
  },
];

// 列表操作方法
// 新增按钮
function newlyAdded() {
  resetFormData();
  isChild.value = false;
  selectAllCustomer().then((res) => {
    if (res.data.flg === "error") {
      ElMessage({
        message: "无客户信息,请添加客户信息",
        type: "warning",
      });
    } else if (res.data.flg === "N") {
      dialogTitle.value = "添加" + dialogText.value;
      formList[0].isShow = false;
      state.formData.customerId = res.data.customerId;
      popupBox();
    } else if (res.data.flg === "Y") {
      formList[0].isShow = true;
      dialogTitle.value = "添加" + dialogText.value;
      popupBox();
    }
    state.formData.deptName = "";
  });
}
let isChild = ref(false);
// 修改按钮
function modifyButton(row, open = true) {
  console.log(row);
  state.formData.parentId = row.parentId;
  state.formData.deptName = row.deptName;
  state.formData.deptId = row.deptId;
  state.formData.isDefault = row.isDefault;
  if (open) {
    dialogTitle.value = "编辑" + dialogText.value;
    formList[0].isShow = false;
    popupBox();
  }
}
// 设置默认按钮
function setDefault(row) {
  modifyButton(row, false);
  state.formData.isDefault = row.isDefault == "0" ? "1" : "0";
  let params = state.formData;
  params.parentId = row.parentId;
  if (!isChild.value) {
    delete params.parentId;
  }
  addItem(params)
    .then((res) => {
      tableRef.value.reload();
      ElMessage.success("操作成功");
    })
    .catch(() => {});
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 批量删除
function batchDelete(arr) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteDept({ deptId: arr, status: "0" }).then(() => {
      ElMessage.success("删除成功");
      tableRef.value.reload();
      tableRef.value.table.clearSelection();
    });
  });
}
// 弹框
let dialogTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  dialog.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "select",
    prop: "customerId",
    label: "客户名称",
    size: "mini",
    isShow: false,
    required: true,
    filterable: true,
    options: [], //字典自定义
    // 字典关键字
    // 调字典接口
    seleteCode: {
      code: selectAllCustomer,
      label: "name",
      value: "id",
      resKey: "customerList",
    },
    onChange(val) {
      state.formData.customerId = val;
    },
  },
  {
    formType: "input",
    prop: "deptName",
    label: "部门名称",
    required: true,
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading();
      let addFn = addItem;
      let params = { ...state.formData };

      if (!isChild.value) {
        delete params.parentId;
      }
      addFn(params)
        .then((res) => {
          tableRef.value.reload();
          ElMessage.success("操作成功");
          close();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleForm.value.resetFields();
  });
  ruleForm.value.resetFields();
}
//子部门列表配置项
const dictColumns = [
  {
    prop: "",
    label: "",
    width: "50px",
  },
  {
    prop: "deptName",
    label: "部门名称",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtn",
    width: "250px",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick(row) {
          dictionaryTitle.value = "修改" + dialogText.value;
          isChild.value = true;
          modifyButton(row);
        },
      },
      {
        icon: "delete",
        title: "删除",
        onClick(row) {
          batchDelete(row.deptId);
        },
      },
      {
        icon: "CircleCheck",
        title: "设置默认",
        onClick(row) {
          isChild.value = true;
          setDefault(row);
        },
      },
    ],
  },
];
// 新增字典数据弹框
function newDictionary(ref) {
  formList[0].isShow = false;
  state.formData.parentId = ref.deptId;
  state.formData.customerId = ref.deptId;
  state.formData.deptName = "";
  state.formData.customerId = ref.customerId;
  state.formData.deptId = "";
  popupBox();
  dialogTitle.value = "添加子" + dialogText.value;
}
// 字典数据内容
let formdictionary = reactive([
  {
    formType: "input",
    prop: "dictType",
    label: "部门名称",
  },
]);
</script>
<style scoped lang="scss"></style>
