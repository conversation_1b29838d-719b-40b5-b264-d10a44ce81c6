<template>
  <el-card>
    <el-form :model="searchData" ref="ruleForm" label-width="100px" size="mini" class="formWrapper">
      <!-- 普遍类型用一个就可以了，如果存在特殊表单项可单独设置 -->
      <xel-form-item
        v-for="(item, index) in formList"
        :key="index"
        v-model="searchData[item.prop]"
        v-bind="item"
        :required="item.required"
        :type="item.type"
        :formType="item.formType"
        :clearable="item.clearable"
        :multiple="item.multiple"
        :arr="item.arr"
        :prop="item.prop"
        :width="'240px'"
      ></xel-form-item>
      <XelQueryMenu ref="XelQueryMenuRef" :menuData="state.menuData" @selectValue="selectValue" :showAll="false" />
    </el-form>
    <div class="">
      <div class="pull-left">功能角色管理</div>
      <div class="pull-right margin-bottom20">
        <el-tooltip content="新增" placement="top" effect="light" class="pull-left margin-top5">
          <el-button size="small">
            <el-icon :size="12">
              <plus />
            </el-icon>
            新增
          </el-button>
        </el-tooltip>
        <el-tooltip content="批量删除" placement="top" effect="light" class="pull-left margin-top5">
          <el-button size="small" @click="batchDelete">
            <el-icon :size="12">
              <delete-filled />
            </el-icon>
            批量删除
          </el-button>
        </el-tooltip>
        <el-input v-model="state.searchList.value" placeholder="请输入关键词搜索" class="pull-left margin-left20" style="width: 250px">
          <template #append>
            <el-button type="primary" icon="el-icon-search"></el-button>
          </template>
        </el-input>
        <div class="clearfix" />
      </div>
      <div class="clearfix" />
    </div>
    <xel-table
      ref="table"
      :columns="columns"
      :load-data="listMenu"
      :checkbox="false"
      @selection-change="handleSelectionChange"
      row-key="menuId"
      :pagination="false"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :handle-data="handleTree"
    >
      <template #tubiao="scope">
        {{ scope.row.icon }}
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="title" ref="dialog" width="600px">
      <el-form ref="formDemo" :model="state.formDemo">
        <xel-form-item type="input" required="true" v-model="state.formDemo.name" v-bind="nameValue"> </xel-form-item>
        <xel-form-item type="radio" :required="true" v-model="state.formDemo.specialRadio" v-bind="radioList"> </xel-form-item>
      </el-form>
      <template v-slot:button class="late">
        <el-button type="primary" @click="save" size="mini">提交</el-button>
        <el-button type="danger" @click="closeDialog" size="mini">关闭</el-button>
      </template>
    </xelDialog>
  </el-card>
</template>
<script setup>
import { listMenu } from "@/api/system/menu";
import { handleTree } from "@/utils/ruoyi";
import { ref, reactive, onBeforeMount, onMounted, computed, toRefs } from "vue";
let title = ref("");
let dialog = ref();
function openDialog() {
  dialog.value.open();
}
function closeDialog() {
  dialog.value.cancel();
}
let state = reactive({
  formDemo: {
    name: "",
    specialRadio: "",
  },
  searchList: {
    value: "",
  },
  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      data: [
        {
          dictLabel: "dictLabel",
          dictValue: "dictValue",
        },
      ],
    },
  ],
});
// 搜索条件
let searchData = reactive({
  name: "",
  menuName: "",
  status: "",
});
// 特殊单选
let formList = reactive([
  {
    formType: "input",
    prop: "menuName",
    label: "菜单名称",
    size: "mini",
    required: false,
    type: "text",
  },
]);
let nameValue = reactive({
  formType: "input",
  prop: "name",
  label: "上传菜单",
  size: "mini",
  required: false,
  type: "text",
});
function save() {
  console.info(state.formDemo);
  console.info(state.searchList);
}
// 列表多选
function handleSelectionChange(val) {
  state.multipleSelection = val;
}
function batchDelete() {
  console.info(state.multipleSelection);
}
const columns = [
  {
    prop: "menuName",
    label: "菜单名称",
    width: 160,
  },
  {
    prop: "icon",
    label: "图标",
    width: 100,
    slotName: "tubiao",
  },
  {
    prop: "orderNum",
    label: "排序",
    width: 60,
  },
  {
    prop: "perms",
    label: "权限标识",
  },
  {
    prop: "component",
    label: "组件路径",
  },
  {
    prop: "status",
    label: "状态",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    width: "270px",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick() {
          title.value = "修改";
          openDialog();
        },
      },
      {
        icon: "plus",
        title: "新增",
        onClick() {
          title.value = "新增";
          openDialog();
        },
      },
      {
        icon: "delete",
        title: "删除",
        onClick() {
          title.value = "删除";
          openDialog();
        },
      },
    ],
  },
];
function getList() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: {
          rows: [
            {
              a: "dsadasdasdasdasdasd",
              b: 2,
              c: 3,
              d: "新建角色",
              e: "日期时间",
            },
          ],
          total: 1000,
        },
        msg: "查询成功",
      });
    });
  });
}
let radioList = reactive({
  formType: "radio",
  prop: "specialRadio",
  label: "菜单类型",
  size: "mini",
  required: true,
  arr: [
    { value: "目录", id: 1 },
    { value: "菜单", id: 2 },
    { value: "按钮", id: 3 },
  ],
});
let dateList = reactive([
  {
    formType: "date",
    prop: "dateTime",
    label: "活动时间",
  },
  {
    formType: "input",
    prop: "email",
    label: "邮箱",
    size: "mini",
    required: true,
  },
]);
</script>
<style scoped lang="scss">
.tagClass {
  border-radius: $raduisM;
  width: 48px;
  height: 36px;
  background: #eef5ff;
  border-radius: $raduisM;
  border: 1px solid #c5daff;
  text-align: center;
  line-height: 36px;
  color: #63729d;
}
</style>
