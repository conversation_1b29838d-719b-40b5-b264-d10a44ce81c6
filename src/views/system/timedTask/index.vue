<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange" :pagination="false"> </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>

        <xel-form-item
          type="datetime"
          formType="time"
          v-model="formData.runTime"
          prop="beginTime"
          label="定时时间"
          :format="'HH:mm'"
          :value-format="'HH:mm'"
        ></xel-form-item>
        <!-- 开始时间 -->

        <xel-form-item
          type="datetime"
          formType="date"
          v-model="formData.beginTimeStr"
          v-bind="dateList[1]"
          value-format="YYYY-MM-DD HH:mm:ss"
        ></xel-form-item>

        <!-- 结束时间 -->
        <xel-form-item
          type="datetime"
          formType="date"
          v-model="formData.endTimeStr"
          v-bind="dateList[2]"
          :value-format="'YYYY-MM-DD HH:mm:ss'"
        ></xel-form-item>
        <xel-form-item v-for="(item, index) in formitem" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "TimedTask",
};
</script>
<script setup>
import { getTableData, getDetail, saveMttForm } from "@/api/param/mttd";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let dialogText = ref("定时任务");
let idKey = "roleId";
let tableRef = ref();

let state = reactive({
  formData: { id: "", runTime: "", beginTimeStr: "", endTimeStr: "", desc: "" },
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    id: "",
    runTime: "",
    beginTimeStr: "",
    endTimeStr: "",
    desc: "",
  };
}

// 列表配置项
const columns = [
  {
    prop: "id",
    label: "定时类型",
  },
  {
    prop: "runTime",
    label: "定时时间",
  },
  {
    prop: "beginTimeStr",
    label: "开始时间",
  },
  {
    prop: "endTimeStr",
    label: "结束时间",
  },
  {
    prop: "desc",
    label: "描述",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    hasPermi: "operation:matt:update",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton(scope.row.id);
        },
      },
    ],
  },
];
// 特殊表单项
let dateList = reactive([
  {
    formType: "date",
    prop: "runTime",
    label: "定时时间",
  },
  // 单个年月日
  {
    formType: "date",
    prop: "beginTimeStr",
    label: "开始时间",
  },
  {
    formType: "date",
    prop: "endTimeStr",
    label: "结束时间",
  },
  // 单个日期时间
]);
let editId = ref("");
// 修改按钮
function modifyButton(ids) {
  getDetail({ id: ids }).then((res) => {
    if (res.data.runTime.split(":").length == 2) {
      res.data.runTime = res.data.runTime + ":00";
    }
    (state.formData.id = res.data.id),
      (state.formData.runTime = res.data.runTime),
      (state.formData.beginTimeStr = res.data.beginTimeStr),
      (state.formData.endTimeStr = res.data.endTimeStr),
      (state.formData.desc = res.data.desc),
      popupBox();
  });
}

// 弹框
let dialogTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  dialog.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "id",
    label: " 定时类型",
    required: true,
    disabled: true,
  },
]);
let formitem = reactive([
  {
    formType: "input",
    size: "mini",
    required: true,
    prop: "desc",
    label: "描述",
    type: "textarea",
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading();
      let params = { ...state.formData };
      let runTimeArr = params.runTime.split(":");
      if (runTimeArr.length == 3) {
        runTimeArr.pop();

        params.runTime = runTimeArr.join(":");
      }

      saveMttForm(params)
        .then((res) => {
          close();
          dialog.value.close();
          ElMessage.success({
            type: "success",
            message: "操作成功",
          });
          tableRef.value.reload();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleForm.value.resetFields();
  });
  ruleForm.value.resetFields();
}
</script>
<style scoped lang="scss">
:deep .el-select {
  width: 100%;
}
</style>
