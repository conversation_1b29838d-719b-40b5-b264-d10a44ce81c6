<template>
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button @click="newlyAdded" class="search-button">
      <el-icon :size="12">
        <Download />
      </el-icon>
      批量下载
    </el-button>
  </common-search>
  <xel-table
    ref="internetRef"
    :columns="columns"
    :load-data="reportList"
    @selection-change="handleSelectionChange"
    :default-params="{ type: parameterTextObject[parameter] }"
    checkbox="true"
    row-key="reportFile"
  >
  </xel-table>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { reportCount, reportList, canDownLoad } from "@/api/system/report";
import { download } from "@/plugins/request";
let internetRef = ref();
let props = defineProps({
  parameter: {
    type: String,
    default: "",
  },
});

const parameterTextObject = {
  1: "日报",
  2: "周报",
  3: "月报",
  4: "季度报告",
  5: "阶段报告",
  6: "测试报告",
};
// 列表配置项
const columns = [
  {
    prop: "reportTime",
    label: "提交日期",
    sortable: "custom",
  },
  {
    prop: "reportName",
    label: "报告名称",
  },
  {
    prop: "beginTime",
    label: "报告覆盖时间范围",
    formatter(row) {
      return row.beginTime + " - " + row.endTime;
    },
  },
  {
    label: "操作",
    slotName: "actionBtns",
    btnsWidth: "100px",
    btnList: [
      {
        icon: "Download",
        title: "下载文件",
        onClick(scope) {
          romPortal(scope.row.reportFile);
        },
      },
    ],
  },
];
//搜索相关
let searchState = reactive({
  data: {
    reportName: "",
    createTimeStr: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "reportName",
      label: "报告名称",
    },
    {
      formType: "date",
      type: "daterange",
      prop: "createTimeStr",
      label: "创建日期",
    },
  ],
});
// 搜索方法
function search() {
  let params = { ...searchState.data, beginTimeStr: "", endTimeStr: "" };
  if (searchState.data.createTimeStr && searchState.data.createTimeStr.length > 0) {
    params.beginTimeStr = searchState.data.createTimeStr[0];
    params.endTimeStr = searchState.data.createTimeStr[1];
  }
  delete params.createTimeStr;
  internetRef.value.reload(params, true);
}
// 重置方法
function reset() {
  searchState.data = {
    reportName: "",
    createTimeStr: "",
  };
  internetRef.value.reload(searchState.data, true);
}
// 下载方法
function romPortal(val) {
  canDownLoad({ fileId: val }).then((res) => {
    let url = "/system/report/getFileFromPortal";
    download(url, "template.xlsx", { fileId: val }, "get");
  });
  // .catch((res) => {
  //   console.log(res);
  // });
}
//多选数据
let onAdd = ref([]);
function handleSelectionChange(val) {
  onAdd.value = val;
}
//批量下载
function newlyAdded() {
  let ids = onAdd.value.map((item) => {
    return item["reportFile"];
  });
  if (ids.length <= 0) {
    ElMessage({
      message: "请选择要下载的文件",
      type: "warning",
    });
    return false;
  } else {
    canDownLoad({ fileId: ids.join(",") })
      .then((res) => {
        let url = "/system/report/getFileFromPortal";
        download(url, "template.xlsx", { fileId: ids.join(",") }, "get");
        internetRef.value.reload(searchState.data, true);
      })
      .catch((res) => {
        if (res.code == 202) {
          let url = "/system/report/getFileFromPortal";
          download(url, "template.xlsx", { fileId: ids.join(",") }, "get");
          internetRef.value.reload(searchState.data, true);
        }
      });
  }
}
</script>
