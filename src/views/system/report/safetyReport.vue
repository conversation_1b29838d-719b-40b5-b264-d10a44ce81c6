<template>
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button @click="newlyAdded" class="search-button">
      <el-icon :size="12">
        <Download />
      </el-icon>
      批量下载
    </el-button>
    <el-button @click="newlyImport" class="search-button" v-if="IdAdd !== 3">
      <el-icon :size="12">
        <Download />
      </el-icon>
      导入
    </el-button>
  </common-search>
  <xel-table
    ref="internetRef"
    :columns="columns"
    :load-data="paradd"
    @selection-change="handleSelectionChange"
    :default-params="{ type: parameter }"
    checkbox="true"
    row-key="reportFile"
  >
  </xel-table>
  <!-- 导入弹框 -->
  <xel-dialog title="上传报告" ref="dialogRef" @submit="submitForm" @close="closeDialog">
    <el-form :model="state.formfile" ref="ruleFormRef" label-width="120px" size="mini" action="javascript:;">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="state.formfile[item.prop]" v-bind="item"></xel-form-item>
      <xel-form-item
        form-type="upload"
        prop="file"
        label="报告"
        :fileListFa="fileListFa"
        accept=".txt,  .doc,  .docx,  .pdf,   .xlsx ,   .xls "
        @fileList="changeFileList"
        :required="true"
      ></xel-form-item>
    </el-form>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import { exportReport, canDownLoad } from "@/api/system/report";
import { download } from "@/plugins/request";
import { ElMessageBox, ElMessage } from "element-plus";
let internetRef = ref();
let dialogRef = ref();
let ruleFormRef = ref();
let fileListFa = ref([]);
let props = defineProps({
  paradd: {
    type: String,
    default: "",
  },
  IdAdd: {
    type: String,
    default: "",
  },
});
let add = ref("");
if (props.IdAdd == 3) {
  add.value = 7;
} else if (props.IdAdd == 1) {
  add.value = 8;
} else if (props.IdAdd == 2) {
  add.value = 9;
}
// 列表配置项
const columns = [
  {
    prop: "createTime",
    label: "提交日期",
    sortable: "custom",
  },
  {
    prop: "reportName",
    label: "报告名称",
  },
  {
    label: "操作",
    slotName: "actionBtns",
    btnsWidth: "100px",
    btnList: [
      {
        icon: "Download",
        title: "下载文件",
        onClick(scope) {
          romPortal(scope.row.reportFile);
        },
      },
    ],
  },
];
//搜索相关
let searchState = reactive({
  data: {
    reportName: "",
    createTimeStr: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "reportName",
      label: "报告名称",
    },
  ],
});
// 搜索方法
function search() {
  internetRef.value.reload(searchState.data, true);
}
//多选数据
let onAdd = ref([]);
function handleSelectionChange(val) {
  onAdd.value = val;
}
// 重置方法
function reset() {
  searchState.data = {
    reportName: "",
  };
  internetRef.value.reload(searchState.data, true);
}
// 下载方法
function romPortal(val) {
  canDownLoad({ fileId: val, type: add.value }).then((res) => {
    let url = "/system/report/getFileFromPortal";
    download(url, "template.xlsx", { fileId: val, type: add.value }, "get");
  });
}

//批量下载
function newlyAdded() {
  let ids = onAdd.value.map((item) => {
    return item["reportFile"];
  });
  let url = "/system/report/getFileFromPortal";
  if (ids.length <= 0) {
    ElMessage({
      message: "请选择要下载的文件",
      type: "warning",
    });
    return false;
  } else {
    canDownLoad({ fileId: ids.join(","), type: add.value })
      .then((res) => {
        download(url, "template.xlsx", { fileId: ids.join(","), type: add.value }, "get");
        internetRef.value.reload(searchState.data, true);
      })
      .catch((res) => {
        if (res.code == 202) {
          download(url, "template.xlsx", { fileId: ids.join(","), type: add.value }, "get");
          internetRef.value.reload(searchState.data, true);
        }
      });
  }
}

let state = reactive({
  formfile: {
    name: "",
    file: "",
  },
});
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "报告名称",
    required: true,
    change: false,
  },
]);
function changeFileList(list) {
  if (list[0]) {
    state.formfile.file = list[0];
  } else {
    state.formfile.file = "";
  }
}
function newlyImport() {
  dialogRef.value.open();
}
function closeDialog() {
  (state.formfile.name = ""), (state.formfile.file = "");
  fileListFa.value = [];
  ruleFormRef.value.resetFields();
}
// 导入按钮
let emits = defineEmits(["transfer"]);
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let formData = new FormData();
      formData.append("reportName", state.formfile.name);
      formData.append("file", state.formfile.file.raw);
      formData.append("type", props.IdAdd);
      exportReport(formData).then((res) => {
        dialogRef.value.close();
        emits("transfer");
        closeDialog();
        internetRef.value.reload(searchState.data, true);
        ElMessage({
          message: res.msg,
          type: "success",
        });
      });
    }
  });
}
</script>
