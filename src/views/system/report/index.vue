<template>
  <el-card class="head report-wrapper">
    <el-row :gutter="24">
      <el-col :span="2">
        <div class="tap-tubiao">
          <el-icon size="37">
            <tickets />
          </el-icon>
        </div>
      </el-col>
      <el-col :span="22">
        <el-row :gutter="24">
          <el-col :span="4">
            <p class="tap-title">当前服务期内报告总数</p>
            <p class="tap-number">{{ overview.currentReportCount }}</p>
          </el-col>
          <el-col :span="20" class="report-items">
            <el-row :gutter="24" class="margin-bottom20">
              <el-col :span="5"
                ><span class="spanName">安全运营季度报告</span><span class="ziti">{{ overview.quarterlyReport }}</span></el-col
              >
              <el-col :span="5"
                ><span class="spanName">安全运营月报</span><span class="ziti">{{ overview.monthlyReport }}</span></el-col
              >
              <el-col :span="5"
                ><span class="spanName">安全运营周报</span><span class="ziti">{{ overview.weeklyReport }}</span></el-col
              >
              <el-col :span="5"
                ><span class="spanName">安全运营日报</span><span class="ziti">{{ overview.dailyReport }}</span></el-col
              >
              <el-col :span="4"
                ><span class="spanName">安全运营阶段报告</span><span class="ziti">{{ overview.operationalReports }}</span></el-col
              >
            </el-row>
            <el-row :gutter="24" class="report-items">
              <el-col :span="5"
                ><span class="spanName">安全测试报告</span><span class="ziti">{{ overview.testReport }}</span></el-col
              >
              <el-col :span="5"
                ><span class="spanName">事件报告</span><span class="ziti">{{ overview.eventReport }}</span></el-col
              >
              <el-col :span="5"
                ><span class="spanName">安全敏感信息泄露报告</span><span class="ziti">{{ overview.leakTaskReport }}</span></el-col
              >
              <el-col :span="5"
                ><span class="spanName">安全意识隐患报告</span><span class="ziti">{{ overview.safetyTaskReport }}</span></el-col
              >
            </el-row>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <div>
          <p class="tap-title">历史累计报告总数</p>
          <p class="tap-number">{{ overview.historicalReport }}</p>
        </div>
      </el-col>
    </el-row>
  </el-card>
  <!--报告  -->
  <el-card>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="安全测试报告" name="first" :lazy="true"><collection :parameter="6"></collection></el-tab-pane>
      <el-tab-pane label="安全运营季度报告" name="second" :lazy="true"><collection :parameter="4"></collection></el-tab-pane>
      <el-tab-pane label="安全运营月报" name="third" :lazy="true"><collection :parameter="3"></collection></el-tab-pane>
      <el-tab-pane label="安全运营周报" name="fourth1" :lazy="true"><collection :parameter="2"></collection></el-tab-pane>
      <el-tab-pane label="安全运营日报" name="fourth2" :lazy="true"><collection :parameter="1"></collection></el-tab-pane>
      <el-tab-pane label="安全运营阶段报告" name="fourth3" :lazy="true"><collection :parameter="5"></collection></el-tab-pane>
      <el-tab-pane label="事件报告" name="fourth4" :lazy="true"><safetyReport :paradd="getEvent" :IdAdd="3"></safetyReport></el-tab-pane>
      <el-tab-pane label="安全意识隐患报告" name="fourth5" :lazy="true"
        ><safetyReport @transfer="openTransfer" :paradd="getSafety" :IdAdd="1"></safetyReport
      ></el-tab-pane>
      <el-tab-pane label="安全敏感信息泄露报告" name="fourth6" :lazy="true"
        ><safetyReport :paradd="getLeakage" @transfer="openTransfer" :IdAdd="2"></safetyReport
      ></el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "ReportList",
};
</script>
<script setup>
import { reportCount, reportList, getEvent, getLeakage, getSafety } from "@/api/system/report";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import Collection from "./collection.vue";
import SafetyReport from "./safetyReport.vue";

let idKey = "roleId";
let tableRef = ref();
let activeName = ref("first");
let searchState = reactive({
  data: {
    title: "",
    operName: "",
    pageNum: 1,
    pageSize: 10,
  },
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "报告名称",
    },
    {
      formType: "input",
      prop: "operName",
      label: "操作人员",
    },
  ],
});
const emit = defineEmits(["getData"]);
//获取表格数据
let loading = ref(false);
let overview = ref({});
getData();
function getData() {
  reportCount().then((res) => {
    overview.value = res.data;
  });
}
function openTransfer() {
  getData();
}
function search(initPage = true) {
  getData();
}
function reset() {
  searchState.data = {
    title: "",
    operName: "",
    pageNum: 1,
    pageSize: 10,
  };
  getData();
}
// 弹框
let dialog = ref();
let state = reactive({ form: {} });
let { form } = toRefs(state);
let formRef = ref();
// 弹框
function batchDelete(row) {
  state.form = row;
  dialog.value.open();
}
//关闭按钮
function close() {
  dialog.value.close();
}
</script>
<style scoped lang="scss">
.head {
  margin-bottom: 20px;
  width: 100%;
}
.spanName {
  color: $fontColorSoft;
  font-size: 14px;
  font-weight: 300;
  min-width: 130px;
  max-width: 140px;
  display: inline-block;
}
.ziti {
  color: #28334f;
  font-size: 24px;
  font-weight: 600;
  margin-left: 2px;
  position: relative;
  top: 3px;
}
</style>
