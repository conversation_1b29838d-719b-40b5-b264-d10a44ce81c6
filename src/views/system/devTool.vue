<template>
  <el-card>
    <el-form ref="form" :model="form" label-width="120px">
      <el-button type="success" round>添加</el-button>
      <el-form-item label="prop">
        <el-input v-model="form.prop"></el-input>
      </el-form-item>
      <el-form-item label="label">
        <el-input v-model="form.label"></el-input>
      </el-form-item>
    </el-form>
  </el-card>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
let form = reactive({
  prop: "",
  label: "",
});
</script>

<style lang="scss" scoped></style>
