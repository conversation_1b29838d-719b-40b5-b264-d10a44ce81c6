<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="search" @reset="reset"> </common-search>
    <xel-table
      ref="table"
      :columns="isSiem ? siemColumns : columns"
      :load-data="isSiem ? getSysUserSendTypes : getTableData"
      :checkbox="false"
      :pagination="!isSiem"
    >
      <!-- 成员账号 -->
      <template #loginCode="scope">
        {{ scope.row.sysUser.userName }}
      </template>

      <!-- 姓名 -->
      <template #name="scope">
        {{ scope.row.sysUser.nickName }}
      </template>

      <!-- 手机号 -->
      <template #mobilePhoneNo="scope">
        {{ scope.row.sysUser.phonenumber }}
      </template>

      <!-- 邮箱地址 -->
      <template #email="scope">
        {{ scope.row.sysUser.email }}
      </template>

      <template #status="scope">
        <el-checkbox
          :disabled="!hasPermi('system:sendType:changeSendStatus')"
          v-model="scope.row.wechatStatus"
          true-label="1"
          false-label="0"
          @change="changeSendType('wechatStatus', scope.row)"
          >微信</el-checkbox
        >
        <el-checkbox
          :disabled="!hasPermi('system:sendType:changeSendStatus')"
          v-model="scope.row.phoneStatus"
          true-label="1"
          false-label="0"
          @change="changeSendType('phoneStatus', scope.row)"
          >短信</el-checkbox
        >
        <el-checkbox
          :disabled="!hasPermi('system:sendType:changeSendStatus')"
          v-model="scope.row.emailStatus"
          true-label="1"
          false-label="0"
          @change="changeSendType('emailStatus', scope.row)"
          >邮箱</el-checkbox
        >
        <el-checkbox
          :disabled="!hasPermi('system:sendType:changeSendStatus')"
          v-model="scope.row.voiceStatus"
          true-label="1"
          false-label="0"
          @change="changeSendType('voiceStatus', scope.row)"
          >电话</el-checkbox
        >
      </template>
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "SendTypemain",
};
</script>
<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { getSendTypePage as getTableData, changeSend, getSysUserSendTypes, changeSendStatusSiem } from "@/api/system/license";
import hasPermi from "@/utils/hasPermi.js";

/* 是否是 siem */
const isSiem = import.meta.env.VITE_IS_SIME;

// 查询条件
let searchState = reactive({
  data: {
    searchValue: "",
    sort: "",
  },
  formList: [
    {
      formType: "input",
      prop: "searchValue",
      label: "查询内容",
    },
  ],
});
let table = ref();
// 搜索
function search(initPage = true) {
  table.value && table.value.reload(searchState.data, initPage);
}
// 重置
function reset() {
  table.value && table.value.reload();
  searchState.data = {
    searchValue: "",
    sort: "",
  };
}
// 表格
const columns = [
  {
    prop: "loginCode",
    label: "成员账号",
  },
  {
    prop: "name",
    label: "姓名",
  },
  {
    prop: "mobilePhoneNo",
    label: "手机号",
  },
  {
    prop: "email",
    label: "邮箱地址",
  },
  {
    prop: "status",
    label: "发送类型",
    slotName: "status",
    width: "400px",
  },
];

/* siem - 表格配置 */
const siemColumns = [
  {
    prop: "loginCode",
    label: "成员账号",
    slotName: "loginCode",
  },
  {
    prop: "name",
    label: "姓名",
    slotName: "name",
  },
  {
    prop: "mobilePhoneNo",
    label: "手机号",
    slotName: "mobilePhoneNo",
  },
  {
    prop: "email",
    label: "邮箱地址",
    slotName: "email",
  },
  {
    prop: "status",
    label: "发送类型",
    slotName: "status",
    width: "400px",
  },
];

/*改变通知发送类型*/
function changeSendType(type, scope) {
  /* 新增 - siem与前哨共用 通知 */
  const query = {
    id: isSiem ? "" : scope.id,
    userId: isSiem ? scope.userId : scope.spare1,
  };

  if (type === "wechatStatus") {
    query.wechatStatus = scope.wechatStatus;
  } else if (type === "phoneStatus") {
    query.phoneStatus = scope.phoneStatus;
  } else if (type === "emailStatus") {
    query.emailStatus = scope.emailStatus;
  } else if (type === "voiceStatus") {
    query.voiceStatus = scope.voiceStatus;
  }

  const API = isSiem ? changeSendStatusSiem : changeSend;
  API(query)
    .then((res) => {
      ElMessage({
        type: "success",
        message: "修改成功",
      });
    })
    .finally(() => {
      search(false);
    });
}
</script>

<style lang="scss" scoped></style>
