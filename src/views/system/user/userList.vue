<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      labelWidth="5em"
      @search="search"
      @reset="reset"
    >
      <el-button size="small" @click="newlyAdded" v-hasPermi="'system:user:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
      <xel-upload-dialog
        exportUrl="/system/user/downloadUserTemplate"
        importUrl="/system/user/importUserTemplate"
        v-hasPermi="'system:user:import'"
        @updateData="search"
      ></xel-upload-dialog>
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData">
      <template #status="scope">
        <el-switch v-model="scope.row.status" active-value="1" inactive-value="2" @change="handleStatusChange(scope.row)"></el-switch>
      </template>
      <template #icon="scope">
        <img v-if="scope.row.photoId" class="column-avatar" :src="'/outpost-api/system/file/getFile?fileId=' + scope.row.photoId" alt="" />
        <img v-else class="column-avatar" src="@/assets/imgs/default.png" alt="头像" />
      </template>
    </xel-table>
    <AddEditUser v-if="isshowAddEdit" :dialogTitle="dialogTitle" :edit-id="editData.id" :editInfo="editData.info" @closeDialog="closeDialog" />
    <!-- <ResetPwd v-if="isshowpsd" :psdTitle="psdTitle" @closeDialog="closeDialog" /> -->
    <xel-dialog ref="resetPsdRef" size="small" title="重置密码" @submit="resetPsdSubmit">
      <p class="margin-bottom10">请输入" {{ editData.info.userName }} "的新密码</p>
      <el-form ref="psdFormRef" :model="psdForm">
        <el-form-item prop="psdReset" :rules="passRule">
          <el-input v-model="psdForm.psdReset"></el-input>
        </el-form-item>
      </el-form>
    </xel-dialog>
    <xel-dialog ref="delGuestRef" size="small" @submit="delGuest" top="30vh" title="警告">
      <div class="del-warning-title">
        <el-icon size="24"><WarningFilled /></el-icon> 确认删除选中的数据项？
        <el-checkbox class="isSossDel" v-model="isSossDel" :true-label="2" :false-label="0">是否一并删除SOSS上的客户联系人</el-checkbox>
      </div>
    </xel-dialog>

    <!-- 日志权限过滤器配置     -->
    <LogPermissionDia ref="LogPermissionDiaRef" @close="getList" />
  </el-card>
</template>
<script>
export default {
  name: "UserList",
};
</script>
<script setup>
import {
  getUserList as getTableData,
  getuserForm as getDetail,
  deleteUser as delItem,
  changeStatus,
  resetWrongPasswordCount,
  resetPwd,
} from "@/api/system/userList";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive } from "vue";
import AddEditUser from "./components/addEditUser.vue";
import ResetPwd from "./components/resetPwd.vue";
import { batchDelete } from "@/utils/delete";
import { ciiEncrypt } from "@/utils/jsencrypt";
import { validatePass } from "@/utils/passwordValid.js";

import LogPermissionDia from "@/views/sime/system/user/logPermissionDia.vue";
const LogPermissionDiaRef = ref();

const passRule = [
  { required: true, trigger: "blur", message: "请输入密码" },
  { validator: (a, b, c) => validatePass(a, b, c, true), trigger: "blur" },
];
// 搜索条件
let searchState = reactive({
  data: {
    searchValue: "",
  },
  formList: [
    {
      formType: "input",
      prop: "searchValue",
      label: "关键字",
    },
  ],
});
// 表格内容重查
let tableRef = ref();
function search(initPage = true) {
  tableRef.value && tableRef.value.reload(searchState.data, initPage);
}
function reset() {
  searchState.data = {
    searchValue: "",
  };
  search();
}
let imgIcon = ref("");
let idKey = "roleId";
// 列表配置项
const columns = [
  {
    prop: "status",
    label: "",
    slotName: "status",
    width: "80px",
  },
  {
    prop: "userName",
    label: "成员账号",
  },
  {
    prop: "nickName",
    label: "成员姓名",
  },
  {
    prop: "email",
    label: "邮箱地址",
    width: "140px",
  },
  {
    prop: "phonenumber",
    label: "手机号码",
    width: "140px",
  },
  {
    prop: "dataRoleName",
    label: "功能权限",
    width: "120px",
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: "160px",
  },
  {
    prop: "loginDate",
    label: "最后登录",
    width: "160px",
  },
  {
    prop: "loginDate",
    label: "头像",
    slotName: "icon",
    width: "100px",
  },
  {
    prop: "userTypeText",
    label: "用户类型",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton(scope.row);
        },
        hasPermi: "system:user:update",
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "system:user:remove",
        onClick(scope) {
          activeGuest.value = null;
          if (scope.row.userType == 2) {
            isSossDel.value = 0;
            delGuestRef.value.open(() => {
              activeGuest.value = scope.row;
            });
          } else {
            batchDelete().then(() => {
              delItem({ userId: scope.row.userId }).then(() => {
                ElMessage({
                  type: "success",
                  message: "删除成功",
                });
                search(false);
              });
            });
          }
        },
      },
      {
        icon: "Refresh",
        title: "重置密码",
        hasPermi: "system:user:resetPwd",
        onClick(scope) {
          psdForm.psdReset = "";
          modifyButton(scope.row, true);
          resetPsdRef.value.open();
        },
      },
      {
        icon: "Unlock",
        title: "解锁",
        onClick(scope) {
          recoverUser(scope.row);
        },
      },
      {
        icon: "Setting",
        title: "日志权限",
        hasPermi: ["system:user:edit", "system:user:add"],
        onClick(scope) {
          LogPermissionDiaRef.value.open(scope.row);
        },
      },
    ],
  },
];
// 锁定
function handleStatusChange(row) {
  //1 正常 2 停用 0 删除
  let prevIsLock = row.status == "1" ? "2" : "1";
  let text = prevIsLock == "2" ? "启用" : "停用";
  ElMessageBox.confirm("确认要 " + text + ' "' + row.userName + '"账号吗?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let params = {
        userId: row.userId,
        status: prevIsLock == "2" ? "1" : "2",
      };
      changeStatus(params).then(() => {
        ElMessage({
          type: "success",
          message: text + "成功",
        });
        search(false);
      });
    })
    .catch(function () {
      row.status = row.status == "2" ? "1" : "2";
    });
}
//解冻
function recoverUser(row) {
  resetWrongPasswordCount({ userId: row.userId }).then((res) => {
    ElMessage.success("操作成功");
    search(false);
  });
}
// 列表操作方法
let dialogText = ref("用户");
// 新增按钮
// 新增-编辑弹框
let dialogTitle = ref("");
// 控制新增-编辑
let isshowAddEdit = ref(false);
let editData = reactive({
  id: "",
  info: {},
});
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  editData.id = "";
  editData.info = {};
  isshowAddEdit.value = true;
}

// 修改按钮
function modifyButton(row, isReset = false) {
  editData.id = row.userId;
  editData.info = JSON.parse(JSON.stringify(row));
  if (!isReset) {
    isshowAddEdit.value = true;
  }
}

// 重置密码
let resetPsdRef = ref();
let psdForm = reactive({
  psdReset: "",
});
let psdFormRef = ref();
function resetPsdSubmit(close, load) {
  if (!psdForm.psdReset) {
    ElMessage.warning("请输入新密码");
    return;
  }

  psdFormRef.value.validate((valid) => {
    if (!valid) return;
    load();

    resetPwd({ userId: editData.id, password: ciiEncrypt(psdForm.psdReset) })
      .then(() => {
        ElMessage.success("重置成功");
        close();
      })
      .catch(() => {
        close(false);
      });
  });
}

// let isshowpsd = ref(false);
// function resetPwd(row) {
//   isshowpsd.value = true;
// }

// 关闭子组件弹框
function closeDialog(val, reload = true) {
  if (val == "addEdit") {
    isshowAddEdit.value = false;
  }
  reload && search(false);
}

let delGuestRef = ref();
let isSossDel = ref(0);
let activeGuest = ref(null);
function delGuest() {
  delItem({ userId: activeGuest.value.userId, isSossDel: isSossDel.value }).then(() => {
    ElMessage({
      type: "success",
      message: "删除成功",
    });
    delGuestRef.value.close();
    search(false);
  });
}
</script>
<style scoped lang="scss">
.column-avatar {
  width: 30px;
  height: 30px;
  border-radius: 100%;
}
.del-warning-title {
  i {
    color: $color;
    vertical-align: middle;
  }

  font-size: 16px;

  text-align: center;
}
.isSossDel {
  margin-left: 30px;
  display: block;
  margin-top: 10px;
}
</style>
