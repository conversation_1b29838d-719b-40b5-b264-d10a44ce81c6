<template>
  <xelDialog :title="dialogTitle" ref="dialogRef" @submit="submitForm" @close="closeDialog(false)">
    <el-form :model="formData" ref="ruleForm" label-width="100px">
      <xel-upload-img
        v-if="props.editId"
        class="avatar-img"
        ref="uploadAvatarRef"
        formType="upload"
        prop="avatar"
        label="头像"
        size="60px"
        :action="avatarUrl"
        :data="{ userId: props.editId }"
        name="avatarfile"
        v-model="avatarSrc"
        :default-src="getImageUrl()"
        :round="true"
      ></xel-upload-img>
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      <xel-form-item
        v-if="state.formData.userType == 3"
        formType="input"
        prop="companyName"
        label="公司名称"
        v-model="formData.companyName"
        :required="true"
      ></xel-form-item>
      <!-- disabled-key="disabled" -->

      <xel-form-item
        v-if="state.formData.userType == 2"
        form-type="tree"
        v-model="formData.deptIds"
        :tree-data="deptState.deptData"
        prop="deptIds"
        label="部门名称"
        :required="true"
      >
      </xel-form-item>
      <xel-form-item v-for="(item, index) in formList2" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xelDialog>
</template>
<script setup>
import {
  getuserForm as getDetail,
  addUserList as addItem,
  editUserList as updateItem,
  deleteUser as delItem,
  getuserRole as getRoleDict,
  getRoleInfoById,
  avatarUrl,
  getDeptInfoUrl,
  checkMerge,
} from "@/api/system/userList";

import { praseStrEmpty } from "@/utils/ruoyi";
import { changePassword } from "@/api/login";
import { validatePass, validatePass2 } from "@/utils/passwordValid.js";

import { ref, reactive, onMounted, computed, watch, nextTick, toRefs } from "vue";
import { encrypt, decrypt, ciiEncrypt } from "@/utils/jsencrypt";
import { ElMessage, ElMessageBox } from "element-plus";
const emit = defineEmits([]);
import { useStore } from "vuex";

const store = useStore();

//定义props属性
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  dialogTitle: {
    type: String,
    default: "",
  },
  editId: {
    type: String,
    default: "",
  },
  editInfo: {
    type: Object,
    default() {
      return {};
    },
  },
});
// 打开弹框
let dialogRef = ref();
let state = reactive({
  formData: {},
  roleList: [],
});

// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "userName",
    label: "成员账号",
    required: true,
  },
  {
    formType: "input",
    prop: "nickName",
    label: "成员姓名",
    required: true,
  },
  {
    formType: "input",
    prop: "phonenumber",
    label: "手机号码",
    required: true,
    vxRule: "Mobile",
  },
  {
    formType: "input",
    prop: "email",
    label: "邮箱地址",
    required: true,
    exRule: "email",
  },
  {
    formType: "select",
    prop: "userType",
    label: "用户类型",
    required: true,
    options: [],
    dictName: "user_type",
  },
]);
let formList2 = reactive([
  {
    formType: "radio",
    prop: "sex",
    label: "用户性别",
    required: true,
    options: [],
    dictName: "sys_user_sex",
  },
  {
    formType: "select",
    prop: "roleIds",
    label: "用户角色",
    multiple: true,
    // required: true,
    options: state.roleList,
  },
  {
    formType: "input",
    type: "password",
    name: "new-field",
    prop: "password",
    label: "登录密码",
    required: !props.editId,
    rules: [{ validator: (a, b, c) => validatePass(a, b, c, true), trigger: "blur" }],
  },
  {
    formType: "input",
    type: "password",
    prop: "qrPwd",
    label: "确认密码",
    required: !props.editId,
    rules: [{ validator: (a, b, c) => validatePass2(state.formData.password, a, b, c), trigger: "blur" }],
  },
  {
    formType: "input",
    type: "textarea",
    prop: "remark",
    label: "备注",
  },
]);
// 表单内容
let ruleForm = ref();

//列表重置
function resetFormData() {
  state.formData = {
    userName: "",
    nickName: "",
    phonenumber: "",
    email: "",
    userType: "",
    sex: "",
    roleIds: "",
    password: "",
    qrPwd: "",
    remark: "",
    netsecurity: "",
    zcdwId: "",
    jgdwId: "",
    deptIds: [],
  };
}
// 关闭取消方法
function closeDialog(reload = true) {
  resetFormData();
  nextTick(() => {
    // ruleForm.value.resetFields();
  });
  emit("closeDialog", "addEdit", reload);
}
// 弹框确定按钮
let editId = ref(""); //编辑Id
// 提交
const submitForm = async function (close, loading) {
  let valid = await ruleForm.value.validate();

  if (valid) {
    let query = {
      ...state.formData,
    };
    if (!(props.editId && !query.password)) {
      query.password = ciiEncrypt(state.formData.password);
    }

    let canSave = await checkMergeFn(query);
    if (!canSave) {
      close(false);
      return;
    }
    loading();

    let addFn = props.editId ? updateItem : addItem;
    addFn(query)
      .then((res) => {
        ElMessage.success("操作成功");
        close();
        emit("closeDialog", "addEdit");
      })
      .catch(() => {
        close(false);
        // emit("closeDialog", "addEdit");
      });
  } else {
    return false;
  }
};
//验证
const checkMergeFn = async function (query) {
  if (query.userType != 2) {
    return true;
  } else {
    let res = await checkMerge(query);
    //针对客户类型，是否合并 1
    if (res.isMerge == 1) {
      try {
        await ElMessageBox.confirm("发现系统中已存在被删除账号，确定后新账号将与旧账号合并，取消则不会创建账号", "确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        query.isMerge = res.isMerge;
        return true;
      } catch (err) {
        return false;
      }
    } else {
      return true;
    }
  }
};

//头像
let avatarSrc = ref(props.editInfo.photoId ? "/outpost-api/system/file/getFile?fileId=" + props.editInfo.photoId : "");
watch(
  () => avatarSrc.value,
  () => {
    store.dispatch("updateUserInfo");
  }
);

function getImageUrl() {
  return new URL(`../../../../assets/imgs/default.png`, import.meta.url).href;
}

//获取角色
function getRoleInfoByIdFn() {
  getRoleInfoById({ userId: props.editId }).then(({ data }) => {
    formList2[1].options = data.roles.map((item) => {
      return {
        value: item.roleId,
        label: item.roleName,
      };
    });
    state.formData.roleIds = data.roleIds;
  });
}

//获取部门树
let deptState = reactive({
  deptData: [],
});
function getDeptInfoUrlFn() {
  getDeptInfoUrl({ userId: props.editId }).then(({ data }) => {
    deptState.deptData = data.deptTree.map((item) => {
      return {
        ...item,
        // disabled: true,
      };
    });
    state.formData.deptIds = data.deptIds;
  });
}

onMounted(() => {
  if (props.editId) {
    state.formData = props.editInfo;

    state.formData.password = "";
    state.formData.qrPwd = "";
  }
  getRoleInfoByIdFn();
  getDeptInfoUrlFn();
  dialogRef.value.open();
});
watch(props.editId, (val) => {});
let depeRef = ref();
watch(
  () => state.formData.deptIds,
  (val) => {
    if (val && val.length > 0) {
      depeRef.value && depeRef.value.clearValidate();
    }
  }
);
let { formData } = toRefs(state);
</script>

<style lang="scss" scoped>
:deep(.avatar-img) {
  margin: 0 auto 20px;
}
</style>
