<template>
  <!-- 重置密码 -->
  <xelDialog :dialogVisible="dialogVisible" :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
      <xel-form-item v-for="(item, index) in psdList" :key="index" v-model="psdformData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xelDialog>
</template>
<script setup>
import { getuserForm as getDetail, addUserList as addItem, editUserList as updateItem, deleteUser as delItem } from "@/api/system/userList";
import { ciiEncrypt } from "@/utils/jsencrypt";
import { ref, reactive, onMounted, computed, watch, nextTick, toRefs } from "vue";
const emit = defineEmits([]);
//定义props属性
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  dialogTitle: {
    type: String,
    default: "",
  },
});
let dialogRef = ref();
onMounted(() => {
  dialogRef.value.open();
});
// 弹框标题
let ruleForm = ref();
let psdformData = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: "",
});
let psdList = reactive(
  {
    formType: "input",
    prop: "oldPassword",
    label: "旧密码",
    required: true,
    type: "password",
  },
  {
    formType: "input",
    prop: "newPassword",
    label: "新密码",
    required: true,
    type: "password",
  },
  {
    formType: "input",
    prop: "confirmPassword",
    label: "确认密码",
    required: true,
    type: "password",
  }
);
// 弹框确定按钮
let editId = ref("");
// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      psdformData.oldPassword.value = ciiEncrypt(psdformData.oldPassword);
      psdformData.newPassword.value = ciiEncrypt(psdformData.newPassword);
      let addFn = editId.value ? updateItem : addItem;
      loading();
      addFn(formData)
        .then((res) => {
          close();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}

let state = reactive({
  formData: {},

  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    resource: "",
  };
}
function closeDialog() {
  resetFormData();
  ruleForm.value.resetFields();
  emit("closeDialog", "addEdit");
}
//列表重置
</script>

<style lang="scss" scoped></style>
