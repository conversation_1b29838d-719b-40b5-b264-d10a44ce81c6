<!---->

<template>
  <h4 class="change-flag" v-if="$route.name == 'License'">{{ changeFlag }}</h4>

  <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini" class="width">
    <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    <el-form-item class="buut">
      <el-button type="primary" size="mini" :loading="loading" @click="submitForm" :disabled="!formData.licenseCode">提 交</el-button>
      <el-button type="primary" size="mini" v-show="false">退 出</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: "License",
};
</script>
<script setup>
import { getCode, saveCode } from "@/api/system/license";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let changeFlag = ref("");
let disabled = ref(true);
import { useRouter, useRoute } from "vue-router";

const router = useRouter();

const route = useRoute();

// 获取页面数据
function getlist() {
  getCode().then(({ data }) => {
    let res = data;
    formData.loginCode = res.license.loginCode;
    formData.id = res.license.id;
    if (!res.license.changeLicense) {
      formData.licenseCode = res.license.licenseCode;
      formData.effectiveTime = res.license.effectiveTime;
      formList[2].required = true;
    } else {
      changeFlag.value = res.license.changeFlag;
    }
  });
}

let ruleForm = ref();
// 表格配置项
let formData = reactive({
  id: "",
  loginCode: "",
  licenseCode: "",
  effectiveTime: "",
});
let state = reactive({
  formData: {},
});
// 表格配置项
let formList = reactive([
  {
    formType: "input",
    prop: "loginCode",
    label: "申请码",
    required: true,
    disabled: true,
  },
  {
    formType: "input",
    prop: "licenseCode",
    label: "授权码",
    required: true,
  },
  {
    isShow: route.name != "licensePage",
    formType: "input",
    prop: "effectiveTime",
    label: "有效截止日期",

    disabled: true,
  },
]);

if (route.name == "licensePage" && window.licenseInfo) {
  formData.loginCode = window.licenseInfo.login_code;
  formData.id = window.licenseInfo.id;
} else {
  getlist();
}

let loading = ref(false);

function submitForm() {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      saveCode(formData)
        .then((res) => {
          if (res.code === 200) {
            if (route.name == "licensePage") {
              ElMessage.success("提交成功，将为您跳转到登录页面!");
              setTimeout(() => {
                location.href = "/login";
              }, 1000);
              return;
            }
            state.formData.effectiveTime = res.data.effectiveTime;
            ElMessageBox.confirm("提交成功，请重新登录系统！", "提示", {
              distinguishCancelAndClose: true,
              closeOnPressEscape: false,
              closeOnClickModal: false,
              showClose: false,
              showCancelButton: false,
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "success",
            })
              .then(() => {
                location.href = "/login";
              })
              .catch((action) => {
                ElMessage({
                  type: "info",
                  message: "取消操作",
                });
              });
          } else {
            ElMessage.error("操作失败");
          }
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
}
</script>
<style scoped lang="scss">
.width {
  width: 60%;
  margin: 0 auto;
}
.buut {
  width: 334px;
  margin: 0 auto;
}
.change-flag {
  color: red;
  font-size: 16px;
  margin: 20px;
  text-align: center;
}
</style>
