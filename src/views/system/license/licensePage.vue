<!--单独的授权页面-->

<template>
  <div class="login-wrapper">
    <img class="system-title" src="@/assets/imgs/systemTitle.png" alt="" />
    <div class="login-form">
      <p class="remark-info">第一次运行前请先提交授权码</p>

      <submitForm></submitForm>
      <div class="version-box" v-if="licenseInfo.versionNo">[{{ licenseInfo.versionNo }}]</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import submitForm from "./components/submitForm.vue";
let licenseInfo = window.licenseInfo || {};
</script>

<style scoped lang="scss">
.remark-info {
  color: #eee;
  font-size: 20px;
  width: 100%;
  margin: -20px auto 40px;
  padding-left: 50px;
}

.login-form {
  width: 700px;
  margin-top: 80px;
}
.login-code {
  float: right;
  cursor: pointer;
}
.login-wrapper {
  :deep {
    .width {
      width: 100%;
    }
    .el-input__inner {
      border-radius: 0;
      line-height: 60px;
      height: 60px;
      background: transparent !important;
      border-color: transparent !important;
      border-bottom-color: #39437d;
      padding-left: 60px;
      color: #fff !important;
      font-size: 14px !important;
      &:-internal-autofill-previewed,
      &:-internal-autofill-selected {
        -webkit-text-fill-color: #ffffff !important;
        transition: background-color 5000s ease-in-out 0s !important;
      }
    }
    .el-form-item.is-error .el-input__inner,
    .el-form-item.is-error .el-input__inner:focus,
    .el-form-item.is-error .el-textarea__inner,
    .el-form-item.is-error .el-textarea__inner:focus {
      border-color: transparent;
      border-bottom-color: #39437d;
    }
    .el-form-item--mini.el-form-item:not(:last-child) {
      position: relative;
      &:after {
        content: "";
        display: block;
        width: calc(100% - 50px);
        height: 1px;
        background: #777;
        position: absolute;
        left: 50px;
        bottom: 0;
      }
    }
    .el-form-item__error {
      padding-left: 60px;
      padding-top: 4px;
    }
    .el-icon {
      color: #36407a;
      font-size: 20px;
      vertical-align: bottom;
      transform: translateY(5px);
    }
    .el-form-item__label {
      line-height: 60px;
      color: #fff !important;
    }
    .el-button--mini {
      padding: 16px 20px;
      width: 160px;
      margin-top: 20px;
      border-radius: 14px;
      margin-left: 70px;
    }
    .buut {
      width: 100%;
      margin: 0 auto;
      text-align: right;
    }
    .el-input__suffix {
      display: none !important;
    }
  }
}
</style>
