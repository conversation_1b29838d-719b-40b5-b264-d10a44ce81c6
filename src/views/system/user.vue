<template>
  <xelDialog title="弹框标题" ref="dialog" @submit="submit" sise="large">
    <div>内容插槽</div>
    <!-- <template v-slot:button>
      <el-button type="primary" @click="springFrame">主要按钮</el-button>
      <el-button type="primary" @click="springFrame">主要按钮</el-button>
    </template> -->
  </xelDialog>
</template>
<script setup>
import { defineComponent, ref, computed, watch } from "vue";
let dialog = ref();
function springFrame() {
  dialog.value.open();
}

function submit(callback) {
  setTimeout(() => {
    callback(false);
  }, 1000);
}
submit();
</script>
