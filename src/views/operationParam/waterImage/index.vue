<!---->

<template>
  <el-card>
    <h3 class="conH3Tit">配置水印图片</h3>

    <el-radio-group v-model="waterChecked" @change="update">
      <template v-for="item in imgsList" :key="item.value">
        <el-radio :label="item.value">{{ item.name }}</el-radio>

        <xel-upload-img
          v-if="item.value == 4"
          v-model="waterImageUrl"
          class="upload-inline"
          size="70px"
          action="/outpost-api/system/file/uploadWatermarkImage"
          name="file"
          prop="avatar"
          :success="success"
        ></xel-upload-img>
      </template>
    </el-radio-group>
  </el-card>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { selectParamValue, updateParamValue, uploadWatermarkImage } from "@/api/param/waterImage";
let imgsList = ref([]);
let waterChecked = ref(-1);
getList();
async function getList() {
  return await selectParamValue().then(({ data }) => {
    imgsList.value = data;
    waterChecked.value = data.find((item) => item.check == 1).value;
    waterImage.value = data.find((item) => item.value == 4).waterImage;
  });
}

let waterImage = ref("");
let waterImageUrl = computed(() => (waterImage.value ? "/outpost-api/system/file/getFile?fileId=" + waterImage.value : ""));

function update(val) {
  if (val == 4 && !waterImage.value) {
    ElMessage.warning("请上传自定义图片");
    return;
  }
  updateParamValue({ paramValue: val }).then(() => {
    ElMessage.success("设置成功");
    getList();
  });
}

function success(res) {
  if (!waterImage.value) {
    getList().then(() => {
      update(4);
    });
  } else {
    ElMessage.success("上传成功");
    getList();
  }
}
</script>

<style scoped lang="scss">
:deep(.upload-inline) {
  display: inline-block;
  margin-right: 20px;
  vertical-align: middle;
}
</style>
