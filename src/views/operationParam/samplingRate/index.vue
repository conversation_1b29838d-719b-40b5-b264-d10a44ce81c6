<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button size="small" @click="addSamplingRate">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <xel-table ref="table" :columns="columns" :load-data="getSamplingRate"> </xel-table>
    <xelDialog :title="samplingRateDialogTitle" ref="samplingRateDialog" size="small" @submit="samplingRateChange" @close="colse_edit">
      <el-form v-if="showForm" :model="state.sampling_rate_form" ref="sampling_rate_form" label-width="120px" size="small">
        <xel-form-item
          v-for="(item, index) in state.sampling_rate_form_list"
          :key="index"
          v-model="state.sampling_rate_form[item.prop]"
          v-bind="item"
          @changeTree="changeTree"
        ></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "SamplingRate",
};
</script>
<script setup>
import { ref, reactive, onMounted, watch, onActivated, nextTick } from "vue";
import { getSamplingRate, getSamplingRateParam, saveSamplingParam, dellingRate } from "@/api/param/samplingRate";
import { ElMessage } from "element-plus";
import { batchDelete } from "@/utils/delete";
import { Loading } from "@element-plus/icons";
// 查询重置
let searchState = reactive({
  data: {
    userName: "",
    roleName: "",
  },
  formList: [
    {
      formType: "input",
      prop: "userName",
      label: "用户名称",
      // on: (change = "search"),
    },
    {
      formType: "input",
      prop: "roleName",
      label: "角色名称",
    },
  ],
});
let table = ref();
function search(initPage = true) {
  table.value && table.value.reload(searchState.data, initPage);
}
function reset() {
  // table.value && table.value.reload({});
  searchState.data = {
    userName: "",
    roleName: "",
  };
  search();
}
// 列表
const columns = [
  {
    prop: "userName",
    label: "用户名称",
  },
  {
    prop: "roleName",
    label: "角色名称",
  },
  {
    prop: "ratio",
    label: "抽检率(%)",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",

    btnList: [
      {
        icon: "edit",
        title: "修改",
        hasPermi: "operation:rate:edit",
        onClick(scope) {
          handleUpdate(scope.row);
        },
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "operation:rate:remove",
        onClick(scope) {
          batchDelete().then(() => {
            dellingRate({ id: scope.row.id }).then(() => {
              ElMessage({
                type: "success",
                message: "删除成功",
              });
              search(false);
            });
          });
        },
      },
    ],
  },
];
// 新增/编辑
let samplingRateDialogTitle = ref("");
let samplingRateDialog = ref();
// 是否是修改
let is_edit = ref(true);
let disabled = ref(false);

let state = reactive({
  sampling_rate_form: {
    userIds: [],
    ratio: "",
  },
  sampling_rate_form_list: [
    {
      formType: "tree",
      prop: "userIds",
      label: "用户",
      required: is_edit,
      multiple: true, //是否多选
      treeOptions: {
        loadData: getSamplingRateParam, //接口名称
        resKey: "userTreePartake",
        params: {},
      },
      disabled: disabled,
      disabledKey: "value",
    },
    {
      formType: "number",
      prop: "ratio",
      required: true,
      label: "抽检率(%)",
      vxRule: "IntPlus",
      max: 100,
    },
  ],
});
/*提交方法 */
let sampling_rate_form = ref();
function samplingRateChange(close, loading) {
  sampling_rate_form.value.validate((valid) => {
    if (valid) {
      loading();
      let query = {};
      if (state.sampling_rate_form.id) {
        query = state.sampling_rate_form;
        delete query.userIds;
      } else {
        let userIdArr = state.sampling_rate_form.userIds.join(",");
        query = {
          userIds: userIdArr,
          ratio: state.sampling_rate_form.ratio,
        };
      }
      saveSamplingParam(query)
        .then((res) => {
          ElMessage({
            type: "success",
            message: "操作成功",
          });
          search(false);
          showForm.value = false;
          close();
        })
        .catch(() => {
          close(false);
        });
    }
  });
}
// 修改
function handleUpdate(row) {
  samplingRateDialogTitle.value = "修改抽检率";
  state.sampling_rate_form.userIds = row.userId.split(",");
  state.sampling_rate_form.ratio = row.ratio;
  state.sampling_rate_form.id = row.id;
  is_edit.value = false;
  disabled.value = true;
  samplingRateDialog.value.open();
  showForm.value = true;
}
/*关闭方法 */
function colse_edit() {
  showForm.value = false;
}
let showForm = ref(false);
function addSamplingRate() {
  samplingRateDialogTitle.value = "添加抽检率";
  is_edit.value = true;
  disabled.value = false;
  state.sampling_rate_form = {
    userIds: [],
    ratio: "",
  };
  samplingRateDialog.value.open();
  showForm.value = true;
}
// tree传值
function changeTree(data, id) {
  state.sampling_rate_form[id] = data;
}
</script>

<style lang="scss" scoped></style>
