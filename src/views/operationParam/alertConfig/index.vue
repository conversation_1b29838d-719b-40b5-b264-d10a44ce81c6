<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="aqldActiveTabs">
      <el-tab-pane label="过滤清单" name="filterList"> </el-tab-pane>
      <el-tab-pane label="紧急告警配置" name="alarmConfig"> </el-tab-pane>
      <filter-list :type="aqldActiveTabs" :key="aqldActiveTabs" />
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "AlertConfig",
};
</script>
<script setup>
import filterList from "./compontents/filterList.vue";
import { ref, reactive, onMounted, watch, onActivated } from "vue";
let aqldActiveTabs = ref("filterList");
</script>

<style lang="scss" scoped></style>
