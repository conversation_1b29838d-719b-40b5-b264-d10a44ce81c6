<template>
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button size="small" @click="is_add_show = !is_add_show" v-hasPermi="'alertConfig:filter:add'" v-if="props.type == 'filterList'">
      <el-icon :size="12">
        <plus />
      </el-icon>
      添加忽略规则
    </el-button>
    <el-button size="small" @click="is_add_show = !is_add_show" v-hasPermi="'alertConfig:enmergency:add'" v-else>
      <el-icon :size="12">
        <plus />
      </el-icon>
      添加紧急告警规则
    </el-button>
  </common-search>
  <div>
    <div v-show="is_add_show">
      <add ref="addShowRef" :type="props.type" :is_add_show="is_add_show" @close="close_add_filter" @submit="search" />
    </div>
  </div>
  <xel-table ref="tableRef" :columns="state.columns" :load-data="state.getdata">
    <template #status="scope">
      <el-switch v-model="scope.row.status" active-value="1" inactive-value="2" @change="update_statu(scope.row)"> </el-switch>
    </template>
    <template #prescription="scope"> {{ scope.row.prescription }} {{ scope.row.definedTypeStr }} </template>
  </xel-table>
</template>

<script setup>
import { ref, reactive, onMounted, watch, onActivated, computed, nextTick } from "vue";
import { getDicts } from "@/api/system/dict/data";
import { ElMessageBox, ElMessage } from "element-plus";
import add from "./add.vue";
import {
  getEnmergencyData,
  getFilterData,
  updateEnmergencyStatus,
  updateStatus,
  deleteFilter,
  deleteEnmergencyFilter,
} from "@/api/param/alertConfig";
import { batchDelete } from "@/utils/delete";
import { rollBackVersion } from "../../../../api/sime/config/batchEngine";
// 获取字典值
let props = defineProps({
  type: {
    type: String,
    default: "",
  },
});
let addShowRef = ref();
let is_add_show = ref(false);
let searchState = reactive({
  data: {
    alertTitle: "",
    alertFrom: "",
    alertPriority: "",
    alertDevIp: "",
    alertCsrcip: "",
    alertCdstip: "",
    alertIdstport: "",
    alertDomain: "",
    alertDescription: "",
  },
  menuData: [
    {
      lable: "告警等级",
      prop: "alertPriority",
      options: [],
      dictName: "alertConfig_alertPriority",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "alertTitle",
      label: "告警名称",
    },
    {
      formType: "input",
      prop: "alertDevIp",
      label: "设备IP",
    },
    {
      formType: "input",
      prop: "alertCsrcip",
      label: "源地址",
    },
    {
      formType: "input",
      prop: "alertCdstip",
      label: "目的地址",
    },
    {
      formType: "input",
      prop: "alertIdstport",
      label: "目的端口",
    },
    {
      formType: "input",
      prop: "alertDomain",
      label: "域名",
    },
    {
      formType: "input",
      prop: "alertDescription",
      label: "告警描述",
    },
  ],
});
function close_add_filter() {
  is_add_show.value = false;
}
// 重置
let tableRef = ref();
function reset() {
  searchState.data = {
    alertTitle: "",
    alertFrom: "",
    alertPriority: "",
    alertDevIp: "",
    alertCsrcip: "",
    alertCdstip: "",
    alertIdstport: "",
    alertDomain: "",
  };
  addShowRef.value.add_filter ? addShowRef.value.add_filter.reset() : "";
  tableRef.value && tableRef.value.reload({ ...searchState.data });
}
function search(initPage = true) {
  tableRef.value && tableRef.value.reload({ ...searchState.data }), initPage;
}
// 表格内容
let state = reactive({
  columns: [],
  getdata: null,
});
watch(
  () => props.type,
  (type, oldType) => {
    changeType();
    nextTick(() => {
      reset();
    });
  }
);
changeType();
function changeType() {
  // 过滤规则
  if (props.type === "filterList") {
    state.columns = [
      {
        prop: "status",
        label: "状态",
        slotName: "status",
      },
      {
        prop: "alertTitle",
        label: "告警名称",
      },

      {
        prop: "alertPriorityStr",
        label: "告警等级",
      },
      {
        prop: "alertDevIp",
        label: "设备IP",
      },
      {
        prop: "alertDescription",
        label: "告警描述",
      },
      {
        prop: "alertCsrcip",
        label: "源地址",
      },
      {
        prop: "alertCdstip",
        label: "目的地址",
      },
      {
        prop: "alertIdstport",
        label: "目的端口",
      },
      {
        prop: "alertDomain",
        label: "域名",
      },
      {
        prop: "prescription",
        label: "有效期",
        slotName: "prescription",
      },
      {
        prop: "expireTime",
        label: "截止时间",
        width: "240",
      },
      {
        label: "操作",
        fixed: "right",
        width: "100px",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "delete",
            title: "删除",
            hasPermi: "alertConfig:filter:delete",
            onClick(scope) {
              handDetele(scope.row.id);
            },
          },
        ],
      },
    ];
    state.getdata = getFilterData;
  } else {
    // 紧急告警配置
    state.columns = [
      {
        prop: "status",
        label: "状态",
        slotName: "status",
      },
      {
        prop: "alertTitle",
        label: "告警名称",
      },

      {
        prop: "alertPriorityStr",
        label: "告警等级",
      },
      {
        prop: "alertDevIp",
        label: "设备IP",
      },
      {
        prop: "alertCsrcip",
        label: "源地址",
      },
      {
        prop: "alertCdstip",
        label: "目的地址",
      },
      {
        prop: "alertIdstport",
        label: "目的端口",
      },
      {
        prop: "alertDomain",
        label: "域名",
      },
      {
        prop: "alertDescription",
        label: "告警描述",
      },
      {
        label: "操作",
        fixed: "right",
        width: "100px",
        slotName: "actionBtns",

        btnList: [
          {
            icon: "delete",
            title: "删除",
            hasPermi: "alertConfig:enmergency:delete",
            onClick(scope) {
              handDetele(scope.row.id);
            },
          },
        ],
      },
    ];
    state.getdata = getEnmergencyData;
  }
  nextTick(() => {
    reset();
  });
}
// 删除
function handDetele(id) {
  let query = { id };
  batchDelete().then(() => {
    if (props.type === "filterList") {
      deleteFilter(query).then(() => {
        ElMessage({
          type: "success",
          message: "删除成功",
        });
        search(false);
      });
    } else {
      deleteEnmergencyFilter(query).then(() => {
        ElMessage({
          type: "success",
          message: "删除成功",
        });
        search(false);
      });
    }
  });
}
// 修改状态
function update_statu(row) {
  let query = {
    id: row.id,
    status: row.status,
  };
  ElMessageBox.confirm("是否确认修改该数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(function () {
      if (props.type === "filterList") {
        updateStatus(query).then((res) => {
          ElMessage({
            type: "success",
            message: "修改成功",
          });
          search(false);
        });
      } else {
        updateEnmergencyStatus(query).then((res) => {
          ElMessage({
            type: "success",
            message: "修改成功",
          });
          search(false);
        });
      }
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "取消修改",
      });

      search(false);
    });
}
</script>

<style lang="scss" scoped></style>
