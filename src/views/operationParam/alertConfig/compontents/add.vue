<template>
  <el-form ref="add_filter" :model="state.add_filter_form" label-width="30px" label-position="center">
    <el-row class="filter_class">
      <el-col v-if="props.type === 'filterList'">
        <el-form-item label-width="0px">
          <el-select clearable v-model="state.add_filter_form.definedType" placeholder="请选择时效单位" size="small" style="width: 150px">
            <el-option v-for="item in state.definedTypeList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"> </el-option>
          </el-select>
        </el-form-item>
      </el-col>

      <el-col v-if="props.type === 'filterList'">
        <el-form-item label="&#38;">
          <el-input clearable size="small" v-model="state.add_filter_form.prescription" placeholder="请输入整数" type="number" style="width: 150px" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertTitle">
        <el-form-item label="&#38;" v-if="props.type === 'filterList'">
          <el-input clearable size="small" v-model="state.add_filter_form.alertTitle" placeholder="请输入告警名称" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertTitle')" />
        </el-form-item>
        <el-form-item label="" v-else>
          <el-input clearable size="small" v-model="state.add_filter_form.alertTitle" placeholder="请输入告警名称" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertTitle')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertPriority">
        <el-form-item label="&#38;">
          <el-select clearable v-model="state.add_filter_form.alertPriority" placeholder="请选择告警等级" size="small" style="width: 150px">
            <el-option v-for="item in state.alertLevelList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"> </el-option>
          </el-select>
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertPriority')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertDevIp">
        <el-form-item label="&#38;">
          <el-input clearable size="small" v-model="state.add_filter_form.alertDevIp" placeholder="请输入设备IP" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertDevIp')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertDescription">
        <el-form-item label="&#38;">
          <el-input clearable size="small" v-model="state.add_filter_form.alertDescription" placeholder="请输入告警描述" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertDescription')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertCsrcip">
        <el-form-item label="&#38;">
          <el-input clearable size="small" v-model="state.add_filter_form.alertCsrcip" placeholder="请输入源地址" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertCsrcip')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertCdstip">
        <el-form-item label="&#38;">
          <el-input clearable size="small" v-model="state.add_filter_form.alertCdstip" placeholder="请输入目的地址" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertCdstip')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertIdstport">
        <el-form-item label="&#38;">
          <el-input clearable size="small" v-model="state.add_filter_form.alertIdstport" placeholder="请输入目的端口" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertIdstport')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.is_show.alertDomain">
        <el-form-item label="&#38;">
          <el-input clearable size="small" v-model="state.add_filter_form.alertDomain" placeholder="请输入域名" style="width: 150px" />
          <i class="removeIcon el-icon-circle-close" @click="removeIconFn('alertDomain')" />
        </el-form-item>
      </el-col>

      <el-col v-if="state.isAndShow">
        <el-form-item>
          <el-button :disabled="!isRemain" type="primary" plain size="mini" v-if="props.is_add_show" @click="addFilter"
            ><span style="font-size: 18px; font-weight: 400">&#38;</span></el-button
          >
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item style="float: right">
      <el-button icon="el-icon-plus" size="mini" type="primary" @click="createFilterList">{{
        props.type === "filterList" ? "添加忽略规则" : "添加紧急告警规则"
      }}</el-button>
      <el-button size="mini" @click="addCancel">取消</el-button>
    </el-form-item>
  </el-form>
  <xelDialog title="添加规则条件" ref="add_filter_show" size="mini" @submit="submit_show" @close="close_show">
    <el-row :gutter="20">
      <el-checkbox-group v-model="state.check_list">
        <el-col v-for="item in state.show_list" :key="item.value" v-show="item.isShow">
          <el-checkbox :label="item.value">{{ item.lable }}</el-checkbox>
        </el-col>
      </el-checkbox-group>
    </el-row>
  </xelDialog>
</template>

<script setup>
import { ref, reactive, onMounted, watch, onActivated, computed } from "vue";
import { getDicts } from "@/api/system/dict/data";
import { ElMessageBox, ElMessage } from "element-plus";
import { createFilter, createEnmergencyFilter } from "@/api/param/alertConfig";
let props = defineProps({
  type: {
    type: String,
    default: "",
  },
  is_add_show: {
    type: Boolean,
    default: false,
  },
});
let isRemain = ref(true);
getDicts("alertConfig_definedType").then((response) => {
  state.definedTypeList = response.data;
});
//告警等级
getDicts("alertConfig_alertPriority").then((response) => {
  state.alertLevelList = response.data;
});
let state = reactive({
  // 时效单位
  definedTypeList: [],
  alertFromList: [],
  alertLevelList: [],
  check_list: [],
  show_list: [
    { value: "alertTitle", lable: "告警名称", isShow: true },
    { value: "alertPriority", lable: "告警等级", isShow: true },
    { value: "alertDevIp", lable: "设备IP", isShow: true },
    { value: "alertDescription", lable: "告警描述", isShow: true },
    { value: "alertCsrcip", lable: "源地址", isShow: true },
    { value: "alertCdstip", lable: "目的地址", isShow: true },
    { value: "alertIdstport", lable: "目的端口", isShow: true },
    { value: "alertDomain", lable: "域名", isShow: true },
  ],
  is_show: {
    alertTitle: false,
    alertPriority: false,
    alertDevIp: false,
    alertDescription: false,
    alertCsrcip: false,
    alertCdstip: false,
    alertIdstport: false,
    alertDomain: false,
  },
  add_filter_form: {},
  isAndShow: true,
});
function addFilter() {
  add_filter_show.value.open();
}
// 移除规则
function removeIconFn(type) {
  delete state.add_filter_form[type];
  state.is_show[type] = false;
  state.show_list.findIndex((item2) => {
    if (item2.value === type) {
      item2.isShow = true;
    }
  });
  andShowFun();
}
/* 判断是否显示 And */
function andShowFun() {
  let isNum = 0;
  state.show_list.forEach((item) => {
    if (item.isShow) {
      isNum++;
    }
  });
  isNum === 0 ? (state.isAndShow = false) : (state.isAndShow = true);
  // 判断当前是否还有未添加的规则，
  isRemain.value = state.show_list.some((item) => {
    return item.isShow === true;
  });
}
// 弹窗
let add_filter_show = ref();
function submit_show() {
  state.check_list.forEach((item) => {
    state.is_show[item] = true;
    state.show_list.findIndex((item2) => {
      if (item2.value === item) {
        item2.isShow = false;
      }
    });
  });
  close_show();
  state.check_list = [];
}
function close_show() {
  add_filter_show.value.close();
  state.check_list = [];
  state.show_list.filter((sItem) => {});
  // 判断当前是否还有未添加的规则，
  isRemain.value = state.show_list.some((item) => {
    return item.isShow === true;
  });
}
// 添加规则
let add_filter = ref();
function createFilterList() {
  add_filter.value.validate((valid) => {
    if (valid) {
      if (props.type === "filterList") {
        if (state.add_filter_form.definedType === "" || state.add_filter_form.definedType === undefined) {
          ElMessage({
            type: "warning",
            message: "时效单位不能为空",
          });
          return false;
        }
        if (state.add_filter_form.prescription === "" || state.add_filter_form.prescription === undefined) {
          ElMessage({
            type: "warning",
            message: "时效值不能为空",
          });
          return false;
        }

        createFilter(state.add_filter_form).then((res) => {
          ElMessage({
            type: "success",
            message: "操作成功",
          });
          emits("submit");
          state.add_filter_form = {};
          reset();
        });
      } else if (props.type === "alarmConfig") {
        createEnmergencyFilter(state.add_filter_form).then((res) => {
          ElMessage({
            type: "success",
            message: "操作成功",
          });
          emits("submit");
          state.add_filter_form = {};
          reset();
        });
      }
    }
  });
}
// 关闭添加模块
let emits = defineEmits(["close", "submit"]);
function addCancel() {
  emits("close");
  reset();
}
// 重置
function reset() {
  isRemain.value = true;
  state.check_list = [];
  state.show_list = [
    { value: "alertTitle", lable: "告警名称", isShow: true },
    { value: "alertPriority", lable: "告警等级", isShow: true },
    { value: "alertDevIp", lable: "设备IP", isShow: true },
    { value: "alertDescription", lable: "告警描述", isShow: true },
    { value: "alertCsrcip", lable: "源地址", isShow: true },
    { value: "alertCdstip", lable: "目的地址", isShow: true },
    { value: "alertIdstport", lable: "目的端口", isShow: true },
    { value: "alertDomain", lable: "域名", isShow: true },
  ];
  state.is_show = {
    alertTitle: false,
    alertPriority: false,
    alertDevIp: false,
    alertDescription: false,
    alertCsrcip: false,
    alertCdstip: false,
    alertIdstport: false,
    alertDomain: false,
  };
  state.add_filter_form = {};
}
defineExpose({
  reset,
});
</script>

<style lang="scss" scoped>
.filter_class {
  .el-col {
    width: auto;
    flex: inherit;
    max-width: inherit;
    position: relative;
    :deep(.el-form-item__label) {
      padding: 0px;
      text-align: center;
    }
  }
  .removeIcon {
    cursor: pointer;
    position: absolute;
    right: -2px;
    height: 10px;
    top: -4px;
    width: 10px;
  }
}
</style>
