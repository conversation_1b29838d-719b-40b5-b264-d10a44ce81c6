<template>
  <el-card>
    <!-- <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3> -->
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="search" @reset="reset"> </common-search>
    <xel-table ref="table" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange" :checkbox="false">
      <template #level="scope">
        <el-tag :type="levelData[scope.row.priority]">{{ scope.row.priorityStr }}</el-tag>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog title="操作日志详细" ref="dialog">
      <el-form ref="formRef" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作模块：">{{ form.title }} / </el-form-item>
            <el-form-item label="登录信息：">{{ form.operName }} / {{ form.operIp }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求地址：">{{ form.operUrl }}</el-form-item>
            <el-form-item label="请求方式：">{{ form.requestMethod }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="请求参数：">{{ form.operParam }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="返回参数：">{{ form.jsonResult }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作状态：">
              <div v-if="form.status === 0">正常</div>
              <div v-else-if="form.status === 1">失败</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="操作时间：">{{ parseTime(form.operTime) }}</el-form-item> -->
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常信息：" v-if="form.status === 1">{{ form.errorMsg }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #button>
        <el-button round @click="close" size="mini">关闭</el-button>
      </template>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "AlarmflowQuery",
};
</script>
<script setup>
import { getAlertOperationList as getTableData } from "@/api/param/alertConfig";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import { Level_priority } from "@/config/constant";
let idKey = "roleId";
let tableRef = ref();
let searchState = reactive({
  data: {
    title: "",
    description: "",
    devIp: "",
    remark: "",
  },
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "告警名称",
    },
    {
      formType: "input",
      prop: "description",
      label: "告警描述",
    },
    {
      formType: "input",
      prop: "devIp",
      label: "设备地址",
    },
    {
      formType: "input",
      prop: "remark",
      label: "告警流说明",
    },
  ],
});
// 列表配置项
const columns = [
  {
    prop: "title",
    label: "告警名称",
  },
  {
    prop: "priorityStr",
    label: "告警等级",
    slotName: "level",
  },
  {
    prop: "devIp",
    label: "设备地址",
  },
  {
    prop: "description",
    label: "告警描述",
  },
  {
    prop: "remark",
    label: "告警流说明",
  },
  {
    prop: "operationTimeStr",
    label: "告警流时间",
  },
];
//获取表格数据

// let loading = ref(false);
// getData();
// function getData() {
//   loading.value = true;
//   getTableData(searchState.data).then(({ data }) => {
//     // state.list = handleTree(data, "menuId");
//     loading.value = false;
//   });
// }
let table = ref();
function search(initPage = true) {
  table.value && table.value.reload({ ...searchState.data }, initPage);
}
function reset() {
  searchState.data = {
    title: "",
    description: "",
    devIp: "",
    remark: "",
  };
  table.value && table.value.reload(searchState.data);
}
// 弹框
let dialog = ref();
let state = reactive({ form: {}, levelData: Level_priority });
let { form } = toRefs(state);
let { levelData } = toRefs(state);
let formRef = ref();
// 弹框
function batchDelete(row) {
  state.form = row;

  //   console.log(form);
  dialog.value.open();
}
//关闭按钮
function close() {
  dialog.value.close();
}
</script>
<style scoped lang="scss"></style>
