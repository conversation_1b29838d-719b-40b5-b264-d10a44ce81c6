<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button @click="newlyAdded" class="search-button">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange">
      <template #expand="{ row }">
        <!-- <div v-if="row.childParam.length == 0" class="no-data margin-bottom10 margin-top10">暂无数据</div> -->
        <xel-table
          class="gray-table"
          :ref="(el) => setItemRef(el, row.id)"
          :columns="dictColumns"
          :load-data="operationParamChild"
          :default-params="{ parentId: row.id, searchValue: searchState.data.searchValue }"
        ></xel-table>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
    <!-- 新增子弹窗内容 -->
    <xelDialog :title="dictionaryTitle" ref="childDialogRef" size="small" @submit="submitChildForm" @close="closeChildDialog">
      <el-form :model="formChildData" :key="disabled" ref="ruleFormChild" label-width="120px" size="mini">
        <xel-form-item v-for="(citem, cindex) in formChildList" :key="cindex" v-model="formChildData[citem.prop]" v-bind="citem"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "Config",
};
</script>
<script setup>
import {
  operationParam as getTableData,
  operationParamChild,
  checkParamId,
  saveParent as addItem,
  saveParamValue,
  deleteParentValue,
  deleteParamValue,
  listParent,
} from "@/api/param/config";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let dialogText = ref("类别");
let idKey = "roleId";
let tableRef = ref();
let tableChildRefs = ref({});
function setItemRef(el, id) {
  if (el) {
    tableChildRefs.value[id] = el;
  }
}
let dictionaryTitle = ref("");
// 搜索配置
let searchState = reactive({
  data: {
    searchValue: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "searchValue",
      label: "名称/类型",
    },
  ],
});
// 搜索按钮
function search(initPage = true) {
  tableRef.value.reload(searchState.data, initPage);
}
// 重置按钮
function reset() {
  searchState.data = {
    searchValue: "",
    pageNum: 1,
    pageSize: 1,
  };
  search();
}
let loading = ref(false);
let disabled = ref(false);
let typeDisabled = ref(false);
let state = reactive({
  // 父
  formData: {
    id: "",
    name: "",
  },
  // 子
  formChildData: {
    paramId: "",
    paramValue: "",
    decription: "",
    paramName: "",
    parentId: "",
    id: "",
  },
  multipleSelection: [],
  menuData: [],
  // 弹框父
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "名称",
      required: true,
      maxlength: 100,
    },
  ],

  // 弹框子
  formChildList: [
    {
      formType: "select",
      prop: "parentId",
      label: "类别",
      disabled: disabled,
      options: [],
      required: true,
      filterable: true,
      // 调字典接口
      seleteCode: {
        code: listParent,
        resKey: "rows",
        // 传递取值的字段名
        label: "name",
        value: "id",
        params: {},
      },
    },
    {
      formType: "input",
      prop: "paramName",
      label: "名称",
      required: true,
    },
    {
      formType: "input",
      prop: "paramId",
      label: "类型",
      required: true,
      disabled: typeDisabled,
    },
    {
      formType: "input",
      prop: "paramValue",
      label: "参数值",
      required: true,
    },
    {
      formType: "input",
      prop: "decription",
      label: "描述",
      required: true,
    },
  ],
});
let { formData, formChildData, formList, formChildList } = toRefs(state);
function resetFormData(type) {
  if (type == "1") {
    state.formData = {
      id: "",
      name: "",
    };
  } else {
    state.formChildData = {
      paramId: "",
      paramValue: "",
      decription: "",
      paramName: "",
      parentId: "",
      pname: "",
    };
  }
}

// 列表配置项
const columns = [
  {
    type: "expand",
    slotName: "expand",
  },
  {
    prop: "name",
    label: "名称",
  },
  {
    label: "操作",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        hasPermi: "operation:param:edit",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton("1", scope.row);
        },
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "operation:param:remove",
        onClick(scope) {
          batchDelete("1", scope.row.id);
        },
      },
      {
        icon: "Plus",
        title: "添加运营参数",
        onClick(scope) {
          disabled.value = true;
          typeDisabled.value = false;
          newDictionary(scope.row);
        },
      },
    ],
  },
];

// 列表操作方法

let editId = ref("");
// 修改按钮
function modifyButton(type, row) {
  if (type == "1") {
    state.formData.name = row.name;
    state.formData.id = row.id;
    editId.value = row.id;
    dialogTitle.value = "编辑" + dialogText.value;
    popupBox();
  } else {
    state.formChildData.paramId = row.paramId;
    state.formChildData.paramValue = row.paramValue;
    state.formChildData.decription = row.decription;
    state.formChildData.paramName = row.paramName;
    state.formChildData.parentId = row.parentId;
    state.formChildData.id = row.paramId;
    ceditId.value = "edit";
    popupChildBox();
  }
}
// 设置默认按钮
function setDefault() {}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 批量删除
function batchDelete(type, id) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let port = type == "1" ? deleteParentValue : deleteParamValue;
    port({ paramId: id }).then(() => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      tableRef.value.table.clearSelection();

      search(false);
    });
  });
}
// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  popupBox();
}
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 子弹框
let dialogChildTitle = ref();
let childDialogRef = ref();
let ruleFormChild = ref();
// 打开子弹框
function popupChildBox() {
  childDialogRef.value.open();
}

// 提交新增/修改父类
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let params = { ...state.formData };
      if (!editId.value) {
        delete params.id;
      }
      addItem(params)
        .then((res) => {
          ElMessage({
            message: "操作成功",
            type: "success",
          });
          search(false);

          close();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}

//父弹框重置
function closeDialog() {
  nextTick(() => {
    resetFormData("1");
  });
}
//子弹框重置
function closeChildDialog() {
  nextTick(() => {
    resetFormData("2");
  });
}
//列表配置项
const dictColumns = [
  {
    prop: "",
    label: "",
    width: "50px",
  },
  {
    prop: "paramName",
    label: "名称",
  },
  {
    prop: "paramId",
    label: "类型",
  },
  {
    prop: "decription",
    label: "描述",
  },
  {
    prop: "paramValue",
    label: "参数值",
  },
  {
    label: "操作",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick(scope) {
          dictionaryTitle.value = "修改运营参数";
          disabled.value = false;
          typeDisabled.value = true;
          modifyButton("2", scope.row);
        },
      },
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          batchDelete("2", scope.row.paramId);
        },
      },
    ],
  },
];
// 新增弹框
function newDictionary(res) {
  ceditId.value = "";
  state.formChildData.parentId = res.id;
  popupChildBox();
  dictionaryTitle.value = "添加运营参数";
}
let ceditId = ref("");
// 运营参数--校验运营参数id
async function checkId(params) {
  let query = {
    id: params.id,
    paramId: params.paramId,
  };
  return checkParamId(query).then((res) => {
    return res.data.result;
  });
}
// 提交新增运营参数
function submitChildForm(close, loading) {
  ruleFormChild.value.validate(async (valid) => {
    if (valid) {
      loading();
      let params = { ...state.formChildData };

      if (ceditId.value !== "edit") {
        let isrepeat = await checkId(state.formChildData);

        if (!isrepeat) {
          ElMessage({
            type: "warning",
            message: "类型不可重复或者该类型已存在",
          });
          close(false);
          return;
        }
        delete params.id;
      }

      saveParamValue(params)
        .then((res) => {
          ElMessage.success("操作成功");
          search(false);
          close();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
</script>
<style scoped lang="scss"></style>
