<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <div class="upload-screen" v-hasPermi="'operation:screen:upload'">
      <xel-upload-dialog
        size="70px"
        exportUrl="/system/largeScreen/downloadTemplate"
        importUrl="/system/largeScreen/uploadScreen"
      ></xel-upload-dialog>
    </div>
  </el-card>
</template>
<script>
export default {
  name: "LargeScreen",
};
</script>
<script setup>
import { ref, reactive, onMounted } from "vue";
</script>

<style lang="scss" scoped>
.upload-screen {
  margin-top: 30px;
}
</style>
