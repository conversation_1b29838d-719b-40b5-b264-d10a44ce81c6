<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button @click="newlyAdded" class="search-button">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange">
      <template #status="scope">
        <el-switch v-model="scope.row.enable" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)"></el-switch>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleForm" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "Resource",
};
</script>
<script setup>
import { getTableData, getDetail, addItem, updateItem, delItem, checkFlag, updateIsLock } from "@/api/param/resource";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import { batchDelete } from "@/utils/delete";
let dialogText = ref("资源");
let idKey = "id";
let tableRef = ref();
let searchState = reactive({
  data: {
    searchValue: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "searchValue",
      label: "关键字",
      placeholder: "请输入名称或英文短名称",
      itemWidth: "50%",
      labelWidth: "5em",
    },
  ],
});
let state = reactive({
  formData: {},

  multipleSelection: [],
  menuData: [],
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    resource: "",
  };
}
function search(initPage = true) {
  tableRef.value.reload(searchState.data, initPage);
}
function reset() {
  searchState.data = {
    searchValue: "",
  };
  search(true);
}
// 列表配置项
const columns = [
  {
    prop: "enable",
    label: "",
    slotName: "status",
  },
  {
    prop: "shortname",
    label: "英文短名称",
  },
  {
    prop: "name",
    label: "名称",
  },
  {
    prop: "shorttype",
    label: "类型短名称",
  },
  {
    prop: "devtype",
    label: "设备类型",
  },
  {
    prop: "type",
    label: "类型",
  },
  {
    prop: "ip",
    label: "IP",
  },
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "auth",
    label: "认证信息",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton(scope.row[idKey]);
        },
      },
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          batchDelete([scope.row]).then(() => {
            delItem({ id: scope.row.id }).then(() => {
              ElMessage.success({
                message: "删除成功",
              });
              search(false);
            });
          });
        },
      },
    ],
  },
];

// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  resetFormData();
  popupBox();
}
let editId = ref("");
// 修改按钮
function modifyButton(id) {
  editId.value = id;
  getDetail({ id }).then(({ data }) => {
    for (let key in data) {
      state.formData[key] = data[key];
    }
    popupBox();
  });
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}

// 搜索条件
let searchData = reactive({
  name: "",
  menuName: "",
  status: "",
});
// 获取querMenu组件返回值
function selectValue(data) {
  if (data.id === -1) {
    searchData[data.parameter] = "";
  } else {
    searchData[data.parameter] = data.id;
  }
}

// 弹框
let dialogTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  dialog.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "shortname",
    label: "英文短名称",
    required: true,
  },
  {
    formType: "input",
    prop: "name",
    label: "名称",
    required: true,
  },
  {
    formType: "input",
    prop: "shorttype",
    label: "类型短名称",
    required: true,
  },
  {
    formType: "select",
    prop: "devtype",
    label: "设备类型",
    dictName: "monitor_devtype",
  },
  {
    formType: "input",
    prop: "type",
    label: "类型",
    required: true,
  },
  {
    formType: "input",
    prop: "ip",
    label: "IP",
    required: true,
    vxRule: "IP",
  },
  {
    formType: "input",
    prop: "url",
    label: "URL",
    vxRule: "URL",
  },
  {
    formType: "input",
    prop: "auth",
    label: "认证信息",
  },
]);
// 弹框确定按钮

// 提交
let submitForm = function (close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      checkFlag({ id: state.formData.id, shortname: state.formData.shortname }).then(({ data }) => {
        loading();

        if (data.result) {
          loading();
          let addFn = editId.value ? updateItem : addItem;
          addFn(state.formData)
            .then((res) => {
              search(false);

              ElMessage.success("操作成功");
              close();
            })
            .catch((res) => {
              close(false);
            });
        } else {
          close(false);

          ElMessage.warning(state.formData.shortname + "英文短名称已存在，请修改英文短名称");
        }
      });
    } else {
      return false;
    }
  });
};
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleForm.value.resetFields();
  });
  ruleForm.value.resetFields();
}

//修改状态
function handleStatusChange(row) {
  console.log("row: ", row);
  let prevenable = row.enable == 1 ? 0 : 1;
  let text = prevenable == 0 ? "启用" : "关闭";
  ElMessageBox.confirm("确认要 " + text + ' "' + row.shortname + '"吗?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let params = {
        id: row.id,
        enable: prevenable == 0 ? 1 : 0,
      };
      updateIsLock(params).then(() => {
        ElMessage({
          type: "success",
          message: text + "成功",
        });
        search(false);
      });
    })
    .catch(function () {
      row.enable = row.enable == 0 ? 1 : 0;
    });
}
</script>
<style scoped lang="scss"></style>
