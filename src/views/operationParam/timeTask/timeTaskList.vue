<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button @click="newlyAdded" class="search-button" v-hasPermi="'monitor:job:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
      <el-button @click="modifyXg" :disabled="modify" class="search-button" v-hasPermi="'monitor:job:edit'">
        <el-icon :size="12">
          <edit />
        </el-icon>
        修改
      </el-button>
      <el-button @click="batchDelete" :disabled="multiples" class="search-button" v-hasPermi="'monitor:job:remove'">
        <el-icon :size="12">
          <delete />
        </el-icon>
        批量删除
      </el-button>
      <el-button @click="journal" class="search-button">
        <el-icon :size="12">
          <message />
        </el-icon>
        日志
      </el-button>
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="getTableData"
      @selection-change="handleSelectionChange"
      :checkbox="true"
      :row-key="idKey"
    >
      <template #status="scope" v-hasPermi="'monitor:job:changeStatus'">
        <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" @change="updateStatu(scope.row)"> </el-switch>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialog" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleForm" label-width="140px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
    <!-- 详细弹框 -->
    <xelDialog title="任务详情" ref="dialogRef">
      <el-form ref="formRef" :model="form" label-width="120px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务编号：">{{ state.form.jobId }}</el-form-item>
            <el-form-item label="任务名称：">{{ state.form.jobName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务组名：">{{ state.form.jobGroupName }}</el-form-item>
            <el-form-item label="创建时间：">{{ state.form.createTime }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="cron表达式：">{{ state.form.cronExpression }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次执行时间：">{{ state.form.nextValidTime }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="调用目标字符串：">{{ state.form.invokeTarget }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态：">
              <div v-if="state.form.status == 1">正常</div>
              <div v-else-if="state.form.status == 0">暂停</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否并发：">
              <div v-if="state.form.concurrent == 0">允许</div>
              <div v-else-if="state.form.concurrent == 1">禁止</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="错误策略：">
              <div v-if="state.form.misfirePolicy == 0">默认策略</div>
              <div v-else-if="state.form.misfirePolicy == 1">立即执行</div>
              <div v-else-if="state.form.misfirePolicy == 2">执行一次</div>
              <div v-else-if="state.form.misfirePolicy == 3">放弃执行</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #button>
        <el-button round @click="close" size="mini">关闭</el-button>
      </template>
    </xelDialog>
  </el-card>
</template>
<script>
export default {
  name: "TimeTask",
};
</script>
<script setup>
import { getTableData, getDetail, addItem, updateItem, delItem, runJob, changeStatus } from "@/api/param/scheduleJob";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
let dialogText = ref("定时任务");
let idKey = "jobId";
let tableRef = ref();
let dialogRef = ref();
let searchState = reactive({
  data: {
    jobGroup: "",
    jobName: "",
    status: "",
  },
  menuData: [
    {
      lable: "任务组名：",
      prop: "jobGroup",
      options: [],
      dictName: "sys_job_group",
    },
    {
      lable: "任务状态：",
      prop: "status",
      options: [],
      dictName: "sys_job_status",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "jobName",
      label: "任务名称",
    },
  ],
});
let state = reactive({
  formData: {
    jobName: "",
    jobGroup: "",
    invokeTarget: "",
    cronExpression: "",
    concurrent: "0",
    misfirePolicy: "1",
    status: "1",
    jobId: "",
  },
  multipleSelection: [],
  menuData: [],
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    jobName: "",
    jobGroup: "",
    invokeTarget: "",
    cronExpression: "",
    concurrent: "0",
    misfirePolicy: "1",
    status: "1",
    jobId: "",
  };
}
// 搜查重置方法
function reset() {
  searchState.data = {
    jobGroup: "",
    jobName: "",
    status: "",
  };
  search();
}
// 搜索按钮
function search(initPage = true) {
  tableRef.value.reload(searchState.data, initPage);
}
// 列表配置项
const columns = [
  {
    prop: "status",
    label: "",
    slotName: "status",
  },
  {
    prop: "jobId",
    label: "任务编号",
  },
  {
    prop: "jobName",
    label: "任务名称",
  },
  {
    prop: "jobGroupName",
    label: "任务组名",
    // formatter(row, column) {
    //   return row.jobGroup == "DEFAULT" ? "默认" : "系统";
    // },
  },
  {
    prop: "invokeTarget",
    label: "调用目标字符串",
  },
  {
    prop: "cronExpression",
    label: "cron执行表达式",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "SemiSelect",
        title: "执行一次",
        onClick(scope) {
          modifyButton(scope.row);
        },
      },
      {
        icon: "More",
        title: "详细",
        onClick(scope) {
          detailed(scope.row.jobId);
        },
      },
    ],
  },
];

// 列表操作方法
// 修改状态
function updateStatu(row) {
  let query = {
    jobId: row.jobId,
    status: row.status,
  };
  ElMessageBox.confirm("是否确认修改该数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(function () {
      changeStatus(query).then((res) => {
        ElMessage({
          type: "success",
          message: "修改成功",
        });
        search(false);
      });
    })
    .catch(() => {
      search(false);
    });
}
// 新增按钮
function newlyAdded() {
  editId.value = "";
  dialogTitle.value = "添加" + dialogText.value;
  resetFormData();
  popupBox();
}
let editId = ref("");
// 执行一次
function modifyButton(row) {
  ElMessageBox.confirm("确定要立即执行一次" + '"' + `${row.jobName}` + '"' + "任务吗？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      runJob({ jobId: row.jobId, jobGroup: row.jobGroup }).then(() => {
        ElMessage({
          message: "操作成功",
          type: "success",
        });
      });
    })

    .catch((action) => {
      ElMessage({
        type: "info",
        message: "取消操作",
      });
    });
}
// 修改按钮
function modifyXg() {
  let ids = state.multipleSelection.map((item) => {
    return item["jobId"];
  });
  getDetail({ jobId: ids.join() }).then((res) => {
    state.formData.jobName = res.data.jobName;
    state.formData.jobGroup = res.data.jobGroup;
    state.formData.invokeTarget = res.data.invokeTarget;
    state.formData.cronExpression = res.data.cronExpression;
    state.formData.concurrent = res.data.concurrent;
    state.formData.misfirePolicy = res.data.misfirePolicy;
    state.formData.status = res.data.status;
    state.formData.jobId = res.data.jobId;
    editId.value = res.data.jobId;
    dialogTitle.value = "修改" + dialogText.value;

    dialog.value.open();
    // modify.value = true;
  });
}
// 日志按钮
function journal() {
  router.push({ name: "TimeTaskLog" });
}
// 列表多选
let multiples = ref(true);
let modify = ref(true);
function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
  if (val.length === 1) {
    modify.value = false;
  } else if (val.length > 1) {
    modify.value = true;
  }
}
// 批量删除
function batchDelete(arr) {
  let rows = [];
  if (Array.isArray(arr)) {
    rows = arr;
  } else {
    rows = state.multipleSelection;
  }
  let ids = rows.map((item) => {
    return item[idKey];
  });
  console.log(ids.join());
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delItem({ jobId: ids.join() }).then(() => {
      tableRef.value.reload(searchState.data, false);
      tableRef.value.table.clearSelection();
      ElMessage.success("删除成功");
    });
  });
}
let form = reactive({});
let formRef = ref();
// 详细按钮
function detailed(ref) {
  getDetail({ jobId: ref }).then((res) => {
    state.form = res.data;
    dialogRef.value.open();
  });
}
//关闭按钮
function close() {
  dialogRef.value.close();
}
// 弹框
let dialogTitle = ref("");
let dialog = ref();
let ruleForm = ref();
// 打开弹框
function popupBox() {
  dialog.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "jobName",
    label: "任务名称",
    required: true,
  },
  {
    formType: "select",
    prop: "jobGroup",
    label: "任务组名",
    required: true,
    options: [],
    dictName: "sys_job_group",
  },
  {
    formType: "input",
    prop: "invokeTarget",
    label: "调用目标字符串",
    required: true,
  },
  {
    formType: "input",
    prop: "cronExpression",
    label: "cron表达式",
    required: true,
  },
  {
    formType: "radio",
    prop: "concurrent",
    label: "是否并发",
    required: true,
    options: [
      { value: "0", label: "允许" },
      { value: "1", label: "禁止" },
    ],
  },
  {
    formType: "radio",
    prop: "misfirePolicy",
    label: "错误策略",
    required: true,
    options: [
      { value: "1", label: "立即执行" },
      { value: "2", label: "执行一次" },
      { value: "3", label: "放弃执行" },
    ],
  },
  {
    formType: "radio",
    prop: "status",
    label: "状态",
    required: true,
    options: [],
    dictName: "sys_job_status",
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
      loading();
      let addFn = editId.value ? updateItem : addItem;
      addFn(state.formData)
        .then((res) => {
          search(false);
          close();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleForm.value.resetFields();
  });
  ruleForm.value.resetFields();
}
</script>
<style scoped lang="scss"></style>
