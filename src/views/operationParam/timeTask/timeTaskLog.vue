<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button @click="delFn(state.multipleSelection)" :disabled="multiples" class="search-button">
        <el-icon :size="12">
          <delete />
        </el-icon>
        批量删除
      </el-button>
      <el-button @click="clearFn" class="search-button">
        <el-icon :size="12">
          <delete />
        </el-icon>
        清空
      </el-button>
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="getTableData"
      :checkbox="true"
      :row-key="idKey"
      @selection-change="handleSelectionChange"
    >
    </xel-table>
    <!-- 弹窗内容 -->
    <xel-dialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini"> </el-form>
    </xel-dialog>
    <!-- 详情弹框 -->
    <detail-dialog ref="detailDialogRef" title="日志详情" :list="detailList" :data="detailInfo"></detail-dialog>
  </el-card>
</template>
<script>
export default {
  name: "TimeTaskLog",
};
</script>
<script setup>
import { getLogList as getTableData, delLogs as delItem, cleanLogs } from "@/api/param/scheduleJob";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import { batchDelete } from "@/utils/delete";
let dialogText = ref("日志");
let idKey = "jobLogId";
let tableRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    status: "",
    jobName: "",
    jobGroup: "",
    timeRange: ["", ""],
  },
  menuData: [
    {
      lable: "任务组名:",
      prop: "jobGroup",
      options: [],
      dictName: "sys_job_group",
    },
    {
      lable: "状态：",
      prop: "status",
      options: [],
      dictName: "sys_job_status",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "jobName",
      label: "任务名称",
    },
    {
      formType: "date",
      type: "daterange",
      prop: "timeRange",
      label: "执行时间",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD",
    },
  ],
});
function search(initPage = true) {
  let params = {
    ...searchState.data,
    params: {
      beginTime: searchState.data.timeRange[0],
      endTime: searchState.data.timeRange[1],
    },
    // "params[beginTime]": searchState.data.timeRange[0],
    // "params[endTime]": searchState.data.timeRange[1],
  };
  delete params.timeRange;
  tableRef.value && tableRef.value.reload(params, initPage);
}
function reset() {
  searchState.data = {
    status: "",
    jobName: "",
    jobGroup: "",
    timeRange: ["", ""],
  };
  search();
}
//搜索结束

let state = reactive({
  formData: {}, //新增编辑表单
  multipleSelection: [],
});
let { formData } = toRefs(state);

// 列表配置项
const columns = [
  {
    prop: "jobName",
    label: "任务名称",
  },
  {
    prop: "jobGroupName",
    label: "任务组名",
    //     formatter(row, column) {
    //   return row.jobGroup == "DEFAULT" ? "默认" : "系统";
    // },
  },
  {
    prop: "invokeTarget",
    label: "调用目标字符串",
  },
  {
    prop: "jobMessage",
    label: "日志信息",
  },
  {
    prop: "statusName",
    label: "执行状态",
    // formatter(row, column) {
    //   return row.status == "0" ? "暂停" : "正常";
    // },
  },
  {
    prop: "createTime",
    label: "执行时间",
  },

  {
    label: "操作",
    fixed: "right",
    formatter() {
      return "详细";
    },
    click({ row }) {
      for (let attr in row) {
        detailInfo[attr] = row[attr];
      }
      detailDialogRef.value.open();
    },
  },
];
//删除，批量删除
function delFn(rows) {
  batchDelete().then(() => {
    let ids = rows.map((item) => item[idKey]);

    delItem({ ids: ids.join() }).then(() => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      tableRef.value.table.clearSelection();

      search(false);
    });
  });
}

//清空
function clearFn() {
  ElMessageBox.confirm("确定清空日志？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    cleanLogs().then((res) => {
      ElMessage({
        message: "清空成功",
        type: "success",
      });
      search(false);
    });
  });
}

// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}

// 弹框
let dialogTitle = ref("详情");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "字典类型",
    required: true,
  },
  {
    formType: "radio",
    prop: "resource",
    label: "执行状态",
    required: true,
    options: [],
    dictName: "sys_normal_disable",
  },
]);

//详情相关
let detailDialogRef = ref();
let detailInfo = reactive({});
let detailList = [
  {
    prop: "jobName",
    label: "任务名称",
  },
  {
    prop: "jobGroupName",
    label: "任务组名",
  },
  {
    prop: "invokeTarget",
    label: "调用目标字符串",
  },

  {
    prop: "statusName",
    label: "执行状态",
    // formatter(row, column) {
    //   return row.status == "1" ? "正常" : "暂停";
    // },
  },
  {
    prop: "createTime",
    label: "执行时间",
  },
  {
    prop: "jobMessage",
    label: "日志信息",
    width: "100%",
  },
];
</script>
<style scoped lang="scss"></style>
