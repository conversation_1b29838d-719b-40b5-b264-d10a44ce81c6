<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <div class="table-handler-btns">
      <el-button @click="newlyAdded" v-hasPermi="'operation:dbProperties:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </div>

    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange"> </xel-table>
    <!-- 弹窗内容 -->
    <xel-dialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xel-dialog>
  </el-card>
</template>
<script>
export default {
  name: "DataSource",
};
</script>
<script setup>
import {
  getDbPropertiesList as getTableData,
  getDbPropertiesDetail as getDetail,
  createDbProperties as addItem,
  deleteDbProperties as delItem,
} from "@/api/param/dbProperties";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let dialogText = ref("数据源");
let idKey = "roleId";
let tableRef = ref();

let state = reactive({
  formData: {},
  multipleSelection: [],
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
});
let { formData } = toRefs(state);
function resetFormData() {
  state.formData = {
    id: "",
    flag: "",
    url: "",
    username: "",
    password: "",
    remark: "",
  };
}
function reset() {
  tableRef.value.reload();
}
// 列表配置项
const columns = [
  {
    prop: "flag",
    label: "标识",
  },
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "username",
    label: "用户名",
  },
  {
    prop: "password",
    label: "密码",
  },
  {
    prop: "remark",
    label: "备注",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton(scope.row);
        },
      },
      {
        icon: "delete",
        hasPermi: "operation:dbProperties:remove",
        title: "删除",
        onClick(scope) {
          batchDelete(scope.row);
        },
      },
    ],
  },
];

// 列表操作方法
// 新增按钮
function newlyAdded() {
  formList[0].disabled = false;
  dialogTitle.value = "添加" + dialogText.value;
  resetFormData();
  popupBox();
}
let editId = ref("");
// 修改按钮
function modifyButton(data) {
  formList[0].disabled = true;
  state.formData = {
    id: data.id,
    flag: data.flag,
    url: data.url,
    username: data.username,
    password: data.password,
    remark: data.remark,
  };
  editId.value = data.id;
  getDetail({ id: data.id }).then(({ data }) => {
    popupBox();
  });
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 批量删除
function batchDelete(data) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delItem({ id: data.id }).then(() => {
        ElMessage({
          message: "删除成功",
          type: "success",
        });
        tableRef.value.table.clearSelection();

        reset();
        close();
      });
    })
    .catch(() => {
      close(false);
    });
}

// 搜索条件
let searchData = reactive({
  name: "",
  menuName: "",
  status: "",
});
// 获取querMenu组件返回值
function selectValue(data) {
  if (data.id === -1) {
    searchData[data.parameter] = "";
  } else {
    searchData[data.parameter] = data.id;
  }
}

// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "flag",
    label: "标识",
    required: true,
    disabled: false,
  },
  {
    formType: "input",
    prop: "url",
    label: "url",
    required: true,
    // vxRule: "URL",
  },
  {
    formType: "input",
    prop: "username",
    label: "用户名",
    required: true,
  },
  {
    formType: "input",
    prop: "password",
    label: "密码",
    required: true,
  },
  {
    formType: "input",
    type: "textarea",
    prop: "remark",
    label: "备注",
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let query = { ...state.formData };
      if (!editId.value) {
        delete query.id;
      }
      addItem(state.formData)
        .then((res) => {
          tableRef.value.reload({}, false);
          ElMessage.success({
            type: "success",
            message: "操作成功",
          });
          close();
        })
        .catch((res) => {
          console.log(res);
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
  ruleFormRef.value.resetFields();
}
</script>
<style scoped lang="scss">
.addbtn {
  float: right;
}
</style>
