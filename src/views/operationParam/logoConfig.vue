<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <div class="upload-logo" v-hasPermi="'system:logo:upload'">
      LOGO大图
      <xel-upload-img
        class="upload-inline"
        size="70px"
        action="/outpost-api/system/logo/uploadLogo"
        name="logo"
        :data="{ type: 'bigLogo' }"
        :success="success"
      ></xel-upload-img>
    </div>
    <div class="upload-logo" v-hasPermi="'system:logo:upload'">
      LOGO小图
      <xel-upload-img
        class="upload-inline"
        size="70px"
        action="/outpost-api/system/logo/uploadLogo"
        name="logo"
        :data="{ type: 'smallLogo' }"
        :success="success"
      ></xel-upload-img>
    </div>
  </el-card>
</template>
<script>
export default {
  name: "LogoConfig",
};
</script>
<script setup>
import { ElMessage } from "element-plus";

function success() {
  ElMessage.success("上传图片成功,请清理浏览器缓存后刷新");
}
</script>

<style lang="scss" scoped>
.upload-logo {
  float: left;
  margin: 50px 0 50px;
  width: 50%;
  color: $fontColorSoft;
}
:deep(.upload-inline) {
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
}
</style>
