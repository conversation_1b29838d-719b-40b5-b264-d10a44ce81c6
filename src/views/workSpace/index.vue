<template>
  <article class="workbench-wrapper">
    <!-- 一线 二线 -->
    <workbenchYiErXian
      v-if="userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:asset') >= 0 || userRoles.indexOf('permission:two') >= 0"
      :userRoles="userRoles"
      style="margin-bottom: 20px"
    ></workbenchYiErXian>
    <!-- 三线 -->
    <workbench-san-xian v-if="userRoles.indexOf('permission:three') >= 0" :userRoles="userRoles" style="margin-bottom: 20px"></workbench-san-xian>
    <!-- 交付经理 -->
    <!-- 前场交付经理  permission:dataCheck -->
    <workbench-jiao-fu
      v-if="
        userRoles.indexOf('permission:deliveryManager') >= 0 ||
        userRoles.indexOf('permission:operation') >= 0 ||
        userRoles.indexOf('permission:dataCheck') >= 0
      "
      :userRoles="userRoles"
      style="margin-bottom: 20px"
    ></workbench-jiao-fu>
    <!-- 管理员 -->
    <security-administrator v-if="userRoles.indexOf('permission:customerManager') >= 0" :userRoles="userRoles"></security-administrator>
  </article>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import WorkbenchYiErXian from "./components/workbenchYiErXian.vue";
import WorkbenchSanXian from "./components/workbenchSanXian.vue";
import workbenchJiaoFu from "./components/workbenchJiaoFu.vue";
import SecurityAdministrator from "./components/securityAdministrator.vue";
import { useStore } from "vuex";
const store = useStore();
let userRoles = computed(() => {
  return store.state.roles;
});
</script>

<style lang="scss" scoped></style>
