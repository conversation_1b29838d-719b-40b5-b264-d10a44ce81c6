<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="计算设备资产" name="underlying">
      <Underlying v-if="activeName == 'underlying'" :isDialog="isDialog" :isDialogType="isDialogType" @connect="getAsetsData"></Underlying>
    </el-tab-pane>
    <el-tab-pane label="终端资产" name="terminal">
      <Terminal v-if="activeName == 'terminal'" :isDialog="isDialog" :isDialogType="isDialogType" @connect="getAsetsData"></Terminal>
    </el-tab-pane>
  </el-tabs>
  <!-- 资产合并详情界面 -->
  <xel-dialog title="资产合并" ref="mergeDetailDialog" width="80%" @close="closeDialog" :ishiddenDialog="true">
    <merge-asset-detail v-if="detailShow" :mergeData="mergeData.data" @close="closeDetail"></merge-asset-detail>
  </xel-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
// 引入终端资产、计算设备资产列表界面
import Underlying from "@/views/securityAssets/underlying/index.vue";
import Terminal from "@/views/securityAssets/terminal/index.vue";
import MergeAssetDetail from "./mergeAssetDetail.vue";
let props = defineProps({
  isDialog: {
    type: Boolean,
    default: true,
  },
  isDialogType: {
    type: String,
    default: "workbench",
  },
  ipUuid: {
    type: String,
    default: "",
  },
});
let activeName = ref("underlying");
let mergeData = reactive({
  data: {
    ipUuid: props.ipUuid,
    assetId: "",
    assetType: "",
  },
});
// 获取资产列表传出数据
let detailShow = ref(false);
let mergeDetailDialog = ref();
function getAsetsData(data) {
  mergeData.data.assetId = data.id;
  mergeData.data.assetType = data.type;
  detailShow.value = true;
  mergeDetailDialog.value.open();
}
function closeDialog() {
  detailShow.value = false;
  mergeDetailDialog.value.close();
}
function closeDetail(type) {
  closeDialog();
  if (type === "save") {
    emits("close");
  }
}
let emits = defineEmits(["close"]);
</script>

<style scoped lang="scss"></style>
