<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="anData"> <businessCard :analyst="analyst"></businessCard> </el-card>
    </el-col>
    <el-col :span="18">
      <el-card class="anData">
        <ul>
          <li>
            <p class="giveAn">本年度相关事件数</p>
            <p class="quanNA">{{ onAllow.eventCountYear }}</p>
          </li>
          <li>
            <p class="giveAn">月度相关事件数</p>
            <p class="quanNA">{{ onAllow.eventCountMonth }}</p>
          </li>
          <li>
            <p class="giveAn">本年度待闭环事件数</p>
            <p class="quanNA">{{ onAllow.eventStayCountYear }}</p>
          </li>
          <li>
            <p class="giveAn">待处置任务数</p>
            <p class="quanNA">{{ onAllow.eventTaskStayCount }}</p>
          </li>
          <li>
            <div ref="indexChart" style="width: 100%; height: 128px"></div>
          </li>
        </ul> </el-card
    ></el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="12">
      <el-card class="cardAdd">
        <ul>
          <li>
            <p class="giveAn">本年度相关漏洞数量</p>
            <p class="quanNA">{{ onAllow.vulnCountYear }}</p>
          </li>
          <li>
            <p class="giveAn">月度漏洞新增量</p>
            <p class="quanNA">{{ onAllow.vulnCountMonth }}</p>
          </li>
          <li>
            <p class="giveAn">待处置漏洞数</p>
            <p class="quanNA">{{ onAllow.vulnStayCountYear }}</p>
            <ul class="level-data">
              <li>
                <span class="level-color" :style="{ background: Level_Color.urgent }"></span><span>{{ onAllow.urgentCount }}</span>
              </li>
              <li>
                <span class="level-color" :style="{ background: Level_Color.hight }"></span><span>{{ onAllow.highCount }}</span>
              </li>
              <li>
                <span class="level-color" :style="{ background: Level_Color.middle }"></span><span>{{ onAllow.midCount }}</span>
              </li>
              <li>
                <span class="level-color" :style="{ background: Level_Color.low }"></span><span>{{ onAllow.lowCount }}</span>
              </li>
            </ul>
          </li>
          <!-- <li>
            <div ref="pieChart" style="width: 100%; height: 128px"></div>
          </li> -->
        </ul>
      </el-card>
    </el-col>
    <el-col :span="12">
      <el-card class="cardAdd">
        <ul>
          <li>
            <p class="giveAn">业务系统资产总数</p>
            <p class="quanNA">{{ onRes.bussAssetsCount }}</p>
          </li>
          <li>
            <p class="giveAn">基础资源资产总数</p>
            <p class="quanNA">{{ onRes.resAssetsCount }}</p>
          </li>
          <li>
            <p class="giveAn">关联业务系统基础资源数</p>
            <p class="quanNA">{{ onRes.resAssetsRelBussCount }}</p>
          </li>
          <!-- <li></li> -->
        </ul>
      </el-card>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col>
      <el-card>
        <ul>
          <li>
            <p class="giveAn">漏洞影响业务资产总数</p>
            <p class="quanNA">{{ onRes.bussAssetsRelVulnCount }}</p>
          </li>
          <li>
            <p class="giveAn">漏洞影响基础资源总数</p>
            <p class="quanNA">{{ onRes.resAssetsRelVulnCount }}</p>
          </li>
          <li>
            <p class="giveAn">无主资产数</p>
            <p class="quanNA">{{ onRes.noPersonAssetsCount }}</p>
          </li>
          <li>
            <p class="giveAn">无主漏洞数</p>
            <p class="quanNA">{{ onRes.noPersonVulnCount }}</p>
          </li>
          <li>
            <div ref="onexChart" style="width: 100%; height: 128px"></div>
          </li>
        </ul>
      </el-card>
    </el-col>
  </el-row>
  <el-card>
    <manageList :buttName="'待处置事件任务'"></manageList>
  </el-card>
  <el-card class="cardON">
    <manageList :buttName="'分析中相关事件'"></manageList>
  </el-card>
  <el-card>
    <manageList :buttName="'待处置漏洞'"></manageList>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import * as echarts from "echarts";
import BusinessCard from "./businessCard.vue";
import { Level_Color } from "@/config/constant";
import { selectWorkBenchData, assetsAndVulnStatistics, getEventTaskStay, noPersonAssets } from "@/api/workSpace/securityManagement.js";
import { useStore } from "vuex";
import ManageList from "./manageList.vue";
let indexChart = ref();
let props = defineProps({
  userRoles: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let onAllow = ref({});
let onexChart = ref();
let onRes = ref({});
let pieChart = ref();
function management() {
  selectWorkBenchData().then((res) => {
    onAllow.value = res.data;
  });
  assetsAndVulnStatistics().then((res) => {
    onRes.value = res.data;
  });
  noPersonAssets().then((res) => {
    onMyChart = echarts.init(onexChart.value);
    onOption = {
      grid: {
        x: 30,
        y: 10,
        x2: 10,
        y2: 20,
        borderWidth: 1,
        containLabel: true,
      },
      tooltip: {
        formatter: function (params) {
          return `${params.name}<br/>${`当月无主资产数`}：${params.value}`;
        },
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: res.data.month,
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          name: "Email",
          type: "line",
          stack: "Total",
          data: res.data.allEvent,
        },
      ],
    };
    onMyChart.setOption(onOption);
  });
}
management();
const store = useStore();
let userInfo = computed(() => {
  return store.state.userInfo;
});
let analyst = ref("");
if (props.userRoles.indexOf("permission:customerManager") >= 0) {
  analyst.value = "安全管理员";
}
let myChart = null;
let option = reactive({});
// 柱状图
onMounted(() => {
  myChart = echarts.init(indexChart.value);
  option = {
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: false,
            fontSize: "40",
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: "Search Engine" },
          { value: 735, name: "Direct" },
        ],
      },
    ],
  };
  myChart.setOption(option);
});

let onMyChart = null;
let onOption = reactive({});

// 饼图
</script>
<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}
ul {
  display: flex;
  li {
    width: 20%;
  }
}
.quanNA {
  font-size: 36px;
  font-weight: 600;
  color: #28334f;
  margin-top: 30px;
}
.level-data {
  margin-top: 10px;
  display: flex;
  li {
    margin-right: 20px;
  }
}
.level-color {
  margin-right: 10px;
}
.cardON {
  margin: 20px 0 20px 0;
}
.cardAdd {
  height: 178px;
  ul {
    display: flex;
    li {
      width: 33%;
    }
  }
}
.anData {
  height: 194px;
}
</style>
