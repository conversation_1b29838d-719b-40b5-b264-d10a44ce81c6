<template>
  <el-row :gutter="20">
    <el-col :span="4" class="left">
      <el-card>
        <template #header>
          <div class="card-header" style="padding: 0px 20px">
            <span style="line-height: 38px">任务列表</span>
            <el-button class="button pull-right" type="text" @click="addPlanTask">
              <el-icon><plus /></el-icon>
            </el-button>
            <div class="clearfix"></div>
          </div>
        </template>
        <div>
          <ul class="executor-list">
            <li
              v-for="item in executorData"
              :key="item.id"
              @click="executorClick(item.executorId)"
              :class="choseList.indexOf(item.executorId) >= 0 ? 'executorChose' : ''"
            >
              {{ item.executorName }}
            </li>
          </ul>
        </div>
      </el-card>
    </el-col>
    <el-col :span="20">
      <el-card>
        <full-calendar ref="fullCalendar" :ids="ids" @getTaskDetail="getFullCalenDetail"></full-calendar>
      </el-card>
    </el-col>
  </el-row>
  <xel-dialog :title="planTaskDialogTitle" ref="planTaskDialog" :ishiddenDialog="true" @close="closeAddPlanTask" size="mini">
    <div :class="{ 'is-disabled': isHistoryTask }">
      <div v-if="addShow" class="form">
        <el-form :model="state.formData" ref="ruleFormRef" label-width="120px" size="mini">
          <xel-form-item
            v-for="(item, index) in state.formList"
            itemWidth="calc(100% - 30px)"
            :key="index"
            v-model="state.formData[item.prop]"
            v-bind="item"
            :disabled="isHistoryTask"
          >
            <template #default>
              <div class="cycleType-box" v-if="item.prop === 'cycleType'">
                <span v-if="state.formData.cycleUserDefinedType == '0'">每{{ state.formData.cycleInterval }}天重复</span>
                <span v-if="state.formData.cycleUserDefinedType == '1'">每{{ state.formData.cycleInterval }}周 {{ state.text }}</span>
                <span v-if="state.formData.cycleUserDefinedType == '2'">每{{ state.formData.cycleInterval }}月 {{ state.text }}</span>
                <el-checkbox
                  :disabled="isHistoryTask"
                  size="mini"
                  true-label="1"
                  false-label="0"
                  v-model="state.formData.isWorkday"
                  label="是否考虑工作日"
                />
              </div>
              <template v-else-if="item.prop === 'taskDate'">
                <el-time-picker
                  v-model="state.formData.taskTime"
                  is-range
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 55%; margin-left: 5%"
                  :clearable="false"
                  :disabled="isHistoryTask"
                />
              </template>
            </template>
          </xel-form-item>
        </el-form>
      </div>
      <div class="text-right" style="padding-right: 20px">
        <!-- <el-button type="danger">任务删除</el-button> -->
        <el-popover ref="taskPopover" v-model:visible="visible" placement="top" :width="400" trigger="click" v-if="planTaskType == 'edit'">
          <p>确认删除任务吗?</p>
          <div style="text-align: right; margin: 20px 0px 0px 0px">
            <el-button size="small" type="primary" @click="deletePlanTask('0')">只删除当前</el-button>
            <el-button size="small" @click="deletePlanTask('1')">删除当前及后续</el-button>
            <el-button size="small" @click="visible = false">取消</el-button>
          </div>
          <template #reference>
            <div style="width: 80px">
              <el-button v-if="!isHistoryTask" type="danger" class="pull-left" @click.stop="showTaskPopover">任务删除</el-button>
            </div>
          </template>
        </el-popover>
        <el-button v-if="!isHistoryTask" type="primary" @click="savePlanTask">保存</el-button>
        <el-button @click="closePlanTaskDialog">{{ isHistoryTask ? "关闭" : "取消" }}</el-button>
      </div>
    </div>
  </xel-dialog>
  <xel-dialog title="重复周期选择" ref="choseDialog" size="mini" @submit="saveCycleType" @close="resetCycleType">
    <el-form label-width="120px" ref="cycleForm">
      <el-form-item label="重复">
        <el-select v-model="cycleState.data.cycleUserDefinedType" @change="getCycleType">
          <el-option value="0" label="每天重复"></el-option>
          <el-option value="1" label="每周重复"></el-option>
          <el-option value="2" label="每月重复"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="间隔">
        <el-select v-model="cycleState.data.cycleInterval">
          <el-option value="1" label="1"></el-option>
          <el-option value="2" label="2"></el-option>
          <el-option value="3" label="3"></el-option>
          <el-option value="4" label="4"></el-option>
          <el-option value="5" label="5"></el-option>
          <el-option value="6" label="6"></el-option>
          <el-option value="7" label="7"></el-option>
        </el-select>
        <span v-if="cycleState.data.cycleUserDefinedType == '0'"> 天</span>
        <span v-if="cycleState.data.cycleUserDefinedType == '1'"> 周</span>
        <span v-if="cycleState.data.cycleUserDefinedType == '2'"> 月</span>
      </el-form-item>
      <el-form-item label="">
        <div v-if="cycleState.data.cycleUserDefinedType == '1'" class="">
          <el-checkbox-group v-model="cycleState.data.interval">
            <el-checkbox-button :label="1">一</el-checkbox-button>
            <el-checkbox-button :label="2">二</el-checkbox-button>
            <el-checkbox-button :label="3">三</el-checkbox-button>
            <el-checkbox-button :label="4">四</el-checkbox-button>
            <el-checkbox-button :label="5">五</el-checkbox-button>
            <el-checkbox-button :label="6">六</el-checkbox-button>
            <el-checkbox-button :label="7">日</el-checkbox-button>
          </el-checkbox-group>
        </div>
        <div v-if="cycleState.data.cycleUserDefinedType == '2'" class="">
          <el-checkbox-group v-model="cycleState.data.interval">
            <el-checkbox-button class="margin-top10" v-for="item in 31" :key="item" :label="item">{{ item }}</el-checkbox-button>
          </el-checkbox-group>
        </div>
        <p class="margin-top10">
          <span v-if="cycleState.data.cycleUserDefinedType == '1'">每{{ cycleState.data.cycleInterval }}周</span>
          <span v-if="cycleState.data.cycleUserDefinedType == '2'">每{{ cycleState.data.cycleInterval }}月</span>
          {{ cycleState.text2 }}
        </p>
      </el-form-item>
    </el-form>
  </xel-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { downloadUrl } from "@/api/system/download.js";
import { download } from "@/plugins/request";
import { parseTime } from "@/utils/ruoyi";
import {
  selectWorkBenchUserTree,
  loadScheduleTaskExecutorData,
  saveScheduleTask,
  selectScheduleTaskDetail,
  deleteScheduleTask,
  checkCyclicNewTask,
} from "@/api/workSpace/workbench";
import FullCalendar from "./fullCalendar.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useStore } from "vuex";

const store = useStore();

let ids = ref("");
let emits = defineEmits(["countTask"]);
let fullCalendar = ref();
// 验证是否删除
let taskPopover = ref();
function showTaskPopover() {
  checkCyclicNewTask({ id: state.formData.id }).then((res) => {
    if (res.data.result == false) {
      ElMessage.warning("该任务是当前循环组最新任务，删除任务后会重新创建，请终止任务循环后删除");
    } else {
      visible.value = true;
    }
  });
}
// 获取执行人参与人树
let userTreeData = ref([]);
let addShow = ref(false);
function getWorkbenchTree() {
  selectWorkBenchUserTree().then((res) => {
    userTreeData.value = res.data;
    userTreeData.value.forEach((item) => {
      item.disabled = true;
    });
  });
}
// 获取执行人
let executorData = ref([]);
function getExecutorData() {
  loadScheduleTaskExecutorData().then((res) => {
    executorData.value = res.data.executorList;
    emits("countTask", res.data.taskCount);
  });
}
getWorkbenchTree();
getExecutorData();
// 点击选中效果
let choseList = ref([]);
function executorClick(id) {
  ids.value = "";
  let index = choseList.value.indexOf(id);
  if (index >= 0) {
    choseList.value.splice(index, 1);
  } else {
    choseList.value.push(id);
  }
  ids.value = choseList.value.join(",");
}
// 新建任务
let planTaskDialogTitle = ref("");
let planTaskDialog = ref();
let planTaskType = ref("add");
function addPlanTask() {
  addShow.value = true;
  planTaskDialogTitle.value = "新建任务";

  planTaskType.value = "add";
  resetAddTask();
  state.formList[state.formList.length - 1].isShow = false;
  setTimeout(() => {
    state.formList[state.formList.length - 1].fileListFa = [];
    state.formList[state.formList.length - 1].isShow = true;
  }, 500);
  planTaskDialog.value.open();
}
function closePlanTaskDialog() {
  planTaskDialog.value.close();
}
// 编辑
function getFullCalenDetail(id) {
  // 传值任务ID
  selectScheduleTaskDetail(id).then((res) => {
    resetAddTask();
    let data = res.data;
    state.formData.title = data.title;
    state.formData.taskDate = data.begainTimeStr;
    state.formData.taskTime = [new Date(data.begainTimeStr), new Date(data.endTimeStr)];

    state.formData.begainTimeStr = data.begainTimeStr;
    state.formData.endTimeStr = data.endTimeStr;
    state.formData.cycleType = data.cycleType;
    state.formData.cycleUserDefinedType = data.cycleUserDefinedType;
    state.formData.cycleInterval = data.cycleInterval;
    state.formData.intervalStr = data.intervalStr;
    state.formData.taskGroup = data.taskGroup;
    state.formData.isWorkday = data.isWorkday;
    if (data.intervalStr) {
      state.formData.interval = data.intervalStr.split(",").map(Number);
    }

    state.formData.type = data.type;
    state.formData.executorId = data.executorId;
    state.formData.description = data.description;
    state.formData.partakeUserIds = data.partakeUserIds;
    if (data.partakeUserIds) {
      state.formData.partakeUserIdsList = data.partakeUserIds.split(",");
    }
    let arr = state.formData.interval.sort(function (a, b) {
      return a - b;
    });
    state.text = "";
    if (state.formData.cycleUserDefinedType == "1") {
      for (let i = 0; i < arr.length; i++) {
        state.text = state.text + " " + arr2[arr[i] - 1];
      }
      state.text = state.text + " 重复";
    } else if (state.formData.cycleUserDefinedType == "2") {
      for (let i = 0; i < arr.length; i++) {
        if (state.text == "") {
          state.text = state.text + arr[i] + "号";
        } else {
          state.text = state.text + " , " + arr[i] + "号";
        }
      }
      state.text = state.text + " 重复";
    }
    state.formData.level = data.level;
    state.formData.uploadDetailReportFile = "";
    state.formList[state.formList.length - 1].fileListFa = [];
    state.formData.detailReportId = data.detailReportId;

    setTimeout(() => {
      if (data.detailReportId !== "" && data.detailReportId !== null) {
        state.formList[state.formList.length - 1].fileListFa.push({
          name: data.detailReportName,
          url: data.detailReportId,
          uid: data.detailReportId,
        });
      }
      state.formList[state.formList.length - 1].isShow = true;
    }, 500);

    state.formData.id = data.id;
    addShow.value = true;
    planTaskDialogTitle.value = "编辑任务";
    planTaskType.value = "edit";

    planTaskDialog.value.open();
  });
}
function resetAddTask() {
  state.formData = {
    title: "",

    begainTimeStr: "",
    endTimeStr: "",
    taskTime: "",
    taskDate: "",
    cycleType: "",
    cycleUserDefinedType: "",
    cycleInterval: "",
    intervalStr: "",
    type: "",
    description: "",
    executorId: "",
    partakeUserIds: "",
    partakeUserIdsList: [],
    level: "0",
    uploadDetailReportFile: "",
    detailReportId: "",
    id: "", /// 任务id 修改时传入
    isUpdateNext: "",
    interval: [],
    file_upload: [],
    isWorkday: 0,
  };
  state.formList[state.formList.length - 1].isShow = false;
}
// 关闭弹窗时  清空值
function closeAddPlanTask() {
  resetAddTask();
}
// 新建、修改任务表单内容
let state = reactive({
  formData: {
    title: "",
    taskDate: "",
    taskTime: [],

    begainTimeStr: "",
    endTimeStr: "",
    cycleType: "",
    cycleUserDefinedType: "",
    cycleInterval: "",
    intervalStr: "",
    type: "",
    description: "",
    executorId: "",
    partakeUserIds: "",
    partakeUserIdsList: [],
    level: "0",
    uploadDetailReportFile: "",
    detailReportId: "",
    id: "", /// 任务id 修改时传入
    isUpdateNext: "",
    interval: [],
    file_upload: [],
    isWorkday: 0,
  },
  text: "",
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "任务标题",
      maxlength: "100",
      required: true,
    },
    {
      formType: "date",
      day: true,
      prop: "taskDate",
      label: "任务时间",
      required: true,
      width: "40%",
      disabledDate(time) {
        if (!store.state.permissions.includes("workflow:editHistoryTask")) {
          return time.getTime() + 24 * 60 * 60 * 1000 < Date.now();
        }
      },
      onChange: () => {
        if (state.formData.id == "" && state.formData.taskTime.length != 2) {
          state.formData.taskTime = [new Date(2016, 9, 10, 0, 0), new Date(2016, 9, 10, 23, 59, 59)];
        }
      },
    },

    {
      formType: "select",
      prop: "cycleType",
      label: "是否重复",
      width: "50%",
      options: [
        {
          label: "不重复",
          value: "0",
        },
        {
          label: "每日重复",
          value: "1",
        },
        {
          label: "每周重复",
          value: "2",
        },
        {
          label: "每月重复",
          value: "3",
        },
        {
          label: "每年重复",
          value: "4",
        },
        // {
        //   label: "工作日",
        //   value: "5",
        // },
        {
          label: "自定义重复",
          value: "6",
        },
      ],
      onChange(val) {},
      onOptionClick(val) {
        if (val == 6) {
          cycleState.data.cycleUserDefinedType = state.formData.cycleUserDefinedType;
          cycleState.data.cycleInterval = state.formData.cycleInterval ? state.formData.cycleInterval : "1";
          cycleState.data.intervalStr = state.formData.intervalStr;
          cycleState.data.interval = state.formData.interval;
          cycleState.text2 = state.text;
          choseDialog.value.open();
        } else {
          state.formData.cycleUserDefinedType = "";
          state.formData.cycleInterval = "";
          state.formData.intervalStr = "";
          state.formData.interval = [];
          state.text = "";
        }
      },
      required: true,
    },
    {
      formType: "select",
      prop: "type",
      label: "类型",
      options: [
        {
          label: "普通",
          value: "0",
        },
        {
          label: "值班",
          value: "1",
        },
        {
          label: "请假",
          value: "2",
        },
      ],
      required: true,
    },
    {
      formType: "input",
      type: "textarea",
      prop: "description",
      label: "描述",
      maxlength: "500",
      required: true,
    },
    {
      formType: "tree",
      prop: "executorId",
      label: "执行人",
      multiple: false,
      required: true,
      showIcon: false,
      // treeOptions: {
      //   loadData: selectWorkBenchUserTree, //接口名称
      //   params: {},
      // },
      treeData: userTreeData,
      treeProps: {
        id: "id",
        label: "label",
        children: "children",
      },
      onNodeClick(data) {},
    },
    {
      formType: "tree",
      prop: "partakeUserIdsList",
      label: "参与人",
      multiple: true,
      required: true,
      showIcon: false,
      treeData: userTreeData,
      // treeOptions: {
      //   loadData: selectWorkBenchUserTree, //接口名称
      //   params: {},
      // },
      treeProps: {
        id: "id",
        label: "label",
        children: "children",
      },
      onNodeClick(data) {},
    },
    {
      formType: "select",
      prop: "level",
      label: "优先级",
      dictName: "schedule_task_level",
    },
    {
      isShow: true,
      formType: "upload",
      prop: "file_upload",
      label: "上传文件",
      accept: ".doc,.docx,.pdf,.xlsx",
      limit: 1,
      fileListFa: [],
      multiple: false,
      onFileList(list) {
        state.formData.uploadDetailReportFile = list[0];
        state.formData.detailReportId = "";
      },
      onPreview(list) {
        downLoadFile(list.url, list.name);
      },
    },
  ],
});
watch(
  () => state.formData.taskDate,
  (val) => {
    let taskDate = state.formData.taskDate.split(" ");
    if (taskDate.length > 1) {
      state.formData.taskDate = taskDate[0];
    }
  }
);
watchEffect(() => {
  if (state.formData.taskTime.length == 2) {
    state.formData.begainTimeStr = state.formData.taskDate + " " + parseTime(state.formData.taskTime[0], "{h}:{i}:{s}");

    state.formData.endTimeStr = state.formData.taskDate + " " + parseTime(state.formData.taskTime[1], "{h}:{i}:{s}");
  }
});
let isHistoryTask = ref(false);
watchEffect(() => {
  if (!store.state.permissions.includes("workflow:editHistoryTask")) {
    isHistoryTask.value = false;
    if (state.formData.id && state.formData.taskDate) {
      if (new Date(state.formData.taskDate).getTime() + 24 * 60 * 60 * 1000 < new Date().getTime()) {
        isHistoryTask.value = true;
      }
    }
  }
});
// 保存任务
let ruleFormRef = ref();
function savePlanTask() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      state.formData.partakeUserIds = state.formData.partakeUserIdsList.join(",");
      let formData = new FormData();
      console.log("state.formData.uploadDetailReportFile: ", state.formData.uploadDetailReportFile);
      if (
        state.formData.uploadDetailReportFile !== "" &&
        state.formData.uploadDetailReportFile !== undefined &&
        state.formData.detailReportId == ""
      ) {
        formData.append("uploadDetailReportFile", state.formData.uploadDetailReportFile.raw);
        formData.append("detailReportName", state.formData.uploadDetailReportFile.name);
      }

      delete state.formData["uploadDetailReportFile"];

      for (const key in state.formData) {
        // 获取当前值
        const currentData = state.formData[key];

        // 对于空值进行过滤
        if (currentData === "") {
          continue;
        }
        if (key == "taskDate" || key == "taskTime") continue;
        if (key == "isWorkday") {
          formData.append(key, currentData || 0);
        } else {
          formData.append(key, currentData);
        }
      }

      if (state.formData.id == "") {
        saveScheduleTask(formData).then((res) => {
          ElMessage.success("操作成功");
          fullCalendar.value.seleteFullCalendar();
          planTaskDialog.value.close();
          getExecutorData();
        });
      } else {
        ElMessageBox.confirm("此次修改是否要影响后续的任务？", "信息", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          closeOnClickModal: false,
          type: "info",
        })
          .then(() => {
            formData.append("isUpdateNext", "Y");
          })
          .catch(() => {
            formData.append("isUpdateNext", "N");
          })
          .then(() => {
            saveScheduleTask(formData).then((res) => {
              ElMessage.success("操作成功");
              fullCalendar.value.seleteFullCalendar();
              planTaskDialog.value.close();
              getExecutorData();
            });
          });
      }
    }
  });
}
// 重复周期选择
let cycleState = reactive({
  data: {
    cycleUserDefinedType: "0",
    cycleInterval: "1",
    intervalStr: "",
    interval: [],
  },
  text1: "天",
  text2: "",
});
let choseDialog = ref();
// 监听选中的值
watch(
  () => cycleState.data.interval,
  (newVal, oldVal) => {
    let arr = newVal.sort(function (a, b) {
      return a - b;
    });
    cycleState.text2 = "";

    if (cycleState.data.cycleUserDefinedType == "1") {
      for (let i = 0; i < arr.length; i++) {
        cycleState.text2 = cycleState.text2 + " " + arr2[arr[i] - 1];
      }
      cycleState.text2 = cycleState.text2 + " 重复";
    } else if (cycleState.data.cycleUserDefinedType == "2") {
      for (let i = 0; i < arr.length; i++) {
        if (cycleState.text2 == "") {
          cycleState.text2 = cycleState.text2 + arr[i] + "号";
        } else {
          cycleState.text2 = cycleState.text2 + " , " + arr[i] + "号";
        }
      }
      cycleState.text2 = cycleState.text2 + " 重复";
    }
    cycleState.data.intervalStr = "";
    cycleState.data.intervalStr = newVal.join(",");
  }
);
let arr2 = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
function getCycleType() {
  cycleState.data.interval = [];
}
function saveCycleType() {
  // if(cycleState.data.cycleUserDefinedType == '0')
  state.formData.cycleUserDefinedType = cycleState.data.cycleUserDefinedType;
  state.formData.cycleInterval = cycleState.data.cycleInterval;
  state.formData.intervalStr = cycleState.data.intervalStr;
  state.formData.interval = cycleState.data.interval;
  state.text = cycleState.text2;
  choseDialog.value.close();
}
function resetCycleType() {
  cycleState.data = {
    cycleUserDefinedType: "",
    cycleInterval: "",
    intervalStr: "",
    interval: [],
  };
  // state.formData.cycleType = "1";
  cycleState.text1 = "";
}
// 任务删除
let visible = ref(false);
function deletePlanTask(type) {
  let params = {
    id: state.formData.id,
    status: type,
  };
  deleteScheduleTask(params).then((res) => {
    ElMessage.success("操作成功");
    fullCalendar.value.seleteFullCalendar();
    planTaskDialog.value.close();
    getExecutorData();
    visible.value = false;
  });
}
// 文件下载
function downLoadFile(fileId, fileName) {
  download(downloadUrl + fileId, fileName, {}, "get");
}
</script>

<style scoped lang="scss">
.executor-list {
  width: 60%;
  color: $fontColor;
  font-weight: 400;
  > li {
    line-height: 32px;
    margin-bottom: 10px;
    padding: 5px 20px;
    cursor: pointer;
  }
  .executorChose {
    background: #ffe7d4;
    border-radius: 0px 100px 100px 0px;
  }
}
:deep(.left) {
  .el-card__body {
    padding-left: 0px;
    padding-right: 0px;
  }
}
.choseMonthOrWeek {
  width: 100%;
  text-align: center;
  display: flex;
  align-content: flex-start;
  flex-flow: row wrap;
  .list {
    flex: 0 0 25%;
    margin-bottom: 20px;
  }
}
:deep(.el-checkbox-button:last-child, ) {
  .el-checkbox-button__inner {
    border-radius: 0px;
  }
}
:deep(.el-checkbox-button:first-child) {
  .el-checkbox-button__inner {
    border-radius: 0px;
  }
}
:deep(.el-checkbox-button__inner) {
  border-radius: 0px;
  border-left: 1px solid #dcdfe6;
  margin-right: -2px;
  width: 42px;
  text-align: center;
}
.cycleType-box {
  display: inline-block;

  & > span {
    margin-left: 25px;
  }
  label {
    transform: translateY(2px);
    margin: 0 30px;
  }
}
</style>
