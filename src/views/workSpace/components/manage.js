import router from "@/router";
let loopholeColumns = [
  {
    prop: "spare1",
    label: "事件名称",
  },
  {
    prop: "taskType",
    label: "当前阶段",
  },
  {
    prop: "title",
    label: "任务名称",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    prop: "assignee",
    label: "当前处置人",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "DArrowRight",
        title: "查看",
        onClick(scope) {
          router.push({
            name: "EventDetail",
            params: {
              id: scope.row.eventId,
            },
            query: {
              taskId: scope.row.id,
              taskName: scope.row.title,
              isManageTask: scope.row.isManageTask,
            },
          });
        },
      },
    ],
  },
];
let eventColumns = [
  {
    prop: "title",
    label: "事件名称",
  },
  {
    prop: "taskType",
    label: "当前阶段",
  },
  {
    prop: "levelName",
    label: "事件等级",
  },

  {
    prop: "createTimeStr",
    label: "事件提报时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "DArrowRight",
        title: "查看",
        onClick(scope) {
          router.push({
            name: "EventDetail",
            params: {
              id: scope.row.id,
            },
          });
        },
      },
    ],
  },
];
let PendingColumns = [
  {
    prop: "type",
    label: "漏洞类型",
  },
  {
    prop: "title",
    label: "漏洞标题/名称",
  },
  {
    prop: "levelName",
    label: "漏洞等级",
  },

  {
    prop: "submitTimeStr",
    label: "发现时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "DArrowRight",
        title: "查看",
        onClick(scope) {
          router.push(
            scope.row.type == "业务系统漏洞"
              ? { name: "VulnBusinessDetail", params: { id: scope.row.id } }
              : { name: "VulnBasicDetail", params: { id: scope.row.id } }
          );
        },
      },
    ],
  },
];
export { loopholeColumns, eventColumns, PendingColumns };
