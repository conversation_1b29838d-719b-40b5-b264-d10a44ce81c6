<template>
  <el-row :gutter="70">
    <el-col :span="10">
      <div class="title-bottom-line">
        <p>新增信息</p>
      </div>
      <el-form :model="formData" ref="ruleFormRef" label-width="80px" size="mini" class="addInfo">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item">
          <el-button class="btn" size="small" @click="change(item)" v-if="formData[item.prop]">
            <el-icon :size="12"> <ArrowRightBold /></el-icon>
          </el-button>
        </xel-form-item>
      </el-form>
    </el-col>
    <!-- 摘要 -->
    <el-col :span="14">
      <div class="title-bottom-line">
        <p>摘要</p>
      </div>
      <el-row class="onname">
        <el-form ref="leftForm" class="base-info-form" style="width: 100%">
          <div class="pull-left asset_detail_class">
            <el-form-item label="资产对象类型：">业务系统资产</el-form-item>
            <el-form-item label="等级保护级别：">{{ store.abstract.levelName }}</el-form-item>
            <el-form-item label="责任人：">{{ userNames }}</el-form-item>
          </div>
          <div class="pull-left asset_detail_class">
            <el-form-item label="资产对象名称：">{{ store.abstract.name }}</el-form-item>
            <el-form-item label="责任主体：">{{ store.abstract.deptName }}</el-form-item>
          </div>
        </el-form>
      </el-row>
      <div v-if="store.domainList.length > 0">
        <div class="title-bottom-line">
          <p>系统入口</p>
        </div>
        <p v-for="item in store.domainList" :key="item.id" class="inp">{{ item.domain }}</p>
      </div>
      <div v-if="store.portsServiceList.length > 0">
        <div class="title-bottom-line">
          <p>互联网IP、端口与服务组</p>
        </div>
        <ip-port :list="store.portsServiceList" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
      </div>

      <div v-if="store.portsName.length > 0">
        <div class="title-bottom-line">
          <p>局域网IP、端口与服务组</p>
        </div>
        <ip-port :list="store.portsName" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
      </div>
    </el-col>
  </el-row>
  <xel-dialog title="添加端口号及服务协议" ref="URLRef" @submit="addNumber" @close="closeDialog">
    <dynamic ref="dynamicIpRef" label-width="6em" :formList="formListIp" :data-rows="formAdd" v-if="dynamicIpstatus"></dynamic>
  </xel-dialog>
  <div class="text-center margin-top20">
    <el-button @click="emits('close')">取消</el-button>
    <el-button type="primary" @click="saveAssetMerge">变更资产</el-button>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import {
  getAssetsConfirmedList,
  openDomainNotConfirm,
  ignoreDomainNotConfirmAsset,
  changeDomainNotConfirmAssets,
} from "@/api/securityAssets/confirm";
import { getdetail } from "@/api/securityAssets/business";
import { getDicts } from "@/api/system/dict/data";
let props = defineProps({
  assetId: {
    type: String,
    default: "",
  },
  rightData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
// 传值
let emits = defineEmits(["close"]);
let domain = ref("");
let uid = ref("");
console.info(props.assetId);
let formAdd = ref([]);
let store = reactive({
  abstract: {},
  domainList: [],
  portsServiceList: [],
  portsName: [],
  spare1: [],
  spare2: [],
});
// 弹框内容
let formListIp = reactive([
  {
    formType: "number",
    prop: "port",
    label: "端口",
    itemWidth: "calc((97% - 70px) / 2 )",
    value: null,
    min: 0,
    max: 65535,
    precision: "0",
  },
  {
    formType: "select",
    prop: "serverAgreement",
    label: "服务协议",

    dictName: "service_agreement",
    filterable: true,
    itemWidth: "calc((97% - 70px) / 2 )",
    value: "",
  },
]);
//
let valId = ref("");
function saveAssetMerge() {
  let val = {
    id: valId.value,
    spare1: store.spare1
      .map((item) => {
        return item.join("#_portServer_#");
      })
      .join("#_ssp_#"),
    spare2: store.spare2
      .map((item) => {
        return item.join("#_portServer_#");
      })
      .join("#_ssp_#"),
    domain: domain.value,
    spare3: uid.value,
  };
  if (val.spare1 !== "" || val.domain !== "" || val.spare2 !== "") {
    ElMessageBox.confirm("确定要变更资产吗？", "信息", {
      distinguishCancelAndClose: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      changeDomainNotConfirmAssets(val).then(() => {
        ElMessage({
          message: "操作成功",
          type: "success",
        });
        emits("close", "save");
      });
    });
  } else {
    ElMessage({
      type: "info",
      message: "请先添加合并项！",
    });
  }
}
let formList = reactive([
  {
    formType: "input",
    prop: "domain",
    label: "URL",
    disabled: true,
    isShow: true,
  },
  {
    formType: "input",
    prop: "internetIp",
    label: "互联网IP",
    disabled: true,
    isShow: true,
  },
  {
    formType: "input",
    prop: "lanIp",
    label: "局域网IP",
    disabled: true,
    isShow: true,
  },
]);
let formData = reactive({
  lanIp: "",
  internetIp: "",
  domain: "",
});

function getAssetRightDetail(val, data) {
  valId.value = props.rightData.id;
  getdetail({ id: props.rightData.id }).then((res) => {
    console.info(res);
    store.abstract = { ...res.data.business, auList: res.data.business.auList };
    store.domainList = res.data.business.domainList;
    let arr = res.data.business.portsServiceList;
    arr.forEach((item) => {
      item.type = item.serviceAgreementName;
    });
    arr.forEach((item) => {
      if (item.networkType === "1") {
        // 互联网
        store.portsServiceList.push(item);
      } else {
        // 局域网
        store.portsName.push(item);
      }
    });
  });
}
let userNames = computed(() => {
  return store.abstract.auList ? store.abstract.auList.map((item) => item.userName).join() : "";
});
// 弹框打开
function ipAssets() {
  uid.value = props.assetId;
  openDomainNotConfirm({ id: props.assetId }).then((res) => {
    formData.domain = res.data.domain;
    formData.internetIp = res.data.internetIp;
    formData.lanIp = res.data.lanIp;
  });
  // dialogRef.value.open();
}
getAssetRightDetail();
ipAssets();
let dynamicIpRef = ref();
let URLRef = ref();
let activeIPType = "";
let dynamicIpstatus = ref(false);
function addNumber() {
  for (let item of dynamicIpRef.value.list) {
    if (item[0].value == undefined || item[1].value == "") {
      ElMessage.warning(item[0].value == undefined ? "请输入端口" : "请输入服务协议");
      return false;
    }
  }

  // 本地添加的数据
  let addData = [];
  let result = false;
  dynamicIpRef.value.list.forEach((item) => {
    addData.push({ ip: activeIPType == "internetIp" ? formData.internetIp : formData.lanIp, port: item[0].value, typeId: item[1].value });
  });

  // 判断是否存在重复
  if (activeIPType == "internetIp") {
    // 互联网
    result = existFn(addData, store.portsServiceList);
  } else {
    // 局域网
    result = existFn(addData, store.portsName);
  }
  if (result) {
    if (result) {
      ElMessage({
        type: "warning",
        message: "存在重复,请重新填写",
      });
    }
    return;
  }
  //互联网IP
  if (activeIPType == "internetIp") {
    dynamicIpRef.value.list.forEach((item) => {
      store.portsServiceList.push({ ip: formData.internetIp, port: item[0].value, type: getDictText(item[1].value) });
      store.spare1.push([formData.internetIp, item[0].value, item[1].value]);
      URLRef.value.close();
      dynamicIpstatus.value = false;
      formList[1].isShow = false;
    });
  } else {
    dynamicIpRef.value.list.forEach((item) => {
      store.portsName.push({ ip: formData.lanIp, port: item[0].value, type: getDictText(item[1].value) });
      store.spare2.push([formData.lanIp, item[0].value, item[1].value]);
      URLRef.value.close();
      dynamicIpstatus.value = false;
      formList[2].isShow = false;
    });
  }
}
// 判断是否添加端口和协议重复 比较已经存在的和新添加的数据
function existFn(addData, lineData) {
  // 先判断addData 本地添加数据
  let result = false;
  let locResult = false;
  let lineResult = false;
  locResult = addData.some((item, index, array) =>
    array.slice(index + 1).find((other) => item.ip == other.ip && item.port == other.port && item.typeId == other.typeId)
  );
  // 本地无重复，与线上数据比较
  if (!locResult && lineData.length > 0) {
    lineResult =
      addData.filter((row) => !lineData.some((item) => item.ip == row.ip && item.port === row.port && item.typeId == row.typeId)).length > 0
        ? false
        : true;
  }

  if (locResult || lineResult) {
    result = true;
  } else {
    result = false;
  }

  return result;
}

// 左右切换
function change(data) {
  formAdd.value = [];
  if (data.label == "URL") {
    formList[0].isShow = false;
    domain.value = formData.domain;
    store.domainList.push({
      domain: formData.domain,
    });
  } else {
    activeIPType = data.prop;
    URLRef.value.open();
    dynamicIpstatus.value = true;
  }
}
// 获取字典值内容
let typeOptions = ref([]);
// 获取字典值
function getDictValyes() {
  getDicts("service_agreement").then((res) => {
    console.info(res);
    typeOptions.value = res.data;
  });
}
getDictValyes();
function getDictText(value) {
  let selecteditem = typeOptions.value.find((item) => {
    return item.dictValue == value;
  });

  return selecteditem.dictLabel;
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.onname {
  p {
    margin: 20px;
  }
}
.inp {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf1;
  margin-left: 20px;
}
.addInfo {
  :deep .el-form-item__content {
    display: flex;
  }
  .btn {
    margin-left: 20px;
  }
}
.magin-auto {
  margin-left: 20px;
}
.formWrapper {
  display: flex;
}
:deep(.ip-port-list) {
  & > li {
    width: 100%;
  }
}
.asset_detail_class {
  width: 50%;
}
@media screen and (max-width: 1900px) {
  .asset_detail_class {
    width: 50%;
  }
}
@media screen and (max-width: 1500px) {
  .asset_detail_class {
    width: 100%;
  }
}
</style>
