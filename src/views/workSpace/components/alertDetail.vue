<template>
  <!-- 告警详情弹窗 -->
  <div class="title-bottom-line AlertSearchBtn">
    <p>告警信息</p>
    <AlertSearchBtn :detailData="state.detail" />
  </div>
  <div class="">
    <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
      <el-row>
        <el-col :span="12">
          <el-form-item label="告警编号：">{{ state.detail.alertNo }}</el-form-item>
          <!-- <el-form-item label="告警级别：">{{ state.detail.priorityStr }}</el-form-item> -->
          <el-form-item label="告警级别：">
            <el-tag :type="levelData[state.detail.priority]">{{ state.detail.priorityStr }}</el-tag>
          </el-form-item>
          <el-form-item label="设备类型：">{{ state.detail.devType }}</el-form-item>
          <el-form-item label="首轮告警发生时间：">{{ state.detail.createTime }}</el-form-item>
          <el-form-item label="首条日志目的地址：">{{ state.detail.cdstip }}</el-form-item>
          <el-form-item label="事件摘要：" v-if="typeStr !== 'jf'">{{ state.detail.ceventdigest }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="告警名称：">{{ state.detail.title }}</el-form-item>
          <el-form-item label="告警发生设备：">{{ state.detail.devName }}</el-form-item>
          <el-form-item
            v-if="state.detail.stage !== null && state.detail.stage !== '' && state.detail.stage > 1"
            :label="state.detail.stage + '轮告警发生时间：'"
          >
            <p v-if="state.detail.stage == 2">
              {{ state.detail.createTime2 }}
            </p>
            <p v-else-if="state.detail.stage == 3">
              {{ state.detail.createTime3 }}
            </p>
            <p v-else-if="state.detail.stage == 4">
              {{ state.detail.createTime4 }}
            </p>
            <p v-else>
              {{ state.detail.stageTime }}
            </p>
          </el-form-item>
          <el-form-item label="首条日志源地址：">{{ state.detail.csrcip }}</el-form-item>
          <el-form-item label="告警内容描述：">{{ state.detail.description }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="title-bottom-line margin-top20">
    <p>报送状态</p>
  </div>
  <div class="" style="margin-bottom: 20px; padding: 10px">
    <p v-if="state.detail.lunci !== null && state.detail.lunci !== ''" class="baosong">
      该告警为第{{ state.detail.lunci }}轮告警，当前共计{{ state.detail.num }}次告警
    </p>
    <p v-if="state.detail.lunci === null || state.detail.lunci === ''" class="baosong">该告警还未分配，当前共计{{ state.detail.num }}次告警</p>
  </div>
  <div class="title-bottom-line">
    <p>辅助分析</p>
  </div>
  <div class="">
    <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
      <el-row>
        <el-col :span="12">
          <el-form-item label="绑定域名：">{{ state.detail.reverseDomains }}</el-form-item>
          <el-form-item label="情报置信度：">{{ state.detail.threatScore }}</el-form-item>
          <el-form-item label="可疑度：">{{ state.detail.score }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="情报类型：">{{ state.detail.categories }}</el-form-item>
          <el-form-item label="位置：">{{ state.detail.location }}</el-form-item>
          <el-form-item label="推断原因：">{{ state.detail.reason }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="title-bottom-line margin-top20">
    <p>关联资产</p>
  </div>
  <div class="" style="margin-bottom: 20px; padding: 10px">
    <div v-if="state.detail.assetsList !== undefined && state.detail.assetsList !== '[]' && state.detail.assetsList.length > 0">
      <div v-for="item in state.detail.assetsList" :key="item.id" class="pull-left btn-margin-left" style="width: 60%; margin: 5px 0px">
        <p class="pDetail">
          {{ item.name }} <span class="pull-right">{{ item.ips }}</span>
        </p>
      </div>
    </div>
    <p v-else>无相关资产</p>
  </div>
  <div class="clearfix"></div>
  <div class="title-bottom-line margin-top20">
    <p>疑似关联事件</p>
  </div>
  <div class="" style="margin-bottom: 20px; padding: 10px">
    <div v-if="state.detail.eventList !== undefined && state.detail.eventList !== '[]' && state.detail.eventList.length > 0" style="overflow: hidden">
      <div v-for="item in state.detail.eventList" :key="item.id" class="pull-left btn-margin-left" style="width: 60%; margin: 5px 0px">
        <p class="pDetail">
          {{ item.eventTitle }}
        </p>
      </div>
    </div>
    <p v-else>无疑似关联事件</p>
  </div>

  <div v-if="state.deleteWaitingConfirm && typeStr === 'jf'">
    <div class="title-bottom-line margin-top20">
      <p>申请删除原因</p>
    </div>
    <div class="" style="margin-bottom: 20px; padding: 10px">
      <div>{{ state.workbenchAlertDel.applyReason }}</div>
    </div>
  </div>
  <div class="clearfix"></div>

  <!-- 误报原因  -->
  <div class="title-bottom-line margin-top20" v-if="getworkbenchList.length">
    <p>误报原因</p>
  </div>
  <div class="" style="margin-bottom: 20px; padding: 10px" v-if="getworkbenchList.length">
    <div style="overflow: hidden">
      <xel-table ref="tableRef" v-if="getworkbenchList.length" :columns="columns" :data="getworkbenchList" :pagination="false"> </xel-table>
    </div>
  </div>
  <div class="clearfix"></div>

  <div class="text-right margin-top20 margin-right20 margin-bottom20">
    <el-button
      v-if="state.applyDelete && typeStr !== 'jf' && (!state.detail.delStatus || state.detail.delStatus != '已申请删除')"
      type="danger"
      @click="applyDeleteAlert('delete')"
    >
      <el-icon><delete-filled /></el-icon>申请删除
    </el-button>
    <el-button v-if="state.deleteWaitingConfirm && typeStr === 'jf'" @click="deleteAlert" type="danger">
      <el-icon><delete-filled /></el-icon>删除
    </el-button>
    <el-button
      v-if="(userRoles.indexOf('permission:three') >= 0 || userRoles.indexOf('permission:deliveryManager') >= 0) && typeStr === 'jf'"
      @click="rejectAlertInfo"
      type="danger"
    >
      <el-icon><delete-filled /></el-icon>驳回
    </el-button>
    <el-button
      type="danger"
      v-if="(userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0) && typeStr !== 'jf'"
      @click="applyDeleteAlert('wubao')"
    >
      <el-icon><circle-close /></el-icon>误报
    </el-button>
    <el-button v-hasPermi="'search:query:view'" @click="traceDetail">
      <el-icon><position /></el-icon>
      追溯
    </el-button>
    <el-button
      v-if="(userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0) && typeStr !== 'jf'"
      @click="aelrtAddEvent(state.detail.id, state.detail.cdstip, state.detail.csrcip)"
    >
      <svg class="icon" aria-hidden="true" style="margin-right: 5px; font-size: 14px">
        <use xlink:href="#icon-tijiao"></use></svg
      >提报事件
    </el-button>
    <el-button
      v-if="(userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0) && typeStr !== 'jf'"
      @click="alertAddtoEvent()"
    >
      <el-icon><plus /></el-icon>告警追加
    </el-button>
  </div>
  <!-- 申请删除告警/误报原因 -->
  <xel-dialog :title="dialogTitle" ref="alertChange" @submit="saveApplyDeleteAlert" @close="closeDelteAlert">
    <el-form ref="alertForm" label-width="120px" :model="state.dataForm" :rules="state.rules">
      <el-form-item :label="formTitle" prop="value">
        <el-input v-model="state.dataForm.value" :placeholder="placeholderText" maxlength="500" type="textarea" rows="5"></el-input>
      </el-form-item>
    </el-form>
  </xel-dialog>
  <!-- 追加告警时间选择列表 -->
  <xel-dialog title="选择事件" ref="addToEvent" width="60%" @submit="alertAddTask" @close="closeAddToEvent">
    <event-list v-if="addToEventShow" compType="workAlert" :isComp="true" ref="eventList"></event-list>
  </xel-dialog>
  <xel-dialog title="提报事件任务" ref="addToEventTask" width="60%" @submit="addTask" @close="closeAddTask">
    <add-event-task ref="addEventTask"></add-event-task>
  </xel-dialog>
  <!-- 提报事件选择模板 -->
  <xel-dialog :title="eventDialogTitle" ref="choseEventTemplate" :ishiddenDialog="true" width="60%" @close="closeAddEvent">
    <add-event
      v-if="addEventShow"
      :alertAddEvent="addEventState.data"
      @toParentDialogTItle="getDialogTitle"
      addType="WorkbenchAlert"
      @close="colse_event"
    ></add-event>
  </xel-dialog>
  <el-drawer v-model="traceDrawer" :before-close="handleCloseDetail">
    <alarm-trace-detail v-if="traceDrawer" ref="log_detail" :traceData="traceData.data"></alarm-trace-detail>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import {
  selectarningAwaitingConfirmatio,
  deleteWaitingConfirm,
  applyWaitingConfirm,
  misWarning,
  saveEventAlertTask,
  rejectApplyWorkbenchAlertInfo,
} from "@/api/workSpace/alert";
import { batchDelete } from "@/utils/delete";
import { ElMessage, ElMessageBox } from "element-plus";
import EventList from "@/views/event/threatEvent/eventList.vue";
import AddEventTask from "./addEventTask.vue";
import AddEvent from "@/views/event/threatEvent/components/addEvent.vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { Level_priority } from "@/config/constant";
// 追溯详情界面
import alarmTraceDetail from "@/views/event/alarmTraceDetail.vue";
import AlertSearchBtn from "./alertSearchBtn.vue";
const store = useStore();
let userRoles = computed(() => {
  return store.state.roles;
});
const router = useRouter();
let props = defineProps({
  alertId: {
    type: String,
    default: () => {
      return "";
    },
  },

  /* 新增 - 类型 - 增加来源类型，
   * erxian  二线工作台
   * jf 交付工作台
   *  */
  typeStr: {
    type: String,
    default: () => {
      return "erxian";
    },
  },
});

/* 新增 - 误报原因 */
const columns = [
  {
    prop: "userName",
    label: "操作人",
  },
  {
    prop: "endTimeStr",
    label: "操作时间",
  },
  {
    prop: "misreportReason",
    label: "误报原因",
  },
  {
    prop: "stage",
    label: "轮次",
  },
  {
    prop: "createTime",
    label: "发生时间",
  },
  {
    prop: "num",
    label: "告警次数",
  },
];
let getworkbenchList = ref([]);

let eventDialogTitle = ref("选择事件模板");
function getDialogTitle(title) {
  eventDialogTitle.value = title;
}
let state = reactive({
  detail: {},
  levelData: Level_priority,
  workbenchAlertDel: {},
  // 权限
  deleteWaitingConfirm: false,
  applyDelete: false,
  // 申请删除原因
  dataForm: {
    value: "",
  },
  rules: {
    value: [
      {
        required: true,
        message: "请输入",
        trigger: "blur",
      },
    ],
  },
});
function getAlertDetail() {
  selectarningAwaitingConfirmatio(props.alertId).then((res) => {
    let data = res.data;
    state.detail = data.record;
    state.workbenchAlertDel = data.workbenchAlertDel;
    state.applyDelete = data.applyDelete;
    state.deleteWaitingConfirm = data.deleteWaitingConfirm;
    if (Number(data.record.stage) > 4 && data.record.space1) {
      const space1Array = data.record.space1.split(";");
      state.detail["stageTime"] = space1Array[space1Array.length - 1];
    }
    /* 误报原因 */
    getworkbenchList.value = data.record.alertMisInfos ? data.record.alertMisInfos : [];
  });
}

let { levelData } = toRefs(state);
// 删除告警
function deleteAlert() {
  batchDelete().then((res) => {
    let param = {
      ids: state.detail.id,
    };
    deleteWaitingConfirm(param).then((res) => {
      ElMessage.success("删除成功");
      emits("close");
    });
  });
}
// 驳回告警
function rejectAlertInfo() {
  ElMessageBox.confirm(`确认驳回吗？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let param = {
      id: state.detail.id,
    };
    rejectApplyWorkbenchAlertInfo(param).then((res) => {
      ElMessage.success("驳回成功");
      emits("close");
    });
  });
}
// 申请删除告警
let alertChange = ref();
let alertForm = ref();
let formTitle = ref("");
let dialogTitle = ref("");
let placeholderText = ref("");
let alertType = ref("");
function applyDeleteAlert(type) {
  alertChange.value.open();
  alertType.value = type;
  if (type === "delete") {
    formTitle.value = "申请原因";
    dialogTitle.value = "申请删除";
    placeholderText.value = "请输入申请删除原因";
    state.rules.value[0].message = "请输入申请删除原因";
  } else if (type === "wubao") {
    formTitle.value = "误报原因";
    dialogTitle.value = "告警误报";
    placeholderText.value = "请输入误报原因";
    state.rules.value[0].message = "请输入误报原因";
  }
  if (alertForm.value) {
    alertForm.value.clearValidate();
  }
}
function saveApplyDeleteAlert() {
  alertForm.value.validate((valid) => {
    if (valid) {
      let param = {};
      if (alertType.value === "delete") {
        param = {
          ids: state.detail.id,
          applyReason: state.dataForm.value,
        };
        applyWaitingConfirm(param).then((res) => {
          ElMessage.success("提交成功");
          emits("close");
        });
      } else if (alertType.value === "wubao") {
        param = {
          id: state.detail.id,
          misreportReason: state.dataForm.value,
        };
        misWarning(param).then((res) => {
          ElMessage.success("操作成功");
          emits("close");
        });
      }
    }
  });
}
function closeDelteAlert() {
  alertChange.value.close();
  state.dataForm.value = "";
  formTitle.value = "";
  dialogTitle.value = "";
  alertType.value = "";
}
// 告警追加
let addToEvent = ref();
let addToEventShow = ref(false);
function alertAddtoEvent() {
  addToEventShow.value = true;
  alertEventState.data.alertId = state.detail.id;
  addToEvent.value.open();
}
let eventList = ref();
let alertEventState = reactive({
  data: {},
});
function alertAddTask() {
  if (eventList.value.eventId !== "") {
    alertEventState.data.eventId = eventList.value.eventId;
    addToEventTask.value.open();
  } else {
    ElMessage.warning("请选择事件");
  }
}
function closeAddToEvent() {
  alertEventState.data.eventId = "";
  addToEventShow.value = false;
}
let addEventTask = ref();
let addToEventTask = ref();
function addTask() {
  addEventTask.value.ruleFormRef.validate((valid) => {
    if (valid) {
      let data = addEventTask.value.formData;
      alertEventState.data.title = data.title;
      alertEventState.data.taskGroup = data.taskGroup;
      alertEventState.data.detail = data.detail;
      alertEventState.data.isManageTask = "0";
      alertEventState.data.sourceId = "2";
      saveEventAlertTask(alertEventState.data).then((res) => {
        ElMessage.success("告警追加成功");
        addToEventTask.value.close();
        addToEvent.value.close();
        emits("close");
        router.push({
          name: "EventDetail",
          params: {
            id: res.eventId,
          },
          query: {
            taskId: res.taskId,
            taskName: data.title,
            isManageTask: "0",
          },
        });
      });
    }
  });
}
function closeAddTask() {
  alertEventState.data.title = "";
  alertEventState.data.taskGroup = "";
  alertEventState.data.detail = "";
}
// 传值
let emits = defineEmits(["close"]);
onMounted(() => {
  getAlertDetail();
});
// 告警提事件
let choseEventTemplate = ref();
let addEventShow = ref(false);
let addEventState = reactive({
  data: {},
});
function aelrtAddEvent(alertId, cdstip, csrcip) {
  addEventState.data = {
    alertId: alertId,
    cdstip: cdstip,
    csrcip: csrcip,
    sourceId: 2,
    firstTaskId: "",
    samplingRecordId: "",
    handleFrom: "",
  };
  addEventShow.value = true;
  choseEventTemplate.value.open();
}
function closeAddEvent() {
  addEventShow.value = false;
}
// colse_event
function colse_event() {
  // choseEventTemplate.value.close();
  emits("close");
}
let traceDrawer = ref(false);
let traceData = reactive({
  data: {
    cruleid: "",
    alertId: "",
    alertTime: "",
  },
});
// 告警追溯
function traceDetail() {
  traceDrawer.value = true;
  traceData.data.cruleid = state.detail.cruleid.split("_")[1];
  traceData.data.alertId = state.detail.alertNo;
  traceData.data.alertTime = state.detail.createTime;
}
function handleCloseDetail() {
  traceDrawer.value = false;
}
</script>

<style lang="scss" scoped>
.AlertSearchBtn {
  display: flex;
  justify-content: space-between;
}
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.pDetail {
  border: 1px solid #e2e2e4;
  box-shadow: none;
  color: #797979;
  font-size: 12px;
  width: 100%;
  display: block;
  width: 100%;
  min-height: 34px;
  padding: 6px 12px;
  border-radius: 3px;
  word-break: break-all;
  line-height: 22px;
}
.baosong {
  margin: 0px 20px;
  width: calc(100% - 40px);
  border-bottom: 1px solid #ebedf1;
  height: 62px;
  line-height: 62px;
}
:deep(.baseInfo) {
  margin-top: 10px;
  border: none;
}
</style>
