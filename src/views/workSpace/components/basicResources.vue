<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="PendingColumns"
      :load-data="selectStayVulnList"
      :default-params="{
        type: route.params.id == 0 ? 'business' : 'resources ',
      }"
    >
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "BasicResources",
};
</script>
<script setup>
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
onActivated(() => {
  search(false);
});
// import { loopholeColumns, eventColumns, PendingColumns } from "./manage";
import { getEventTaskStay, getAnalysisingEvent, selectStayVulnList } from "@/api/workSpace/securityManagement.js";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
let columns = ref();
let tableRef = ref();

const PendingColumns = [
  {
    prop: "type",
    label: "漏洞类型",
  },
  {
    prop: "title",
    label: "漏洞标题/名称",
  },
  {
    prop: "levelName",
    label: "漏洞等级",
  },

  {
    prop: "submitTimeStr",
    label: "发现时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "DArrowRight",
        title: "查看",
        onClick(scope) {
          router.push(
            route.params.id == 0
              ? { name: "VulnBusinessDetail", params: { id: scope.row.id } }
              : { name: "VulnBasicDetail", params: { id: scope.row.id } }
          );
        },
      },
    ],
  },
];
//搜索相关
let searchState = reactive({
  data: {
    title: "",
    level: "",
  },
  menuData: [
    {
      lable: "漏洞等级",
      prop: "level",
      options: [],
      dictName: "event_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "漏洞标题/名称",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    title: "",
    level: "",
  };
  search();
}
</script>
<style scoped lang="scss"></style>
