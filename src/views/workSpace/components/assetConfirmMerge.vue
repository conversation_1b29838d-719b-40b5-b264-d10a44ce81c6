<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="业务系统资产" name="business" v-if="type == 'url'">
      <business v-if="activeName == 'business'" :isDialog="isDialog" :isDialogType="isDialogType" @connect="getAsetsData"></business>
    </el-tab-pane>
    <el-tab-pane label="计算设备资产" name="underlying" v-if="type == 'ip'">
      <Underlying v-if="activeName == 'underlying'" :isDialog="isDialog" :isDialogType="isDialogType" @connect="getAsetsData"></Underlying>
    </el-tab-pane>
    <el-tab-pane label="终端资产" name="terminal" v-if="type == 'ip'">
      <Terminal v-if="activeName == 'terminal'" :isDialog="isDialog" :isDialogType="isDialogType" @connect="getAsetsData"></Terminal>
    </el-tab-pane>
    <!-- 合并操作内容 -->
    <xel-dialog title="资产合并" ref="mergeAssetDialog" width="80%" :ishiddenDialog="true">
      <asset-confirm-merge-ip
        v-if="type == 'ip' && mergerShow"
        :assetId="assetId"
        :rightData="rightData"
        @close="closeDialog"
      ></asset-confirm-merge-ip>
      <asset-confirm-merge-url
        v-if="type == 'url' && mergerShow"
        :assetId="assetId"
        :rightData="rightData"
        @close="closeDialog"
      ></asset-confirm-merge-url>
    </xel-dialog>
  </el-tabs>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import business from "@/views/securityAssets/businessAssets/index.vue";
import Underlying from "@/views/securityAssets/underlying/index.vue";
import Terminal from "@/views/securityAssets/terminal/index.vue";
// 域名URL资产合并操作内容
import AssetConfirmMergeUrl from "./assetConfirmMergeUrl.vue";
// ip待确认资产黑帮详情
import AssetConfirmMergeIp from "./assetConfirmMergeIp.vue";
let emits = defineEmits(["close"]);
let props = defineProps({
  type: {
    type: String,
    default: "",
  },
  assetId: {
    type: String,
    default: "",
  },
});
let activeName = props.type == "url" ? "business" : "underlying";
let isDialogType = ref("workbench");
let isDialog = ref(true);
function handleClick() {}
//  选择合并的事件后传值
let mergeAssetDialog = ref();
let rightData = ref({});
let mergerShow = ref();
function getAsetsData(data) {
  console.info(data);
  rightData.value = data;
  mergerShow.value = true;
  mergeAssetDialog.value.open();
}
function closeDialog(data) {
  if (data) {
    emits("close");
  }
  rightData.value = {};
  mergerShow.value = false;
  mergeAssetDialog.value.close();
}
</script>

<style scoped lang="scss"></style>
