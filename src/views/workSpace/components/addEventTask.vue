<template>
  <el-form :model="state.formData" ref="ruleFormRef" label-width="120px" size="mini">
    <xel-form-item v-for="(item, index) in state.formList" :key="index" v-model="state.formData[item.prop]" v-bind="item"></xel-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";

let state = reactive({
  formData: {
    title: "",
    taskGroup: "",
    detail: "",
  },
  formList: [
    {
      formType: "input",
      prop: "title",
      required: true,
      label: "任务名称",
    },
    {
      formType: "select",
      required: true,
      prop: "taskGroup",
      label: "任务阶段",
      dictName: "task_group",
    },
    {
      formType: "editor",
      prop: "detail",
      label: "任务要求",
      onEditorValue(val) {
        state.formData.detail = val;
      },
    },
  ],
});
let ruleFormRef = ref();
// 重置表单内容
function resetForm() {
  state.formData = {
    title: "",
    taskGroup: "",
    detail: "",
  };
}
defineExpose({
  ruleFormRef: computed(() => {
    return ruleFormRef.value;
  }),
  formData: computed(() => {
    return state.formData;
  }),
  resetForm,
});
</script>

<style lang="scss" scoped></style>
