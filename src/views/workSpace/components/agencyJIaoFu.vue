<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <el-card class="card">
        <div class="card_title">资产审核</div>
        <div class="margin-bottom20">
          <div class="card_first pointer" @click="openAssetAuditDetail(auditAssetState.first)" v-if="JSON.stringify(auditAssetState.first) !== '{}'">
            <p>{{ auditAssetState.first.spare1 }}</p>
            <p>
              <span v-if="auditAssetState.first.confirmType == '1'">新增资产</span>
              <span v-if="auditAssetState.first.confirmType == '2'">合并资产</span>
              <span v-if="auditAssetState.first.confirmType == '3'">忽略资产</span>
              <span>{{ minToDate(auditAssetState.first.count) }}</span>
              <span class="clearfix"></span>
            </p>
          </div>
          <div class="card_count eventTask">{{ auditAssetState.total }}</div>
          <div class="clearfix"></div>
        </div>
        <el-table :data="auditAssetState.data" @row-click="openAssetAuditDetail">
          <el-table-column prop="spare1" label="资产名称"></el-table-column>
          <el-table-column prop="" label="操作类型">
            <template #default="scope">
              <span v-if="scope.row.confirmType == '1'">新增资产</span>
              <span v-if="scope.row.confirmType == '2'">合并资产</span>
              <span v-if="scope.row.confirmType == '3'">忽略资产</span>
            </template>
          </el-table-column>
          <el-table-column label="时长">
            <template #default="scope">
              <span
                v-if="scope.row.count < parseInt(auditAssetState.time.firstInterval)"
                :class="scope.row.count < auditAssetState.time.firstInterval ? '' : ''"
                >{{ minToDate(scope.row.count) }}
              </span>
              <span
                v-else-if="scope.row.count > parseInt(auditAssetState.time.secondInterval)"
                :class="scope.row.count > auditAssetState.time.secondInterval ? 'work_time' : ''"
                >{{ minToDate(scope.row.count) }}
              </span>
              <span
                v-else
                :class="
                  scope.row.count > parseInt(auditAssetState.time.firstInterval) && scope.row.count < parseInt(auditAssetState.time.secondInterval)
                    ? 'work_time1'
                    : ''
                "
                >{{ minToDate(scope.row.count) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-col>
    <el-col :span="12">
      <el-card class="card">
        <div class="card_title">资产确认</div>
        <div class="margin-bottom20">
          <div class="card_first pointer" @click="assetConfirm(assetConfirmState.first)" v-if="JSON.stringify(assetConfirmState.first) !== '{}'">
            <p>{{ assetConfirmState.first.name }}</p>
            <p>
              <span>{{ assetConfirmState.first.type == "0" ? "域名待确认资产" : "IP待确认资产" }}</span>
              <span></span>
              <span class="clearfix"></span>
            </p>
          </div>
          <div class="card_count asset pointer" @click="toConfirmAssetList">{{ assetConfirmState.total }}</div>
          <div class="clearfix"></div>
        </div>
        <el-table :data="assetConfirmState.data" @row-click="assetConfirm">
          <el-table-column prop="name" label="资产名称"></el-table-column>
          <el-table-column prop="type" label="资产类型">
            <template #default="scope">
              <span v-if="scope.row.type == '0'">域名待确认资产</span>
              <span v-if="scope.row.type == '1'">IP待确认资产</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-col>
    <el-col :span="12" class="margin-top20">
      <el-card class="card">
        <div class="card_title">已申请删除的告警</div>
        <div class="margin-bottom20">
          <div class="card_first pointer" @click="openAlertDetail(warningState.first)" v-if="JSON.stringify(warningState.first) !== '{}'">
            <p>{{ warningState.first.title }}</p>
            <p>
              <span>{{ warningState.first.applyTimeStr }}</span>
              <span class="asset">{{ warningState.first.userName }}</span>
              <span class="clearfix"></span>
            </p>
          </div>
          <div class="card_count alert pointer" @click="toPendingWarnings">{{ warningState.total }}</div>
          <div class="clearfix"></div>
        </div>
        <el-table ref="warningTable" :data="warningState.data" @row-click="openAlertDetail">
          <el-table-column label="告警名称" prop="title"></el-table-column>
          <el-table-column label="发生时间" prop="applyTimeStr"></el-table-column>
          <el-table-column label="申请人" prop="userName"></el-table-column>
        </el-table>
      </el-card>
    </el-col>
  </el-row>
  <!-- 资产审核弹出 -->
  <xel-dialog title="待审核资产信息" ref="auditAssetDialog" @close="closeAuditAssetDialog" :ishiddenDialog="true" width="80%">
    <asset-audit v-if="assetAuditShow" :assetData="assetAuditDetail.data" @close="closeAuditAssetDialog"></asset-audit>
  </xel-dialog>
  <xel-dialog title="处理资产" ref="assetDialog" width="60%" @close="closeAssetDialog" :ishiddenDialog="true">
    <asset-confirm-detail v-if="assetDetailShow" :assetData="assetData.data" @close="closeAssetDetail"></asset-confirm-detail>
  </xel-dialog>
  <!-- 已申请删除告警详情 -->
  <xel-dialog title="已申请删除的告警" ref="alertDialog" width="60%" @close="closeAlertDialog" :ishiddenDialog="true">
    <alert-detail v-if="alertDetailShow" :alertId="alertId" typeStr="jf" @close="closeAlertDetail"></alert-detail>
  </xel-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { selectAuditAssetList, selectNotConfirmAssetsList, selectApplyAlertList } from "@/api/workSpace/workbench";
import AssetAudit from "./assetAudit.vue";
import assetConfirmDetail from "./assetConfirmDetail.vue";
import AlertDetail from "./alertDetail.vue"; // 告警详情弹窗
import { minToDate } from "@/utils/minToDate";
import AssetConfirmDetail from "./assetConfirmDetail.vue";
import { useRouter } from "vue-router";
const router = useRouter();
// 资产 审核列表
let auditAssetState = reactive({
  first: {},
  data: [],
  total: 0,
  time: {},
});
function getAsssetAuditList() {
  auditAssetState.first = {};
  selectAuditAssetList().then((res) => {
    let resData = res.data;
    auditAssetState.time = res.intervalParam;
    auditAssetState.total = resData.length;
    if (resData.length > 0) {
      auditAssetState.first = resData[0];
      auditAssetState.data = resData.splice(1, resData.length - 1);
    }
  });
}
getAsssetAuditList();
let assetConfirmState = reactive({
  first: {},
  data: [],
  total: 0,
});
// 获取资产确认列表
function getAssetCongirmList() {
  assetConfirmState.first = {};
  selectNotConfirmAssetsList().then((res) => {
    assetConfirmState.total = res.total;
    if (res.rows.length > 0) {
      assetConfirmState.first = res.rows[0];
      assetConfirmState.data = res.rows.splice(1, res.rows.length - 1);
    }
  });
}
getAssetCongirmList();
// 资产审核详情查询
let assetAuditDetail = reactive({
  data: {},
});
let assetAuditShow = ref(false);
let auditAssetDialog = ref();
function openAssetAuditDetail(row) {
  let data = {
    id: row.id,
    spare2: row.spare2,
    assetsIpId: row.assetsIpId,
    assetsId: row.assetsId,
    confirmType: row.confirmType,
    spare3: row.spare3,
  };
  assetAuditDetail.data = data;
  assetAuditShow.value = true;
  auditAssetDialog.value.open();
}
function closeAuditAssetDialog(data) {
  assetAuditDetail.data = {};
  assetAuditShow.value = false;
  auditAssetDialog.value.close();
  if (data == "save") {
    getAsssetAuditList();
  }
}
// 资产确认
let assetDialog = ref();
let assetDetailShow = ref();
let assetData = reactive({
  data: {},
});
function assetConfirm(row) {
  assetData.data = row;
  assetDetailShow.value = true;
  assetDialog.value.open();
}
function closeAssetDialog() {
  assetDialog.value.close();
  assetDetailShow.value = false;
}
function closeAssetDetail() {
  closeAssetDialog();
  getAssetCongirmList();
}
// 已申请删除的告警
let warningState = reactive({
  first: {},
  data: [],
  total: 0,
});
function wargningList() {
  selectApplyAlertList({ pageNum: 1, pageSize: 5 }).then((res) => {
    let arr = res.data;
    warningState.first = {};
    warningState.total = arr.total;
    if (arr.list.length > 0) {
      warningState.first = arr.list[0];
      warningState.data = arr.list.splice(1, arr.list.length - 1);
    }
  });
}
wargningList();
let alertId = ref("");
let alertDetailShow = ref(false);
let alertDialog = ref();
function openAlertDetail(row) {
  alertId.value = row.alertId;
  alertDialog.value.open();
  alertDetailShow.value = true;
}
function closeAlertDialog() {
  alertDialog.value.close();
  alertDetailShow.value = false;
}
// 告警详情操作后向前传值
function closeAlertDetail(data) {
  wargningList();
  closeAlertDialog();
}
// 跳转到已申请删除的告警界面
function toPendingWarnings() {
  router.push({
    name: "PendingWarningsDelete",
    // params: {},
  }); //路由跳转
}
// 跳转到资产列表
function toConfirmAssetList() {
  router.push({
    name: "Confirm",
    // params: {},
  }); //路由跳转
}
</script>

<style lang="scss" scoped>
.card {
  .card_title {
    height: 22px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $fontColor;
    line-height: 22px;
  }
  .card_first {
    height: 50px;
    float: left;
    width: calc(100% - 110px);
    padding: 0px 20px;
    margin-top: 48px;
    border-left: 6px solid $color;
    border-radius: 5px 0px 0px 5px;
    > p {
      height: 25px;
      font-size: 12px;
      font-weight: 400;
      color: $fontColorSoft;
      line-height: 25px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:nth-child(2) {
        font-weight: 300;
      }
      > span:nth-child(1) {
        display: inline-block;
        margin-right: 30px;
      }
      > span:nth-child(2) {
        display: inline-block;
        &.asset {
          font-weight: 400;
          color: #6e7ca5;
          font-size: 12px;
          padding: 0px 8px;
          background: #eef5ff;
          border-radius: $radiusS;
        }
        &.taskGroup {
          font-weight: 400;
          color: #6e7ca5;
          font-size: 12px;
          padding: 0px 8px;
          background: #eef5ff;
          border-radius: $radiusS;
        }
        > span {
          background: #fcedee;
          border-radius: $radiusSM;
          padding: 2px 8px;
          margin: 0px 5px;
        }
      }
    }
  }
  .card_count {
    float: right;

    height: 99px;
    min-width: 99px;
    line-height: 99px;
    text-align: center;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.05);
    border-radius: $radiusL;
    font-size: 32px;
    font-weight: 600;
    &.alert {
      background: #dae6ff;
    }
    &.asset {
      background: #e4f9f0;
    }
    &.event {
      background: #fcedee;
    }
    &.eventTask {
      background: #fefae6;
    }
  }
}
.work_time1 {
  color: #0079fe;
  margin: 0px 5px;
}
.work_time {
  color: #ff0000;
  margin: 0px 5px;
}
:deep(.el-table__row) {
  cursor: pointer;
}
</style>
