<template>
  <div class="">
    <el-row :gutter="30">
      <el-col :span="assetDetailState.confirmType == '3' ? 24 : 12">
        <div class="title-bottom-line">
          <p>新增信息</p>
        </div>
        <el-form ref="form" label-width="120px" label-position="left" class="base-info-form" v-if="assetDetailState.assets.length > 0">
          <el-form-item label="IP地址："> {{ assetDetailState.assets[0].ip }}</el-form-item>
          <el-form-item label="主机名称：">
            {{ assetDetailState.assets[0].host }}
          </el-form-item>
          <el-form-item label="操作系统："> {{ assetDetailState.assets[0].os }}</el-form-item>
          <el-form-item label="端口服务：">
            <div v-for="item in assetDetailState.assets" :key="item.port + item.service" class="portAndSerive">
              <p>
                <span>{{ item.port }}</span>
                <span>{{ item.service }}</span>
              </p>
              <div class="clearfix"></div>
            </div>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col
        v-if="assetDetailState.confirmType !== '3' && (assetDetailState.assetsPage == 'auditAsset' || assetDetailState.assetsPage == 'auditTerminal')"
        :span="12"
      >
        <div class="title-bottom-line">
          <p>摘要</p>
        </div>
        <el-form ref="rightForm" label-width="120px" label-position="left" class="base-info-form">
          <div class="pull-left asset_detail_class">
            <el-form-item label="资产对象类型：">{{ assetDetailState.assetsPage == "auditAsset" ? "计算设备资产" : "终端资产" }}</el-form-item>
            <el-form-item label="资产对象名称：">{{ assetDetailState.resource.name }}</el-form-item>
            <el-form-item label="主机名：">{{ assetDetailState.resource.hostName }}</el-form-item>
          </div>
          <div class="pull-left asset_detail_class">
            <el-form-item label="等级保护级别：">{{ assetDetailState.resource.levelName }}</el-form-item>
            <el-form-item label="责任主体：">{{ assetDetailState.resource.deptName }} </el-form-item>
            <el-form-item label="责任人：">{{ assetDetailState.resource.assetsPerson }} </el-form-item>
          </div>
          <div class="clearfix"></div>
        </el-form>
        <div v-if="assetDetailState.assetsPage == 'auditAsset'" class="ipports">
          <div class="title-bottom-line margin-top20">
            <p>局域网IP、端口与服务组</p>
          </div>
          <ip-port :list="assetDetailState.resourceServers" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
          <div class="title-bottom-line margin-top20">
            <p>基础资源软件</p>
          </div>

          <ul class="soft-ul">
            <li v-for="item in assetDetailState.resourceSoftwares" :key="item.id" class="inp">
              <div><span class="item-label">类型：</span>{{ item.softwareType }}</div>
              <div><span class="item-label">软件：</span>{{ item.softwareValue }}</div>
              <div><span class="item-label">版本：</span>{{ item.softwareEdition }}</div>
            </li>
          </ul>
          <div class="margin-top20 allAssemblyBody">
            <span class="title">其他应用组件：</span>
            <p class="allAssembly">
              <span v-if="assetDetailState.resourceAssemblys.length === 0">暂无</span>
              <el-tag v-for="item in assetDetailState.resourceAssemblys" style="margin-bottom: 10px" :key="item.id" class="assembly">{{
                item.assemblyName
              }}</el-tag>
            </p>
          </div>
        </div>
        <div v-else>
          <div class="title-bottom-line margin-top20">
            <p>局域网IP</p>
          </div>
          <ip-port
            :list="assetDetailState.terminalIps"
            :is-detail="true"
            :show-type="false"
            :showPort="false"
            width="100%"
            class="magin-auto"
          ></ip-port>
        </div>
      </el-col>
    </el-row>
  </div>
  <div class="title-bottom-line">
    <p>审核</p>
  </div>
  <div>
    <el-form ref="auditForm" label-width="140px" label-position="left" class="base-info-form">
      <el-form-item label="审核结果">
        <el-radio v-model="auditData.data.auditStatus" label="Y">审核通过</el-radio>
        <el-radio v-model="auditData.data.auditStatus" label="N" v-if="assetDetailState.confirmType == '1' && assetDetailState.resource.spare1 == 'N'"
          >驳回</el-radio
        >
        <el-radio v-model="auditData.data.auditStatus" label="N" v-if="assetDetailState.confirmType == '2' && assetDetailState.spare3 == 'N'"
          >驳回</el-radio
        >
        <el-radio v-model="auditData.data.auditStatus" label="N" v-if="assetDetailState.confirmType == '3'">驳回</el-radio>
      </el-form-item>
      <el-form-item label="审核意见">
        <el-input v-model="auditData.data.auditOpinion" type="textarea" rows="5"></el-input>
      </el-form-item>
    </el-form>
  </div>
  <div class="text-center">
    <el-button @click="closeAudit">取消</el-button>
    <el-button type="primary" @click="saveAuditData">确定</el-button>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { getAuditAsset, saveAssetsAudit, getBasicPortsServic } from "@/api/workSpace/asset";
import { ElMessage } from "element-plus";
let props = defineProps({
  assetData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let assetDetailState = reactive({
  assets: [],
  resource: {},
  confirmType: "",
  assetsPage: "",
  spare2: "",
  spare3: "",
  ipSet: [],
  portsServices: [],
  resourceServers: [],
  resourceSoftwares: [],
  terminalIps: [],
  resourceAssemblys: [],
});
let auditData = reactive({
  data: {
    id: "",
    auditStatus: "Y",
    auditOpinion: "",
  },
});
watch(
  () => auditData.data.auditStatus,
  (count, prevCount) => {
    /* ... */
    auditData.data.auditOpinion = "";
  }
);

function getAuditAssetDetail() {
  getAuditAsset(props.assetData).then((res) => {
    console.info(res);
    assetDetailState.assetsPage = res.assetsPage;
    assetDetailState.resource = res.resource;
    assetDetailState.confirmType = res.confirmType;
    assetDetailState.spare2 = res.spare2;
    assetDetailState.spare3 = res.spare3;
    assetDetailState.assets = res.assets;
    if (assetDetailState.confirmType != "3" && assetDetailState.assetsPage == "auditAsset") {
      assetDetailState.resourceServers = res.resource.resourceServers;
      assetDetailState.resourceSoftwares = res.resource.resourceSoftwares;
      assetDetailState.resourceAssemblys = res.resource.resourceAssemblys;
      assetDetailState.resourceServers.forEach((item) => {
        item.type = item.serviceAgreementName;
      });
    } else if (assetDetailState.confirmType !== "3" && assetDetailState.assetsPage == "auditTerminal") {
      assetDetailState.terminalIps = res.resource.terminalIps;
    }
  });
}
getAuditAssetDetail();
let emits = defineEmits(["close"]);
function closeAudit() {
  emits("close");
}
// 保存审核结果
function saveAuditData() {
  if (auditData.data.auditOpinion.trim() === "") {
    ElMessage.warning("请输入审核意见");
    return false;
  }
  auditData.data.id = assetDetailState.spare2;
  saveAssetsAudit(auditData.data).then((res) => {
    ElMessage.success("操作成功");
    emits("close", "save");
  });
}
</script>

<style lang="scss" scoped>
.portAndSerive {
  > p {
    float: left;
    margin-bottom: 10px;
    border: 1px solid #e2e2e4;
    padding: 5px 10px;
    border-radius: 4px;
    > span {
      display: inline-block;
      padding: 0px 7px;
      text-align: center;
      border-radius: $radiusS;
    }
    > span:nth-child(1) {
      border: 1px solid #dae6ff;
      background: #dae6ff;
      margin-right: 10px;
    }
    > span:nth-child(2) {
      border: 1px solid #e4f9f0;
      background: #e4f9f0;
    }
  }
  > span {
    float: left;
    width: 80px;
    margin-left: 20px;
    margin-top: 10px;
    cursor: pointer;
  }
}
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.allAssemblyBody {
  .allAssembly {
    display: inline-block;
    word-break: break-all;
  }
  .title {
    width: 120px;
    float: left;
  }
  > p {
    float: left;
    width: calc(100% - 130px);
    .assembly {
      margin-right: 10px;
      white-space: break-spaces;
      height: auto;
    }
  }
}
:deep(.ipports) {
  .ip-port-list.detail > li ul {
    margin-left: 0px;
  }
}
.asset_detail_class {
  width: calc(50% - 10px);
}
@media screen and (max-width: 1900px) {
  .asset_detail_class {
    width: calc(50% - 10px);
  }
}
@media screen and (max-width: 1500px) {
  .asset_detail_class {
    width: calc(100% - 10px);
  }
}
.soft-ul {
  > li {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid #ebedf1;
    > div {
      flex: 1;
    }
  }
}
</style>
