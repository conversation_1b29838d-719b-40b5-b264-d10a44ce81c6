<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="onData"> </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "WorkbenchList",
};
</script>
<script setup>
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { loopholeColumns, eventColumns, PendingColumns } from "./manage";
import { getEventTaskStay, getAnalysisingEvent, selectStayVulnList } from "@/api/workSpace/securityManagement.js";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
let columns = ref();
let onData = ref();
let tableRef = ref();
if (route.name == "WorkbenchList") {
  columns.value = loopholeColumns;
  onData.value = getEventTaskStay;
} else if (route.name == "AnalysisList") {
  columns.value = eventColumns;
  onData.value = getAnalysisingEvent;
} else {
  columns.value = PendingColumns;
  onData.value = selectStayVulnList;
}
//搜索相关
let searchState = reactive({
  data: {
    title: "",
    // levelId: "",
  },
  menuData: [
    // {
    //   lable: "事件等级",
    //   prop: "levelId",
    //   options: [],
    //   dictName: "event_level",
    // },
  ],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "事件名称",
    },
  ],
});

function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    title: "",
    levelId: "",
  };
  search();
}
</script>
<style scoped lang="scss"></style>
