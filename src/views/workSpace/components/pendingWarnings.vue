<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <statistics
      :border="true"
      :list="[
        {
          num: tableRef ? tableRef.staticTotal : 0,
          text:
            (userRoles.indexOf('permission:three') >= 0 || userRoles.indexOf('permission:deliveryManager') >= 0) &&
            $route.name === 'PendingWarningsDelete'
              ? '已申请删除告警数'
              : '待处理告警数',
        },
      ]"
    ></statistics>
    <common-search
      v-if="changeShow"
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <template
        v-if="
          (userRoles.indexOf('permission:three') >= 0 || userRoles.indexOf('permission:deliveryManager') >= 0) &&
          $route.name === 'PendingWarningsDelete'
        "
      >
        <el-button type="danger" :loading="deleteLoading" @click="delteSelect('three')" size="mini" :disabled="selectOptions.length == 0">
          <el-icon :size="12"><delete-filled /></el-icon>
          删除
        </el-button>
      </template>
      <template
        v-if="
          (userRoles.indexOf('permission:three') >= 0 || userRoles.indexOf('permission:deliveryManager') >= 0) &&
          $route.name === 'PendingWarningsDelete'
        "
      >
        <el-button type="danger" :disabled="tableRef && tableRef.staticTotal == 0" @click="emptySelect" :loading="deleteLoading" size="mini">
          <el-icon :size="12"><delete-filled /></el-icon>
          清空
        </el-button>
      </template>
      <template v-else>
        <el-button type="danger" :disabled="selectOptions.length == 0" @click="openApplyDeleteDialog" :loading="deleteLoading" size="mini">
          <el-icon :size="12"><delete-filled /></el-icon>
          申请删除
        </el-button>
        <el-button type="danger" @click="openApplyDeleteByTimeDialog" size="mini">
          <el-icon :size="12"><delete-filled /></el-icon>
          选择时间申请删除
        </el-button>
      </template>
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="getTableData"
      :checkbox="true"
      :row-key="
        (userRoles.indexOf('permission:three') >= 0 || userRoles.indexOf('permission:deliveryManager') >= 0) &&
        $route.name === 'PendingWarningsDelete'
          ? 'alertId'
          : 'id'
      "
      @row-click="openAlertDetail"
      @selection-change="selectionChange"
    >
      <template #newTime="scope">
        <span v-if="scope.row.stage == '1'">{{ scope.row.createTime }}</span>
        <span v-if="scope.row.stage == '2'">{{ scope.row.createTime2 }}</span>
        <span v-if="scope.row.stage == '3'">{{ scope.row.createTime3 }}</span>
        <span v-if="scope.row.stage == '4'">{{ scope.row.createTime4 }}</span>
      </template>
      <template #asset="scope">
        <p>
          <span v-for="(item, index) in scope.row.assetsList" :key="item.id">
            {{ item.name }}
            <span v-if="index !== scope.row.assetsList.length - 1">/</span>
          </span>
        </p>
      </template>
      <template #level="scope">
        <el-tag :type="levelData[scope.row.priority]">{{ scope.row.priorityStr }}</el-tag>
      </template>
    </xel-table>
  </el-card>
  <xel-dialog title="申请删除原因" ref="applyDeleteDialog" @submit="delteSelect('')" @close="resetApplyDeleteData">
    <el-form>
      <el-form-item label="申请原因" required>
        <el-input type="textarea" v-model="applyReason" placeholder="请输入申请原因" rows="5"></el-input>
      </el-form-item>
    </el-form>
  </xel-dialog>
  <xel-dialog
    :title="
      (userRoles.indexOf('permission:three') >= 0 || userRoles.indexOf('permission:deliveryManager') >= 0) && $route.name === 'PendingWarningsDelete'
        ? '已申请删除的告警'
        : '待处理告警'
    "
    ref="alertDialog"
    width="60%"
    @close="closeAlertDialog"
    :ishiddenDialog="true"
  >
    <alert-detail
      v-if="alertDetailShow"
      :alertId="alertId"
      @close="closeAlertDetail"
      :typeStr="
        (userRoles.indexOf('permission:three') >= 0 || userRoles.indexOf('permission:deliveryManager') >= 0) &&
        $route.name === 'PendingWarningsDelete'
          ? 'jf'
          : 'erxian'
      "
    ></alert-detail>
  </xel-dialog>
  <xel-dialog title="选择时间范围申请删除" ref="delByTimeDialog" @submit="delByTime" @close="resetApplyDeleteData">
    <el-form>
      <xel-form-item
        label="首轮时间"
        type="datetimerange"
        form-type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        v-model:start="delStartTime"
        v-model:end="delEndTime"
        required
      ></xel-form-item>
      <xel-form-item label="申请原因" required="true" type="textarea" v-model="applyReason" rows="5"></xel-form-item>
    </el-form>
  </xel-dialog>
</template>
<script>
export default {
  name: "PendingWarnings",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import {
  selectAllPendingWarnings,
  applyWaitingConfirm,
  deleteWaitingConfirm,
  applyWaitingConfirmByTime,
  deleteAllWaitingConfirm,
} from "@/api/workSpace/alert";
import { selectApplyAlertPage } from "@/api/workSpace/workbench";
import { batchDelete } from "@/utils/delete";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import AlertDetail from "./alertDetail.vue"; // 告警详情弹窗
import { Level_priority } from "@/config/constant";
import { ElMessageBox } from "element-plus";
const store = useStore();
const route = useRoute();
let userRoles = computed(() => {
  return store.state.roles;
});
let state = reactive({
  levelData: Level_priority,
});
let { levelData } = toRefs(state);
let getTableData = ref(null);
if (
  (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
  route.name === "PendingWarningsDelete"
) {
  getTableData.value = selectApplyAlertPage;
} else {
  getTableData.value = selectAllPendingWarnings;
}
onMounted(() => {});
let searchState = reactive({
  data: computed(() => {
    if (
      (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
      route.name === "PendingWarningsDelete"
    ) {
      return {
        title: "",
        priority: "",
        applyUser: "",
        applyTimeStr: "",
        applyTimeList: [],
      };
    } else {
      return {
        title: "",
        devName: "",
        csrcip: "",
        priority: "",
        devIp: "",
        description: "",
        ceventdigest: "",
        devType: "",
        cdstip: "",
      };
    }
  }),
  menuData: [
    {
      lable: "告警等级",
      prop: "priority",

      dictName: "alert_level",
    },
  ],
  formList: computed(() => {
    if (
      (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
      route.name === "PendingWarningsDelete"
    ) {
      return [
        {
          formType: "input",
          prop: "title",
          label: "告警名称",
        },
        {
          formType: "input",
          prop: "applyUser",
          label: "申请人",
        },
        {
          formType: "daterange",
          type: "datetimerange",
          prop: "applyTimeList",
          label: "申请时间",
          day: false,
          onChange(val) {
            searchState.data.applyTimeStr = val[0] + " ~ " + val[1];
          },
        },
      ];
    } else {
      return [
        {
          formType: "input",
          prop: "title",
          label: "告警名称",
        },
        {
          formType: "input",
          prop: "devName",
          label: "设备名称",
        },
        {
          formType: "input",
          prop: "csrcip",
          label: "源地址",
        },
        {
          formType: "input",
          prop: "devIp",
          label: "设备地址",
        },
        {
          formType: "input",
          prop: "description",
          label: "告警详情",
        },
        {
          formType: "input",
          prop: "ceventdigest",
          label: "事件摘要",
        },
        {
          formType: "input",
          prop: "devType",
          label: "设备类型",
        },
        {
          formType: "input",
          prop: "cdstip",
          label: "目的地址",
        },
      ];
    }
  }),
});
let columns = computed(() => {
  if (
    (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
    route.name === "PendingWarningsDelete"
  ) {
    return [
      {
        prop: "title",
        label: "告警名称",
      },
      {
        prop: "priorityStr",
        label: "等级",
        slotName: "level",
      },
      {
        prop: "userName",
        label: "申请人",
      },
      {
        prop: "applyTimeStr",
        label: "申请时间",
      },
      {
        prop: "applyReason",
        label: "申请原因",
      },
    ];
  } else {
    return [
      {
        prop: "title",
        label: "告警名称",
      },
      {
        prop: "priorityStr",
        label: "等级",
        slotName: "level",
        width: 100,
      },
      {
        prop: "stage",
        label: "轮次",
        width: 100,
      },
      {
        prop: "num",
        label: "次数",
        width: 100,
      },
      {
        prop: "delStatus",
        label: "申请删除状态",
      },
      {
        prop: "description",
        label: "告警描述",
      },
      {
        prop: "ceventdigest",
        label: "事件摘要",
      },
      {
        prop: "createTime",
        label: "首轮出现时间",
      },
      {
        prop: "",
        label: "当前轮次出现时间",
        slotName: "newTime",
      },
      {
        prop: "",
        label: "相关资产",
        slotName: "asset",
      },
    ];
  }
});
// let columns =
let tableRef = ref();
// 申请删除的原因
let applyReason = ref("");
let applyDeleteDialog = ref();
// 关闭方法
function resetApplyDeleteData() {
  applyReason.value = "";
}
// 打开申请原因的弹窗
function openApplyDeleteDialog() {
  if (selectOptions.value.length > 0) {
    applyDeleteDialog.value.open();
  } else {
    ElMessage.warning("请至少选择一条数据");
  }
}
// 选择
let selectOptions = ref([]);
function selectionChange(val) {
  selectOptions.value = val;
}
// 查看详情
let alertId = ref("");
let alertDetailShow = ref(false);
let alertDialog = ref();

function openAlertDetail(row) {
  if (
    (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
    route.name === "PendingWarningsDelete"
  ) {
    alertId.value = row.alertId;
  } else {
    alertId.value = row.id;
  }
  alertDialog.value.open();
  alertDetailShow.value = true;
}
function closeAlertDialog() {
  alertDialog.value.close();
  alertDetailShow.value = false;
}
function closeAlertDetail(data) {
  closeAlertDialog();
  search();
}
// 申请删除
function delteSelect(type) {
  let ids = "";
  let arr = [];
  if (
    !(
      (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
      route.name === "PendingWarningsDelete"
    )
  ) {
    if (applyReason.value === "") {
      ElMessage.warning("请输入申请原因");
      return false;
    }
  }

  batchDelete().then(() => {
    deleteLoading.value = true;

    let ids = "";
    let ids2 = "";
    selectOptions.value.forEach((item) => {
      if (ids == "") {
        ids = ids + item.id;
        ids2 = ids2 + item.alertId;
      } else {
        ids = ids + "," + item.id;
        ids2 = ids2 + "," + item.alertId;
      }
    });
    if (
      (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
      route.name === "PendingWarningsDelete"
    ) {
      const param = {
        ids: ids2,
      };
      deleteWaitingConfirm(param)
        .then((res) => {
          ElMessage.success(res.msg);
          tableRef.value.table.clearSelection();
          search();
        })
        .finally(() => {
          deleteLoading.value = false;
        });
    } else {
      let param = {
        ids: ids,
        applyReason: applyReason.value,
      };

      applyWaitingConfirm(param)
        .then((res) => {
          ElMessage.success(res.msg);

          tableRef.value.table.clearSelection();
          search();
          applyDeleteDialog.value.close();
        })
        .finally(() => {
          deleteLoading.value = false;
        });
    }
  });
}
function search(initPage = true) {
  tableRef.value.reload(searchState.data, initPage);
  /*console.log(tableRef.value.staticTotal, 111);*/
}
let changeShow = ref(true);
function reset() {
  changeShow.value = false;
  if (
    (userRoles.value.indexOf("permission:three") >= 0 || userRoles.value.indexOf("permission:deliveryManager") >= 0) &&
    route.name === "PendingWarningsDelete"
  ) {
    searchState.data.title = "";
    searchState.data.priority = "";
    searchState.data.applyUser = "";
    searchState.data.applyTimeStr = "";
    searchState.data.applyTimeList = [];
  } else {
    searchState.data.title = "";
    searchState.data.devName = "";
    searchState.data.csrcip = "";
    searchState.data.priority = "";
    searchState.data.devIp = "";
    searchState.data.description = "";
    searchState.data.ceventdigest = "";
    searchState.data.devType = "";
    searchState.data.cdstip = "";
  }
  tableRef.value.reload();
  setTimeout(() => {
    changeShow.value = true;
  }, 100);
}

let delByTimeDialog = ref();
let delStartTime = ref("");
let delEndTime = ref("");
function openApplyDeleteByTimeDialog() {
  delStartTime.value = "";
  delEndTime.value = "";
  delByTimeDialog.value.open();
}
function delByTime() {
  if (!(delStartTime.value || delEndTime.value)) {
    ElMessage.warning("请先选择时间范围");
    return false;
  }
  if (applyReason.value === "") {
    ElMessage.warning("请输入申请原因");
    return false;
  }

  ElMessageBox.confirm(`确定删除${delStartTime.value} - ${delEndTime.value}时间范围内的所有告警?`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customStyle: { width: "460px" },
  }).then(() => {
    //todo 删除接口
    let params = {
      beginGetTimeStr: delStartTime.value,
      endGetTimeStr: delEndTime.value,
      applyReason: applyReason.value,
    };
    applyWaitingConfirmByTime(params).then((res) => {
      ElMessage.success(res.msg);

      search(true);
      delByTimeDialog.value.close();
    });
  });
}
// 清空按钮
function emptySelect() {
  ElMessageBox.confirm(`确定清空所有已申请删除的告警？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteLoading.value = true;
    deleteAllWaitingConfirm()
      .then((res) => {
        search();
        selectOptions.value = [];
        ElMessage.success("操作成功");
      })
      .finally(() => {
        deleteLoading.value = false;
      });
  });
}
let deleteLoading = ref(false);
</script>

<style lang="scss" scoped>
:deep(.el-table__row) {
  cursor: pointer;
}
</style>
