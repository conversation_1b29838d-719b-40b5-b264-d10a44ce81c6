<template>
  <div class="title-bottom-line">
    <p>任务信息</p>
  </div>
  <div class="">
    <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
      <el-form-item label="任务标题：">{{ state.detail.title }}</el-form-item>
      <el-form-item label="任务时间：">{{ state.detail.begainTimeStr }} - {{ state.detail.endTimeStr }}</el-form-item>
      <el-form-item label="是否重复：">
        <span v-if="state.detail.cycleType === '0'">不重复</span>
        <span v-if="state.detail.cycleType === '1'">每日重复</span>
        <span v-if="state.detail.cycleType === '2'">每周重复</span>
        <span v-if="state.detail.cycleType === '3'">每月重复</span>
        <span v-if="state.detail.cycleType === '4'">每年重复</span>
        <span v-if="state.detail.cycleType === '5'">工作日</span>
        <span v-if="state.detail.cycleType === '6'"
          >自定义重复-
          <span v-if="state.detail.cycleUserDefinedType === '0'">每日重复-每隔 {{ state.detail.cycleInterval }}天重复</span>
          <span v-if="state.detail.cycleUserDefinedType === '1'"
            >每{{ state.detail.cycleInterval }}周重复-每周 {{ state.detail.intervalStr }}重复</span
          >
          <span v-if="state.detail.cycleUserDefinedType === '2'"
            >每{{ state.detail.cycleInterval }}月重复-每月 {{ state.detail.intervalStr }}天重复</span
          >
        </span>
      </el-form-item>
      <el-form-item label="是否考虑工作日：">{{ !!Number(state.detail.isWorkday || 0) ? "是" : "否" }}</el-form-item>
      <el-form-item label="类型：">
        <span v-if="state.detail.type == '0'">普通</span>
        <span v-if="state.detail.type == '1'">值班</span>
        <span v-if="state.detail.type == '2'">请假</span>
      </el-form-item>
      <el-form-item label="描述：">{{ state.detail.description }}</el-form-item>
      <el-form-item label="执行人：">{{ state.detail.executorName }}</el-form-item>
      <el-form-item label="参与人：">{{ state.detail.partakeUserNames }}</el-form-item>
      <el-form-item label="优先级：">{{ state.detail.levelName }}</el-form-item>
      <el-form-item label="附件：">
        <a @click="downLoadFile(state.detail.detailReportId, state.detail.detailReportName)" class="pointer">{{ state.detail.detailReportName }}</a>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { scheduleTaskDetail } from "@/api/workSpace/fullCalendar";
import { downloadUrl } from "@/api/system/download.js";
import { download } from "@/plugins/request";
let props = defineProps({
  fullCalendarTaskId: {
    type: String,
    default: "",
  },
});
let state = reactive({
  detail: {},
});
function getDetail() {
  scheduleTaskDetail(props.fullCalendarTaskId).then((res) => {
    state.detail = res.data;
  });
}
onMounted(() => {
  getDetail();
});
function downLoadFile(fileId, fileName) {
  download(downloadUrl + fileId, fileName, {}, "get");
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
</style>
