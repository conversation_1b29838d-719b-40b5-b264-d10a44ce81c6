<template>
  <el-card>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <!--  -->
    <xel-table ref="tableRef" :load-data="getUnAssignAuditList" :columns="columns" @selection-change="handleSelectionChange">
      <template #eventTitle="{ row }">
        <div :class="!isComp ? 'xel-clickable' : ''" @click="toDetail(row)">
          Case #{{ row.eventNo }}-{{ row.titlePrefix }} &nbsp;&nbsp;{{ row.title }}
          <p class="event-tag-box">
            <icon n="icon-a-2" :size="20"></icon>
            <span v-for="tag in row.eventTagList" :key="tag.id">{{ tag.tagName }}</span>
          </p>
        </div>
      </template>
      <template #level="scope">
        <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
      </template>
      <template #actions="{ row }">
        <ul class="action-btns-ul">
          <li
            v-if="(row.auditResultStatus == 1 || row.auditResultStatus == 5) && row.isClose == 0"
            v-hasPermi="'eventAudit:audit'"
            @click="modifyButton(row)"
          >
            <el-tooltip content="认领" placement="top" effect="light">
              <el-icon><Select /></el-icon>
            </el-tooltip>
          </li>
        </ul>
      </template>
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "PendingTasks",
};
</script>
<script setup>
import { ref, reactive, onActivated, toRefs } from "vue";
onActivated(() => {
  search(false);
});
import { Level_Data } from "@/config/constant";
import { getUnAssignAuditList, claimEventAudit } from "@/api/workSpace/event";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
const router = useRouter();
const route = useRoute();

let state = reactive({
  levelData: Level_Data,
});
let { levelData } = toRefs(state);

let tableRef = ref();
// 列表配置项
const columns = [
  {
    prop: "title",
    label: "事件名称",
    slotName: "eventTitle",
  },
  {
    prop: "auditResultStatusStr",
    label: "审核状态",
  },
  {
    prop: "levelId",
    label: "事件级别",
    slotName: "level",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actions",
    width: 100,
  },
];
//搜索相关
let searchState = reactive({
  data: {
    title: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "事件名称",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    title: "",
  };
  search();
}

function toDetail(row) {
  /* 新增 - 事件引用查询分析 */
  router.push({
    name: "EventDetail",
    params: {
      id: row.id,
    },
    query: {
      auditResultId: row.auditResultId,
    },
  }); //路由跳转
}
// 认领
function modifyButton(row) {
  claimEventAudit(row.id).then((res) => {
    ElMessage.success("认领成功");
    toDetail(row, 2);
  });
}
</script>
<style lang="sss" scoped></style>
