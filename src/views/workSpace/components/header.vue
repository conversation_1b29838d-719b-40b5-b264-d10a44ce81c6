<template>
  <el-row :gutter="20">
    <el-col :span="6" class="user-section">
      <el-card class="onadd"> <businessCard :timeliness="timeliness" :analyst="analyst"></businessCard></el-card
    ></el-col>
    <el-col :span="18">
      <el-card class="onadd">
        <ul v-if="userRoles[0] == 'permission:one' || userRoles[0] == 'permission:asset'">
          <li>
            <p class="giveAn">本年度处置告警数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.alertCountYear }}</p>
              <el-tag class="alarm">{{ frontline.alertRateYear }}%</el-tag>
            </div>
            <p class="giveAn">月度处置告警数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.alertCountMonth }}</p>
              <el-tag class="alarm">{{ frontline.alertRateMonth }}%</el-tag>
            </div>
          </li>
          <li>
            <p class="giveAn">本年度提报事件数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.eventCountYear }}</p>
              <el-tag class="alarm">{{ frontline.eventRateYear }}%</el-tag>
            </div>
            <p class="giveAn">月度提报事件数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.eventCountMonth }}</p>
              <el-tag class="alarm">{{ frontline.eventRateMonth }}%</el-tag>
            </div>
          </li>
          <li>
            <p class="giveAn">本年度安全测试成果数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.submitCountYear }}</p>
              <el-tag class="alarm">{{ frontline.submitRateYear }}%</el-tag>
            </div>
            <p class="giveAn">月度安全测试成果数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.submitCountMonth }}</p>
              <el-tag class="alarm">{{ frontline.submitRateMonth }}%</el-tag>
            </div>
          </li>
        </ul>
        <ul v-else>
          <li>
            <p class="giveAn">本年度事件任务审核数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.yearEventAuditCount }}</p>
              <el-tag class="alarm">{{ frontline.yearEventAuditRate }}%</el-tag>
            </div>
            <p class="giveAn">月度事件任务审核数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.monthEventAuditCount }}</p>
              <el-tag class="alarm">{{ frontline.monthEventAuditRate }}%</el-tag>
            </div>
          </li>
          <li>
            <p class="giveAn">本年度执行事件内任务数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.yearTaskCount }}</p>
              <el-tag class="alarm">{{ frontline.yearTaskRate }}%</el-tag>
            </div>
            <p class="giveAn">月度执行事件内任务数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.monthTaskCount }}</p>
              <el-tag class="alarm">{{ frontline.monthTaskRate }}%</el-tag>
            </div>
          </li>
          <!-- <li>
            <p class="giveAn">本年风险处置任务数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.submitCountYear }}</p>
              <el-tag class="alarm">{{ frontline.submitRateYear }}%</el-tag>
            </div>
            <p class="giveAn">月度风险处置任务数量</p>
            <div class="front">
              <p class="quantity">{{ frontline.submitCountMonth }}</p>
              <el-tag class="alarm">{{ frontline.submitRateMonth }}%</el-tag>
            </div>
          </li> -->
        </ul>
      </el-card></el-col
    >
  </el-row>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { selectWorkBenchData } from "@/api/workSpace/workbench.js";
import BusinessCard from "./businessCard.vue";
import { useStore } from "vuex";
let props = defineProps({
  userRoles: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
const store = useStore();
let userInfo = computed(() => {
  return store.state.userInfo;
});
let analyst = ref("");
if (props.userRoles.indexOf("permission:one") >= 0) {
  analyst.value = "一线分析师";
} else if (props.userRoles.indexOf("permission:asset") >= 0) {
  analyst.value = "资产确认";
} else if (props.userRoles.indexOf("permission:two") >= 0) {
  analyst.value = "二线分析师";
} else if (props.userRoles.indexOf("permission:three") >= 0) {
  analyst.value = "三线分析师";
}
console.log(props.userRoles[0]);
let frontline = ref({});
let timeliness = ref("");
function name() {
  selectWorkBenchData().then((res) => {
    frontline.value = res.data;
    timeliness.value = res.data.timelyRate;
  });
}
name();
</script>

<style lang="scss" scoped>
ul {
  display: flex;
  li {
    width: 50%;
  }
}
.front {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}
.alarm {
  margin-top: 30px;
  margin-right: 50px;
}
.alarm[data-v-e6c2333c] {
  border-radius: 15px;
}
.onadd {
  height: 247px;
}
</style>
