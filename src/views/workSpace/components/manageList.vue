<template>
  <div class="inDiv title-bottom-line">
    <p>{{ buttName }}</p>
    <el-button size="mini" @click="jump" v-if="tableRef">{{
      buttName == "待处置漏洞" ? tableRef.staticTotal - resultMap.resourceNum : tableRef.staticTotal
    }}</el-button>
    <el-button size="mini" @click="jumpBasis" v-if="buttName == '待处置漏洞'">{{ resultMap.resourceNum }}</el-button>
  </div>
  <section>
    <xel-table ref="tableRef" :columns="columns" :load-data="onData" :defaultParams="{ pageNum: 1, pageSize: 5 }" :pagination="false"> </xel-table>
  </section>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import { loopholeColumns, eventColumns, PendingColumns } from "./manage.js";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
import { getEventTaskStay, getAnalysisingEvent, selectStayVulnList } from "@/api/workSpace/securityManagement.js";
let props = defineProps({
  buttName: {
    type: String,
    default: "",
  },
  assetsId: {
    type: String,
    default: "",
  },

  getTableData: {
    type: String,
    default: "",
  },
});
let tableRef = ref();
let columns = ref();
let onData = ref();
let url = ref("");
if (props.buttName == "待处置事件任务") {
  columns.value = loopholeColumns;
  onData.value = getEventTaskStay;
  url.value = "WorkbenchList";
} else if (props.buttName == "分析中相关事件") {
  columns.value = eventColumns;
  onData.value = getAnalysisingEvent;
  url.value = "AnalysisList";
} else {
  url.value = "BasicResources";
  columns.value = PendingColumns;
  onData.value = selectStayVulnList;
  selec();
}
let resultMap = ref({});
function selec() {
  selectStayVulnList({ pageNum: 1, pageSize: 5 }).then((res) => {
    resultMap.value = res.data.resultMap;
  });
}
// 跳转
function jump() {
  router.push({
    name: url.value,
    params: { id: 0 },
  });
}
function jumpBasis() {
  router.push({
    name: "BasicResources",
    params: { id: 1 },
  });
}
</script>
<style scoped lang="scss">
.inDiv {
  display: flex;
  p {
    margin-right: 10px;
    margin-top: 2px;
  }
}
</style>
