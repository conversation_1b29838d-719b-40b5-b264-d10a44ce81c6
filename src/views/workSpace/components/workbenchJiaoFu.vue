<template>
  <div>
    <workbench-header v-if="!userRoles.includes('permission:operation')" :userRoles="userRoles"></workbench-header>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="margin-top20">
      <el-tab-pane v-if="!userRoles.includes('permission:operation')" name="agency">
        <template #label> 待办事务 </template>
        <agency></agency>
      </el-tab-pane>
      <el-tab-pane name="planTask" :lazy="true">
        <template #label>
          计划任务
          <span class="count">{{ taskCount }}</span>
        </template>
        <plan-task @countTask="getTaskCount"></plan-task>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import WorkbenchHeader from "./headerJiaofu.vue";
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import PlanTask from "./planTaskJiaoFu.vue";
import { selectWorkBenchUserTree, loadScheduleTaskExecutorData } from "@/api/workSpace/workbench";
import Agency from "./agencyJIaoFu.vue";
let props = defineProps({
  userRoles: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
function getExecutorData() {
  loadScheduleTaskExecutorData().then((res) => {
    taskCount.value = res.data.taskCount;
  });
}
getExecutorData();
let activeName = ref(props.userRoles != "permission:operation" ? "agency" : "planTask");
let taskCount = ref(0);
function getTaskCount(data) {
  taskCount.value = data;
}
function handleClick() {}
</script>

<style lang="scss" scoped>
.count {
  padding: 3px 5px;
  border: 1px solid $borderColor;
  background: $borderColor;
  border-radius: 6px;
}
</style>
