<template>
  <div class="content_box">
    <FullCalendar ref="fullCalendar" :options="state2.calendarOptions" locale="zh-cn" />
    <div class="clearfix"></div>
  </div>
</template>

<script setup>
import "@fullcalendar/core/vdom";
import FullCalendar from "@fullcalendar/vue3";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { selectCalendarTask } from "@/api/workSpace/fullCalendar";
let props = defineProps({
  ids: {
    type: String,
    default: () => {
      return "";
    },
  },
});
watch(
  () => props.ids,
  (newVal, oldeVal) => {
    seleteFullCalendar();
  }
);
let fullCalendar = ref();
// 获取日历数据
function seleteFullCalendar() {
  let data = selectCalendarTask(props.ids).then((res) => {
    /*console.info(res);*/
    state.fullCalendarData = [];
    res.data.taskList.forEach((item) => {
      var className = "";
      var backgroundColor = "transparent";
      var textColor = "#797979";
      if (item.level == "1") {
        className = "border-left-level1 cursorP";
      } else if (item.level == "2") {
        className = "border-left-level2 cursorP";
      } else {
        className = "border-left-level3 cursorP";
      }
      if (item.type == "2") {
        className += " leave-red-text";
      }
      state.fullCalendarData.push({
        textColor: textColor,
        title: item.title,
        start: item.begainTimeStr,
        end: item.endTimeStr,
        backgroundColor: backgroundColor,
        className: className,
        allDay: false,
        description: item.description,
        id: item.id,
      });
    });
    fullCalendar.value.options.events = state.fullCalendarData;
  });
}
onMounted(() => {
  seleteFullCalendar();
});
// 日历数据
let state = reactive({
  fullCalendarData: [],
});
let emits = defineEmits(["getTaskDetail"]);
let state2 = reactive({
  calendarOptions: {
    plugins: [dayGridPlugin, interactionPlugin],
    locale: "zh-cn",
    initialView: "dayGridMonth",
    customButtons: {
      myCustomButton: {
        text: "custom",
        click: function () {
          alert("clicked the custom button!");
        },
      },
    },
    headerToolbar: {
      left: "prev,next today",
      center: "title",
      right: "dayGridMonth,dayGridWeek,dayGridDay",
    },
    buttonText: { today: "当天", month: "月", week: "周", day: "日" },
    eventTime: {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    },
    events: [], //事件事件+文本
    eventColor: "#378006", //事件背景颜色
    eventClick: (info) => {
      // alert("Event: " + info.event.title);
      emits("getTaskDetail", info.event.id);
      // info.el.style.borderColor = "red";
    },
    dateClick: (info) => {
      // alert("Clicked on: " + info.dateStr);
      // info.dayEl.style.backgroundColor = "red";
    },
    editable: false,

    // dayMaxEventRows: 2,
  },
});
let calendarOptions = {
  plugins: [dayGridPlugin, interactionPlugin],
  locale: "zh-cn",
  initialView: "dayGridMonth",
  customButtons: {
    myCustomButton: {
      text: "custom",
      click: function () {
        alert("clicked the custom button!");
      },
    },
  },
  headerToolbar: {
    left: "prev,next today",
    center: "title",
    right: "dayGridMonth,dayGridWeek,dayGridDay",
  },
  buttonText: { today: "当天", month: "月", week: "周", day: "日" },
  eventTime: {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  },
  events: [], //事件事件+文本
  eventColor: "#378006", //事件背景颜色
  eventClick: (info) => {
    // alert("Event: " + info.event.title);
    // info.el.style.borderColor = "red";
  },
  dateClick: (info) => {
    // alert("Clicked on: " + info.dateStr);
    // info.dayEl.style.backgroundColor = "red";
  },
  editable: true,
  dayMaxEventRows: 2,
};
defineExpose({
  seleteFullCalendar,
});
</script>

<style lang="scss">
.cursorP {
  cursor: pointer;
}
.border-left-level1 {
  border-left: 3px solid #f9d423;
  border-radius: 0px;
  margin-left: 5px;
  padding-left: 5px;
  margin-bottom: 7px;
}
.border-left-level2 {
  border-left: 3px solid #f1404b;
  border-radius: 0px;
  margin-left: 5px;
  padding-left: 5px;
  margin-bottom: 7px;
}
.border-left-level3 {
  border-left: 3px solid #3498db;
  border-radius: 0px;
  margin-left: 5px;
  padding-left: 5px;
  margin-bottom: 7px;
}
.leave-red-text {
  background: #ffe7d6;

  position: relative;
  .fc-event-title {
    padding-left: 5px;
  }
  &:after {
    display: none;
    background: #fff;
    color: #000;
    border-radius: 3px;
    content: "请假";
    position: absolute;
    top: 0;
    left: 50%;
    padding: 5px 10px;
    border: 1px solid #ccc;

    transform: translateY(-70%) translateX(-50%);
  }
  &:hover {
    background: #ffe7d6;

    .fc-event-title {
      padding-left: 5px;
    }
    &:after {
      display: block;
    }
  }
}
.fc-ltr .fc-dayGrid-view .fc-day-top .fc-day-number {
  text-align: center;
  display: block;
  margin-top: 10px;
  float: inherit;
}
:deep(.fc-direction-ltr) {
  .fc-daygrid-event {
    .fc-event-time {
      display: none !important;
    }
  }
}
</style>
