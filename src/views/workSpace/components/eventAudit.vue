<template>
  <el-card>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="type === 'Os' ? selectAuditTaskPageOs : selectAuditTaskPage"
      @selection-change="handleSelectionChange"
    >
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "EventAudit",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { selectAuditTaskPage } from "@/api/workSpace/event";
import { selectAuditTaskPageOs } from "@/api/offStandardEvent/workSpace/event";

import { useRoute, useRouter } from "vue-router";
const router = useRouter();

const route = useRoute();
let type = route.params.type;

let tableRef = ref();
// 列表配置项
const columns = [
  {
    prop: "eventTitle",
    label: type === "Os" ? "综合服务名称" : "事件名称",
  },
  {
    prop: "taskGroup",
    label: "阶段",
    hide: type === "Os",
  },
  {
    prop: "title",
    label: "任务名称",
  },
  {
    prop: "createTimeStr",
    label: "创建时间",
  },
  {
    prop: "assigneeName",
    label: "分析师",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "审核",
        onClick(scope) {
          modifyButton(scope.row);
        },
      },
    ],
  },
];

//搜索相关
let searchState = reactive({
  data: {
    title: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "任务名称",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    title: "",
  };
  search();
}
function modifyButton(val) {
  router.push({
    name: type === "Os" ? "OffEventDetail" : "EventDetail",
    params: {
      id: val.eventId,
    },
    query: {
      taskId: val.id,
      taskName: val.title,
      isManageTask: val.isManageTask,
    },
  });
}
</script>
<style lang="sss" scoped></style>
