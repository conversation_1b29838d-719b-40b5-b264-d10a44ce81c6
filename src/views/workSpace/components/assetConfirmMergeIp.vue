<template>
  <el-row :gutter="70">
    <el-col :span="10">
      <div class="title-bottom-line">
        <p>新增信息</p>
      </div>
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini" class="addInfo">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item">
          <el-button class="btn" size="small" @click="change(item)" v-if="item.label == '端口服务' && rightData.type === 'basic'">
            <el-icon :size="12"> <ArrowRightBold /></el-icon>
          </el-button>
        </xel-form-item>
      </el-form>
    </el-col>
    <el-col :span="14">
      <div class="title-bottom-line">
        <p>摘要</p>
      </div>
      <el-row class="onname">
        <el-form ref="leftForm" class="base-info-form" style="width: 100%">
          <div class="pull-left asset_detail_class" style="width: 50%">
            <el-form-item label="资产对象类型：">{{ rightData.type == "basic" ? "计算设备资产" : "终端资产" }}</el-form-item>
            <el-form-item label="资产对象名称：">{{ rightData.type == "basic" ? store.abstract.name : store.terminal.name }}</el-form-item>
            <el-form-item label="主机名称：">{{ rightData.type == "basic" ? store.abstract.hostName : store.terminal.hostName }}</el-form-item>
          </div>
          <div class="pull-left asset_detail_class" style="width: 50%">
            <el-form-item label="等级保护级别：">{{ rightData.type == "basic" ? store.abstract.levelName : store.terminal.levelName }}</el-form-item>
            <el-form-item label="责任主体："> {{ rightData.type == "basic" ? store.abstract.deptName : store.terminal.deptName }}</el-form-item>
            <el-form-item label="责任人：">{{ userNames }} </el-form-item>
          </div>
        </el-form>
      </el-row>

      <div v-if="rightData.type == 'basic'">
        <div class="title-bottom-line">
          <p>局域网IP、端口与服务组</p>
        </div>
        <ip-port :list="store.resourceServers" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
        <div class="title-bottom-line">
          <p>基础资源软件</p>
        </div>
        <ul class="soft-ul">
          <li v-for="item in store.resourceSoftwares" :key="item.id" class="inp">
            <div><span class="item-label">类型：</span>{{ item.softwareType }}</div>
            <div><span class="item-label">软件：</span>{{ item.softwareValue }}</div>
            <div><span class="item-label">版本：</span>{{ item.softwareEdition }}</div>
          </li>
        </ul>
        <p v-for="item in store.resourceSoftwares" :key="item.id" class="inp"></p>
        <div class="margin-top20 allAssemblyBody">
          <span class="title">其他应用组件：</span>
          <p class="allAssembly">
            <span v-if="store.resourceAssemblys.length === 0">暂无</span>
            <el-tag v-for="item in store.resourceAssemblys" :key="item.id" class="assembly">{{ item.assemblyName }}</el-tag>
          </p>
        </div>
      </div>
      <div v-else>
        <div class="title-bottom-line">
          <p>局域网IP</p>
        </div>
        <ip-port :list="store.terminalIps" :is-detail="false" :show-type="true" width="100%" class="magin-auto"></ip-port>
      </div>
    </el-col>
  </el-row>
  <!-- 添加端口号级服务协议弹框 -->
  <xel-dialog title="添加端口号及服务协议" ref="addtoRef" @submit="AddForm" @close="closeDialog" size="mini">
    <dynamic ref="dynamicIpRef" label-width="6em" :formList="formListIp" :data-rows="formAdd" v-if="dynamicIpstatus"></dynamic>
  </xel-dialog>
  <div class="text-center margin-top20">
    <el-button @click="closeFn">取消</el-button>
    <el-button type="primary" @click="saveAssetMerge">变更资产</el-button>
  </div>
</template>

<script setup>
import {
  getAssetsResourcesList as getTableData,
  selectSoftTypeListByPage,
  selectSoftListByPage,
  assetsDetail,
} from "@/api/securityAssets/assetsList";
import { getTerminalDetail } from "@/api/securityAssets/terminal";
import { openIpNotConfirm, changeIpNotConfirmAssets, changeIpNotConfirmTerminalAssets } from "@/api/securityAssets/confirm";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed } from "vue";
import { getDicts } from "@/api/system/dict/data";
import { useRouter, useRoute } from "vue-router";
let props = defineProps({
  assetId: {
    type: String,
    default: "",
  },
  rightData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
// 弹框列表
let formData = reactive({
  ip: "",
  internetIp: "",
  domain: "",
});
let formAdd = reactive({
  number: "",
  serverAgreement: "",
});
let formList = computed(() => {
  if (props.rightData.type == "basic") {
    return [
      {
        formType: "input",
        prop: "ip",
        label: "IP地址",
        disabled: true,
      },

      {
        formType: "input",
        prop: "lanIp",
        label: "端口服务",
        placeholder: "请添加端口服务",
        disabled: true,
      },
    ];
  } else {
    return [
      {
        formType: "input",
        prop: "ip",
        label: "IP地址",
        disabled: true,
      },
    ];
  }
});

let store = reactive({
  abstract: {},
  resourceServers: [],
  resourceSoftwares: [],
  spare1: [],
  terminal: {},
  terminalIps: [],
  resourceAssemblys: [],
});
let formListIp = reactive([
  {
    formType: "number",
    prop: "port",
    label: "端口",
    itemWidth: "calc((97% - 70px) / 2 )",
    value: "",
    min: 0,
    max: 65535,
    precision: "0",
  },
  {
    formType: "select",
    prop: "serverAgreement",
    label: "服务协议",

    dictName: "service_agreement",
    filterable: true,
    itemWidth: "calc((97% - 70px) / 2 )",
    value: "",
  },
]);
// 打开添加弹框
let dynamicIpRef = ref();
let dynamicIpstatus = ref(false);
let addtoRef = ref();
function change() {
  dynamicIpstatus.value = true;
  addtoRef.value.open();
}
// 获取资产详情
let uId = ref("");
function getDetail() {
  uId.value = props.rightData.id;
  openIpNotConfirm({ id: props.assetId }).then((res) => {
    formData.ip = res.data.ip;
  });
  // 查看详情
  if (props.rightData.type == "basic") {
    assetsDetail({ id: props.rightData.id }).then((res) => {
      store.abstract = { ...res.data.resource, personList: res.data.resource.personList }; //基本信息+责任人
      store.resourceServers = res.data.resource.resourceServers;
      store.resourceSoftwares = res.data.resource.resourceSoftwares;
      store.resourceAssemblys = res.data.resource.resourceAssemblys;
      store.resourceServers.forEach((item) => {
        item.type = item.serviceAgreementName;
      });
    });
  } else {
    getTerminalDetail({ id: props.rightData.id }).then((res) => {
      console.info(res);
      store.terminal = { ...res.data.terminal, personList: res.data.terminal.personList }; //基本信息+责任人

      store.terminalIps = res.data.terminal.terminalIps;
    });
  }
}
let userNames = computed(() => {
  let user = "";
  if (props.rightData.type == "basic") {
    user = store.abstract.personList ? store.abstract.personList.map((item) => item.userName).join() : "";
  } else {
    user = store.terminal.personList ? store.terminal.personList.map((item) => item.userName).join() : "";
  }
  return user;
});
getDetail();
function AddForm() {
  for (let item of dynamicIpRef.value.list) {
    if (item[0].value == undefined || item[1].value == "") {
      ElMessage.warning(item[0].value == undefined ? "请输入端口" : "请输入服务协议");
      return false;
    }
  }
  dynamicIpRef.value.list.forEach((item) => {
    store.resourceServers.push({ ip: formData.ip, port: item[0].value, type: getDictText(item[1].value) });
    store.spare1.push([item[0].value, item[1].value]);
    formAdd.serverAgreement = "";
    formAdd.number = "";
    dynamicIpstatus.value = false;
    addtoRef.value.close();
  });
}
function getDictText(value) {
  let selecteditem = typeOptions.value.find((item) => {
    return item.dictValue == value;
  });

  return selecteditem.dictLabel;
}
// 获取字典值内容
let typeOptions = ref([]);
// 获取字典值
function getDictValyes() {
  getDicts("service_agreement").then((res) => {
    console.info(res);
    typeOptions.value = res.data;
  });
}
getDictValyes();
// 保存变更
let emits = defineEmits(["close"]);
function saveAssetMerge() {
  if (props.rightData.type == "basic") {
    let val = {
      id: uId.value,
      spare1: props.assetId,
      spare2: store.spare1
        .map((item) => {
          return item.join("#_ipServer_#");
        })
        .join("#_ssp_#"),
      ip: formData.ip,
    };
    if (val.spare2 !== "") {
      ElMessageBox.confirm("确定要变更资产吗？", "信息", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        changeIpNotConfirmAssets(val).then(() => {
          ElMessage({
            message: "操作成功",
            type: "success",
          });
          store.spare1 = [];

          emits("close", "save");
        });
      });
    } else {
      ElMessage({
        type: "info",
        message: "请先添加端口/服务！",
      });
    }
  } else {
    ElMessageBox.confirm("确定要变更资产吗？", "信息", {
      distinguishCancelAndClose: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      changeIpNotConfirmTerminalAssets({ id: uId.value, spare1: props.assetId }).then(() => {
        ElMessage({
          message: "操作成功",
          type: "success",
        });
        store.spare1 = [];

        emits("close", "save");
      });
    });
  }
}
function closeFn() {
  store.spare1 = [];
  emits("close");
}
</script>

<style lang="scss" scoped>
.button {
  margin-right: 10px;
}
.addInfo {
  :deep .el-form-item__content {
    display: flex;
  }
  .btn {
    margin-left: 20px;
  }
}
.onname {
  p {
    margin: 20px;
  }
}
.input-nane {
  margin-top: 20px;
}
.inp {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf1;
  margin-left: 20px;
}
.magin-auto {
  margin-left: 15px;
}
.allAssemblyBody {
  .title {
    width: 120px;
    float: left;
  }
  > p {
    float: left;
    width: calc(100% - 130px);
    .assembly {
      margin-right: 10px;
    }
  }
}
:deep(.ip-port-list) {
  & > li {
    width: 100%;
  }
}
.asset_detail_class {
  width: calc(50% - 10px);
}
@media screen and (max-width: 1900px) {
  .asset_detail_class {
    width: calc(50% - 10px);
  }
}
@media screen and (max-width: 1500px) {
  .asset_detail_class {
    width: calc(100% - 10px);
  }
}
.soft-ul {
  > li {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid #ebedf1;
    > div {
      flex: 1;
    }
  }
}
</style>
