<template>
  <div>
    <workbench-header :userRoles="userRoles"></workbench-header>
    <el-row :gutter="20" class="margin-top20">
      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title">事件任务审核</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetail(eventAuditState.first)" v-if="JSON.stringify(eventAuditState.first) !== '{}'">
              <p>{{ eventAuditState.first.title }}</p>
              <p>
                <span>{{ minToDate(eventAuditState.first.count) }}</span>
                <span>
                  <el-tag :type="levelData[eventAuditState.first.levelId]">{{ eventAuditState.first.levelName }}</el-tag>
                </span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count event pointer" @click="eventAudit">{{ eventAuditState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table :data="eventAuditState.data" @row-click="toEventDetail">
            <el-table-column prop="title" label="任务名称"></el-table-column>
            <el-table-column label="时长">
              <template #default="scope">
                <span
                  v-if="scope.row.count < parseInt(eventAuditState.time.firstInterval)"
                  :class="scope.row.count < eventAuditState.time.firstInterval ? '' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else-if="scope.row.count > parseInt(eventAuditState.time.secondInterval)"
                  :class="scope.row.count > eventAuditState.time.secondInterval ? 'work_time' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.count > parseInt(eventAuditState.time.firstInterval) && scope.row.count < parseInt(eventAuditState.time.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                  >{{ minToDate(scope.row.count) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="等级">
              <template #default="scope">
                <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!--  综合服务任务审核    -->
      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title">综合服务任务审核</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetailOs(eventAuditStateOs.first)" v-if="JSON.stringify(eventAuditStateOs.first) !== '{}'">
              <p>{{ eventAuditStateOs.first.title }}</p>
              <p>
                <!--                <span>{{ minToDate(eventAuditStateOs.first.count) }}</span>-->
                <span>
                  <el-tag :type="levelData[eventAuditStateOs.first.levelId]">{{ eventAuditStateOs.first.levelName }}</el-tag>
                </span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count event pointer" @click="eventAuditOs">{{ eventAuditStateOs.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table :data="eventAuditStateOs.data" @row-click="toEventDetailOs">
            <el-table-column prop="title" label="任务名称"></el-table-column>
            <el-table-column label="等级" width="100px">
              <template #default="scope">
                <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title">事件审核</div>
          <div class="margin-bottom20">
            <div
              class="card_first pointer"
              @click="toEventAuditDetail(eventSubmitAuditState.first)"
              v-if="JSON.stringify(eventSubmitAuditState.first) !== '{}'"
            >
              <p>{{ eventSubmitAuditState.first.title }}</p>
              <p>
                <span>{{ minToDate(eventSubmitAuditState.first.duration) }}</span>
                <el-tag :type="levelData[eventSubmitAuditState.first.levelId]">{{ eventSubmitAuditState.first.levelName }}</el-tag>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count alert pointer" @click="toEventSubmitAudit">{{ eventSubmitAuditState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table :data="eventSubmitAuditState.data" @row-click="toEventAuditDetail">
            <el-table-column prop="title" label="事件名称"></el-table-column>
            <el-table-column prop="levelId" label="事件级别">
              <template #default="scope">
                <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="时长">
              <template #default="scope">
                <div>{{ minToDate(scope.row.duration) }}</div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title" v-if="userRoles.indexOf('permission:one') >= 0">待确认告警</div>
          <div class="card_title" v-if="userRoles.indexOf('permission:two') >= 0">疑似误诊审核</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="openAlertDetail(warningState.first)" v-if="JSON.stringify(warningState.first) !== '{}'">
              <p>{{ warningState.first.title }}</p>
              <p>
                <span>{{ minToDate(warningState.first.jiange) }}</span>
                <span
                  >已告警<span>{{ warningState.first.num }}</span
                  >次</span
                >
                <span class="clearfix"></span>
              </p>
            </div>
            <div
              v-if="userRoles.indexOf('permission:one') >= 0 && warningState.viewAllAlert"
              class="card_count alert pointer"
              @click="toPendingWarnings"
            >
              {{ warningState.total }}
            </div>
            <div v-if="userRoles.indexOf('permission:two') >= 0" class="card_count alert pointer">{{ warningState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="warningTable" :data="warningState.data" @row-click="openAlertDetail">
            <el-table-column prop="title" label="告警名称" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">{{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="delStatus" label="申请删除状态" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">{{ scope.row.delStatus }}</span>
              </template>
            </el-table-column>
            <el-table-column label="时长">
              <template #default="scope">
                <span
                  v-if="scope.row.jiange < parseInt(time.intervalParam.firstInterval)"
                  :class="scope.row.jiange < time.intervalParam.firstInterval ? '' : ''"
                  >{{ minToDate(scope.row.jiange) }}
                </span>
                <span
                  v-else-if="scope.row.jiange > parseInt(time.intervalParam.secondInterval)"
                  :class="scope.row.jiange > time.intervalParam.secondInterval ? 'work_time' : ''"
                  >{{ minToDate(scope.row.jiange) }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.jiange > parseInt(time.intervalParam.firstInterval) && scope.row.jiange < parseInt(time.intervalParam.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                  >{{ minToDate(scope.row.jiange) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="告警次数">
              <template #default="scope">
                <span
                  v-if="scope.row.jiange < parseInt(time.intervalParam.firstInterval)"
                  :class="scope.row.jiange < time.intervalParam.firstInterval ? '' : ''"
                  >{{ scope.row.num }}
                </span>
                <span
                  v-else-if="scope.row.jiange > parseInt(time.intervalParam.secondInterval)"
                  :class="scope.row.jiange > time.intervalParam.secondInterval ? 'work_time' : ''"
                  >{{ scope.row.num }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.jiange > parseInt(time.intervalParam.firstInterval) && scope.row.jiange < parseInt(time.intervalParam.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                >
                  {{ scope.row.num }}
                </span>
                次
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:asset') >= 0">
        <el-card class="card">
          <div class="card_title">待确认资产</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" v-if="JSON.stringify(assetState.first) !== '{}'" @click="openAssetDialog(assetState.first)">
              <p>{{ assetState.first.ip }}</p>
              <p>
                <span>{{ minToDate(assetState.first.count) }}</span>
                <span class="asset" v-if="assetState.first.type == '1'">IP新增</span>
                <span class="asset" v-else-if="assetState.first.type == '0'">端口/服务新增</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count asset">{{ assetState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="assetTable" :data="assetState.data" @row-click="openAssetDialog">
            <el-table-column prop="title" label="IP" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">{{ scope.row.ip }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类型">
              <template #default="scope">
                <span v-if="scope.row.type == '1'">IP新增</span>
                <span v-else-if="scope.row.type == '0'">端口/服务新增</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="时长">
              <template #default="scope">
                <span
                  v-if="scope.row.count < parseInt(time.intervalParamAsset.firstInterval)"
                  :class="scope.row.count < time.intervalParamAsset.firstInterval ? '' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else-if="scope.row.count > parseInt(time.intervalParamAsset.secondInterval)"
                  :class="scope.row.count > time.intervalParamAsset.secondInterval ? 'work_time' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.count > parseInt(time.intervalParamAsset.firstInterval) &&
                    scope.row.count < parseInt(time.intervalParamAsset.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                  >{{ minToDate(scope.row.count) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title">待处理事件任务</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetail(eventState.first)" v-if="JSON.stringify(eventState.first) !== '{}'">
              <p>{{ eventState.first.eventTitle }}</p>
              <p>
                <span>{{ eventState.first.title }}</span>
                <span>{{ minToDate(eventState.first.count) }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count event" @click="tasksreview">{{ eventState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="eventTaskTable" :data="eventState.data" @row-click="toEventDetail">
            <el-table-column prop="title" label="事件名称" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">{{ scope.row.eventTitle }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="任务名称">
              <template #default="scope">
                <span> {{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="历时">
              <template #default="scope">
                <span
                  v-if="scope.row.count < parseInt(time.intervalParamEvent.firstInterval)"
                  :class="scope.row.count < time.intervalParamEvent.firstInterval ? '' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else-if="scope.row.count > parseInt(time.intervalParamEvent.secondInterval)"
                  :class="scope.row.count > time.intervalParamEvent.secondInterval ? 'work_time' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.count > parseInt(time.intervalParamEvent.firstInterval) &&
                    scope.row.count < parseInt(time.intervalParamEvent.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                  >{{ minToDate(scope.row.count) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!--  待处理综合服务任务  -->
      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title">待处理综合服务任务</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetailOs(offeventState.first)" v-if="JSON.stringify(offeventState.first) !== '{}'">
              <p>{{ offeventState.first.eventTitle }}</p>
              <p>
                <span>{{ offeventState.first.title }}</span>
                <span>{{ minToDate(offeventState.first.count) }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count event" @click="tasksreviewOs">{{ offeventState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="eventTaskTable" :data="offeventState.data" @row-click="toEventDetailOs">
            <el-table-column prop="title" label="综合服务名称" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">{{ scope.row.eventTitle }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="任务名称">
              <template #default="scope">
                <span> {{ scope.row.title }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title">待认领任务</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetail(eventTaskState.first)" v-if="JSON.stringify(eventTaskState.first) !== '{}'">
              <p>{{ eventTaskState.first.eventTitle }}</p>
              <p>
                <span>{{ eventTaskState.first.title }}</span>
                <span class="taskGroup">{{ eventTaskState.first.taskGroup }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count eventTask" @click="claim">{{ eventTaskState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table :data="eventTaskState.data" @row-click="toEventDetail">
            <el-table-column prop="eventTitle" label="事件名称"></el-table-column>
            <el-table-column prop="title" label="任务名称"></el-table-column>
            <el-table-column prop="taskGroup" label="阶段"></el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 待认领综合服务任务 -->
      <el-col :span="12" class="margin-bottom20" v-if="userRoles.indexOf('permission:one') >= 0 || userRoles.indexOf('permission:two') >= 0">
        <el-card class="card">
          <div class="card_title">待认领综合服务任务</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetailOs(offeventTaskState.first)" v-if="JSON.stringify(offeventTaskState.first) !== '{}'">
              <p>{{ offeventTaskState.first.eventTitle }}</p>
              <p>
                <span>{{ offeventTaskState.first.title }}</span>
                <span class="taskGroup">{{ offeventTaskState.first.taskGroup }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count eventTask" @click="claimOs">{{ offeventTaskState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table :data="offeventTaskState.data" @row-click="toEventDetailOs">
            <el-table-column prop="eventTitle" label="综合服务名称"></el-table-column>
            <el-table-column prop="title" label="任务名称"></el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="24" class="margin-bottom20">
        <el-card>
          <full-calendar ref="fullCalendar" :ids="ids" @cycleDetail="selectScheduleTask" @getTaskDetail="getFullCalenDetail"></full-calendar>
        </el-card>
      </el-col>
    </el-row>
    <!-- 告警详情弹窗 疑似误诊审核 待确认告警 -->
    <xel-dialog :title="alarmDialogTitle" ref="alertDialog" width="60%" @close="closeAlertDialog" :ishiddenDialog="true">
      <alert-detail v-if="alertDetailShow" :alertId="alertId" @close="closeAlertDetail"></alert-detail>
    </xel-dialog>
    <!-- 资产详情弹窗 -->
    <xel-dialog title="待确认资产" ref="assetDialog" width="60%" @close="closeAssetDialog" :ishiddenDialog="true">
      <asset-detail v-if="assetDetailShow" :ipUuid="ipUuid" @close="closeAssetDetail"></asset-detail>
    </xel-dialog>
    <!-- 日历任务详情 -->
    <xel-dialog title="查看任务" ref="taskDetail" @close="closeFullcalenIdalog" :ishiddenDialog="true">
      <full-calendar-detail v-if="taskDetailShow" :fullCalendarTaskId="fullCalendarTaskId"></full-calendar-detail>
    </xel-dialog>
  </div>
</template>

<script setup>
import WorkbenchHeader from "./header.vue";
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { getWarningList, getNoticeiIterval, selectAssets } from "@/api/workSpace/workbench";

import { getNotFinishedTaskList, selectUnclaimedTaskPage, selectAuditTaskList, getUnAssignAuditList, getPreAuditList } from "@/api/workSpace/event";
import { getNotFinishedTaskListOs, selectAuditTaskListOs, selectUnclaimedTaskPageOs } from "@/api/offStandardEvent/workSpace/event";
import { Level_Data } from "@/config/constant";
import FullCalendar from "./fullCalendar.vue";
import { minToDate } from "@/utils/minToDate";
import AlertDetail from "./alertDetail.vue"; // 告警详情弹窗
import AssetDetail from "./assetDetail.vue"; // 资产详情弹窗
import FullCalendarDetail from "./fullCalendarDetail.vue";
import { useRouter } from "vue-router";
let props = defineProps({
  userRoles: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

onMounted(() => {
  getTime();
  if (props.userRoles.indexOf("permission:one") >= 0 || props.userRoles.indexOf("permission:asset") >= 0) {
    // 待确认告警
    wargningList();
    // 待确认资产
    assetsList();
    // 待处理事件任务
    getEventTaskList();
    // 待认领任务
    getUnclaimedTaskPage();
  } else if (props.userRoles.indexOf("permission:two") >= 0) {
    // 事件任务审核
    getAuditTaskList();
    // 待处理事件任务
    getEventTaskList();
    // 待认领任务
    getUnclaimedTaskPage();
    // 疑似误诊审核
    wargningList();
    // 事件审核
    eventAuditList();
  }
});
const router = useRouter();
// 获取时间间隔
let time = reactive({
  intervalParam: {},
  intervalParamAsset: {},
  intervalParamEvent: {},
});
function getTime() {
  getNoticeiIterval("event").then((res) => {
    time.intervalParam = res.data;
  });
  getNoticeiIterval("asset").then((res) => {
    time.intervalParamAsset = res.data;
  });
}
//一线二线获取待提报审核事件
let eventSubmitAuditState = reactive({
  first: {},
  data: [],
  total: 0,
});

function eventAuditList() {
  //二线工作台--事件审核--左侧列表展示信息
  getPreAuditList({ pageNum: 1, pageSize: 5 }).then((res) => {
    let arr = JSON.parse(JSON.stringify(res.data));
    eventSubmitAuditState.first = {};
    if (arr.total > 0) {
      eventSubmitAuditState.first = arr.rows[0];
      eventSubmitAuditState.data = arr.rows.splice(1, arr.rows.length - 1);
    }
  });
  //二线工作台--事件审核-右侧个数展示total值，点进去列表展示接口结果
  getUnAssignAuditList().then((res) => {
    eventSubmitAuditState.total = res.data.total;
  });
}
//一线二线获取待确认告警
let warningState = reactive({
  first: {},
  data: [],
  total: 0,
  viewAllAlert: false,
});
function wargningList() {
  getWarningList().then((res) => {
    let arr = res.data;
    warningState.total = res.total;
    warningState.viewAllAlert = res.viewAllAlert;
    warningState.first = {};
    if (arr.length > 0) {
      warningState.first = arr[0];
      warningState.data = arr.splice(1, arr.length - 1);
    }
  });
}
// 跳转到待确认告警总列表
function toPendingWarnings() {
  router.push({
    name: "PendingWarnings",
    // params: {},
  }); //路由跳转
}
// 一线二线待确认资产
let assetState = reactive({
  first: {},
  total: 0,
  data: [],
});
function assetsList() {
  selectAssets().then((res) => {
    // assetState.total = res.total;
    assetState.total = res.data.length;
    let arr = res.data;
    assetState.first = {};
    if (arr.length > 0) {
      assetState.first = arr[0];
      assetState.data = arr.splice(1, arr.length - 1);
    }
  });
}

//   待处理事件任务
let eventState = reactive({
  first: {},
  total: 0,
  data: [],
});

/* 待处理综合服务任务 */
let offeventState = reactive({
  first: {},
  total: 0,
  data: [],
});
function getEventTaskList() {
  getNotFinishedTaskList({ pageNum: 1, pageSize: 5 }).then((res) => {
    let resData = res.data;
    time.intervalParamEvent = resData.resultMap.intervalParam;
    eventState.total = resData.total;
    eventState.first = {};
    if (resData.rows.length > 0) {
      eventState.first = resData.rows[0];
      eventState.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });

  /* 待处理综合服务任务 */
  getNotFinishedTaskListOs({ pageNum: 1, pageSize: 5 }).then((res) => {
    let resData = res.data;
    time.intervalParamEvent = resData.resultMap.intervalParam;
    offeventState.total = resData.total;
    offeventState.first = {};
    if (resData.rows.length > 0) {
      offeventState.first = resData.rows[0];
      offeventState.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });
}
// 待认领任务
let eventTaskState = reactive({
  first: {},
  data: [],
  total: 0, //
});

let offeventTaskState = reactive({
  first: {},
  data: [],
  total: 0, //
});

function getUnclaimedTaskPage() {
  selectUnclaimedTaskPage({ pageNum: 1, pageSize: 5 }).then((res) => {
    let resData = res.data;
    eventTaskState.total = resData.total;
    eventTaskState.first = {};
    if (resData.rows.length > 0) {
      eventTaskState.first = resData.rows[0];
      eventTaskState.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });

  /* 待认领综合服务任务 */
  selectUnclaimedTaskPageOs({ pageNum: 1, pageSize: 5 }).then((res) => {
    let resData = res.data;
    offeventTaskState.total = resData.total;
    offeventTaskState.first = {};
    if (resData.rows.length > 0) {
      offeventTaskState.first = resData.rows[0];
      offeventTaskState.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });
}
// 跳转到事件任务详情
function toEventDetail(row) {
  router.push({
    name: "EventDetail",
    params: {
      id: row.eventId,
    },
    query: {
      taskId: row.id,
      taskName: row.title,
      isManageTask: row.isManageTask,
    },
  }); //路由跳转
}
//跳转到事件任务详情
function toEventAuditDetail(row) {
  router.push({
    name: "EventDetail",
    params: {
      id: row.id,
    },
    query: {
      auditResultId: row.auditResultId,
    },
  }); //路由跳转
}
// 跳转到待处理综合服务任务
function toEventDetailOs(row) {
  router.push({
    name: "OffEventDetail",
    params: {
      id: row.eventId,
    },
    query: {
      taskId: row.id,
      taskName: row.title,
      isManageTask: row.isManageTask,
    },
  }); //路由跳转
}

//告警详情弹窗
let alertId = ref("");
let alertDetailShow = ref(false);
const alarmDialogTitle = ref("待确认告警");
let alertDialog = ref();
function openAlertDetail(row) {
  alertId.value = row.alertId;
  alertDialog.value.open();
  alertDetailShow.value = true;
  if (props.userRoles.indexOf("permission:one") >= 0) {
    alarmDialogTitle.value = "待确认告警";
  } else {
    alarmDialogTitle.value = "疑似误诊审核";
  }
}
function closeAlertDialog() {
  console.log(1111);
  alertDialog.value.close();
  alertDetailShow.value = false;
}
// 告警详情操作后向前传值
function closeAlertDetail(data) {
  wargningList();
  closeAlertDialog();
}
// 资产详情弹窗
let assetDialog = ref();
let ipUuid = ref("");
let assetDetailShow = ref(false);
function openAssetDialog(row) {
  ipUuid.value = row.ipUuid;
  assetDetailShow.value = true;
  assetDialog.value.open();
}
function closeAssetDialog() {
  assetDialog.value.close();
  assetDetailShow.value = false;
}
function closeAssetDetail(data) {
  assetsList();
  closeAssetDialog();
}
//  获取日历详情
let taskDetailShow = ref(false);
let fullCalendarTaskId = ref("");
let taskDetail = ref();
function getFullCalenDetail(id) {
  taskDetailShow.value = true;
  fullCalendarTaskId.value = id;
  taskDetail.value.open();
}
function closeFullcalenIdalog() {
  taskDetailShow.value = false;
}
//  事件任务审核
let eventAuditState = reactive({
  first: {},
  data: [],
  total: 0,
  time: {},
});

let eventAuditStateOs = reactive({
  first: {},
  data: [],
  total: 0,
  time: {},
});

let state = reactive({
  levelData: Level_Data,
});
let { formData } = toRefs(state);
let { levelData } = toRefs(state);
// selectAuditTaskList
function getAuditTaskList() {
  selectAuditTaskList({ pageNum: 1, pageSize: 5 }).then((res) => {
    let resData = res.data;
    eventAuditState.first = {};
    eventAuditState.total = resData.total;
    eventAuditState.time = resData.intervalParam;
    if (resData.rows.length > 0) {
      eventAuditState.first = resData.rows[0];
      eventAuditState.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });

  /* 综合服务任务审核 */
  selectAuditTaskListOs({ pageNum: 1, pageSize: 5 }).then((res) => {
    let resData = res.data;
    eventAuditStateOs.first = {};
    eventAuditStateOs.total = resData.total;
    eventAuditStateOs.time = resData.intervalParam;
    if (resData.rows.length > 0) {
      eventAuditStateOs.first = resData.rows[0];
      eventAuditStateOs.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });
}
//
function tasksreview() {
  router.push({ name: "Tasksreviewed" });
}

function tasksreviewOs() {
  router.push({ name: "Tasksreviewed", params: { type: "Os" } });
}

//
function claim() {
  router.push({ name: "PendingTasks" });
}

function claimOs() {
  router.push({ name: "PendingTasks", params: { type: "Os" } });
}
function eventAudit() {
  router.push({ name: "EventAudit" });
}
function eventAuditOs() {
  router.push({ name: "EventAudit", params: { type: "Os" } });
}
function toEventSubmitAudit() {
  router.push({ name: "EventSubmitAudit", params: { type: "Os" } });
}
</script>

<style lang="scss" scoped>
.card {
  .card_title {
    height: 22px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $fontColor;
    line-height: 22px;
  }
  .card_first {
    height: 50px;
    float: left;
    width: calc(100% - 110px);
    padding: 0px 20px;
    margin-top: 48px;
    border-left: 6px solid $color;
    border-radius: 5px 0px 0px 5px;
    > p {
      height: 25px;
      font-size: 12px;
      font-weight: 400;
      color: $fontColorSoft;
      line-height: 25px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:nth-child(2) {
        font-weight: 300;
      }
      > span:nth-child(1) {
        display: inline-block;
        margin-right: 30px;
      }
      > span:nth-child(2) {
        display: inline-block;
        &.asset {
          font-weight: 400;
          color: #6e7ca5;
          font-size: 12px;
          padding: 0px 8px;
          background: #eef5ff;
          border-radius: $radiusS;
        }
        &.taskGroup {
          font-weight: 400;
          color: #6e7ca5;
          font-size: 12px;
          padding: 0px 8px;
          background: #eef5ff;
          border-radius: $radiusS;
        }
        > span {
          background: #fcedee;
          border-radius: $radiusSM;
          padding: 2px 8px;
          margin: 0px 5px;
        }
      }
    }
  }
  .card_count {
    float: right;
    cursor: pointer;
    height: 99px;
    min-width: 99px;
    line-height: 99px;
    text-align: center;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.05);
    border-radius: $radiusL;
    font-size: 32px;
    font-weight: 600;
    &.alert {
      background: #dae6ff;
    }
    &.asset {
      background: #e4f9f0;
    }
    &.event {
      background: #fcedee;
    }
    &.eventTask {
      background: #fefae6;
    }
  }
}
.work_time1 {
  color: #0079fe;
  margin: 0px 5px;
}
.work_time {
  color: #ff0000;
  margin: 0px 5px;
}
:deep(.el-table__row) {
  cursor: pointer;
}
</style>
