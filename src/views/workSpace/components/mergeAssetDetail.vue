<template>
  <el-row :gutter="30">
    <el-col :span="10">
      <div class="title-bottom-line">
        <p>新增信息</p>
      </div>
      <el-form ref="form" label-width="130px" label-position="left" class="base-info-form">
        <el-form-item label="IP地址：">{{ state.leftData.length > 0 ? state.leftData[0].ip : "" }}</el-form-item>
        <el-form-item label="主机名称：">{{ state.leftData.length > 0 ? state.leftData[0].host : "" }}</el-form-item>
        <el-form-item label="操作系统：">{{ state.leftData.length > 0 ? state.leftData[0].os : "" }}</el-form-item>
        <el-form-item label="端口/服务：">
          <div v-for="(item, index) in state.leftData" :key="item.port + item.service" class="portAndSerive">
            <p v-if="item.showAdd">
              <span>{{ item.port }}</span>
              <span>{{ item.service }}</span>
            </p>
            <span v-if="state.type !== 'terminalAssetsIp' && item.showAdd">
              <el-button @click="changeIps(item, index)">
                <el-icon><arrow-right-bold /></el-icon>
              </el-button>
            </span>
            <div class="clearfix"></div>
          </div>
        </el-form-item>
      </el-form>
    </el-col>
    <el-col :span="14">
      <div class="title-bottom-line">
        <p>摘要</p>
      </div>
      <el-form ref="form2" label-width="130px" label-position="left" class="base-info-form">
        <div class="pull-left asset_detail_class">
          <el-form-item label="资产对象类型：">{{ state.type === "terminalAssetsIp" ? "终端资产" : "计算设备资产" }}</el-form-item>
          <el-form-item label="资产对象名称：">{{ state.rightData.name }}</el-form-item>
          <el-form-item label="主机名称：">{{ state.rightData.hostName }}</el-form-item>
        </div>
        <div class="pull-left asset_detail_class">
          <el-form-item label="等级保护级别：">{{ state.rightData.levelName }}</el-form-item>
          <el-form-item label="责任主体：">{{ state.rightData.deptName }}</el-form-item>
          <el-form-item label="责任人：">{{ state.rightData.assetsPerson }}</el-form-item>
        </div>
        <div class="clearfix"></div>
      </el-form>
      <div v-if="state.type === 'terminalAssetsIp'" class="title-bottom-line margin-top20">
        <p>局域网IP</p>
      </div>
      <div v-else class="title-bottom-line margin-top20">
        <p>局域网IP、端口与服务组</p>
      </div>
      <div style="padding: 0px 20px" v-if="state.type === 'terminalAssetsIp'">
        <div class="rightIp" v-for="item in state.rightData.terminalIps" :key="item.id">
          <span class="pull-left">{{ item.ip }}</span>

          <div class="clearfix"></div>
        </div>
      </div>
      <div v-else style="padding: 0px 20px">
        <div class="rightIp" v-for="item in state.ipset" :key="item">
          <span class="pull-left">{{ item }}</span>
          <div class="infoP">
            <p class="info" v-for="(server, index) in state.portsServices" v-show="item == server.ip" :key="index">
              <span v-if="item == server.ip">{{ server.port }}</span>
              <span v-if="item == server.ip && server.serviceAgreementName">{{ server.serviceAgreementName }}</span>
              <span v-if="server.showRemove" @click="removeIps(server, index)" style="color: #303133; cursor: pointer">
                <el-icon><close-bold /></el-icon>
              </span>
            </p>
          </div>
          <div class="clearfix"></div>
        </div>
      </div>
      <div v-if="state.type !== 'terminalAssetsIp'">
        <div class="title-bottom-line">
          <p>基础资源软件</p>
        </div>
        <div style="margin: 0px 20px">
          <ul
            class="soft-ul"
            v-if="
              state.rightData.resourceSoftwares !== null &&
              state.rightData.resourceSoftwares !== undefined &&
              state.rightData.resourceSoftwares.length > 0
            "
          >
            <li v-for="item in state.rightData.resourceSoftwares" :key="item.id">
              <div><span class="item-label">类型：</span>{{ item.softwareType }}</div>
              <div><span class="item-label">软件：</span>{{ item.softwareValue }}</div>
              <div><span class="item-label">版本：</span>{{ item.softwareEdition }}</div>
            </li>
          </ul>
          <div class="margin-top20 allAssemblyBody">
            <span class="title">其他应用组件：</span>
            <p class="allAssembly">
              <span v-if="state.rightData.resourceAssemblys !== undefined && state.rightData.resourceAssemblys.length == 0">暂无</span>
              <el-tag v-for="item in state.rightData.resourceAssemblys" style="margin-bottom: 10px" :key="item.id" class="assembly">{{
                item.assemblyName
              }}</el-tag>
            </p>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <div class="text-center margin-top30 margin-bottom20">
    <el-button @click="closeDetail">取消</el-button>
    <el-button type="primary" @click="saveMergeAsset">变更资产</el-button>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { selectServiceAssetsConfirmatio, getBasicPortsServic, changeTerminalAssets, changeAssets } from "@/api/workSpace/asset";
import { ElMessage, ElMessageBox } from "element-plus";
let props = defineProps({
  mergeData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let state = reactive({
  leftData: [],
  rightData: {},
  type: "",
  portsServices: [],
  ipset: [],
});
// 获取合并资产的详情
function getMergeDetail() {
  selectServiceAssetsConfirmatio({ ipUuid: props.mergeData.ipUuid, id: props.mergeData.assetId }).then((res) => {
    state.leftData = res.assets;
    // console.info(state.leftData);
    state.leftData.forEach((item) => {
      item.showAdd = true;
    });
    state.type = res.type;
    if (res.type == "terminalAssetsIp") {
      state.rightData = res.terminal;
    } else {
      state.rightData = res.resource;
      getBasicPortsServic({ assetsId: res.resource.id }).then((result) => {
        state.ipset = result.data.ipSet;
        state.portsServices = result.data.portsServices;
      });
    }
  });
}
getMergeDetail();
// 保存资产变更
function saveMergeAsset() {
  ElMessageBox.confirm("确认变更资产吗？", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }).then(() => {
    if (state.type === "terminalAssetsIp") {
      changeTerminalAssets({ id: state.rightData.id, ipUuid: props.mergeData.ipUuid }).then((res) => {
        ElMessage.success("合并成功");
        emits("close", "save");
      });
    } else {
      if (saveState.data.length > 0) {
        let ids = "";
        for (let i = 0; i < saveState.data.length; i++) {
          if (ids == "") {
            ids = ids + saveState.data[i];
          } else {
            ids = ids + "," + saveState.data[i];
          }
        }
        changeAssets({ ipUuid: props.mergeData.ipUuid, id: state.rightData.id, spare1: ids }).then((res) => {
          ElMessage.success("合并成功");
          emits("close", "save");
        });
      } else {
        ElMessage.warning("请先添加端口/服务");
      }
    }
  });
}
// 传值
let emits = defineEmits("close");
function closeDetail() {
  emits("close");
}
// 切换位置
let saveState = reactive({
  data: [],
});
function changeIps(item, index) {
  let ss = state.ipset.indexOf(item.ip);
  if (ss < 0) {
    state.ipset.push(item.ip);
  }
  // state.leftData.splice(index, 1);
  state.leftData[index].showAdd = false;
  item.showRemove = true;
  item.serviceAgreementName = item.service;
  state.portsServices.push(item);
  saveState.data.push(item.id);
}
function removeIps(item, index) {
  let ss = 0;
  state.portsServices.forEach((port) => {
    if (item.ip === port.ip) {
      ss = ss + 1;
    }
  });
  if (ss <= 1) {
    ElMessage.warning("至少保留一组IP、端口、服务");
    return false;
  } else {
    state.leftData.forEach((add) => {
      if (add.id === item.id) {
        add.showAdd = true;
      }
    });
    state.portsServices.splice(index, 1);
    let removeIndex = saveState.data.indexOf(item.id);
    saveState.data.splice(removeIndex, 1);
  }
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.portAndSerive {
  > p {
    float: left;
    margin-bottom: 10px;
    border: 1px solid #e2e2e4;
    padding: 5px 10px;
    border-radius: 4px;
    width: calc(100% - 120px);
    > span {
      display: inline-block;
      padding: 0px 7px;
      text-align: center;
      border-radius: $radiusS;
    }
    > span:nth-child(1) {
      border: 1px solid #dae6ff;
      background: #dae6ff;
      margin-right: 10px;
    }
    > span:nth-child(2) {
      border: 1px solid #e4f9f0;
      background: #e4f9f0;
    }
  }
  > span {
    float: left;
    width: 80px;
    margin-left: 20px;
    margin-top: 10px;
    cursor: pointer;
  }
}
.rightIp {
  border: 1px solid #e2e2e4;
  padding: 5px 10px;
  border-radius: 4px;
  width: 70%;
  margin-bottom: 10px;
  .infoP {
    float: right;
    .info {
      width: 100%;
      &:not(:last-child) {
        margin-bottom: 10px;
      }
      > span {
        display: inline-block;
        padding: 0px 7px;
        text-align: center;
        border-radius: $radiusS;
        color: #fff;
      }
      > span:nth-child(1) {
        border: 1px solid #4598c4;
        background: #4598c4;
        margin-right: 10px;
      }
      > span:nth-child(2) {
        border: 1px solid #749f83;
        background: #749f83;
      }
    }
  }
}
.soft-ul {
  > li {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid #ebedf1;
    > div {
      flex: 1;
    }
  }
}
.allAssemblyBody {
  .allAssembly {
    display: inline-block;
    word-break: break-all;
  }
  .title {
    width: 120px;
    float: left;
  }
  > p {
    float: left;
    width: calc(100% - 130px);
    .assembly {
      margin-right: 10px;
      white-space: break-spaces;
      height: auto;
    }
  }
}
.asset_detail_class {
  width: calc(50% - 10px);
}
@media screen and (max-width: 1900px) {
  .asset_detail_class {
    width: calc(50% - 10px);
  }
}
@media screen and (max-width: 1500px) {
  .asset_detail_class {
    width: calc(100% - 10px);
  }
}
</style>
