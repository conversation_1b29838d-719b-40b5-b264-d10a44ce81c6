<template>
  <div class="ondiv">
    <img v-if="userInfo.photoId" class="avatar" :src="'/outpost-api/system/file/getFile?fileId=' + userInfo.photoId" alt="" />
    <img v-else class="avatar" src="@/assets/imgs/default.png" alt="头像" />
    <div class="user-identity">
      <p class="onNane">Hi，{{ userInfo.nickName }}</p>
      <p class="analyst">{{ analyst }}</p>
    </div>
  </div>
  <div v-if="timeliness">
    <p class="timeliness">任务及时性</p>
    <el-progress :percentage="timeliness" />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { downloadUrl } from "@/api/system/download.js";
import { useStore } from "vuex";
let props = defineProps({
  timeliness: {
    type: String,
    default: "",
  },
  analyst: {
    type: String,
    default: "",
  },
});
const store = useStore();
let userInfo = computed(() => {
  return store.state.userInfo;
});
</script>

<style lang="scss" scoped>
.front {
  display: flex;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 100%;
  margin-right: 20px;
  vertical-align: middle;
  margin-left: 8px;
}
.ondiv {
  display: flex;
  min-width: 100%;
}
.timeliness {
  font-size: 12px;
  font-weight: 300;
  color: $fontColorSoft;
  margin-bottom: 23px;
  margin-left: 10px;
}
.user-identity {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100px;
  margin-bottom: 20px;
}
.onNane {
  font-size: 16px;
  font-weight: 500;
  color: $fontColor;
  // margin-top: 15px;
}
.analyst {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  background: $color;
  padding: 5px 0 5px 0;
  width: 100px;
  text-align: center;
  border-radius: 15px;
  // margin-top: 30px;
}
@media screen and (max-width: 1500px) {
  .avatar {
    width: 80px;
    height: 80px;
    margin: 0 10px 0 0;
  }

  .user-identity {
    height: 0;
  }
  .onNane {
    margin-bottom: 30px;
  }
}
</style>
