<template>
  <el-row :gutter="20">
    <el-col :span="5" class="heAdd user-section">
      <el-card> <businessCard :analyst="analyst" :timeliness="timeliness" v-if="timeliness"></businessCard> </el-card>
    </el-col>
    <el-col :span="19">
      <el-card class="heAdd count-ul">
        <ul>
          <li>
            <p class="examine">本年度资产审核数量</p>
            <p class="quantity">{{ delivery.yearAssetCount || 0 }}</p>
          </li>
          <li>
            <p class="examine">月度资产审核数量</p>
            <p class="quantity">{{ delivery.monthAssetCount || 0 }}</p>
          </li>
          <li>
            <p class="examine">月度风险处置任务数量</p>
            <p class="quantity">702</p>
          </li>
          <li>
            <p class="examine">本年度风险处置任务数量</p>
            <p class="quantity">1024</p>
          </li>
        </ul></el-card
      >
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import BusinessCard from "./businessCard.vue";
import { selectWorkBenchData } from "@/api/workSpace/workbench.js";
let props = defineProps({
  userRoles: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let analyst = ref("");
let timeliness = ref("");
if (props.userRoles.indexOf("permission:deliveryManager") >= 0) {
  analyst.value = "交付经理";
}
let delivery = ref({});
function onQuantity() {
  selectWorkBenchData().then(({ data }) => {
    delivery.value = data;
    timeliness.value = data.timelyRate;
  });
}
onQuantity();
</script>

<style scoped lang="scss">
ul {
  display: flex;
  li {
    width: 25%;
    border-right: 1px solid #ebedf1;
    margin-right: 50px;
  }
  li:last-child {
    border: 0;
  }
}
.examine {
  font-size: 14px;
  font-weight: 300;
  color: $fontColorSoft;
  margin-bottom: 20px;
  margin-top: 35px;
}
.quantity {
  font-size: 52px;
  font-weight: 600;
  color: #28334f;
}
.heAdd {
  height: 237px;
}
</style>
