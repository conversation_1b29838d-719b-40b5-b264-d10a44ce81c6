<template>
  <div>
    <workbench-header :userRoles="userRoles"></workbench-header>
    <el-row :gutter="20" class="margin-top20">
      <el-col :span="12">
        <el-card class="card">
          <div class="card_title">风险处置任务</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetail(moreTaskState.first)" v-if="JSON.stringify(moreTaskState.first) !== '{}'">
              <p>Case # {{ moreTaskState.first.eventNo }}</p>
              <p>
                <span>{{ moreTaskState.first.title }}</span>
                <span>{{ minToDate(moreTaskState.first.count) }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count eventTask">{{ moreTaskState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="eventTaskTable" :data="moreTaskState.data" @row-click="toEventDetail">
            <el-table-column prop="title" label="事件编号" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">Case # {{ scope.row.eventNo }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="任务名称">
              <template #default="scope">
                <span> {{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="时长">
              <template #default="scope">
                <span
                  v-if="scope.row.count < parseInt(moreTaskState.time.firstInterval)"
                  :class="scope.row.count < moreTaskState.time.firstInterval ? '' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else-if="scope.row.count > parseInt(moreTaskState.time.secondInterval)"
                  :class="scope.row.count > moreTaskState.time.secondInterval ? 'work_time' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.count > parseInt(moreTaskState.time.firstInterval) && scope.row.count < parseInt(moreTaskState.time.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                  >{{ minToDate(scope.row.count) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="card">
          <div class="card_title">抽检事件</div>
          <div class="margin-bottom20">
            <div
              class="card_first pointer"
              @click="openSamplingDetail(samplingTaskState.first)"
              v-if="JSON.stringify(samplingTaskState.first) !== '{}'"
            >
              <p>Case # - {{ samplingTaskState.first.spare1 }}</p>
              <p>
                <span>{{ minToDate(samplingTaskState.first.count) }}</span>
                <!-- <span>{{ minToDate(samplingTaskState.first.count) }}</span> -->
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count event">{{ samplingTaskState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="eventTaskTable2" :data="samplingTaskState.data" @row-click="openSamplingDetail">
            <el-table-column prop="title" label="事件名称" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">Case # - {{ scope.row.spare1 }} </span>
              </template>
            </el-table-column>

            <el-table-column prop="type" label="时长">
              <template #default="scope">
                <span
                  v-if="scope.row.count < parseInt(moreTaskState.time.firstInterval)"
                  :class="scope.row.count < moreTaskState.time.firstInterval ? '' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else-if="scope.row.count > parseInt(moreTaskState.time.secondInterval)"
                  :class="scope.row.count > moreTaskState.time.secondInterval ? 'work_time' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.count > parseInt(moreTaskState.time.firstInterval) && scope.row.count < parseInt(moreTaskState.time.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                  >{{ minToDate(scope.row.count) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12" class="margin-top20">
        <el-card class="card">
          <div class="card_title">已申请删除的告警</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="openAlertDetail(warningState.first)" v-if="JSON.stringify(warningState.first) !== '{}'">
              <p>{{ warningState.first.title }}</p>
              <p>
                <span>{{ warningState.first.applyTimeStr }}</span>
                <span class="asset">{{ warningState.first.userName }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count alert pointer" @click="toPendingWarnings">{{ warningState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="warningTable" :data="warningState.data" @row-click="openAlertDetail">
            <el-table-column label="告警名称" prop="title"></el-table-column>
            <el-table-column label="发生时间" prop="applyTimeStr"></el-table-column>
            <el-table-column label="申请人" prop="userName"></el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12" class="margin-top20">
        <el-card class="card">
          <div class="card_title">待处理事件任务</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetail(eventState.first)" v-if="JSON.stringify(eventState.first) !== '{}'">
              <p>{{ eventState.first.eventTitle }}</p>
              <p>
                <span>{{ eventState.first.title }}</span>
                <span>{{ minToDate(eventState.first.count) }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count eventTask pointer" @click="tasksreview">{{ eventState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table ref="eventTaskTable" :data="eventState.data" @row-click="toEventDetail">
            <el-table-column prop="title" label="事件名称" :show-overflow-tooltip="true">
              <template #default="scope">
                <span class="pointer">{{ scope.row.eventTitle }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="任务名称">
              <template #default="scope">
                <span> {{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="历时">
              <template #default="scope">
                <span
                  v-if="scope.row.count < parseInt(eventState.time.firstInterval)"
                  :class="scope.row.count < eventState.time.firstInterval ? '' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else-if="scope.row.count > parseInt(eventState.time.secondInterval)"
                  :class="scope.row.count > eventState.time.secondInterval ? 'work_time' : ''"
                  >{{ minToDate(scope.row.count) }}
                </span>
                <span
                  v-else
                  :class="
                    scope.row.count > parseInt(eventState.time.firstInterval) && scope.row.count < parseInt(eventState.time.secondInterval)
                      ? 'work_time1'
                      : ''
                  "
                  >{{ minToDate(scope.row.count) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12" class="margin-top20">
        <el-card class="card">
          <div class="card_title">待认领任务</div>
          <div class="margin-bottom20">
            <div class="card_first pointer" @click="toEventDetail(eventTaskState.first)" v-if="JSON.stringify(eventTaskState.first) !== '{}'">
              <p>{{ eventTaskState.first.eventTitle }}</p>
              <p>
                <span>{{ eventTaskState.first.title }}</span>
                <span class="taskGroup">{{ eventTaskState.first.taskGroup }}</span>
                <span class="clearfix"></span>
              </p>
            </div>
            <div class="card_count eventTask pointer" @click="claim">{{ eventTaskState.total }}</div>
            <div class="clearfix"></div>
          </div>
          <el-table :data="eventTaskState.data" @row-click="toEventDetail">
            <el-table-column prop="eventTitle" label="事件名称"></el-table-column>
            <el-table-column prop="title" label="任务名称"></el-table-column>
            <el-table-column prop="taskGroup" label="阶段"></el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="24" class="margin-top20">
        <el-card>
          <full-calendar ref="fullCalendar" :ids="ids" @cycleDetail="selectScheduleTask" @getTaskDetail="getFullCalenDetail"></full-calendar>
        </el-card>
      </el-col>
    </el-row>
    <xel-dialog title="抽检事件" ref="samplingEventDialog" @close="closeSamplingDialog" size="large" :ishiddenDialog="true">
      <sampling-event-detail v-if="samplingDetailShow" :eventId="eventId" :eventSamplingId="eventSamplingId"></sampling-event-detail>
    </xel-dialog>
    <!-- 已申请删除告警详情 -->
    <xel-dialog title="已申请删除的告警" ref="alertDialog" width="60%" @close="closeAlertDialog" :ishiddenDialog="true">
      <alert-detail v-if="alertDetailShow" :alertId="alertId" @close="closeAlertDetail"></alert-detail>
    </xel-dialog>
    <xel-dialog title="查看任务" ref="taskDetail" @close="closeFullcalenIdalog" :ishiddenDialog="true">
      <full-calendar-detail v-if="taskDetailShow" :fullCalendarTaskId="fullCalendarTaskId"></full-calendar-detail>
    </xel-dialog>
  </div>
</template>

<script setup>
import WorkbenchHeader from "./header.vue";
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { minToDate } from "@/utils/minToDate";
import { selectMoreTask, selectApplyAlertList } from "@/api/workSpace/workbench";
import { selectAuditSamplingTask, selectUnclaimedTaskPage, getNotFinishedTaskList } from "@/api/workSpace/event";
import AlertDetail from "./alertDetail.vue"; // 告警详情弹窗
import SamplingEventDetail from "./samplingEventDetail.vue";
import FullCalendar from "./fullCalendar.vue";
import FullCalendarDetail from "./fullCalendarDetail.vue";
import { useRouter } from "vue-router";
const router = useRouter();
let props = defineProps({
  userRoles: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
// 风险处置任务
let moreTaskState = reactive({
  first: {},
  data: [],
  total: 0,
  time: {},
});
function getMoreTask() {
  selectMoreTask().then((res) => {
    let resData = res.data;
    moreTaskState.total = resData.total;
    moreTaskState.time = resData.intervalParam;
    if (resData.list.length > 0) {
      moreTaskState.first = resData.list[0];
      moreTaskState.data = resData.list.splice(1, resData.list.length - 1);
    }
  });
}
getMoreTask();
// 跳转到事件任务详情
function toEventDetail(row) {
  router.push({
    name: "EventDetail",
    params: {
      id: row.eventId,
    },
    query: {
      taskId: row.id,
      taskName: row.title,
      isManageTask: row.isManageTask,
    },
  }); //路由跳转
}
// 事件抽检
let samplingTaskState = reactive({
  first: {},
  data: [],
  time: {},
  total: 0,
});
function getAuditSamplingTask() {
  selectAuditSamplingTask().then((res) => {
    samplingTaskState.time = res.intervalParam;
    samplingTaskState.total = res.data.length;
    samplingTaskState.first = {};
    if (res.data.length > 0) {
      samplingTaskState.first = res.data[0];
      samplingTaskState.data = res.data.splice(1, res.data.length - 1);
    }
  });
}
getAuditSamplingTask();
// 抽检事件详情
let samplingEventDialog = ref();
let samplingDetailShow = ref(false);
let eventId = ref("");
let eventSamplingId = ref("");
function openSamplingDetail(row) {
  samplingEventDialog.value.open();
  samplingDetailShow.value = true;
  eventId.value = row.eventId;
  eventSamplingId.value = row.id;
}
function closeSamplingDialog() {
  samplingDetailShow.value = false;
  eventId.value = "";
  eventSamplingId.value = "";
  getAuditSamplingTask();
}
// 已申请删除告警
let warningState = reactive({
  first: {},
  data: [],
  total: 0,
});
function wargningList() {
  selectApplyAlertList({ pageNum: 1, pageSize: 5 }).then((res) => {
    let arr = res.data;
    warningState.total = arr.total;
    warningState.first = {};
    if (arr.list.length > 0) {
      warningState.first = arr.list[0];
      warningState.data = arr.list.splice(1, arr.list.length - 1);
    }
  });
}
wargningList();
let alertId = ref("");
let alertDetailShow = ref(false);
let alertDialog = ref();
function openAlertDetail(row) {
  alertId.value = row.alertId;
  alertDialog.value.open();
  alertDetailShow.value = true;
}
function closeAlertDialog() {
  alertDialog.value.close();
  alertDetailShow.value = false;
}
// 告警详情操作后向前传值
function closeAlertDetail(data) {
  wargningList();
  closeAlertDialog();
}
function toPendingWarnings() {
  router.push({
    name: "PendingWarningsDelete",
    // params: {},
  }); //路由跳转
}
//   待处理事件任务
let eventState = reactive({
  first: {},
  total: 0,
  data: [],
  time: {},
});
function getEventTaskList() {
  getNotFinishedTaskList({ pageNum: 1, pageSize: 5 }).then((res) => {
    console.info(res);
    let resData = res.data;
    eventState.time = resData.resultMap.intervalParam;
    eventState.total = resData.total;
    eventState.first = {};
    if (resData.rows.length > 0) {
      eventState.first = resData.rows[0];
      eventState.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });
}
getEventTaskList();
// 待认领任务
let eventTaskState = reactive({
  first: {},
  data: [],
  total: 0, //
});
function getUnclaimedTaskPage() {
  selectUnclaimedTaskPage({ pageNum: 1, pageSize: 5 }).then((res) => {
    console.info(res);
    let resData = res.data;
    eventTaskState.total = resData.total;
    eventTaskState.first = {};
    if (resData.rows.length > 0) {
      eventTaskState.first = resData.rows[0];
      eventTaskState.data = resData.rows.splice(1, resData.rows.length - 1);
    }
  });
}
getUnclaimedTaskPage();
// 日历
let taskDetailShow = ref(false);
let fullCalendarTaskId = ref("");
let taskDetail = ref();
function getFullCalenDetail(id) {
  taskDetailShow.value = true;
  fullCalendarTaskId.value = id;
  taskDetail.value.open();
}
function closeFullcalenIdalog() {
  taskDetailShow.value = false;
}
// 跳转事件详情
function tasksreview() {
  router.push({ name: "Tasksreviewed" });
}
//
function claim() {
  router.push({ name: "PendingTasks" });
}
function eventAudit() {
  router.push({ name: "EventAudit" });
}
</script>

<style lang="scss" scoped>
.card {
  .card_title {
    height: 22px;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: $fontColor;
    line-height: 22px;
  }
  .card_first {
    height: 50px;
    float: left;
    width: calc(100% - 110px);
    padding: 0px 20px;
    margin-top: 48px;
    border-left: 6px solid $color;
    border-radius: 5px 0px 0px 5px;
    > p {
      height: 25px;
      font-size: 12px;
      font-weight: 400;
      color: $fontColorSoft;
      line-height: 25px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:nth-child(2) {
        font-weight: 300;
      }
      > span:nth-child(1) {
        display: inline-block;
        margin-right: 30px;
      }
      > span:nth-child(2) {
        display: inline-block;
        &.asset {
          font-weight: 400;
          color: #6e7ca5;
          font-size: 12px;
          padding: 0px 8px;
          background: #eef5ff;
          border-radius: $radiusS;
        }
        &.taskGroup {
          font-weight: 400;
          color: #6e7ca5;
          font-size: 12px;
          padding: 0px 8px;
          background: #eef5ff;
          border-radius: $radiusS;
        }
        > span {
          background: #fcedee;
          border-radius: $radiusSM;
          padding: 2px 8px;
          margin: 0px 5px;
        }
      }
    }
  }
  .card_count {
    float: right;
    height: 99px;
    min-width: 99px;
    line-height: 99px;
    text-align: center;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.05);
    border-radius: $radiusL;
    font-size: 32px;
    font-weight: 600;
    &.alert {
      background: #dae6ff;
    }
    &.asset {
      background: #e4f9f0;
    }
    &.event {
      background: #fcedee;
    }
    &.eventTask {
      background: #fefae6;
    }
  }
}
.work_time1 {
  color: #0079fe;
  margin: 0px 5px;
}
.work_time {
  color: #ff0000;
  margin: 0px 5px;
}
:deep(.el-table__row) {
  cursor: pointer;
}
</style>
