<template>
  <div class="title-bottom-line">
    <p>新增信息</p>
  </div>
  <div class="" v-if="state.detail.length > 0">
    <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
      <el-form-item label="IP地址：">{{ state.detail[0].ip }}</el-form-item>
      <el-form-item label="主机名称：">{{ state.detail[0].host }}</el-form-item>
      <el-form-item label="操作系统：">{{ state.detail[0].os }}</el-form-item>
      <el-form-item label="端口/服务：">
        <p v-for="(item, index) in state.detail" :key="item.ipUuid" class="serveList" :class="index > 0 ? 'margin-top10' : ''">
          <span class="port">{{ item.port }}</span>
          <span class="service">{{ item.service }}</span>
        </p>
      </el-form-item>
    </el-form>
  </div>
  <div class="text-right margin-top20 margin-right20 margin-bottom20" v-if="state.detail.length > 0">
    <el-button type="danger" @click="toIgnoreAsset">
      <el-icon><circle-close /></el-icon>忽略
    </el-button>
    <el-button @click="mergeAssets">
      <svg class="icon" aria-hidden="true" style="margin-right: 5px; font-size: 14px">
        <use xlink:href="#icon-hebingdanyuange"></use></svg
      >与现有资产合并
    </el-button>
    <el-button v-if="state.detail[0].type == '1'" @click="addAsset">
      <el-icon><plus /></el-icon>新增资产
    </el-button>
  </div>
  <!-- 资产合并 -->
  <xel-dialog title="资产列表" ref="mergerAssetsDialog" @close="closeMergerAssets" :ishiddenDialog="true" width="80%">
    <merge-asset v-if="mergeShow" :isDialog="true" isDialogType="workbench" :ipUuid="ipUuid" @close="closeMerge"></merge-asset>
  </xel-dialog>
  <!-- 服务端口新增 -->
  <xel-dialog title="资产合并" ref="mergeDetail" width="80%" @close="detailShow = false" :ishiddenDialog="true">
    <merge-asset-detail v-if="detailShow" :mergeData="mergeData.data" @close="closeDetail"></merge-asset-detail>
  </xel-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { selectAssetsByIpUuid, ignoreAsset } from "@/api/workSpace/asset";
import MergeAsset from "./mergeAsset.vue";
import MergeAssetDetail from "./mergeAssetDetail.vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
let props = defineProps({
  ipUuid: {
    type: String,
    default: "",
  },
});
let state = reactive({
  detail: [],
});
// 获取资产详情内容
function getAssetDetail() {
  selectAssetsByIpUuid({ ipUuid: props.ipUuid }).then((res) => {
    console.info(res);
    state.detail = res.data;
  });
}
getAssetDetail();
// 资产忽略
function toIgnoreAsset() {
  ElMessageBox.confirm("确认忽略此条资产吗？", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }).then(() => {
    let ipUuid = props.ipUuid;
    ignoreAsset(ipUuid).then((res) => {
      ElMessage.success("资产忽略成功");
      emits("close");
    });
  });
}
const router = useRouter();
function addAsset() {
  router.push({
    name: "WorkBenchConfirmAdd",
    params: { type: 1, assetType: state.detail[0].type == 1 ? 2 : 1, confirmId: state.detail[0].ipUuid, id: state.detail[0].ipUuid },
  });
}
// 资产合并
let mergerAssetsDialog = ref();
let mergeShow = ref(false);
function mergeAssets() {
  console.info(state.detail[0].type);
  if (state.detail[0].type == "1") {
    mergeShow.value = true;
    mergerAssetsDialog.value.open();
  } else if (state.detail[0].type == "0") {
    mergeData.data = {
      ipUuid: state.detail[0].ipUuid,
      assetId: "",
    };
    detailShow.value = true;
    mergeDetail.value.open();
  }
}
function closeMergerAssets() {
  if (state.detail[0].type == "1") {
    mergeShow.value = false;
    mergerAssetsDialog.value.close();
  } else if (state.detail[0].type == "0") {
    detailShow.value = false;
    mergeDetail.value.close();
  }
}
// 传值关闭刷新
let emits = defineEmits(["close"]);
function closeMerge() {
  closeMergerAssets();
  emits("close");
}
// 合并 端口服务新增
let mergeDetail = ref();
let detailShow = ref();
let mergeData = reactive({
  data: {},
});
function closeDetail(data) {
  closeMergerAssets();
  if (data == "save") {
    emits("close");
  }
}
</script>

<style scoped lang="scss">
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.serveList {
  padding: 5px 10px;
  border: 1px solid #e2e2e4;
  width: 50%;
  border-radius: 4px;
  > span {
    padding: 0px 8px;
    border-radius: 5px;
    display: inline-block;
    margin-right: 10px;
  }
  > .port {
    background: #dae6ff;
  }
  > .service {
    background: #e4f9f0;
  }
}
</style>
