<template>
  <div class="title-bottom-line">
    <p>摘要</p>
  </div>
  <div class="">
    <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
      <el-form-item label="标题："> Case # - {{ stateDetail.detail.spare1 }}</el-form-item>
      <el-form-item label="事件级别：">
        <el-tag :type="levelData[stateDetail.detail.levelId]">{{ stateDetail.detail.levelName }}</el-tag>
      </el-form-item>
      <el-form-item label="事件描述：">
        <p v-html="stateDetail.detail.detail"></p>
      </el-form-item>
    </el-form>
  </div>
  <div v-if="stateDetail.auditList.length > 0" class="title-bottom-line margin-top20">
    <p>已审核任务</p>
  </div>
  <div v-if="stateDetail.auditList.length > 0">
    <p class="taskList" v-for="item in stateDetail.auditList" :key="item.id" @click="openTaskDetail(item.id, item.taskId, item.samplingStatus)">
      {{ item.taskTitle }}
    </p>
  </div>
  <div v-if="stateDetail.noAuditList.length > 0" class="title-bottom-line margin-top20">
    <p>未审核任务</p>
  </div>
  <div v-if="stateDetail.noAuditList.length > 0">
    <p class="taskList" v-for="item in stateDetail.noAuditList" :key="item.id" @click="openTaskDetail(item.id, item.taskId, item.samplingStatus)">
      {{ item.taskTitle }}
    </p>
  </div>
  <xel-dialog :title="taskDialogTitle" ref="taskDialog" @close="closeTaskDialog" size="large" :ishiddenDialog="true">
    <event-task-detail v-if="taskDetailShow" :samplingStatus="samplingStatus.data" @close="closeTaskDialog"></event-task-detail>
  </xel-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { Level_Data } from "@/config/constant";
import { selectEventById, getAuditedTaskList } from "@/api/workSpace/workbench";
import EventTaskDetail from "@/views/event/threatEvent/components/taskDetail.vue";
let state = reactive({
  levelData: Level_Data,
});
let { formData } = toRefs(state);
let { levelData } = toRefs(state);
let props = defineProps({
  eventId: {
    type: String,
    default: "",
  },
  eventSamplingId: {
    type: String,
    default: "",
  },
});
let stateDetail = reactive({
  detail: {},
  noAuditList: [],
  auditList: [],
});
// 获取事件详情
function getEventDetail() {
  selectEventById(props.eventId).then((res) => {
    stateDetail.detail = res.data;
  });
  // 获取任务
  getAuditedTaskList({ eventSamplingId: props.eventSamplingId, samplingStatus: "0" }).then((res) => {
    // 未审核任务
    stateDetail.noAuditList = res.data;
  });
  getAuditedTaskList({ eventSamplingId: props.eventSamplingId, samplingStatus: "1" }).then((res) => {
    // 已审核任务
    stateDetail.auditList = res.data;
  });
}
getEventDetail();
// 事件任务详情
let taskDialogTitle = ref("");
let taskDialog = ref();
let taskDetailShow = ref(false);
let samplingStatus = reactive({
  data: {},
});
// 打开详情弹窗
function openTaskDetail(id, taskId, status) {
  samplingStatus.data = {
    id: id,
    taskId: taskId,
    samplingStatus: status,
  };
  taskDetailShow.value = true;

  taskDialog.value.open();
}
function closeTaskDialog() {
  taskDialogTitle.value = "";
  taskDialog.value.close();
  taskDetailShow.value = false;
  getEventDetail();
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.taskList {
  line-height: 40px;
  border-bottom: 1px dashed #ebedf1;
  padding-left: 10px;
  margin: 0px 20px;
  cursor: pointer;
}
</style>
