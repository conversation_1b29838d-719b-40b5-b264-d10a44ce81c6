<template>
  <!-- 查询按钮 -->
  <el-button size="mini" v-if="!isSearch" @click="handBtn">
    <el-icon><search /></el-icon> 检索日志
  </el-button>
  <!-- 检索日志 -->
  <el-popover placement="bottom" :width="400" trigger="click" :hide-after="0" v-if="popoverShow && isSearch">
    <template #reference>
      <el-button size="mini" @click="backfillData">
        <el-icon><search /></el-icon> 检索日志
        <el-icon><ArrowDown /></el-icon>
      </el-button>
    </template>
    <div style="text-align: left">
      <el-form ref="search_forms">
        <div>
          时间范围：首轮告警发生时间向前
          <el-input-number
            size="mini"
            controls-position="right"
            v-model="formData.time"
            :precision="0"
            placeholder="时间"
            min="1"
            max="999999"
            style="width: 120px"
          />分钟
        </div>
        <div>
          <el-checkbox v-model="formData.address">设备地址</el-checkbox>
        </div>
        <div>
          <el-checkbox v-model="formData.devType">设备类型</el-checkbox>
        </div>
        <div>
          <el-checkbox v-model="formData.csrcip">首条日志源地址</el-checkbox>
        </div>
        <div>
          <el-checkbox v-model="formData.cdstip">首条日志目的地址</el-checkbox>
        </div>
      </el-form>

      <div class="text-center">
        <el-button round type="primary" @click="subBtnFun" :loading="loading">查询</el-button>
      </div>
    </div>
  </el-popover>
</template>

<script>
export default {
  name: "alertSearchBtn",
};
</script>

<script setup>
import { computed, ref } from "vue";
import { formatDate } from "@/utils/public";
import { useStore } from "vuex";
const store = useStore();

import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
const route = useRoute();
const router = useRouter();

let props = defineProps({
  /* 详情数据 */
  detailData: {
    type: Object,
    default: () => {
      return {};
    },
  },

  /* 是否显示检索日志 - 配置信息 */
  isSearch: {
    type: Boolean,
    default: () => {
      return true;
    },
  },

  /* 是否按钮传递 */
  isBtnEmits: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
});

/* 查询 - 表单项 */
let formData = ref({
  time: 15,
  address: false,
  devType: true,
  csrcip: true,
  cdstip: true,
});

/* 判断是否有配置信息 */
const backfillData = () => {
  if (sessionStorage.getItem("configForm")) {
    formData.value = {
      ...JSON.parse(sessionStorage.getItem("configForm")),
    };
  }
};

/* 查询 - 提交按钮 */
let popoverShow = ref(true);
let saveData = ref(JSON.parse(JSON.stringify(store.state.siem.saveData)));
const subBtnFun = () => {
  /* 判断formData.value.time 为大于0 且不能为空 */
  if (formData.value.time <= 0 || formData.value.time === undefined) {
    ElMessage.info("时间范围不能为空（1~999999）");
    return false;
  }
  formData.value.time = formData.value.time * 1;
  /* endTime = 首轮告警发生时间 */
  saveData.value.endTime = props.detailData.createTime;
  /* 首轮告警发生时间 - 时间范围，生成时间格式为 2024-01-01 00:00:00 */
  saveData.value.startTime = formatDate(new Date(props.detailData.createTime).getTime() - formData.value.time * 60 * 1000);

  saveData.value.filters = [];
  /* 设备地址 */
  if (formData.value.address) {
    saveData.value.filters.push({
      name: "cdevip",
      operator: "equal",
      value: props.detailData.devIp,
      nameText: "设备地址",
      operatorText: "等于",
      valueText: props.detailData.devIp,
      valueList: [],
      content: "",
      nameType: "",
      finished: true,
    });
  }
  /* 设备类型 */
  if (formData.value.devType) {
    saveData.value.filters.push({
      name: "cdevtype",
      operator: "equal",
      value: props.detailData.devType,
      nameText: "设备类型",
      operatorText: "等于",
      valueText: props.detailData.devType,
      content: "",
      nameType: "",
      finished: true,
    });
  }
  /* 首条日志源地址 */
  if (formData.value.csrcip) {
    saveData.value.filters.push({
      name: "csrcip",
      operator: "equal",
      value: props.detailData.csrcip,
      nameText: "源地址",
      operatorText: "等于",
      valueText: props.detailData.csrcip,
      valueList: [],
      content: "",
      nameType: "",
      finished: true,
    });
  }
  /* 首条日志目的地址 */
  if (formData.value.cdstip) {
    saveData.value.filters.push({
      name: "cdstip",
      operator: "equal",
      value: props.detailData.cdstip,
      nameText: "目的地址",
      operatorText: "等于",
      valueText: props.detailData.cdstip,
      valueList: [],
      content: "",
      nameType: "",
      finished: true,
    });
  }

  /* 将数据data存储到sessionStorage中*/
  sessionStorage.setItem("alertForm", JSON.stringify(saveData.value));
  store.commit("setTabType", "btn");

  if (import.meta.env.VITE_IS_OUTPOST) {
    router.push("/simeSearch/analysis/alert");
  } else {
    window.open("/simeSearch/analysis/alert", "_blank");
  }
  popoverShow.value = false;
  setTimeout(() => {
    popoverShow.value = true;
  }, 10);
  /* 存储当前配置信息 */
  sessionStorage.setItem("configForm", JSON.stringify(formData.value));
};

/* 直接查询 */
let emits = defineEmits(["click"]);
const handBtn = () => {
  if (props.isBtnEmits) {
    emits("click");
  } else {
    router.push("/simeSearch/analysis");
  }
};
</script>

<style scoped></style>
