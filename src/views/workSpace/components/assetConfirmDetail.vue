<template>
  <div class="title-bottom-line">
    <p>新增信息</p>
  </div>
  <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
    <div v-if="assetData.type == '1'">
      <el-form-item label="IP地址：">{{ assetData.lanIp }}</el-form-item>
    </div>
    <div v-if="assetData.type == '0'">
      <el-form-item label="URL：">{{ assetData.domain }}</el-form-item>
      <el-form-item label="互联网IP：">{{ assetData.internetp }}</el-form-item>
      <el-form-item label="局域网IP">{{ assetData.lanIp }}</el-form-item>
    </div>
  </el-form>
  <div class="text-right margin-top20 margin-right20 margin-bottom20">
    <el-button type="danger" @click="toIgnoreAsset">
      <el-icon><circle-close /></el-icon>忽略
    </el-button>
    <el-button @click="mergeAssets">
      <svg class="icon" aria-hidden="true" style="margin-right: 5px; font-size: 14px">
        <use xlink:href="#icon-hebingdanyuange"></use></svg
      >与现有资产合并
    </el-button>
    <el-button @click="addAsset">
      <el-icon><plus /></el-icon>新增资产
    </el-button>
  </div>
  <xel-dialog title="资产列表" ref="AssetConfirmDialog" width="80%" :ishiddenDialog="true" @close="closeDialog">
    <asset-confirm-merge v-if="assetConfirmShow" :type="type" :assetId="assetData.id" @close="closeMerge"></asset-confirm-merge>
  </xel-dialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import AssetConfirmMerge from "./assetConfirmMerge.vue";
import { useRouter, useRoute } from "vue-router";
import { ignoreDomainNotConfirmAsset, ignoreIpNotConfirmAsset } from "@/api/securityAssets/confirm";
import { ElMessage, ElMessageBox } from "element-plus";
let props = defineProps({
  assetData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let type = ref("");
let emits = defineEmits(["close"]);
console.info(props.assetData);
onMounted(() => {
  if (props.assetData.type == "0") {
    type.value = "url";
  } else if (props.assetData.type == "1") {
    type.value = "ip";
  }
});
// 打开选择资产界面
let assetConfirmShow = ref(false);
let AssetConfirmDialog = ref();
function mergeAssets() {
  assetConfirmShow.value = true;
  AssetConfirmDialog.value.open();
}
function toIgnoreAsset() {
  ElMessageBox.confirm("确认忽略此条资产吗？", { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" }).then(() => {
    console.info(props.assetData);
    let apiFn = props.assetData.type == 0 ? ignoreDomainNotConfirmAsset : ignoreIpNotConfirmAsset;
    apiFn({ id: props.assetData.id }).then((res) => {
      ElMessage.success("资产忽略成功");
      emits("close");
    });
  });
}
function closeDialog(data) {
  assetConfirmShow.value = false;
}
function closeMerge() {
  AssetConfirmDialog.value.close();
  emits("close");
}
// 添加资产跳转
const router = useRouter();
function addAsset() {
  router.push({ name: "ConfirmAdd", params: { type: props.assetData.type, confirmId: props.assetData.id } });
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
</style>
