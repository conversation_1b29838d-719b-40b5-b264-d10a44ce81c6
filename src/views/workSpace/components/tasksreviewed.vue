<template>
  <el-card>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="type === 'Os' ? getNotFinishedTaskListOs : getNotFinishedTaskList"
      @selection-change="handleSelectionChange"
    >
      <template #eventTitle="{ row }"> Case# {{ row.eventTitle }} </template>
      <template #count="{ row }">{{ minToDate(row.count) }} </template>
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "Tasksreviewed",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { getNotFinishedTaskList, selectUnclaimedTaskPage } from "@/api/workSpace/event";
import { getNotFinishedTaskListOs } from "@/api/offStandardEvent/workSpace/event";
import { useRoute, useRouter } from "vue-router";
import { minToDate } from "@/utils/minToDate";
const router = useRouter();
const route = useRoute();
let type = route.params.type;

let tableRef = ref();
// 列表配置项
const columns = [
  {
    prop: "eventTitle",
    label: type === "Os" ? "综合服务名称" : "事件名称",
    slotName: "eventTitle",
  },
  {
    prop: "taskGroup",
    label: "阶段",
    hide: type === "Os",
  },
  {
    prop: "title",
    label: "任务名称",
  },
  {
    prop: "count",
    label: "历时",
    slotName: "count",
    hide: type === "Os",
  },
  {
    prop: "assigneeName",
    label: "分析师",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "处理",
        onClick(scope) {
          modifyButton(scope.row);
        },
      },
    ],
  },
];

//搜索相关
let searchState = reactive({
  data: {
    title: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "任务名称",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    title: "",
  };
  search();
}

// 跳转事件
function modifyButton(val) {
  router.push({
    name: type === "Os" ? "OffEventDetail" : "EventDetail",
    params: {
      id: val.eventId,
    },
    query: {
      taskId: val.id,
      taskName: val.title,
      isManageTask: val.isManageTask,
    },
  });
}
</script>
<style lang="sss" scoped></style>
