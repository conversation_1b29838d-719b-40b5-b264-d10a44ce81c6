<template>
  <div class="table_count" v-loading="loading">
    <div class="pull-left" v-if="JSON.stringify(state.count) !== '{}'">
      <span class="pull-left">
        <span class="countAll">{{ state.count.count }}</span> 个命中
      </span>
      <el-popover placement="right" :width="350" trigger="hover">
        <template #reference>
          <el-icon :color="$themeColor" style="cursor: pointer; margin-top: 3px; margin-left: 5px" class="pull-left"><question-filled /></el-icon>
        </template>
        <div class="list">
          <p style="padding-left: 10px; font-weight: 900; font-size: 16px; margin-bottom: 10px">{{ state.count.aggrfieldAlias }}</p>
          <p
            v-for="(item, index) in state.count.list"
            :key="item.key"
            :class="'color' + (index - parseInt(index / 6) * 6 + 1)"
            class="list_b"
            style="width: 100%"
          >
            <span></span>
            <span>{{ item.count }} ({{ item.name }})</span>
          </p>
        </div>
      </el-popover>
      <div class="pull-left list" v-if="state.count.list !== undefined && state.count.list.length > 0" style="margin-left: 5px">
        <span v-for="(item, index) in state.count.list" :key="item.key" :class="'color' + (index - parseInt(index / 6) * 6 + 1)" class="list_b">
          <span></span>
          <span>{{ item.count }}</span>
        </span>
      </div>
    </div>
    <div class="clearfix"></div>
    <div>
      <div class="pull-right text-right">
        <span style="margin-right: 10px; cursor: pointer" v-show="innerCharts" @click="offInnerCharts">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-fanhui"></use>
          </svg>
        </span>
        <span class="pointer" @click="changeZhu = !changeZhu">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-yincang"></use>
          </svg>
        </span>
      </div>
      <div class="clearfix"></div>
      <div v-show="changeZhu" ref="zhuEchart" style="height: 200px" class="margin-top20"></div>
    </div>

    <!-- 日志导出 -->
    <div class="margin-top20">
      <span class="pointer" @click="exportLog">
        <svg class="icon" aria-hidden="true" style="color: #4292c9">
          <use xlink:href="#icon-daochu"></use>
        </svg>
        日志导出
      </span>

      <!-- 事件引入  前哨独有的功能 -->

      <span class="pointer" @click="eventLink" style="margin-left: 15px" v-if="activeTable.length && ifShowLinkBut">
        <el-icon :size="20" style="color: #4292c9; position: relative; top: 5px">
          <Link />
        </el-icon>
        引入
      </span>
    </div>
    <!-- 表格内容 -->
    <el-table
      v-loading="loading2"
      :data="activeTable"
      class="margin-top20"
      ref="scrollTable"
      stripe
      @sort-change="chageTable"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :width="activeTable.length > 0 ? (activeTable[0].idatacategory == 3 && ifShowLinkBut ? '120px' : '60px') : '60px'"
        label="操作"
      >
        <template #default="scope">
          <span class="clickable" @click="toMessageDetail(scope.row)">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-chakan"></use>
            </svg>
            查看</span
          >
          <!-- 追溯---告警   前哨，isoss 功能 -->
          <span v-if="scope.row.idatacategory == 3 && ifShowLinkBut" style="margin-left: 5px" class="clickable" @click="toAlarmDetails(scope.row)">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-chakan"></use>
            </svg>
            追溯
          </span>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="40" />
      <el-table-column v-for="(item, index) in show_type_list" :key="item.field + '===' + index" :sortable="item.type !== 'fieldDiy'">
        <template #header>
          <!-- type = fieldDiy  可扩展字段 不进行点击操作 -->
          <span v-if="item.type === 'fieldDiy'">{{ item.alias }}</span>
          <a v-else @click.stop="headerClick(item)" class="clickable">{{ item.alias }}</a>
        </template>
        <template #default="scope">
          <p class="content-p-box" @mouseenter="scope.row.showFilter = true" @mouseleave="scope.row.showFilter = false">
            <span style="white-space: pre-wrap" v-text="$globalShowOriginStr(scope.row[item.field + 'Text'] || scope.row[item.field])"></span>
            <span
              v-show="scope.row.showFilter && item.type !== 'fieldDiy'"
              @click="toSearch(scope, item.field, item.alias)"
              class="pointer clickable filter-icon-span"
              style="margin-left: 5px"
            >
              <span>
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-guolv"></use>
                </svg>
              </span>
            </span>
          </p>
        </template>
      </el-table-column>
    </el-table>
    <div class="moreList" ref="moreList"></div>
    <!-- 字段统计 -->
    <el-drawer v-model="fieldCount" :before-close="handleClose">
      <fileld-count v-if="fieldCount" ref="fileds_count" :field="state.field" @add_list="to_add_list"></fileld-count>
    </el-drawer>
    <!-- 日志详情信息 -->
    <el-drawer v-model="logDetail" :before-close="handleCloseDetail">
      <log-detail v-if="logDetail" ref="log_detail" :logDetail="state.logDetail" :search_filter="props.search_filter"></log-detail>
    </el-drawer>
    <!-- 导出弹窗 -->
    <xel-dialog
      title="选择字段（选择想要导出的字段，左边列表是所有字段，右边列表是导出字段）"
      ref="dialogRef"
      size="large"
      :ishiddenDialog="true"
      @close="closeDialog"
    >
      <siem-log-export v-if="exportD" :type_list="props.type_list" @export="exportLogXlsx" @close="closeDialog"></siem-log-export>
    </xel-dialog>

    <!-- 事件引用  -->
    <EventLinkDia ref="EventLinkDiaRef" />
  </div>
</template>

<script setup>
import { alertDetailByAlertNo, alertDetailByIsossAlertNo } from "@/api/workSpace/home.js";
import { search_sum, search_aggs_sum, search_list } from "@/api/sime/search/search";
import { ref, reactive, toRefs, onMounted, watch, computed } from "vue";
import FileldCount from "./fieldCount.vue";
import LogDetail from "./syslogDetail.vue";
import SiemLogExport from "./siemLogExport.vue";
import EventLinkDia from "./eventLinkDia.vue";
import { download } from "@/plugins/request";
// 文件前台下载
import { export_json_to_excel } from "@/utils/Export2Excel.js";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import { debounce } from "lodash";
import { useRouter } from "vue-router";

import { useStore } from "vuex";
const store = useStore();
const router = useRouter();

let props = defineProps({
  search_filter: {
    type: Object,
    default: () => {
      return {};
    },
  },
  show_type_list: {
    type: Array,
    default: () => {
      return [];
    },
  },
  // 所有的字段名
  type_list: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let scrollTable = ref();

let changeZhu = ref(true);
let state = reactive({
  count: {},
  // 表格数据
  table_list: [],
  // 分页
  pageValues: [],
  search_data: {},
  table_list_totle: 0,
  field: {},
  // log详情信息
  logDetail: {},
  // 表格选中数据
  multipleSelection: [],
});
// 返回第一层数据展示
let innerCharts = ref(false);
let loading = ref(false);
let loading2 = ref(false);
// 表格数据加载

/* 预加载所用 */
let pageNum = ref(0);
let activeTable = ref([]);

// 无限滚动
let intersectionObserver = new IntersectionObserver(function (entries) {
  if (entries[0].intersectionRatio <= 0) {
    return;
  }
  if (state.table_list.length < state.table_list_totle) {
    /*console.log(state.search_data);*/
    preloadData("roll");
    getTableList(state.search_data, "roll");
    /*console.log("Loaded new rows");*/
  } else {
    activeTable.value = state.table_list;
  }
});

// 开始观察
function offInnerCharts() {
  innerCharts.value = false;
  search_sun_all(props.search_filter);
}
//表头点击事件
let fieldCount = ref(false);
let FileldCountTitle = ref("");
let fileds_count = ref();
function headerClick(item) {
  /* 增加判断 扩展字段不进行点击操作 */
  if (item.type !== "fieldDiy") {
    let data = JSON.parse(JSON.stringify(state.search_data));
    FileldCountTitle.value = item.alias;
    state.field = data;
    state.field.aggrfield = item.field;
    delete state.field.pageValues;
    fieldCount.value = true;
  }
}
function handleClose() {
  fieldCount.value = false;
}
// 数据信息
let logDetail = ref(false);
function toMessageDetail(row) {
  state.logDetail = row;
  logDetail.value = true;
}
//告警详情
function toAlarmDetails(row) {
  // 前哨版本处理
  if (ifShowAlarmBut.value) {
    alertDetailByAlertNo({ alertNo: row.cuid }).then((res) => {
      router.push({
        name: "AlarmDetails",
        params: { id: res.msg },
        query: {
          type: "search",
        },
      });
    });
  } else {
    // 跳转ISOSS
    alertDetailByIsossAlertNo({ alertNo: row.cuid }).then((res) => {
      if (res.data.alertId && res.data.type) {
        //type 1：正式告警 2：无归属告警
        const params =
          res.data.type == "1"
            ? {
                name: "EventAlertDetail",
                params: {
                  id: res.data.alertId,
                  spare1: "2",
                },
              }
            : { name: "DubtAlertDetail", params: { id: res.data.alertId, type: res.data.type } };
        window.$wujie?.bus.$emit("openEventAlertDetailk", params);
      }
    });
  }
}
// 详情信息关闭
function handleCloseDetail() {
  logDetail.value = false;
}
// 排序
const chageTable = debounce((column) => {
  let data = column.column;
  /* 修改 - 因上述Key增加 === 故该处进行截取 */
  if (data && data.rawColumnKey) {
    state.search_data.sort = data.rawColumnKey.split("===")[0];
  } else {
    state.search_data.sort = "";
  }
  if (data && data.order === "ascending") {
    state.search_data.order = "asc";
  } else if (data && data.order === "descending") {
    state.search_data.order = "desc";
  } else {
    state.search_data.order = "";
  }
  state.pageValues = [];
  lastPageValues.value = [];
  lastPageValuesStr.value = [];
  getTableList(state.search_data);
}, 200);

/* 暴露的查询方法 - 外部调用 */
function search_sun_all(data, type) {
  /* 查询时，清空原排序状态 */
  scrollTable.value.clearSort();

  /* 点击过滤器 - 清空列表 */
  if (data === "noData") {
    clearTabList();
    return false;
  }
  if (data.filterId === "" && data.indexId === "") {
    state.count = {};
    state.table_list = [];
    state.search_data = JSON.parse(JSON.stringify(data));
    zhuChart && zhuChart.dispose();
    return false;
  }
  if (type) {
    innerCharts.value = false;
  }
  state.pageValues = [];
  lastPageValues.value = [];
  lastPageValuesStr.value = [];
  state.search_data = JSON.parse(JSON.stringify(data));
  state.search_data.sort = "";
  state.search_data.order = "";
  loading.value = true;
  search_sum(state.search_data).then((res) => {
    if (res.code === 200) {
      state.count = res.data;
    }
  });

  // 柱状图数据
  search_aggs_sum(state.search_data).then((res) => {
    drawEchart(res);
  });

  getTableList(state.search_data);
  loading.value = false;
  // 表格数据
}

/* 点击过滤器 - 清空列表 */
function clearTabList() {
  zhuChart && zhuChart.dispose();
  state.count = {};
  state.table_list = [];
  activeTable.value = [];
  state.table_list_totle = 0;
}
const lastPageValues = ref([]); // 存储上一次分页参数[0]
const lastPageValuesStr = ref([]); //  存储上一次分页参数[1]
// const
// 表格数据
function getTableList(data, type) {
  /* 新增 - 加载时，禁止点击重置按钮 */
  store.commit("setBtnDis", true);
  /* 判断是加载方式 - 不是滚动roll 与 首次加载*/
  if (type !== "roll" && pageNum.value === 0) {
    loading2.value = true;
  }
  data.pageValues = state.pageValues;
  /* 根据是否有分页参数判断 是否为首次或查询 重置分页为 0 */
  data.pageValues && !data.pageValues.length ? (pageNum.value = 0) : "";

  search_list(data)
    .then((res) => {
      if (res.data.list) {
        let list = JSON.parse(JSON.stringify(res.data.list));
        list.forEach((item) => {
          item.showFilter = false;
        });
        if (state.pageValues.length === 0) {
          state.table_list = JSON.parse(JSON.stringify(res.data.list));
        } else {
          // 检查data.pageValues是否与上一次一致
          if (lastPageValues.value.includes(state.pageValues[0]) && lastPageValuesStr.value.includes(state.pageValues[1])) {
            console.log("data.pageValues未变化，不用push");
            return;
          } else {
            JSON.parse(JSON.stringify(res.data.list)).forEach((item) => {
              state.table_list.push(item);
            });
            // 更新lastPageValues
            lastPageValues.value.push(JSON.parse(JSON.stringify(data.pageValues[0])));
            lastPageValuesStr.value.push(JSON.parse(JSON.stringify(data.pageValues[1])));
          }
        }
      } else {
        state.table_list = [];
        activeTable.value = [];
      }
      state.table_list_totle = res.data.totle;
      state.pageValues = JSON.parse(JSON.stringify(res.data.pageValues));

      /* 首次加载 - 模拟滚动 传参 roll */
      if (pageNum.value === 0 && state.pageValues && state.pageValues.length) {
        getTableList(state.search_data, "roll");
      }
      preloadData("new");
      loading2.value = false;
    })
    .finally(() => {
      /* 将查询数据data,存到state中, 用于事件引用 */
      store.commit("setEventSearch", JSON.stringify(data));
      setTimeout(() => {
        store.commit("setBtnDis", false);
      }, 800); //清除
      loading2.value = false;
    })
    .catch((msg) => {
      if (msg && msg == "重复请求已被取消") {
        return;
      }
    });
}
/* 预加载机制 - 负责处理界面显示数据 默认每页20条 */
function preloadData(type) {
  let limit = 20;
  if (type === "new") {
    pageNum.value++;
    limit = (pageNum.value - 1) * 20;
  } else {
    limit = pageNum.value * 20;
  }
  activeTable.value = state.table_list.slice(0, limit === 0 ? 20 : limit);
}

// 柱状图数据处理及绘画
let zhuEchart = ref();
let zhuChart = null;
function drawEchart(data) {
  let result = data.data;
  zhuChart = echarts.init(zhuEchart.value);
  zhuChart.clear();
  zhuChart.off("click");
  let gtime = result.gtime;
  let series = [];
  let timekey = [];
  let colors = ["#819d5e", "#1177c0", "#eaaf4b", "#da7216", "#b60000", "#7E5350"];
  let names = [];

  if (result.list && result.list[0].data) {
    result.list[0].data.forEach((item) => {
      names.push(item.name);
    });
    for (let i = 0; i < names.length; i++) {
      let arr = [];
      result.list.forEach((item) => {
        let obj = {};
        obj.name = item.name;
        item.data.forEach((itemIn) => {
          if (itemIn.name === names[i]) {
            obj.value = itemIn.count;
          }
        });
        obj.times = [item.key, item.key + gtime];
        arr.push(obj);
      });
      series.push({
        data: arr,
        name: names[i],
        type: "bar",
        stack: "total",
      });
    }
  } else {
    if (result.aggrfieldAlias) {
      names = [result.aggrfieldAlias];
    } else {
      names = ["统计"];
    }
    for (let i = 0; i < names.length; i++) {
      let arr = [];
      if (result.list) {
        result.list.forEach((item) => {
          let obj = {};
          obj.name = item.name;
          obj.value = item.count;
          obj.times = [item.key, item.key + gtime];
          arr.push(obj);
        });
      }
      series.push({
        data: arr,
        name: names[i],
        type: "bar",
        stack: "total",
      });
    }
  }

  // timekey
  if (result.list) {
    result.list.forEach((item) => {
      timekey.push(item.name);
    });
  }
  let option = {
    legend: { show: false },
    calculable: true,
    tooltip: { trigger: "axis" },
    yAxis: [{ type: "value" }],
    xAxis: [
      {
        type: "category",
        data: timekey,
      },
    ],
    grid: {
      /* 间距 */
      left: "1%",
      right: "1%",
      bottom: "1%",
      top: "4%",
      containLabel: true,
    },
    series: series,
    color: colors,
  };
  zhuChart.setOption(option, true);

  zhuChart.on("click", (a) => {
    let date1 = formatDate(a.data.times[0]);
    let date2 = formatDate(a.data.times[1]);
    state.search_data.timeWindow = "";
    state.search_data.startTime = date1;
    state.search_data.endTime = date2;
    state.pageValues = [];
    lastPageValues.value = [];
    lastPageValuesStr.value = [];
    search_sun_all(state.search_data);
    innerCharts.value = true;
  });
}
// echarts resize
onMounted(() => {
  window.addEventListener("resize", function () {
    zhuChart && zhuChart.resize();
  });
  intersectionObserver.observe(document.querySelector(".moreList"));
});
// 往查询界面传值
let emits = defineEmits(["add_list", "exportDia"]);
function toSearch(scope, field, alias) {
  let operator = "";
  let operatorText = "";
  if (scope.row[field] === "" && scope.row[field + "Text"] === undefined) {
    operator = "isnull";
    operatorText = "为空";
  } else {
    operator = "equal";
    operatorText = "等于";
  }
  let str = field.substr(field.length - 4);
  let value = "";
  if (str === "time") {
    value = scope.row[field];
  } else {
    value = "" + scope.row[field];
  }
  /*console.log("scope.row: ", scope.row);
  console.log("field: ", field);*/
  let data = {
    name: field,
    nameText: alias,
    operator: operator,
    operatorText: operatorText,
    value: value,

    valueText: scope.row[field + "Text"] ? scope.row[field + "Text"] : scope.row[field],

    kong: false,
  };
  to_add_list(data);
}
function to_add_list(data) {
  emits("add_list", data);
}
// 表格多选
function handleSelectionChange(val) {
  state.multipleSelection = val;
}
// 日志导出
let dialogRef = ref();
function exportLog() {
  dialogRef.value.open();
  exportD.value = true;
}

/* 事件引入 */
let EventLinkDiaRef = ref();
const ifShowLinkBut = ref(false);
const ifShowAlarmBut = computed(() => {
  return import.meta.env.VITE_IS_OUTPOST === "true" ? true : false;
});
if (import.meta.env.VITE_IS_OUTPOST === "true" || import.meta.env.VITE_IS_ISOSS === "true") {
  ifShowLinkBut.value = true;
} else {
  ifShowLinkBut.value = false;
}
const eventLink = () => {
  let ids = state.multipleSelection.map((item) => {
    return item.cuid;
  });
  if (import.meta.env.VITE_IS_ISOSS === "true") {
    let eventSearch = JSON.parse(store.state.siem.eventSearch);
    window.$wujie?.bus.$emit("openEventLink", ids, eventSearch);
  } else {
    /*console.log(store.state.siem);
  console.log(ids, 'table');*/
    EventLinkDiaRef.value.open(ids);
  }
};

let exportD = ref(false);
function closeDialog() {
  dialogRef.value.close();
  exportD.value = false;
}
function exportLogXlsx(result, check, num) {
  if (check === "all") {
    let searchData = JSON.parse(JSON.stringify(props.search_filter));
    searchData.fieldsArray = [];
    result.forEach((item) => {
      searchData.fieldsArray.push(item.field);
    });
    searchData.rowCount = num;
    emits("exportDia", true);
    download("/search/search/export", null, {}, "post", searchData).then((res) => {
      if (!res) {
        ElMessage.error("条效大于最大条数：50000,无法导出，请确认！");
      }
      emits("exportDia", false);
    });

    closeDialog();
  } else {
    if (state.multipleSelection.length > 0) {
      let tHeader = []; // 导出的excel的表头字段
      let filterVal = []; // 对象属性，对应于tHeader
      let filename = "data" + Date.now();
      let list = JSON.parse(JSON.stringify(state.multipleSelection));
      result.forEach((item) => {
        tHeader.push(item.alias);
        filterVal.push(item.field);
        list.forEach((item2) => {
          if (item2[item.field + "Text"]) {
            item2[item.field] = "" + item2[item.field + "Text"];
          } else {
            item2[item.field] = "" + item2[item.field];
          }
        });
      });
      let data = formatJson(filterVal, list);
      export_json_to_excel({
        header: tHeader,
        data,
        filename,
      });
      closeDialog();
    } else {
      ElMessage.warning("请选择至少一条日志");
    }
  }
}
function formatJson(filterVal, jsonData) {
  return jsonData.map((v) => filterVal.map((j) => v[j]));
}
// 时间戳转为时间
function formatDate(value) {
  let date = new Date(value);
  let y = date.getFullYear();
  let MM = date.getMonth() + 1;
  MM = MM < 10 ? "0" + MM : MM;
  let d = date.getDate();
  d = d < 10 ? "0" + d : d;
  let h = date.getHours();
  h = h < 10 ? "0" + h : h;
  let m = date.getMinutes();
  m = m < 10 ? "0" + m : m;
  let s = date.getSeconds();
  s = s < 10 ? "0" + s : s;
  return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
}
defineExpose({
  search_sun_all,
});
</script>

<style lang="scss" scoped>
.table_count {
  .countAll {
    font-size: 16px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }
}
:deep(.el-drawer__body) {
  padding: 10px;
  height: 100%;
}
:deep(.el-drawer__header) {
  margin-bottom: 0px;
}
:deep(.el-table .caret-wrapper) {
  height: 18px;
  position: absolute;
  transform: translateY(3px);
}
.list {
  .list_b {
    display: inline-block;
    padding: 0px 10px;
    font-size: 14px;
    font-weight: 600;
    color: #28334f;
    line-height: 20px;
    > span:nth-child(1) {
      height: 13px;
      width: 5px;
      float: left;
      margin-top: 3px;
    }
    > span:nth-child(2) {
      padding: 0px 10px;
      float: left;
    }
    $line-color-list: #819d5e #1177c0 #eaaf4b #da7216 #b60000 #7e5350;
    @each $color in $line-color-list {
      $i: index($line-color-list, $color);
      &.color#{$i} {
        > span:nth-child(1) {
          background: $color;
          border-radius: 5px;
        }
      }
    }
  }
}
.clickable {
  color: $color;
  cursor: pointer;
}
.filter-icon-span {
  width: 0 !important;
  position: relative;
  span {
    position: absolute;
  }
}
.content-p-box {
  margin: -5px -10px;
  padding: 5px 10px;
}
/*:deep(.el-dialog__body) {
  padding: 0px 20px;
}*/
</style>
