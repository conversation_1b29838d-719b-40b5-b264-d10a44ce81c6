<template>
  <div v-loading="loading">
    <xel-table ref="tableRef" :columns="columns" :data="tabData" :pagination="false" style="width: 100%">
      <template #auditStatus="{ row }">
        <audit-status :status="row.auditStatus" />
      </template>
      <template #btns="scope">
        <xel-handle-btns :btnList="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
      </template>
    </xel-table>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { initTaskList, saveEventTaskEsLog } from "@/api/event/task";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
const store = useStore();
let emits = defineEmits(["update:ids"]);
let props = defineProps({
  /* 事件id */
  eventId: {
    type: String,
    default: "",
  },
  /* 已选择的日志 ids */
  ids: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

let columns = ref([
  { prop: "taskGroupText", label: "阶段", width: "100px" },
  {
    prop: "title",
    label: "任务",
  },
  { prop: "createTime", label: "创建时间" },
  { prop: "assigneeName", label: "分析师" },
  { prop: "auditStatus", label: "审核", slotName: "auditStatus" },
  { prop: "auditName", label: "审核人" },
  {
    label: "操作",
    /*fixed: "right",*/
    slotName: "btns",
    width: "185px",
  },
]);

/* 基础数据 */
let loading = ref(true);
let tabData = ref([]);
let query = ref({
  eventTaskId: props.eventId,
  queryFilterId: "",
  startTime: "",
  endTime: "",
  timeWindow: "",
  filtersType: "",
  filters: "",
  ids: [],
});

function getBtnList(row) {
  return [
    {
      hide: Number(row.auditStatus) === 3,
      icon: "Link",
      title: "引用",
      onClick(scope) {
        loading.value = true;
        let eventSearch = JSON.parse(store.state.siem.eventSearch);
        /* 组装数据 */
        query.value.eventTaskId = scope.row.id;
        query.value.ids = props.ids;

        query.value.queryFilterId = eventSearch.queryFilterId;
        query.value.startTime = eventSearch.startTime;
        query.value.endTime = eventSearch.endTime;
        query.value.timeWindow = eventSearch.timeWindow;
        query.value.filtersType = eventSearch.filtersType;
        query.value.filters = eventSearch.filters;

        saveEventTaskEsLog(query.value)
          .then(() => {
            ElMessage.success("操作成功");
            emits("update:ids");
          })
          .finally(() => {
            loading.value = false;
          });
      },
    },
  ];
}

/* 获取事件任务日志列表 */
const getTaskList = () => {
  loading.value = true;
  tabData.value = [];
  initTaskList({ eventId: props.eventId, sourceId: "siem" }).then((res) => {
    res.data.taskGroupList.forEach((item) => {
      if (item.taskList && item.taskList.length > 0) {
        tabData.value = [...tabData.value, ...item.taskList];
      }
    });
    loading.value = false;
  });
};
getTaskList();
</script>

<style scoped></style>
