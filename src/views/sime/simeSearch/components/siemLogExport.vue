<template>
  <div v-loading="loading">
    <div>
      <el-button @click="selectionAll(true)" size="mini">全选</el-button>
      <el-button @click="selectionAll(false)">全不选</el-button>
    </div>
    <div class="chose_field margin-top10">
      <div style="margin-right: 2%">
        <el-table ref="tableSelect" :data="props.type_list" height="360" :show-header="false" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column label="" prop="alias"></el-table-column>
        </el-table>
      </div>
      <div style="height: 360px; overflow: auto">
        <vue-draggable-next class="dragArea list-group w-full" :list="state.multipleSelection" :sort="true" @change="log" :move="checkMove">
          <div v-for="item in state.multipleSelection" :key="item.field" class="selectNames">
            {{ item.alias }}
          </div>
        </vue-draggable-next>
      </div>
      <span class="clearfix"></span>
    </div>
    <div class="margin-top20">
      <div class="pull-left">
        <el-radio v-model="state.checkValue" label="check">导出选中日志</el-radio>
        <el-radio v-model="state.checkValue" label="all">导出全部日志</el-radio>
      </div>
      <div class="pull-left margin-left10"><el-input v-model="rowCount" style="width: 100px" type="number"></el-input></div>
      <div class="clearfix"></div>
    </div>
    <div class="text-center" style="padding: 30px">
      <el-button type="button" @click="closeExport">取消</el-button>
      <el-button type="primary" @click="exportLog">导出</el-button>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { ref, reactive, toRefs, onMounted, watch } from "vue";
// 拖拽排序
import { VueDraggableNext } from "vue-draggable-next";
let props = defineProps({
  type_list: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let rowCount = ref(10000);
// watch(rowCount, (newVal, oldVal) => {
//   if (newVal > 10000) {
//     rowCount.value = 10000;
//     ElMessage.warning("导出日志条数最大不能超过50000");
//   }
//   if (newVal < 0) {
//     rowCount.value = 0;
//     ElMessage.warning("请输入正整数");
//   }
// });
let state = reactive({
  multipleSelection: [],
  checkValue: "check",
});
let tableSelect = ref();
function handleSelectionChange(val) {
  state.multipleSelection = val;
}
onMounted(() => {
  props.type_list.forEach((item) => {
    if (item.isShow === 1) {
      state.multipleSelection.push(item);
      tableSelect.value.toggleRowSelection(item);
    }
  });
});
// 全选
// 空值按钮选择
let changeSelect = ref(false);
function selectionAll(check) {
  if (check === true) {
    state.multipleSelection = [];
    props.type_list.forEach((item) => {
      state.multipleSelection.push(item);
      tableSelect.value.toggleRowSelection(item, true);
    });
  } else {
    tableSelect.value.clearSelection();
  }
}
let emits = defineEmits(["export", "close"]);
let loading = ref(false);
function exportLog() {
  // loading.value = true;
  emits("export", state.multipleSelection, state.checkValue, rowCount.value);
}
function closeExport() {
  emits("close");
}
// 移动
function log(event) {}
function checkMove(evt) {
  // console.log("Future index: " + evt.draggedContext.futureIndex);
  // console.log("element: " + evt.draggedContext.element.alias);
}
</script>

<style lang="scss" scoped>
.chose_field {
  > div {
    width: 48%;
    float: left;
    border: 1px solid #e4e4e4;
    border-radius: 3px;
  }
  > div:nth-child(1) {
    border-width: 1px 1px 1px 1px;
  }
}
.selectNames {
  line-height: 35px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  padding: 4px 0;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
}
</style>
