<template>
  <xel-dialog title="事件列表" ref="dialogRef" width="1200px" :show-submit="false" buttonCancel="关闭">
    <EventList :isSimeSearch="true" @getEventId="getEventId" isSearchDia="true" />
  </xel-dialog>

  <xel-dialog title="事件任务" ref="eventTaskRef" width="1200px" :show-submit="false" buttonCancel="关闭">
    <EventTaskList :eventId="eventId" :ids="ids" @update:ids="updateIds" />
  </xel-dialog>
</template>

<script>
export default {
  name: "eventLinkDia",
};
</script>

<script setup>
import { ref } from "vue";

import EventList from "@/views/event/threatEvent/eventList.vue";
import EventTaskList from "./eventTaskList.vue";

let dialogRef = ref();
let eventTaskRef = ref();

let ids = ref([]);
const open = (data) => {
  console.log(data);
  ids.value = data;
  dialogRef.value.open();
};

/*
 * 获取事件id, 打开事件任务
 * */
let eventId = ref();
const getEventId = (id) => {
  console.log(id);
  eventId.value = id;
  eventTaskRef.value.open();
};

const updateIds = () => {
  eventTaskRef.value.close();
};
defineExpose({
  open,
});
</script>

<style scoped></style>
