<template>
  <div ref="wrapperRef" style="height: 100%">
    <div>
      <p class="pull-left fieldTitle">数据信息</p>
      <p class="pull-left change" @click="changeShowOrHide">
        <span>
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-yincang"></use>
          </svg>
        </span>
        <span v-show="fieldisnull">显示空字段</span>
        <span v-show="!fieldisnull">隐藏空字段</span>
      </p>
      <div class="clearfix"></div>
    </div>
    <div class="margin-top20" v-show="logDetail.idatacategory == '3' || logDetail.idatacategory == '1'">
      <el-tree
        :data="state.treeList"
        node-key="cuid"
        :props="defaultProps"
        :load="loadNode"
        lazy
        v-resize="DomResize"
        :highlight-current="true"
        :expand-on-click-node="false"
        @node-click="getNode"
      />
    </div>
    <div class="margin-top20" ref="table_content">
      <el-table :data="state.detail" stripe :show-header="false" border height="100%">
        <el-table-column label="字段名" prop="fieldName">
          <template #default="scope">
            <div class="column-label-icon">
              {{ scope.row.fieldName }}
              <el-icon size="16" @click="getColumnLabel(scope.row)"><ZoomIn /></el-icon></div
          ></template>
        </el-table-column>
        <el-table-column label="值" prop="value">
          <template #default="scope">
            <div v-if="scope.row.value" style="white-space: pre-wrap" v-text="$globalShowOriginStr(scope.row.value)"></div>
            <span v-else>{{ scope.row.value == 0 ? scope.row.value : 0 }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="overlay-box" v-if="dialogVisible">
      <div class="overlay-dialog">
        <vue-draggable-resizable
          :w="dialogW"
          :h="dialogH"
          :x="dialogL"
          :y="dialogT"
          :minW="240"
          :minH="140"
          :parent="true"
          :active="true"
          :grid="[10, 10]"
          :draggable="draggable"
          class-name="draggable-resizable"
        >
          <section class="overlay-section">
            <div class="header-box">
              <span class="dialog-title" id="titleID">{{ dialogTitle }}</span>
              <el-button type="text" @click="dialogVisible = false">
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
            <section class="dialog-content" @mousemove.stop id="contentID">
              <div>
                <div id="contentTextID" v-if="dialogContentText" style="white-space: pre-wrap" v-text="$globalShowOriginStr(dialogContentText)"></div>
                <span v-else>{{ dialogContentText == 0 ? dialogContentText : 0 }}</span>
              </div>
            </section>
          </section>
        </vue-draggable-resizable>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  //添加逻辑运算符 直接选中
  directives: {
    resize: {
      // 指令的名称
      mounted(el, binding) {
        // el为绑定的元素，binding为绑定给指令的对象
        let width = "",
          height = "";
        function isReize() {
          const style = document.defaultView.getComputedStyle(el);
          if (width !== style.width || height !== style.height) {
            binding.value({ width: style.width, height: style.height });
          }
          width = style.width;
          height = style.height;
        }
        el.__vueSetInterval__ = setInterval(isReize, 300);
      },
      updated() {},
      unmounted(el) {
        clearInterval(el.__vueSetInterval__);
      },
    },
  },
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import { filterIndexIdNames } from "@/api/sime/search/filter";
import { search_trace } from "@/api/sime/search/search";
import VueDraggableResizable from "vue3-draggable-resizable";
let props = defineProps({
  logDetail: {
    type: Object,
    default() {
      return {};
    },
  },
  search_filter: {
    type: Object,
    default() {
      return {};
    },
  },
});
//获取屏幕大小
const $globalWindowSize = window.$globalWindowSize;

let defaultProps = {
  label: "ceventname",
  children: "children",
  isLeaf: "leaf",
};
let state = reactive({
  detail: [],
  treeList: [],
  oldData: {},
});
let fieldisnull = ref(true);

function changeShowOrHide() {
  fieldisnull.value = !fieldisnull.value;
  getLogDetail(state.oldData);
}
async function getLogDetail(data) {
  state.oldData = data;
  let res = await filterIndexIdNames(props.search_filter.indexId);
  let arr = res.data;
  state.detail = [];
  arr.forEach((item) => {
    for (let key in data) {
      let str = key.split("time");
      if (item.field === key) {
        if (data[key] !== "" && fieldisnull.value === true) {
          state.detail.push({
            fieldName: item.alias,
            value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
          });
        } else if (fieldisnull.value === false) {
          state.detail.push({
            fieldName: item.alias,
            value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
          });
        }
      }
    }
  });
  // for (let key in data) {
  //   let str = key.split("time");
  //   arr.forEach((item) => {
  //     if (item.field === key) {
  //       if (data[key] !== "" && fieldisnull.value === true) {
  //         state.detail.push({
  //           fieldName: item.alias,
  //           value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
  //         });
  //       } else if (fieldisnull.value === false) {
  //         state.detail.push({
  //           fieldName: item.alias,
  //           value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
  //         });
  //       }
  //     }
  //   });
  // }
  // state.treeList = [];
}
async function loadNode(node, resolve) {
  if (node.level >= 1) {
    if (node.data.idatacategory === 3 || node.data.idatacategory === 1) {
      let res2 = await search_trace({ indexId: props.search_filter.indexId, coriginlogids: node.data.coriginlogids });
      let data = res2.data;
      data.forEach((item) => {
        if (item.idatacategory == "0") {
          item.leaf = true;
        }
      });
      return resolve(data);
    } else {
      return resolve([]);
    }
  }
}
// 点击获取node内容
function getNode(node, key) {
  if (key.level == "1") {
    getLogDetail(props.logDetail);
  } else {
    getLogDetail(node);
  }
}
let table_content = ref();
function DomResize(data) {
  let ss = data.height.split("px");
  let height = 0;
  if (parseFloat(ss[0]) > 0) {
    height = parseFloat(ss[0]) + 100;
  } else {
    height = 100;
  }
  table_content.value.style.height = "calc(100% - " + height + "px)";
}
const dialogVisible = ref(false);
const dialogContentText = ref("");
const dialogTitle = ref("");
function getColumnLabel(row) {
  dialogContentText.value = row.value;
  dialogTitle.value = row.fieldName;
  nextTick(() => {
    openContentDialog(dialogContentText.value);
  });
}
onMounted(() => {
  state.treeList.push(props.logDetail);
  getLogDetail(props.logDetail);
});
const dialogT = ref(100);
const dialogL = ref(100);

const dialogW = ref(550);
const dialogH = ref(140);
const wrapperRef = ref(null);
function openContentDialog(str) {
  const emptyW = window.innerWidth - wrapperRef.value.offsetWidth;
  dialogL.value = emptyW * 0.2;

  dialogW.value = emptyW * 0.6;

  dialogH.value = 140;

  if (str) {
    if (str.length > 700) {
      dialogH.value = window.innerHeight * 0.8;
    } else if (str.length > 300) {
      dialogH.value = 500;
    } else if (str.length > 100) {
      dialogH.value = 300;
    }
  }
  dialogT.value = (window.innerHeight - dialogH.value) / 3;

  dialogVisible.value = true;
}

// 鼠标移动事件
const draggable = ref(true); //是否可以拖动弹框
function handleMouseUp(event) {
  if (event.target.id == "contentTextID" || event.target.id == "contentID" || event.target.id == "titleID") {
    draggable.value = false;
  } else {
    draggable.value = true;
  }
}
</script>

<style lang="scss" scoped>
.fieldTitle {
  font-weight: 600;
  font-size: 18px;
}
.change {
  color: #127dca;
  border: 1px solid #127dca;
  padding: 3px 5px;
  border-radius: 3px;
  margin-left: 10px;
}
.column-label-icon {
  cursor: pointer;
  display: flex;
  .el-icon {
    top: 3px;
    display: none;
    margin-left: 4px;
  }
  &:hover {
    .el-icon {
      display: block;
    }
  }
}

.overlay-box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2000;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: auto;
}
.overlay-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
}
.overlay-section {
  height: 100%;
  width: 100%;
  transform: none;
  position: relative;
  margin: 0 auto;
  border-radius: 10px;
  background: #fff;
  padding: 20px;
  &:after,
  &:before {
    position: absolute;
    content: "";
    display: block;
    right: 2px;
    bottom: 5px;
    height: 1px;
    transform: rotate(-45deg);
    width: 12px;
    background: #aaa;
  }
  &:before {
    width: 18px;
    bottom: 8px;
    right: 2px;
  }
}

.dialog-content {
  max-height: calc(100% - 50px);
  padding-right: 10px;
  overflow: overlay;
  overflow-y: auto;
  padding: 0 20px 20px;
  word-wrap: break-word;
  & > div {
    word-wrap: break-word;
    line-height: 26px;
  }
}

.header-box {
  padding: 0 0 20px 10px;
  display: flex;
  justify-content: space-between;
  .el-button {
    padding: 0 0 10px 0;
    text-align: center;
    color: #000;
    &:hover {
      color: $color;
    }
  }
}

.dialog-title {
  line-height: 24px;
  color: #303133;
  font-size: 18px;
  font-weight: 400;
}
</style>
<style lang="scss">
.vdr-handle {
  opacity: 0;
  &:not(.vdr-handle-br) {
    display: none !important;
  }
}
.vdr-container.active {
  border-color: transparent;
}
.vdr-handle-br {
  width: 15px;
  height: 15px;
  bottom: -5px;
  right: -5px;
  z-index: 1;
}
</style>
