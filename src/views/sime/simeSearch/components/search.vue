<template>
  <h3 class="conH3Tit siem-search-conH3Tit">
    <span class="pull-left">{{ "" || ($route.meta && $route.meta.title) }}</span>
    <div class="pull-right">
      <el-button size="mini" @click="reset()" :loading="$store.state.siem.btnDis">重置</el-button>
      <el-button type="primary" size="mini" @click="saveFastFilter">保存查询</el-button>

      <!-- 新增 - 二次筛选 - 修改 -->
      <el-dropdown>
        <el-button size="mini" @click="changeErciFilter">
          <el-icon><search /></el-icon> 二次筛选
          <el-icon><ArrowDown /></el-icon>
        </el-button>

        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-for="item in dropOptin" :key="item.value" :disabled="dropActive === item.value" @click="handleCommand(item.value)">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-popover :placement="$wujie ? 'left-start' : 'left'" :width="300" trigger="click" :disabled="state.search_template_list.length == 0">
        <template #reference>
          <el-button size="mini" @click="getTemplateListLength">
            <el-icon><search /></el-icon> 快速查询
          </el-button>
        </template>
        <el-scrollbar height="300px">
          <ul class="search_template_list">
            <li
              v-for="item in state.search_template_list"
              :key="item.id"
              @click.stop="choseTemplate(item)"
              @mouseenter="item.showDelete = true"
              @mouseleave="item.showDelete = false"
            >
              {{ item.name }}
              <el-icon
                v-show="item.showDelete"
                :size="10"
                class="del-template-icon"
                color="#ba271d"
                style="margin-right: 20px; margin-top: 13px; background: #efdfe1; color: #ba271d; border-radius: 3px"
                @click.stop="deleteFilter(item)"
                ><close
              /></el-icon>
            </li>
          </ul>
          <p v-if="state.search_template_list.length == 0" class="text-center">暂无数据</p>
        </el-scrollbar>
      </el-popover>

      <!-- 新增 - 定时查询 -->
      <el-popover :placement="$wujie ? 'left-start' : 'bottom'" :width="400" trigger="click" :hide-after="0">
        <template #reference>
          <el-button size="mini">
            <el-icon><search /></el-icon> 定时查询
            <el-icon><ArrowDown /></el-icon>
          </el-button>
        </template>
        <div>
          <el-form ref="search_forms" label-width="90px">
            <el-form-item label="定时查询">
              <el-switch
                v-model="state.search_form_data.timeSwitch"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949; width: 60px; float: left; margin-top: 8px; margin-right: 10px"
                active-value="1"
                inactive-value="0"
              />
              <el-input v-model="state.search_form_data.time" type="number" class="pull-left" style="width: calc(50% - 40px); margin-right: 10px" />
              <el-select v-model="state.search_form_data.timeType" class="pull-left" style="width: calc(50% - 40px)">
                <el-option value="0" label="秒" />
                <el-option value="1" label="分" />
                <el-option value="2" label="时" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-popover>
      <el-button size="mini" @click="copyOpenWindow">
        <el-icon><CopyDocument /></el-icon>
        复制标签</el-button
      >
    </div>
    <div class="clearfix"></div>
  </h3>
  <div class="searchBody">
    <el-form ref="search_form" label-width="120px">
      <el-row>
        <el-col class="siemSearchClass1">
          <el-form-item label="时间选择">
            <el-date-picker
              v-model="state.search_form_data.time_list"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
              @change="change_time($event)"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col class="siemSearchClass2">
          <el-form-item label="常用" style="margin-bottom: 0px">
            <div class="CY_time_list">
              <span
                @click="chose_cy_time(item.value)"
                :class="state.search_data.timeWindow === item.value ? 'chose' : ''"
                v-for="item in state.search_form_data.CY_time_list"
                :key="item.value"
                >{{ item.label }}</span
              >
            </div>
          </el-form-item>
        </el-col>
        <el-col style="border-bottom: 2px dashed #ededed">
          <el-form-item label="过滤器">
            <div class="filter_list">
              <span
                v-for="item in state.search_form_data.filter_list"
                :class="state.search_data.filterId == item.filterId ? 'chose' : ''"
                :key="item.filterId"
                @click="chose_filter(item)"
                >{{ item.name }}</span
              >
            </div>
            <div class="pull-right more_filter">
              <span class="pull-right" v-if="!filter_text_show" @click="getMoreFilter()">
                更多
                <el-icon><arrow-down /> </el-icon>
              </span>
              <span v-else class="pull-right" @click="getMoreFilter()">
                收起
                <el-icon><arrow-up /></el-icon>
              </span>
              <div ref="filter_select" v-show="filter_text_show">
                <el-select v-model="state.filterId" filterable placeholder="请先选择过滤器">
                  <el-option
                    v-for="item in state.search_form_data.all_filter_list"
                    :key="item.filterId"
                    :value="item.filterId"
                    :label="item.name"
                    @click="changeFilter(item)"
                  ></el-option>
                </el-select>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="筛选条件">
            <condition-result
              v-if="
                (conditionList.length && dropActive === 1) ||
                (showCondition && judgmentContentRealTime[0] && judgmentContentRealTime[0].children.length && dropActive === 2)
              "
              :list="dropActive === 1 ? conditionList : arrFitFun(judgmentContentRealTime[0].children, conditionList)"
              style="margin-top: 2px; padding: 2px"
            />
            <condition-result
              v-else-if="ConditionList && ConditionList.length && simeSearchVersion == 1"
              :list="ConditionList"
              style="margin-top: 2px; padding: 2px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-show="erciFilter">
          <!-- {{ filterType.add_list }} -->
          <el-form-item label="二次筛选">
            <!--  原简单查询 -->
            <div class="two_filter_list" v-if="dropActive === 1">
              <div class="two_filter" v-for="(item, index) in filterType.add_list" :key="item.id">
                <span class="colse_filter">
                  <span @click="resetErci(index)">
                    <el-icon color="#D4D4D4"><refresh-right /></el-icon>
                  </span>
                  <span @click="remove_add_filter(index)">
                    <el-icon color="#D4D4D4"><close-bold /></el-icon>
                  </span>
                </span>
                <edit-condition
                  v-if="showEditCondition"
                  :type-list="type_list"
                  :data="item"
                  :key="index + delKey"
                  @finish="
                    (a, b) => {
                      changeValue(index, a, b);
                    }
                  "
                ></edit-condition>
              </div>
              <span class="add_filter" @click="add_filter">
                <el-icon>
                  <Plus />
                </el-icon>
              </span>
            </div>

            <!-- 新增 - 新查询  -->
            <div class="newConDiv" v-else>
              <div class="newConComDiv" v-show="showCon">
                <template v-if="simeSearchVersion == 1">
                  <condition
                    v-if="showCondition"
                    ref="conditionRef"
                    :showResult="false"
                    :data="conditionsObject"
                    :type-list="type_list"
                    :labelList="labelList"
                    @getConditionList="getConditionList"
                    @delItemFun="delItemFun"
                    :isShowClose="true"
                    @closeFun="changeErciFilter"
                  />
                </template>

                <template v-else-if="simeSearchVersion == 2">
                  <conditionNew
                    v-if="showCondition"
                    :list="judgmentContent"
                    :content-chose-id="choseId"
                    :chose-if-id="choseIfId"
                    :type-list="type_list"
                    @change-list="changeConditionData"
                    :haveFilter="false"
                    @deleteItemFun="delItemFun"
                    :isShowClose="true"
                    @closeFun="changeErciFilter"
                  />
                </template>
              </div>
              <span class="lookBtn" @click="showCon = !showCon">
                <svg class="icon" aria-hidden="true" v-if="!showCon">
                  <use xlink:href="#icon-chakan"></use>
                </svg>
                <svg class="icon" aria-hidden="true" v-else>
                  <use xlink:href="#icon-yincang"></use>
                </svg>
              </span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="">
            <el-button type="primary" size="mini" @click="searchTable" :loading="$store.state.siem.btnDis"> 查询 </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>

  <!-- 保存查询 - 弹窗 -->
  <xelDialog title="快速查询模板" ref="dialogRef" width="500px" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleForm" label-width="auto" @submit.prevent>
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item" />
    </el-form>
  </xelDialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, watch, computed, getCurrentInstance, nextTick, watchEffect, provide } from "vue";
import { getDictsData } from "@/utils/getDicts";
import { ElMessageBox, ElMessage } from "element-plus";
import editCondition from "../../components/editCondition.vue";
import { batchDelete } from "@/utils/delete";
import conditionResult from "../../components/conditionResult.vue"; //条件语句

/* 新查询 */
import conditionNew from "@/views/sime/components/newCondition/ifContent.vue";
import useCondition from "@/views/sime/components/newCondition/conditionEdit";
import useSiemRuleEditorVersion from "@/utils/siemRuleEditorVersion";
import conditionTypeList from "@/views/sime/utils/conditionTypeList";
import condition from "@/views/sime/components/condition.vue";
import dicsStore from "@/store/modules/dictsData";
import { v4 as uuidv4 } from "uuid";

if (!dicsStore.state.numberFlag || !dicsStore.state.stringFlag || !dicsStore.state.ruleFlag) {
  dicsStore.actions.getFilterOperatorNumRes();
  dicsStore.actions.getFilterOperatorStrRes();
  dicsStore.actions.getRuleOperatorRes();
}
const { simeSearchVersion } = useSiemRuleEditorVersion();

import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const router = useRouter();

/* 条件 list */
let labelList = ref([
  {
    notetype: "event",
    icon: "icon-IF",
    label: "事件",
    color: import.meta.env.VITE_COLOR,
  },
  {
    notetype: "or",
    icon: "icon-or",
    label: "OR",
    color: "#409EFF",
    bgColor: "#ECF5FF",
  },
  {
    notetype: "and",
    icon: "icon-and",
    label: "AND",
    color: "#67C23A",
    bgColor: "#F0F9EB",
  },
  {
    notetype: "not",
    icon: "icon-not",
    label: "NOT",
    color: "#F56C6B",
    bgColor: "#FEF0F0",
  },
  {
    notetype: "condition",
    icon: "icon-condition",
    label: "条件",
    color: "#E6A23C",
    bgColor: "#f0f1f4",
    editStatus: true,
    kong: false,
    data: {
      nameText: "", //filledText
      operatorText: "", //操作汉字
      valueText: "",
    },
  },
]);

const ConditionList = ref([]);
const getConditionList = (data) => {
  let targetData = data[0].children.length ? data[0].children : data;
  ConditionList.value = [...conditionList.value, ...targetData];
};

/* 删除 */
const delItemFun = (data) => {
  if (data && !data.data) return;
  let dataIndex = filterType.add_list.findIndex((item) => {
    return (
      item.name === data.data.name &&
      item.nameText === data.data.nameText &&
      item.operator === data.data.operator &&
      item.operatorText === data.data.operatorText &&
      item.value === data.data.value &&
      item.valueText === data.data.valueText
    );
  });
  if (dataIndex !== -1) {
    remove_add_filter(dataIndex);
  }

  let dataIndexs =
    conditionsObject.value &&
    conditionsObject.value.children &&
    conditionsObject.value.children.findIndex((item) => {
      return (
        item.name === data.data.name &&
        item.nameText === data.data.nameText &&
        item.operator === data.data.operator &&
        item.operatorText === data.data.operatorText &&
        item.value === data.data.value &&
        item.valueText === data.data.valueText
      );
    });
  if (dataIndexs !== -1) {
    conditionsObject.value && conditionsObject.value.children && conditionsObject.value.children.splice(dataIndexs, 1);
  }
};

import {
  defaultList,
  filterListAll,
  filterIndexIdNames,
  saveFilterTemplate,
  searchTemplateList,
  updateSearchTemplate,
  deleteSearchTemplate,
} from "@/api/sime/search/filter";
import store from "@/store/index";
let props = defineProps({
  type_list: {
    type: Array,
    default() {
      return [];
    },
  },
  field_add_list: {
    type: Object,
    default() {
      return {};
    },
  },
});

/* 新查询 - 相关 */
let showCon = ref(true);
let conditionsObject = ref();
let newForm = ref({
  formData: {
    indexId: "",
  },
});

let { filterListSameIndexId, typeList, showCondition, confirmIndexId, emptyLastIndexId } = conditionTypeList(
  newForm.value,
  10000,
  true,
  false,
  () => {
    conditionsObject.value = {};
  }
);

//条件编辑
const { choseId, getChoseId, choseIfId, getIfId, judgmentContent, changeConditionData, judgmentContentRealTime, getConditionsData, echoConditions } =
  useCondition(showCondition);

/* 新增 - 二次筛选 - 相关*/
let dropActive = ref(1);
let dropOptin = ref([
  { value: 1, name: "简单筛选" },
  { value: 2, name: "高级筛选" },
]);
/* 切换事件 */
const handleCommand = (data) => {
  dropActive.value = data;
  if (!erciFilter.value) {
    changeErciFilter();
  } else {
    erciFilter.value = true;
  }
  /* 切换后，清除原选项 */
  resetTwoQuery();
};

/* 数据适配方法 */
const arrFitFun = (nArr, oArr) => {
  if (oArr.length === 0) return [...oArr];
  if (nArr[0].name || nArr[0].children) {
    return [...oArr, ...nArr];
  } else {
    return [...oArr];
  }
};

watch(
  () => props.field_add_list,
  (newVal, oldVal) => {
    let data = JSON.parse(JSON.stringify(newVal));
    let selectType = props.type_list.find((item) => item.field == newVal.name);
    if (selectType.dictionaryType) {
      data.value = data.value.toString();
    }
    if (JSON.stringify(data) !== "{}") {
      let i = 0;
      filterType.add_list.forEach((item) => {
        if (
          data.name === item.name &&
          data.operator === item.operator &&
          data.operatorText === item.operatorText &&
          data.nameText === item.nameText &&
          data.value === item.value
        ) {
          i = i + 1;
        }
      });

      if (i <= 0) {
        showEditCondition.value = false;
        if (filterType.add_list.length > 0) {
          let oldArr = filterType.add_list[filterType.add_list.length - 1];
          if (oldArr.name === "" && oldArr.operator === "" && oldArr.value === "") {
            filterType.add_list.splice(filterType.add_list.length - 1, 1);
          }
        }

        filterType.add_list.push(data);
        /*
         * 新增
         * filterType.add_list 保持原规则 与 原校验方式
         * dropActive = 2 新编辑器
         * 数据处理 为 新编辑器格式
         * */
        if (dropActive.value === 2) {
          let newData = JSON.parse(JSON.stringify(data));
          newData.content = "";
          newData.notetype = "condition";
          newData.valueType = "input";

          /* 老编辑器 */
          if (simeSearchVersion.value == 1) {
            let getLists = conditionRef.value.getList();
            getLists[0].children.push(newData);
            conditionsObject.value = getLists[0];
          } else {
            let newObject = getConditionsData();
            let newD = null;
            if (!newObject) {
              newD = {
                name: "事件",
                notetype: "and",
                children: [newData],
              };
            } else {
              newObject.children.push(newData);
              newD = newObject;
            }
            showCondition.value = false;
            setTimeout(() => {
              judgmentContent.value = echoConditions(newD);
              showCondition.value = true;
            }, 100);
          }
        }

        setTimeout(() => {
          showEditCondition.value = true;
          erciFilter.value = true;
          showCon.value = true;
          ElMessage.success("已添加二次筛选条件");
        }, 500);
      } else {
        ElMessage.warning("请勿重复添加二次筛选条件");
      }
    }
  }
);

// 判断模板列表长度
function getTemplateListLength() {
  if (state.search_template_list.length == 0) {
    ElMessage.warning("无快速查询信息");
  }
}

let conditionList = computed(() => {
  let list = [];
  let ss = JSON.parse(JSON.stringify(state.filter_text1_body));

  if (ss && ss.children && ss.children.length) {
    list.push({ name: "filter", notetype: "condition", filterOptions: ss.children });
  }

  /* 简单筛选 */
  if (dropActive.value === 1) {
    for (let item of filterType.add_list.filter((l) => l.name)) {
      list.push({
        ...item,
        notetype: "condition",
      });
    }
  }
  return list;
});

// 传值
let emits = defineEmits(["search", "filterTypeList"]);
let state = reactive({
  search_form_data: {
    time_list: [],
    CY_time_list: [],
    filter_list: [],
    all_filter_list: [],
    timeSwitch: "0",
    time: "",
    timeType: "0",
  },
  search_template_list: [],
  search_data: {
    // 快速查询的id
    id: "",
    name: "",
    startTime: "",
    endTime: "",
    timeWindow: "",
    filterId: "",
    indexId: "",
    filtersArray: [],
  },
  filterId: "",
  filter_text1_body: {},
});
let filterType = reactive({
  type_list: props.type_list,

  add_list: [
    {
      name: "",
      operator: "",
      value: "",
      nameText: "",
      operatorText: "",
      valueText: "",
      operatorList: [],
      valueType: "input",
      kong: false,
      refName: "",
      refNameText: "",
      refOperator: "",
      refOperatorText: "",
    },
  ],
});

// 查询
function searchTable() {
  let arr = [];
  let noGo = false;
  filterType.add_list.forEach((item) => {
    if (item.name !== "" && item.operator !== "") {
      if (item.operator === "isnull" || item.operator === "notnull") {
        arr.push(item);
      } else if (item.value !== "") {
        if (item.operator == "refRule") {
          // 添加引用规则 并多选
          if (!item.refName || !item.refOperator) {
            ElMessage.warning("请输入搜索条件！");
            noGo = true;
          } else {
            arr.push({ ...item, value: item.value.join(",") });
          }
        } else {
          arr.push(item);
        }
      } else {
        ElMessage.warning("请输入搜索条件！");
        noGo = true;
      }
    }
  });
  if (noGo) {
    return false;
  }
  state.search_data.filtersArray = arr;
  if (state.search_data.indexId !== "" && state.search_data.filterId !== "") {
    let data = chuli(state.search_data);
    data.timeSwitch = state.search_form_data.timeSwitch;
    data.time = state.search_form_data.time;
    data.timeType = state.search_form_data.timeType;
    if (data.timeSwitch == "1") {
      if (data.time == "" || data.timeType == "") {
        ElMessage.warning("请输入定时查询周期");
        return false;
      }
    }
    if (data.timeWindow !== "" || (data.startTime !== "" && data.endTime !== "")) {
      /* 修改 - 接口数据格式变更，增加字段 */
      let newData = addNewWord(data);
      if (!newData) return;
      emits("search", newData);
    } else {
      ElMessage.warning("请选择时间范围");
    }
  } else {
    ElMessage.warning("请选择筛选条件");
  }
}

/* 新增 - 字段 */
let conditionRef = ref();
const addNewWord = (data) => {
  let newData = JSON.parse(JSON.stringify(data));
  newData.filtersType = dropActive.value;

  if (dropActive.value === 1) {
    newData.filters = JSON.stringify(data.filtersArray);
  } else {
    if (erciFilter.value) {
      if (simeSearchVersion.value == 1) {
        let conditions = conditionRef.value ? conditionRef.value.getList() : [];
        if (!conditions) return;
        if (conditions && conditions.length && conditions[0].children.length) {
          newData.filters = JSON.stringify(conditions[0]);
        } else {
          newData.filters = "";
        }
      } else {
        let newObject = getConditionsData();
        if (!newObject) {
          ElMessage.warning("请完善条件!");
          return false;
        }
        !newObject ? (newData.filters = "") : (newData.filters = JSON.stringify(newObject));
      }
    }
  }
  delete newData.filtersArray;
  return newData;
};

/* 清除二次筛选 */
const resetTwoQuery = () => {
  showCon.value = true;
  filterType.add_list = [
    {
      name: "",
      operator: "",
      value: "",
      nameText: "",
      operatorText: "",
      valueText: "",
      valueType: "input",
      kong: false,
    },
  ];
  if (state.search_data.indexId) {
    newForm.value.formData.indexId = state.search_data.indexId;
    confirmIndexId(state.search_data.indexId);
  }
  judgmentContent.value = echoConditions({});
  conditionsObject.value = {};
};

// 重置
function reset() {
  state.search_data = {
    id: "",
    name: "",
    startTime: "",
    endTime: "",
    timeWindow: "",
    filterId: "",
    indexId: "",
    filtersArray: [],
  };
  state.search_form_data.time_list = [];
  state.search_form_data.timeSwitch = "0";
  state.search_form_data.time = null;
  state.search_form_data.timeType = "0";
  state.filterId = "";
  state.filter_text1_body = {};
  filterType.type_list = [];
  resetTwoQuery();
  erciFilter.value = false;
  dropActive.value = 1;

  emits("search", state.search_data);
}

// 重置二次查询条件
function resetErci(index) {
  showEditCondition.value = false;

  setTimeout(() => {
    filterType.add_list[index] = {
      name: "",
      operator: "",
      value: "",
      nameText: "",
      operatorText: "",
      valueText: "",
      valueType: "input",
      kong: false,
    };

    showEditCondition.value = true;
  }, 100);
}
// 删除快速查询
function deleteFilter(item) {
  batchDelete().then(() => {
    deleteSearchTemplate(item.id).then(() => {
      ElMessage.success("操作成功");
      getSearchTemplateList();
    });
  });
}
// 字典获取常用项
async function get_CY_time_list() {
  let res = await getDictsData("search_timewindow");
  state.search_form_data.CY_time_list = res;
}
get_CY_time_list();
// 选择常用时间
function chose_cy_time(val) {
  state.search_data.timeWindow = val;
  state.search_form_data.time_list = [];
  state.search_data.startTime = "";
  state.search_data.endTime = "";
}
// 时间范围选择监听
function change_time(val) {
  if (val) {
    state.search_data.startTime = val[0];
    state.search_data.endTime = val[1];
  } else {
    state.search_data.startTime = "";
    state.search_data.endTime = "";
  }
  state.search_data.timeWindow = "";
}
// 默认展示的过滤器
async function getDefaultList() {
  let res = await defaultList();
  state.search_form_data.filter_list = res.data.rows;
}
// 筛选条件
let filter_text1 = ref("");
let filter_text2 = ref("");
watch(
  () => state.filter_text1_body,
  (newVal, prevCount) => {
    if (JSON.stringify(newVal) !== "{}") {
      filter_text1.value = pinjieSX(newVal);
    } else {
      filter_text1.value = "";
    }
  }
);
watch(
  () => filterType.add_list,
  (addList, prevCount) => {
    let str = "";
    if (addList.length > 0) {
      addList.forEach((item, index) => {
        if (item.name !== "" && item.operator !== "") {
          str = str + "AND (" + item.nameText + " " + item.operatorText + " " + item.valueText + ")";
        }
      });
      filter_text2.value = str;
    }
  },
  { deep: true }
);
// 拼接筛选条件
function pinjieSX(node) {
  if (node.children) {
    let chd = node.children;
    let type = node.notetype;
    let c = "";
    for (let i = 0; i < chd.length; i++) {
      let sc = pinjieSX(chd[i]);
      if (sc !== null) {
        if (c.length > 0) {
          let s = "or" === type ? "OR" : "AND";
          c = c + " " + s + " ";
        }
        c = c + sc;
      }
    }
    if (c.toString().trim().length === 0) {
      return null;
    } else {
      let str = "not" === type ? "NOT" : "";
      str = str + "(" + c.toString() + ")";
      return str;
      // return ("not".equals(type) ? "not " : "") + "(" + c.toString() + ")";
    }
  } else {
    return node.nameText + " " + node.operatorText + "  " + node.valueText;
  }
}

// 操作符替换
// function caozuof(name, operator, value) {
//   let c = "";
// }
getDefaultList();
// 过滤器选择
function chose_filter(item) {
  /* 新增 - 判断新的过滤器的索引配置是否与之前的过滤器相同，若相同，则不清空当前已设置的二次筛选条件 - 取反*/
  let isAlike = item.indexId === state.search_data.indexId;

  state.search_data.filterId = item.filterId;
  state.search_data.indexId = item.indexId;
  state.filter_text1_body = JSON.parse(item.conditions);
  state.filterId = "";
  getFilterTypeList(!isAlike);
  filter_text_show.value = false;

  /* 新查询 - 相关 */
  newForm.value.formData.indexId = item.indexId;
  confirmIndexId(item.indexId);
  judgmentContent.value = echoConditions({});
}
// 获取更多过滤器
async function getAllFilter() {
  let res = await filterListAll();
  state.search_form_data.all_filter_list = res.data.rows;
}
getAllFilter();
let filter_select = ref();
let filter_text_show = ref(false);
function getMoreFilter() {
  filter_text_show.value = !filter_text_show.value;
  filter_select.value.style.height = filter_text_show.value ? "40px" : "0px";
}
function changeFilter(item) {
  state.search_data.indexId = item.indexId;
  state.search_data.filterId = item.filterId;
  state.filter_text1_body = JSON.parse(item.conditions);
  getFilterTypeList();
}

async function getFilterTypeList(isAlike = true) {
  showEditCondition.value = false;
  /* 判断新的过滤器的索引配置是否与之前的过滤器相同，若相同，则不清空当前数据项 */
  /* isAlike 判断是否需要清除二次筛选条件 - 默认true 扩展不影响以前逻辑 */
  if (isAlike) {
    let res = await filterIndexIdNames(state.search_data.indexId);
    emits("filterTypeList", state.search_data.indexId);
    filterType.type_list = res.data;

    resetTwoQuery();
  }
  showEditCondition.value = true;
}
// 二次筛选
let erciFilter = ref(false);
async function changeErciFilter() {
  if (!erciFilter.value) {
    if (state.search_data.filterId !== "" && state.search_data.indexId !== "") {
      showEditCondition.value = false;
      erciFilter.value = true;
      resetTwoQuery();
      setTimeout(() => {
        showEditCondition.value = true;
      }, 100);

      // getFilterTypeList();
    } else {
      ElMessage.warning("请选择过滤器");
    }
  } else {
    erciFilter.value = false;
    dropActive.value = 1;
    state.search_data.filtersArray = [];

    resetTwoQuery();
  }
}
// 二次筛选type
function changeValue(index, result, data) {
  if (!filterType.add_list[index]) return;
  if (result) {
    filterType.add_list[index] = JSON.parse(JSON.stringify(data));
  }
  filterType.add_list[index].finished = result;
}
// 添加多条二次筛选
function add_filter() {
  let unfinishIndex = filterType.add_list.findIndex((item) => !item.finished);
  if (unfinishIndex > -1) {
    ElMessage.warning("请先填写已添加的二次筛选条件");
    return;
  }
  filterType.add_list.push({
    name: "",
    operator: "",
    value: "",
    nameText: "",
    operatorText: "",
    valueText: "",
    valueType: "input",
    kong: false,
  });
}
let delKey = ref(false);
function remove_add_filter(index) {
  filterType.add_list.splice(index, 1);
  delKey.value = !delKey.value;

  setTimeout(() => {}, 500);
}
// 保存的数据处理
/* 扩展 - 增加参数 verify 用于提交是检验格式 - 不影响以前逻辑默认 false */
function chuli(cpData, verify = false) {
  /* 看不懂原逻辑，增加深拷贝，防止数据混乱 */
  let data = JSON.parse(JSON.stringify(cpData));
  if (data.filtersArray.length > 0 && dropActive.value === 1) {
    data.filtersArray.forEach((item, index) => {
      if (item.valueType === "input") {
        /*item.valueText = item.value;*/
      }
      if (item.valueType === "datetime") {
        //? 筛选条件不显示时间戳，屏蔽重置valueText
        // item.valueText = item.value;
      }
      if (item.valueType === "datetimerange") {
        // item.valueText = item.value[0] + "," + item.value[1];
        // item.value = item.valueText;
      }
      if (item.name === "" && item.nameText === "") {
        ElMessage.warning("请完善第" + (index + 1) + "条筛选条件");
        if (verify) {
          data = false;
        }
        return false;
      }
      if (item.operator === "") {
        ElMessage.warning("请完善第" + (index + 1) + "条筛选条件");
        if (verify) {
          data = false;
        }
        return false;
      }

      if (item.operator !== "isnull" && item.operator !== "notnull") {
        if (item.value === "" && item.valueText === "") {
          ElMessage.warning("请完善第" + (index + 1) + "条筛选条件");
          if (verify) {
            data = false;
          }
          return false;
        }
      }
      delete item.valueType;
      delete item.kong;
    });
  }
  return data;
}

/* 查询保存弹窗相关 */
let dialogRef = ref();
let form = reactive({
  formData: {
    type: 1,
  },
});
let { formData } = toRefs(form);
/* 表单内容 */
let formList = ref([
  {
    formType: "input",
    prop: "name",
    label: "模板名称",
    required: true,
  },
]);

/* 保存查询 - 按钮事件 */
function saveFastFilter() {
  /* 根据是否存在 id 判断 */
  if (state.search_data.id) {
    form.formData.name = state.search_data.name;
    formList.value.push({
      formType: "radio",
      prop: "type",
      label: "类型",
      required: true,
      hide: true,
      options: [
        { value: 0, label: "新增" },
        { value: 1, label: "修改" },
      ],
    });
  }
  dialogRef.value.open();
}
/* 修改类型 - 清空 name*/
watch(
  () => form.formData.type,
  (val) => {
    if (val === 0) {
      form.formData.name = "";
    }
  }
);

/* 提交按钮 */
let ruleForm = ref();
//isRealApi 保存查询条件，调用接口。false 保存查询条件，临时调用接口，打开新窗口后删除
const submitForm = async function (close, loading, isRealApi = true) {
  let valid = isRealApi ? await ruleForm.value.validate() : true;
  if (valid) {
    /* 数据逻辑处理 */
    state.search_data.name = form.formData.name;
    let data = null;
    if (erciFilter.value === true) {
      state.search_data.filtersArray = JSON.parse(JSON.stringify(filterType.add_list));
    }

    /* 增加 - 数据验证 - 格式不正确返回 false */
    data = chuli(state.search_data, true);
    if (!data) {
      close();
      return false;
    }

    let arr1 = [];
    let arr2 = [];
    props.type_list.forEach((fields) => {
      if (fields.isShow === 1) {
        arr1.push(fields.field);
        arr2.push(fields.alias);
      }
    });
    data.fieldsArray = arr1;
    data.fieldsTextArray = arr2;

    /* 增加定时参数 */
    data.configsObject = {
      timeSwitch: state.search_form_data.timeSwitch,
      time: state.search_form_data.time,
      timeType: state.search_form_data.timeType,
    };

    if ((data.endTime !== "" && data.startTime !== "") || data.timeWindow !== "") {
      if (isRealApi) {
        loading();
        let API = form.formData.type === 0 ? saveFilterTemplate : updateSearchTemplate;
        if (form.formData.type === 0) delete data.id;
        /* 修改 - 接口数据格式变更，增加字段 */
        let newData = addNewWord(data);
        if (!newData) return;
        API(newData)
          .then((res) => {
            if (res.code === 200) {
              ElMessage.success("保存快速查询模板成功");
              close();
              getSearchTemplateList();
            }
          })
          .catch(() => {
            close(false);
          });
      } else {
        openNewWindowWithSearchOptions(data);
      }
    } else {
      ElMessage.warning("请选择时间范围");
      close();
    }
  }
};

/* 重置 */
const closeDialog = () => {
  form.formData = {
    type: 1,
  };
  formList.value = [
    {
      formType: "input",
      prop: "name",
      label: "模板名称",
      required: true,
    },
  ];
};

// 获取快速查询列表
function getSearchTemplateList(ifInit = false) {
  searchTemplateList().then((res) => {
    state.search_template_list = res.data.rows;
    state.search_template_list.forEach((item) => {
      item.showDelete = false;
    });
    if (ifInit && window.$wujie) {
      ifCopyWindow(res.data.rows);
    }
  });
}
getSearchTemplateList(true);
// 选择快速查询模板
let showEditCondition = ref(true);
watch(
  () => props.type_list,
  (val) => {
    if (showEditCondition && showEditCondition.value != "undefined") showEditCondition.value = true;
  },
  { immediate: true }
);

async function choseTemplate(item) {
  reset();
  let count = 0;
  state.search_form_data.filter_list.forEach((filter) => {
    if (filter.filterId == item.filterId) {
      count = count + 1;
    }
  });
  state.search_data.id = item.id;
  state.search_data.name = item.name;
  state.search_data.filterId = item.filterId;
  state.search_data.indexId = item.queryFilter.indexId;

  /* 定时查询回填 */
  if (item.configsObject) {
    state.search_form_data.timeSwitch = item.configsObject.timeSwitch;
    state.search_form_data.time = item.configsObject.time;
    state.search_form_data.timeType = item.configsObject.timeType;
  } else {
    state.search_form_data.timeSwitch = "";
    state.search_form_data.time = "";
    state.search_form_data.timeType = "";
  }

  // getFilterTypeList();
  emits("filterTypeList", state.search_data.indexId, item.fieldsArray);
  if (count <= 0) {
    state.filterId = item.filterId;
  }

  /* 时间选择 与 常用 回填 */
  if (item.startTime == null && item.endTime == null) {
    state.search_data.startTime = "";
    state.search_data.endTime = "";
    state.search_form_data.time_list = [];
    state.search_data.timeWindow = item.timeWindow;
  } else {
    state.search_data.startTime = item.startTime;
    state.search_data.endTime = item.endTime;
    state.search_form_data.time_list = [item.startTime, item.endTime];
    state.search_data.timeWindow = "";
  }
  state.filter_text1_body = JSON.parse(item.queryFilter.conditions);
  /* 二次筛选回填 */
  if (item.filtersType == 1) {
    let filtersArray = JSON.parse(item.filters);
    if (filtersArray.length > 0) {
      erciFilter.value = true;
      dropActive.value = 1;
      filtersArray.forEach((filter, index) => {
        let str = filter.name.substr(0, 1);
        let str2 = filter.name.substr(filter.name.length - 4);
        let chose = {};
        let valueType = "";
        let kong = false;
        let value = filter.value;
        props.type_list.forEach((type) => {
          if (filter.name === type.field) {
            chose = type;
          }
        });
        if (chose.dictionaryType) {
          valueType = "select";
        } else {
          if (str2 === "time") {
            if (filter.operatorText === "属于" || filter.operatorText === "不属于") {
              valueType = "datetimerange";
              value = filter.value.split(",");
            } else {
              valueType = "datetime";
            }
          } else {
            valueType = "input";
          }
        }
        // alert(valueType);
        showEditCondition.value = false;
        if (filter.operatorText === "为空" || filter.operatorText === "不为空") {
          kong = true;
        }
        if (index === 0) {
          filterType.add_list = [];
        }
        filterType.add_list.push({
          name: filter.name,
          operator: filter.operator,
          value: value,
          nameText: filter.nameText,
          operatorText: filter.operatorText,
          valueText: filter.valueText,
          valueType: valueType,
          kong: kong,
          // 引用规则--操作/类型
          refNameText: filter?.refNameText,
          refName: filter?.refName,
          refOperatorText: filter?.refOperatorText,
          refOperator: filter?.refOperator,
        });
      });

      // showEditCondition.value = true;
    } else {
      erciFilter.value = false;
      resetTwoQuery();
    }
  } else {
    showCondition.value = false;
    erciFilter.value = true;
    showCon.value = true;
    dropActive.value = 2;
    let filters = JSON.parse(item.filters);
    setTimeout(() => {
      if (simeSearchVersion.value == 1) {
        conditionsObject.value = filters;
      } else {
        judgmentContent.value = echoConditions(filters);
      }
      showCondition.value = true;
    }, 500);
  }
}

/* 监听路由是否包含alert*/
let ifRouteParamsFlag = true;
function ifRouteParams() {
  if (!ifRouteParamsFlag) return;
  ifRouteParamsFlag = false;
  setTimeout(() => {
    ifRouteParamsFlag = true;
  }, 1000);
  /* 获取sessionStorage中的 alertForm 数据 */
  let sData = sessionStorage.getItem("alertForm");
  if (!sData) return;

  let alertForm = JSON.parse(sData);

  alertForm.filters = JSON.stringify(alertForm.filters);

  /* 判断进入页面形式，如果是按钮点击进入，则执行choseTemplate */
  if (store.state.siem.tabType === "btn" || sessionStorage.getItem("tabType") === "btn") {
    setTimeout(() => {
      choseTemplate(alertForm);
      setTimeout(() => {
        store.commit("setTabType", "tab");
        sessionStorage.removeItem("tabType");
      }, 2000);
    }, 500);
  }
}
watch(
  () => route.params.alert,
  (val) => {
    if (val) {
      ifRouteParams();
    }
  },
  { immediate: true }
);
onActivated(() => {
  if (route.params.alert) {
    ifRouteParams();
  }
});
// 字段统计点击过滤后添加二次查询条件
function field_add_list(data) {}

//打开新页面，并保留当前搜索条件
function copyOpenWindow() {
  submitForm(
    () => {},
    () => {},
    false
  );
}
/* 保存临时的查询条件，携带参数打开新窗口*/
function openNewWindowWithSearchOptions(data) {
  let newData = addNewWord(data);
  if (!newData) return;

  const name = uuidv4();
  newData.name = name;
  newData.id = "";
  saveFilterTemplate(newData).then((res) => {
    if (res.code === 200) {
      localStorage.setItem("copyUUID", name);
      window.top.open(window.top.location.href, "_blank");
    }
  });
}
/* 如果是复制窗口，回填查询条件，并去掉url中的new参数,并删除临时模板 */
const new_uuid = localStorage.getItem("copyUUID");
if (window.$wujie) {
  localStorage.removeItem("copyUUID");
}

let topWindowInitPage = ref(false); // 非isoss内置siem,保存是否初始化页面
if (!window.$wujie) {
  window.addEventListener("PageInitEvent", () => {
    topWindowInitPage.value = true;
  });
}

const stopInitCopyWatcher = watchEffect(() => {
  if (!window.$wujie) {
    if (new_uuid) {
      if (topWindowInitPage.value && state.search_template_list.length) {
        localStorage.removeItem("copyUUID");
        ifCopyWindow(state.search_template_list);
        stopInitCopyWatcher();
      }
    } else {
      setTimeout(() => {
        stopInitCopyWatcher();
      }, 100);
    }
  } else {
    setTimeout(() => {
      stopInitCopyWatcher();
    }, 100);
  }
});

function ifCopyWindow(list) {
  if (new_uuid) {
    const item = list.find((item) => item.name === new_uuid);

    if (item) {
      deleteSearchTemplate(item.id).then(() => {
        getSearchTemplateList();
      });
      choseTemplate(item);
      setTimeout(() => {
        try {
          searchTable();
        } catch {
          setTimeout(() => {
            searchTable();
          }, 1000);
        }
      }, 100);
    }
  }
}
provide(
  "ruleIndexId",
  computed(() => {
    return state.search_data.indexId;
  })
);
</script>

<style lang="scss" scoped>
.el-dropdown {
  .el-button {
    margin: 0 10px;
  }
}

.newConDiv {
  position: relative;
  .newConComDiv {
    width: 80%;
    display: inline-block;
    background: #eeeeee;
    border-radius: 8px;
    padding: 20px;
    margin-right: 10px;
    /* 原-编辑，样式微调 */
    ::v-deep .condition-item-ul li .condition-item-ul {
      line-height: 15px;
    }
    ::v-deep .condition-wrapper {
      padding-top: 5px;
    }
  }
  .lookBtn {
    position: absolute;
    /*right: -40px;
    top: 10px;*/
    width: 32px;
    height: 32px;
    line-height: 32px;
    background: #ffe7d4;
    border-radius: 8px;
    border: 1px solid #ef8936;
    text-align: center;
    cursor: pointer;
    color: #ef8936;
    svg {
      font-size: 18px;
    }
  }
}

/* 新增- 覆盖新查询样式 - 不影响原效果 */
::v-deep .if-conent .item-wrapper:nth-child(1)::before,
::v-deep .if-conent .item-wrapper:last-child::before {
  background: #eeeeee !important;
}
::v-deep .add-formula:before {
  background: #dedede;
}

.searchBody {
  margin-top: 20px;
}
.CY_time_list {
  > span {
    float: left;
    padding: 0px 10px;
    border: 1px solid $borderColor;
    border-radius: $raduisM;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $fontColor;
    font-weight: 400;
    cursor: pointer;
  }
  > span.chose {
    border-color: $color;
    color: $color;
  }
}
.filter_list {
  width: calc(100% - 300px);
  > span {
    float: left;
    // height: 36px;
    // line-height: 36px;
    padding: 0px 10px;
    border-radius: $raduisM;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $fontColor;
    font-weight: 400;
    cursor: pointer;
  }
  > span.chose {
    color: #2797ff;
  }
}
.more_filter {
  font-size: 14px;
  color: #333333;
  position: relative;
  text-align: right;
  > span {
    cursor: pointer;
  }
  > div {
    position: absolute;
    right: 55px;
    top: 0px;
    height: 0px;
    width: 280px;
    overflow: hidden;
    transition: height 0.5s;
    -moz-transition: height 0.5s; /* Firefox 4 */
    -webkit-transition: height 0.5s; /* Safari 和 Chrome */
    -o-transition: height 0.5s;
  }
}
.two_filter_list {
  > .two_filter {
    width: 23%;
    float: left;
    margin-right: 1%;
    margin-bottom: 20px;
    background: #eee;
    border-radius: $radiusS;
    padding: 20px 10px;
    position: relative;
    :deep(.edit-condition-wrapper) {
      > p {
        &:not(:last-child) {
          margin-bottom: 10px;
        }
        > span {
          display: inline-block;
          width: 40px;
          color: $fontColor;
          font-weight: 400;
          font-size: 14px;
        }
      }
    }
    :deep(.custom-select) {
      width: calc(100% - 40px);
      float: right;
    }
    > .colse_filter {
      position: absolute;
      right: 5px;
      top: -3px;
      cursor: pointer;
    }
    > .reset_filter {
      position: absolute;
      right: 15px;
      top: -3px;
      cursor: pointer;
    }
  }
  > .add_filter {
    width: 24px;
    height: 24px;
    background: #eeeeee;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: 300;
    border: 1px solid #cccccc;
    cursor: pointer;
    color: #a5a5a5;
    margin-right: 5px;
  }
}
:deep(.el-input--small .el-input__inner) {
  // color: #37383b;
}
.search_template_list {
  > li {
    min-height: 30px;
    line-height: 30px;
    // border-bottom: 1px dashed #ccc;
    cursor: pointer;
  }
}
:deep(.condition-result-wrapper.is-root) {
  padding: 2px;
}
.siemSearchClass2 {
  max-width: 65%;
}
.siemSearchClass1 {
  max-width: 35%;
}
.del-template-icon {
  position: absolute;
  right: 0;
}
@media screen and (max-width: 1570px) {
  .siemSearchClass2 {
    max-width: 100%;
  }
  .siemSearchClass1 {
    max-width: 100%;
  }
}
</style>
