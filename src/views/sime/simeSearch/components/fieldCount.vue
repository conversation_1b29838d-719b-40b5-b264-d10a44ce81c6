<template>
  <div style="height: 100%">
    <div>
      <p class="pull-left fieldTitle">{{ state.fieldTitle }}</p>
      <p class="pull-right choseEchart">
        <span :class="state.type === '1' ? 'chose' : ''" title="饼图" @click="changeEchart('1')">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-nav_tsgz"></use>
          </svg>
        </span>
        <span :class="state.type === '2' ? 'chose' : ''" title="柱状图" @click="changeEchart('2')">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-zhuzhuangtu"></use>
          </svg>
        </span>
        <span :class="state.type === '3' ? 'chose' : ''" title="折线图" @click="changeEchart('3')">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-zhexiantu"></use>
          </svg>
        </span>
      </p>
      <div class="clearfix"></div>
    </div>
    <div style="height: 200px" ref="echart" class="margin-top20"></div>
    <div class="table margin-top20" style="height: calc(100% - 270px)">
      <el-table :data="state.tableData" border height="100%">
        <el-table-column label="统计值">
          <template #default="scope">
            <span style="margin-right: 5px">{{ scope.row.name || scope.row.key }}</span>
            <span @click="toSearch(scope)" class="pointer">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-guolv"></use>
              </svg>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="数量" prop="count"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { search_sum } from "@/api/sime/search/search";
import * as echarts from "echarts";
let state = reactive({
  type: "1",
  fieldTitle: "",
  zhuzhuangtu: {
    data1: [],
    data2: [],
  },
  bingtu: [],
  zhexiantu: {
    data1: [],
    data2: [],
  },
  tableData: [],
});
let props = defineProps({
  field: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
watch(
  () => props.field,
  (newval, old) => {
    // getFields(newval);
  },
  { deep: true }
);
// field数据统计
async function getFields(data) {
  let res = await search_sum(props.field);
  let result = res.data;
  state.tableData = result.list;
  state.fieldTitle = result.aggrfieldAlias;
  result.list.forEach((item) => {
    /*  */
    let name = item.name !== "" ? item.name : item.key;
    let value = item.count;
    state.bingtu.push({
      value: value,
      name: name,
    });
    state.zhuzhuangtu.data1.push(name);
    state.zhuzhuangtu.data2.push(value);
    state.zhexiantu.data1.push(name);
    state.zhexiantu.data2.push(value);
  });
  drawEchart();
}
// 切换图形
function changeEchart(val) {
  state.type = val;
  mycharts.dispose();
  drawEchart();
}
// 图形内容切换
let echart = ref();
let mycharts = null;
function drawEchart() {
  mycharts = echarts.init(echart.value);

  let option = null;
  if (state.type === "1") {
    let color = [];
    for (let i = 0; i < state.bingtu.length; i++) {
      color.push("rgb(" + [Math.round(Math.random() * 160), Math.round(Math.random() * 160), Math.round(Math.random() * 160)].join(",") + ")");
    }
    option = {
      tooltip: {
        trigger: "item",
      },
      color: color,
      series: [
        {
          name: "统计",
          type: "pie",
          radius: "50%",
          data: state.bingtu,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    };
  } else if (state.type === "2") {
    option = {
      xAxis: {
        type: "category",
        data: state.zhuzhuangtu.data1,
      },
      yAxis: {
        type: "value",
        nameTextStyle: {
          fontSize: 10,
        },
        axisLabel: {
          interval: 0,
        },
      },
      grid: {
        left: "auto",
        top: 30,
        right: 0,
        bottom: 0,
        containLabel: true,
      },
      series: [
        {
          data: state.zhuzhuangtu.data2,
          type: "bar",
        },
      ],
    };
  } else if (state.type === "3") {
    option = {
      xAxis: {
        type: "category",
        data: state.zhexiantu.data1,
      },
      yAxis: {
        type: "value",
        axisLabel: {
          interval: 0,
        },
      },
      grid: {
        left: "auto",
        top: 30,
        right: 0,
        bottom: 0,
        containLabel: true,
      },
      series: [
        {
          data: state.zhexiantu.data2,
          type: "line",
        },
      ],
    };
  }
  mycharts.setOption(option);
}
// 点击漏洞传递二次搜索条件
let emits = defineEmits(["add_list"]);
function toSearch(scope) {
  let operator = "";
  let operatorText = "";
  if (scope.row.key === "") {
    operator = "isnull";
    operatorText = "为空";
  } else {
    operator = "equal";
    operatorText = "等于";
  }
  let searchVal = null;
  if (scope.row.key == "") {
    searchVal = "";
  } else {
    if (isNaN(Number(scope.row.key))) {
      searchVal = scope.row.key;
    } else {
      searchVal = Number(scope.row.key);
    }
  }
  let data = {
    name: props.field.aggrfield,
    nameText: state.fieldTitle,
    operator: operator,
    operatorText: operatorText,
    value: searchVal,
    valueText: scope.row.name,
    kong: false,
  };
  emits("add_list", data);
}
onMounted(() => {
  getFields();
  window.addEventListener("resize", function () {
    mycharts.resize();
  });
});
defineExpose({
  getFields,
});
</script>

<style scoped lang="scss">
.fieldTitle {
  font-weight: 600;
  font-size: 18px;
}
.choseEchart {
  > span {
    cursor: pointer;
    color: #fff;
    background: #aaa;
    border-radius: 3px;
    padding: 0px 5px;
    margin-right: 5px;
    font-weight: 900;
    font-size: 18px;
    &.chose {
      background: #127dca;
    }
  }
}
</style>
