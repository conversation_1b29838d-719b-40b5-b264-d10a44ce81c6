<template>
  <el-card
    ref="scroll"
    class="bg-p-border-new"
    target="exportDia"
    v-loading="exportDia"
    element-loading-text="正在导出日志....."
    element-loading-background="rgba(122, 122, 122, 0.8)"
  >
    <search-body
      ref="searchBody"
      @search="searchTable"
      @filterTypeList="getFilterTypeList"
      :type_list="state.type_list"
      :field_add_list="state.field_add_list"
    ></search-body>
    <div class="siem-search-content-wrapper mt15-new" v-show="state.show_type_list.length > 0 || state.no_show_type_list.length > 0">
      <!-- 拖拽列表 -->
      <el-row :gutter="20">
        <div :style="state.fieldsShow ? 'width: 200px' : 'width: 30px'" class="searchField">
          <div class="search">
            <el-input
              v-show="state.fieldsShow"
              v-model="state.searchValue"
              placeholder="请输入字段名搜索"
              style="width: calc(100% - 30px)"
            ></el-input>
            <span class="open" @click="state.fieldsShow = !state.fieldsShow">
              <el-icon v-show="state.fieldsShow" :size="20" :color="$themeColor"><fold /></el-icon>
              <el-icon v-show="!state.fieldsShow" :size="20" :color="$themeColor"><expand /></el-icon>
            </span>
          </div>
          <div class="searchBody" v-show="state.fieldsShow">
            <!-- 选定字段 -->
            <p class="title">
              <span @click="chose_type_list = !chose_type_list" class="openClass">
                <el-icon v-if="chose_type_list" :size="14"><arrow-down-bold /></el-icon>
                <el-icon v-if="!chose_type_list" :size="14"><arrow-right-bold /></el-icon>
              </span>
              <span>选定字段</span>
              <span class="pull-right">{{ state.show_type_list.length }}</span>
            </p>
            <div ref="show_type_list" v-show="chose_type_list">
              <vue-draggable-next
                class="dragArea list-group w-full"
                :list="state.show_type_list"
                group="site"
                :sort="true"
                @change="log"
                :move="checkMove"
              >
                <div
                  class="searchItem"
                  v-for="(element, index) in state.show_type_list"
                  :key="element.field"
                  @mouseenter="element.update = true"
                  @mouseleave="element.update = false"
                  v-show="state.searchValue === '' ? true : element.searchShow"
                >
                  <el-tooltip class="box-item" effect="light" :content="element.alias" placement="top-start">
                    <span>{{ element.alias }}</span>
                  </el-tooltip>
                  <span v-show="element.update" class="pull-right choseDelete" @click="deleteChose(element, index)">
                    <el-icon :size="10"><close /></el-icon>
                  </span>
                </div>
              </vue-draggable-next>
            </div>

            <!-- 可用字段 -->
            <p class="title">
              <span @click="no_chose_type_list = !no_chose_type_list" class="openClass">
                <el-icon v-if="no_chose_type_list" :size="14"><arrow-down-bold /></el-icon>
                <el-icon v-if="!no_chose_type_list" :size="14"><arrow-right-bold /></el-icon>
              </span>
              <span>可用字段</span>
              <span class="pull-right">{{ state.no_show_type_list.length }}</span>
            </p>
            <div v-show="no_chose_type_list">
              <vue-draggable-next :list="state.no_show_type_list" group="site" @change="log" :move="checkMove">
                <div
                  class="searchItem"
                  v-for="(element, index) in state.no_show_type_list"
                  :key="element.field"
                  @mouseenter="element.update = true"
                  @mouseleave="element.update = false"
                  v-show="state.searchValue === '' ? true : element.searchShow"
                >
                  <el-tooltip class="box-item" effect="light" :content="element.alias" placement="top-start">
                    <span>{{ element.alias }}</span>
                  </el-tooltip>
                  <span v-show="element.update" class="pull-right choseAdd" @click="addChose(element, index)">
                    <el-icon :size="10"><plus /></el-icon>
                  </span>
                </div>
              </vue-draggable-next>
            </div>
          </div>
        </div>
        <div
          :style="state.fieldsShow ? 'width: calc(100% - 230px)' : 'width: calc(100% - 60px)'"
          style="margin-left: 30px; border: 1px solid #cccccc; border-radius: 8px; padding: 20px"
        >
          <search-echart
            ref="searchEchart"
            :search_filter="state.search_filter"
            :show_type_list="state.show_type_list"
            :type_list="state.type_list"
            @add_list="to_add_list"
            @exportDia="changeExportDia"
          ></search-echart>
        </div>
      </el-row>
    </div>
    <a @click="scrollTop" v-show="toTopShow" style="position: absolute; bottom: 20px; right: 50px; cursor: pointer">
      <el-icon><arrow-up-bold /></el-icon>
    </a>
  </el-card>
</template>
<script>
export default {
  name: "Analysis",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, watch, onUnmounted, nextTick } from "vue";
import { VueDraggableNext } from "vue-draggable-next";
import SearchBody from "./components/search.vue";
import { filterIndexIdNames } from "@/api/sime/search/filter";
import SearchEchart from "./components/searchEchart.vue";
import { ElMessage } from "element-plus";
import { getFieldList } from "@/api/sime/config/extendedField.js";
let state = reactive({
  search_form_data: {
    time_list: [],
  },
  type_list: [],
  show_type_list: [],
  no_show_type_list: [],
  // 搜索字段名
  searchValue: "",
  // 显示隐藏字段选择
  fieldsShow: true,
  // 传到charts/table界面的查询条件
  search_filter: {},
  // 字段点击后添加二次筛选条件
  field_add_list: {},
});
// 数据展示部分显示隐藏
let changeShow = ref(false);
// 直接返回头部
let scroll = ref();
let toTopShow = ref(false);
function scrollToTop() {
  nextTick(() => {
    // 获取界面滚动高度
    let topHeight = document.querySelector(".main-content-overflow").scrollTop;
    if (topHeight > 1000) {
      toTopShow.value = true;
    } else {
      toTopShow.value = false;
    }
  });
}
function scrollTop() {
  document.querySelector(".main-content-overflow").scrollTop = 0;
}
onMounted(() => {
  window.addEventListener("scroll", scrollToTop, true);
});
// 销毁
onUnmounted(() => {
  window.removeEventListener("scroll", scrollToTop);
});
watch(
  () => state.searchValue,
  (newval, oldval) => {
    state.show_type_list.forEach((item) => {
      let ss = item.alias.includes(state.searchValue);
      if (ss === true) {
        item.searchShow = true;
      } else {
        item.searchShow = false;
      }
    });
    state.no_show_type_list.forEach((item) => {
      let ss = item.alias.includes(state.searchValue);
      if (ss === true) {
        item.searchShow = true;
      } else {
        item.searchShow = false;
      }
    });
  }
);
// 拖拽、
function log(event) {
  const { moved, added } = event;

  if (moved) {
    //
  }
  if (added) {
    //
    //
    let arr = [];
    state.show_type_list.forEach((item) => {
      item.isShow = 1;
      arr.push(item);
    });
    state.no_show_type_list.forEach((item) => {
      item.isShow = 0;
      arr.push(item);
    });
    state.type_list = arr;
  }
  storeSelectedFields(clickedFilterId.value);
}
function checkMove(event) {
  //
  //
}
let timer = ref(null);
function searchTable(data) {
  if (timer.value) {
    clearInterval(timer.value);
  }
  if (data.indexId === "") {
    state.show_type_list = [];
    state.no_show_type_list = [];
    changeShow.value = false;
    searchEchart.value.search_sun_all(data, "showAll");
    if (data.timeSwitch == "1") {
      let num = data.timeType == "0" ? 1000 : data.timeType == "1" ? 60000 : 3600000;
      timer.value = setInterval(() => {
        searchEchart.value.search_sun_all(data, "showAll");
      }, data.time * num);
    }
  } else {
    let list = JSON.parse(JSON.stringify(data));
    state.search_filter = JSON.parse(JSON.stringify(data));
    state.search_filter.queryFilterId = data.filterId;
    list.queryFilterId = data.filterId;
    search_table_list(list);
    if (data.timeSwitch == "1") {
      let num = data.timeType == "0" ? 1000 : data.timeType == "1" ? 60000 : 3600000;
      timer.value = setInterval(() => {
        search_table_list(list);
      }, data.time * num);
    }
  }
}
// 列表查询
let searchEchart = ref();
function search_table_list(data) {
  searchEchart.value.search_sun_all(data, "showAll");
  changeShow.value = true;
}
const clickedFilterId = ref("");
async function getFilterTypeList(id, fields) {
  clickedFilterId.value = id;
  /* 每次切换 过滤器 - 清空下列表 */
  searchEchart.value && searchEchart.value.search_sun_all("noData", "showAll");

  // changeShow.value = false;
  let res = await filterIndexIdNames(id);
  state.type_list = res.data;
  state.show_type_list = [];
  state.no_show_type_list = [];
  if (state.type_list.length > 0) {
    if (fields) {
      state.type_list.forEach((item) => {
        item.update = false;
        if (fields.includes(item.field)) {
          state.show_type_list.push(item);
        } else {
          state.no_show_type_list.push(item);
        }
      });
      hebingField();
    } else {
      state.type_list.forEach((item2) => {
        item2.update = false;
        if (item2.isShow === 1) {
          state.show_type_list.push(item2);
        } else {
          state.no_show_type_list.push(item2);
        }
      });
      hebingField();
    }
  }

  /* 扩展字段 */
  let fieldList = await getFieldList({ indexId: id, status: 1 });
  if (fieldList.data.length > 0) {
    fieldList.data.forEach((field) => {
      field.field = "EXTRA_" + field.field;
      field.update = false;
      field.type = "fieldDiy";
      if (field.isShow === 1) {
        state.show_type_list.push(field);
      } else {
        state.no_show_type_list.push(field);
      }
    });
  }

  //判断缓存中是否有选定字段
  getFieldsFromStore(id, state.no_show_type_list, state.show_type_list);
}
// 字段显示隐藏
// 已选定
let chose_type_list = ref(true);
// 未选定
let no_chose_type_list = ref(true);

function deleteChose(item, index, store = true) {
  if (index === -1) {
    index = state.show_type_list.findIndex((i) => i.field === item.field);
  }
  if (index < 0) return;

  state.show_type_list.splice(index, 1);
  item.isShow = 0;
  item.update = false;
  state.no_show_type_list.push(item);
  hebingField();
  if (store) {
    storeSelectedFields(clickedFilterId.value, state.show_type_list);
  }
}
function addChose(item, index, store = true) {
  if (index === -1) {
    index = state.no_show_type_list.findIndex((i) => i.field === item.field);
  }
  if (index < 0) return;
  state.no_show_type_list.splice(index, 1);
  item.isShow = 1;
  item.update = false;
  state.show_type_list.push(item);

  hebingField();

  if (store) {
    storeSelectedFields(clickedFilterId.value, state.show_type_list);
  }
}
// 选定字段和未选定字段合并
function hebingField() {
  let arr = JSON.parse(JSON.stringify(state.show_type_list));
  state.no_show_type_list.forEach((item) => {
    arr.push(item);
  });
  state.type_list = arr;
}
// 向二次查询组件中传值
let searchBody = ref();
function to_add_list(data) {
  let ss = JSON.parse(JSON.stringify(data));
  state.field_add_list = ss;
}
let exportDia = ref(false);
function changeExportDia(data) {
  exportDia.value = data;
}

// 选定字段保存到缓存
function storeSelectedFields(id) {
  try {
    let selectedFields = state.show_type_list.map((item) => item.field);
    if (selectedFields) {
      localStorage.setItem("selectedFields" + id, JSON.stringify(selectedFields));
    }
  } catch (e) {
    console.log(e);
  }
}
// 从缓存中获取选定字段
function getFieldsFromStore(id) {
  try {
    const storeFields = localStorage.getItem("selectedFields" + id);
    if (!storeFields) return;
    const selectedFields = JSON.parse(storeFields);

    if (selectedFields) {
      const needAddFields = state.no_show_type_list.filter((item) => selectedFields.includes(item.field));
      for (let item of needAddFields) {
        addChose(item, -1, false);
      }
    }

    const needDeleteFields = state.show_type_list.filter((item) => !selectedFields.includes(item.field));
    for (let item of needDeleteFields) {
      deleteChose(item, -1, false);
    }

    state.show_type_list.sort((a, b) => selectedFields.indexOf(a.field) - selectedFields.indexOf(b.field));
  } catch (e) {
    console.log(e);
  }
}
</script>

<style lang="scss" scoped>
.searchField {
  font-size: 14px;
  .searchBody > div {
    padding-bottom: 20px;
  }
  .openClass {
    margin-right: 5px;
    cursor: pointer;
  }
  .title {
    height: 24px;
    line-height: 24px;
    > span:nth-child(3) {
      padding: 0px 5px;
      background: #eeeeee;
      font-size: 12px;
      height: 20px;
      line-height: 20px;
      border-radius: 5px;
      border: 1px solid $borderColor;
      color: $fontColor;
      text-align: center;
    }
  }
  .searchItem {
    padding: 5px 0px 5px 30px;
    color: #2797ff;
    height: 24px;
    line-height: 24px;
    cursor: pointer;
    position: relative;
    > span:last-child {
      position: absolute;
      right: 10px;
      top: 10px;
      height: 14px;
      line-height: 14px;
      border-radius: 50%;
      padding: 0px 2px;
      cursor: pointer;
    }
    > .choseDelete {
      background: #efdfe1;
      color: #ba271d;
    }
    > .choseAdd {
      background: #0971b1;
      color: #fff;
    }
  }
}
.search {
  margin-bottom: 20px;
  position: relative;
  > .open {
    position: absolute;
    right: 5px;
    top: 0px;
  }
}
.el-card {
  > :deep(.el-loading-mask) {
    position: fixed;
  }
}
.box-item {
  display: inline-block;
  width: 85%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
