<template>
  <xel-dialog :title="title" ref="dialogRef" width="600px" buttonCancel="关闭" @submit="submitForm">
    <el-form :model="formData" ref="ruleFormRef">
      <el-row>
        <el-col :span="24">
          <xel-form-item v-for="item in formList" :key="item.prop" v-model="formData[item.prop]" v-bind="item" />
        </el-col>
      </el-row>
    </el-form>
  </xel-dialog>
</template>

<script>
export default {
  name: "logPermissionDia",
};
</script>

<script setup>
/*
 * 日志权限配置 - 弹窗
 * */
import { ref, computed, reactive } from "vue";

import { getTreeData, postGetListByUserId, postUserRelation } from "@/api/sime/config/logPermisson";
import { ElMessage } from "element-plus";

let props = defineProps({
  /* 弹框标题 */
  title: {
    type: String,
    default: "配置日志权限过滤器",
  },
});

const emits = defineEmits(["close"]);

/* 表单 */
let formData = ref({
  userId: "",
  logPermissionIds: [],
});

let formList = reactive([
  {
    formType: "tree",
    prop: "logPermissionIds",
    label: "日志权限过滤器",
    multiple: true,
    required: false,
    disabledKey: "isGroupNode",
    treeOptions: {
      loadData: getTreeData, //接口名称
      params: { filter: 1, status: 1 },
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
    itemWidth: "500px",
  },
]);

/* 确定按钮事件 */
let ruleFormRef = ref();
const submitForm = (close, loading) => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let query = new FormData();
      query.append("userId", formData.value.userId);
      query.append("logPermissionIds", formData.value.logPermissionIds);
      postUserRelation(query)
        .then((res) => {
          emits("close");
          ElMessage.success("操作成功");
          close();
        })
        .catch(() => {
          close(false);
        });
    }
  });
};

let dialogRef = ref();
function open(row) {
  formData.value.userId = row.userId;
  postGetListByUserId({ userId: row.userId }).then((res) => {
    formData.value.logPermissionIds = res.data.map((item) => {
      return item.logPermissionId;
    });
    dialogRef.value.open();
  });
}

defineExpose({
  open,
});
</script>

<style scoped></style>
