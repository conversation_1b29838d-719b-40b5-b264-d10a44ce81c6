<template>
  <!-- 日志管理 - 导出日志 -->
  <div>
    <div class="btnDiv" v-for="item in downList" :key="item.type" @click="exportLogFun(item.type, item.fildName)">
      <p>{{ item.name }}</p>
      <p>
        <img src="@/assets/imgs/download.svg" alt="" />
        <!--        <el-icon :size="20">
          <Download />
        </el-icon>-->
      </p>
    </div>
  </div>
</template>

<script setup>
import { download } from "@/plugins/request";
import { ref } from "vue";

/* 按钮组 */
const downList = ref([
  { name: "导出全部", type: 1, fildName: "全部日志.zip" },
  { name: "导出最新", type: 2, fildName: "最新日志.zip" },
]);

/* 下载日志方法 */
const exportLogFun = (type, name) => {
  download("/system/log/export", name, { type: type }, "post");
};
</script>

<style scoped lang="scss">
.btnDiv {
  text-align: center;
  width: 160px;
  border: 1px solid #e6ebf5;
  cursor: pointer;
  display: inline-block;
  margin-right: 20px;
  font-size: 13px;
  color: #756c6e;
  border-radius: 10px;
  p {
    margin: 10px 0;
    img {
      width: 40px;
    }
  }
}
</style>
