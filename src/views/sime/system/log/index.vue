<template>
  <el-card>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="操作日志" name="first"><Operlog /></el-tab-pane>
      <el-tab-pane label="登录日志" name="second"><Logininfor /></el-tab-pane>
      <el-tab-pane label="运行日志" name="logExport"><LogExport /></el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "Log",
};
</script>
<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import Logininfor from "./components/logininfor.vue";
import Operlog from "./components/operlog.vue";
import LogExport from "./components/logExport.vue";
let activeName = ref("first");
function handleClick(tab, event) {}
</script>
<style scoped lang="scss"></style>
