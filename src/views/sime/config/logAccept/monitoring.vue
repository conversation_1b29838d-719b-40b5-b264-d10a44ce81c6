<template>
  <!-- 分析配置 - 状态监控 -->
  <el-card v-loading="loading" class="bg-new p-12-16-new">
    <RealTimeState @loadingFun="loadingFun" />
    <HisStatistics ref="HisStatisticsRef" />
    <!--    <div class="flex-between">
      &lt;!&ndash; <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3> &ndash;&gt;
      <el-button round @click="refresh">刷新</el-button>
    </div>

    <el-divider></el-divider>
    <el-row :gutter="100">
      <el-col :span="24">
        <p>监听器状态</p>
        <el-row class="onname">
          <el-col :span="8">
            <p>监听器名称：{{ route.params.name }}</p>
          </el-col>
          <el-col :span="8">
            <p>监听器启动时间：{{ timestamp }}</p>
          </el-col>
          <el-col :span="8">
            <p>接收数据量：{{ getListen.receiverDataSize }}</p>
          </el-col>
          <el-col :span="8">
            <p>处理队列：{{ getListen.handlerDataSize }}</p>
          </el-col>
          <el-col :span="8">
            <p>转发队列：{{ getListen.transferDataSize }}</p>
          </el-col>
          <el-col :span="8">
            <p>信息收集处理：{{ getListen.serverInfoHandlerDataSize }}/{{ getListen.serverInfoHandlerDataTime }}ms</p>
          </el-col>
          <el-col :span="8">
            <p>字段拆分处理：{{ getListen.fieldHandlerDataSize }}/{{ getListen.fieldHandlerDataTime }}ms</p>
          </el-col>
          &lt;!&ndash; <el-col :span="8">
            <p>分类处理：{{ getListen.categoryHandlerDataSize }}/{{ getListen.categoryHandlerDataTime }}ms</p>
          </el-col> &ndash;&gt;
          &lt;!&ndash; <el-col :span="8">
            <p>客户匹配处理：{{ getListen.mappingHandlerDataSize }}/{{ getListen.mappingHandlerDataTime }}ms</p>
          </el-col> &ndash;&gt;
          <el-col :span="8">
            <p>ID分配处理：{{ getListen.cuidHandlerDataSize }}/{{ getListen.cuidHandlerDataTime }}ms</p>
          </el-col>
          <el-col :span="8">
            <p>转发成功数量：{{ getListen.transferDataCounttrue }}</p>
          </el-col>
          <el-col :span="8">
            <p>转发失败数量：{{ getListen.transferDataCountfalse }}</p>
          </el-col>
          <el-col :span="8">
            <p>ES成功数量：{{ getListen.transferDataCountElasticsearchtrue }}</p>
          </el-col>
          <el-col :span="8">
            <p>ES失败数量：{{ getListen.transferDataCountElasticsearchfalse }}</p>
          </el-col>
        </el-row>

        &lt;!&ndash; 修改 - 表格 &ndash;&gt;
        <xel-table
            ref="tableRef"
            :columns="columns" :data="tabData || []"
            @selection-change="handleSelectionChange"
            :checkbox="false"
            :pagination="false"
        />
      </el-col>
    </el-row>-->
  </el-card>
</template>
<script>
export default {
  name: "monitoring",
};
</script>
<script setup>
import { ref } from "vue";
import RealTimeState from "./component/realTimeState.vue";
import HisStatistics from "./component/hisStatistics.vue";

let loading = ref(true);
let HisStatisticsRef = ref();
const loadingFun = (v) => {
  loading.value = v;
  if (v) return;
  HisStatisticsRef.value && HisStatisticsRef.value.getList();
};
</script>
<style scoped lang="scss">
.onname {
  p {
    margin: 20px;
  }
}
</style>
