<template>
  <!-- 编辑监听器 -->
  <el-card class="bg-p-border-new">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <section>
      <p class="title-bottom-line">基本信息</p>
      <el-form :model="formData" ref="ruleFormRef" label-width="130px" size="mini">
        <el-row :gutter="20">
          <el-col :span="12" v-for="item in formList" :key="item.prop">
            <xel-form-item v-model="formData[item.prop]" v-bind="item"></xel-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <section>
      <p class="title-bottom-line">日志预过滤</p>
      <el-row :gutter="20">
        <el-col :span="12" class="ml20">
          <dynamic ref="dynamicRef" label-position="right" :formList="formIpList" :data-rows="ipRows"></dynamic>
        </el-col>
      </el-row>
    </section>
    <section>
      <p class="title-bottom-line">日志解析</p>
      <el-form :model="formData" ref="logFormRef" label-width="120px" size="mini">
        <el-row :gutter="20">
          <el-col :span="8">
            <xel-form-item v-model="formData.parseType" v-bind="parseList[0]"></xel-form-item>
          </el-col>
          <el-col :span="12">
            <xel-form-item class="resize-select" v-model="formData.parseRuleList" v-bind="parseList[1]"></xel-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <section>
      <p class="title-bottom-line">内容填充</p>
      <content-fill ref="fillterRef" :data="formData.filterList"></content-fill>
    </section>

    <!-- 转发器配置  -->
    <section>
      <repeater ref="transferRef"></repeater>
    </section>
    <div class="mt20 text-center">
      <el-button type="primary" @click="submit" :loading="saveLoading">确定</el-button>
      <el-button type="button" @click="cancel">取消</el-button>
    </div>
  </el-card>
</template>
<script setup>
import { ref, reactive, onMounted, watch, toRefs, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { addReceiver, updateReceiver, getReceiver } from "@/api/sime/config/logAcceptState";
import { ElMessageBox, ElMessage } from "element-plus";
import contentFill from "./component/contentFill.vue";
import repeater from "./component/repeater.vue";
import { getIpBindingTree } from "@/api/sime/config/ruleBind";
import { useStore } from "vuex";
const store = useStore();
const router = useRouter();
const route = useRoute();
let state = reactive({
  formData: {
    name: "",
    port: undefined,
    type: "",
    numThreads: null,
    maxBytesPerSec: null,
    remark: "",
    preFilterList: [],
    parseType: "",
    /* 不知道干啥的  原来parseRuleList 的值  "1639152838305980418" */
    parseRuleList: [],
    filterList: [],
    transferList: [],
  },
  saveLoading: false,
});
let { formData, saveLoading } = toRefs(state);
// 基本信息
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "名称",
    size: "mini",
    required: true,
    type: "text",
    maxLength: "50",
  },
  {
    formType: "number",
    prop: "port",
    label: "监听端口",
    size: "mini",
    required: true,
    type: "text",
    min: 0,
    max: 65535,
    precision: "0",
  },
  {
    formType: "select",
    prop: "type",
    label: "监听类型",
    size: "mini",
    required: true,
    options: [], //字典自定义
    dictName: "receiver_server_type",
    sime: true,
  },
  {
    formType: "number",
    prop: "numThreads",
    label: "处理线程数",
    size: "mini",
    required: true,
    vxRule: "IntPlus",
    min: 1,
    max: 64,
  },
  {
    formType: "number",
    prop: "maxBytesPerSec",
    label: "每秒最大字节数",
    size: "mini",
    required: true,
    min: 0,
    max: 999999999,
    precision: "0",
  },
  {
    formType: "input",
    size: "mini",
    prop: "remark",
    label: "描述",
    type: "textarea",
    maxLength: "512",
  },
]);
// 日志预过滤
let formIpList = reactive([
  {
    formType: "input",
    prop: "ip",
    label: "IP地址",
    itemWidth: "calc(97% - 120px) ",
    value: "",
    vxRule: "IP",
  },
]);

// 日志解析
let parseList = reactive([
  {
    formType: "select",
    prop: "parseType",
    label: "解析类型",
    dictName: "parse_type",
    sime: true,
    required: true,
    onChange(val) {
      changeParseType(val, "change");
    },
  },
  {
    formType: "tree",
    prop: "parseRuleList",
    label: "解析规则",
    multiple: true,
    treeData: [],
    // treeOptions: {
    //   loadData: getIpBindingTree, //接口名称
    // },
    disabledKey: "isGroupNode",
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
    required: true,
  },
]);
let ruleFormRef = ref();
let dynamicRef = ref();
let logFormRef = ref();
let fillterRef = ref();
let transferRef = ref();
// 切换解析类型
function changeParseType(parseType, type) {
  if (type) {
    state.formData.parseRuleList = [];
  }

  getParseTree(parseType);
}

// 获取解析树
getParseTree();
async function getParseTree(parseType) {
  const res = await getIpBindingTree({ parseType: parseType, status: 1 });
  parseList[1].treeData = res.data;
}

onMounted(() => {
  if (route.params.id) {
    getLogInfo();
  }
});

// 获取详情信息
async function getLogInfo() {
  const res = await getReceiver(route.params.id, route.params.urlPath ? route.params.urlPath : "receiver");
  state.formData = res.data;
  setIpList(res.data.preFilterList, false);
  /* 增加延迟，测试数据异常问题 */
  nextTick(() => {
    changeParseType(res.data.parseType);
  });
  state.formData.parseRuleList = res.data.parseRuleList.map((item) => item.ruleId);
}

// 处理ip数据
let ipRows = reactive([]);
function setIpList(list, disabled = false, isInternet = false) {
  for (let item of list) {
    let copyFormList = JSON.parse(JSON.stringify(formIpList));

    copyFormList[0].value = item.ip;
    if (disabled && item.ip) {
      copyFormList[0].disabled = true;
    }
    ipRows.push(copyFormList);
  }
}
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      logFormRef.value.validate((lValid) => {
        if (lValid) {
          if (transferRef.value.transferList.length > 0) {
            // 判断内容填充有无保存的
            let finishList = fillterRef.value.filterList.filter((item) => !item.isFinish);

            if (finishList.length > 0) {
              ElMessage.warning("内容填充有未保存数据");
              return;
            }
            state.saveLoading = true;
            let parseRuleList = state.formData.parseRuleList.map((item) => {
              return { ruleId: item };
            });

            let ipList = dynamicRef.value.list
              .filter((item) => item[0].value)
              .map((dItem) => {
                return {
                  ip: dItem[0].value,
                };
              });

            let params = {
              version: route.params.id ? "" : "0",
              ...state.formData,
              parseRuleList: parseRuleList,
              preFilterList: ipList,
              filterList: fillterRef.value.filterList,

              transferList: transferRef.value.transferList,
            };
            let port = route.params.id ? updateReceiver : addReceiver;
            port(params, route.params.urlPath ? route.params.urlPath : "receiver")
              .then((res) => {
                cancel();
              })
              .finally(() => {
                state.saveLoading = false;
              });
          } else {
            ElMessage.warning("转发器配置不能为空");
          }
        }
      });
    }
  });
}

function cancel() {
  state.formData = {
    name: "",
    port: undefined,
    type: "",
    numThreads: null,
    remark: "",
    preFilterList: [],
    parseType: "",
    parseRuleList: [],
    filterList: [],
    transferList: [],
  };
  router.push({
    name: "LogAccept",
  });
  store.commit("closeCurrentTab");
}
</script>

<style lang="scss" scoped>
.title-bottom-line {
  margin: 20px;
  font-size: 14px;
}
:deep(.resize-select .custom-select) {
  resize: both;
}
//解析规则
:deep(.custom-select) {
  max-height: none;
  height: 50px;
}
</style>
