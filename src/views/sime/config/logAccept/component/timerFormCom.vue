<template>
  <el-popover placement="bottom" :width="400" trigger="click" :hide-after="0">
    <template #reference>
      <el-button size="mini">
        <el-icon><search /></el-icon> 自动刷新
        <el-icon><ArrowDown /></el-icon>
      </el-button>
    </template>
    <div style="text-align: center">
      <el-form ref="search_forms" label-width="90px">
        <el-form-item label="自动刷新">
          <el-switch
            v-model="formData.timeSwitch"
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949; width: 60px; float: left; margin-top: 8px; margin-right: 10px"
            active-value="1"
            inactive-value="0"
          />
          <el-input v-model="formData.time" type="number" placeholder="时间" class="pull-left" style="width: calc(50% - 40px); margin-right: 10px" />
          <el-select v-model="formData.timeType" class="pull-left" style="width: calc(50% - 40px)">
            <el-option value="0" label="秒" />
            <el-option value="1" label="分" />
            <el-option value="2" label="时" />
          </el-select>
        </el-form-item>
      </el-form>

      <el-button round type="primary" @click="subBtnFun" :loading="loading">确定</el-button>
    </div>
  </el-popover>
</template>

<script>
export default {
  name: "TimerFormCom",
};
</script>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
let emits = defineEmits(["searchFun"]);
/* 自动刷新 - 表单项 */
let formData = ref({
  timeSwitch: "0",
  time: "",
  timeType: "0",
});

/* 定时器 */
let timer = ref(null);
let loading = ref(false);
const timerFun = () => {
  if (timer.value) clearInterval(timer.value);
  let num = formData.value.timeType === "0" ? 1000 : formData.value.timeType === "1" ? 60000 : 3600000;
  timer.value = setInterval(() => {
    emits("searchFun");
  }, formData.value.time * num);
  ElMessage.success("已开启自动刷新");
};

/* 自动刷新 */
const subBtnFun = () => {
  loading.value = true;
  if (formData.value.timeSwitch === "0") {
    loading.value = false;
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
      ElMessage.success("已关闭自动刷新");
      return false;
    }
    ElMessage.warning("请先开启自动刷新");
  } else if (formData.value.time === "") {
    loading.value = false;
    ElMessage.warning("请输入/选择自动刷新周期");
    if (timer.value) clearInterval(timer.value);
    return false;
  } else {
    timerFun();
  }
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};
</script>

<style scoped></style>
