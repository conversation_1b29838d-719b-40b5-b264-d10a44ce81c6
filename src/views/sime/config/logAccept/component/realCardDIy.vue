<template>
  <!-- 自定义 - 卡片 -->
  <div class="realCardDiv">
    <el-row :gutter="20">
      <el-col :span="7" class="successCol">
        <p class="mt15">
          <span class="iconSpan success">
            <el-icon :size="20"><Check /></el-icon>
          </span>
          {{ successName }}
        </p>
        <p class="counP">
          {{ successNum }}
        </p>
      </el-col>
      <el-col :span="7">
        <p class="mt15">
          <span class="iconSpan error">
            <el-icon :size="20"><Close /></el-icon>
          </span>
          {{ errorName }}
        </p>
        <p class="counP">
          {{ errorNum }}
        </p>
      </el-col>
      <el-col :span="10">
        <echartPieBase v-if="isShow" :options="options" width="100%" height="120px" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import echartPieBase from "starso/echartPieBase";
import { ref, watch, onMounted, onBeforeUnmount } from "vue";
let props = defineProps({
  /* 成功 数量 - 名称 */
  successName: {
    type: String,
    default() {
      return "成功数量";
    },
  },
  successNum: {
    type: Number,
    default() {
      return 0;
    },
  },
  /* 失败 数量 - 名称 */
  errorName: {
    type: String,
    default() {
      return "失败数量";
    },
  },
  errorNum: {
    type: Number,
    default() {
      return 0;
    },
  },
});

/* 饼图相关 */
let isShow = ref(false);
let options = ref({});

let screenWid = ref(0);
const getCgl = (s, e, type) => {
  /* 监测宽度变化 */
  let screenW = window.innerWidth || document.documentElement.clientWidth;
  if (screenWid.value === 0) {
    screenWid.value = screenW;
  } else {
    if (screenWid.value === screenW && type === "resize") {
      return false;
    } else {
      screenWid.value = screenW;
    }
  }
  isShow.value = false;
  let num = ((s / (s + e)) * 100).toFixed(3);
  let num2 = 100;
  if (num !== 0 && num != "NaN") {
    let cnum = num.substring(0, num.lastIndexOf(".") + 3);
    num2 = cnum === "100.00" ? 100 : cnum;
  }
  options.value = {
    color: ["#9FE081", "#5470C6"],
    /* orient: 'vertical', */
    legend: { bottom: "0%", right: "0%" },
    tooltip: {
      trigger: "item",
      confine: true,
      formatter: function (data) {
        return `<span style="display: inline-block;width: 10px; height: 10px;border-radius: 20px;background: ${data.color}"></span>
            ${data.data.name}：${data.data.value}`;
      },
    },
    series: [
      {
        type: "pie",
        radius: ["70%", "90%"],
        center: ["30%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 5,
          borderColor: "#fff",
          borderWidth: 1,
        },
        label: {
          normal: {
            show: true,
            position: "center",
            color: "#000",
            fontWeight: 900,
            fontSize: "11px",
            formatter: `成功率 ${num2}%`,
          },
          emphasis: {
            //中间文字显示
            show: true,
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { name: "成功", value: s },
          { name: "失败", value: e },
        ],
      },
    ],
  };
  setTimeout(() => {
    isShow.value = true;
  }, 100);
};

/* 窗体变化 */
const handleResize = () => {
  getCgl(props.successNum, props.errorNum, "resize");
};
onMounted(() => {
  window.addEventListener("resize", handleResize);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});

watch(
  () => props.successNum,
  (val) => {
    getCgl(props.successNum, props.errorNum, "updata");
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.realCardDiv {
  .mt15 {
    margin-top: 15px;
  }
  display: inline-block;
  margin: 5px;
  width: 100%;
  /*height: 128px;*/
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.12);

  .el-col {
    text-align: center;
    color: #848484;
    font-size: 14px;
  }
  .successCol {
    border-right: 1px solid #ebedf1;
  }

  .counP {
    font-size: 28px;
    color: #28334f;
    font-weight: bold;
    margin-top: 25px;
  }

  /* 图标 */
  .iconSpan {
    width: 25px;
    height: 25px;
    display: inline-block;
    color: #fff;
    border-radius: 20px;
    text-align: center;
    padding-top: 2px;
    font-weight: bold;
    position: relative;
    top: 5px;
    left: -5px;
    &.success {
      background: #91cc75;
    }
    &.error {
      background: #ee6666;
    }
  }
}
</style>
