<template>
  <el-form ref="staticFormRef" :model="staticForm" :class="{ staticForm: staticType }">
    <el-table :data="staticForm.tableData" :border="parentBorder" ref="tableRef" width="100%">
      <el-table-column label="字段名" prop="name">
        <template #default="scope">
          <el-form-item :prop="'tableData.' + scope.$index + '.name'" :rules="rules.name">
            <el-select
              :placeholder="'请选择字段名'"
              v-model="scope.row.name"
              clearable
              filterable
              :disabled="!scope.row.editable"
              @change="changeFile($event)"
            >
              <el-option
                v-for="item in fileOptions"
                :key="item.field"
                :label="`${item.fieldText}_${item.field}`"
                :value="`${item.fieldText}_${item.field}`"
              >
                <span class="pull-left">{{ item.fieldText }}</span>
                <span class="pull-right">{{ item.field }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="填充值" prop="value">
        <template #default="scope">
          <el-form-item :prop="'tableData.' + scope.$index + '.value'">
            <el-select
              v-if="scope.row.fillOptions && scope.row.fillOptions.length > 0"
              :placeholder="'请选择字典'"
              style="width: 100%"
              v-model="scope.row.value"
              clearable
              filterable
              :disabled="!scope.row.editable"
            >
              <el-option v-for="item in scope.row.fillOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-input v-else v-model="scope.row.value" :disabled="!scope.row.editable" maxlength="100"></el-input>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <xel-handle-btns :btnList="getBtnList(scope)" :scope="scope"></xel-handle-btns>
        </template>
      </el-table-column>
    </el-table>

    <el-button class="mt20 mb20" type="button" @click="addFill()">
      <el-icon :size="12" class="mr20"> <plus /> </el-icon>
      添加字段
    </el-button>
    <div class="pull-right mt20 mb20" v-if="staticType && staticForm.tableData.length > 0">
      <el-button type="primary" @click="submitTable" :loading="loading"> 确定 </el-button>
      <!-- <el-button type="buttom" @click="cancelTable"> 取消 </el-button> -->
    </div>
  </el-form>
</template>
<script setup>
import { reactive, toRefs, nextTick, watch, computed, ref } from "vue";

import { getLogFieldList } from "@/api/sime/config/field.js";
import { useRouter, useRoute } from "vue-router";
import { getDictsData } from "@/utils/getDicts";
import { ElMessageBox, ElMessage } from "element-plus";

const route = useRoute();
let emits = defineEmits(["addStaticData"]);
let props = defineProps({
  tableData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  detail: {
    type: Boolean,
    default: false,
  },
  sort: {
    type: Number,
    default: null,
  },
  expand: {
    type: Boolean,
    default: true,
  },
  staticType: {
    type: Boolean,
    default: true,
  },
});
let state = reactive({
  staticForm: {
    tableData: [],
  },
  allFileOptions: [],
  fileOptions: [], //字段下拉
  loading: false,
});
let isFinish = ref(true);
let { staticForm, fileOptions, allFileOptions, loading } = toRefs(state);
let staticFormRef = ref();
const rules = reactive({
  name: [
    {
      required: true,
      message: "请选择字段名",
      trigger: ["blur", "change"],
    },
  ],
});
getLogFieldListFn();
//  获取表格字段名
function getLogFieldListFn() {
  getLogFieldList().then((res) => {
    state.allFileOptions = res.data.rows;
    state.fileOptions = res.data.rows;
  });
}

// 字段名唯一 并且确定填充值的下拉
function changeFile(data) {
  let currentField = data.split("_")[1];
  var dictType = state.allFileOptions.filter((item) => item.field === currentField).map((item) => item.dictType)[0];
  // 选中字段时 根据其中的包含的字典，默认值变为下拉框 反之 input
  state.staticForm.tableData.forEach(async (item) => {
    if (item.name.split("_")[1] == currentField) {
      item.fillOptions = dictType ? await getDictsData(dictType) : [];
    }
  });
}
watch(
  () => state.staticForm.tableData,
  // 字段名唯一
  () => {
    if (state.staticForm.tableData.length > 0) {
      // 筛选出已经选中
      let selectedField = state.staticForm.tableData.map((item) => {
        return { field: item.name ? item.name.split("_")[1] : "" };
      });

      state.fileOptions = state.allFileOptions.filter((item) => !selectedField.some((cItem) => cItem.field === item.field));
    } else {
      state.fileOptions = state.allFileOptions;
    }
  },
  { deep: true, immediate: true }
);
// 新增静态数据
function addFill() {
  isFinish.value = false;
  state.staticForm.tableData.push({
    name: "",
    value: "",
    editable: true,
  });
}
// 表格按钮
function getBtnList() {
  return [
    {
      icon: "edit",
      title: "修改",
      onClick(scope) {
        isFinish.value = false;
        state.staticForm.tableData[scope.$index]["editable"] = true;
      },
    },
    {
      icon: "delete",
      title: "删除",
      onClick(scope) {
        state.staticForm.tableData.splice(scope.$index, 1);
      },
    },
  ];
}

watch(
  () => props.expand,
  (val) => {
    if (val) {
      if (props.tableData.length > 0) {
        console.log("props.tableData: ", props.tableData);
        state.staticForm.tableData = props.tableData.map((item) => {
          return {
            ...item,
            name: item.nameText + "_" + item.name,
            editable: false,
          };
        });
        state.staticForm.tableData.forEach((item) => {
          changeFile(item.name);
        });
      }
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
// 提交表格
function submitTable() {
  staticFormRef.value.validate((valid) => {
    if (valid) {
      isFinish.value = true;
      state.loading = true;
      state.staticForm.tableData = state.staticForm.tableData.map((item) => {
        return {
          ...item,
          editable: false,
        };
      });
      // 静态填充时
      emits("addStaticData", {
        filterFieldList: state.staticForm.tableData,
        sort: props.sort,
        isFinish: isFinish.value,
      });

      props.staticType ? ElMessage.success("操作成功") : "";
      state.loading = false;
    }
  });
}
// 取消
function cancelTable() {
  isFinish.value = true;
  state.staticForm.tableData = props.tableData;
  state.staticForm.tableData.forEach((item) => {
    changeFile(item.name);
  });
}
defineExpose({
  filterFieldList: computed(() => {
    return state.staticForm.tableData || [];
  }),
});
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.staticForm .el-form-item--small) {
  margin-top: 18px;
}
.staticForm {
  width: calc(100% - 260px);
  margin: 0 auto;
}
</style>
