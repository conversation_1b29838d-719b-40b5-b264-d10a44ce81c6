<template>
  <div class="title-bottom-line flex-between">
    <p>转发器配置</p>
    <el-button class="search-button" @click="addRepeater">
      <el-icon :size="12">
        <plus />
      </el-icon>
      转发器
    </el-button>
  </div>

  <xel-table ref="tableRef" :columns="columns" :data="tableData || []" :pagination="false"> </xel-table>
  <!-- 转发器弹框 -->
  <xel-dialog :title="dialogTit" ref="repeaterRef" @submit="addTransfer" @close="resetForm">
    <el-form :model="formData" ref="tranFormRef" label-width="200px" size="mini" class="formWrapper">
      <xel-form-item v-for="(item, index) in formList" :key="index + '1'" v-model="formData[item.prop]" v-bind="item" :width="'340px'" />
      <el-form-item label="索引格式" prop="indexFormat" v-if="isShows" :rules="rules">
        <el-select
          v-model="formData.indexFormat"
          filterable
          clearable
          allow-create
          default-first-option
          style="width: 340px"
          placeholder="请选择索引格式"
        >
          <el-option v-for="item in formaList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <xel-form-item v-for="(item, index) in formList1" :key="index" v-model="formData[item.prop]" v-bind="item" :width="'340px'" />
    </el-form>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick, watch, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getLogFieldList } from "@/api/sime/config/field.js";
import { transfer, getDetails as getTableData } from "@/api/sime/config/logAcceptState";
import { getDictsData } from "@/utils/getDicts";
let props = defineProps({
  tableData: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
const route = useRoute();
let state = reactive({
  typeOptions: [],
  formData: {
    type: "",
    protocol: "",
    host: "",
    port: "",
    regex: "",
    isAuthorization: 0,
    userName: "",
    password: "",
    topic: "",
    id: "",
    timeField: "",
    indexFormat: "",
  },
  tableData: [],
  editIndex: -1,
});
let { formData, tableData } = toRefs(state);
let rules = ref([{ required: true, message: "请选择索引格式", trigger: ["blur", "change"] }]);
let tableRef = ref();
let repeaterRef = ref();
let tranFormRef = ref();
let dialogTit = ref("新增转发器");
const columns = [
  {
    prop: "type",
    label: "转发器类型",
    formatter(row) {
      let label = state.typeOptions
        .filter((item) => item.value == row.type)
        .map((tItem) => {
          return tItem.label;
        });
      return label;
    },
  },
  {
    prop: "host",
    label: "地址",
  },
  {
    prop: "port",
    label: "端口",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Edit",
        title: "编辑",
        onClick(scope) {
          editData(scope.row, scope.$index);
        },
      },
      {
        icon: "Delete",
        title: "删除",
        onClick(scope) {
          state.tableData.splice(scope.$index, 1);
        },
      },
    ],
  },
];

let isShows = ref(false);

let formList = reactive([
  {
    formType: "select",
    prop: "type",
    label: "转发器类型",
    size: "mini",
    required: true,
    options: [], //字典自定义
    // 字典关键字
    dictName: "receiver_transfer_type",
    sime: true,
    onChange(val) {
      if (val == "1") {
        formList[1].isShow = false;
        isShows.value = false;
        formList1[0].isShow = false;
        formList1[1].isShow = false;
        formList1[2].isShow = false;
        formList1[3].isShow = false;
        formList1[4].isShow = false;
      } else if (val == "2") {
        formList[1].isShow = true;
        isShows.value = true;
        formList1[0].isShow = true;
        formList1[1].isShow = false;
        formList1[2].isShow = true;
        state.formData.isAuthorization = 0;
        state.formData.userName = "";
        state.formData.password = "";
        formList1[3].isShow = false;
        formList1[4].isShow = false;
      } else {
        formList[1].isShow = false;
        isShows.value = false;
        formList1[0].isShow = false;
        formList1[1].isShow = true;
        formList1[2].isShow = true;
        state.formData.isAuthorization = 0;
        state.formData.userName = "";
        state.formData.password = "";
        formList1[3].isShow = false;
        formList1[4].isShow = false;
      }
    },
  },
  {
    formType: "select",
    prop: "protocol",
    label: "协议",
    size: "mini",
    required: true,
    filterable: true,
    dictName: "config_es_protocol",
    sime: true,
    isShow: false,
  },
  {
    formType: "input",
    prop: "host",
    label: "转发地址",
    size: "mini",
    required: true,
    type: "text",
    maxLength: "50",
    // vxRule: "IP",
  },
  {
    formType: "input",
    prop: "port",
    label: "转发端口",
    size: "mini",
    required: true,
    type: "text",
    maxLength: "50",
  },
  {
    formType: "input",
    prop: "regex",
    label: "地址/端口分隔符",
    size: "mini",
    required: true,
    type: "text",
    maxLength: "32",
  },
]);

let formList1 = reactive([
  {
    formType: "select",
    prop: "timeField",
    label: "时间字段",
    maxLength: "32",
    size: "mini",
    required: true,
    filterable: true,
    options: [], //字典自定义
    // 字典关键字
    dictName: "",
    sime: true,
    isShow: false,
  },
  {
    formType: "input",
    prop: "topic",
    label: "队列名",
    size: "mini",
    required: true,
    type: "text",
    isShow: false,
  },
  {
    formType: "radio",
    prop: "isAuthorization",
    label: "是否开启身份认证",
    isNumber: true,
    required: true,
    // 字典关键字
    dictName: "receiver_yes_no",
    sime: true,
    onChange(val) {
      let arr = {
        1: true,
        0: false,
      };
      formList1[3].isShow = arr[val];
      formList1[4].isShow = arr[val];
    },
  },
  {
    formType: "input",
    type: "text",
    prop: "userName",
    label: "用户名",
    required: true,
    isShow: false,
    autocomplete: "new-password",
  },
  {
    formType: "input",
    type: "password",
    prop: "password",
    label: "密码",
    required: true,
    isShow: false,
    autocomplete: "new-password",
  },
]);

getLogFieldListFn();
getTypeList();
// 获取表格数据
onMounted(() => {
  if (route.params.id) {
    getDetailData();
  }
});
watch(
  () => props.tableData,
  (val) => {
    if (val) {
      state.tableData = props.tableData;
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
//// 转发类型
async function getTypeList() {
  let res = await getDictsData("receiver_transfer_type");
  state.typeOptions = res;
}
//  获取表格字段名
let formaList = ref([]);
function getLogFieldListFn() {
  getLogFieldList().then((res) => {
    formList1[0].options = res.data.rows.map((item) => {
      return {
        label: item.fieldText,
        value: item.field,
      };
    });
  });

  /* 新增 - 获取索引格式 - 动态接口 */
  transfer({ type: 2 }, route.params.urlPath ? route.params.urlPath : "receiver").then((res) => {
    formaList.value = res.data.map((val) => {
      return {
        label: val.indexFormat,
        value: val.indexFormat,
      };
    });
  });
}
async function getDetailData() {
  const res = await getTableData({ id: route.params.id }, route.params.urlPath ? route.params.urlPath : "receiver");
  state.tableData = res.data.transferList;
}

// 新增
function addRepeater() {
  resetForm();
  dialogTit.value = "添加转发器";
  state.formData.isAuthorization = 0;
  state.formData.userName = "";
  state.formData.password = "";
  formList[1].isShow = false;
  formList1[3].isShow = false;
  formList1[4].isShow = false;
  getLogFieldListFn();
  state.editIndex = -1;
  repeaterRef.value.open();
}

function resetForm() {
  Object.keys(state.formData).forEach((keys) => {
    state.formData[keys] = "";
  });
  state.formData["isAuthorization"] = 0;
  isShows.value = false;
}

// 修改
function editData(val, index) {
  dialogTit.value = "编辑转发器";
  state.editIndex = index;
  Object.keys(state.formData).forEach((keys) => {
    state.formData[keys] = val[keys];
  });
  // 显隐控制

  if (state.formData.type == "1") {
    formList[1].isShow = false;
    isShows.value = false;
    formList1[0].isShow = false;
    formList1[1].isShow = false;
    formList1[2].isShow = false;
    formList1[3].isShow = false;
    formList1[4].isShow = false;
  } else if (state.formData.type == "2") {
    formList[1].isShow = true;
    isShows.value = true;
    formList1[0].isShow = true;
    formList1[1].isShow = false;
    formList1[2].isShow = true;
  } else {
    formList[1].isShow = false;
    isShows.value = false;
    formList1[0].isShow = false;
    formList1[1].isShow = true;
    formList1[2].isShow = true;
  }

  let arr = {
    1: true,
    0: false,
  };
  formList1[3].isShow = arr[state.formData.isAuthorization];
  formList1[4].isShow = arr[state.formData.isAuthorization];

  repeaterRef.value.open();
}

// 提交按钮
function addTransfer() {
  tranFormRef.value.validate((valid) => {
    if (valid) {
      let copyTransferData = JSON.parse(JSON.stringify(state.formData));
      if (state.editIndex == -1) {
        state.tableData.push(copyTransferData);
      } else {
        state.tableData[state.editIndex] = copyTransferData;
      }
      repeaterRef.value.close();
      formList1[0].isShow = false;
      resetForm();
    } else {
      return false;
    }
  });
}

defineExpose({
  transferList: computed(() => {
    return state.tableData || [];
  }),
});
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
