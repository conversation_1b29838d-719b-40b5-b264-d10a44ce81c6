<template>
  <ul class="condition-item-ul" v-if="listSelf.length > 0">
    <li v-for="(item, index) in listSelf" :key="index">
      <!-- 条件语句 -->
      <el-popover placement="bottom" :width="500" class="condition" :visible="index == selectIndex" trigger="click">
        <template #reference>
          <el-tag closable @click="editCond(item, index)" @close="delItemByIndex(item, index)" v-if="item.data.name">
            <span>
              {{ item.data.nameText }}_{{ item.data.name }}
              <span class="margin-right20 margin-left20"> {{ item.data.operatorText }}</span>
              <span>{{ item.data.value }}</span>
              <span v-if="item.data.operator == 'isnull'"> 为空</span>
              <span v-if="item.data.operator == 'notnull'"> 不为空</span>
            </span>
          </el-tag>
          <el-button @click="editCond(item, index, 'addType')" v-else>
            <el-icon :size="12" class="mr20"> <plus /> </el-icon>添加筛选<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
        </template>
        <!-- 条件编辑 -->
        <div class="condition-form-wrapper">
          <edit-condition
            v-if="index == selectIndex"
            :typeList="allFileOptions"
            :data="item.data"
            @finish="
              (a, b) => {
                finishCon(item, a, b);
              }
            "
          ></edit-condition>
          <div class="footer pull-right mt20">
            <el-button v-if="editable" type="primary" @click="saveCond(item, index)">确定</el-button>
            <el-button type="button" @click="cancel(item, index)">取消</el-button>
          </div>
        </div>
      </el-popover>
    </li>
  </ul>
</template>
<script>
export default {
  //添加逻辑运算符 直接选中
  directives: {
    focus: {
      // 指令的定义
      mounted(el) {
        if (el.classList.contains("pointer")) {
          el.click();
        }
      },
    },
  },
};
</script>
<script setup>
import { reactive, toRefs, computed, watch, ref } from "vue";
import { ElMessage } from "element-plus";
import editCondition from "../../../components/editCondition.vue";
import { getLogFieldList } from "@/api/sime/config/field.js";
import dicsStore from "@/store/modules/dictsData";
if (!dicsStore.state.numberFlag || !dicsStore.state.stringFlag || !dicsStore.state.ruleFlag) {
  dicsStore.actions.getFilterOperatorNumRes();
  dicsStore.actions.getFilterOperatorStrRes();
  dicsStore.actions.getRuleOperatorRes();
}
let state = reactive({
  selectIndex: null, //当前修改规则
  typeList: [],
  allFileOptions: [],
  conditionList: props.list,
});
let { typeList, selectIndex, allFileOptions } = toRefs(state);

let props = defineProps({
  list: {
    type: Array,
    default() {
      return [];
    },
  },
  levelIndexArr: {
    type: Array,
    default() {
      return [];
    },
  },
  //条件编辑的操作列表
  // typeList: {
  //   type: Array,
  //   default() {
  //     return [];
  //   },
  // },
  //是否可编辑
  editable: {
    type: Boolean,
    default: true,
  },
});

let listSelf = computed(() => {
  let addItemCont = {
    notetype: "condition",
    icon: "icon-condition",
    label: "条件",
    color: "#E6A23C",
    bgColor: "#f0f1f4",
    editStatus: false,
    kong: false,
    data: {
      nameText: "", //filledText
      operatorText: "", //操作汉字
      valueText: "",
    },
  };

  return [...state.conditionList, addItemCont];
});
getLogFieldListFn();
//  获取表格字段名
function getLogFieldListFn() {
  getLogFieldList().then((res) => {
    state.allFileOptions = res.data.rows.map((item) => {
      return {
        ...item,
        alias: item.fieldText,
      };
    });
    state.typeList = res.data.rows.map((item) => {
      return {
        ...item,
        alias: item.fieldText,
      };
    });
  });
}
watch(
  () => state.conditionList,
  // 字段名唯一
  () => {
    if (state.conditionList.length > 0) {
      // 筛选出已经选中
      let selectedField = state.conditionList.map((item) => {
        return { field: item.data.name };
      });

      state.typeList = state.allFileOptions
        .filter((item) => !selectedField.some((cItem) => cItem.field === item.field))
        .map((aItem) => {
          return {
            ...aItem,
            alias: aItem.fieldText,
          };
        });
    } else {
      state.typeList = state.allFileOptions;
    }
  },
  { deep: true, immediate: true }
);

//条件编辑
/* 增加中间键 */
const cpData = ref({});
function finishCon(item, result, data) {
  item.finished = result;
  if (result) {
    cpData.value = JSON.parse(JSON.stringify(item));
    cpData.value.data = {
      name: data.name, //字段名 filed
      operator: data.operator, //操作
      value: data.value, //输入框值
      nameText: data.nameText, //filledText
      operatorText: data.operatorText, //操作汉字
      valueText: data.valueText,
      valueType: data.valueType,
    };
    /*item.data = {
      name: data.name, //字段名 filed
      operator: data.operator, //操作
      value: data.value, //输入框值
      nameText: data.nameText, //filledText
      operatorText: data.operatorText, //操作汉字
      valueText: data.valueText,
    };*/
  }
}

// 编辑/新增填充条件
function editCond(item, index, type) {
  if (!props.editable) return;
  item.editStatus = type == "addType" ? false : true;
  state.selectIndex = index;
}

//删除条件
function delItemByIndex(list, index) {
  if (!props.editable) {
    return;
  }

  state.conditionList.splice(index, 1);
}

// 确定添加规则
function saveCond(item, index) {
  item = cpData.value;
  /* 判断 能否继续 */
  if (item.data.value === "" && item.data.operatorText !== "为空" && item.data.operatorText !== "不为空") {
    ElMessage.warning("条件不完整，请填写");
    return false;
  }
  if (item.editStatus) {
    state.conditionList[state.selectIndex] = item;
  } else {
    state.conditionList.push(item);
  }
  item.editStatus = false;
  state.selectIndex = null;
}
// 取消添加
function cancel(item) {
  item.editStatus = false;
  state.selectIndex = null;
}

//父组件可以调用的方法
defineExpose({
  list: computed(() => {
    return (
      listSelf.value
        .filter((item) => item.data.name)
        .map((lItem) => {
          return {
            name: lItem.data.name,
            nameText: lItem.data.nameText,
            operator: lItem.data.operator,
            operatorText: lItem.data.operatorText,
            value: lItem.data.value,
            valueText: lItem.data.valueText,
          };
        }) || []
    );
  }),
});
//
</script>

<style lang="scss" scoped>
.condition-item-ul {
  li {
    margin-top: 18px;
    margin-right: 18px;
    position: relative;
    z-index: 1;
    display: inline-block;
    .condition-item-ul {
      padding-left: 46px;
      position: relative;
      &::before {
        content: "";
        display: block;
        background: #dedede;
        position: absolute;
        left: 64px;
        width: 1px;

        top: 30px;
        bottom: 30px;
      }

      li:last-child.has-children {
        & > .condition-item-ul::after {
          content: "";
          display: block;
          background: #f9f9f9;
          position: absolute;
          left: 18px;
          width: 1px;
          top: -18px;
          bottom: 0px;
        }
      }
      .icon-box-active {
        border: 1px solid currentColor;
      }

      .icon-box:hover {
        .close {
          display: block;
        }
      }
    }
    &.has-children {
      &::before,
      &::after {
        content: "";
        display: block;
        background: #dedede;
        position: absolute;
      }
      &::before {
        width: 28px;
        height: 1px;
        left: 36px;
        top: 18px;
      }
      &::after {
        width: 1px;
        height: 36px;
        left: 64px;
        top: 18px;
      }
    }
  }
  .close {
    position: absolute;
    right: -4px;
    top: -5px;
    background: #fff;
    color: #7d7d7d;
    border-radius: 2px;
    display: none;
    cursor: pointer;
  }
  .flex-form {
    display: inline-flex;
    vertical-align: text-bottom;
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
  .cond-data {
    background: #fff;
    padding: 8px;
    display: inline-flex;
    vertical-align: text-bottom;
    border-radius: $radiusS;
    cursor: pointer;
    span {
      color: #409eff;
    }
  }
  .icon-box-active.event-icon {
    border: 1px solid #cb7530;
  }
}

.condition-form-wrapper {
  min-width: 240px;
  vertical-align: bottom;
}

:deep(.edit-condition-wrapper) {
  .label-text {
    display: inline-block;
    width: 100px;
    text-align: center;
  }
  .form-item {
    margin: 10px 0;
    width: calc(100% - 200px);
  }
}
.filter-condition-wrapper {
  display: inline-block;
  vertical-align: bottom;
  margin-top: -2px;
  :deep(.custom-select) {
    display: inline-block;
    width: 200px;
    background: #fff;
  }
  .el-button {
    margin-left: 10px;
    position: relative;
    top: -2px;
  }
}
.el-tag--small {
  display: inline-flex;
}
</style>
