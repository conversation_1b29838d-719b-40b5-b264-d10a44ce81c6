<template>
  <div class="childTable">
    <el-table :data="dynamicTableData" :border="parentBorder" ref="tableRef">
      <el-table-column label="名称" prop="name"> </el-table-column>
      <el-table-column label="描述" prop="remark"> </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <xel-handle-btns :btnList="getBtnList(scope)" :scope="scope"></xel-handle-btns>
        </template>
      </el-table-column>
    </el-table>

    <el-button class="mt20 mb20 fillContent" type="button" @click="addRule">
      <el-icon :size="12" class="mr20"> <plus /> </el-icon>
      {{ logFilter ? "新增过滤规则" : "新增填充规则" }}
    </el-button>
  </div>

  <!-- <div class="pull-right mt20 mb20" v-if="dynamicTableData.length > 0">
    <el-button type="primary" @click="submit" :loading="loading"> 确定 </el-button>
    <el-button type="buttom" @click="cancelTable"> 取消 </el-button>
  </div> -->
  <xel-dialog :title="dialogTitle" ref="dialogRef" @submit="submitForm" @close="closeForm" size="large">
    <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini" label-position="left">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      <section>
        <p class="title-bottom-line tit">{{ logFilter ? "过滤条件" : "填充条件" }}</p>
        <condition-tag-item ref="fillterRef" :list="filterConditionList || []"></condition-tag-item>
      </section>

      <section v-if="!logFilter">
        <p class="title-bottom-line tit mt20">填充值</p>
        <static-field-action :staticType="false" ref="staticRef" :table-data="filterFieldList || []"></static-field-action>
      </section>
    </el-form>
  </xel-dialog>
</template>
<script setup>
import conditionTagItem from "./conditionTagItem.vue";
import staticFieldAction from "./staticFieldAction.vue";
import { ref, reactive, toRefs, nextTick, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { getLogFieldList } from "@/api/sime/config/field.js";
import handlerConData from "../../../utils/handlerConData";

const route = useRoute();
let emits = defineEmits(["addStaticData"]);
let props = defineProps({
  tableData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  detail: {
    type: Boolean,
    default: false,
  },
  sort: {
    type: Number,
    default: null,
  },
  expand: {
    type: Boolean,
    default: true,
  },
  logFilter: {
    type: Boolean,
    default: false,
  },
});
let state = reactive({
  dynamicTableData: [],
  loading: false,
  editStatus: false,
  editIndex: null,
  formData: {
    name: "",
    remark: "",
  },
  filterConditionList: [], //条件列表
  filterFieldList: [], //填充值（动态填充）
  typeList: [],
});
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "名称",
    required: true,
  },
  {
    formType: "input",
    prop: "remark",
    label: "描述",
    type: "textarea",
  },
]);
let { dynamicTableData, loading, formData, filterConditionList, filterFieldList } = toRefs(state);
let ruleFormRef = ref();
let dialogRef = ref();
let dialogTitle = ref("新增动态填充规则");
let staticRef = ref();
let fillterRef = ref(); //填充条件
let isFinish = ref(true); //判断 是否未保存
getLogFieldListFn();
//  获取表格字段名
function getLogFieldListFn() {
  getLogFieldList().then((res) => {
    state.allFileOptions = res.data.rows;
    state.typeList = res.data.rows.map((item) => {
      return {
        ...item,
        alias: item.fieldText,
      };
    });
  });
}
// 新增
function addRule() {
  isFinish.value = false;
  dialogRef.value.open();
  state.editStatus = false;
  state.editIndex = null;
  state.filterConditionList = [];
  state.filterFieldList = [];
  dialogTitle.value = props.logFilter ? "新增过滤规则" : "新增动态填充规则";
}

// 表格按钮
function getBtnList() {
  return [
    {
      icon: "edit",
      title: "修改",
      onClick(scope) {
        isFinish.value = false;
        state.filterConditionList =
          scope.row.filterConditionList &&
          scope.row.filterConditionList.map((item) => {
            let conditionData = handlerConData(item, state.typeList);
            return {
              notetype: "condition",
              icon: "icon-condition",
              label: "条件",
              color: "#E6A23C",
              bgColor: "#f0f1f4",
              editStatus: false,
              kong: false,
              data: conditionData,
            };
          });
        state.editStatus = true;
        state.editIndex = scope.$index;
        state.formData.name = scope.row.name;
        state.formData.remark = scope.row.remark;

        state.filterFieldList = scope.row.filterFieldList;

        dialogRef.value.open();
        dialogTitle.value = props.logFilter ? "修改过滤规则" : "修改动态填充规则";
      },
    },
    {
      icon: "delete",
      title: "删除",
      onClick(scope) {
        state.dynamicTableData.splice(scope.$index, 1);
      },
    },
  ];
}
// 新增弹框一条规则
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (state.editStatus) {
        state.dynamicTableData[state.editIndex] = state.formData;
        state.dynamicTableData[state.editIndex]["filterConditionList"] = fillterRef.value.list;
        state.dynamicTableData[state.editIndex]["filterFieldList"] =
          staticRef.value &&
          staticRef.value.filterFieldList.map((item) => {
            return {
              ...item,
              name: item.name.split("_")[1],
              nameText: item.name.split("_")[0],
            };
          });
      } else {
        state.dynamicTableData.push({
          ...state.formData,
          filterConditionList: fillterRef.value.list,
          filterFieldList: props.logFilter
            ? []
            : staticRef.value.filterFieldList.map((item) => {
                return {
                  ...item,
                  name: item.name.split("_")[1],
                  nameText: item.name.split("_")[0],
                };
              }),
        });
      }

      // ElMessage.success("操作成功");
      closeForm();
      nextTick(() => {
        submit();
      });
    }
  });
}

// 取消新增
function closeForm() {
  state.formData = {};
  state.filterConditionList = [];
  dialogRef.value.close();
}
watch(
  () => props.expand,
  (val) => {
    if (val) {
      state.dynamicTableData = props.tableData;
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

// 提交表格
function submit() {
  isFinish.value = true;
  state.loading = true;
  emits("addStaticData", {
    filterRuleList: state.dynamicTableData,
    sort: props.sort,
    isFinish: isFinish.value,
  });
  ElMessage.success("操作成功");
  state.loading = false;
}
// 取消
function cancelTable() {
  isFinish.value = true;
  state.dynamicTableData = props.tableData;
}
defineExpose({
  filterFieldList: computed(() => {
    return state.dynamicTableData || [];
  }),
});
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.staticForm .el-form-item--small) {
  margin-top: 18px;
}
.tit {
  font-size: 14px;
}
.childTable {
  width: calc(100% - 260px);
  margin: 0 auto;
}
.fillContent {
  // width: calc(100% - 260px);
  // margin: 0 auto;
}
</style>
