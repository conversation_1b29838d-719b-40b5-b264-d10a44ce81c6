<template>
  <!-- 状态监控 - 实时状态 -->
  <div>
    <el-row class="realTimeStateCom">
      <el-col :span="8">
        <p>
          监听器名称 <span class="titSpan">{{ route.params.name }}</span>
        </p>
      </el-col>
      <el-col :span="8">
        <p>
          监听器启动时间 <span class="titSpan">{{ timestamp || "未知" }}</span>
        </p>
      </el-col>

      <el-col :span="8" style="text-align: end">
        <!-- 新增 - 自动刷新 -->
        <TimerFormCom @searchFun="listenInfo" />
        <el-button round @click="listenInfo">刷新</el-button>
      </el-col>
    </el-row>

    <!-- 统计信息 -->
    <div class="cardDiyDiv">
      <CardDIy v-for="item in cardData" :key="item.title" :itemData="item" />
    </div>

    <el-row :gutter="20" class="realCardDIyDiv">
      <el-col :md="24" :lg="12">
        <RealCardDIy
          successName="转发成功数量"
          :successNum="getListen.transferDataCounttrue"
          errorName="转发失败数量"
          :errorNum="getListen.transferDataCountfalse"
        />
      </el-col>
      <el-col :md="24" :lg="12">
        <RealCardDIy
          successName="入库成功数量"
          :successNum="getListen.transferDataCountElasticsearchtrue"
          errorName="入库失败数量"
          :errorNum="getListen.transferDataCountElasticsearchfalse"
        />
      </el-col>
    </el-row>

    <!-- 设备日志接收量明细  -->
    <div class="tableDiv">
      <p class="titP">设备日志接收量明细</p>
      <xel-table ref="tableRef" :columns="columns" :data="tabData || []" :checkbox="false" :pagination="false" />
    </div>
  </div>
</template>

<script>
export default {
  name: "realTimeState",
};
</script>

<script setup>
import { getListenInfo } from "@/api/sime/config/logAcceptState";
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";

import CardDIy from "@/views/sime/siriusDis/components/cardDIy.vue";
import RealCardDIy from "./realCardDIy.vue";
import TimerFormCom from "./timerFormCom.vue";

let emits = defineEmits(["loadingFun"]);

/* 头部统计信息 */
const cardData = ref([
  {
    icon: "icon-yuming",
    title: "接收数据量",
    number: 0,
  },
  {
    icon: "icon-yuming",
    title: "处理队列",
    number: 0,
  },

  {
    icon: "icon-yuming",
    title: "转发队列",
    number: 0,
  },
]);

const router = useRouter();
const route = useRoute();
let timestamp = ref();
let getListen = ref({});
let tabData = ref([]);

function listenInfo() {
  emits("loadingFun", true);
  /* 新增 - url属性，防止出现问题 - 默认 receiver */
  getListenInfo({ id: route.params.id }, route.params.urlPath ? route.params.urlPath : "receiver")
    .then((res) => {
      tabData.value = res.data.ips;
      getListen.value = res.data;

      /* 赋值 */
      cardData.value[0].number = getListen.value.receiverDataSize;
      cardData.value[1].number = getListen.value.handlerDataSize;
      cardData.value[2].number = getListen.value.transferDataSize;

      if (getListen.value.serverStartTime) {
        let timestamp4 = new Date(getListen.value.serverStartTime);
        timestamp.value = timestamp4.toLocaleDateString().replace(/\//g, "-") + " " + timestamp4.toTimeString().substr(0, 8);
      }
    })
    .finally(() => {
      emits("loadingFun", false);
    });
}
listenInfo();

// 列表配置项
const columns = [
  {
    prop: "ip",
    label: "设备地址",
  },
  {
    prop: "lastTime",
    label: "最后活动时间",
  },
  {
    prop: "count",
    label: "日志总量",
  },
  {
    prop: "dayCount",
    label: "1天内总量",
  },
  {
    prop: "hourCount",
    label: "1小时内总量",
  },
  {
    prop: "minCount",
    label: "1分钟内总量",
  },
  {
    prop: "eps",
    label: "(eps)",
  },
];
</script>

<style scoped lang="scss">
$borderColor: #ebedf1;
.realTimeStateCom {
  color: #999999;
  border-bottom: 2px solid $borderColor;
  padding-top: 10px;
  padding-bottom: 10px;
  .titSpan {
    color: #848484;
    margin-left: 50px;
  }
}
/* cardDiyDiv */
.cardDiyDiv {
  margin: 15px 0;
  .cardDiv {
    margin-right: 15px;
  }
}

/* realCardDIyDiv */
.realCardDIyDiv {
  padding-right: 10px;
}

/* tableDiv */
.tableDiv {
  margin-top: 15px;
  .titP {
    font-size: 14px;
    font-weight: 400;
    color: #262f3d;
    padding-bottom: 18px;
    border-bottom: 2px solid $borderColor;
    margin-bottom: 18px;
  }
}
</style>
