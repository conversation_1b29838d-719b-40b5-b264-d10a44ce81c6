<template>
  <echart-line-base v-if="isShow" :options="options" width="100%" height="185px"></echart-line-base>
</template>

<script>
export default {
  name: "hisLineEchart",
};
</script>

<script setup>
import { onBeforeUnmount, onMounted, ref, watch } from "vue";
import echartLineBase from "starso/echartLineBase";

let props = defineProps({
  echartData: {
    type: Object,
    default() {
      return {};
    },
  },
});

/* 日志接收量趋势图 */
let isShow = ref(false);
let options = ref({});

let xData = ref([]);
let yData = ref([]);
let screenWid = ref(0);
const setOption = (x, y, type) => {
  /* 监测宽度变化 */
  let screenW = window.innerWidth || document.documentElement.clientWidth;
  if (screenWid.value === 0) {
    screenWid.value = screenW;
  } else {
    if (screenWid.value === screenW && type === "resize") {
      return false;
    } else {
      screenWid.value = screenW;
    }
  }
  isShow.value = false;
  options.value = {
    legend: {
      /*icon: 'rect',*/
      right: "0",
      top: "-5px",
      textStyle: {
        /*color: '#fff'*/
      },
      data: y.map((item) => item.name),
    },
    grid: {
      top: "15%",
      left: "1%",
      right: "1%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      /*data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']*/
      data: x,
      /*data: []*/
    },
    yAxis: { type: "value" },
    tooltip: {
      trigger: "axis",
      show: true,
    },
    series: y,
    /*[
      {
        name: "CPU",
        type: "line",
        showSymbol: false,
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        /!*data: [],*!/
      },
      {
        name: "CPU1",
        type: "line",
        showSymbol: false,
        data: [20, 32, 91, 34, 190, 330, 130],
        /!*data: [],*!/
      },
    ]*/
  };

  setTimeout(() => {
    isShow.value = true;
  }, 100);
};
watch(
  () => props.echartData,
  (val) => {
    xData.value = val.xAxis && val.xAxis.length > 0 ? val.xAxis : [];
    yData.value = [];
    val.series.forEach((item) => {
      yData.value.push({
        name: item.name,
        type: "line",
        showSymbol: false,
        data: item.data,
      });
    });
    setOption(xData.value, yData.value, "updata");
  }
);

const handleResize = () => {
  setOption(xData.value, yData.value, "resize");
};
onMounted(() => {
  window.addEventListener("resize", handleResize);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});
</script>
<style scoped></style>
