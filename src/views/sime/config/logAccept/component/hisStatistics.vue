<template>
  <!-- 状态监控 - 历史统计 -->
  <div>
    <el-row class="hisStatisticsCom">
      <el-col :span="8">
        <el-button round @click="setTime('near7day')" :type="formData.timeWindow === 'near7day' ? 'primary' : ''">最近7天</el-button>
        <el-button round @click="setTime('near24hour')" :type="formData.timeWindow === 'near24hour' ? 'primary' : ''">最近24小时</el-button>
        <el-button round @click="setTime('near1hour')" :type="formData.timeWindow === 'near1hour' ? 'primary' : ''">最近1小时</el-button>
      </el-col>
      <el-col :span="8">
        <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini" class="addInfo">
          <xel-form-item
            label="时间选择"
            type="datetimerange"
            form-type="daterange"
            value-format="YYYY-MM-DD HH:mm:ss"
            v-model:start="formData.startTime"
            v-model:end="formData.endTime"
            @change="timerangeFun"
          />
        </el-form>
      </el-col>

      <el-col :span="8" style="text-align: end">
        <el-button round @click="getList" type="primary">查询</el-button>
        <el-button round @click="resetData">重置</el-button>
      </el-col>
    </el-row>

    <!-- 日志接收量趋势图  -->
    <div class="tableDiv">
      <p class="titP">日志接收量趋势图</p>
      <HisLineEchart :echartData="getListen" />
    </div>
    <el-button class="but-right" @click="clickGoBack">返回</el-button>
  </div>
</template>

<script>
export default {
  name: "hisStatistics",
};
</script>

<script setup>
import { getStatisticLogNum } from "@/api/sime/config/logAcceptState";
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";

import HisLineEchart from "./hisLineEchart.vue";

const router = useRouter();
const route = useRoute();
let formData = ref({
  id: "",
  startTime: "",
  endTime: "",
  timeWindow: "",
});
let getListen = ref({});

/* 获取数据 */
let loading = ref(false);
function getList() {
  loading.value = true;
  formData.value.id = route.params.id;
  getStatisticLogNum(formData.value)
    .then((res) => {
      getListen.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}
/*getList();*/

/* 时间设置 */
const setTime = (type) => {
  if (type !== "") {
    formData.value.timeWindow = type;
    formData.value.startTime = "";
    formData.value.endTime = "";
  }
  getList();
};

/* 时间回调 */
const timerangeFun = (v) => {
  if (v) formData.value.timeWindow = "";
};
const resetData = () => {
  formData.value.startTime = "";
  formData.value.endTime = "";
  formData.value.timeWindow = "";
  getList();
};

/* 返回 */
function clickGoBack() {
  router.push({
    name: "LogAccept",
  });
}
defineExpose({
  getList,
  resetData,
});
</script>

<style scoped lang="scss">
$borderColor: #ebedf1;
.hisStatisticsCom {
  color: #999999;
  /*border-bottom: 2px solid $borderColor;*/
  padding-top: 20px;
  /*padding-bottom: 10px;*/
}

/* 日志接收量趋势图 */
.tableDiv {
  margin-top: 15px;
  height: 260px;
  .titP {
    font-size: 14px;
    font-weight: 400;
    color: #262f3d;
    padding-bottom: 18px;
    border-bottom: 2px solid $borderColor;
    margin-bottom: 18px;
  }
}
.but-right {
  float: right;
  margin: 10px 0;
}
</style>
