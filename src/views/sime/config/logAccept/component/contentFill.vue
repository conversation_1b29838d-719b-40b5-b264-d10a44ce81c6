<template>
  <el-table
    ref="fillTableRef"
    :show-header="false"
    :expand-row-keys="expandRowKeys"
    :data="fillTableData"
    style="width: 100%"
    @expand-change="getChangeExpand"
    :row-key="getRowKeys"
  >
    <el-table-column type="expand">
      <template #default="{ row }">
        <static-field-action
          v-if="row.handlerNameText == '【静态填充】'"
          :expand="row.expandChildTable"
          :table-data="row.filterFieldList || []"
          :sort="row.sort"
          @addStaticData="addTableData"
        >
        </static-field-action>
        <dynamic-field-action
          class="expandField"
          v-if="row.handlerNameText == '【动态填充】'"
          :expand="row.expandChildTable"
          :table-data="row.filterRuleList || []"
          :sort="row.sort"
          @addStaticData="addTableData"
        ></dynamic-field-action>
        <dynamic-field-action
          class="expandField"
          v-if="row.handlerNameText == '【日志过滤】'"
          :expand="row.expandChildTable"
          :table-data="row.filterRuleList || []"
          :sort="row.sort"
          :logFilter="true"
          @addStaticData="addTableData"
        ></dynamic-field-action>
      </template>
    </el-table-column>
    <el-table-column type="index" width="50"></el-table-column>
    <el-table-column prop="handlerNameText" label="类型名称"></el-table-column>
    <el-table-column label="操作" width="80">
      <template #default="scope">
        <xel-handle-btns :btnList="getBtnList(scope)" :scope="scope"></xel-handle-btns>
      </template>
    </el-table-column>
  </el-table>
  <div class="mt20 text-center">
    <el-dropdown @command="changeFill">
      <el-button type="button" class="btn">
        <el-icon :size="12" class="mr20"> <plus /> </el-icon> 添加填充类型<el-icon class="ml20"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in fillTypeData" :key="item.value" :command="item.label">
            {{ item.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
<script setup>
import { reactive, ref, toRefs, computed, watch } from "vue";
import { getDictsData } from "@/utils/getDicts";
import { batchDelete } from "@/utils/delete";
import { ElMessageBox, ElMessage } from "element-plus";
import staticFieldAction from "./staticFieldAction.vue";
import dynamicFieldAction from "./dynamicFieldAction.vue";

let props = defineProps({
  data: {
    type: Object,
    default: () => {
      return [];
    },
  },
});

let state = reactive({
  fillTypeData: [], //填充类型
  fillTableData: [],
  expandRowKeys: [],
});
let fillTableRef = ref();
let { fillTypeData, fillTableData, expandRowKeys } = toRefs(state);

// 外层表格按钮
function getBtnList() {
  return [
    {
      icon: "delete",
      title: "删除",
      itemWidth: "80px",
      hide: props.versionDetail,
      onClick(scope) {
        delFn(scope.row);
      },
    },
  ];
}

// 获取填充类型
getFillList();
function getFillList() {
  getDictsData("receiver_handler_name").then((res) => {
    state.fillTypeData = res;
  });
}

watch(
  () => props.data,
  (val) => {
    state.fillTableData = props.data.map((item) => {
      return {
        ...item,
        handlerNameText:
          item.handlerName == "ignoreAction" ? "【日志过滤】" : item.handlerName == "dynamicFieldAction" ? "【动态填充】" : "【静态填充】",
        isFinish: true,
      };
    });
  }
);

// 添加填充类型
function changeFill(val) {
  state.fillTableData.push({
    sort: state.fillTableData.length,
    handlerNameText: "【" + val + "】",
    handlerName: val == "静态填充" ? "staticFieldAction" : val == "动态填充" ? "dynamicFieldAction" : "ignoreAction",
    isFinish: true,
  });
}

// 删除填充类型
function delFn(rows) {
  batchDelete().then(() => {
    state.fillTableData.splice(
      state.fillTableData.findIndex((item) => item.handlerNameText == rows.handlerNameText),
      1
    );
  });
}

// 添加表格
function addTableData(data) {
  state.fillTableData.forEach((item) => {
    if (item.sort == data.sort) {
      if (item.handlerNameText == "【静态填充】") {
        item.filterFieldList = data.filterFieldList.map((item) => {
          return {
            ...item,
            name: item.name.split("_")[1],
            nameText: item.name.split("_")[0],
          };
        });
        item.isFinish = data.isFinish;
      } else if (item.handlerNameText == "【动态填充】" || item.handlerNameText == "【日志过滤】") {
        item.filterRuleList = data.filterRuleList;
        item.isFinish = data.isFinish;
      }
    }
  });
}

// 折叠

function getRowKeys(row) {
  return row.sort;
}

// function getChangeExpand(row, rows) {
// state.expandRowKeys = rows.map((item) => item.sort);
// state.fillTableData.forEach((item) => {
//   if (item.sort == row.sort) {
//     item.expandChildTable = !item.expandChildTable;
//   }
// });
// }
function getChangeExpand(row) {
  if (state.expandRowKeys[0] == row.sort) {
    state.expandRowKeys = [];
  } else {
    state.expandRowKeys = [];
    state.expandRowKeys.push(row.sort);
  }
}
defineExpose({
  filterList: computed(() => {
    return state.fillTableData;
  }),
});
</script>

<style lang="scss" scoped>
.btn {
  min-width: 300px;
}
.expandField {
  width: calc(100% - 260px);
  margin: 0 auto;
}
</style>
