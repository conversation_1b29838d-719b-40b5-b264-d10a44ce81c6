<template>
  <!-- 采集器管理 -->
  <el-card class="card-new-gray">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <!-- 新增 - 多采集器情况 -->
    <el-row :gutter="20" v-if="tabsList.length">
      <el-col :span="3" class="bg-p-border-new" style="text-align: end">
        <el-tabs v-model="activeName" tab-position="left" style="height: 100%">
          <el-tab-pane v-for="item in tabsList" :key="item.path" :label="item.name" :name="item.path" />
        </el-tabs>
      </el-col>
      <el-col :span="21">
        <LogAcceptItem :urlPath="activeName" v-if="activeName" />
      </el-col>
    </el-row>

    <p v-else class="text-center no-data">暂无数据</p>
  </el-card>
</template>
<script>
export default {
  name: "LogAccept",
};
</script>
<script setup>
import { getReceiverRoute } from "@/api/sime/config/logAcceptState";
import LogAcceptItem from "./logAcceptItem.vue";
import { onActivated, ref } from "vue";

const activeName = ref("");
let tabsList = ref([]);

onActivated(() => {
  getReceiverRouteFun();
});
/* 获取采集器 */
const getReceiverRouteFun = () => {
  getReceiverRoute().then((res) => {
    if (!activeName.value) {
      activeName.value = res.data[0].path;
    }
    tabsList.value = res.data;
  });
};
</script>
<style scoped lang="scss">
::v-deep .el-tabs--left .el-tabs__header.is-left {
  float: right;
}
</style>
