<template>
  <sime-layout
    :load-data="getIndexGroupTree"
    :defaultProps="defaultProps"
    :table_data="table_data"
    :node_item_data="add_item_data"
    @changeGroup="changeGroup"
    ref="simeRef"
    :moduleTyp="'indexsManage'"
  ></sime-layout>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import {
  getIndexGroupTree,
  addIndexGroup,
  editIndexGroup,
  detailIndexGroup,
  delIndexGroup,
  getIndexList,
  delIndexData,
  getTableDataSource,
  editTableDataSource,
  getSourceInfo,
  delSourceInfo,
} from "@/api/sime/config/log";
let simeRef = ref();
let state = reactive({
  groupData: {},
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {
    keys: "indexs",
    //搜索相关
    searchState: {
      data: {
        groupId: "",
        name: "",
        indexName: "",
        isAvailable: null,
      },
      menuData: [
        {
          lable: "可用状态:",
          prop: "isAvailable",
          // 字典关键字
          dictName: "config_logStoreDatasource_isAvailable",
          sime: true,
        },
      ],
      formList: [
        {
          formType: "input",
          prop: "name",
          label: "配置名称",
        },
        {
          formType: "input",
          prop: "indexName",
          label: "索引名称",
        },
      ],
    },
    columns: [
      {
        prop: "name",
        label: "配置名称",
      },
      {
        prop: "description",
        label: "配置描述",
      },

      {
        prop: "isAvailableText",
        label: "可用状态",
      },
      {
        prop: "indexName",
        label: "索引名称",
      },
      {
        label: "操作",
        fixed: "right",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "edit",
            title: "修改",
            hasPermi: "logStore:index:update",
            onClick(scope) {
              simeRef.value.tableListRef.editData(scope.row);
            },
          },
          {
            icon: "operation",
            title: "扩展字段配置",
            hasPermi: "logStore:index:add",
            onClick(scope) {
              simeRef.value.tableListRef.editExtendedField(scope.row);
            },
          },
          {
            icon: "delete",
            title: "删除",
            hasPermi: "logStore:index:delete",
            onClick(scope) {
              simeRef.value.tableListRef.delData(scope.row);
            },
          },
        ],
      },
    ],
    table_item_list: {
      specialTable: true,
      add_form_list: [],
      addSpecialName: "addEditIndexes",
      editSpecialName: "editIndexes",
      editFieldName: "extendedField",
      delItem: delIndexData,
    },
    getTableData: getIndexList,
  },
  add_item_data: {
    isCheckName: true,
    delport: delIndexGroup,
    editport: editIndexGroup,
    addport: addIndexGroup,
    detailport: detailIndexGroup,
    nodeeDialogTitle: "索引配置分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
    ],
  },
});
//获取当前选中的分组
function changeGroup(data) {
  state.groupData = data;
}

defineExpose({
  simeRef: computed(() => simeRef.value),
});
let { defaultProps, add_item_data, table_data } = toRefs(state);
</script>

<style lang="scss" scoped></style>
