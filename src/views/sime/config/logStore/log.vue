<template>
  <el-card class="tabs-tree-table-new">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="数据源管理" name="first">
        <data-source ref="firstRef" :key="activeName"></data-source>
      </el-tab-pane>
      <el-tab-pane label="索引配置" name="second">
        <indexes-manage ref="secondRef" :key="activeName"></indexes-manage>
      </el-tab-pane>
    </el-tabs>
    <xel-dialog @addItem="$emit('addNode')"> </xel-dialog>
  </el-card>
</template>
<script>
export default {
  name: "SimeLog",
};
</script>
<script setup>
import dataSource from "./dataSource.vue";
import { ref, reactive, toRefs, onMounted, watch, onActivated } from "vue";
import { getTreeData } from "@/api/sime/config/log";
import IndexesManage from "./indexesManage.vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
const route = useRoute();
let activeName = ref("first");
let secondRef = ref();
activeName.value = route.query.tabName ? route.query.tabName : "first";
function handleClick(val) {
  activeName.value = val.paneName;
  if (activeName.value == "second") {
    secondRef.value ? secondRef.value.simeRef.getData(route.query.groupId) : "";
  }
}
</script>

<style lang="scss" scoped></style>
