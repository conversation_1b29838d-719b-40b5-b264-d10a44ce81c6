<template>
  <!-- <el-card> -->
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button @click="newlyAdded" class="search-button" :disabled="state.disabled" v-if="tableAddBtn">
      <el-icon :size="12">
        <plus />
      </el-icon>
      新增
    </el-button>
  </common-search>
  <xel-table ref="tableRef" :columns="props.table_data.columns" :load-data="props.table_data.getTableData"> </xel-table>
  <!-- 弹窗内容 -->
  <xel-dialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleFormRef" label-width="140px" label-position="right" size="mini">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
    <template #otherButton>
      <el-button @click="testLink" :loading="testLoading">测试连接</el-button>
    </template>
  </xel-dialog>
  <!-- </el-card> -->
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, watch, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { batchDelete } from "@/utils/delete";
import { useRouter } from "vue-router";
import { download } from "@/plugins/request";
import firstToUpper from "@/utils/firstToUpper";
import hasPermi from "@/utils/hasPermi.js";
import { saveTagByTagName } from "../../../../../api/analyticalDisposal/runscript";
import { saveAddTerminal } from "../../../../../api/securityAssets/assetGroup";
import { useStore } from "vuex";
const store = useStore();
const emit = defineEmits(["changeShow"]);
const router = useRouter();
let testLoading = ref(false);
let ParentFormData = props.table_data.table_item_list["form_data"]; //初始状态
let props = defineProps({
  table_data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  groupId: {
    type: String,
    default: "",
  },
  // 模块
  moduleTyp: {
    type: String,
    default: "",
  },
});
let initValue = ref();
initValue.value = JSON.parse(JSON.stringify(props.table_data));
watch(
  () => props.groupId,
  (val) => {
    if (!val || val == "0") {
      state.disabled = true;
    } else {
      state.disabled = false;
    }
    search();
  }
);

let tableAddBtn = ref(false);
// 获取按钮权限
btnFn();
function btnFn() {
  // 日志存储-数据源管理
  if (props.moduleTyp == "dataManage") {
    tableAddBtn.value = hasPermi("logStore:datasource:add");
  }
  // 日志存储-索引管理
  if (props.moduleTyp == "indexsManage") {
    tableAddBtn.value = hasPermi("logStore:index:add");
  }

  //告警转发-数据源管理
  if (props.moduleTyp == "alarmDataManage") {
    tableAddBtn.value = hasPermi("alertRelay:datasource:add");
  }
  // 告警转发-数据表管理
  if (props.moduleTyp == "alarmList") {
    tableAddBtn.value = hasPermi("alertRelay:table:add");
  }
  // 告警转发-消息列表
  if (props.moduleTyp == "mqList") {
    tableAddBtn.value = hasPermi("alertRelay:mq:add");
  }
  if (props.moduleTyp == "syslog") {
    tableAddBtn.value = hasPermi("alertRelay:syslog:add");
  }
}
//搜索相关
let searchState = reactive({
  data: props.table_data.searchState.data,
  menuData: props.table_data.searchState.menuData,
  formList: props.table_data.searchState.formList,
});

let tableRef = ref();
function search(initPageNum = true) {
  searchState.data.groupId = props.groupId == 0 ? null : props.groupId;
  if (searchState.data.port < 0) {
    searchState.data.port = 0;
  }
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  Object.assign(searchState.data, initValue.value.searchState.data);
  searchState.data.groupId = props.groupId == 0 ? null : props.groupId;
  searchState.data.port = undefined;
  tableRef.value.reload(searchState.data);
}
//搜索结束

let state = reactive({
  disabled: true, //新增按钮是否可点击
  formData: props.table_data.table_item_list["form_data"], //新增编辑表单
  multipleSelection: [],
  formList: [],
});
let { formData, formList } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  state.formData = {};
  Object.assign(state.formData, initValue.value.table_item_list["form_data"]);
  emit("changeShow");
}

// 列表配置项
//删除，批量删除
function delFn(rows) {
  batchDelete().then(() => {
    props.table_data.table_item_list.delItem(rows.id).then(() => {
      ElMessage.success("删除成功");
      store.commit("closeTabById", rows.id);
      search(false);
      tableRef.value.table.clearSelection();
    });
  });
}
// 列表操作方法
// 新增按钮
function newlyAdded() {
  if (props.table_data.table_item_list.specialTable) {
    router.push({
      name: firstToUpper(props.table_data.table_item_list.addSpecialName),
      params: {
        id: "add",
      },
      query: {
        groupId: props.groupId,
      },
    }); //路由跳转
  } else {
    editId.value = "";
    dialogTitle.value = "添加" + props.table_data.table_item_list["title_text"];
    resetFormData();
    // 弹框内容
    state.formList = props.table_data.table_item_list["add_form_list"];
    state.formData.groupId = props.groupId;
    popupBox();
  }
}
let editId = ref("");
// 修改按钮
function modifyButton(data) {
  if (props.table_data.table_item_list.specialTable) {
    router.push({
      name: firstToUpper(props.table_data.table_item_list.editSpecialName),
      params: {
        id: data.id,
      },
      query: {
        groupId: data.groupId,
      },
    }); //路由跳转
  } else {
    editId.value = data.id;
    dialogTitle.value = "修改" + props.table_data.table_item_list["title_text"];
    props.table_data.table_item_list.getDetail(data.id).then(({ data }) => {
      state.formData = data;
      // 弹框内容
      state.formList = props.table_data.table_item_list["add_form_list"];
      popupBox();
    });
  }
}

/* 扩展字段配置 - 相关 */
const editExtendedField = (data) => {
  router.push({
    name: firstToUpper(props.table_data.table_item_list.editFieldName),
    params: {
      id: data.id,
    },
  });
};

// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}

// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (props.table_data.pageType == "logData" && !state.formData.isAuthorization) {
        state.formData.username = "";
        state.formData.password = "";
      }
      loading();
      let addFn = editId.value ? props.table_data.table_item_list.updateItem : props.table_data.table_item_list.addItem;
      addFn(state.formData)
        .then((res) => {
          search(false);
          close();
          ElMessage.success("操作成功");
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
  ruleFormRef.value.resetFields();
}
// 测试连接
function testLink() {
  testLoading.value = true;
  let port = props.table_data.table_item_list.testItem;
  state.formData.addTest = editId.value ? false : true;
  port(state.formData)
    .then((res) => {
      ElMessage.success(res.msg);
      search(false);
      close();
      testLoading.value = false;
    })
    .catch(() => {
      close(false);
      testLoading.value = false;
    });
}
//父组件可以调用的方法
defineExpose({
  editExtendedField: editExtendedField,
  editData: modifyButton,
  delData: delFn,
  getList: reset,
});
</script>
<style scoped lang="scss"></style>
