<template>
  <el-card class="bg-p-border-new">
    <div class="top">
      <h3 class="conH3Tit">{{ state.pageTit }}</h3>

      <backbutton text="索引列表" :name="{ name: 'SimeL<PERSON>', tabName: 'second' }"></backbutton>
    </div>
    <el-form class="flex-form" :model="formData" ref="ruleFormRef" label-width="120px" size="mini" :rules="rules">
      <xel-form-item item-width="30%" class="item-space" v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item">
      </xel-form-item>
      <el-form-item label="索引名称:" class="item-space" style="width: 30%" prop="indexName">
        <el-autocomplete
          :title="formData.indexName"
          class="inline-input"
          v-model="formData.indexName"
          :fetch-suggestions="querySearch"
          placeholder="请输入索引名称"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label-width="0" label="" class="item-space timeError" prop="deleteCycleNumber">
        <div class="delInfo">
          <el-checkbox
            v-model="formData.isRegularDelete"
            label="定时删除索引"
            name="type"
            :true-label="1"
            :false-label="0"
            @change="changeCheckbox('1', formData.isRegularDelete)"
          ></el-checkbox>
          <el-input-number
            controls-position="right"
            placeholder="请输入保留天数"
            v-model="formData.deleteCycleNumber"
            :min="1"
            :max="9999"
            :disabled="!formData.isRegularDelete"
          />
          <span>天</span>
        </div>
      </el-form-item>
      <el-form-item label-width="0" label="" class="item-space timeError" prop="closeCycleNumber">
        <div class="delInfo">
          <el-checkbox
            v-model="formData.isRegularClose"
            label="定时关闭索引"
            name="type"
            :true-label="1"
            :false-label="0"
            @change="changeCheckbox('2', formData.isRegularClose)"
          ></el-checkbox>

          <el-input-number
            controls-position="right"
            placeholder="请输入保留天数"
            v-model="formData.closeCycleNumber"
            :min="1"
            :max="9999"
            :disabled="!formData.isRegularClose"
          />
          <span>天</span>
        </div>
      </el-form-item>
      <el-form-item ref="searchRef" label-width="0" style="width: 100%">
        <el-button :loading="loading" type="primary" @click="saveForm" style="float: right">编辑字段</el-button>
      </el-form-item>
    </el-form>
    <xel-table
      height="400"
      ref="tableRef"
      :columns="columns"
      :defaultParams="{ ...state.formData }"
      :load-data="null"
      :data="remainData"
      :pagination="false"
      rowKey="field"
    >
      <!-- 字段别名 -->
      <template #alias="scope"> <el-input v-model="scope.row.alias"></el-input></template>
      <!-- 是否展示 -->
      <template #isShow="scope">
        <el-switch v-model="scope.row.isShow" :active-value="showData.active" :inactive-value="showData.inactive"> </el-switch>
      </template>
      <!-- 字典 -->
      <template #dictionaryType="scope">
        <el-select :placeholder="'请选择字典'" style="width: 100%" v-model="scope.row.dictionaryType" clearable filterable>
          <el-option v-for="item in options" :key="item.dictType" :label="item.dictName" :value="item.dictType"></el-option>
        </el-select>
      </template>
      <!-- 时间戳 -->
      <template #isTimeStamp="scope">
        <el-switch
          v-if="scope.row.field.indexOf('time') != -1"
          v-model="scope.row.isTimeStamp"
          :active-value="stampData.active"
          :inactive-value="stampData.inactive"
          @change="changeTimes(scope.row)"
        >
        </el-switch>
      </template>
      <!-- 统计字段-->
      <template #isAggs="scope">
        <el-switch v-model="scope.row.isAggs" :active-value="aggsData.active" :inactive-value="aggsData.inactive" @change="changeAggs(scope.row)">
        </el-switch>
      </template>
    </xel-table>
    <xel-table height="200" ref="tableRef2" :columns="columns2" :load-data="null" :data="hasDelData" :pagination="false">
      <!-- 是否展示 -->
      <template #isShow="scope">
        <el-switch v-model="scope.row.isShow" :active-value="showData.active" :inactive-value="showData.inactive" disabled> </el-switch>
      </template>
      <!-- 字典 -->
      <template #dictionaryType="scope">
        <el-select :placeholder="'请选择字典'" style="width: 100%" v-model="scope.row.dictionaryType" disabled clearable>
          <el-option v-for="item in options" :key="item.dictType" :label="item.dictName" :value="item.dictType"></el-option>
        </el-select>
      </template>
      <!-- 时间戳 -->
      <template #isTimeStamp="scope">
        <el-switch
          v-if="scope.row.field.indexOf('time') != -1"
          v-model="scope.row.isTimeStamp"
          :active-value="stampData.active"
          :inactive-value="stampData.inactive"
          @change="changeTimes(scope.row)"
          disabled
        >
        </el-switch>
      </template>
      <!-- 统计字段-->
      <template #isAggs="scope">
        <el-switch
          v-model="scope.row.isAggs"
          :active-value="aggsData.active"
          :inactive-value="aggsData.inactive"
          disabled
          @change="changeAggs(scope.row)"
        >
        </el-switch>
      </template>
    </xel-table>
    <div style="text-align: right"><el-button type="primary" @click="submit" :loading="saveLoading">确定</el-button></div>
  </el-card>
</template>
<script>
export default {
  name: "AddEditIndexes",
};
</script>
<script setup>
import {
  getIndexStoreTree as getTree,
  getIndexData as getTableData,
  getIndexLoadFields,
  addIndexData as addItem,
  updateIndexData as updateItem,
  getDicts,
  existsIndex,
} from "@/api/sime/config/log.js";
import { ref, reactive, toRefs, nextTick } from "vue";
import { batchDelete } from "@/utils/delete";
import { getDictsData } from "@/utils/getDicts";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { useStore } from "vuex";
// 验证
import { vxRule } from "@/xelComponents/utils/formValidator";
import sourceListVue from "../../../../operationParam/dataSource/sourceList.vue";
const store = useStore();
const route = useRoute();
const router = useRouter();
let tableRef = ref();
let loading = ref(false);
let saveLoading = ref(false);
let state = reactive({
  formData: {
    id: route.params.id,
    groupId: route.query.groupId,
    name: "",
    description: "",
    isAvailable: null,
    datasourceId: null,
    indexName: "",
    deleteCycleNumber: undefined,
    isRegularDelete: null,
    closeCycleNumber: undefined,
    isRegularClose: null,
  }, //新增编辑表单
  pageTit: "",
  showData: {
    active: 1,
    inactive: 0,
  }, //是否展示
  stampData: {
    active: 1,
    inactive: 0,
  }, //是否时间戳
  aggsData: {
    active: 1,
    inactive: 0,
  }, //是否统计字段
  remainData: [],
  hasDelData: [], //可恢复数据，已删除
  options: [], //字典类型
});
let { formData, showData, stampData, options, aggsData, remainData, hasDelData, option } = toRefs(state);
const validateDelete = (rule, value, callback) => {
  if (value && vxRule(value)) {
    var regex = new RegExp(/^[1-9]\d*$/);
    if (!regex.test(value)) {
      callback(new Error("只能输入正整数"));
    } else {
      callback();
    }
  }
  callback();
};
const rules = reactive({
  deleteCycleNumber: [{ validator: validateDelete, trigger: "change" }],
  closeCycleNumber: [{ validator: validateDelete, trigger: "change" }],
  indexName: [
    {
      required: true,
      message: "请输入索引名称",
      trigger: "blur",
    },
  ],
});
//重置新增编辑表单
function resetFormData() {
  state.formData = {
    id: route.params.id,
    groupId: route.query.groupId,
    name: "",
    description: "",
    isAvailable: null,
    datasourceId: null,
    indexName: "",
    deleteCycleNumber: undefined,
    isRegularDelete: null,
    closeCycleNumber: undefined,
    isRegularClose: null,
  };
}

search(route.params.id);
let editId = ref("");
// 如果是点击编辑进来，先获取数据
function search(id) {
  if (id != "add") {
    state.pageTit = "修改索引配置";
    getTableData(id).then((res) => {
      state.formData = {
        id: route.params.id,
        groupId: route.query.groupId,
        name: res.data.name,
        description: res.data.description,
        isAvailable: res.data.isAvailable,
        datasourceId: res.data.datasourceId,
        indexName: res.data.indexName,
        deleteCycleNumber: res.data.deleteCycleNumber == null ? undefined : res.data.deleteCycleNumber,
        isRegularDelete: res.data.isRegularDelete,
        closeCycleNumber: res.data.closeCycleNumber == null ? undefined : res.data.closeCycleNumber,
        isRegularClose: res.data.isRegularClose,
      };
      res.data.fields.forEach((item) => {
        item.stampData = "isTimeStamp" in item ? item.isTimeStamp : state.stampData.inactive;
        item.isAggs = "isAggs" in item ? item.isAggs : state.aggsData.inactive;
        item.isShow = "isShow" in item ? item.isShow : state.showData.inactive;
      });
      state.remainData = res.data.fields;
      state.hasDelData = [];
      // 获取索引名称
      getIndexName(res.data.datasourceId);
    });
  } else {
    state.pageTit = "新增索引配置";

    getDictsFn("4", "config_Index_defaultIndexName");
  }
}
//定时处理
function changeCheckbox(type, val) {
  if (type == "1" && val && route.params.id == "add") {
    getDictsFn("5", "config_logStoreIndex_defaultRegularNumber");
  } else if (type == "1" && !val) {
    state.formData.deleteCycleNumber = undefined;
  }
  if (type == "2" && val && route.params.id == "add") {
    getDictsFn("6", "config_logStoreIndex_defaultRegularNumber");
  } else if (type == "2" && !val) {
    state.formData.closeCycleNumber = undefined;
  }
}
// 拉取数据
/* 修改 - 增加确认框类型 type */
function pullData(type) {
  loading.value = true;
  let params = {
    datasourceId: state.formData.datasourceId,
    indexName: state.formData.indexName,
  };
  getIndexLoadFields(params)
    .then((res) => {
      res.data.forEach((item) => {
        item.stampData = "isTimeStamp" in item ? item.isTimeStamp : state.stampData.inactive;
        item.isAggs = "isAggs" in item ? item.isAggs : state.aggsData.inactive;
        item.isShow = "isShow" in item ? item.isShow : state.showData.inactive;
      });
      /*
       * 根据确认框类型 决定表格数据
       * 是：进行覆盖 - 原处理逻辑
       * 否：进行二次遍历，保留当前已改
       * */
      if (type) {
        state.remainData = res.data;
        state.hasDelData = [];
        ElMessage.success("操作成功");
      } else {
        forData(res.data);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

/* 新增 - 数据遍历处理 */
const forData = (data) => {
  let dataList = [];
  data.forEach((item) => {
    let oldData = state.remainData.find((o) => o.field === item.field);
    if (oldData) {
      dataList.push(oldData);
    } else {
      dataList.push(item);
    }
  });
  state.remainData = dataList;
  state.hasDelData = [];
  ElMessage.success("操作成功");
};

// 改变时间戳
function changeTimes(rows, index) {
  // 已经设置为true的两个
  let checkTimes = [];
  let allTable = tableRef.value.getDataList();
  checkTimes = allTable.filter((item, index) => {
    return !!item.isTimeStamp;
  });
  // 将当前的设置为true，之前的设置为false
  checkTimes.forEach((cv, ci) => {
    if (cv.field != rows.field) {
      cv.isTimeStamp = 0;
    }
  });
}
function changeAggs(rows, index) {
  // 已经设置为true的两个
  let checkAggs = [];
  let allTable = tableRef.value.getDataList();
  checkAggs = allTable.filter((item, index) => {
    return !!item.isAggs;
  });
  // 将当前的设置为true，之前的设置为false
  checkAggs.forEach((cv, ci) => {
    if (cv.field != rows.field) {
      cv.isAggs = 0;
    }
  });
}
// 列表配置项
const columns = [
  {
    prop: "field",
    label: "索引字段",
    filterable: true,
  },
  {
    prop: "alias",
    label: "字段别名",
    slotName: "alias",
  },
  {
    prop: "isShow",
    label: "默认展示",
    slotName: "isShow",
  },
  {
    prop: "dictionaryType",
    label: "字典",
    slotName: "dictionaryType",
  },
  {
    prop: "isTimeStamp",
    label: "时间戳字段",
    slotName: "isTimeStamp",
  },
  {
    prop: "isAggs",
    label: "统计字段",
    slotName: "isAggs",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
];
// 已删除表格配置项
const columns2 = [
  {
    prop: "field",
    label: "索引字段",
  },
  {
    prop: "alias",
    label: "字段别名",
  },
  {
    prop: "isShow",
    label: "默认展示",
    slotName: "isShow",
  },
  {
    prop: "dictionaryType",
    label: "字典",
    slotName: "dictionaryType",
  },
  {
    prop: "isTimeStamp",
    label: "时间戳字段",
    slotName: "isTimeStamp",
  },
  {
    prop: "isAggs",
    label: "统计字段",
    slotName: "isAggs",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "RefreshLeft",
        title: "恢复",
        onClick(scope) {
          recoverFn(scope.row);
        },
      },
    ],
  },
];

//删除，批量删除
function delFn(rows) {
  // 删除时自动改为关闭
  rows.isTimeStamp = 0;
  // 删除时自动改为关闭
  rows.isAggs = 0;
  state.hasDelData.push(rows);
  let index = state.remainData.indexOf(rows);
  state.remainData.splice(index, 1);
}
// 恢复表格
function recoverFn(rows) {
  state.remainData.push(rows);
  let index = state.hasDelData.indexOf(rows);
  state.hasDelData.splice(index, 1);
}
let ruleFormRef = ref();
let indexsNameArr = ref([]);
// 弹框内容
// 数据源下拉数据
let sourceList = ref([]);
// 获取数据源下拉数据
getTree().then((res) => {
  sourceList.value = res.data;
});
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "配置名称",
    required: true,
  },
  {
    formType: "input",
    prop: "description",
    label: "配置描述",
  },
  {
    formType: "radio",
    prop: "isAvailable",
    label: "可用状态",
    // 字典关键字
    dictName: "config_logStoreDatasource_isAvailable",
    isNumber: true,
    sime: true,
    required: true,
  },
  {
    formType: "tree",
    prop: "datasourceId",
    label: "数据源",
    required: true,
    multiple: false,
    treeOptions: {
      loadData: getTree, //接口名称
      params: {},
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
    onNodeClick: (val) => {
      if (val.isDataNode) {
        getIndexName(val.id);
      }
    },
  },
  // {
  //   formType: "input",
  //   prop: "indexName",
  //   label: "索引名称",
  //   required: true,
  //   options: [],
  // },
]);
// 接口获取数据
function getIndexName(id) {
  existsIndex({ datasourceId: id }).then((res) => {
    indexsNameArr.value = res.data.map((item) => {
      return {
        value: item,
        label: item,
      };
    });
  });
}
// 索引名称
function querySearch(queryString, cb) {
  let results = [];
  if (queryString) {
    results = indexsNameArr.value ? indexsNameArr.value.filter(createFilter(queryString)) : [];
  } else {
    results = indexsNameArr.value;
  }
  // const results = queryString ? indexsNameArr.value.filter(createFilter(queryString)) : indexsNameArr.value;
  // 返回建议列表的数据
  cb(results);
}
function createFilter(queryString) {
  return (restaurant) => {
    return restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
  };
}
// 数据源停用后，此数据源不存在时
function isExistSource() {
  if (sourceList.value.length > 0) {
    var result = getDataByIds(sourceList.value, state.formData.datasourceId);
    return result;
  } else {
    setTimeout(() => {
      checkSource();
    }, 200);
    return [{}];
  }
}
//根据id筛选数组
function getDataByIds(arr, ids = []) {
  if (ids == "") {
    ids = [];
  }
  let list = arr.filter((item) => ids.includes(item["id"]));
  for (let item of arr) {
    if (item.children && item.children.length > 0) {
      list = list.concat(getDataByIds(item.children, ids));
    }
  }
  return list;
}
// 弹框确定按钮
// 编辑字段
function saveForm() {
  /* 修改 - 增加确认提示框 */
  ElMessageBox.confirm("是否覆盖已有的字段？", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    showClose: false,
    closeOnClickModal: false,
    closeOnPressEscape: false,
    type: "warning",
  })
    .then(() => {
      saveFormFun(true);
    })
    .catch(() => {
      saveFormFun(false);
    });
}
function checkSource() {
  if (state.formData.datasourceId != "" && route.params.id != "add") {
    var results = isExistSource();
    if (!results || !results.length) {
      state.formData.datasourceId = "";
      ElMessage({
        type: "warning",
        message: "请选择数据源",
      });
    }
  }
}
/* 根据提示 - 分割方法 */
const saveFormFun = (type) => {
  loading.value = false;
  checkSource();
  let validateList = [];
  ruleFormRef.value.validateField(["datasourceId", "indexName"], (valid) => {
    validateList.push(valid);
  });

  if (validateList.every((item) => item === "")) {
    loading.value = true;
    pullData(type);
  }
};

// 提交全部
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      let port = route.params.id == "add" ? addItem : updateItem;
      if (route.params.id == "add") {
        delete state.formData.id;
      }
      let params = {
        ...state.formData,
        fields: state.remainData,
      };
      port(params)
        .then((res) => {
          ElMessage.success("操作成功");
          resetFormData();
          search(route.params.id);
          saveLoading.value = false;
          store.commit("closeCurrentTab");
          router.push({
            name: "SimeLog",
            query: {
              tabName: "second",
              groupId: route.query.groupId,
            },
          }); //路由跳转
        })
        .catch(() => {
          saveLoading.value = false;
        });
    } else {
      return false;
    }
  });
}
// 获取字典项
// 是否展示
function sortFn(source) {
  // 排序
  const sortArr = ["1", "0"];
  const result = source.sort((item1, item2) => {
    const index1 = sortArr.findIndex((sortItem) => {
      return item1.value === sortItem;
    });
    const index2 = sortArr.findIndex((sortItem) => {
      return item2.value === sortItem;
    });
    if (index1 !== -1 && index2 !== -1) {
      if (index1 > index2) {
        return 1;
      } else if (index1 < index2) {
        return -1;
      }
    } else {
      if (index1 > index2) {
        return -1;
      } else if (index1 < index2) {
        return 1;
      }
    }
  });
  return result;
}

getDictsFn("1", "config_indexField_isShow");
// 是否时间戳
getDictsFn("2", "config_indexField_isTimeStamp");
// 统计字段
getDictsFn("3", "config_indexField_isAggs");
// 索引名称 （新增是通过字典项获取 ，默认填充）
getDictsFn("4", "config_Index_defaultIndexName");
//
function getDictsFn(type, dictName) {
  getDictsData(dictName).then((res) => {
    if (type == "1") {
      let result = sortFn(res);
      state.showData = {
        active: Number(result[0].value),
        inactive: Number(result[1].value),
      };
    }
    if (type == "2") {
      let result = sortFn(res);
      state.stampData = {
        active: Number(result[0].value),
        inactive: Number(result[1].value),
      };
    }
    if (type == "3") {
      let result = sortFn(res);
      state.aggsData = {
        active: Number(result[0].value),
        inactive: Number(result[1].value),
      };
    }
    if (type == "4" && route.params.id == "add") {
      state.formData.indexName = res[0].value;
    }
    // 定时删除索引
    if (type == "5" && route.params.id == "add") {
      state.formData.deleteCycleNumber = res.filter((item) => item.value == "delete")[0].label;
    }
    // 定时关闭索引
    if (type == "6" && route.params.id == "add") {
      state.formData.closeCycleNumber = res.filter((item) => item.value == "close")[0].label;
    }
  });
}
getDataDicts();
function getDataDicts() {
  getDicts()
    .then((res) => {
      state.options = res.data;
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped>
.top {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
.tit {
  font-size: 16px;
  color: rgba(40, 51, 79, 1);
  line-height: 32px;
}
.item-space {
  padding-bottom: 3px;
  margin-right: 3%;
  border-bottom: 1px solid rgba(235, 237, 241, 1);
}
.delInfo {
  display: flex;
  width: 100%;
  .el-input {
    margin: 0 20px;
  }
}
.flex-form {
  :deep .el-input__inner {
    border: none;
    // padding: 0;
  }
  :deep .custom-select {
    border: none;
  }
  :deep .el-form-item__error {
    padding-top: 6px;
  }
}
:deep .inline-input {
  width: 100%;
  .el-input__inner {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
:deep(.el-input-number.is-controls-right) {
  .el-input__inner {
    text-align: left;
  }
}
:deep .el-input-number--mini {
  margin-left: 16px;
  width: 100%;
}
.timeError {
  width: 30%;
  :deep .el-form-item__error {
    margin-left: 140px;
  }
}
</style>
