<template>
  <!-- 日志存储 - 扩展字段配置 -->
  <el-card class="bg-p-border-new">
    <h3 class="conH3Tit mb20">{{ "" || ($route.meta && $route.meta.title) }}</h3>

    <el-form ref="ruleFormRef" :model="ruleForm">
      <div :style="tableHeight">
        <el-table :data="ruleForm.tableData" ref="tableRef" height="100%" v-loading="loading">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="字段名" prop="field">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.field'" :rules="rules.field">
                <el-input v-model="ruleForm.tableData[scope.$index].field" maxlength="20" placeholder="请输入字段名" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="字段别名" prop="alias">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.alias'" :rules="rules.alias">
                <el-input v-model="scope.row.alias" placeholder="请输入字段别名" />
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 默认显示 -->
          <el-table-column label="默认显示" prop="isShow">
            <template #default="scope">
              <el-switch v-model="scope.row.isShow" :active-value="showData.active" :inactive-value="showData.inactive"></el-switch>
            </template>
          </el-table-column>

          <!-- 默认开启 -->
          <el-table-column label="默认开启" prop="status">
            <template #default="scope">
              <el-switch v-model="scope.row.status" :active-value="showData.active" :inactive-value="showData.inactive"> </el-switch>
            </template>
          </el-table-column>

          <el-table-column label="源字段" prop="srcField">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.srcField'" :rules="rules.srcField">
                <el-select :placeholder="'请选择源字段'" style="width: 100%" v-model="scope.row.srcField" clearable filterable>
                  <el-option v-for="item in srcFieldList" :key="item.field" :label="item.alias" :value="item.field" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="实现类" prop="implClass">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.implClass'" :rules="rules.implClass">
                <el-select :placeholder="'请选择实现类'" style="width: 100%" v-model="scope.row.implClass" clearable filterable>
                  <el-option v-for="item in implClassList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="操作" prop="btns">
            <template #default="scope">
              <xel-handle-btns :btnList="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <FieldsTipsCom />

    <div class="mt20 text-center">
      <el-button @click="addField" style="min-width: 200px">
        <el-icon :size="12"> <plus /> </el-icon>添加字段</el-button
      >
    </div>

    <div class="text-right">
      <!-- <el-button @click="getTableData">取消</el-button> -->
      <el-button @click="clickGoBack">返回</el-button>
      <el-button type="primary" v-hasPermi="'config:logField:add'" @click="submit" :loading="saveLoading">确定</el-button>
    </div>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick, watch } from "vue";
import { getDictsData } from "@/utils/getDicts";
import tableMixin from "@/views/sime/components/mixin/tableMixin";
import { getFieldList, saveExtraField } from "@/api/sime/config/extendedField.js";
import { getFilesDicts } from "@/api/sime/config/alarm.js";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import FieldsTipsCom from "@/views/sime/components/fieldsTipsCom.vue";
const route = useRoute();
const router = useRouter();
const store = useStore();
let { tableHeight } = tableMixin(300);
let state = reactive({
  ruleForm: {
    tableData: [],
  },
  /* 源字段 */
  srcFieldList: [],
  /* 实现类 */
  implClassList: [],
  loading: false,
  showData: {
    active: 1,
    inactive: 0,
  },
});
let { srcFieldList, implClassList, ruleForm, loading, showData } = toRefs(state);

/* 获取源字段  */
getFilesDictsFun(route.params.id);
function getFilesDictsFun(id) {
  getFilesDicts(id).then((res) => {
    state.srcFieldList = res.data;
  });
}

/* 获取实现类 - 字典 */
getImportOptions();
function getImportOptions() {
  getDictsData("config_Index_extraFieldImplclass").then((res) => {
    state.implClassList = res;
  });
}

import { fieldsValid } from "@/utils/fieldsValid";
/* 格式校验 */
const rules = reactive({
  field: [
    {
      required: true,
      /*message: "请输入字段名",*/
      validator: fieldsValid,
      trigger: ["blur", "change"],
    },
  ],
  alias: [
    {
      required: true,
      message: "请输入字段别名",
      trigger: ["blur", "change"],
    },
  ],
  srcField: [
    {
      required: true,
      message: "请选择源字段",
      trigger: ["blur", "change"],
    },
  ],
  implClass: [
    {
      required: true,
      message: "请选择实现类",
      trigger: ["blur", "change"],
    },
  ],
});

/* 获取表格数据 */
getTableData();
function getTableData() {
  state.ruleForm.tableData = [];
  state.loading = true;
  getFieldList({ indexId: route.params.id })
    .then((res) => {
      state.ruleForm.tableData = res.data;
    })
    .finally(() => {
      state.loading = false;
    });
}
function clickGoBack() {
  router.go(-1);
}
/* 保存提交 */
let ruleFormRef = ref();
let saveLoading = ref(false);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      let params = state.ruleForm.tableData.map((tItem) => {
        return { ...tItem /*orderNum: index + 1,*/ };
      });
      let query = {
        id: route.params.id,
        extraFields: params,
      };
      saveExtraField(query)
        .then((res) => {
          ElMessage.success("操作成功");
          saveLoading.value = false;
          store.commit("closeCurrentTab");
          router.push({
            name: "SimeLog",
            query: {
              tabName: "second",
              groupId: route.query.groupId,
            },
          });
        })
        .finally(() => {
          saveLoading.value = false;
        });
    }
  });
}

/* 添加新字段行 */
let tableRef = ref();
function addField() {
  state.ruleForm.tableData.push({
    field: "",
    alias: "",
    isShow: 0,
    status: 0,
    srcField: "",
    implClass: "",
    /*orderNum: state.ruleForm.tableData.length,*/
  });
  nextTick(() => {
    document.getElementsByTagName("tr")[state.ruleForm.tableData.length - 1].scrollIntoView(true);
  });
}

/* 删除行 */
function getBtnList(row) {
  return [
    {
      hide: row.internal == 1,
      icon: "delete",
      title: "删除",
      onClick(scope) {
        let index = state.ruleForm.tableData.indexOf(scope.row);
        state.ruleForm.tableData.splice(index, 1);
      },
    },
  ];
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small .el-form-item__content) {
  /* line-height: 32px; */
  margin-top: 18px;
}
</style>
