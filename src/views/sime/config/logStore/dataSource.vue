<template>
  <sime-layout
    :load-data="getTreeData"
    :defaultProps="defaultProps"
    :table_data="table_data"
    @addNode="addNode"
    :node_item_data="add_item_data"
    ref="simeRef"
    @changeGroup="changeGroup"
    @changeShow="changeShow"
    :moduleTyp="'dataManage'"
  ></sime-layout>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, onActivated, watch, computed } from "vue";
import {
  getTreeData,
  addNodeGroup,
  editNodeGroup,
  deleteNodeGroup,
  detailNodeGroup,
  getDataSourceList,
  getTableDataSource,
  editTableDataSource,
  getSourceInfo,
  delSourceInfo,
  test,
} from "@/api/sime/config/log";

function changeShow() {
  table_data.value["table_item_list"]["add_form_list"][6].isShow = true;
  table_data.value["table_item_list"]["add_form_list"][7].isShow = true;
}

let simeRef = ref();
let state = reactive({
  groupData: {},
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {
    pageType: "logData",
    keys: "indexs",
    //搜索相关
    searchState: {
      data: {
        groupId: "",
        name: "",
        ip: "",
        port: undefined,
        username: "",
        isAvailable: null,
      },
      menuData: [
        {
          lable: "可用状态:",
          prop: "isAvailable",
          // 字典关键字
          dictName: "config_logStoreDatasource_isAvailable",
          sime: true,
        },
      ],
      formList: [
        {
          formType: "input",
          prop: "name",
          label: "名称",
        },
        {
          formType: "input",
          prop: "ip",
          label: "连接地址",
        },
        {
          formType: "number",
          prop: "port",
          label: "连接端口",
          // min: -999,
          vxRule: "Ints",
          max: 65535,
          precision: "0",
        },
        {
          formType: "input",
          prop: "username",
          label: "用户名",
        },
      ],
    },
    columns: [
      {
        prop: "name",
        label: "名称",
      },
      {
        prop: "description",
        label: "描述",
      },
      {
        prop: "ip",
        label: "连接地址",
      },
      {
        prop: "port",
        label: "连接端口",
      },
      {
        prop: "isAvailableText",
        label: "可用状态",
      },
      {
        label: "操作",
        fixed: "right",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "edit",
            title: "修改",
            hasPermi: "logStore:datasource:update",
            onClick(scope) {
              if (scope.row.isAuthorization === 1) {
                table_data.value["table_item_list"]["add_form_list"][6].isShow = true;
                table_data.value["table_item_list"]["add_form_list"][7].isShow = true;
              } else {
                table_data.value["table_item_list"]["add_form_list"][6].isShow = false;
                table_data.value["table_item_list"]["add_form_list"][7].isShow = false;
              }
              simeRef.value.tableListRef.editData(scope.row);
            },
          },
          {
            icon: "delete",
            title: "删除",
            hasPermi: "logStore:datasource:delete",
            onClick(scope) {
              simeRef.value.tableListRef.delData(scope.row);
            },
          },
        ],
      },
    ],
    table_item_list: {
      title_text: "数据源",
      delItem: delSourceInfo,
      updateItem: editTableDataSource,
      addItem: getTableDataSource,
      getDetail: getSourceInfo,
      testItem: test,
      form_data: {
        groupId: "",
        name: "",
        description: "",
        ip: "",
        port: undefined,
        isAuthorization: 1,
        username: "",
        password: "",
        isAvailable: 1,
      },
      add_form_list: [
        {
          formType: "input",
          type: "text",
          prop: "name",
          required: true,
          label: "名称",
        },
        {
          formType: "input",
          type: "textarea",
          prop: "description",
          label: "描述",
        },
        {
          formType: "input",
          type: "text",
          prop: "ip",
          label: "连接地址",
          required: true,
          // vxRule: "IP",
          placeholder: "请输入连接地址,格式：用英文逗号分隔",
        },
        {
          formType: "input",
          type: "text",
          prop: "port",
          label: "连接端口",
          required: true,
          // vxRule: "Ints",
          min: 0,
          max: 65535,
          precision: "0",
          placeholder: "请输入连接端口,格式：用英文逗号分隔",
        },
        {
          formType: "select",
          prop: "protocol",
          label: "协议",
          required: true,
          dictName: "config_es_protocol",
          sime: true,
          filterable: true,
        },
        {
          formType: "radio",
          prop: "isAuthorization",
          label: "身份认证",
          isNumber: true,
          required: true,
          // 字典关键字
          dictName: "config_logStoreDatasource_isAuthorization",
          sime: true,
          onChange(val) {
            if (val == 1) {
              table_data.value["table_item_list"]["add_form_list"][6].isShow = true;
              table_data.value["table_item_list"]["add_form_list"][7].isShow = true;
            } else {
              table_data.value["table_item_list"]["add_form_list"][6].isShow = false;
              table_data.value["table_item_list"]["add_form_list"][7].isShow = false;
            }
          },
        },
        {
          formType: "input",
          type: "text",
          prop: "username",
          label: "用户名",
          required: true,
        },
        {
          formType: "input",
          type: "password",
          prop: "password",
          label: "密码",
          required: true,
          autocomplete: "new-password",
        },
        {
          formType: "radio",
          prop: "isAvailable",
          label: "可用状态",
          isNumber: true,
          required: true,
          // 字典关键字
          dictName: "config_logStoreDatasource_isAvailable",
          sime: true,
        },
      ],
    },
    getTableData: getDataSourceList,
  },
  add_item_data: {
    isCheckName: true,
    delport: deleteNodeGroup,
    editport: editNodeGroup,
    addport: addNodeGroup,
    detailport: detailNodeGroup,
    nodeeDialogTitle: "数据源管理分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
    ],
  },
});
// 添加节点
function addNode(data) {}
//获取当前选中的分组
function changeGroup(data) {
  state.groupData = data;
}

let { defaultProps, add_item_data, table_data } = toRefs(state);
defineExpose({
  simeRef: computed(() => simeRef.value),
});
</script>

<style lang="scss" scoped></style>
