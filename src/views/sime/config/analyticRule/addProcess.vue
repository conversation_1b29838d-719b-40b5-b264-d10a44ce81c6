<!-- ***
1：state.tableProForm.tableData  用于保存用户添加的不同模式下的所有数据 格式如下
  [
    { configType: "1", fieldConfigList: [] },
    { configType: "2", fieldConfigList: [] },
    { configType: "3", fieldConfigList: [] },
  ]:

2：currentConfigType // 最终选中的处理模式 作用是父页面确定取哪个处理模式下的数据

3：currentTableData 根据当前模式，展示的相应的表格数据

4：submitTable:向父页面传达所有的数据 调用时间段（模式1，2是表格在添加完form表单后 3是正则表达式 解析保存按钮）

5：expand 每一次展开重新获取处理模式 以及对应表格数据 handleData()

6: changeConfigType: 切换处理模式时  更新currentTableData 用到的方法handleData()

***-->

<template>
  <el-row class="gray mb20 bg-p-border-new">
    <!-- 处理模式 -->
    <el-col :span="5">
      <div>
        <span class="tit">处理模式：</span>
        <el-select
          v-model="configType"
          class="m-2"
          placeholder="请选择处理模式"
          size="small"
          clearable
          filterable
          :disabled="versionDetail"
          @change="changeType"
          style="width: 75%; display: inline-block"
        >
          <el-option v-for="item in parseOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>

      <div class="mt10">
        <span class="tit">默认值类型：</span>
        <el-select
          v-model="defaultValueType"
          class="m-2"
          placeholder="请选择默认值类型"
          size="small"
          clearable
          filterable
          @change="changeDefaultType"
          style="width: 75%; display: inline-block"
        >
          <el-option v-for="item in defaultValueTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </el-col>
    <el-col :span="1" class="tree-wrapper"> </el-col>
    <el-col :span="18" v-if="configType">
      <article>
        <xel-upload-dialog
          v-if="!versionDetail && configType != '3'"
          class="upload-button"
          btnName="导入"
          size="70px"
          exportUrl=""
          importUrl="/config/parseRule/importFeildConfig"
          :showDownLoadBtn="false"
          :data="{ importType, password: importPassword }"
          accept=".xls,.xlsx"
          @click="resetUploadParams"
          @updateData="updateData"
          v-hasPermi="'config:parseRule:import'"
        ></xel-upload-dialog>
        <el-button v-if="!versionDetail" @click="addProcess" class="ml20 search-button">
          <el-icon :size="12">
            <plus />
          </el-icon>
          添加
        </el-button>
        <!-- 表格 -->

        <el-form ref="tableProFormRef" :model="tableProForm" class="tableProForm">
          <xel-table ref="tableRef" :columns="columns" :key="updateConfigType" :data="currentTableData" :pagination="false" max-height="600">
            <template #destValue="scope">
              <span v-if="rowData.dictDataList.length > 0"
                >{{ rowData.dictDataList.filter((item) => item.dictValue == scope.row.destValue).map((cItem) => cItem.dictLabel)[0] }}({{
                  scope.row.destValue
                }})</span
              >
              <span v-else>{{ scope.row.destValue }}</span>
            </template>
          </xel-table>
        </el-form>
      </article>
      <!-- <el-form v-else :model="regularForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="正则表达式"> <el-input v-model="regularForm.sourceKey" :disabled="versionDetail" /> </el-form-item>
            <el-form-item label="解析值"> <el-input type="textarea" v-model="regularForm.destValue" :disabled="versionDetail" /> </el-form-item>
          </el-col>
          <el-col :span="6"> <el-button type="primary" @click="secondParse('save')">解析并保存</el-button></el-col>
        </el-row>
      </el-form> -->
    </el-col>
  </el-row>
  <xel-dialog
    :dialogVisible="dialogVisible"
    :title="dialogTitle"
    ref="dialogRef"
    @submit="submitForm"
    @close="closeForm"
    :showCancel="configType != '3'"
  >
    <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
      <xel-form-item
        v-for="(item, index) in formList"
        :key="index"
        v-model="formData[item.prop]"
        v-bind="item"
        @update:text="getSelectLabel"
      ></xel-form-item>
    </el-form>
    <template #otherButton v-if="configType == '3'">
      <el-button @click="secondParse" :loading="testLoading">解析测试</el-button>
    </template>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick, watch } from "vue";

import { ElMessageBox, ElMessage } from "element-plus";
let emits = defineEmits(["addProcessData"]);

let props = defineProps({
  expand: {
    type: Boolean,
    default: false,
  },
  // 当前行数据
  rowData: {
    type: Object,
    default() {
      return {};
    },
  },
  // 第一次解析结果
  analyResult: {
    type: Array,
    default() {
      return [];
    },
  },
  // 选中的第一次解析结果
  checkAnalya: {
    type: Array,
    default() {
      return [];
    },
  },
  // 是否版本详情
  versionDetail: {
    type: Boolean,
    default: false,
  },
  parseOptions: {
    type: Array,
    default() {
      return [];
    },
  },
  /* 默认值 */
  defaultValueTypeList: {
    type: Array,
    default() {
      return [];
    },
  },
  // 字段配置之前的数据
  initData: {
    type: Array,
    default() {
      return [];
    },
  },
});

let state = reactive({
  configType: "",
  defaultValueType: "",
  // 用于保存 用户添加的所有模式的数据
  tableProForm: {
    tableData: [
      { configType: "1", fieldConfigList: [] },
      { configType: "2", fieldConfigList: [] },
      { configType: "3", fieldConfigList: [] },
    ],
  },
  formData: {
    sourceKey: "",
    destValue: "",
  },
  regularForm: {
    sourceKey: "",
    destValue: "",
  },
  fileDictList: [],
  updateConfigType: false,
});
let { configType, tableProForm, formData, regularForm, updateConfigType, defaultValueType } = toRefs(state);
let dialogTitle = ref("添加值匹配");
let dialogRef = ref();
let ruleFormRef = ref();
let tableProFormRef = ref();
let tableRef = ref();
let formList = reactive([
  {
    formType: "input",
    prop: "sourceKey",
    label: "原始值",
    required: true,
  },
  {
    formType: "input",
    prop: "destValue",
    label: "目的值",
    required: true,
  },
  {
    formType: "select",
    prop: "destValue",
    label: "目的值",
    required: true,
    options: [],
  },
]);
const currentConfigType = "";
let currentTableData = ref([]);

const columns = ref([
  {
    prop: "sourceKey",
    label: "原始值",
  },

  {
    prop: "destValue",
    label: "目标值",
    slotName: "destValue",
    hide: false,
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    hide: props.versionDetail,
    btnList: [
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          state.tableProForm.tableData.forEach((item) => {
            if (item.configType == props.rowData.configType) {
              item.fieldConfigList.splice(item.fieldConfigList.indexOf(scope.row), 1);
            }
          });
          // currentTableData.value.splice(currentTableData.value.indexOf(scope.row), 1);
          submitTable();
        },
      },
    ],
  },
]);

watch(
  () => props.expand,
  // 每次展开 根据模式展示对应的表格数据
  (val) => {
    state.configType = props.rowData.configType;
    state.defaultValueType = props.rowData.defaultValueType;

    /* 新增 - 打开编辑，配置对应 处理模式，列表字段 */
    if (state.configType == 2 || state.configType == 3) {
      columns.value[0].label = "正则表达式";
    } else {
      columns.value[0].label = "原始值";
    }
    columns.value[1].hide = state.configType == 3 ? true : false;
    handleData(props.rowData, "expand");
  },
  {
    deep: true,
    immediate: true,
  }
);

// 添加处理模式的数据 1 值匹配 2：正则 3：正则表达式
function addProcess() {
  popupBox();
}

// 弹框
function popupBox() {
  // 显示方式更新
  formList[1].isShow = true;
  formList[2].isShow = false;
  if (state.configType == 3) {
    // 显示方式更新
    formList[1].type = "textarea";
    formList[1].required = false;
    formList[2].isShow = false;
  }

  if (state.configType != 3 && props.rowData.dictDataList.length > 0) {
    // 目的值是否取字典
    formList[1].isShow = false;
    formList[2].isShow = true;
    formList[2].options = props.rowData.dictDataList.map((item) => {
      return {
        ...item,
        label: item.dictLabel,
        value: item.dictValue,
      };
    });
  }

  formList[0].showDefinedLabel = !formList[0].showDefinedLabel;
  formList[0].label = state.configType == "1" ? "原始值" : "正则表达式";
  formList[1].showDefinedLabel = !formList[1].showDefinedLabel;
  formList[1].label = state.configType == "3" ? "解析结果" : "目的值";
  dialogRef.value.open();
  const typeValue = state.configType == "1" ? "值匹配" : state.configType == "2" ? "正则匹配" : "正则表达式";
  dialogTitle.value = "添加" + typeValue;

  resetFormData();
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
}

//模式1，2 提交表单（同步父页面表格）
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let params = {
        configType: state.configType,

        sourceKey: state.formData.sourceKey,
        destValue: state.formData.destValue,
      };
      handleData(params, "add").then(() => {
        submitTable("save");
      });

      close();
    } else {
      return false;
    }
  });
}

// 解析
function closeForm() {
  resetFormData();
  nextTick(() => {
    ruleFormRef.value && ruleFormRef.value.resetFields();
  });
  ruleFormRef.value && ruleFormRef.value.resetFields();
}

function getSelectLabel(val) {
  // state.formData.destValueText = val + "(" + state.formData.destValue + ")";
}
// 切换处理模式 清空表格
function changeType() {
  let val = {
    1: "原始值",
    2: "正则表达式",
    3: "正则表达式",
  };
  state.updateConfigType = !state.updateConfigType;
  columns.value[0].label = val[state.configType];
  columns.value[1].hide = state.configType == 3 ? true : false;

  let result = state.tableProForm.tableData.filter((item) => item.configType == state.configType);
  currentTableData.value = (result[0] && result[0]["fieldConfigList"]) || [];
  submitTable();
}
//表单重置
function resetFormData() {
  state.formData = {
    sourceKey: "",
    destValue: "",
  };
}

// 二次解析
let testLoading = ref(false);
function secondParse(type) {
  let validateList = [];
  ruleFormRef.value.validateField(["sourceKey"], (valid) => {
    validateList.push(valid);
  });

  if (validateList.every((item) => item === "")) {
    testLoading.value = true;
    let firstResult = props.analyResult

      .filter((item) => props.checkAnalya.some((ele) => ele === item.key))
      .map((item) => {
        return item.value;
      })
      .join("+");

    /* 原逻辑  - 注释 */
    /*const reg = new RegExp(state.formData.sourceKey, "g");
    state.formData.destValue = firstResult.match(reg);*/

    /* 增加为空校验，值为空就不继续执行了 */
    if (!firstResult) {
      ElMessage.info("请先选择解析值");
      testLoading.value = false;
      return false;
    }

    const reg = new RegExp(state.formData.sourceKey);
    if (firstResult.match(reg)) {
      state.formData.destValue = firstResult.match(reg)[1];
    } else {
      state.formData.destValue = "";
    }
    if (state.formData.sourceKey && firstResult) {
      if (!state.formData.destValue) {
        ElMessage.warning("解析失败");
      }
      testLoading.value = false;
    }
    testLoading.value = false;
  }

  // if (type == "save") {
  //   let result = {
  //     configType: state.configType,
  //     sourceKey: state.formData.sourceKey,
  //   };
  //   // 最终处理模式选择哪一个，保存哪一个
  //   handleData(result, "add").then(() => {
  //     submitTable("save");
  //   });
  // }
}

// 导入更新数据
function updateData(data) {
  data.forEach((item) => {
    let result = {
      configType: state.configType,
      ...item,
    };
    handleData(result, "add").then(() => {
      submitTable();
    });
  });
}

// 处理表格数据
function handleData(data, type) {
  return new Promise(function (resolve, reject) {
    // 执行异步操作

    if (type == "expand" && data.allConfigList && data.allConfigList.length > 0) {
      state.tableProForm.tableData = JSON.parse(JSON.stringify(data.allConfigList));
      let result = state.tableProForm.tableData.filter((item) => item.configType == data.configType);
      currentTableData.value = (result[0] && result[0]["fieldConfigList"]) || [];
      // if (data.configType == "3" && data.fieldConfigList.length > 0) {
      //   state.regularForm.sourceKey = data.fieldConfigList[0].sourceKey;
      //   // 处理解析出来的结果
      //   secondParse("");
      // }
      return;
    }

    // 首次展开字段配置 或者是其它添加时
    state.tableProForm.tableData.forEach((aItem) => {
      if (aItem.configType == data.configType) {
        if (type == "expand") {
          // 首次展开
          aItem.fieldConfigList = [];
          aItem.fieldConfigList = JSON.parse(JSON.stringify(data.fieldConfigList));
          // if (data.configType == "3") {
          //   state.regularForm.sourceKey = data.fieldConfigList[0].sourceKey;
          //   // 处理解析出来的结果
          //   secondParse("");
          // }
        } else {
          // if (data.configType == "3") {
          //   aItem.fieldConfigList = [];
          //   aItem.fieldConfigList.push(data);
          // } else {
          aItem.fieldConfigList.push(data);
          // }
        }
      }
    });

    let result = state.tableProForm.tableData.filter((item) => item.configType == data.configType);
    currentTableData.value = (result[0] && result[0]["fieldConfigList"]) || [];

    resolve(true);
  });
}

// 提交表格
function submitTable(submitType) {
  if (submitType == "save") {
    // if ((state.configType != "3" && !state.tableProForm.tableData.length) || (state.configType == "3" && !state.regularForm.sourceKey)) {
    //   ElMessage.warning("字段配置必须至少包含一条信息");
    //   return;
    // }

    if (!state.tableProForm.tableData.length) {
      ElMessage.warning("字段配置必须至少包含一条信息");
      return;
    }
  }

  emits("addProcessData", {
    field: props.rowData.field,
    configType: state.configType,
    defaultValueType: state.defaultValueType,
    allConfigList: state.tableProForm.tableData,

    fieldConfigList: state.configType ? currentTableData.value : [],
  });
  submitType == "save" ? ElMessage.success("添加成功") : "";
}

function changeDefaultType() {
  submitTable();
}
</script>

<style lang="scss" scoped>
.gray {
  width: 100%;
  height: 100%;
  background-color: #f8f8f8;
  padding: 20px 10px;
  // z-index: 99;
  .tit {
    width: 73px;
    display: inline-block;
    line-height: 32px;
  }
  .tree-wrapper {
    position: relative;
    min-height: 100%;
    &::after {
      content: "";
      display: block;
      width: 1px;
      height: 100%;
      position: absolute;
      right: 50%;
      top: 0;
      bottom: 0;
      background: #ededed;
    }
  }
  .text {
    width: 120px;
    display: inline-block;
  }
  .ml40 {
    margin-left: 300px;
  }
}
</style>
