<template>
  <!-- 添加解析规则 、编辑解析规则  -->
  <el-card class="bg-p-border-new">
    <h3 class="conH3Tit mb40 mb20" v-if="!versionDetail">
      {{ "" || ($route.meta && $route.meta.title) }}

      <!-- <backbutton class="pull-right" text="解析规则列表" name="AnalyticRule"></backbutton> -->
      <el-button class="pull-right" @click="backList"
        ><el-icon><Back /></el-icon>返回解析规则列表</el-button
      >
    </h3>
    <h3 class="conH3Tit" v-else>
      {{ "" || ($route.meta && $route.meta.title) }}
      <el-button class="pull-right" style="margin-left: 10px" @click="backVersionFn">
        <el-icon :size="12"> <back /> </el-icon>
        回滚</el-button
      >
      <backbutton class="pull-right" text="版本列表" :name="backRoute"></backbutton>
    </h3>
    <el-form class="flex-form" :model="formData" ref="ruleFormRef" label-width="160px">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item">
        <p v-if="item.prop == 'orderNum'" class="order">
          <el-icon class="mr5"><Warning /></el-icon>数字越大优先级越高，兜底规则请设置为0，请勿设置过多优先级为0的规则，以免影响性能
        </p>
      </xel-form-item>
      <div class="dialog-footer ml20 mt20">
        <el-button type="primary" @click="analyticForm" :loading="loading">解析</el-button>
      </div>
    </el-form>

    <!-- 表格 -->
    <el-form ref="tableFormRef" :model="tableForm" class="tableForm">
      <div :style="{ height: 'calc(100vh - 560px)', minHeight: '400px' }">
        <el-table
          :data="tableForm.tableData"
          :border="parentBorder"
          ref="tableRef"
          height="100%"
          :row-key="getRowKeys"
          :expand-row-keys="parentIds"
          @expand-change="parentExpand"
        >
          <el-table-column type="expand">
            <template #default="scope">
              <add-process
                :expand="scope.row.expandProcess"
                :rowData="scope.row"
                :analyResult="state.analyticResult"
                :checkAnalya="formData.parseType != '3' ? scope.row.indexNum : scope.row.jsonKey"
                @addProcessData="addProcessData"
                :versionDetail="versionDetail"
                :parseOptions="parseOptions"
                :defaultValueTypeList="defaultValueTypeList"
                :initData="initTable"
              ></add-process>
            </template>
          </el-table-column>
          <el-table-column label="字段名" prop="field">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.field'" :rules="rules.field">
                <el-select
                  :placeholder="'请选择字段名'"
                  v-model="scope.row.field"
                  clearable
                  filterable
                  :disabled="props.versionDetail"
                  @change="changeFile($event, 'change')"
                  @clear="clearFile"
                >
                  <el-option
                    v-for="item in fileOptions"
                    :key="item.field"
                    :label="`${item.fieldText}_${item.field}`"
                    :value="`${item.fieldText}_${item.field}`"
                  >
                    <span class="pull-left">{{ item.fieldText }}</span>
                    <span class="pull-right">{{ item.field }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <!-- 解析值 -->
          <el-table-column label="解析值">
            <template #default="scope">
              <el-select
                v-if="state.formData.parseType != '3'"
                :placeholder="'请选择解析值'"
                v-model="scope.row.indexNum"
                clearable
                filterable
                multiple
                :disabled="props.versionDetail"
              >
                <el-option v-for="item in analyticResult" :key="item.key" :label="`${item.key}：${item.value}`" :value="item.key"
                  >{{ item.key }}：{{ item.value }}</el-option
                >
              </el-select>

              <el-select v-else :placeholder="'请选择解析值'" v-model="scope.row.jsonKey" clearable filterable multiple>
                <el-option v-for="item in analyticResult" :key="item.key" :label="`${item.key}：${item.value}`" :value="item.key"
                  >{{ item.key }}：{{ item.value }}</el-option
                >
              </el-select>
            </template>
          </el-table-column>

          <!-- 新增字段 - 多解析值分隔符 -->
          <el-table-column label="多解析值分隔符" prop="delimiter">
            <template #default="scope">
              <el-input v-model="scope.row.delimiter" :disabled="props.versionDetail" placeholder="多解析值分隔符" maxlength="100" />
            </template>
          </el-table-column>

          <!-- 默认值 -->
          <el-table-column label="默认值" prop="defaultValue">
            <template #default="scope">
              <el-select
                v-if="scope.row.dictDataList && scope.row.dictDataList.length > 0"
                :placeholder="'请选择默认值'"
                style="width: 100%"
                v-model="scope.row.defaultValue"
                clearable
                filterable
                :disabled="props.versionDetail"
              >
                <el-option v-for="item in scope.row.dictDataList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
              <el-input v-else v-model="scope.row.defaultValue" placeholder="请输入默认值" :disabled="props.versionDetail" maxlength="100"></el-input>
            </template>
          </el-table-column>
          <!-- 特殊转换 -->
          <el-table-column label="特殊转换" prop="convertType">
            <template #default="scope">
              <el-select :placeholder="'请选择特殊转换'" v-model="scope.row.convertType" clearable filterable :disabled="props.versionDetail">
                <el-option v-for="item in convertOptions" :key="item.value" :label="item.label" :value="item.value"> {{ item.label }}</el-option>
              </el-select>
            </template>
          </el-table-column>
          <!-- 时间格式 -->
          <el-table-column label="时间格式化" prop="timeType">
            <template #default="scope">
              <el-select
                v-if="scope.row.convertType == '4' && scope.row.field && scope.row.field.indexOf('time') > -1"
                :placeholder="'请选择/填写时间格式'"
                v-model="scope.row.timeType"
                clearable
                filterable
                allow-create
                default-first-option
                :disabled="props.versionDetail"
                @blur="timeSelect($event, scope.row)"
              >
                <el-option v-for="item in timeTypeOptions" :key="item.value" :label="item.label" :value="item.value"> {{ item.label }}</el-option>
              </el-select>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200px">
            <template #default="scope">
              <xel-handle-btns :key="scope.row.updateIconStatus + ''" :btnList="getBtnList(scope)" :scope="scope"></xel-handle-btns>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <div class="mt20 text-center" v-if="!props.versionDetail">
      <el-button @click="addField">
        <el-icon :size="12"> <plus /> </el-icon>添加字段</el-button
      >
    </div>
    <div class="text-right mt15-new">
      <el-button v-if="!props.versionDetail" type="primary" @click="submit('valid')" :loading="saveLoading">规则测试</el-button>
      <el-button v-if="!props.versionDetail" type="primary" @click="submit('sub')" :loading="saveLoading">确定</el-button>
      <el-button type="button" @click="cancel">取消</el-button>
    </div>
  </el-card>
  <xel-dialog title="解析结果" ref="dialogResultRef" :show-submit="false" buttonCancel="关闭" @close="$emit('close')" width="60%">
    <!-- 修改 - 替换 ul 为表格 -->
    <el-table :data="analyticResult">
      <el-table-column prop="key" label="键" :show-overflow-tooltip="true" />
      <el-table-column prop="value" label="值" :show-overflow-tooltip="true" />
    </el-table>
    <!--    <ul class="flex-start" v-for="item in analyticResult" :key="item.key">
      <li>{{ item.key }}</li>
      <li>：</li>
      <li class="ml5 pre">{{ item.value }}</li>
    </ul>-->
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick, watch, onMounted, h } from "vue";
import { getLogFieldList, fieldAvailable } from "@/api/sime/config/field.js";
import { getReceiverRoute, restartReceiver } from "@/api/sime/config/logAcceptState";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import addProcess from "./addProcess.vue";
import { getDictsData } from "@/utils/getDicts";
import { useStore } from "vuex";
import tableMixin from "../../components/mixin/tableMixin";
import backVersion from "@/views/sime/components/js/backVersion";
import {
  parseJson,
  addParseRule,
  getDetailRule,
  editParseRule,
  postLogTypeValid,
  getVersionDetail,
  rollBackVersion,
  postParseLog,
} from "@/api/sime/config/analyticRule";
let { tableHeight } = tableMixin(560);
let props = defineProps({
  versionDetail: {
    type: Boolean,
    default: false,
  },
});

const store = useStore();
const route = useRoute();
const router = useRouter();
let state = reactive({
  formData: {
    id: route.query.copy ? "" : route.params.id,
    groupId: route.query.groupId,
    name: "",
    description: "",
    status: null,
    parseType: "",
    codeType: "1",
    logSample: "",
    parseRegular: "",
    validateRegular: "",
    orderNum: 100,
  }, //新增编辑表单
  initTable: [], //最初的表格，为取消用
  tableForm: {
    tableData: [],
  },
  //
  allFileOptions: [], //全部options，用来比对
  fileOptions: [],
  analyticResult: [],
  timeTypeOptions: [], // 时间类型格式 字段名包括time 有可编辑 无空
  convertOptions: [], //转换格式

  parseOptions: [], //字段处理模式字典
  fileDictList: [],
  defaultValueTypeList: [],
});
let backRoute = { name: "ParseVersion", params: { id: route.query.pId } };
let { formData, tableForm, fileOptions, analyticResult, timeTypeOptions, convertOptions, parseOptions, initTable, defaultValueTypeList } =
  toRefs(state);
let loading = ref(false);
let ruleFormRef = ref(); // 解析表单
let dialogResultRef = ref(); //解析结果 弹框
let tableRef = ref(); //外表格
let tableFormRef = ref(); //表格form
let saveLoading = ref(false);
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "名称",
    required: true,
    itemWidth: "46%",
    disabled: props.versionDetail,
  },

  {
    formType: "select",
    prop: "parseType",
    label: "解析类型",
    // 字典关键字

    dictName: "parse_type",
    sime: true,
    required: true,
    itemWidth: "46%",
    disabled: props.versionDetail,
    onChange(val) {
      changeParseType(val);
    },
  },
  {
    formType: "select",
    prop: "codeType",
    label: "编码方式",
    dictName: "parse_code_type",
    sime: true,
    required: true,
    itemWidth: "46%",
    disabled: props.versionDetail,
  },
  {
    formType: "input",
    type: "textarea",
    prop: "description",
    label: "描述",
    itemWidth: "46%",
    disabled: props.versionDetail,
  },
  {
    formType: "radio",
    prop: "status",
    label: "状态",
    // 字典关键字
    dictName: "config_logStoreDatasource_isAvailable",
    isNumber: true,
    sime: true,
    required: true,
    itemWidth: "46%",
    disabled: props.versionDetail,
  },
  {
    formType: "number",
    prop: "orderNum",
    label: "优先级",
    itemWidth: "46%",
    required: true,
    min: 0,
    max: 10000,
    precision: "0",
    disabled: props.versionDetail,
  },
  {
    formType: "input",
    type: "textarea",
    prop: "logSample",
    label: "日志样本",
    required: true,
    itemWidth: "46%",
    disabled: props.versionDetail,
    maxlength: 65536,
    rows: 3,
  },
  {
    formType: "input",
    type: "textarea",
    prop: "validateRegular",
    label: "日志格式校验表达式",
    placeholder: "请输入正则表达式",
    required: false,
    itemWidth: "46%",
    disabled: props.versionDetail,
    maxlength: 4096,
    rows: 3,
    reserveTrim: false,
    isShow: false,
  },
  {
    formType: "input",
    type: "textarea",
    prop: "parseRegular",
    label: "解析表达式",
    required: true,
    itemWidth: "46%",
    isDisplacement: false,
    disabled: props.versionDetail,
    maxlength: 4096,
    rows: 3,
    reserveTrim: false,
  },
]);

// 表格表单验证
const rules = reactive({
  field: [
    {
      required: true,
      message: "请选择字段名",
      trigger: ["blur", "change"],
    },
  ],
});

onMounted(() => {
  getFieldAvailable();
  getDictOptions(); // 获取时间格式化字典/转换字典
  getLogFieldListFn(); //获取表格展示形式 处理字段名
});

// 不同的解析类型 对应不同的解析表达式
function changeParseType(val, type) {
  if (type != "init") {
    state.formData.parseRegular = "";
    state.formData.validateRegular = "";
  }
  let list = {
    1: "正则表达式",
    2: "分隔符",
    3: "JSON提取表达式",
    "": "解析表达式",
  };
  formList[8].reserveTrim = false;
  formList[8].required = true;
  formList[8].label = list[val];

  if (val == 3) {
    formList[8].required = false;
  }
  if (val == 2) {
    formList[8].reserveTrim = true;
  }
  formList.find((item) => item.prop == "validateRegular").isShow = val == 3;
  formList.find((item) => item.prop == "parseRegular").isDisplacement = val == 3;
  formList[8].placeholder = val == 3 ? "请输入正则表达式" : "请输入" + list[val];
  formList[8].showDefinedLabel = !formList[8].showDefinedLabel;
}

// 解析表单 日志样式
let resData = ref();
function analyticForm() {
  /* 解析日志样本 */
  postParseLog({
    parseType: state.formData.parseType,
    logSample: state.formData.logSample,
    parseRegular: state.formData.parseRegular,
    codeType: state.formData.codeType,
    validateRegular: state.formData.parseType != "3" ? "" : state.formData.validateRegular,
  })
    .then((res) => {
      resData.value = res.data;
      let validateList = [];
      ruleFormRef.value.validateField(["logSample", "parseRegular"], (valid) => {
        validateList.push(valid);
      });
      if (validateList.every((item) => item === "")) {
        loading.value = true;
        analysisStandard();
      }
      loading.value = false;
    })
    .catch(() => {
      state.analyticResult = [];
      handleData();
    });
}

//解析标准
async function analysisStandard(type) {
  state.analyticResult = []; //成功展示解析结果 失败展示“解析失败”提示

  /* 修改 - 原逻辑注释，后续维护删除 */
  /*let jsonResult = []; // // 解析类型为json时  区分（存在和不存在表达式）两种情况
  const regJson = new RegExp(state.formData.parseRegular); //解析表达式*/
  /*switch (
    state.formData.parseType // 1: "正则表达式", 2: "分隔符", 3: "JSON的正则表达式",
  ) {
    case "1":
      {
        if (regJson.exec(state.formData.logSample)) {
          //第一步通过表达式解析
          let parseResult = regJson.exec(state.formData.logSample);
          state.analyticResult = parseResult.slice(1, parseResult.length);
        } else {
          state.analyticResult = [];
        }
        /!*console.log("state.analyticResult111: ", state.analyticResult);*!/
      }

      break;
    case "2":
      /!*
      * 新增 - 编辑回填时，使用后端返回数据进行渲染
      * *!/
      state.analyticResult = [];
      if(type === 'edit') {
        const { data } = await postParseLog({
          parseType: state.formData.parseType,
          logSample: state.formData.logSample,
          parseRegular: state.formData.parseRegular,
          codeType: state.formData.codeType,
        });
        resData.value = data;
      }
      for (let i in resData.value) {
        state.analyticResult.push(resData.value[i])
      }
      state.analyticResult = state.analyticResult && state.analyticResult.length > 0 ? state.analyticResult : state.formData.logSample.split(state.formData.parseRegular);
      break;
    case "3":
      {
        /!*
        * 新增 - 判断 JSON的校验表达式 是否为空
        * 空：原逻辑
        * 非空：增加判断
        * *!/
        if(state.formData.validateRegular !== "") {
          const regJson2 = new RegExp(state.formData.validateRegular);
          if (!regJson2.exec(state.formData.logSample)) {
            ElMessage.info("日志格式校验表达式匹配失败！");
            return false;
          }
        }

        if (!state.formData.parseRegular) {
          jsonResult = state.formData.logSample;
        } else {
          if (regJson.exec(state.formData.logSample)) {
            //第一步通过表达式解析
            jsonResult = regJson.exec(state.formData.logSample)[1];
          } else {
            ElMessage.info("JSON提取表达式解析失败！");
            return false
          }
        }
        let res = await parseJson({ logSample: jsonResult }); //第二步通过接口解析
        if (res && res.data) {
          for (let [k, v] of Object.entries(res.data)) {
            state.analyticResult.push({
              key: `${k}`,
              value: `${v}`,
            });
          }
        } else {
          state.analyticResult = null;
        }
      }
      break;
    default:
      break;
  }*/

  /* 判断是否为编辑 - 首次加载 */
  if (type === "edit") {
    const { data } = await postParseLog({
      parseType: state.formData.parseType,
      logSample: state.formData.logSample,
      parseRegular: state.formData.parseRegular,
      codeType: state.formData.codeType,
      validateRegular: state.formData.parseType != "3" ? "" : state.formData.validateRegular,
    });
    resData.value = data;
  }
  for (let [k, v] of Object.entries(resData.value)) {
    state.analyticResult.push({
      key: `${k}`,
      value: `${v}`,
    });
  }

  // 非复制和编辑页面/未通过表达式验证
  if (type != "edit") {
    if (!state.analyticResult) {
      ElMessage.warning("解析失败");
      return;
    } else {
      dialogResultRef.value.open();
    }
  }

  // 处理结果方便dom展示  1: "正则表达式", 2: "分隔符" 3: "JSON的正则表达式",
  /*if (state.formData.parseType != "3" && state.analyticResult.length > 0) {
    state.analyticResult = state.analyticResult.map((sItem, sIndex) => {
      return {
        key: state.formData.parseType == "2" ? `${sIndex}` : `${sIndex + 1}`,
        value: `${sItem}`,
      };
    });
  }*/
  /* 处理数据格式 */
  handleData();
  loading.value = false;
}

/* 处理数据 - 解析值 */
const handleData = () => {
  state.tableForm.tableData.forEach((item) => {
    /* 解析 state.analyticResult 是否为空 */
    if (state.analyticResult.length === 0) {
      /* 直接赋值 [] */
      state.formData.parseType != "3" ? (item.indexNum = []) : (item.jsonKey = []);
    } else {
      /* 不为空 循环判断值是否存在 */
      let data = [];
      if (state.formData.parseType != "3") {
        /* 循环比对 - 存在保留，不存在去掉 */
        item.indexNum.forEach((indexNum) => {
          let ifExist = state.analyticResult.find((i) => i.key == indexNum);
          if (ifExist) {
            data.push(indexNum);
          }
        });
        item.indexNum = data;
      } else {
        /* 循环比对 - 存在保留，不存在去掉 */
        item.jsonKey.forEach((jsonKey) => {
          let ifExist = state.analyticResult.find((i) => i.key == jsonKey);
          if (ifExist) {
            data.push(jsonKey);
          }
        });
        item.jsonKey = data;
      }
    }
  });
};

async function getDictOptions() {
  state.timeTypeOptions = await getDictsData("parse_time_type");
  state.convertOptions = await getDictsData("parse_convert_type");
  state.parseOptions = await getDictsData("parse_config_type");
  state.defaultValueTypeList = await getDictsData("default_value_type");
}
// 获取字典数据

function getFieldAvailable() {
  fieldAvailable().then((res) => {
    state.fileDictList = res.data;
  });
}
// 新增时 获取表格字段名
function getLogFieldListFn() {
  getLogFieldList().then((res) => {
    state.allFileOptions = res.data.rows;
    if (route.params.id) {
      state.fileOptions = res.data.rows;
    } else {
      // 新增时 字段名展示重要的 不重要的作为选项下拉
      state.fileOptions = res.data.rows.filter((item) => !item.important);

      res.data.rows.forEach(async (eItem, index) => {
        if (eItem.important) {
          state.tableForm.tableData.push({
            field: eItem.fieldText + "_" + eItem.field,
            indexNum: null, //正则或者分隔符类型的下标值
            jsonKey: null, //json类型的key值 json类型时候必填？
            defaultValue: "",
            timeType: "",
            convertType: "",
            configType: "",
            important: eItem.important,
            orderNum: index + 1,
            dictDataList: eItem.dictType ? state.fileDictList[eItem.dictType] : [],
            expandProcess: false, //字段配置是否展开
          });
        }
      });
    }
    if (route.params.id) {
      search(route.params.id); //// 编辑/复制 获取表格数据
    }
  });
}

// 编辑时
function search(id) {
  let port = props.versionDetail ? getVersionDetail : getDetailRule;
  port(id)
    .then((res) => {
      Object.keys(res.data).forEach((key) => {
        state.formData[key] = res.data[key];
        state.formData.id = route.query.copy ? "" : res.data.id;
        state.formData.name = route.query.copy ? res.data.name + "-复制" : res.data.name;
      });
      changeParseType(state.formData.parseType, "init");
      // 字段配置会改变表格，最终数据
      state.tableForm.tableData = res.data.fieldVOList.map((fItem) => {
        return {
          ...fItem,
          field: fItem.fieldText + "_" + fItem.field,
          indexNum: fItem.indexNum ? fItem.indexNum.split("+") : [],
          jsonKey: fItem.jsonKey ? fItem.jsonKey.split("+") : [],
          expandProcess: false, //字段配置是否展开
        };
      });

      state.tableForm.tableData.forEach((tItem) => {
        changeFile(tItem.field);
      });

      let selectedField = state.tableForm.tableData.map((item) => {
        return { field: item.field ? item.field.split("_")[1] : "" };
      });

      state.fileOptions = state.allFileOptions.filter((item) => !selectedField.some((cItem) => cItem.field === item.field));
    })
    .finally(() => {
      analysisStandard("edit");
    }); //获取解析表单});
}

// el-select clear
function clearFile(data) {
  let value = data.split("_")[1];
  state.allFileOptions.forEach((item) => {
    if (item.field == value) {
      state.fileOptions.push({ ...item });
    }
  });
}
// 字段名唯一 选中后下拉去除
function changeFile(data, type) {
  let currentField = data.split("_")[1];
  var dictType = state.allFileOptions.filter((item) => item.field === currentField).map((item) => item.dictType)[0];
  // 选中字段时 根据其中的包含的字典，默认值变为下拉框 反之 input
  state.tableForm.tableData.forEach(async (item) => {
    if (item.field.split("_")[1] == currentField) {
      item.dictDataList = dictType ? state.fileDictList[dictType] : [];
      if (type == "change") {
        // 改变字段名时，字段配置清空
        item.configType = "";
        item.fieldConfigList = null;
        item.defaultValue = "";
      }
    }
  });
}

// 外层表格按钮
function getBtnList(scope) {
  return [
    {
      icon: !scope.row.configType ? "Setting" : "Tools",
      title: "字段配置",
      hide: props.versionDetail && !scope.row.fieldConfigList,
      onClick(scope) {
        scope.row.expandProcess = !scope.row.expandProcess;
        tableRef.value.toggleRowExpansion(scope.row);
      },
    },
    {
      icon: "delete",
      title: "删除",
      hide: props.versionDetail,
      onClick(scope) {
        state.tableForm.tableData.splice(
          state.tableForm.tableData.findIndex((item) => item.field == scope.row.field),
          1
        );
      },
    },
  ];
}
// 时间类型输入时自动填充
function timeSelect(e, data) {
  if (e.target.value) {
    state.tableForm.tableData.forEach((tItem) => {
      if (tItem.field == data.field) {
        tItem.timeType = e.target.value;
      }
    });
  }
}
// 添加一行解析规则
function addField() {
  state.tableForm.tableData.push({
    field: "",
    fieldText: "",
    indexNum: [], //正则或者分隔符类型的下标值
    jsonKey: [], //json类型的key值 json类型时候必填？
    defaultValue: "",
    timeType: "",
    convertType: "",
    configType: "",
    important: 0,
    fieldConfigList: [],
    expandProcess: false,
  });
  nextTick(() => {
    document.getElementsByTagName("tr")[state.tableForm.tableData.length - 1].scrollIntoView(true);
  });
}

// 表格每一行添加字段配置
function addProcessData(data) {
  state.tableForm.tableData.forEach((item) => {
    if (item.field == data.field) {
      item.configType = data.configType;
      item.allConfigList = data.allConfigList;
      item.fieldConfigList = data.fieldConfigList;
      item.expandProcess = !item.expandProcess;
      item.updateIconStatus = !item.updateIconStatus;
      item.defaultValueType = data.defaultValueType;
    }
  });
  nextTick(() => {});
  // tableRef.value.reload();
}

function cancel() {
  if (window.location.href.includes("simeConfig/parseVersionDetail")) {
    router.push({
      name: "ParseVersion",
      params: {
        id: route.query.pId,
      },
    }); //路由跳转
  } else {
    router.push({
      name: "AnalyticRule",
      params: {
        groupId: route.query.groupId,
      },
    }); //路由跳转
  }
  store.commit("closeCurrentTab");
}

// 保存解析规则  保存后是否立即启动弹框
function submit(type) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      tableFormRef.value.validate((valid) => {
        if (valid) {
          let params = {
            ...state.formData,
            fieldList: state.tableForm.tableData.map((item) => {
              delete item.allConfigList;
              return {
                ...item,
                field: item.field.split("_")[1],
                indexNum: item.indexNum ? item.indexNum.join("+") : "",
                jsonKey: item.jsonKey ? item.jsonKey.join("+") : "",
              };
            }),
          };
          saveLoading.value = true;
          /* 新增 - 判断按钮点击类型
           * type:
           * valid - 规则校验
           * sub - 确定提交
           * */
          if (type === "valid") {
            postLogTypeValid(params)
              .then((res) => {
                ElMessage.success(`规则测试通过！耗时${res.nanoTime}纳秒（${res.millisTime}毫秒），
              解析速率：${res.millisTime <= 0.5 ? "较快" : res.millisTime > 0.5 && res.millisTime < 1 ? "较慢" : "极慢，请优化解析表达式"}`);
              })
              .finally(() => {
                saveLoading.value = false;
              });
          } else {
            // 判断是否是编辑、复制、还是新增
            let port = route.params.id ? (route.query.copy == "true" ? addParseRule : editParseRule) : addParseRule;
            port(params)
              .then((res) => {
                if (route.params.id && route.query.copy != "true") {
                  isStart();
                } else {
                  ElMessage.success("操作成功");
                  store.commit("closeCurrentTab");
                  router.push({
                    name: "AnalyticRule",
                    params: {
                      groupId: route.query.groupId,
                    },
                  }); //路由跳转
                }
              })
              .finally(() => {
                saveLoading.value = false;
              });
          }
        }
      });
    }
  });
}

let str = ref("");
function isStart() {
  ElMessageBox({
    title: "警告",
    message: h("p", null, [h("p", null, "是否立即启用"), h("p", { style: "color: #E6A23C" }, "注意：选择否，则需要手动重启监听器")]),
    showCancelButton: true,
    confirmButtonText: "是",
    cancelButtonText: "否",
    closeOnClickModal: false,
    showClose: false,
  })
    .then(() => {
      let resetParams = {
        parseRuleList: [{ ruleId: route.params.id }],
      };
      /* 获取采集器 list - 遍历path */
      getReceiverRoute()
        .then((res) => {
          let sum = 0;
          str.value = "";
          res.data.forEach((item, index) => {
            /* 循环重启监听器
             * 成功：拼接成功 msg
             * 失败：拼接失败 msg
             * */
            restartReceiver(resetParams, item.path)
              .then((val) => {
                str.value += `<p>${item.name}: ${val.msg}</p>`;
              })
              .catch((err) => {
                str.value += `<p>${item.name}: ${err.msg}</p>`;
              })
              .finally((fval) => {
                sum += 1;
                if (res.data.length === sum && str.value) {
                  /* 提示信息 */
                  ElMessageBox.alert("<div style='max-height:400px;overflow:auto;'>" + str.value + "</div>", "提示", {
                    confirmButtonText: "关闭",
                    dangerouslyUseHTMLString: true,
                  });
                }
              });
          });
        })
        .then(() => {
          /* 执行完毕，回退 */
          store.commit("closeCurrentTab");
          router.push({
            name: "AnalyticRule",
            params: {
              groupId: route.query.groupId,
            },
          });
        });
    })
    .catch(() => {
      store.commit("closeCurrentTab");
      router.push({
        name: "AnalyticRule",
        params: {
          groupId: route.query.groupId,
        },
      });
    }); //路由跳转});
}
function backVersionFn() {
  backVersion(rollBackVersion, route.query.pId, route.params.id);
}

// 折叠
function getRowKeys(row) {
  return row.field;
}
let parentIds = ref([]);
function parentExpand(row) {
  if (parentIds.value[0] == row.field) {
    parentIds.value = [];
  } else {
    parentIds.value = [];
    parentIds.value.push(row.field);
  }
  row.expandProcess = true;
}

// 返回
function backList() {
  store.commit("closeCurrentTab");
  router.push({
    name: "AnalyticRule",
    params: {
      groupId: route.query.groupId,
    },
  });
}
watch(
  () => state.tableForm.tableData,
  () => {
    if (state.allFileOptions.length > 0) {
      if (state.tableForm.tableData.length > 0) {
        // 筛选出已经选中
        let selectedField = state.tableForm.tableData.map((item) => {
          return { field: item.field ? item.field.split("_")[1] : "" };
        });

        state.fileOptions = state.allFileOptions.filter((item) => !selectedField.some((cItem) => cItem.field === item.field));
      } else {
        state.fileOptions = state.allFileOptions;
      }
    }
  },
  { deep: true }
);
//父组件可以调用的方法
defineExpose({
  // tableData:
});
</script>

<style lang="scss" scoped>
.mb40 {
  height: 40px;
  border-bottom: 1px solid #eee;
}

:deep(.tableForm .el-form-item--small .el-form-item__content) {
  margin-top: 18px;
}
:deep(.el-table__expand-icon > .el-icon) {
  display: none;
}
:deep(.el-form-item__label) {
  color: #515a6e;
}
:deep(.el-select) {
  width: 100%;
}
.order {
  font-size: 12px;
  color: #e6a23c;
  .el-icon {
    vertical-align: middle;
  }
}
.pre {
  white-space: pre;
}
</style>
