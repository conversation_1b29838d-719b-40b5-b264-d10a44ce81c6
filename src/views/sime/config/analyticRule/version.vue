<template>
  <version-table :load-data="getVersionList" :columns="columns" :back-api="rollBackVersion"></version-table>
</template>
<script>
export default {
  name: "RuleVersion",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import versionTable from "@/views/sime/components/versionTable.vue";
import { getVersionList, rollBackVersion } from "@/api/sime/config/analyticRule";
let columns = [
  {
    prop: "version",
    label: "版本号",
  },
  {
    prop: "name",
    label: "规则名称",
  },

  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "parseTypeText",
    label: "解析类型",
  },
  {
    prop: "updateByName",
    label: "创建人",
  },
  {
    prop: "updateTime",
    label: "创建时间",
  },
];
</script>

<style lang="scss" scoped></style>
