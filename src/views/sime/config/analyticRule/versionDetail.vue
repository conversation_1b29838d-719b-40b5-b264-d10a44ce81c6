<template>
  <rule-detail :versionDetail="true"></rule-detail>
</template>
<script>
export default {
  name: "RuleVersionDetail",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import { getVersionDetail, rollBackVersion } from "@/api/sime/config/analyticRule";

import { useRouter, useRoute } from "vue-router";

import ruleDetail from "./addRule.vue";
const route = useRoute();
</script>

<style lang="scss" scoped></style>
