<template>
  <el-card>
    <sime-layout
      ref="simeRef"
      :load-data="getParseRuleGroup"
      :defaultProps="defaultProps"
      :table_data="table_data"
      :node_item_data="add_item_data"
      @changeGroup="changeGroup"
      :tableId="tableId"
      :moduleTyp="'ruleGroup'"
    >
      <div>
        <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
          <el-button
            v-hasPermi="'config:queryFilter:delete'"
            @click="delFn(multipleSelection)"
            class="search-button"
            :disabled="multipleSelection.length === 0"
          >
            <icon n="icon-huabanfuben" :size="12"></icon>
            批量删除
          </el-button>

          <el-button
            @click="newlyAdded"
            class="search-button"
            :disabled="!currentGroup.id || currentGroup.id == '0' || currentGroup.isDataNode"
            v-hasPermi="'config:parseRule:add'"
          >
            <el-icon :size="12">
              <plus />
            </el-icon>
            解析规则
          </el-button>
          <xel-upload-dialog
            class="upload-button"
            btnName="导入"
            size="70px"
            exportUrl=""
            importUrl="/config/parseRule/import"
            :showDownLoadBtn="false"
            :data="{ importType, password: importPassword }"
            accept=".zip"
            @click="resetUploadParams"
            @updateData="search()"
            v-hasPermi="'config:parseRule:import'"
          >
            <el-form label-width="130px">
              <xel-form-item
                label="相同数据操作"
                form-type="select"
                :sime="true"
                dictName="config_analysisrule_importType"
                v-model="importType"
              ></xel-form-item>
              <el-form-item label="是否使用校验密码">
                <el-radio-group v-model="showPassword" style="width: 100%" @change="changeShowPassword">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="校验密码" v-show="showPassword">
                <el-input v-model="importPassword"></el-input>
              </el-form-item>
            </el-form>
          </xel-upload-dialog>
          <el-button v-hasPermi="'config:parseRule:export'" style="margin-left: 10px" @click="openExportDialog">
            <el-icon :size="12">
              <Download />
            </el-icon>
            导出</el-button
          >
          <move-items
            idKey="id"
            :list="filterList"
            :multiple-selection="multipleSelection"
            :current-group-id="currentGroup.id"
            :move-api="moveItem"
            @update="moveUpdate"
          ></move-items>
        </common-search>
        <xel-table
          ref="tableRef"
          :columns="columns"
          :checkbox="true"
          :row-key="idKey"
          :load-data="getParseRuleList"
          :default-params="{ groupId: currentGroup.id }"
          @selection-change="handleSelectionChange"
        >
          <template #status="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="updateStatusFn(scope.row)"> </el-switch>
          </template>
        </xel-table>
      </div>
    </sime-layout>
    <!-- 导出弹框 -->
    <xel-dialog title="导出数据" size="small" ref="exportDialogRef" @submit="exportData">
      <el-form label-width="130px">
        <el-form-item label="导出类型" style="width: 100%">
          <el-radio-group v-model="exportParams.isAll" style="width: 100%">
            <el-radio :label="true">导出全部</el-radio>
            <el-radio :label="false">导出选中</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否使用校验密码">
          <el-radio-group v-model="showPassword" style="width: 100%" @change="changeShowPassword">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="校验密码" v-show="showPassword">
          <el-input v-model="exportParams.password"></el-input>
        </el-form-item>
      </el-form>
    </xel-dialog>
  </el-card>
</template>
<script>
export default {
  name: "AnalyticRule",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, onActivated, nextTick, h } from "vue";
import {
  getParseRuleGroup,
  delParseRuleGroup,
  putParseRuleGroup,
  addParseRuleGroup,
  detailParseRuleGroup,
  delParseRule as delItem,
  getParseRuleList,
  moveParseRule as moveItem,
  updateStatus,
} from "@/api/sime/config/analyticRule";
import handlerBtns from "../../utils/handlerBtns";
import { useRouter, useRoute } from "vue-router";
import moveItems from "@/views/sime/components/moveItems.vue"; //移动组件
import { getReceiverRoute, restartReceiver } from "@/api/sime/config/logAcceptState";
import { ElMessageBox, ElMessage } from "element-plus";
import { download } from "@/plugins/request";
import { batchDelete } from "@/utils/delete";
const router = useRouter();
const route = useRoute();
let idKey = "id";
// 解析组 相关配置
let simeRef = ref();
let state = reactive({
  currentGroup: {},
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {},
  add_item_data: {
    isCheckName: true,
    delport: delParseRuleGroup,
    editport: putParseRuleGroup,
    addport: addParseRuleGroup,
    detailport: detailParseRuleGroup,
    nodeeDialogTitle: "解析规则分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
      {
        formType: "input",
        type: "text",
        prop: "devType",
        label: "设备类型",
      },
      {
        formType: "input",
        type: "text",
        prop: "devCompany",
        label: "厂商",
      },
      {
        formType: "input",
        type: "text",
        prop: "devVersion",
        label: "型号版本",
      },
    ],
  },
});
let { btnList, multipleSelection, handleSelectionChange } = handlerBtns(
  delItem,
  search,
  () => {
    simeRef.value.getData(tableId.value);
  },
  copyItem,
  "parseId",
  router
); //mixins

let columns = [
  {
    prop: "status",
    label: "状态",
    slotName: "status",
  },
  {
    prop: "name",
    label: "名称",
    click(scope) {
      router.push({
        name: "EditAnalyticRule",
        params: {
          id: scope.row.id,
        },
        query: {
          groupId: state.currentGroup.id,
        },
      }); //路由跳转
    },
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "parseTypeText",
    label: "解析类型",
  },
  {
    prop: "codeTypeText",
    label: "编码方式",
  },
  {
    prop: "orderNum",
    label: "优先级",
  },
  {
    prop: "updateTime",
    label: "修改时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList,
  },
];

// 因左侧权 并没有具体的规则 tableId在些页面表示所在规则组
let tableId = ref("");
let dialogTitle = ref("");
let dialogText = ref("过滤器");
let tableRef = ref();
// 解析列表--start
let searchState = reactive({
  data: {
    groupId: "",
    name: "",
    description: "",
    parseType: "",
    codeType: "",
  },

  formList: [
    {
      formType: "input",
      prop: "name",
      label: "名称",
      itemWidth: "30%",
    },
    {
      formType: "input",
      prop: "description",
      label: "描述",

      itemWidth: "30%",
    },
    {
      formType: "select",
      prop: "parseType",
      label: "解析类型",
      itemWidth: "30%",
      dictName: "parse_type",
      sime: true,
    },
    {
      formType: "select",
      prop: "codeType",
      label: "编码方式",

      dictName: "parse_code_type",
      itemWidth: "30%",
      sime: true,
    },

    {
      formType: "input",
      prop: "logSample",
      label: "日志样本",

      itemWidth: "30%",
    },
  ],
});

onMounted(() => {
  tableId.value = route.params.groupId;
  state.currentGroup.id = route.params.groupId;
  tableId.value ? simeRef.value.getData(tableId.value) : "";
  searchState.data.groupId = tableId.value;
  search(false);
});
onActivated(() => {
  search(false);
});
//获取当前选中的分组
function changeGroup(data) {
  tableId.value = data.id;
  state.currentGroup = data;

  searchState.data.groupId = data.id;
  if (data.isDataNode) {
    dialogTitle.value = "修改" + dialogText.value;
    // modifyButton(data.id);
  } else {
    nextTick(() => {
      search();
    });
  }
}

function search(initPageNum = true) {
  tableRef.value.table.clearSelection();
  tableRef.value.reload({ ...searchState.data, groupId: tableId.value }, initPageNum);
  simeRef.value.getData(tableId.value);
}
function reset() {
  searchState.data = {
    groupId: "",
    name: "",
    description: "",
    parseType: "",
    codeType: "",
  };
  tableRef.value.table.clearSelection();
  search();
}
//删除，批量删除
function delFn(rows) {
  batchDelete().then(() => {
    let ids = rows.map((item) => item[idKey]);

    delItem(ids.join()).then(() => {
      isStart(rows);
    });
  });
}

let str = ref("");
function isStart(data) {
  let ruleIds = data.map((item) => {
    return {
      ruleId: item.id,
    };
  });
  ElMessageBox({
    title: "警告",
    message: h("p", null, [h("p", null, "是否立即启用"), h("p", { style: "color: #E6A23C" }, "注意：选择否，则需要手动重启监听器")]),
    showCancelButton: true,
    confirmButtonText: "是",
    cancelButtonText: "否",
    closeOnClickModal: false,
    showClose: false,
  })
    .then(() => {
      let resetParams = {
        parseRuleList: ruleIds,
      };

      /* 获取采集器 list - 遍历path */
      getReceiverRoute()
        .then((res) => {
          let sum = 0;
          str.value = "";
          res.data.forEach((item, index) => {
            /* 循环重启监听器
             * 成功：拼接成功 msg
             * 失败：拼接失败 msg
             * */
            restartReceiver(resetParams, item.path)
              .then((val) => {
                str.value += `<p>${item.name}: ${val.msg}</p>`;
              })
              .catch((err) => {
                str.value += `<p>${item.name}: ${err.msg}</p>`;
              })
              .finally((fval) => {
                sum += 1;
                if (res.data.length === sum && str.value) {
                  /* 提示信息 */
                  ElMessageBox.alert("<div style='max-height:400px;overflow:auto;'>" + str.value + "</div>", "提示", {
                    confirmButtonText: "关闭",
                    dangerouslyUseHTMLString: true,
                  });
                }
              });
          });
        })
        .then(() => {
          /* 执行完毕 */
          tableRef.value.table.clearSelection();
          search(false);
        });
    })
    .catch(() => {
      tableRef.value.table.clearSelection();
      search(false);
    });
}

//修改规则状态
function updateStatusFn(row) {
  let prevenable = row.status == 1 ? 0 : 1;
  let text = prevenable == 0 ? "启动" : "停止";
  ElMessageBox.confirm("确认要将解析规则" + row.name + text + "吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let params = {
        id: row["id"],
        status: prevenable == 0 ? 1 : 0,
      };
      updateStatus(params).then(() => {
        ElMessage({
          type: "success",
          message: text + "成功",
        });
        search(false);
      });
    })
    .catch(function () {
      row.status = row.status == 0 ? 1 : 0;
    });
}

//条件语句使用 移动
let filterList = computed(() => {
  return simeRef.value ? simeRef.value.getTreeData() : [];
});
//移动后刷新列表和分组
function moveUpdate() {
  search(false);
  simeRef.value.getData(tableId.value);
  tableRef.value.table.clearSelection();
}

//复制
function copyItem(data) {
  router.push({
    name: "EditAnalyticRule",
    params: {
      id: data.id,
    },
    query: {
      groupId: state.currentGroup.id,
      copy: "true",
    },
  }); //路由跳转
}

// 新增
function newlyAdded() {
  router.push({
    name: "AddAnalyticRule",
    query: {
      groupId: state.currentGroup.id,
    },
  }); //路由跳转
}

//导入 导出
let importType = ref("overwrite");
let importPassword = ref("");
let exportParams = ref({
  isAll: true,
  password: "",
});
let exportDialogRef = ref();
function openExportDialog() {
  exportDialogRef.value.open();
  showPassword.value = false;
  exportParams.value = { isAll: true, password: "" };
}
function resetUploadParams() {
  importType.value = "overwrite";
  importPassword.value = "";
  showPassword.value = false;
}
function exportData(close, load) {
  let url = "/config/parseRule/export";
  if (!exportParams.value.isAll) {
    if (multipleSelection.value.length == 0) {
      ElMessage.warning("请先选择数据");
      return;
    }
    exportParams.value.ids = multipleSelection.value.map((item) => item.id).join();
  }
  load();

  download(url, "分析规则.zip", exportParams.value, "post").finally(() => {
    close();
    tableRef.value.table.clearSelection();
  });
}
let showPassword = ref(false);
function changeShowPassword(val) {
  if (!val) {
    exportParams.value.password = "";
    importPassword.value = "";
  }
}

defineExpose({
  simeRef: computed(() => simeRef.value),
});
let { defaultProps, add_item_data, table_data, currentGroup } = toRefs(state);
</script>

<style lang="scss" scoped></style>
