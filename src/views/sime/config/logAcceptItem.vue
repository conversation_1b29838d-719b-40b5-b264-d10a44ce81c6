<template>
  <!-- 采集器 -列表 -->
  <div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="getList" @reset="reset">
      <el-button @click="newlyAdded" class="search-button" v-hasPermi="'receiver:server:add'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增监听器
      </el-button>
    </common-search>
    <div class="bg-new pb15-new">
      <xel-table ref="tableRef" :columns="columns" :data="tabData" @selection-change="handleSelectionChange" :checkbox="false" :pagination="false">
        <template #status="scope" v-hasPermi="'receiver:server:start' || 'receiver:server:close'">
          <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" @change="update_statu(scope.row)"> </el-switch>
        </template>
      </xel-table>
      <!-- 分页  -->
      <xel-pagination
        ref="paginationRef"
        class="xel-table-pagination m0-new"
        :total="total"
        :page-size="10"
        :normalPage="false"
        @change="changePagination"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: "LogAccept",
};
</script>
<script setup>
import { getLogList, logStart, logClose, remove } from "@/api/sime/config/logAcceptState";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onActivated, watch } from "vue";

let props = defineProps({
  /* 参数 -需要拼接的url */
  urlPath: {
    type: String,
    default() {
      return "";
    },
  },
});

onActivated(() => {
  getList();
});
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
import { useStore } from "vuex";

const store = useStore();

let tableRef = ref();
let paginationRef = ref();
let dialogRef = ref();
let searchState = reactive({
  data: {
    name: "",
    type: "",
    status: "",
  },
  menuData: [
    {
      lable: "协议类型：",
      prop: "jobGroup",
      options: [],
      dictName: "receiver_server_type",
      sime: true,
    },
    {
      lable: "监听器状态：",
      prop: "status",
      options: [],
      dictName: "receiver_server_status",
      sime: true,
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "监听器名称",
    },
  ],
});

// 搜查重置方法
function reset() {
  searchState.data = {
    pageNum: 1,
    pageSize: 10,
    name: "",
    type: "",
    status: "",
  };
  paginationRef.value.resetPageNum();
  getList();
}

let tabData = ref([]);
let total = ref(0);

/* 动态获取当前采集器数据 */
const getList = () => {
  getLogList(searchState.data, props.urlPath)
    .then((res) => {
      total.value = res.data.total;
      tabData.value = res.data.rows;
    })
    .catch(() => {
      tabData.value = [];
    });
};
/* 分页 */
const changePagination = (params) => {
  searchState.data.pageNum = params.pageNum;
  searchState.data.pageSize = params.pageSize;
  getList();
};

watch(
  () => props.urlPath,
  (val) => {
    if (val) getList();
  },
  {
    immediate: true,
  }
);

// 列表配置项
const columns = [
  {
    prop: "status",
    label: "",
    slotName: "status",
  },
  {
    prop: "name",
    label: "监听器",
    click(scope) {
      editButton(scope.row.id);
    },
  },
  {
    prop: "typeText",
    label: "协议",
  },
  {
    prop: "port",
    label: "监听端口",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "MoreFilled",
        hasPermi: "receiver:server:query",
        title: "状态监控",
        onClick(scope) {
          moreFilled(scope.row);
        },
      },
      {
        icon: "delete",
        hasPermi: "receiver:server:remove",
        title: "删除",
        onClick(scope) {
          batchDelete(scope.row.id);
        },
      },
    ],
  },
];

// 列表操作方法
// 修改状态
function update_statu(row) {
  ElMessageBox.confirm("是否确认修改该数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(function () {
      let API = row.status == "1" ? logStart : logClose;
      API({ id: row.id }, props.urlPath)
        .then((res) => {
          ElMessage({
            type: "success",
            message: "修改成功",
          });
        })
        .finally(() => {
          getList();
        });
    })
    .catch(() => {
      row.status === "1" ? (row.status = "0") : (row.status = "1");
    });
}

// 删除
function batchDelete(arr) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      remove({ ids: arr }, props.urlPath).then(() => {
        ElMessage.success("删除成功");
        getList();
        //删除打开的tab编辑页面
        store.commit("closeTabById", arr);
      });
    })
    .catch(() => {});
}
// 新增监听器按钮
function newlyAdded() {
  router.push({
    name: "Management",
    params: {
      urlPath: props.urlPath,
    },
  });
}
// 编辑按钮
function editButton(val) {
  router.push({ name: "EditListener", params: { id: val, urlPath: props.urlPath } });
}
// 监控状态
function moreFilled(val) {
  router.push({ name: "Monitoring", params: { id: val.id, name: val.name, urlPath: props.urlPath } });
}
</script>
<style scoped lang="scss"></style>
