<template>
  <!-- syslog转发配置 -->
  <sime-layout
    :load-data="getSyslogTreeData"
    :defaultProps="defaultProps"
    @addNode="addNode"
    :table_data="table_data"
    :node_item_data="add_item_data"
    ref="simeRef"
    :moduleTyp="'syslog'"
  />
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import {
  getSyslogTreeData,
  deleteSyslogGroup,
  getSyslogGroup,
  editSyslogroup,
  addSyslogGroup,
  getSyslogList,
  delInfoSyslogData,
} from "@/api/sime/config/syslog";
let simeRef = ref();
let state = reactive({
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {
    //搜索相关
    searchState: {
      data: {
        groupId: "",
        name: "",
        ip: "",
        isAvailable: null,
      },
      menuData: [
        {
          lable: "可用状态:",
          prop: "isAvailable",
          sime: true,
          dictName: "config_alertRelaySyslog_isAvailable",
        },
      ],
      formList: [
        {
          formType: "input",
          prop: "name",
          label: "配置名称",
        },
        {
          formType: "input",
          prop: "ip",
          label: "目标地址",
        },
      ],
    },
    columns: [
      {
        prop: "name",
        label: "配置名称",
      },
      {
        prop: "description",
        label: "配置描述",
      },
      {
        prop: "isAvailableText",
        label: "可用状态",
      },
      {
        prop: "ip",
        label: "目的地址",
      },

      {
        label: "操作",
        fixed: "right",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "edit",
            title: "修改",
            hasPermi: "alertRelay:syslog:update",
            onClick(scope) {
              simeRef.value.tableListRef.editData(scope.row);
            },
          },
          {
            icon: "delete",
            title: "删除",
            hasPermi: "alertRelay:syslog:delete",
            onClick(scope) {
              simeRef.value.tableListRef.delData(scope.row);
            },
          },
        ],
      },
    ],
    table_item_list: {
      specialTable: true,
      addSpecialName: "addSyslog",
      editSpecialName: "editSyslog",
      delItem: delInfoSyslogData,
      add_form_list: [],
    },
    getTableData: getSyslogList,
  },
  add_item_data: {
    isCheckName: true,
    delport: deleteSyslogGroup,
    editport: editSyslogroup,
    addport: addSyslogGroup,
    detailport: getSyslogGroup,
    nodeeDialogTitle: "syslog转发分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
    ],
  },
});
// 添加节点
function addNode(data) {}
let { defaultProps, add_item_data, table_data } = toRefs(state);
</script>

<style lang="scss" scoped></style>
