<template>
  <sime-layout
    :load-data="getTreeData"
    :defaultProps="defaultProps"
    @addNode="addNode"
    :table_data="table_data"
    :node_item_data="add_item_data"
    ref="simeRef"
    :moduleTyp="'alarmDataManage'"
  ></sime-layout>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import {
  getTreeData,
  delAlertTree,
  getAlertTree,
  editAlertTree,
  addAlertTree,
  getTableAlert,
  getAlertInfo,
  delAlertInfo,
  editAlertInfo,
  addAlertInfo,
  testAlertInfo,
} from "@/api/sime/config/alarm";
let simeRef = ref();
let state = reactive({
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {
    //搜索相关
    searchState: {
      data: {
        groupId: "",
        name: "",
        ip: "",
        port: null,
        username: "",
        isAvailable: null,
      },
      menuData: [
        {
          lable: "可用状态:",
          prop: "isAvailable",
          sime: true,
          dictName: "config_alertRelayDatasource_isAvailable",
        },
      ],
      formList: [
        {
          formType: "input",
          prop: "name",
          label: "名称",
        },
        {
          formType: "input",
          prop: "ip",
          label: "连接地址",
        },
        {
          formType: "number",
          prop: "port",
          label: "连接端口",
          vxRule: "Ints",
          max: 65535,
          precision: "0",
        },
        {
          formType: "input",
          prop: "username",
          label: "用户名",
        },
      ],
    },
    columns: [
      {
        prop: "name",
        label: "名称",
      },
      {
        prop: "description",
        label: "描述",
      },
      {
        prop: "ip",
        label: "连接地址",
      },
      {
        prop: "port",
        label: "连接端口",
      },
      {
        prop: "isAvailableText",
        label: "可用状态",
      },
      {
        label: "操作",
        fixed: "right",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "edit",
            title: "修改",
            hasPermi: "alertRelay:datasource:update",
            onClick(scope) {
              simeRef.value.tableListRef.editData(scope.row);
            },
          },
          {
            icon: "delete",
            title: "删除",
            hasPermi: "alertRelay:datasource:delete",
            onClick(scope) {
              simeRef.value.tableListRef.delData(scope.row);
            },
          },
        ],
      },
    ],
    table_item_list: {
      title_text: "数据源",
      delItem: delAlertInfo,
      updateItem: editAlertInfo,
      addItem: addAlertInfo,
      getDetail: getAlertInfo,
      testItem: testAlertInfo,
      form_data: {
        groupId: "",
        name: "",
        description: "",
        ip: "",
        port: undefined,
        username: "",
        password: "",
        db: "",
        isAvailable: 1,
      },
      add_form_list: [
        {
          formType: "input",
          type: "text",
          prop: "name",
          required: true,
          label: "名称",
        },
        {
          formType: "input",
          type: "textarea",
          prop: "description",
          label: "描述",
        },
        {
          formType: "input",
          type: "text",
          prop: "ip",
          label: "连接地址",
          required: true,
          vxRule: "IP",
        },
        {
          formType: "number",
          type: "text",
          prop: "port",
          label: "连接端口",
          required: true,
          vxRule: "Ints",
          min: 0,
          max: 65535,
          precision: "0",
        },
        {
          formType: "input",
          prop: "db",
          label: "数据库名称",
          required: true,
        },
        {
          formType: "input",
          type: "text",
          prop: "username",
          label: "用户名",
          required: true,
        },
        {
          formType: "input",
          type: "password",
          prop: "password",
          label: "密码",
          required: true,
          autocomplete: "new-password",
        },
        {
          formType: "radio",
          prop: "isAvailable",
          label: "可用状态",
          isNumber: true,
          required: true,
          // 字典关键字
          dictName: "config_alertRelayDatasource_isAvailable",
          sime: true,
        },
      ],
    },
    getTableData: getTableAlert,
  },
  add_item_data: {
    isCheckName: true,
    delport: delAlertTree,
    editport: editAlertTree,
    addport: addAlertTree,
    detailport: getAlertTree,
    nodeeDialogTitle: "数据源管理分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
    ],
  },
});
// 添加节点
function addNode(data) {}
let { defaultProps, add_item_data, table_data } = toRefs(state);
</script>

<style lang="scss" scoped></style>
