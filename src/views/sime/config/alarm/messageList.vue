<template>
  <sime-layout
    :load-data="getMqTreeData"
    :defaultProps="defaultProps"
    @addNode="addNode"
    :table_data="table_data"
    :node_item_data="add_item_data"
    ref="simeRef"
    :moduleTyp="'mqList'"
  ></sime-layout>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import { getMqTreeData, deletteMqGroup, getMqGroup, editMqroup, addMqGroup, getMqList, delInfoMqData } from "@/api/sime/config/alarm";
let simeRef = ref();
let state = reactive({
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {
    //搜索相关
    searchState: {
      data: {
        groupId: "",
        name: "",
        topic: "",
        isAvailable: null,
      },
      menuData: [
        {
          lable: "可用状态:",
          prop: "isAvailable",
          sime: true,
          dictName: "config_alertRelayDatasource_isAvailable",
        },
      ],
      formList: [
        {
          formType: "input",
          prop: "name",
          label: "配置名称",
        },
        {
          formType: "input",
          prop: "topic",
          label: "主题",
        },
      ],
    },
    columns: [
      {
        prop: "name",
        label: "配置名称",
      },
      {
        prop: "description",
        label: "配置描述",
      },
      {
        prop: "isAvailableText",
        label: "可用状态",
      },
      {
        prop: "topic",
        label: "主题",
      },

      {
        label: "操作",
        fixed: "right",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "edit",
            title: "修改",
            hasPermi: "alertRelay:mq:update",
            onClick(scope) {
              simeRef.value.tableListRef.editData(scope.row);
            },
          },
          {
            icon: "delete",
            title: "删除",
            hasPermi: "alertRelay:mq:delete",
            onClick(scope) {
              simeRef.value.tableListRef.delData(scope.row);
            },
          },
        ],
      },
    ],
    table_item_list: {
      specialTable: true,
      addSpecialName: "addEditMessage",
      editSpecialName: "editMessage",
      delItem: delInfoMqData,
      add_form_list: [],
    },
    getTableData: getMqList,
  },
  add_item_data: {
    isCheckName: true,
    delport: deletteMqGroup,
    editport: editMqroup,
    addport: addMqGroup,
    detailport: getMqGroup,
    nodeeDialogTitle: "消息队列分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
    ],
  },
});
// 添加节点
function addNode(data) {}
let { defaultProps, add_item_data, table_data } = toRefs(state);
</script>

<style lang="scss" scoped></style>
