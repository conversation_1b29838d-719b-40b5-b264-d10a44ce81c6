<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="数据源管理" name="first">
        <data-source :key="activeName"></data-source>
      </el-tab-pane>
      <el-tab-pane label="数据表配置" name="second">
        <alarm-list :key="activeName"></alarm-list>
      </el-tab-pane>
      <el-tab-pane label="消息队列" name="three">
        <message-list :key="activeName"></message-list>
      </el-tab-pane>
      <el-tab-pane label="syslog转发" name="syslog">
        <syslog-list :key="activeName"></syslog-list>
      </el-tab-pane>
    </el-tabs>
    <xel-dialog @addItem="$emit('addNode')"> </xel-dialog>
  </el-card>
</template>
<script>
export default {
  name: "Alarm",
};
</script>
<script setup>
import dataSource from "./dataSource.vue";
import alarmList from "./alarmList.vue";
import messageList from "./messageList.vue";
import syslogList from "./syslogList.vue";
import { ref, reactive, toRefs, onMounted, onActivated } from "vue";
import { getTreeData } from "@/api/sime/config/log";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
const route = useRoute();
let activeName = ref("first");
activeName.value = route.query.tabName ? route.query.tabName : "first";

function handleClick(val) {
  activeName.value = val.paneName;
}
</script>

<style lang="scss" scoped></style>
