<template>
  <!-- syslog转发配置 -->
  <el-card class="bg-p-border-new">
    <div class="top">
      <h3 class="conH3Tit">
        {{ state.pageTit }}
      </h3>
      <backbutton text="索引列表" :name="{ name: 'Alarm', tabName: 'syslog' }"></backbutton>
    </div>
    <el-form class="flex-form" :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
      <xel-form-item
        item-width="30%"
        class="item-space"
        v-for="(item, index) in formList"
        :key="index"
        v-model="formData[item.prop]"
        v-bind="item"
      ></xel-form-item>
      <el-form-item ref="searchRef" label-width="0" style="text-align: right; width: 100%">
        <el-button type="primary" @click="saveForm" style="ustify-content: flex-end" :loading="loading">编辑字段</el-button>
      </el-form-item>
    </el-form>
    <xel-table
      height="400"
      ref="tableRef"
      :columns="columns"
      :defaultParams="{ ...state.formData }"
      :load-data="null"
      :data="remainData"
      :pagination="false"
      rowKey="field"
    >
      <!-- 字段别名 -->
      <template #syslogField="scope">
        <el-input v-model="scope.row.syslogField"></el-input>
      </template>
    </xel-table>
    <xel-table height="200" ref="tableRef2" :columns="columns2" :load-data="null" :data="hasDelData" :pagination="false"></xel-table>
    <div style="text-align: right"><el-button type="primary" @click="submit" :loading="saveLoading">确定</el-button></div>
  </el-card>
</template>
<script>
export default {
  name: "AddEditSyslog",
};
</script>
<script setup>
import {
  getSyslogData as getTableData,
  addSyslogData as addItem,
  editSyslogData as updateItem,
  getSyslogTreeIndex,
  loadSyslogData,
  getFilesDicts,
} from "@/api/sime/config/syslog";
import { ref, reactive, toRefs, nextTick, watch } from "vue";
import { batchDelete } from "@/utils/delete";
import { getDictsData } from "@/utils/getDicts";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { useStore } from "vuex";
const store = useStore();
const route = useRoute();
const router = useRouter();
let loading = ref(false);
let saveLoading = ref(false);
let tableRef = ref();
let state = reactive({
  formData: {
    id: route.params.id,
    groupId: route.query.groupId,
    name: "",
    description: "",
    isAvailable: null,
    datasourceId: null,
    tableName: "",
    indexId: "",
    ip: "",
    port: "",
  }, //新增编辑表单
  pageTit: "",
  showData: {
    active: null,
    inactive: null,
  }, //是否展示
  stampData: {
    active: null,
    inactive: null,
  }, //是否时间戳
  aggsData: {
    active: null,
    inactive: null,
  }, //是否统计字段
  remainData: [],
  hasDelData: [], //可恢复数据，已删除
  options: [], //字典类型
});
let { formData, showData, stampData, options, aggsData, remainData, hasDelData, option } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  state.formData = {
    id: route.params.id,
    groupId: route.query.groupId,
    name: "",
    description: "",
    isAvailable: null,
    datasourceId: null,
    tableName: "",
    indexId: "",
    ip: "",
    port: "",
  };
}

search(route.params.id);
let editId = ref("");
// 如果是点击编辑进来，先获取数据
function search(id) {
  if (id != "add") {
    state.pageTit = "修改syslog转发配置";
    getTableData(id).then((res) => {
      state.formData = {
        id: route.params.id,
        groupId: route.query.groupId,
        name: res.data.name,
        description: res.data.description,
        isAvailable: res.data.isAvailable,
        datasourceId: res.data.datasourceId,
        tableName: res.data.tableName,
        indexId: res.data.indexId,
        ip: res.data.ip,
        port: res.data.port,
      };
      state.remainData = res.data.fields;
      state.hasDelData = [];
    });
  } else {
    state.pageTit = "新增syslog转发配置";
  }
}
// 拉取数据
function pullData() {
  loading.value = true;
  let params = {
    indexId: state.formData.indexId,
  };
  loadSyslogData(params)
    .then((res) => {
      ElMessage.success("操作成功");
      state.remainData = res.data;
      state.hasDelData = [];
      loading.value = false;
    })
    .finally(() => {
      loading.value = false;
    });
}
// 改变时间戳
function changeTimes(e, rows, index) {}
// 列表配置项
const columns = [
  {
    prop: "indexField",
    label: "索引字段",
  },
  {
    prop: "syslogField",
    label: "消息字段",
    slotName: "syslogField",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
];
// 已删除表格配置项
const columns2 = [
  {
    prop: "indexField",
    label: "索引字段",
  },
  {
    prop: "syslogField",
    label: "消息字段",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "RefreshLeft",
        title: "恢复",
        onClick(scope) {
          recoverFn(scope.row);
        },
      },
    ],
  },
];

//删除，批量删除
function delFn(rows) {
  state.hasDelData.push(rows);
  let index = state.remainData.indexOf(rows);
  state.remainData.splice(index, 1);
}
// 恢复表格
function recoverFn(rows) {
  if (rows.isTimeStamp == 1) {
    rows.isTimeStamp = 0;
  }
  state.remainData.push(rows);
  let index = state.hasDelData.indexOf(rows);
  state.hasDelData.splice(index, 1);
}
let ruleFormRef = ref();
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "配置名称",
    required: true,
  },
  {
    formType: "input",
    prop: "description",
    label: "配置描述",
  },
  {
    formType: "radio",
    prop: "isAvailable",
    label: "可用状态",
    // 字典关键字
    dictName: "config_alertRelaySyslog_isAvailable",
    isNumber: true,
    sime: true,
    required: true,
  },
  {
    formType: "input",
    prop: "ip",
    label: "目的地址",
    required: true,
  },
  {
    formType: "number",
    prop: "port",
    label: "目标端口",
    required: true,
    min: 0,
    max: 65535,
    precision: "0",
  },
  {
    formType: "tree",
    prop: "indexId",
    label: "索引",
    multiple: false,
    required: true,
    treeOptions: {
      loadData: getSyslogTreeIndex, //接口名称
      params: {},
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
  },
]);
// 弹框确定按钮
// 提交表单
function saveForm() {
  let validateList = [];
  ruleFormRef.value.validateField("indexId", (valid) => {
    validateList.push(valid);
  });
  if (validateList.every((item) => item === "")) {
    pullData();
  }
}
// 提交全部
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      let port = route.params.id == "add" ? addItem : updateItem;
      if (route.params.id == "add") {
        delete state.formData.id;
      }
      let params = {
        ...state.formData,
        fields: state.remainData,
      };
      port(params)
        .then((res) => {
          ElMessage.success("操作成功");
          resetFormData();
          search(route.params.id);
          saveLoading.value = false;
          store.commit("closeCurrentTab");
          router.push({
            name: "Alarm",
            query: {
              tabName: "syslog",
            },
          }); //路由跳转
        })
        .catch(() => {
          saveLoading.value = false;
        });
    } else {
      return false;
    }
  });
}
</script>

<style lang="scss" scoped>
.top {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
.tit {
  font-size: 16px;
  color: rgba(40, 51, 79, 1);
  line-height: 32px;
}
.item-space {
  padding-bottom: 3px;
  margin-right: 3%;
  border-bottom: 1px solid rgba(235, 237, 241, 1);
}
.flex-form {
  :deep .el-input__inner {
    border: none;
  }
  :deep .custom-select {
    border: none;
  }
  :deep .el-form-item__error {
    padding-top: 6px;
  }
}
</style>
