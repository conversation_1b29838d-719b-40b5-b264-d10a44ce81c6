<template>
  <el-card class="bg-p-border-new">
    <div class="top">
      <h3 class="conH3Tit">
        {{ state.pageTit }}
      </h3>

      <backbutton text="索引列表" :name="{ name: 'Alarm', tabName: 'second' }"></backbutton>
    </div>
    <el-form class="flex-form" :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
      <xel-form-item
        item-width="30%"
        class="item-space"
        v-for="(item, index) in formList"
        :key="index"
        v-model="formData[item.prop]"
        v-bind="item"
      ></xel-form-item>
      <el-form-item ref="searchRef" label-width="0" style="text-align: right; width: 100%">
        <el-button type="primary" @click="saveForm" style="ustify-content: flex-end" :loading="loading">编辑字段</el-button>
      </el-form-item>
    </el-form>
    <xel-table
      height="400"
      ref="tableRef"
      :columns="columns"
      :defaultParams="{ ...state.formData }"
      :load-data="null"
      :data="remainData"
      :pagination="false"
      rowKey="field"
    >
      <!-- 字段别名 -->
      <template #indexField="scope">
        <el-select
          v-if="options.length > 0 && formData.indexId"
          :placeholder="'请选择字典'"
          style="width: 100%"
          v-model="scope.row.indexField"
          filterable
        >
          <el-option v-for="item in options" :key="item.field" :label="item.alias" :value="item.field"></el-option>
        </el-select>
        <el-input v-else v-model="scope.row.indexField"></el-input>
      </template>
    </xel-table>
    <xel-table height="200" ref="tableRef2" :columns="columns2" :load-data="null" :data="hasDelData" :pagination="false"></xel-table>
    <div style="text-align: right"><el-button type="primary" @click="submit" :loading="saveLoading">确定</el-button></div>
  </el-card>
</template>
<script>
export default {
  name: "AddEditAlarm",
};
</script>
<script setup>
import {
  getAlertRelayDataTree as getTree,
  getAlertRelay as getTableData,
  addlertRelay as addItem,
  editAlertRelay as updateItem,
  getIndexTree,
  getRelayData,
  getFilesDicts,
} from "@/api/sime/config/alarm.js";
import { ref, reactive, toRefs, nextTick, watch } from "vue";
import { batchDelete } from "@/utils/delete";
import { getDictsData } from "@/utils/getDicts";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { useStore } from "vuex";
const store = useStore();
const route = useRoute();
const router = useRouter();
let tableRef = ref();
let loading = ref(false);
let saveLoading = ref(false);
let state = reactive({
  formData: {
    id: route.params.id,
    groupId: route.query.groupId,
    name: "",
    description: "",
    isAvailable: null,
    datasourceId: null,
    tableName: "",
    indexId: "",
  }, //新增编辑表单
  pageTit: "",
  showData: {
    active: null,
    inactive: null,
  }, //是否展示
  stampData: {
    active: null,
    inactive: null,
  }, //是否时间戳
  aggsData: {
    active: null,
    inactive: null,
  }, //是否统计字段
  remainData: [],
  hasDelData: [], //可恢复数据，已删除
  options: [], //字典类型
});
let { formData, showData, stampData, options, aggsData, remainData, hasDelData, option } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  state.formData = {
    id: route.params.id,
    groupId: route.query.groupId,
    name: "",
    description: "",
    isAvailable: null,
    datasourceId: null,
    tableName: "",
    indexId: "",
  };
}

search(route.params.id);
let editId = ref("");
// 如果是点击编辑进来，先获取数据
function search(id) {
  if (id != "add") {
    state.pageTit = "修改数据表配置";
    getTableData(id).then((res) => {
      state.formData = {
        id: route.params.id,
        groupId: route.query.groupId,
        name: res.data.name,
        description: res.data.description,
        isAvailable: res.data.isAvailable,
        datasourceId: res.data.datasourceId,
        tableName: res.data.tableName,
        indexId: res.data.indexId,
      };
      state.remainData = res.data.fields;
      state.hasDelData = [];
    });
  } else {
    state.pageTit = "新增数据表配置";
  }
}
// 拉取数据
function pullData() {
  loading.value = true;
  if (route.params.id == "add") {
    delete state.formData.id;
  }
  let params = {
    tableName: state.formData.tableName,
    datasourceId: state.formData.datasourceId,
  };
  getRelayData(params)
    .then((res) => {
      ElMessage.success("操作成功");
      state.remainData = res.data;
      state.hasDelData = [];
    })
    .finally(() => {
      loading.value = false;
    });
}
// 改变时间戳
function changeTimes(e, rows, index) {
  console.log("index");
}
// 列表配置项
const columns = [
  {
    prop: "tableField",
    label: "告警表字段",
  },
  {
    prop: "indexField",
    label: "索引字段",
    slotName: "indexField",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
];
// 已删除表格配置项
const columns2 = [
  {
    prop: "tableField",
    label: "告警表字段",
  },
  {
    prop: "indexField",
    label: "索引字段",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "RefreshLeft",
        title: "恢复",
        onClick(scope) {
          recoverFn(scope.row);
        },
      },
    ],
  },
];

//删除，批量删除
function delFn(rows) {
  let name = options.value.length > 0 ? options.value.find((ele) => ele.field === rows.indexField).alias : "";
  if (options.value.length > 0) {
    rows.indexField = name;
  }
  state.hasDelData.push(rows);
  let index = state.remainData.indexOf(rows);
  state.remainData.splice(index, 1);
}
// 恢复表格
function recoverFn(rows) {
  if (rows.isTimeStamp == 1) {
    rows.isTimeStamp = 0;
  }
  state.remainData.push(rows);
  let index = state.hasDelData.indexOf(rows);
  state.hasDelData.splice(index, 1);
}
let ruleFormRef = ref();
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "配置名称",
    required: true,
  },
  {
    formType: "input",
    prop: "description",
    label: "配置描述",
  },
  {
    formType: "radio",
    prop: "isAvailable",
    label: "可用状态",
    // 字典关键字
    dictName: "config_logStoreDatasource_isAvailable",
    isNumber: true,
    sime: true,
    required: true,
  },
  {
    formType: "tree",
    prop: "indexId",
    label: "索引",
    multiple: false,
    treeOptions: {
      loadData: getIndexTree, //接口名称
      params: {},
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
  },
  {
    formType: "tree",
    prop: "datasourceId",
    label: "数据源",
    required: true,
    multiple: false,
    treeOptions: {
      loadData: getTree, //接口名称
      params: {},
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
  },

  {
    formType: "input",
    prop: "tableName",
    label: "告警表名称",
    required: true,
  },
]);
// 弹框确定按钮
// 提交表单
function saveForm() {
  loading.value = false;
  let validateList = [];
  ruleFormRef.value.validateField(["tableName", "datasourceId"], (valid) => {
    validateList.push(valid);
  });
  if (validateList.every((item) => item === "")) {
    pullData();
  }
}
// 提交全部
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      let port = route.params.id == "add" ? addItem : updateItem;
      if (route.params.id == "add") {
        delete state.formData.id;
      }
      let params = {
        ...state.formData,
        fields: state.remainData,
      };
      port(params)
        .then((res) => {
          ElMessage.success("操作成功");
          resetFormData();
          search(route.params.id);
          saveLoading.value = false;
          store.commit("closeCurrentTab");
          router.push({
            name: "Alarm",
            query: {
              tabName: "second",
            },
          }); //路由跳转
        })
        .catch(() => {
          saveLoading.value = false;
        });
    } else {
      return false;
    }
  });
}
// 获取字典项
// 是否展示
function sortFn(source) {
  // 排序
  const sortArr = ["1", "0"];
  const result = source.sort((item1, item2) => {
    const index1 = sortArr.findIndex((sortItem) => {
      return item1.value === sortItem;
    });
    const index2 = sortArr.findIndex((sortItem) => {
      return item2.value === sortItem;
    });
    if (index1 !== -1 && index2 !== -1) {
      if (index1 > index2) {
        return 1;
      } else if (index1 < index2) {
        return -1;
      }
    } else {
      if (index1 > index2) {
        return -1;
      } else if (index1 < index2) {
        return 1;
      }
    }
  });
  return result;
}
watch(
  () => state.formData.indexId,
  (val) => {
    if (val) {
      getDataDicts(val);
    }
  }
);
// 获取字段项
function getDataDicts(val) {
  getFilesDicts(val)
    .then((res) => {
      state.options = res.data;
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped>
.top {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
.tit {
  font-size: 16px;
  color: rgba(40, 51, 79, 1);
  line-height: 32px;
}
.item-space {
  padding-bottom: 3px;
  margin-right: 3%;
  border-bottom: 1px solid rgba(235, 237, 241, 1);
}
.flex-form {
  :deep .el-input__inner {
    border: none;
  }
  :deep .custom-select {
    border: none;
  }
  :deep .el-form-item__error {
    padding-top: 6px;
  }
}
</style>
