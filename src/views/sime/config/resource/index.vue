<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane lazy label="时间资源" name="first">
        <time-resource></time-resource>
      </el-tab-pane>
      <el-tab-pane lazy label="地址资源" name="four">
        <ip></ip>
      </el-tab-pane>
      <el-tab-pane lazy label="端口资源" name="second">
        <port></port>
      </el-tab-pane>
      <el-tab-pane lazy label="自定义资源" name="three">
        <custom></custom>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "AnalyseResource",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import ip from "./ip.vue";
import port from "./port.vue";
import timeResource from "./time.vue";
import custom from "./custom.vue";
const route = useRoute();
let activeName = ref("first");
activeName.value = route.query.tabName ? route.query.tabName : "first";

function handleClick(val) {
  activeName.value = val.paneName;
}
</script>

<style lang="scss" scoped></style>
