<template>
  <component
    :is="isDialog ? xelDialog : divComponent"
    size="large"
    class="dialogWrapper"
    :title="dialogTit + '资源内容'"
    ref="dialogChildRef"
    @submit="submitForm"
    @close="closeDialog(false)"
    :ishiddenDialog="!isDialog"
  >
    <el-form ref="childFormRef" :model="childFormData" label-width="100px" label-position="left" :rules="rules">
      <el-row :gutter="10" v-for="(item, index) in formList" :key="index">
        <el-col :span="1"
          ><el-radio-group v-model="childFormData.radioValue">
            <el-radio :label="item.radioValue">{{ "" }} </el-radio>
          </el-radio-group></el-col
        >

        <el-col :span="21"> <xel-form-item v-model="childFormData[item.prop]" v-bind="item"></xel-form-item> </el-col>
        <el-col :span="1">
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-model="checkList" :label="item.prop">取反</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
      <el-row :gutter="10" v-for="(item, index) in formList3" :key="index">
        <el-col :span="1"
          ><el-radio-group v-model="childFormData.radioValue">
            <el-radio :label="item.radioValue">{{ "" }} </el-radio>
          </el-radio-group></el-col
        >

        <el-col :span="11"> <xel-form-item v-model="childFormData[item.prop]" v-bind="item"></xel-form-item> </el-col>
        <el-col :span="10">
          <el-time-picker
            is-range
            v-model="childFormData[item.time]"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
            value-format="HH:mm:ss"
          >
          </el-time-picker>
        </el-col>
        <el-col :span="1">
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-model="checkList" :label="item.prop">取反</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
      <el-row gutter="10">
        <el-col :span="1">
          <el-radio-group v-model="childFormData.radioValue">
            <el-radio label="week">{{ "" }} </el-radio>
          </el-radio-group>
        </el-col>
        <el-col :span="7">
          <el-form-item label="每周">
            <el-select v-model="childFormData.startWeek" placeholder="开始周">
              <el-option v-for="wItem in weekList" :key="wItem.value" :value="wItem.value" :label="wItem.label">{{ wItem.label }}</el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="" label-width="0px">
            <el-select v-model="childFormData.endWeek" placeholder="结束周">
              <el-option v-for="wItem in weekList" :key="wItem.label" :value="wItem.value" :label="wItem.label">{{ wItem.label }}</el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-time-picker
            is-range
            v-model="childFormData.weekTime"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
            value-format="HH:mm:ss"
          >
          </el-time-picker>
        </el-col>
        <el-col :span="1">
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-model="checkList" label="week">取反</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
      <el-row :gutter="10" v-for="(item, index) in formList2" :key="index">
        <el-col :span="1">
          <el-radio-group v-model="childFormData.radioValue">
            <el-radio :label="item.radioValue">{{ "" }} </el-radio>
          </el-radio-group></el-col
        >

        <el-col :span="21"> <xel-form-item v-model="childFormData[item.prop]" v-bind="item"></xel-form-item> </el-col>
        <el-col :span="1">
          <el-checkbox-group v-model="checkList">
            <el-checkbox v-model="checkList" :label="item.prop">取反</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
    </el-form>
  </component>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import divComponent from "./divComponent.vue";
import xelDialog from "@/xelComponents/xelDialog.vue";
import { updateTime } from "@/api/sime/config/resource";
import { ElMessageBox, ElMessage } from "element-plus";
//定义props属性
const props = defineProps({
  isDialog: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    default: "",
  },
  content: {
    type: Array,
    default: [],
  },
  childData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

// 打开弹框
let dialogChildRef = ref();
let childFormRef = ref();
let dialogTit = ref("添加");

let emits = defineEmits(["close"]);
let ishiddenDialog = ref(false);

let childFormData = reactive({
  radioValue: "time",
  day: [],
  time: [],
  year: [],
  yearTime: [],
  monthTime: [],
  month: [],
  week: [],
});

// 取反
let checkList = ref([]);
let weekList = reactive([
  { value: "1", label: "星期一" },
  { value: "2", label: "星期二" },
  { value: "3", label: "星期三" },
  { value: "4", label: "星期四" },
  { value: "5", label: "星期五" },
  { value: "6", label: "星期六" },
  { value: "7", label: "星期日" },
]);

let formList = reactive([
  {
    formType: "daterange",
    type: "datetimerange",
    prop: "time",
    label: "时间",
    require: true,
    radioValue: "time",
    info: "",
    onChange(val) {
      if (val) {
        childFormData["time"] = val;
      } else {
        childFormData["time"] = [];
      }
    },
  },
]);
// 每年 每月 日期
let formList3 = reactive([
  {
    formType: "daterange",
    prop: "year",
    time: "yearTime",
    label: "每年",
    require: !props.isDialog,
    format: "MM-DD",
    valueFormat: "MM-DD",
    radioValue: "year",
    info: "",
    onChange(val) {
      if (val) {
        childFormData["year"] = val;
      } else {
        childFormData["year"] = [];
      }
    },
  },
  {
    formType: "daterange",
    prop: "month",
    time: "monthTime",
    label: "每月",
    require: !props.isDialog,
    format: "DD",
    valueFormat: "DD",
    radioValue: "month",
    info: "",
    onChange(val) {
      if (val) {
        childFormData["month"] = val;
      } else {
        childFormData["month"] = [];
      }
    },
  },
]);

let formList2 = [
  {
    formType: "time",
    isRange: true,
    prop: "day",
    label: "每天",
    require: !props.isDialog,
    format: "HH:mm:ss",
    valueFormat: "HH:mm:ss",
    radioValue: "day",
    info: "",
  },
];

// 提交
function submitForm(close, loading) {
  let content = formateData();

  // 新增 添加，编辑 修改对应的数据 删除 减一条

  if (content.length == 0) {
    ElMessage.warning("请填写全部资源内容");
    return false;
  }
  let result = props.childData["contentJson"];

  if (props.childData.type == "edit") {
    result[props.childData.index] = content;
  } else {
    result.push(content);
  }

  let params = {
    id: props.childData.id,
    name: props.childData.name,
    description: props.childData.description,
    contentJson: result,
  };
  console.log(params, "params");

  childFormRef.value.validate((valid) => {
    if (valid) {
      console.log("valid");
      loading();
      let addFn = updateTime;
      addFn(params)
        .then((res) => {
          if (res.code == 200) {
            ElMessage.success("操作成功");
            checkList.value = [];
            emits("close", "save");
            close();
          }
        })
        .catch(() => {
          close(false);
        });
    } else {
      console.log("12333");
      return false;
    }
  });
}
// 取消
function closeDialog() {
  emits("close");
}

function validateData() {
  let contentJson = [];

  childFormRef.value.validate((valid) => {
    if (valid) {
      checkList.value = [];
      let result = formateData();
      if (result.length == 0) {
        contentJson = [];
      } else {
        contentJson.push(result);
      }
    } else {
      return false;
    }
  });

  return contentJson;
}

function formateData() {
  // isWhole 代表时间是否选择完整，isExist：是否选择

  let isExist = true;
  let radioValue = childFormData.radioValue;

  let content = {
    type: radioValue,
    operator: "equal",
    start: "",
    end: "",
    stime: "",
    etime: "",
  };

  switch (radioValue) {
    case "time":
      delete content.start;
      delete content.end;
      if (childFormData.time && childFormData.time.length > 0) {
        content.stime = childFormData.time[0];
        content.etime = childFormData.time[1];
      }

      // 取反
      if (checkList.value.includes("time")) {
        content.operator = "nequal";
      }
      isExist = true;
      if (!content.stime || !content.etime) {
        isExist = false;
      }
      break;
    case "year":
      if (childFormData.year && childFormData.year.length > 0) {
        content.start = childFormData.year[0];
        content.end = childFormData.year[1];
      }
      if (childFormData.yearTime && childFormData.yearTime.length > 0) {
        content.stime = childFormData.yearTime[0];
        content.etime = childFormData.yearTime[1];
      }

      // 取反
      if (checkList.value.includes("year")) {
        content.operator = "nequal";
      }
      if (!content.start || !content.end || !content.stime || !content.etime) {
        isExist = false;
      }
      break;
    case "month":
      if (childFormData.month && childFormData.month.length > 0) {
        content.start = childFormData.month[0];
        content.end = childFormData.month[1];
      }
      if (childFormData.monthTime && childFormData.monthTime.length > 0) {
        content.stime = childFormData.monthTime[0];
        content.etime = childFormData.monthTime[1];
      }

      // 取反
      if (checkList.value.includes("month")) {
        content.operator = "nequal";
      }

      if (!content.start || !content.end || !content.stime || !content.etime) {
        isExist = false;
      }

      break;
    case "week":
      content.start = childFormData.startWeek;
      content.end = childFormData.endWeek;

      if (childFormData.weekTime && childFormData.weekTime.length > 0) {
        content.stime = childFormData.weekTime ? childFormData.weekTime[0] : "";
        content.etime = childFormData.weekTime ? childFormData.weekTime[1] : "";
      }

      // 取反

      if (checkList.value.includes("week")) {
        content.operator = "nequal";
      }
      console.log(!content.start || !content.end || !content.stime || !content.etime);
      console.log((content, "content"));
      if (!content.start || !content.end || !content.stime || !content.etime) {
        isExist = false;
      }
      break;
    case "day":
      delete content.start;
      delete content.end;
      if (childFormData.day && childFormData.day.length > 0) {
        content.stime = childFormData.day[0];
        content.etime = childFormData.day[1];
      }

      // 取反
      if (checkList.value.includes("day")) {
        content.operator = "nequal";
      }
      if (!content.stime || !content.etime) {
        isExist = false;
      }
      break;
  }
  console.log(isExist);
  return isExist ? content : [];
}
function changeRadio() {
  childFormRef.value && childFormRef.value.clearValidate && childFormRef.value.clearValidate();
}
onMounted(() => {
  changeRadio();
  if (props.isDialog) {
    dialogChildRef.value.open();
  }
  // 回显数据
  if (props.childData.type == "edit") {
    dialogTit.value = "修改";
    let contJson = props.childData["contentJson"];
    childFormData.radioValue = contJson.type; //单选
    if (contJson.operator == "nequal") checkList.value.push(contJson.type); //取反

    switch (contJson.type) {
      case "time":
        childFormData.time[0] = contJson.stime;
        childFormData.time[1] = contJson.etime;
        break;
      case "year":
        childFormData.year[0] = contJson.start;
        childFormData.year[1] = contJson.end;
        childFormData.yearTime[0] = contJson.stime;
        childFormData.yearTime[1] = contJson.etime;
        break;
      case "month":
        childFormData.month[0] = contJson.start;
        childFormData.month[1] = contJson.end;
        childFormData.monthTime[0] = contJson.stime;
        childFormData.monthTime[1] = contJson.etime;
        break;
      case "week":
        childFormData.startWeek = contJson.start;
        childFormData.endWeek = contJson.end;
        childFormData.weekTime = [contJson.stime, contJson.etime];

        break;
      case "day":
        childFormData.day[0] = contJson.stime;
        childFormData.day[1] = contJson.etime;

        break;
    }
  }
});

//父组件可以调用的方法
defineExpose({
  validateData,
  submitForm,
});
</script>

<style lang="scss" scoped>
.dialogWrapper {
  margin: 0 40px;
}
.txt {
  display: inline-block;
  font-size: 12px;
  color: #b9b9b9;
  line-height: 32px;
}
.text-right {
  width: 100%;
  float: right;
}
.el-selet {
  width: 100%;
}
</style>
