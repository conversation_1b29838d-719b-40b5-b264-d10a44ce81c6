<template>
  <div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <el-button @click="newlyAdded" class="search-button">
        <el-icon :size="12">
          <plus />
        </el-icon>
        {{ addName }}
      </el-button>
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="listUrl" @selection-change="handleSelectionChange">
      <template #expand="{ row }">
        <xel-table
          ref="tableChildRef"
          class="gray-table"
          :columns="contentColumns"
          :data="childFn(row)"
          :pagination="false"
          :default-params="{ pageSize: 200, pageNum: 1, dictType: row.dictType }"
          :checkbox="false"
        >
          <template #dictAction="scope">
            <xel-handle-btns :scope="scope" class="row-action" :btn-list="getDictgBtnList(scope)"></xel-handle-btns>
          </template>
        </xel-table>
      </template>
      <template #action="scope">
        <xel-handle-btns :scope="scope" class="row-action" :btn-list="getBtnList(scope)"></xel-handle-btns>
      </template>
    </xel-table>
    <!-- 弹窗内容 -->
    <xel-dialog :title="dialogTitle" ref="dialogRef" size="large" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
        <el-row :gutter="10" v-for="(item, index) in formList" :key="index">
          <el-col :span="item.fullGrid ? 24 : 16">
            <xel-form-item v-model="formData[item.prop]" v-bind="item"></xel-form-item>
          </el-col>
          <el-col :span="item.fullGrid ? 0 : 8">
            <span class="txt">{{ item.info }}</span>
          </el-col>
          <span class="txt text-right" v-if="item.fullGrid">{{ item.info }}</span>
        </el-row>
        <p v-if="type != 'Time'" class="content">{{ hideCon ? "修改" : "添加" }}资源内容</p>

        <time-child-form
          v-if="type == 'Time' && !hideCon"
          :isDialog="false"
          :type="type"
          :childData="state.formData"
          @close="closeDialog"
          ref="timeChildRef"
        ></time-child-form>
        <ip-child-form
          v-if="type != 'Time'"
          :isDialog="false"
          :type="type"
          :childData="state.formData"
          :isMultiple="hideCon"
          @close="closeDialog"
          ref="childRef"
        ></ip-child-form>
      </el-form>
    </xel-dialog>
    <ip-child-form
      v-if="type != 'Time' && addContent"
      :type="type"
      :childData="state.formData || { content: [] }"
      @close="closeDialog"
    ></ip-child-form>
    <time-child-form v-if="type == 'Time' && addContent" :type="type" :childData="state.formData" @close="closeDialog"></time-child-form>
  </div>
</template>
<script setup>
import ipChildForm from "./ipChildForm.vue";
import timeChildForm from "./timeChildForm.vue";
import { listRole, updateRole as updateItem, delRole as delItem } from "@/api/system/role";
import {
  getIpList,
  addIp,
  viewIP,
  updateIp,
  delIp,
  getPortList,
  addPort,
  viewPort,
  updatePort,
  delPort,
  getCustomList,
  addCustom,
  viewCustom,
  updateCustom,
  delCustom,
  getTimeList,
  addTime,
  viewTime,
  updateTime,
  delTime,
} from "@/api/sime/config/resource";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onMounted } from "vue";
import { batchDelete } from "@/utils/delete";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const router = useRouter();
const props = defineProps({
  type: {
    type: String,
    default: "",
  },
});
let hideCon = ref(false);
let childRef = ref();
// 时间子组件
let timeChildRef = ref();
let idKey = "id";
let tableRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    name: "",
    description: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "名称",
    },
    {
      formType: "input",
      prop: "description",
      label: "描述",
    },
  ],
});

// 子组件数据
let childFormData = ref({});

// 处理时间表格
function formateTime(data) {
  let name = "";
  let start = "";
  let end = "";
  let stime = "";
  let etime = "";
  let operator = data.operator == "nequal" ? "!" : "";

  switch (data.type) {
    case "year":
      name = "每年";
      start = data.start;
      end = data.end;
      stime = data.stime;
      etime = data.etime;
      break;
    case "month":
      name = "每月";
      start = data.start;
      end = data.end;
      stime = data.stime;
      etime = data.etime;
      break;
    case "week":
      name = "每周";
      start = data.start;
      end = data.end;
      stime = data.stime;
      etime = data.etime;
      break;
    case "day":
      name = "每天";
      start = data.stime;
      end = data.etime;
      break;
    case "time":
      name = "时间段";
      start = data.stime;
      end = data.etime;
      break;
  }
  let result = operator + " " + start + " 至 " + end;
  let result2 = operator + " " + name + " " + start + " 至 " + end;
  let result3 = operator + " " + name + " " + start + " 至 " + end + " 的 每天" + stime + " 至 " + etime;

  return data.type == "time" ? result : data.type == "day" ? result2 : result3;
}
let childList = ref([]);
// 处理子列表数据
function childFn(rows) {
  let arr = [];
  if (props.type == "Time") {
    // 时间处理
    arr = JSON.parse(rows.content);
  } else {
    arr = rows.content.split(";");
  }

  let result = arr.map((item, index) => {
    return {
      id: rows.id,
      index: index,
      contentJson: item,
      content: props.type == "Time" ? formateTime(item) : item,
    };
  });

  childList.value = result;
  return result;
}

function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    value: "",
  };
  search();
}
//搜索结束

let state = reactive({
  formData: {}, //新增编辑表单
  multipleSelection: [],
});
let { formData } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  state.formData = {
    name: "",
    description: "",
    content: [],
  };
}

let addName = ref("");
let addTxt = "资源";
let listUrl = ref(); //列表接口地址
let addUrl = ref(); //新增接口地址
let viewUrl = ref(); //查看接口
let updateUrl = ref(); //更新接口
let delUrl = ref(); //删除接口
// 列表配置项
const columns = [
  {
    type: "expand",
    slotName: "expand",
  },
  {
    prop: "name",
    label: "资源名称",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "createByName",
    label: "创建者",
  },
  // {
  //   prop: "description",
  //   label: "类型",
  // },

  {
    label: "操作",
    width: "180px",
    slotName: "action",
  },
];
function getBtnList(scope) {
  return [
    {
      icon: "plus",
      title: "新增",
      onClick() {
        addEditContent(scope.row, "add");
      },
    },
    {
      icon: "edit",
      title: "修改",
      onClick() {
        dialogTitle.value = "修改" + addName.value;
        modifyButton(scope.row[idKey]);
      },
    },
    {
      icon: "delete",
      title: "删除",

      onClick() {
        delFn(scope.row);
      },
    },
  ];
}
// 资源内容列表
const contentColumns = [
  {
    prop: "",
    label: "",
    width: "50px",
  },
  {
    prop: "content",
    label: "资源内容",
  },
  {
    prop: "content",
    label: "操作",
    slotName: "dictAction",
    width: "125px",
  },
];
function getDictgBtnList(scope) {
  return [
    {
      icon: "edit",
      title: "修改",
      onClick() {
        addEditContent(scope.row, "edit");
      },
    },
    {
      icon: "delete",
      title: "删除",
      onClick() {
        delContent(scope.row);
      },
    },
  ];
}
switch (props.type) {
  case "Ip":
    addName.value = "地址" + addTxt;
    listUrl.value = getIpList;
    addUrl.value = addIp;
    viewUrl.value = viewIP;
    updateUrl.value = updateIp;
    delUrl.value = delIp;
    break;
  case "Port":
    addName.value = "端口" + addTxt;
    listUrl.value = getPortList;
    addUrl.value = addPort;
    viewUrl.value = viewPort;
    updateUrl.value = updatePort;
    delUrl.value = delPort;
    break;
  case "Custom":
    addName.value = "自定义" + addTxt;
    listUrl.value = getCustomList;
    addUrl.value = addCustom;
    viewUrl.value = viewCustom;
    updateUrl.value = updateCustom;
    delUrl.value = delCustom;
    break;
  case "Time":
    addName.value = "时间" + addTxt;
    listUrl.value = getTimeList;
    addUrl.value = addTime;
    viewUrl.value = viewTime;
    updateUrl.value = updateTime;
    delUrl.value = delTime;
    break;
  default:
    addName.value = "地址" + addTxt;
    listUrl.value = getIpList;
    addUrl.value = addIp;
    viewUrl.value = viewIP;
    updateUrl.value = updateIp;
    delUrl.value = delIp;
}
onMounted(() => {});

//删除，批量删除
function delFn(rows) {
  batchDelete().then(() => {
    delUrl.value(rows.id).then(() => {
      ElMessage.success("操作成功");
      search(false);
    });
  });
}
// 列表操作方法
// 新增按钮
function newlyAdded() {
  editId.value = "";
  hideCon.value = false;
  dialogTitle.value = "添加" + addName.value;
  resetFormData();
  popupBox();
}
let editId = ref("");

// 修改按钮
function modifyButton(id) {
  hideCon.value = true;
  editId.value = id;

  getDetail(id, "mod");
}

// 获取资源详情

function getDetail(id, type) {
  viewUrl.value(id).then(({ data }) => {
    for (let key in data) {
      state.formData[key] = data[key];
      state.formData.allContent = data["content"].split(";").map((item) => item);
      // 时间资源处理

      if (props.type == "Time") {
        state.formData.contentJson = JSON.parse(data["content"]);
      }
      if (type == "mod") {
        state.formData.edit = true;

        state.formData.content = data["content"].split(";").map((item) => item);
        popupBox();
      }
    }
  });
}
// 新增资源内容
let addContent = ref(false);

// 新增修改删除资源内容
function addEditContent(data, type) {
  getDetail(data.id);
  for (let key in data) {
    state.formData[key] = data[key];
  }

  state.formData.type = type;
  /*console.log(state.formData, "formData");*/
  addContent.value = true;
}
// 删除资源内容

function delContent(data) {
  getDetail(data.id);
  batchDelete().then(() => {
    if (props.type == "Time") {
      state.formData.contentJson.splice(data.index, 1);
    } else {
      state.formData.allContent.splice(data.index, 1);
    }

    let params = {
      id: state.formData.id,
      name: state.formData.name,
      description: state.formData.description,
      content: state.formData.allContent.join(";"),
      contentJson: state.formData.contentJson,
    };
    if (props.type == "Time") {
      delete params.content;
    }

    if (props.type == "Time") {
      if (params.contentJson.length == 0) {
        ElMessage.success("资源内容必须有一条");
        return;
      }
    } else if (!params.content) {
      ElMessage.success("资源内容必须有一条");
      return;
    }

    updateUrl
      .value(params)
      .then((res) => {
        if (res.code == 200) {
          ElMessage.success("操作成功");
          search();
        }
      })
      .catch(() => {});
  });
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}

// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "资源名称",
    maxLength: 64,
    required: true,
    info: "（资源名称不能超过64个字符）",
  },
  {
    formType: "input",
    type: "textarea",
    prop: "description",
    label: "资源描述",
    maxLength: 128,
    fullGrid: true,
    info: "（资源描述不能超过128个字符）",
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let content = "";
      // if (!hideCon.value) {
      content = props.type == "Time" ? timeChildRef.value.validateData() : childRef.value.validateData();
      /*if (!content || content.length == 0) {*/
      if (content === "" || content === undefined) {
        if (props.type == "Time") {
          ElMessage.warning("请填写全部资源内容");
        }

        return false;
      } else if (!content) {
        return false;
      }
      // } else {
      // content = state.formData.content.join(";");
      // }
      let params = {
        ...state.formData,
        content: content,
        contentJson: [],
      };

      if (props.type == "Time") {
        delete params.content;
        // 编辑时，后台返回字符串
        params.contentJson = typeof content === "string" ? JSON.parse(content) : content;
      } else {
        delete params.contentJson;
      }

      loading();
      let addFn = editId.value ? updateUrl.value : addUrl.value;
      addFn(params)
        .then((res) => {
          if (res.code == 200) {
            ElMessage.success("操作成功");

            search(true);
            close();
          }
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog(type) {
  addContent.value = false;
  resetFormData();
  if (type == "save") {
    search();
  }
}
</script>
<style scoped lang="scss">
.txt {
  display: inline-block;
  font-size: 12px;
  color: #b9b9b9;
  line-height: 32px;
}
.text-right {
  width: 100%;
  float: right;
}
.content {
  color: #303133;
  font-size: 16px;
  margin: 20px;
}
</style>
