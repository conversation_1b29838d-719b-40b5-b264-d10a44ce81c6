<template>
  <component
    :is="isDialog ? xelDialog : divComponent"
    size="large"
    class="dialogWrapper"
    :title="isDialog ? dialogTit + '资源内容' : ''"
    ref="dialogChildRef"
    @submit="submitForm"
    @close="closeDialog(false)"
    :ishiddenDialog="!isDialog"
  >
    <el-form
      ref="childFormRef"
      :model="childFormData"
      label-width="100px"
      label-position="left"
      v-if="type == 'Ip'"
      :rules="rules"
      @change="changeRadio"
    >
      <el-row :gutter="10">
        <el-radio-group v-model="childFormData.radioValue" v-if="childData.type == 'edit'">
          <el-radio label="1">{{ "" }} </el-radio>
        </el-radio-group>
        <el-col :span="childData.type != 'edit' ? 22 : 15">
          <!-- ip地址--添加多个 -->
          <el-form-item label="ip地址：" v-if="childData.type != 'edit'">
            <section class="add-section">
              <template v-for="(item, index) in addressIPs" :key="index">
                <section class="flex">
                  <el-input v-model="item.address" class="input-item"></el-input>
                  <section class="buts">
                    <el-button
                      size="small"
                      style="padding-left: 10px"
                      circle
                      :disabled="addressIPs.length == 1"
                      @click="deleteItem(index, 'addressIPs')"
                      ><el-icon><Delete /></el-icon
                    ></el-button>
                    <el-button
                      size="small"
                      style="padding-left: 10px"
                      circle
                      class="addBut"
                      v-show="addressIPs.length == 1 || addressIPs.length - 1 == index"
                      @click="addItem('addressIPs')"
                      ><el-icon><Plus /></el-icon
                    ></el-button>
                  </section>
                </section>
              </template>
            </section>
          </el-form-item>
          <!-- ip地址--添加单个 -->
          <el-form-item v-else label="ip地址：" prop="address">
            <el-input v-model="childFormData.address" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 地址范围--添加多个 -->
      <template v-if="childData.type != 'edit'">
        <el-row :gutter="10">
          <!-- <el-radio-group v-model="childFormData.radioValue" v-if="childData.type == 'edit'">
            <el-radio label="2">{{ "" }} </el-radio>
          </el-radio-group> -->
          <el-col :span="22">
            <el-form-item label="地址范围：">
              <section class="add-section">
                <template v-for="(item, index) in ipRangeList" :key="index">
                  <section class="flex" style="margin-bottom: 10px">
                    <el-input v-model="item.ipStart" style="width: 33%" />
                    <span class="separate" style="margin: 0 15px"> - </span>
                    <el-input v-model="item.ipEnd" style="width: 33%" />
                    <el-checkbox
                      v-model="item.checkList"
                      label="取反"
                      :style="childData.type == 'add' ? { marginLeft: '17px' } : { marginLeft: '12px' }"
                    ></el-checkbox>
                    <section class="buts">
                      <el-button
                        size="small"
                        style="padding-left: 10px"
                        circle
                        :disabled="ipRangeList.length == 1"
                        @click="deleteItem(index, 'ipRangeList')"
                        ><el-icon><Delete /></el-icon
                      ></el-button>
                      <el-button
                        size="small"
                        style="padding-left: 10px"
                        circle
                        class="addBut"
                        v-show="ipRangeList.length == 1 || ipRangeList.length - 1 == index"
                        @click="addItem('ipRangeList')"
                        ><el-icon><Plus /></el-icon
                      ></el-button>
                    </section>
                  </section>
                </template>
              </section>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <!-- 地址范围--添加单个 -->
      <template v-else>
        <el-row :gutter="10">
          <el-radio-group v-model="childFormData.radioValue" v-if="childData.type == 'edit'">
            <el-radio label="2">{{ "" }} </el-radio>
          </el-radio-group>
          <el-col :span="15">
            <el-row>
              <el-col :span="14">
                <el-form-item label="地址范围：" prop="ipStart"><el-input v-model="childFormData.ipStart" /> </el-form-item>
              </el-col>
              <el-col :span="1"> <span class="separate"> - </span></el-col>
              <el-col :span="9">
                <el-form-item label="" prop="ipEnd" label-width="0px"> <el-input v-model="childFormData.ipEnd" /> </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="2">
            <el-checkbox v-model="checkList" label="取反"></el-checkbox>
          </el-col>
          <el-col :span="6">
            <!-- <span class="txt">(格式为：***********:*************)</span> -->
          </el-col>
        </el-row>
      </template>
      <!-- 子网地址--添加多个 -->
      <template v-if="childData.type != 'edit'">
        <el-row :gutter="10">
          <!-- <el-radio-group v-model="childFormData.radioValue">
            <el-radio label="3">{{ "" }} </el-radio>
          </el-radio-group> -->
          <el-col :span="22">
            <el-form-item label="子网地址：">
              <section class="add-section">
                <template v-for="(item, index) in subNets" :key="index">
                  <section class="flex" style="margin-bottom: 10px">
                    <el-input v-model="item.subNet" style="width: 38%" />
                    <span class="separate" style="margin: 0 12px"> / </span>
                    <el-input-number v-model="item.subNetNum" :min="1" :max="128" controls-position="right" size="small" style="width: 38%" />
                    <section class="buts">
                      <el-button size="small" style="padding-left: 10px" circle :disabled="subNets.length == 1" @click="deleteItem(index, 'subNets')"
                        ><el-icon><Delete /></el-icon
                      ></el-button>
                      <el-button
                        size="small"
                        style="padding-left: 10px"
                        circle
                        class="addBut"
                        v-show="subNets.length == 1 || subNets.length - 1 == index"
                        @click="addItem('subNets')"
                        ><el-icon><Plus /></el-icon
                      ></el-button>
                    </section>
                  </section>
                </template>
              </section>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <!-- 子网地址--添加单个 -->
      <template v-else>
        <el-row :gutter="10">
          <el-radio-group v-model="childFormData.radioValue">
            <el-radio label="3">{{ "" }} </el-radio>
          </el-radio-group>
          <el-col :span="15">
            <el-row>
              <el-col :span="14">
                <el-form-item label="子网地址：" prop="subNet"><el-input v-model="childFormData.subNet" /> </el-form-item>
              </el-col>
              <el-col :span="1"> <span class="separate"> / </span></el-col>
              <el-col :span="9">
                <el-form-item label="" prop="subNetNum" label-width="0px">
                  <el-input-number v-model="childFormData.subNetNum" :min="1" :max="128" controls-position="right" size="small" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>

          <!-- <el-col :span="8"> -->
          <!-- <span class="txt">(子网格式为：***********/24))</span> -->
          <!-- </el-col> -->
        </el-row>
      </template>
    </el-form>
    <el-form
      ref="portFormRef"
      :model="childFormData"
      label-width="100px"
      label-position="left"
      v-if="type == 'Port'"
      :rules="rules"
      @change="changeRadio"
      @submit.native.prevent
    >
      <el-row :gutter="10">
        <el-radio-group v-model="childFormData.portRadio" v-if="childData.type == 'edit'">
          <el-radio label="p1">{{ "" }} </el-radio>
        </el-radio-group>
        <el-col :span="childData.type != 'edit' ? 22 : 15">
          <!-- 端口--添加多个 -->
          <el-form-item label="端口：" v-if="childData.type != 'edit'">
            <section class="add-section">
              <template v-for="(item, index) in portList" :key="index">
                <section class="flex">
                  <el-input-number
                    v-model="item.port"
                    class="input-item"
                    :min="0"
                    :max="65535"
                    controls-position="right"
                    size="small"
                    :precision="0"
                  />
                  <section class="buts">
                    <el-button size="small" style="padding-left: 10px" circle :disabled="portList.length == 1" @click="deleteItem(index, 'portList')"
                      ><el-icon><Delete /></el-icon
                    ></el-button>
                    <el-button
                      size="small"
                      style="padding-left: 10px"
                      circle
                      class="addBut"
                      v-show="portList.length == 1 || portList.length - 1 == index"
                      @click="addItem('portList')"
                      ><el-icon><Plus /></el-icon
                    ></el-button>
                  </section>
                </section>
              </template>
            </section>
          </el-form-item>
          <!-- 端口--添加单个 -->
          <el-form-item v-else label="端口：" prop="port">
            <el-input-number v-model="childFormData.port" :min="0" :max="65535" controls-position="right" size="small" :precision="0" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 端口范围--添加多个 -->
      <template v-if="childData.type != 'edit'">
        <el-row :gutter="10">
          <!-- <el-radio-group v-model="childFormData.portRadio">
            <el-radio label="p2">{{ "" }} </el-radio>
          </el-radio-group> -->
          <el-col :span="22">
            <el-form-item label="端口范围：">
              <section class="add-section">
                <template v-for="(item, index) in portRange" :key="index">
                  <section class="flex" style="margin-bottom: 10px">
                    <el-input-number
                      v-model="item.portStart"
                      style="width: 33%"
                      :min="0"
                      :max="65535"
                      controls-position="right"
                      size="small"
                      :precision="0"
                    />
                    <span class="separate" style="margin: 0 14px"> - </span>
                    <el-input-number
                      v-model="item.portEnd"
                      style="width: 33%"
                      :min="0"
                      :max="65535"
                      controls-position="right"
                      size="small"
                      :precision="0"
                    />
                    <el-checkbox
                      v-model="item.checkList"
                      label="取反"
                      :style="{
                        marginLeft: isDialog ? '20px' : '16px',
                      }"
                    ></el-checkbox>
                    <section class="buts">
                      <el-button
                        size="small"
                        style="padding-left: 10px"
                        circle
                        :disabled="portRange.length == 1"
                        @click="deleteItem(index, 'portRange')"
                        ><el-icon><Delete /></el-icon
                      ></el-button>
                      <el-button
                        size="small"
                        style="padding-left: 10px"
                        circle
                        class="addBut"
                        v-show="portRange.length == 1 || portRange.length - 1 == index"
                        @click="addItem('portRange')"
                        ><el-icon><Plus /></el-icon
                      ></el-button>
                    </section>
                  </section>
                </template>
                <span class="txt">(端口号范围：0-65535)</span>
              </section>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <!-- 端口范围--添加单个 -->
      <template v-else>
        <el-row :gutter="10">
          <el-radio-group v-model="childFormData.portRadio">
            <el-radio label="p2">{{ "" }} </el-radio>
          </el-radio-group>
          <el-col :span="15">
            <el-row>
              <el-col :span="14">
                <el-form-item label="端口范围：" prop="portStart">
                  <el-input-number v-model="childFormData.portStart" :min="0" :max="65535" controls-position="right" size="small" :precision="0" />
                </el-form-item>
              </el-col>
              <el-col :span="1"> <span class="separate"> - </span></el-col>
              <el-col :span="9">
                <el-form-item label="" prop="portEnd" label-width="0px">
                  <el-input-number v-model="childFormData.portEnd" :min="0" :max="65535" controls-position="right" size="small" :precision="0" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="2">
            <el-checkbox v-model="checkList" label="取反"></el-checkbox>
          </el-col>
          <el-col :span="6">
            <span class="txt">(端口号范围：0-65535)</span>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <el-form
      ref="customFormRef"
      :model="childFormData"
      label-width="140px"
      label-position="left"
      v-if="type == 'Custom'"
      :rules="rules"
      @change="changeRadio"
    >
      <el-row :gutter="10">
        <!-- <el-radio-group v-model="childFormData.customRadio">
          <el-radio label="c1">{{ "" }} </el-radio>
        </el-radio-group> -->
        <el-col :span="childData.type != 'edit' ? 21 : 15">
          <!-- 自定义资源字符串--添加多个 -->
          <el-form-item v-if="childData.type != 'edit'" label="自定义资源字符串:">
            <section class="add-section">
              <template v-for="(item, index) in customChartList" :key="index">
                <section class="flex">
                  <el-input
                    maxlength="64"
                    class="input-item"
                    style="width: 75%"
                    v-model="item.customChart"
                    @input="changeChart($event, index, 'type')"
                  ></el-input>
                  <section class="buts">
                    <el-button
                      size="small"
                      style="padding-left: 10px"
                      circle
                      :disabled="customChartList.length == 1"
                      @click="deleteItem(index, 'customChartList')"
                      ><el-icon><Delete /></el-icon
                    ></el-button>
                    <el-button
                      size="small"
                      style="padding-left: 10px"
                      circle
                      class="addBut"
                      v-show="customChartList.length == 1 || customChartList.length - 1 == index"
                      @click="addItem('customChartList')"
                      ><el-icon><Plus /></el-icon
                    ></el-button>
                  </section>
                </section>
              </template>
              <span class="txt">(长度不能超过64个字符)</span>
              <el-input class="input-false" style="width: 30px" v-model="valueFalse"></el-input>
            </section>
          </el-form-item>
          <!-- 自定义资源字符串--添加单个 -->
          <el-form-item v-else label="自定义资源字符串:" prop="customChart">
            <el-input maxlength="64" v-model="childFormData.customChart" @input="changeChart"></el-input>
          </el-form-item>
        </el-col>
        <template v-if="childData.type == 'edit'">
          <el-col :span="6">
            <span class="txt">(长度不能超过64个字符)</span>
            <el-input class="input-false" style="width: 30px" v-model="valueFalse"></el-input>
          </el-col>
        </template>
      </el-row>
    </el-form>
  </component>
</template>
<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import divComponent from "./divComponent.vue";
import xelDialog from "@/xelComponents/xelDialog.vue";
import { updateIp, updatePort, updateCustom } from "@/api/sime/config/resource";
import { compareIP, comparePort } from "@/xelComponents/utils/common";
import { ElMessageBox, ElMessage } from "element-plus";

//定义props属性
const props = defineProps({
  isDialog: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    default: "",
  },
  content: {
    type: Array,
    default: [],
  },
  childData: {
    type: Object,
    default: () => {
      return {};
    },
  },
  isMultiple: {
    type: Boolean,
    default: false,
  }, //是否是总编辑
});
let emits = defineEmits(["close"]);

let checkList = ref(false);
let childFormRef = ref();
let portFormRef = ref();
let customFormRef = ref();
// 打开弹框
let dialogChildRef = ref();

onMounted(() => {});

const valueFalse = ref(""); //不显示，用于自定义资源只有一个输入框，按回车时页面刷新
let ishiddenDialog = ref(false);

let childFormData = reactive({
  radioValue: "1", //地址
  portRadio: "p1", // 端口
  customRadio: "c1", //自定义资源radio
});
// ip地址
const addressIPs = ref([
  {
    address: "",
  },
]);
// 地址范围
const ipRangeList = ref([getFormDefault("ipRangeList")]);
// 子网地址
const subNets = ref([getFormDefault("subNets")]);
// 端口
const portList = ref([
  {
    port: undefined,
  },
]);
function getFormDefault(type) {
  if (type == "portRange") {
    return {
      portStart: undefined,
      portEnd: undefined,
      checkList: false,
    };
  } else if (type == "ipRangeList") {
    return {
      ipStart: "",
      ipEnd: "",
      checkList: false,
    };
  } else if (type == "subNets") {
    return {
      subNet: "",
      subNetNum: undefined,
    };
  }
}
// 端口范围
const portRange = ref([getFormDefault("portRange")]);
// 自定义资源字符串
const customChartList = ref([
  {
    customChart: "",
  },
]);
function changeRadio() {
  childFormRef.value && childFormRef.value.clearValidate && childFormRef.value.clearValidate();
}

watch(
  () => props.content,
  (val) => {}
);

// 添加单条数据
function addItem(type) {
  if (type === "addressIPs") {
    addressIPs.value.push({
      address: "",
    });
  } else if (type === "ipRangeList") {
    ipRangeList.value.push(getFormDefault("ipRangeList"));
  } else if (type === "subNets") {
    subNets.value.push(getFormDefault("subNets"));
  } else if (type === "portRange") {
    portRange.value.push(getFormDefault("portRange"));
  } else if (type === "portList") {
    portList.value.push({
      port: undefined,
    });
  } else if (type === "customChartList") {
    customChartList.value.push({
      customChart: "",
    });
  }
}
// 删除单条数据
function deleteItem(index, type) {
  if (type === "addressIPs") {
    addressIPs.value.splice(index, 1);
  } else if (type === "ipRangeList") {
    ipRangeList.value.splice(index, 1);
  } else if (type === "subNets") {
    subNets.value.splice(index, 1);
  } else if (type === "portRange") {
    portRange.value.splice(index, 1);
  } else if (type === "portList") {
    portList.value.splice(index, 1);
  } else if (type === "customChartList") {
    customChartList.value.splice(index, 1);
  }
}
function changeChart(e, index, type) {
  if (type) {
    customChartList.value[index].customChart = e.replace(";", "");
  } else {
    childFormData.customChart = e.replace(";", "");
  }
}
// 验证

// ip地址是否正确
const validateIp = (rule, value, callback) => {
  let radio = childFormData.radioValue;
  if (radio != "1") {
    callback();
  } else {
    const ipRule =
      /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;
    if (value && value.length > 0) {
      if (!ipRule.test(value)) {
        callback(new Error("请输入合法的IPv4或IPv6"));
      } else {
        callback();
      }
    } else {
      callback(new Error("请填写IP地址"));
    }
  }
};

// ip地址范围是否正确
const validateIpStr = (rule, value, callback) => {
  let radio = childFormData.radioValue;

  if (radio != "2") {
    callback();
  } else {
    const ipRule =
      /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;

    if (value && value.length > 0) {
      if (!ipRule.test(value)) {
        callback(new Error("请输入合法的IPv4或IPv6"));
      } else {
        if (compareIP(childFormData.ipStart, childFormData.ipEnd) > 0) {
          // callback(new Error("结束IP必须大于开始IP"));
          callback();
        } else {
          callback();
        }
      }
    } else {
      callback(new Error("请填写地址范围"));
    }
  }
};
// 子网地址是否正确
const validateSubNet = (rule, value, callback) => {
  let radio = childFormData.radioValue;

  if (radio != "3") {
    callback();
  } else {
    const ipRule =
      /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;
    if (value && value.length > 0) {
      if (!ipRule.test(value)) {
        callback(new Error("请输入合法的IPv4或IPv6"));
      } else {
        callback();
      }
    } else {
      callback(new Error("请填写子网地址"));
    }
  }
};
const validateSubNum = (rule, value, callback) => {
  let radio = childFormData.radioValue;
  if (radio != "3") {
    callback();
  } else {
    if (!value || value.length == 0) {
      callback(new Error("请填写子网段"));
    } else {
      callback();
    }
  }
};
const validatePort = (rule, value, callback) => {
  let radio = childFormData.portRadio;

  if (radio != "p1") {
    callback();
  } else {
    /*if (!value || value.length == 0) {*/
    if (value === undefined) {
      callback(new Error("请填写端口"));
    } else {
      callback();
    }
  }
};

const validatePorts = (rule, value, callback) => {
  let radio = childFormData.portRadio;

  if (radio != "p2") {
    callback();
  } else {
    /*if (!value) {*/
    if (value === undefined) {
      callback(new Error("请填写端口"));
    } else {
      if (comparePort(childFormData.portStart, childFormData.portEnd) > 0) {
        callback(new Error("结束端口必须大于开始端口"));
      } else {
        callback();
      }
    }
  }
};

// 自定义资源
const validateCustomChart = (rule, value, callback) => {
  let radio = childFormData.customRadio;
  if (radio != "c1") {
    callback();
  } else {
    if (!value || value.length == 0) {
      callback(new Error("请填写自定义资源字符串"));
    } else {
      callback();
    }
  }
};
const rules = reactive({
  address: [{ validator: validateIp, trigger: "blur" }],
  ipStart: [{ validator: validateIpStr, trigger: ["blur"] }],
  ipEnd: [{ validator: validateIpStr, trigger: "blur" }],
  subNet: [{ validator: validateSubNet, trigger: "blur" }],
  subNetNum: [{ validator: validateSubNum, trigger: "blur" }],
  port: [{ validator: validatePort, trigger: ["blur", "change"] }],
  portStart: [{ validator: validatePorts, trigger: ["blur", "change"] }],
  portEnd: [{ validator: validatePorts, trigger: ["blur", "change"] }],
  customChart: [{ validator: validateCustomChart, trigger: "blur" }],
});

let updateUrl = ref(); //更新接口
let editId = ref("");
let content = "";
let dialogTit = ref("添加");
onMounted(() => {
  changeRadio();
  if (props.isDialog) {
    dialogChildRef.value.open();
  }
  // 取反是否勾选
  if (props.childData.type == "edit") {
    checkList.value = props.childData.content.includes("!") ? true : false;
  }

  props.childData.content = props.childData.content.includes("!") ? props.childData.content.replace("!", "") : props.childData.content;
  console.log("props.childData.content: 根据父组件传过来的值", props.childData.content);
  // 回显数据 根据父组件传过来的值
  switch (props.type) {
    case "Ip":
      let content = [];
      // 新增资源内容时content置为空

      if (props.childData.type == "edit") {
        dialogTit.value = "修改";
        content = props.childData.content.includes("!") ? props.childData.content.replace("!", "") : props.childData.content;
      } else if (props.isMultiple) {
        addressIPs.value = [];
        ipRangeList.value = [];
        subNets.value = [];
        const allList = props.childData?.allContent;
        for (let i = 0; i < allList.length; i++) {
          if (allList[i].includes("-")) {
            ipRangeList.value.push({
              ipStart: allList[i].split("-")[0].includes("!") ? allList[i].split("-")[0].replace("!", "") : allList[1].split("-")[0],
              ipEnd: allList[i].split("-")[1],
              checkList: allList[i].includes("!") ? true : false,
            });
          } else if (allList[i].includes("/")) {
            subNets.value.push({
              subNet: allList[i].split("/")[0],
              subNetNum: Number(allList[i].split("/")[1]),
            });
          } else {
            addressIPs.value.push({
              address: allList[i],
            });
          }
        }
      } else {
        content = [];
      }
      if (ipRangeList.value.length == 0) {
        ipRangeList.value.push(getFormDefault("ipRangeList"));
      }
      if (subNets.value.length == 0) {
        subNets.value.push(getFormDefault("subNets"));
      }
      if (addressIPs.value.length == 0) {
        addressIPs.value.push({
          address: "",
        });
      }
      updateUrl.value = updateIp;

      childFormData.radioValue = content.includes("-") ? "2" : content.includes("/") ? "3" : "1";
      childFormData.radioValue == "1"
        ? (childFormData.address = content)
        : childFormData.radioValue == "2"
        ? ((childFormData.ipStart = content.split("-")[0]), (childFormData.ipEnd = content.split("-")[1]))
        : ((childFormData.subNet = content.split("/")[0]), (childFormData.subNetNum = Number(content.split("/")[1])));

      break;
    case "Port":
      let portContent = [];
      // 新增资源内容时content置为空

      if (props.childData.type == "edit") {
        dialogTit.value = "修改";
        portContent = props.childData.content;
      } else if (props.isMultiple) {
        // 修改地址资源
        portRange.value = [];
        portList.value = [];
        const allList = props.childData?.allContent;
        for (let i = 0; i < allList.length; i++) {
          if (allList[i].includes("-")) {
            portRange.value.push({
              portStart: allList[i].split("-")[0].includes("!") ? Number(allList[i].split("-")[0].replace("!", "")) : allList[i].split("-")[0],
              portEnd: Number(allList[i].split("-")[1]),
              checkList: allList[i].includes("!") ? true : false,
            });
          } else {
            portList.value.push({
              port: allList[i],
            });
          }
        }
        if (portRange.value.length == 0) {
          portRange.value.push(getFormDefault("portRange"));
        }
        if (portList.value.length == 0) {
          portList.value.push({
            port: undefined,
          });
        }
      } else {
        portContent = [];
      }

      updateUrl.value = updatePort;
      childFormData.portRadio = portContent.includes("-") ? "p2" : "p1";

      childFormData.portRadio == "p1"
        ? (childFormData.port = Number(portContent))
        : ((childFormData.portStart = Number(portContent.split("-")[0])), (childFormData.portEnd = Number(portContent.split("-")[1])));

      break;
    case "Custom":
      let customContent = "";
      // 新增资源内容时content置为空

      if (props.childData.type == "edit") {
        dialogTit.value = "修改";
        customContent = props.childData.content;
      } else if (props.isMultiple) {
        customChartList.value = props.childData.content.map((v) => {
          return {
            customChart: v,
          };
        });
      } else {
        customContent = "";
      }

      updateUrl.value = updateCustom;
      childFormData.customRadio = "c1";

      childFormData.customChart = customContent;

      break;
    case "Time":
      addName.value = "时间" + addTxt;
      url.value = listRole;
      break;
    default:
  }
});

// 提交
function submitForm(close, loading) {
  let content = props.childData.type == "edit" ? formateData() : formateDataMultiple();
  console.log("content: ", content);
  // 新增 添加，编辑 修改对应的数据 删除 减一条
  let allContent = props.childData.content.split(";");
  if (props.childData.type == "edit") {
    allContent[props.childData.index] = content; //编辑时单个修改
  } else {
    if (content) {
      allContent.push(content.join(";")); //新增时多个添加
    } else {
      return;
    }
  }

  let params = {
    id: props.childData.id,
    name: props.childData.name,
    description: props.childData.description,
    content: allContent.join(";"),
  };
  let formRef = props.type == "Port" ? portFormRef.value : props.type == "Custom" ? customFormRef.value : childFormRef.value;
  formRef.validate((valid) => {
    if (valid) {
      loading();
      let addFn = updateUrl.value;
      addFn(params)
        .then((res) => {
          if (res.code == 200) {
            ElMessage.success("操作成功");
            checkList.value = false;
            emits("close", "save");
            close();
          }
        })
        .catch(() => {
          close(false);
        });
    } else {
      /*console.log("12333");*/
      return false;
    }
  });
}
// 取消
function closeDialog() {
  emits("close");
}
//父组件获取的值，数组
function validateData() {
  let content = "";
  let formRef = props.type == "Port" ? portFormRef.value : props.type == "Custom" ? customFormRef.value : childFormRef.value;
  formRef.validate((valid) => {
    if (valid) {
      // checkList.value = false;
      content = props.childData.type == "edit" ? formateData() : formateDataMultiple(); //单个数据
      if (props.childData.type != "edit" && content) {
        content = content.join(";"); //新增时多个添加
      }
    } else {
      content = "";
      return false;
    }
  });
  return content;
}

// -----单个编辑时
function formateData() {
  let content = "";
  let radioValue = childFormData.radioValue;
  let portRadio = childFormData.portRadio;
  let customRadio = childFormData.customRadio;
  // ip
  switch (props.type) {
    case "Ip":
      content =
        radioValue == "1"
          ? childFormData.address
          : radioValue == "2"
          ? childFormData.ipStart + "-" + childFormData.ipEnd
          : childFormData.subNet + "/" + childFormData.subNetNum;
      // 取反

      if (radioValue == "2" && checkList.value) {
        content = "!" + content;
      }
      break;
    case "Port":
      content = portRadio == "p2" ? childFormData.portStart + "-" + childFormData.portEnd : childFormData.port;
      // 取反

      if (portRadio == "p2" && checkList.value) {
        content = "!" + content;
      }
      break;
    case "Custom":
      content = childFormData.customChart;
      break;
  }

  return content;
}
// -----多个新增时
function formateDataMultiple() {
  let content = [];
  let addressIPsArr, ipArr, subNetsArr, portArr, portRangeArr, customChart;
  let cleanedPort, cleanedPortRange, cleanedAddressIPs, cleanedIPs, cleanedSubNets;

  switch (props.type) {
    case "Ip":
      cleanedAddressIPs = addressIPs.value.filter((item) => item.address !== undefined && item.address !== ""); //
      cleanedIPs = ipRangeList.value.filter(
        (item) => item.ipStart !== undefined && item.ipStart !== "" && item.ipEnd !== undefined && item.ipEnd !== ""
      );
      cleanedSubNets = subNets.value.filter(
        (item) => item.subNet !== undefined && item.subNet !== "" && item.subNetNum !== undefined && item.subNetNum !== ""
      );
      if ([...cleanedAddressIPs, ...cleanedIPs, ...cleanedSubNets].length == 0) {
        ElMessage.warning("请填写资源内容");
        content = false;
      } else {
        addressIPsArr = processAndDeduplicateArray(addressIPs.value, "address", "ip地址");
        if (!addressIPsArr) {
          content = false;
        } else {
          ipArr = processRangeArray(ipRangeList.value, "ipStart", "ipEnd", "地址范围", "-", true);
          if (!ipArr) {
            content = false;
          } else {
            subNetsArr = processRangeArray(subNets.value, "subNet", "subNetNum", "子网地址", "/", true, false);
            if (!addressIPsArr || !ipArr || !subNetsArr) {
              content = false;
            } else {
              content = [...(addressIPsArr || []), ...(ipArr || []), ...(subNetsArr || [])];
            }
          }
        }
      }

      break;
    case "Port":
      cleanedPort = portList.value.filter((item) => item.port !== undefined && item.port !== ""); //
      cleanedPortRange = portRange.value.filter(
        (item) => item.portStart !== undefined && item.portStart !== "" && item.portEnd !== undefined && item.portEnd !== ""
      );
      if ([...cleanedPort, ...cleanedPortRange].length == 0) {
        ElMessage.warning("请填写资源内容");
        content = false;
      } else {
        portArr = processAndDeduplicateArray(portList.value, "port", "端口", false);
        portRangeArr = processRangeArray(portRange.value, "portStart", "portEnd", "端口范围", "-");
        if (!portArr || !portRangeArr) {
          content = false;
        } else {
          content = [...(portArr || []), ...(portRangeArr || [])];
        }
      }
      break;
    case "Custom":
      customChart = customChartList.value.filter((item) => item.customChart !== undefined && item.customChart !== ""); //
      if (customChart.length == 0) {
        ElMessage.warning("请填写资源内容");
        content = false;
      } else {
        content = processAndDeduplicateArray(customChartList.value, "customChart", "自定义资源字符串", false);
      }
      break;
  }

  return content;
}
//效验单个IP数据
function validateIP(ip) {
  const ipRule =
    /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^...$/; // 简化正则表达式展示
  return ipRule.test(ip);
}
//效验多个单数据
function processAndDeduplicateArray(arr, key, msg, isPpRule = true) {
  let areAllIPsValid = true; // 默认需要效验ip
  // 删除空数据值
  let cleanedArr = arr.filter((item) => item[key] !== undefined && item[key] !== ""); //
  if (cleanedArr.length == 0) {
    // ElMessage.warning("请填写" + msg);
    return [];
  } else {
    // 基于key的值进行去重
    let uniqueArr = [...new Set(cleanedArr.map((item) => item[key]))];
    if (uniqueArr.length !== cleanedArr.length) {
      ElMessage.warning(msg + "存在重复数据，请重新填写");
      return false;
    }
    // 判断是否是需要效验合法的IPv4或IPv6
    if (isPpRule) {
      areAllIPsValid = uniqueArr.every(function (item) {
        return validateIP(item);
      });
    }

    if (areAllIPsValid) {
      return uniqueArr;
    } else {
      ElMessage.warning(msg + "请输入合法的IPv4或IPv6");
      return false;
    }
  }
}

//效验多个范围内数据
function processRangeArray(arr, key1, key2, msg, symbol, isPpRule = false, ifShowKey2 = true) {
  // isPpRule 是否效验ip格式 ifShowKey2 不效验第二个key
  let areAllIPsValid = true; //
  let list = []; //储存范围拼接后的值
  let setList = []; //储存去重后范围拼接后的值
  let cleanedArr = arr.filter((item) => item[key1] !== undefined && item[key1] !== "" && item[key2] !== undefined && item[key2] !== ""); //
  if (cleanedArr.length == 0) {
    return [];
  }
  let startArr = [cleanedArr.map((item) => item[key1])]; //开始端口
  let endArr = [cleanedArr.map((item) => item[key2])]; //结束端口
  if (cleanedArr.length == 0) {
    return false;
  }
  if (startArr.length !== endArr.length) {
    ElMessage.warning("请填写完整" + msg);
    return false;
  } else {
    if (isPpRule) {
      areAllIPsValid = cleanedArr.every(function (i) {
        if (ifShowKey2) {
          return validateIP(i[key1]) && validateIP(i[key2]);
        } else {
          return validateIP(i[key1]);
        }
      });
    }
    if (!areAllIPsValid) {
      ElMessage.warning(msg + "请输入合法的IPv4或IPv6");
      return false;
    }
    list = cleanedArr.map((v) => {
      setList.push(v[key1] + symbol + v[key2]);
      if (v.checkList) {
        //有checkList字段且checkList长度大于0，取反
        return "!" + v[key1] + symbol + v[key2];
      } else {
        return v[key1] + symbol + v[key2];
      }
    });
  }
  let setLength = [...new Set(setList)].length;
  if (setLength != list.length) {
    ElMessage.warning(msg + "存在重复数据，请重新填写");
    return false;
  } else {
    return list;
  }
}

//父组件可以调用的方法
defineExpose({
  validateData,
  submitForm,
});
</script>

<style lang="scss" scoped>
.dialogWrapper {
  margin-left: 40px;
}
.txt {
  display: inline-block;
  font-size: 12px;
  color: #b9b9b9;
  line-height: 32px;
}
.text-right {
  width: 100%;
  float: right;
}
.separate {
  display: inline-block;
  margin: 6px 10px;
}
.add-section {
  .input-item {
    width: 80%;
    margin-bottom: 10px;
  }
  .buts {
    margin-left: 14px;
  }
}
.input-false {
  //透明度为0
  opacity: 0;
  margin-left: 10px;
}
</style>
