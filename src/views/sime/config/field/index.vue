<template>
  <el-card class="bg-p-border-new">
    <!-- 日志采集 - 字段配置 - 列表 -->
    <h3 class="conH3Tit mb20">{{ "" || ($route.meta && $route.meta.title) }}</h3>

    <el-form ref="ruleFormRef" :model="ruleForm">
      <div :style="tableHeight">
        <el-table
          :data="ruleForm.tableData"
          :border="parentBorder"
          ref="tableRef"
          height="100%"
          v-loading="loading"
          @sortChange="sortChange"
          row-key="orderNum"
          class-name="drag-index-table"
        >
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="字段名" prop="field">
            <template #default="scope">
              <el-form-item v-if="!scope.row.internal" :prop="'tableData.' + scope.$index + '.field'" :rules="rules.field">
                <el-input v-model="ruleForm.tableData[scope.$index].field" maxlength="20" placeholder="请输入字段名" />
              </el-form-item>
              <span v-else>{{ scope.row.field }}</span>
            </template>
          </el-table-column>
          <el-table-column label="字段别名" prop="fieldText">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.fieldText'" :rules="rules.fieldText">
                <el-input v-model="scope.row.fieldText" placeholder="请输入字段别名" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="字典" prop="dictType">
            <template #default="scope">
              <el-select
                :placeholder="'请选择字典'"
                style="width: 100%"
                v-model="scope.row.dictType"
                clearable
                filterable
                @change="changeDict(scope.row)"
              >
                <el-option v-for="item in options" :key="item.dictType" :label="item.dictName" :value="item.dictType"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="默认值" prop="defaultValue">
            <template #default="scope">
              <el-select
                v-if="scope.row.dictDataList && scope.row.dictDataList.length > 0"
                :placeholder="'请选择默认值'"
                style="width: 100%"
                v-model="scope.row.defaultValue"
                clearable
                filterable
              >
                <el-option v-for="item in scope.row.dictDataList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
              </el-select>
              <el-input v-else v-model="scope.row.defaultValue" placeholder="请输入默认值" />
            </template>
          </el-table-column>
          <el-table-column label="重要字段" prop="important">
            <template #default="scope">
              <el-select :placeholder="'请选择重要字段'" style="width: 100%" v-model="scope.row.important" clearable filterable>
                <el-option v-for="item in importOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="btns">
            <template #default="scope">
              <xel-handle-btns :btnList="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <div style="margin-top: 10px">
      <FieldsTipsCom />
    </div>

    <div class="mt20 text-center">
      <el-button @click="addField" style="min-width: 200px">
        <el-icon :size="12"> <plus /> </el-icon>添加字段</el-button
      >
    </div>

    <div class="text-right">
      <el-button @click="getTableData">取消</el-button>
      <el-button type="primary" v-hasPermi="'config:logField:add'" @click="submit" :loading="saveLoading">确定</el-button>
    </div>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick, watch } from "vue";
import FieldsTipsCom from "@/views/sime/components/fieldsTipsCom.vue";
import { getDicts } from "@/api/sime/config/log.js";
import { getDictsData } from "@/utils/getDicts";
import { getLogFieldList, saveLogFieldList, fieldAvailable } from "@/api/sime/config/field.js";
import tableMixin from "../../components/mixin/tableMixin";
import Sortable from "sortablejs";
let { tableHeight } = tableMixin(300);
let state = reactive({
  ruleForm: {
    tableData: [],
  },
  options: [], //字典类型
  importOptions: [], //重要字段
  loading: false,
  fileDictList: [],
});
let { options, importOptions, ruleForm, loading } = toRefs(state);
// 获取字典
getDataDicts();
function getDataDicts() {
  getDicts()
    .then((res) => {
      state.options = res.data;
    })
    .catch(() => {});
}
// 获取重要字段
getImportOptions();
function getImportOptions() {
  getDictsData("common_logic_judgment").then((res) => {
    state.importOptions = res.map((item) => {
      return {
        label: item.label,
        value: Number(item.value),
      };
    });
  });
}
// 列表配置项
const columns = [
  {
    prop: "field",
    label: "字段名",
    slotName: "field",
  },
  {
    prop: "fieldText",
    label: "字段别名",
    slotName: "fieldText",
  },
  {
    prop: "dictType",
    label: "字典",
    slotName: "dictType",
  },
  {
    prop: "defaultValue",
    label: "默认值",
    slotName: "defaultValue",
  },
  {
    prop: "important",
    label: "重要字段",
    slotName: "important",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "btns",
  },
];

import { fieldsValid } from "@/utils/fieldsValid";
const rules = reactive({
  field: [
    {
      required: true,
      /*message: "请输入字段名",*/
      validator: fieldsValid,
      trigger: ["blur", "change"],
    },
  ],
  fieldText: [
    {
      required: true,
      message: "请输入字段别名",
      trigger: ["blur", "change"],
    },
  ],
});

//
function getBtnList(row) {
  return [
    {
      hide: row.internal == 1,
      icon: "delete",
      title: "删除",
      onClick(scope) {
        delFn(scope.row);
      },
    },
  ];
}

// 获取字典数据
getFieldAvailable();
function getFieldAvailable() {
  fieldAvailable().then((res) => {
    state.fileDictList = res.data;
  });
}
//获取表格数据
getTableData();
function getTableData() {
  state.ruleForm.tableData = [];
  state.loading = true;
  getLogFieldList()
    .then((res) => {
      state.ruleForm.tableData = res.data.rows;
    })
    .finally(() => {
      state.loading = false;
      rowDrop();
    });
}

// 选中字典时，默认值变为下拉框 反之 input
function changeDict(row) {
  state.ruleForm.tableData.forEach((item) => {
    if (item.field == row.field) {
      item.dictDataList = [];
      item.defaultValue = "";
      item.dictDataList = item.dictType ? state.fileDictList[row.dictType] : [];
    }
  });
}

//删除
function delFn(rows) {
  let name = options.value.length > 0 ? options.value.find((ele) => ele.field === rows.indexField).alias : "";
  if (options.value.length > 0) {
    rows.indexField = name;
  }

  let index = state.ruleForm.tableData.indexOf(rows);
  state.ruleForm.tableData.splice(index, 1);
}

// 添加字段 新增一行
let tableRef = ref();
function addField() {
  state.ruleForm.tableData.push({
    field: "",
    fieldText: "",
    dictType: "",
    defaultValue: "",
    important: 0,
    internal: 0, //新增的为0
    orderNum: state.ruleForm.tableData.length,
  });
  nextTick(() => {
    document.getElementsByTagName("tr")[state.ruleForm.tableData.length - 1].scrollIntoView(true);
  });
}

// 保存
let ruleFormRef = ref();
let saveLoading = ref(false);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      let params = state.ruleForm.tableData.map((tItem, index) => {
        return {
          ...tItem,
          orderNum: index + 1,
        };
      });
      saveLogFieldList(params)
        .then((res) => {
          getTableData();
          saveLoading.value = false;
        })
        .finally(() => {
          saveLoading.value = false;
        });
    }
  });
}

function rowDrop() {
  // 此时找到的元素是要拖拽元素的父容器
  const tbody = document.querySelector(".el-table__body-wrapper tbody");
  Sortable.create(tbody, {
    //  指定父元素下可被拖拽的子元素
    draggable: ".el-table__row",
    handle: "td:first-child",
    onEnd({ newIndex, oldIndex }) {
      const currRow = state.ruleForm.tableData.splice(oldIndex, 1)[0];
      state.ruleForm.tableData.splice(newIndex, 0, currRow);
    },
  });
}
let lastParams = {}; //排序使用
let sortParams = {};
//排序
function sortChange({ prop, order }) {
  sortParams.orderByColumn = prop;
  sortParams.isAsc = order == "descending" ? "desc" : "asc";
  getTableData({ ...lastParams, ...sortParams });
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small .el-form-item__content) {
  /* line-height: 32px; */
  margin-top: 18px;
}
</style>
