<template>
  <el-card class="bg-p-border-new px0-new">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-button size="small" @click="newlyAdded" class="list-button-str mb20 mr20-new" v-hasPermi="'config:parseRuleIpBinding:add'">
      <el-icon :size="12">
        <plus />
      </el-icon>
      新增
    </el-button>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData"></xel-table>
    <!-- 弹窗内容 -->
    <xelDialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import {
  getIpBindingList as getTableData,
  getIpBindingTree,
  addIpBinding as addItem,
  updateIpBinding as updateItem,
  detailIpBinding as getDetail,
  delIpBinding as delItem,
} from "@/api/sime/config/ruleBind";
import { useRoute } from "vue-router";
import { batchDelete } from "@/utils/delete";
const route = useRoute();
let dialogText = ref("规则绑定");
let dialogTitle = ref("");
let dialogRef = ref();
let editId = ref("");
let tableRef = ref();
let ruleFormRef = ref();
let state = reactive({
  formData: {
    ip: "",
    parseRuleIdList: [],
  },
});
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "ip",
    label: "设备IP",
    required: true,
    vxRule: "IPV6",
  },
  {
    formType: "tree",
    prop: "parseRuleIdList",
    label: "绑定规则",
    required: true,
    multiple: true,
    disabledKey: "isGroupNode",
    treeOptions: {
      loadData: getIpBindingTree, //接口名称
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
  },
]);
let { formData } = toRefs(state);
const columns = [
  {
    prop: "ip",
    label: "设备IP",
  },
  {
    prop: "parseruleName",
    label: "绑定规则",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    prop: "updateTime",
    label: "修改时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        hasPermi: "config:parseRuleIpBinding:edit",
        onClick(scope) {
          dialogTitle.value = "修改" + dialogText.value;
          modifyButton(scope.row);
        },
      },
      {
        icon: "delete",
        title: "删除",
        hasPermi: "config:parseRuleIpBinding:delete",
        onClick(scope) {
          batchDelete([scope.row]).then(() => {
            delItem(scope.row.id).then(() => {
              ElMessage({
                message: "删除成功",
                type: "success",
              });
              tableRef?.value.reload();
            });
          });
        },
      },
    ],
  },
];
// 新增
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  editId.value = "";
  popupBox();
  state.formData = {
    ip: "",
    parseRuleIdList: [],
  };
}
// 修改
function modifyButton(data) {
  editId.value = data.id;
  getDetail(data.id).then(({ data }) => {
    console.log("data: ", data);
    state.formData = { id: data.id, ip: data.ip, parseRuleIdList: data.parseRuleIdList };
    popupBox();
  });
}
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}

// 提交
function submitForm(close) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let port = editId.value ? updateItem : addItem;
      port(state.formData).then((res) => {
        ElMessage.success("操作成功");
        close();
        tableRef?.value.reload();
      });
    }
  });
}

function closeDialog() {}
</script>
<style lang="scss"></style>
