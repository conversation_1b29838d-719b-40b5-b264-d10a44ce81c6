<template>
  <version-table class="mt-10-new" :load-data="getVersionList" :columns="columns" :back-api="rollBackVersion"></version-table>
</template>
<script>
export default {
  name: "FilterVersion",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import versionTable from "@/views/sime/components/versionTable.vue";
import { getVersionList, rollBackVersion } from "@/api/sime/config/filter";
let columns = [
  {
    prop: "version",
    label: "版本号",
  },
  {
    prop: "name",
    label: "过滤器名称",
  },
  {
    prop: "indexName",
    label: "索引名称",
  },
  {
    prop: "description",
    label: "描述",
  },

  {
    prop: "updateByName",
    label: "创建人",
  },
  {
    prop: "updateTime",
    label: "创建时间",
  },
];
</script>

<style lang="scss" scoped></style>
