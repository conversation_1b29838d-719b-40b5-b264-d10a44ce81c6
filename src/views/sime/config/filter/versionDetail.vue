<template>
  <el-card>
    <h3 class="conH3Tit">
      {{ "" || ($route.meta && $route.meta.title) }}
      <el-button class="pull-right" style="margin-left: 10px" @click="backVersionFn">
        <el-icon :size="12"> <back /> </el-icon>
        回滚</el-button
      >
      <backbutton class="pull-right" text="版本列表" :name="backRoute"></backbutton>
    </h3>
    <el-row :gutter="80">
      <el-col :span="8">
        <div class="title-bottom-line">属性</div>
        <el-form ref="ruleFormRef" label-width="8em" label-position="right">
          <el-form-item v-for="(item, index) in formList" :key="index" :label="item.label">
            <span v-if="item.prop != 'isDefault'">{{ details[item.prop] }}</span>
            <span v-else>{{ details[item.prop] == "Y" ? "是" : "否" }}</span>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="16">
        <div class="title-bottom-line">条件</div>
        <template v-if="ruleEditorVersion == 1">
          <condition v-if="showCondition" :editable="false" :data="details.conditionsObject" :type-list="typeList"></condition>
        </template>
        <template v-else-if="ruleEditorVersion == 2">
          <conditionNew v-if="showCondition" :list="conditionData" :type-list="typeList" :editable="false"></conditionNew>
        </template>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
export default {
  name: "FilterVersionDetail",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import condition from "@/views/sime/components/condition.vue";

import { getVersionDetail } from "@/api/sime/config/filter";
import { useRouter, useRoute } from "vue-router";
import { rollBackVersion } from "@/api/sime/config/filter";
import useCondition from "@/views/sime/components/newCondition/conditionEdit";
import conditionNew from "@/views/sime/components/newCondition/ifContent.vue";
import useSiemRuleEditorVersion from "@/utils/siemRuleEditorVersion";

//规则器版本
const { ruleEditorVersion } = useSiemRuleEditorVersion();

const router = useRouter();
const route = useRoute();

import conditionTypeList from "../../utils/conditionTypeList";

import backVersion from "@/views/sime/components/js/backVersion";

let id = ref(route.params.id);
let state = reactive({
  formData: {
    indexId: route.query.indexId,
  },
});

let details = ref({});
const conditionData = ref([]);
getVersionDetail(id.value).then(({ data }) => {
  details.value = data;
  showCondition.value = true;
  state.formData.indexId = data.indexId;
  conditionData.value = echoConditions(data.conditionsObject);
  console.log("conditionData.value: ", conditionData.value);
});

let { typeList, showCondition, getFilterTypeList } = conditionTypeList(state, id.value, false);
getFilterTypeList(route.query.indexId);

let formList = [
  {
    prop: "name",
    label: "名称",
  },
  {
    prop: "version",
    label: "版本号",
  },
  {
    prop: "indexName",
    label: "索引表",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "isDefault",
    label: "常用过滤器",
  },
  {
    prop: "updateByName",
    label: "创建人",
  },
  {
    prop: "updateTime",
    label: "创建时间",
  },

  {
    prop: "optName",
    label: "操作名称",
  },
];

let backRoute = { name: "FilterVersion", params: { id: route.query.pId } };

function backVersionFn() {
  backVersion(rollBackVersion, route.query.pId, route.params.id);
}

//条件编辑
const { echoConditions } = useCondition();
</script>

<style lang="scss" scoped></style>
