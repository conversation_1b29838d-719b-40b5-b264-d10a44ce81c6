<template>
  <!-- 日志权限过滤器 - 界面 -->
  <el-card class="filter-wrapper" :class="{ 'rule-editor-2': ruleEditorVersion == 2 }">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <sime-layout
      ref="simeRef"
      :load-data="getTreeData"
      :defaultProps="defaultProps"
      @changeGroup="changeGroup"
      :table_data="table_data"
      :node_item_data="add_item_data"
      :tableId="tableId"
      :moduleTyp="'queryFilter'"
    >
      <div v-show="listStatus">
        <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
          <template #form>
            <xel-form-item
              label="创建时间"
              type="datetimerange"
              form-type="daterange"
              value-format="YYYY-MM-DD HH:mm:ss"
              v-model:start="searchState.startTime"
              v-model:end="searchState.endTime"
              itemWidth="41%"
            ></xel-form-item>
          </template>

          <el-button
            v-hasPermi="'config:logPermission:delete'"
            @click="delFn(multipleSelection)"
            class="search-button"
            :disabled="multipleSelection.length === 0"
          >
            <icon n="icon-huabanfuben" :size="12"></icon>
            批量删除
          </el-button>
          <el-button
            @click="newlyAdded"
            class="search-button"
            :disabled="!currentGroup.id || currentGroup.id == '0' || currentGroup.isDataNode"
            v-hasPermi="'config:logPermission:add'"
          >
            <el-icon :size="12">
              <plus />
            </el-icon>
            新增
          </el-button>
          <move-items
            :idKey="idKey"
            apiKey="logPermissionIds"
            :list="filterList"
            :multiple-selection="multipleSelection"
            :current-group-id="currentGroup.id"
            :move-api="moveItem"
            @update="moveUpdate"
          ></move-items>
        </common-search>

        <xel-table
          ref="tableRef"
          :columns="columns"
          :checkbox="true"
          :row-key="idKey"
          :load-data="getDataSourceList"
          :default-params="{ groupId: currentGroup.id }"
          @selection-change="handleSelectionChange"
        >
          <template #status="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="updateStatus(scope.row)"> </el-switch>
          </template>
        </xel-table>
      </div>
      <div v-if="!listStatus" class="filter-edit-wrapper bg-p-border-new">
        <el-row :gutter="$globalWindowSize == 'L' ? '60' : '20'">
          <el-col :span="8">
            <div class="title-bottom-line">属性</div>
            <el-form ref="ruleFormRef" :model="formData" label-width="8em" label-position="right">
              <xel-form-item v-for="item in formList" :key="item.prop" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
            </el-form>
            <div class="footer" v-hasPermi="'config:logPermission:update'">
              <el-button type="primary" @click="submitForm" :loading="loading">保存</el-button>
              <el-button @click="closeDialog">取消</el-button>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="title-bottom-line">条件</div>
            <template v-if="ruleEditorVersion == 1">
              <condition v-if="showCondition" ref="conditionRef" :labelList="labelList" :data="conditionsObject" :type-list="typeList" />
            </template>
            <template v-else-if="ruleEditorVersion == 2">
              <section class="condition-wrapper">
                <conditionResult
                  v-if="showCondition && judgmentContentRealTime[0] && judgmentContentRealTime[0].children.length"
                  :list="judgmentContentRealTime[0].children"
                />
                <conditionNew
                  v-if="showCondition"
                  :list="judgmentContent"
                  :content-chose-id="choseId"
                  :chose-if-id="choseIfId"
                  :type-list="typeList"
                  @change-list="changeConditionData"
                  :haveFilter="false"
                />
              </section>
            </template>
          </el-col>
        </el-row>
      </div>
    </sime-layout>
  </el-card>
</template>

<script>
export default {
  name: "LogPermisson",
};
</script>
<script setup>
import condition from "@/views/sime/components/condition.vue";
import moveItems from "@/views/sime/components/moveItems.vue"; //移动组件

import { ref, reactive, toRefs, nextTick, computed, watch, provide, onActivated } from "vue";
onActivated(() => {
  search(false);
});

import { useRouter } from "vue-router";
const router = useRouter();

import { ElMessage, ElMessageBox } from "element-plus";
import { getLogTree } from "@/api/sime/config/log";
import handlerBtns from "../../utils/handlerBtns";
import conditionTypeList from "../../utils/conditionTypeList";
import { batchDelete } from "@/utils/delete";
import {
  getTreeData,
  addNodeGroup,
  editNodeGroup,
  deleteNodeGroup,
  detailNodeGroup,
  getDataSourceList,
  addTableDataSource as addItem,
  editTableDataSource as updateItem,
  getSourceInfo as getDetail,
  delSourceInfo as delItem,
  moveItem,
  editStatus,
} from "@/api/sime/config/logPermisson";

/* 新编辑器 */
import conditionNew from "@/views/sime/components/newCondition/ifContent.vue";
import conditionResult from "@/views/sime/components/conditionResult.vue"; //封装后的条件语句
import useCondition from "@/views/sime/components/newCondition/conditionEdit";
import useSiemRuleEditorVersion from "@/utils/siemRuleEditorVersion";

//规则器版本
const { ruleEditorVersion } = useSiemRuleEditorVersion();

let simeRef = ref();

/* 条件 list */
let labelList = ref([
  {
    notetype: "event",
    icon: "icon-IF",
    label: "事件",
    color: import.meta.env.VITE_COLOR,
  },
  {
    notetype: "or",
    icon: "icon-or",
    label: "OR",
    color: "#409EFF",
    bgColor: "#ECF5FF",
  },
  {
    notetype: "and",
    icon: "icon-and",
    label: "AND",
    color: "#67C23A",
    bgColor: "#F0F9EB",
  },
  {
    notetype: "not",
    icon: "icon-not",
    label: "NOT",
    color: "#F56C6B",
    bgColor: "#FEF0F0",
  },
  {
    notetype: "condition",
    icon: "icon-condition",
    label: "条件",
    color: "#E6A23C",
    bgColor: "#f0f1f4",
    editStatus: true,
    kong: false,
    data: {
      nameText: "", //filledText
      operatorText: "", //操作汉字
      valueText: "",
    },
  },
]);

//条件语句使用
let filterList = computed(() => {
  return simeRef.value ? simeRef.value.getTreeData() : [];
});
let tableId = ref("");
//过滤器条件使用start
provide(
  "filterList",
  computed(() => {
    return filterListSameIndexId.value;
  })
);
provide(
  "id",
  computed(() => {
    return editId.value;
  })
);

//过滤器条件使用end

let dialogText = ref("过滤器");
let idKey = "id";
let tableRef = ref();
let tablePid = ref();
//搜索相关
let searchState = reactive({
  data: {
    status: "",
    name: "",
    description: "",
    conditions: "",
  },
  menuData: [
    {
      lable: "状态",
      prop: "status",
      menuBtnShow: true,
      sime: true,
      options: [],
      dictName: "config_logPermission_isAvailable",
    },
  ],
  startTime: "",
  endTime: "",
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "名称",
      labelWidth: "4em",
      itemWidth: "20%",
    },
    {
      prop: "description",
      label: "描述",
      labelWidth: "4em",
      itemWidth: "30%",
    },
    {
      prop: "conditions",
      label: "条件",
      labelWidth: "4em",
      itemWidth: "30%",
    },
  ],
});
function search(initPageNum = true) {
  if (listStatus.value) {
    tableRef.value.reload({ ...searchState.data, startTime: searchState.startTime, endTime: searchState.endTime }, initPageNum);
  }
}
function reset() {
  searchState.data = {
    status: "",
    name: "",
    description: "",
    conditions: "",
  };
  searchState.startTime = "";
  searchState.endTime = "";
  search();
  /*simeRef.value.getData(tableId.value);*/
}

//重置新增编辑表单
function resetFormData() {
  state.formData = {
    name: "",
    description: "",
    indexId: "",
    conditions: [],
    status: 0,
  };
  tableId.value = "";
  judgmentContent.value = echoConditions({});
  showCondition.value = false;
}

let { btnList, multipleSelection, handleSelectionChange } = handlerBtns(
  delItem,
  search,
  () => {
    simeRef.value.getData(tableId.value);
  },
  copyItem,
  idKey,
  router
); //mixins

let columns = [
  {
    prop: "isDefaultText",
    label: "状态",
    slotName: "status",
  },
  {
    prop: "name",
    label: "过滤器名称",
    click(scope) {
      dialogTitle.value = "修改" + dialogText.value;
      modifyButton(scope.row[idKey]);
      // 同时左侧树选中
      /*tablePid.value = scope.row.groupId;*/
      tableId.value = scope.row[idKey];
    },
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "indexName",
    label: "索引名称",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "document-copy",
        title: "复制",
        hasPermi: "config:logPermission:add",
        onClick(scope) {
          copyItem(scope.row);
        },
      },

      {
        icon: "delete",
        title: "删除",
        hasPermi: "config:logPermission:delete",
        onClick(scope) {
          delFn([scope.row]);
        },
      },
    ],
  },
];

//修改过滤器是否常用状态
function updateStatus(row) {
  let prevenable = row.status == 0 ? 1 : 0;
  let text = prevenable == 0 ? "开启" : "关闭";
  ElMessageBox.confirm(`确认${text}过滤器（${row.name}）吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let params = {
        logPermissionId: row[idKey],
        status: prevenable == 0 ? 1 : 0,
      };
      editStatus(params)
        .then(() => {
          ElMessage({
            type: "success",
            message: text + "成功",
          });
          search(false);
        })
        .catch(function () {
          search(false);
        });
    })
    .catch(function () {
      row.status = row.status == 0 ? 1 : 0;
    });
}

let state = reactive({
  formData: {
    name: "",
    description: "",
    indexId: "",
    conditions: [],
  }, //新增编辑表单
  currentGroup: {}, //当前选中的分组
  disabled: true,
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {},
  add_item_data: {
    isCheckName: true,
    delport: deleteNodeGroup,
    editport: editNodeGroup,
    addport: addNodeGroup,
    detailport: detailNodeGroup,
    nodeeDialogTitle: "过滤器分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
    ],
  },
});

let editId = ref("");

let { filterListSameIndexId, typeList, showCondition, getFilterTypeList, confirmIndexId, emptyLastIndexId } = conditionTypeList(
  state,
  editId,
  true,
  true,
  () => {
    conditionsObject.value = {};
  }
);

const { choseId, getChoseId, choseIfId, getIfId, judgmentContent, changeConditionData, judgmentContentRealTime, getConditionsData, echoConditions } =
  useCondition(showCondition);

// 批量删除
function delFn(rows) {
  batchDelete().then(() => {
    let ids = rows.map((item) => item[idKey]);

    delItem(ids.join()).then(() => {
      ElMessage.success("删除成功");
      tableRef.value.table.clearSelection();
      simeRef.value.getData(tableId.value);
      search(false);
    });
  });
}
// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  editId.value = "";
  resetFormData();
  popupBox();
  emptyLastIndexId();
  showCondition.value = true;
  conditionsObject.value = {};
}

let conditionsObject = ref();
// 修改按钮
function modifyButton(id) {
  editId.value = id;
  getDetail(id).then(({ data }) => {
    for (let key in data) {
      state.formData[key] = data[key];
    }
    conditionsObject.value = data.conditionsObject || {};
    popupBox();
    getFilterTypeList(state.formData.indexId);
    judgmentContent.value = echoConditions(data.conditionsObject || {});
  });
}

//复制
function copyItem(data) {
  editId.value = "";
  for (let key in data) {
    state.formData[key] = data[key];
  }
  state.formData.name += "—复制";
  delete state.formData[idKey];
  conditionsObject.value = data.conditionsObject || {};
  popupBox();
  getFilterTypeList(state.formData.indexId);
  judgmentContent.value = echoConditions(data.conditionsObject || {});
}

//

// 弹框
let dialogTitle = ref("");
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  //展示编辑过滤器
  changeListStatus(false);
}
// 弹框内容
let formList = reactive([
  {
    prop: "name",
    label: "名称",
    required: true,
  },
  {
    prop: "indexId",
    label: "索引表",
    required: true,
    formType: "tree",
    multiple: false,
    treeOptions: {
      loadData: getLogTree, //接口名称
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
    onNodeClick(data) {
      if (data.isDataNode) {
        confirmIndexId(data.id, editId.value);
      }
    },
  },
  {
    prop: "description",
    label: "描述",
    type: "textarea",
    rows: "4",
  },
  {
    prop: "status",
    label: "是否开启",
    formType: "radio",
    options: [
      { value: 1, label: "是" },
      { value: 0, label: "否" },
    ],
  },
]);

// 弹框确定按钮
let loading = ref(false);
let conditionRef = ref();

// 提交
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let params = { ...state.formData };

      let addFn = editId.value ? updateItem : addItem;
      //复制时不需要改grupId
      if (!editId.value && !params.groupId) {
        params.groupId = state.currentGroup.id;
      }

      //  条件
      if (ruleEditorVersion.value == 1) {
        let conditions = conditionRef.value ? conditionRef.value.getList() : [];

        if (!conditions) return;
        params.conditionsObject = conditions[0];
      } else if (ruleEditorVersion.value == 2) {
        let conditionsObject = getConditionsData();
        if (!conditionsObject) {
          ElMessage.warning("请完善条件!");
          return;
        }
        params.conditionsObject = conditionsObject;
      }

      if (params.conditionsObject && params.conditionsObject.children.length == 0) {
        ElMessage.warning("请编辑条件");
        return;
      }

      delete params.conditions;
      loading.value = true;

      addFn(params)
        .then((res) => {
          ElMessage.success("保存成功");
          editId.value = res.data.id;
          modifyButton(editId.value);
          //刷新分组
          simeRef.value.getData(tableId.value);
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
}

//列表重置
function closeDialog() {
  changeListStatus(true);
  resetFormData();
  simeRef.value.getData(tablePid.value);
  if (listStatus.value) {
    tableRef.value.reload({ ...searchState.data, startTime: searchState.startTime, endTime: searchState.endTime, groupId: tablePid.value }, true);
  }
  state.currentGroup.id = tablePid.value;
  /* 详情返回后，节点新增不可用，重置 isDataNode 为 false */
  state.currentGroup.isDataNode = false;
  nextTick(() => {
    if (ruleFormRef.value) ruleFormRef.value.resetFields();
  });
}

//获取当前选中的分组
function changeGroup(data) {
  /* 新增 - 修改 tablePid 显示逻辑 */
  if (data.id == 0 || data.isEditable === 1) {
    tablePid.value = data.id;
  }
  tableId.value = data.id;
  state.currentGroup = data;
  /*tablePid.value = data.parentId;*/
  changeListStatus(!data.isDataNode);

  if (data.isDataNode) {
    dialogTitle.value = "修改" + dialogText.value;
    modifyButton(data.id);
  } else {
    nextTick(() => {
      search();
    });
  }
}
//列表和新增，编辑显示隐藏
let listStatus = ref(true);
function changeListStatus(val) {
  listStatus.value = val;
}

//移动后刷新列表和分组
function moveUpdate() {
  search(false);
  simeRef.value.getData(tableId.value);
  tableRef.value.table.clearSelection();
}

let { defaultProps, add_item_data, table_data, formData, currentGroup } = toRefs(state);
</script>

<style lang="scss" scoped>
.filter-wrapper {
  :deep(.el-card__body) {
    padding-bottom: 5px;
  }
}
.filter-edit-wrapper {
  .footer {
    padding: 25px 0 30px;
    margin-top: 20px;
    border-top: 1px solid #ebedf1;
    text-align: right;
  }
}
.rule-editor-2 .condition-result-wrapper.is-root {
  width: calc(100% - 18px);
  background: #f4f7fb;
}
</style>
