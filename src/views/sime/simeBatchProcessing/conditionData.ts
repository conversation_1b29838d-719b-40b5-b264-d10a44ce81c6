const arr = {
  name: "事件",
  notetype: "and",
  children: [
    {
      name: "lrecepttime",
      operator: "nequal",
      value: 1691596800000,
      nameText: "接收时间",
      operatorText: "不等于",
      valueText: "2023-08-10 00:00:00",
      content: "",
      nameType: "",
      notetype: "condition",
    },
    {
      name: "",
      notetype: "or",
      children: [
        {
          name: "lrecepttime",
          operator: "equal",
          value: 1691510400000,
          nameText: "接收时间",
          operatorText: "等于",
          valueText: "2023-08-09 00:00:00",
          content: "",
          nameType: "",
          notetype: "condition",
        },
        {
          name: "ieventlevel",
          operator: "equal",
          value: "2",
          nameText: "等级",
          operatorText: "等于",
          valueText: "轻微",
          content: "",
          nameType: "",
          notetype: "condition",
        },
        {
          name: "",
          notetype: "not",
          children: [
            {
              name: "cdstip",
              operator: "startwith",
              value: "*******",
              nameText: "目的地址",
              operatorText: "开始于",
              valueText: "*******",
              content: "",
              nameType: "",
              notetype: "condition",
            },
          ],
        },
      ],
    },
    {
      name: "",
      notetype: "not",
      children: [
        {
          name: "ceventname",
          operator: "equal",
          value: "888",
          nameText: "事件名称",
          operatorText: "等于",
          valueText: "888",
          content: "",
          nameType: "",
          notetype: "condition",
        },
        {
          name: "idstport",
          operator: "ins",
          value: "8",
          nameText: "目的端口",
          operatorText: "属于",
          valueText: "8",
          content: "",
          nameType: "",
          notetype: "condition",
        },
        {
          name: "filter",
          operator: "in",
          value: "1501015646325555201",
          notetype: "condition",
          nameText: "过滤器",
          operatorText: "等于",
          valueText: "原始日志",
        },
      ],
    },
  ],
};
