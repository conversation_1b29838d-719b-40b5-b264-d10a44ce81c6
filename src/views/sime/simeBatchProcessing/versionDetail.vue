<template>
  <el-card>
    <h3 class="conH3Tit">
      {{ "" || ($route.meta && $route.meta.title) }}
      <el-button class="pull-right" style="margin-left: 10px" @click="backVersionFn">
        <el-icon :size="12"> <back /> </el-icon>
        回滚</el-button
      >
      <backbutton class="pull-right" text="版本列表" :name="backRoute"></backbutton>
    </h3>
    <el-row :gutter="80">
      <el-col :span="8">
        <div class="title-bottom-line">属性</div>
        <el-form ref="ruleFormRef" label-width="8em" label-position="right">
          <el-form-item v-for="(item, index) in formList" :key="index" :label="item.label">{{ details[item.prop] }}</el-form-item>
        </el-form>
      </el-col>
      <el-col :span="16">
        <!-- <div class="title-bottom-line">条件</div> -->

        <el-tabs v-model="detailName" @tab-click="detailTabs(detailName)">
          <el-tab-pane label="条件" name="first">
            <template v-if="ruleEditorVersion == 1">
              <condition v-if="showCondition" :editable="false" :data="details.conditionsObject" :type-list="typeList"></condition>
            </template>
            <template v-else-if="ruleEditorVersion == 2">
              <conditionNew v-if="showCondition" :list="conditionData" :type-list="typeList" :editable="false"></conditionNew>
            </template>
          </el-tab-pane>
          <el-tab-pane label="聚合" name="second">
            <Aggregation
              v-if="showCondition"
              :editable="false"
              ref="aggRef"
              :indexId="state.formData.indexId"
              :data="aggregationObject"
            ></Aggregation>
          </el-tab-pane>
          <el-tab-pane label="动作" name="third">
            <MoveMent v-if="showCondition" :editable="false" ref="moveRef" :indexId="state.formData.indexId" :data="movementObject"></MoveMent>
          </el-tab-pane>
          <!-- <el-tab-pane label="运行记录" name="fourth">
            <RunRecord v-if="showCondition"></RunRecord>
          </el-tab-pane>  -->
        </el-tabs>
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
export default {
  name: "RuleVersionDetail",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import condition from "@/views/sime/components/condition.vue";
import MoveMent from "./components/movement.vue";
import RunRecord from "./components/runRecord.vue";
import ArtOperation from "./components/artOperation.vue";
import Aggregation from "./components/aggregation.vue";

import { getVersionDetail, rollBackVersion } from "@/api/sime/config/batchEngine";

import { useRouter, useRoute } from "vue-router";
import commonSearchVue from "../../../components/commonSearch.vue";
import useCondition from "@/views/sime/components/newCondition/conditionEdit";
import conditionNew from "@/views/sime/components/newCondition/ifContent.vue";

import useSiemRuleEditorVersion from "@/utils/siemRuleEditorVersion";

import conditionTypeList from "../utils/conditionTypeList";
import backVersion from "@/views/sime/components/js/backVersion";

//规则器版本
const { ruleEditorVersion } = useSiemRuleEditorVersion();

const router = useRouter();
const route = useRoute();

let id = ref(route.params.id);
let state = reactive({
  formData: {
    indexId: route.query.indexId,
  },
});
// 聚合
let aggregationObject = ref();
// 动作
let movementObject = ref();
let details = ref({});
const conditionData = ref([]);

getVersionDetail(id.value).then(({ data }) => {
  details.value = data;
  showCondition.value = true;
  state.formData.indexId = data.indexId;
  conditionData.value = echoConditions(data.conditionsObject);

  formList[2].isShow = true;
  formList[3].isShow = true;
  formList[4].isShow = true;
  formList[5].isShow = true;
  // 聚合
  aggregationObject.value = {
    indexId: data.indexId,
    isTriggerControl: data.isTriggerControl,
    isDelay: data.isDelay,
    queryValue: data.queryValue, //查询时间范围
    queryType: data.queryType, //时间范围类型
    countNumber: data.countNumber, // 触发动作计数
    sameFields: data.sameFields, // 相同字段
    sameFieldsArray: data.sameFieldsArray ? data.sameFieldsArray : [],
    sameFieldsTextArray: data.sameFieldsTextArray,
    differentFields: data.differentFields, //不同字段
    differentFieldsArray: data.differentFieldsArray ? data.differentFieldsArray : [], // 不同字段
    differentFieldsTextArray: data.differentFieldsTextArray,
    intervalValue: data.intervalValue, //运行频率
    intervalType: data.intervalType,
    triggerControlCount: data.triggerControlCount, // 相同字段触发动作时间间隔
    triggerControlValue: data.triggerControlValue,
    triggerControlType: data.triggerControlType,
    delayValue: data.delayValue,
    delayType: data.delayType,
  };
  // 动作
  movementObject.value = { actionsArray: data.actionsArray, indexId: data.indexId };
});

let { typeList, showCondition, getFilterTypeList } = conditionTypeList(state, id.value, false, false);
getFilterTypeList(route.query.indexId);

let formList = [
  {
    prop: "name",
    label: "名称",
  },
  {
    prop: "version",
    label: "版本号",
  },

  {
    prop: "indexName",
    label: "索引表",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "updateByName",
    label: "创建人",
  },
  {
    prop: "updateTime",
    label: "创建时间",
  },

  {
    prop: "optName",
    label: "操作名称",
  },
];
// 切换tabs
let detailName = ref("first");
function detailTabs(val) {
  detailName.value = val;
}

let backRoute = { name: "RuleVersion", params: { id: route.query.pId } };
function backVersionFn() {
  backVersion(rollBackVersion, route.query.pId, route.params.id);
}

//条件编辑
const { echoConditions } = useCondition();
</script>

<style lang="scss" scoped></style>
