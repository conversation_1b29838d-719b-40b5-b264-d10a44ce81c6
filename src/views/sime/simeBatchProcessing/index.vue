<template>
  <!-- 分析规则 -->
  <el-card :class="{ 'rule-editor-2': ruleEditorVersion == 2 }">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <sime-layout
      :load-data="getTreeData"
      :defaultProps="defaultProps"
      @changeGroup="changeGroup"
      :table_data="table_data"
      :node_item_data="add_item_data"
      :moduleTyp="'Analysis'"
      ref="simeRef"
      :tableId="tableId"
    >
      <div v-show="listStatus">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="列表" name="list">
            <div v-if="activeName == 'list'">
              <common-search
                v-model="searchState.data"
                :menu-data="searchState.menuData"
                :form-list="searchState.formList"
                @search="search"
                @reset="reset"
              >
                <template #form>
                  <xel-form-item
                    label="创建时间"
                    type="datetimerange"
                    form-type="daterange"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    v-model:start="searchState.startTime"
                    v-model:end="searchState.endTime"
                    itemWidth="41%"
                  ></xel-form-item>
                </template>
                <el-button v-hasPermi="'config:analysisRule:view'" :disabled="multipleSelection.length === 0" @click="dialogOpen(multipleSelection)">
                  <el-icon><Share /></el-icon>
                  拓扑图
                </el-button>
                <el-button
                  v-hasPermi="'config:analysisRule:delete'"
                  @click="delFn(multipleSelection)"
                  class="search-button"
                  :disabled="multipleSelection.length === 0"
                >
                  <icon n="icon-huabanfuben" :size="12"></icon>
                  批量删除
                </el-button>
                <el-button @click="artificialRun" class="search-button" :disabled="multipleSelection.length === 0" v-if="runBtn">
                  <icon n="icon-huabanfuben" :size="12"></icon>
                  人工运行
                </el-button>
                <el-button
                  @click="newlyAdded"
                  class="search-button"
                  :disabled="!currentGroup.id || currentGroup.id == '0' || currentGroup.isDataNode"
                  v-hasPermi="'config:analysisRule:add'"
                >
                  <el-icon :size="12">
                    <plus />
                  </el-icon>
                  分析规则
                </el-button>
                <move-items
                  :list="filterList"
                  :multiple-selection="multipleSelection"
                  :current-group-id="currentGroup.id"
                  :move-api="moveBatchRule"
                  idKey="ruleId"
                  title="规则分组"
                  @update="moveUpdate"
                ></move-items>
                <xel-upload-dialog
                  class="upload-button"
                  btnName="导入"
                  size="70px"
                  exportUrl=""
                  importUrl="/config/analysisRule/import"
                  :showDownLoadBtn="false"
                  :data="{ importType, password: importPassword }"
                  accept=".zip"
                  @click="resetUploadParams"
                  @updateData="search(), simeRef.getData()"
                  v-hasPermi="'config:analysisRule:import'"
                >
                  <el-form label-width="130px">
                    <xel-form-item
                      label="相同数据操作"
                      form-type="select"
                      :sime="true"
                      dictName="config_analysisrule_importType"
                      v-model="importType"
                    ></xel-form-item>
                    <el-form-item label="是否使用校验密码">
                      <el-radio-group v-model="showPassword" style="width: 100%" @change="changeShowPassword">
                        <el-radio :label="true">是</el-radio>
                        <el-radio :label="false">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="校验密码" v-show="showPassword">
                      <el-input v-model="importPassword"></el-input>
                    </el-form-item>
                  </el-form>
                </xel-upload-dialog>
                <el-button v-hasPermi="'config:analysisRule:export'" style="margin-left: 10px" @click="openExportDialog">
                  <el-icon :size="12">
                    <Download />
                  </el-icon>
                  导出</el-button
                >
              </common-search>
              <xel-table
                :checkbox="true"
                :row-key="idKey"
                :key="activeName"
                ref="tableRef"
                :columns="columns"
                :load-data="getListData"
                :default-params="{ groupId: currentGroup.isGroupNode ? currentGroup.id : currentGroup.parentId }"
                @selection-change="handleSelectionChange"
              >
                <template #intervalValue="scope">
                  <span>{{ scope.row.intervalValue }}{{ scope.row.intervalTypeText }}</span>
                </template>
                <template #status="scope">
                  <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="update_status(scope.row)"> </el-switch>
                </template>
              </xel-table>
            </div>
          </el-tab-pane>
          <el-tab-pane label="运行记录" name="record">
            <RunRecord v-if="activeName == 'record'" :key="activeName" :groupId="currentGroup.id"></RunRecord>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-if="!listStatus" class="filter-edit-wrapper bg-p-border-new">
        <el-row class="mb-10">
          <el-col :span="24">
            <el-tabs v-model="detailName" class="show-border" @tab-click="detailTabs(detailName)">
              <el-tab-pane label="属性" name="attribute">
                <el-form ref="ruleFormRef" :model="formData" label-width="7em" label-position="right">
                  <xel-form-item
                    v-for="item in formList"
                    :key="item.prop"
                    v-model="formData[item.prop]"
                    v-bind="item"
                    @selection-change="handleSelectionChange"
                  >
                  </xel-form-item>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="条件" name="first">
                <template v-if="ruleEditorVersion == 1">
                  <condition v-if="showCondition" ref="conditionRef" :data="conditionsObject" :type-list="typeList"></condition>
                </template>
                <template v-else-if="ruleEditorVersion == 2">
                  <section class="condition-wrapper">
                    <conditionResult
                      v-if="showCondition && judgmentContentRealTime[0] && judgmentContentRealTime[0].children.length"
                      :list="judgmentContentRealTime[0].children"
                    ></conditionResult>
                    <conditionNew
                      v-if="showCondition"
                      :list="judgmentContent"
                      :content-chose-id="choseId"
                      :chose-if-id="choseIfId"
                      :type-list="typeList"
                      @change-list="changeConditionData"
                    ></conditionNew>
                  </section>
                </template>
              </el-tab-pane>
              <el-tab-pane label="聚合" name="second">
                <Aggregation v-if="showCondition" ref="aggRef" :editId="editId" :indexId="formData.indexId" :data="aggregationObject"></Aggregation>
              </el-tab-pane>
              <el-tab-pane label="动作" name="third">
                <MoveMent v-if="showCondition" ref="moveRef" :indexId="formData.indexId" :data="movementObject"></MoveMent>
              </el-tab-pane>
              <el-tab-pane label="运行记录" name="fourth" v-if="!copyCondition">
                <RunRecord v-if="showCondition" :isDetail="true" :ruleIds="editId"></RunRecord>
              </el-tab-pane>
              <el-tab-pane label="规则测试" name="five">
                <ArtOperation
                  v-if="showCondition"
                  :isDialog="false"
                  :pageType="true"
                  :dataObject="{
                    groupId: state.currentGroup.id,
                    formData: formData,
                    conditionsObject: '',
                    aggData: aggRef,
                    moveData: moveRef,
                  }"
                />
              </el-tab-pane>
            </el-tabs>
            <!-- <div class="title-bottom-line">条件</div> -->
          </el-col>
        </el-row>
        <div class="footer" v-hasPermi="'config:analysisRule:update'">
          <el-button type="primary" @click="submitForm" :loading="loading">保存</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </div>
    </sime-layout>
    <ArtOperation :pageType="true" v-if="isshowOperate" @closeDialog="closeOperation" :multipleSelection="multipleSelection" />
    <!-- 导出弹框 -->
    <xel-dialog title="导出数据" size="small" ref="exportDialogRef" @submit="exportData">
      <el-form label-width="130px">
        <el-form-item label="导出类型" style="width: 100%">
          <el-radio-group v-model="exportParams.isAll" style="width: 100%">
            <el-radio :label="true">导出全部</el-radio>
            <el-radio :label="false">导出选中</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否使用校验密码">
          <el-radio-group v-model="showPassword" style="width: 100%" @change="changeShowPassword">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="校验密码" v-show="showPassword">
          <el-input v-model="exportParams.password"></el-input>
        </el-form-item>
      </el-form>
    </xel-dialog>
    <!-- 关系拓扑图 弹框-->
    <DiagramDialog ref="diagramDialog" :diagramData="diagramData"></DiagramDialog>
  </el-card>
</template>
<script>
export default {
  name: "Rule",
};
</script>
<script setup>
import condition from "@/views/sime/components/condition.vue";

import moveItems from "@/views/sime/components/moveItems.vue"; //移动组件

import conditionNew from "@/views/sime/components/newCondition/ifContent.vue";
import conditionResult from "@/views/sime/components/conditionResult.vue"; //封装后的条件语句
import useCondition from "../components/newCondition/conditionEdit";
import useSiemRuleEditorVersion from "@/utils/siemRuleEditorVersion";
import DiagramDialog from "./components/diagramDialog.vue"; // 关系图

import { ref, reactive, toRefs, nextTick, provide, computed, onActivated, watch } from "vue";
import dicsStore from "@/store/modules/dictsData";

if (!dicsStore.state.numberFlag || !dicsStore.state.stringFlag || !dicsStore.state.ruleFlag) {
  console.log("运行记录1: ");
  dicsStore.actions.getFilterOperatorNumRes();
  dicsStore.actions.getFilterOperatorStrRes();
  dicsStore.actions.getRuleOperatorRes();
}
//规则器版本
const { ruleEditorVersion } = useSiemRuleEditorVersion();

onActivated(() => {
  search(false);
});
import { batchDelete } from "@/utils/delete";
import { ElMessage } from "element-plus";
import { getLogTree } from "@/api/sime/config/log";
import { useRouter } from "vue-router";

import { download } from "@/plugins/request";

const router = useRouter();
import {
  getTreeData,
  addNodeGroup,
  editNodeGroup,
  deleteNodeGroup,
  detailNodeGroup,
  getListData,
  editStatus,
  addBatchRule as addItem,
  editBatchRule as updateItem,
  getRuleInfo as getDetail,
  delBatchRule as delItem,
  moveBatchRule,
  getRefInfo,
} from "@/api/sime/config/batchEngine";

import MoveMent from "./components/movement.vue";
import RunRecord from "./components/runRecord.vue";
import ArtOperation from "./components/artOperation.vue";
import Aggregation from "./components/aggregation.vue";

import conditionTypeList from "../utils/conditionTypeList";
import handlerBtns from "../utils/handlerBtns";
import hasPermi from "@/utils/hasPermi.js";

let tableId = ref("");
let simeRef = ref();
//条件语句使用
let filterList = computed(() => {
  return simeRef.value ? simeRef.value.getTreeData() : [];
});

// 人工运行需要的信息
let dataObject = ref();
let copyCondition = ref(false);
let isshowOperate = ref(false);
let activeName = ref("list");
let dialogText = ref("分析规则");
let idKey = "ruleId";
let tableRef = ref();
let tablePid = ref();
// 聚合
let aggRef = ref();
// 动作
let moveRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    name: "",
    description: "",
    conditions: "",
    status: null,
  },
  startTime: "",
  endTime: "",
  menuData: [
    {
      lable: "状态:",
      prop: "status",
      options: [],
      sime: true,
      dictName: "config_analysisrule_status",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "名称",
      labelWidth: "4em",
      itemWidth: "20%",
    },
    {
      prop: "description",
      label: "描述",
      labelWidth: "4em",
      itemWidth: "30%",
    },
    {
      prop: "conditions",
      label: "条件",
      labelWidth: "4em",
      itemWidth: "30%",
    },
  ],
});
let tableAddBtn = ref(false);
let tableEditBtn = ref(false);
let tableDeleteBtn = ref(false);
let runBtn = ref(false);
// 获取按钮权限
btnFn();
function btnFn() {
  // 分析规则页面
  tableAddBtn.value = hasPermi("config:analysisRule:add");
  tableEditBtn.value = hasPermi("config:analysisRule:update");
  tableDeleteBtn.value = hasPermi("config:analysisRule:delete");
  runBtn.value = hasPermi("config:analysisRule:add") || hasPermi("config:analysisRule:update");
  //
}
function search(initPageNum = true) {
  if (activeName.value == "list" && listStatus.value) {
    tableRef.value.reload({ ...searchState.data, startTime: searchState.startTime, endTime: searchState.endTime }, initPageNum);
  }
}
function reset() {
  searchState.data = {
    name: "",
    description: "",
  };
  searchState.startTime = "";
  searchState.endTime = "";
  search();
  simeRef.value.getData(tableId.value);
}

// 改变批处理状态
function update_status(data) {
  let params = {
    ruleId: data.ruleId,
    status: data.status,
  };
  editStatus(params)
    .then(() => {
      ElMessage.success("操作成功");
      search(false);
    })
    .catch(function (error) {
      search(false);
    });
}

let { btnList, multipleSelection, handleSelectionChange } = handlerBtns(
  delItem,
  search,
  () => {
    simeRef.value.getData(tableId.value);
  },
  copyItem,
  idKey,
  router
); //mixins

let diagramDialog = ref(); //拓扑图弹窗
const diagramData = ref([]);
//打开拓扑图弹窗
function dialogOpen(rows) {
  let ids = rows.map((item) => item.ruleId);
  getRefInfo({ ruleIds: ids.join(",") }).then((res) => {
    diagramData.value = res.data;
    nextTick(() => {
      diagramDialog.value.open();
    });
  });
}
let columns = [
  {
    prop: "status",
    label: "状态",
    slotName: "status",
  },
  {
    prop: "name",
    label: "名称",
    click(scope) {
      dialogTitle.value = "修改" + dialogText.value;
      modifyButton(scope.row[idKey]);
      // 同时左侧树选中
      tablePid.value = scope.row.groupId;
      tableId.value = scope.row[idKey];
      state.currentGroup.id = scope.row.groupId;
    },
  },
  {
    prop: "indexName",
    label: "索引",
  },
  {
    prop: "intervalValue",
    label: "运行间隔",
    slotName: "intervalValue",
  },
  {
    prop: "nextRunTime",
    label: "下次运行时间",
    width: $globalWindowSize == "L" ? "" : "100",
  },
  {
    prop: "nextTimeWindow",
    label: "下次数据范围",
    width: $globalWindowSize == "L" ? "" : "100",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      ...btnList,
      {
        icon: "Share",
        title: "拓扑图",
        onClick({ row }) {
          dialogOpen([row]);
        },
      },
    ],
  },
];
let state = reactive({
  formData: {
    groupId: "", //组Id
    // 属性
    name: "", //规则名称
    indexId: "", //索引Id
    status: 0, //0停，1启
    description: "", //描述

    //条件
    conditions: [],

    //动作
    actionsArray: [],
  }, //新增编辑表单
  currentGroup: {}, //当前选中的分组
  disabled: true,
  defaultProps: {
    children: "children",
    label: "name",
  },
  table_data: {},
  add_item_data: {
    isCheckName: true,
    delport: deleteNodeGroup,
    editport: editNodeGroup,
    addport: addNodeGroup,
    detailport: detailNodeGroup,
    nodeeDialogTitle: "分析规则分组",
    add_form_list: [
      {
        formType: "input",
        type: "text",
        prop: "name",
        required: true,
        label: "名称",
      },
      {
        formType: "input",
        type: "textarea",
        prop: "description",
        label: "描述",
      },
    ],
  },
});
let editId = ref("");

let { filterListSameIndexId, typeList, showCondition, getFilterTypeList, confirmIndexId, emptyLastIndexId } = conditionTypeList(
  state,
  editId,
  true,
  false,
  () => {
    conditionsObject.value = {};
  }
);
//子组件引用typeList
provide("typeList", typeList);
//过滤器条件使用start
provide(
  "filterList",
  computed(() => {
    return filterListSameIndexId.value;
  })
);

provide(
  "filterId",
  computed(() => {
    return editId.value;
  })
);

//删除，批量删除
function delFn(rows) {
  batchDelete().then(() => {
    let ids = rows.map((item) => item[idKey]);

    delItem(ids.join()).then(() => {
      ElMessage.success("删除成功");
      tableRef.value.table.clearSelection();
      search(false);
    });
  });
}
// 列表操作方法
// 人工运行
function artificialRun() {
  isshowOperate.value = true;
}

// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  editId.value = "";
  typeList.value = [];
  resetFormData();
  emptyLastIndexId();
  popupBox();
  showCondition.value = true;
}
let conditionsObject = ref();
// 聚合
let aggregationObject = ref();
// 动作
let movementObject = ref();
// 修改按钮
function modifyButton(id) {
  editId.value = id;
  getDetail(id).then(({ data }) => {
    for (let key in data) {
      state.formData[key] = data[key];
    }
    formList[4].isShow = true;
    formList[5].isShow = true;
    formList[6].isShow = true;
    formList[7].isShow = true;
    conditionsObject.value = data.conditionsObject || {};

    judgmentContent.value = echoConditions(data.conditionsObject || {});

    // 聚合
    aggregationObject.value = {
      indexId: data.indexId,
      isTriggerControl: data.isTriggerControl,
      isDelay: data.isDelay,
      queryValue: data.queryValue, //查询时间范围1
      queryType: data.queryType, //时间范围类型
      countNumber: data.countNumber, // 触发动作计数
      sameFields: data.sameFields, // 相同字段
      sameFieldsArray: data.sameFieldsArray ? data.sameFieldsArray : [],
      sameFieldsTextArray: data.sameFieldsTextArray,
      differentFields: data.differentFields, //不同字段
      differentFieldsArray: data.differentFieldsArray ? data.differentFieldsArray : [], // 不同字段
      differentFieldsTextArray: data.differentFieldsTextArray,
      intervalValue: data.intervalValue, //运行频率
      intervalType: data.intervalType,
      triggerControlCount: data.triggerControlCount, // 相同字段触发动作时间间隔
      triggerControlValue: data.triggerControlValue,
      triggerControlType: data.triggerControlType,
      delayValue: data.delayValue,
      delayType: data.delayType,
    };
    // 动作
    movementObject.value = { actionsArray: data.actionsArray, indexId: data.indexId };

    popupBox();
    getFilterTypeList(state.formData.indexId);
  });
}

// 复制
function copyItem(row) {
  copyCondition.value = true;
  getDetail(row[idKey]).then(({ data }) => {
    editId.value = "";
    for (let key in data) {
      state.formData[key] = data[key];
    }
    state.formData.name += "—复制";
    delete state.formData[idKey];
    // judgmentContent.value = echoConditions(data.conditionsObject || {});
    conditionsObject.value = data.conditionsObject || {};

    popupBox();
    // 条件
    getFilterTypeList(state.formData.indexId);

    formList[4].isShow = true;
    formList[5].isShow = true;
    formList[6].isShow = true;
    formList[7].isShow = true;
    judgmentContent.value = echoConditions(data.conditionsObject || {});
    conditionsObject.value = data.conditionsObject || {};

    // 聚合
    aggregationObject.value = {
      indexId: data.indexId,
      isTriggerControl: data.isTriggerControl,
      isDelay: data.isDelay,
      queryValue: data.queryValue, //查询时间范围
      queryType: data.queryType, //时间范围类型
      countNumber: data.countNumber, // 触发动作计数
      sameFields: data.sameFields, // 相同字段
      sameFieldsArray: data.sameFieldsArray ? data.sameFieldsArray : [],
      sameFieldsTextArray: data.sameFieldsTextArray,
      differentFields: data.differentFields, //不同字段
      differentFieldsArray: data.differentFieldsArray ? data.differentFieldsArray : [], // 不同字段
      differentFieldsTextArray: data.differentFieldsTextArray,
      intervalValue: data.intervalValue, //运行频率
      intervalType: data.intervalType,
      triggerControlCount: data.triggerControlCount, // 相同字段触发动作时间间隔
      triggerControlValue: data.triggerControlValue,
      triggerControlType: data.triggerControlType,
      delayValue: data.delayValue,
      delayType: data.delayType,
    };
    // 动作
    movementObject.value = { actionsArray: data.actionsArray, indexId: data.indexId };
  });
}

// 弹框
let dialogTitle = ref("");
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  //展示编辑过滤器
  changeListStatus(false);
}
// 详情属性内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "规则名称",
    required: true,
  },
  {
    formType: "switch",
    prop: "status",
    label: "状态",
    switchOptions: {
      activeValue: 1,
      inactiveValue: 0,
    },
  },
  {
    prop: "indexId",
    label: "索引",
    required: true,
    formType: "tree",
    multiple: false,
    treeOptions: {
      loadData: getLogTree, //接口名称
    },
    treeProps: {
      id: "id",
      label: "name",
      children: "children",
    },
    onNodeClick(data) {
      if (data.isDataNode) {
        confirmIndexId(data.id);
        //清空条件
        // judgmentContent.value = echoConditions({});
      }
    },
  },
  {
    formType: "input",
    type: "textarea",
    rows: "4",
    prop: "description",
    label: "描述",
  },
  {
    formType: "input",
    prop: "createByName",
    label: "创建人",
    isShow: false,
    disabled: true,
    placeholder: " ",
  },
  {
    formType: "input",
    prop: "createTime",
    label: "创建时间",
    isShow: false,
    disabled: true,
    placeholder: " ",
  },
  {
    formType: "input",
    prop: "updateByName",
    label: "修改人",
    isShow: false,
    disabled: true,
    placeholder: " ",
  },
  {
    formType: "input",
    prop: "updateTime",
    label: "修改时间",
    labelWidth: "4em",
    isShow: false,
    disabled: true,
    placeholder: " ",
  },
]);

// 弹框确定按钮
let loading = ref(false);
let conditionRef = ref();
// 提交
function submitForm(isTest) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let addFn = editId.value ? updateItem : addItem;
      let params = { ...state.formData };
      //复制时不需要改grupId
      if (!editId.value && !params.groupId) {
        params.groupId = state.currentGroup.id;
      }
      //  条件

      if (ruleEditorVersion.value == 1) {
        let conditions = conditionRef.value ? conditionRef.value.getList() : [];

        if (!conditions) return;
        params.conditionsObject = conditions[0];
      } else if (ruleEditorVersion.value == 2) {
        // let conditions = conditionRef.value ? conditionRef.value.getList() : [];
        let conditionsObject = getConditionsData();
        /* */

        if (!conditionsObject) {
          ElMessage.warning("请完善条件!");
          return;
        }
        params.conditionsObject = conditionsObject;
      }
      if (params.conditionsObject && params.conditionsObject.children.length == 0) {
        ElMessage.warning("请编辑条件");
        return;
      }
      delete params.conditions;

      // 聚合
      let aggData = aggRef.value ? aggRef.value.getAggFormData() : {};

      //  动作数据--start
      let MergeEventAction = []; //事件重定义
      let AlertAction = []; //生成告警
      let AlertRelayAction = []; //告警转换
      let actionData = moveRef.value ? moveRef.value.getFormData() : {};
      if (Object.keys(actionData).length != 0) {
        actionData.MergeEventAction.forEach((mItem) => {
          mItem.forEach((mv) => {
            MergeEventAction.push({
              formType: mv.formType,
              field: mv.prop,
              fieldText: mv.label,
              value: mv.value,
              valueText: mv.valueText,
              dictionaryType: mv.dictName ? mv.dictName : "",
            });
          });
        });
        actionData.AlertAction.forEach((mItem) => {
          mItem.forEach((mv) => {
            AlertAction.push({
              formType: mv.formType,
              field: mv.prop,
              fieldText: mv.label,
              value: mv.value,
              valueText: mv.valueText,
              dictionaryType: mv.dictName ? mv.dictName : "",
            });
          });
        });
        // 告警转发勾选了，但是没填值

        let isEmpty = false;
        actionData.AlertRelayAction.forEach((mv) => {
          if (!mv.selectInfo.field || !mv.treeInfo.value) {
            isEmpty = true;
          }
          AlertRelayAction.push({
            field: mv.selectInfo.field,
            fieldText: mv.selectInfo.fieldText,
            value: mv.treeInfo.value,
            valueText: mv.treeInfo.valueText,
          });
        });
        if (isEmpty || (actionData.AlertRelayActionIsChecked && actionData.AlertRelayAction.length == 0)) {
          ElMessage.warning("告警转发配置不完整，请确认");
          return;
        }
        params.actionsArray = [
          { action: "MergeLogAction", properties: MergeEventAction, isChecked: true },
          { action: "AlertAction", properties: AlertAction, isChecked: actionData.AlertActionIsChecked },
          { action: "AlertRelayAction", properties: AlertRelayAction, isChecked: actionData.AlertRelayActionIsChecked },
        ];
        params.actions = JSON.stringify(params.actionsArray); // 动作数据
      }

      if (aggData.validate === true) {
        loading.value = true;
        // end 动作数据
        //processType 1.流处理 2.批处理 默认批处理 后续扩展流处理相关
        addFn(Object.assign({ ...params, processType: 2 }, aggData))
          .then((res) => {
            ElMessage.success("保存成功");
            editId.value = res.data.ruleId;
            modifyButton(editId.value);

            //刷新分组
            simeRef.value.getData(tableId.value);
          })
          .finally(() => {
            loading.value = false;
          });
      } else {
        // detailName.value = "second";
        ElMessage.warning("请填写聚合条件必填项");
        return;
      }
    } else {
      return false;
    }
  });
}

//列表重置
function closeDialog() {
  changeListStatus(true);
  resetFormData();
  simeRef.value.getData(tablePid.value, true);

  if (activeName.value == "list" && listStatus.value) {
    tableRef.value.reload({ ...searchState.data, startTime: searchState.startTime, endTime: searchState.endTime, groupId: tablePid.value }, true);
  }
}

//重置新增编辑表单
function resetFormData() {
  detailName.value = "attribute";
  tableRef.value.table.clearSelection();
  tableId.value = "";
  state.formData = {
    // 属性
    name: "", //规则名称
    indexId: "", //索引Id
    status: 0, //0停，1启
    description: "", //描述
    //条件
    conditions: [],
    //动作
    actionsArray: [],
  };
  conditionsObject.value = {};
  judgmentContent.value = echoConditions({});

  movementObject.value = {};
  //  聚合
  aggregationObject.value = {
    isTriggerControl: 0,
    isDelay: 0,
    indexId: "",
    //  聚合
    queryValue: null, //查询时间范围
    queryType: "", //查询时间范围类型
    countNumber: null, //触发动作计数
    sameFieldsArray: [], // 相同字段\
    sameFieldsTextArray: [],
    differentFieldsArray: [], // 不同字段
    differentFieldsTextArray: [],
    intervalValue: null, //运行频率间隔
    intervalType: "", //intervalType
    triggerControlValue: null, //触发间隔
    triggerControlCount: null, //触发次数
    triggerControlType: "", //触发间隔时间类型
    delayValue: null, //延迟时间
    delayType: "", //延迟时间类型

    //动作
    actionsArray: [],
  };
  loading.value = false;
  showCondition.value = false;
  formList[4].isShow = false;
  formList[5].isShow = false;
  formList[6].isShow = false;
  formList[7].isShow = false;
  copyCondition.value = false;
}
// 关闭人工运行
function closeOperation() {
  isshowOperate.value = false;
  search(false);
}
//获取当前选中的分组
function changeGroup(data) {
  tableId.value = data.id;
  state.currentGroup = data;
  tablePid.value = data.parentId;
  changeListStatus(!data.isDataNode);
  if (data.isDataNode) {
    dialogTitle.value = "修改" + dialogText.value;
    modifyButton(data.id);
  } else {
    nextTick(() => {
      search();
    });
  }
}
//列表和新增，编辑显示隐藏
let listStatus = ref(true);
function changeListStatus(val) {
  listStatus.value = val;
}
// 切换列表tabs
function handleClick(tab, event) {
  activeName.value = tab.props.name;
}
let detailName = ref("attribute");
// detailTabs(detailName);
function detailTabs(val) {
  detailName.value = val;
}

//移动后刷新列表和分组
function moveUpdate() {
  search(false);
  simeRef.value.getData(tableId.value);
}

//导入 导出
let importType = ref("overwrite");
let importPassword = ref("");
let exportParams = ref({
  isAll: true,
  password: "",
});
let exportDialogRef = ref();
function openExportDialog() {
  exportDialogRef.value.open();
  showPassword.value = false;
  exportParams.value = { isAll: true, password: "" };
}
function resetUploadParams() {
  importType.value = "overwrite";
  importPassword.value = "";
  showPassword.value = false;
}
function exportData(close, load) {
  let url = "/config/analysisRule/export";
  if (!exportParams.value.isAll) {
    if (multipleSelection.value.length == 0) {
      ElMessage.warning("请先选择数据");
      return;
    }
    exportParams.value.ids = multipleSelection.value.map((item) => item.ruleId).join();
  }
  load();

  download(url, "分析规则.zip", exportParams.value, "post").finally(() => {
    close();
  });
}
let showPassword = ref(false);
function changeShowPassword(val) {
  if (!val) {
    exportParams.value.password = "";
    importPassword.value = "";
  }
}

//条件编辑
const { choseId, getChoseId, choseIfId, getIfId, judgmentContent, changeConditionData, judgmentContentRealTime, getConditionsData, echoConditions } =
  useCondition(showCondition);

defineExpose({
  getList: search,
});

let { defaultProps, add_item_data, table_data, formData, currentGroup } = toRefs(state);

provide(
  "ruleIndexId",
  computed(() => {
    return state.formData.indexId;
  })
);
</script>

<style lang="scss" scoped>
.filter-edit-wrapper {
  .mb-10 {
    margin-bottom: 10px;
  }
  .footer {
    padding-bottom: 10px;
    margin-top: 20px;
    text-align: right;
    position: fixed;
    bottom: 20px;
    right: 3%;
    z-index: 9;
  }
}
.rule-editor-2 .condition-result-wrapper.is-root {
  width: calc(100% - 18px);
  background: #f4f7fb;
}
</style>
