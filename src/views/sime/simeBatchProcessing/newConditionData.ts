const aa = {
  notetype: "and",
  children: [
    {
      notetype: "and",
      itemId: "1693290370728926",
      children: [
        {
          notetype: "condition",
          nameType: "",
          itemId: "1692934799092360",
          valueText: "3",
          operatorText: "等于",
          valueType: "input",
          name: "isrcport",
          nameText: "源端口",
          value: "3",
          operator: "equal",
          content: "",
          kong: false,
        },
        {
          notetype: "not",
          itemId: "1693290376605309",
          children: [
            {
              notetype: "condition",
              itemId: "1693290372754163",
              valueText: "原始日志",
              operatorText: "等于",
              name: "filter",
              nameText: "过滤器",
              value: "1501015646325555201",
              operator: "in",
            },
            {
              nameType: "",
              notetype: "condition",
              itemId: "1693290376605404",
              valueText: "信息",
              operatorText: "属于",
              valueType: "select",
              name: "ieventlevel",
              nameText: "等级",
              value: "1",
              operator: "ins",
              content: "",
              kong: false,
            },
            {
              notetype: "condition",
              itemId: "1693290395525733",
              valueText: "ipv6test-csrcip",
              operatorText: "等于",
              name: "filter",
              nameText: "过滤器",
              value: "1666699446523838465",
              operator: "in",
            },
          ],
          name: "",
          id: "",
          nodeType: "not",
        },
      ],
      name: "",
      id: "",
      nodeType: "and",
    },
  ],
  name: "事件",
};
