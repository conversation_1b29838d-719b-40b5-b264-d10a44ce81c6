<template>
  <version-table :load-data="getVersionList" :columns="columns" :back-api="rollBackVersion"></version-table>
</template>
<script>
export default {
  name: "RuleVersion",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import versionTable from "@/views/sime/components/versionTable.vue";
import { getVersionList, rollBackVersion } from "@/api/sime/config/batchEngine";
let columns = [
  {
    prop: "version",
    label: "版本号",
  },
  {
    prop: "name",
    label: "规则名称",
  },
  {
    prop: "indexName",
    label: "索引名称",
  },
  {
    prop: "countNumber",
    label: "计数",
  },
  {
    prop: "intervalValue",
    label: "运行间隔",
    formatter(row) {
      return row.intervalValue + row.intervalTypeText;
    },
  },

  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "updateByName",
    label: "创建人",
  },
  {
    prop: "updateTime",
    label: "创建时间",
  },
];
</script>

<style lang="scss" scoped></style>
