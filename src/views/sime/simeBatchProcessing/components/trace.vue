<template>
  <div class="div_table_all">
    <div class="div_table">
      <div class="tree">
        <el-tree
          :data="state.treeList"
          node-key="cuid"
          :props="defaultProps"
          :load="loadNode"
          lazy
          :highlight-current="true"
          @node-click="getNode"
          :expand-on-click-node="false"
        >
          <template #default="scope">
            <el-tooltip :content="scope.node.label" placement="top" effect="light" :disabled="!isOverflow(scope.node.label + '超过')">
              <span class="tree-node-label" :title="!isOverflow(scope.node.label) ? scope.node.label : ''">{{ scope.node.label }}</span>
            </el-tooltip>
          </template>
        </el-tree>
      </div>
    </div>
    <div class="div_table">
      <div>
        <p class="pull-left fieldTitle">数据信息</p>
        <p class="pull-left change" @click="changeShowOrHide">
          <span>
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-yincang"></use>
            </svg>
          </span>
          <span v-show="fieldisnull">显示空字段</span>
          <span v-show="!fieldisnull">隐藏空字段</span>
        </p>
        <div class="clearfix"></div>
      </div>
      <div style="height: calc(80vh - 230px); margin-right: 15px; margin-top: 20px">
        <el-table :data="state.detail" stripe border :class="{ 'show-table-bottom': state.detail.length == 0 }" height="100%">
          <el-table-column label="字段名" prop="fieldName"></el-table-column>
          <el-table-column label="值" prop="value">
            <template #default="scope">
              <div style="white-space: pre-wrap" v-text="$globalShowOriginStr(scope.row.value)"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onActivated, onMounted } from "vue";
import { filterIndexIdNames } from "@/api/sime/search/filter";
import { search_trace } from "@/api/sime/search/search";
let props = defineProps({
  alerts: {
    type: Array,
    default() {
      return [];
    },
  },
  indexId: {
    type: String,
    default: "",
  },
});
let state = reactive({
  treeList: [],
  detail: [],
  oldData: {},
});
let defaultProps = {
  label: "ceventname",
  children: "children",
  isLeaf: "leaf",
};
let fieldisnull = ref(false);
async function getLogDetail(data) {
  state.oldData = data;
  let res = await filterIndexIdNames(props.indexId);
  let arr = res.data;
  state.detail = [];
  for (let key in data) {
    let str = key.split("time");
    arr.forEach((item) => {
      if (item.field === key) {
        if (data[key] !== "" && fieldisnull.value === true) {
          state.detail.push({
            fieldName: item.alias,
            value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
          });
        } else if (fieldisnull.value === false) {
          state.detail.push({
            fieldName: item.alias,
            value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
          });
        }
      }
    });
  }
}
function changeShowOrHide() {
  fieldisnull.value = !fieldisnull.value;
  getLogDetail(state.oldData);
}
// 读取数据方法
async function loadNode(node, resolve) {
  if (node.level >= 1) {
    if (node.data.idatacategory === 3 || node.data.idatacategory === 1) {
      let res2 = await search_trace({ indexId: props.indexId, coriginlogids: node.data.coriginlogids });
      let data = res2.data;
      data.forEach((item) => {
        if (item.idatacategory == "0") {
          item.leaf = true;
        }
      });
      return resolve(data);
    } else {
      return resolve([]);
    }
  }
}
// 添加检查文本是否溢出的方法
function isOverflow(text) {
  const testDiv = document.createElement("div");
  testDiv.style.cssText = "position:absolute; visibility:hidden; white-space:nowrap;";
  testDiv.textContent = text;
  document.body.appendChild(testDiv);
  const textWidth = testDiv.offsetWidth;
  document.body.removeChild(testDiv);
  return textWidth > 300;
}

function getNode(node) {
  getLogDetail(node);
}
// 初始加载
onMounted(() => {
  getLogDetail(props.alerts[0]);
  let data = props.alerts;
  data.forEach((item) => {
    if (item.idatacategory == "0") {
      item.leaf = true;
    }
  });
  state.treeList = data;
});
</script>

<style lang="scss" scoped>
.div_table_all {
  display: flex;
  justify-content: space-between;
  & > .div_table {
    width: 49%;
  }
}
.tree-node-label {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.fieldTitle {
  font-weight: 600;
  font-size: 18px;
}
.change {
  color: #127dca;
  border: 1px solid #127dca;
  padding: 3px 5px;
  border-radius: 3px;
  margin-left: 10px;
}
.show-table-bottom {
  &:before {
    height: 1px !important;
  }
}
:deep(.is-current > .el-tree-node__content:hover .el-tree-node__label) {
  background-color: var(--el-color-primary-light-9);
}
</style>
