<template>
  <div v-for="item in titData" :key="item.value">
    <div class="tit">
      <el-checkbox v-model="item.checked" :label="item.label" v-if="item.value != 1" :disabled="!editable" @change="changebox(item)"></el-checkbox>
      <span v-else>{{ item.label }}</span>
      <span @click="changefold(item)" v-show="item.value == 3">
        <el-icon v-if="!item.show"><arrow-right /></el-icon>
        <el-icon v-else><arrow-down /></el-icon>
      </span>
    </div>
    <div class="formbg">
      <div v-if="item.value == 1 && state.eventRedefine.label">
        <AddFormItem
          ref="definedRef"
          :formList="definedForm"
          :editable="editable"
          :dynamicName="'字段'"
          :options="definedOptions"
          :dataRows="MergeEventAction"
          class="movement-relative"
        ></AddFormItem>
      </div>

      <div v-if="item.value == 2 && state.alertRedefine.label">
        <AddFormItem
          ref="alarmRef"
          :formList="alarmForm"
          :editable="editable"
          :dynamicName="'字段'"
          :options="definedOptions"
          :dataRows="AlertAction"
          class="movement-relative"
        ></AddFormItem>
      </div>

      <div v-show="item.show && item.value == 3">
        <Dynamic :dataRows="AlertRelayAction" ref="alertRelayRef" :formList="forwardForm" :editable="editable"></Dynamic>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { getMessageList } from "@/api/sime/config/batchEngine.js";
import { getFilesDicts } from "@/api/sime/config/alarm.js";
import AddFormItem from "../components/addFormItem.vue";
import { ref, reactive, onMounted, toRefs, watch, watchEffect } from "vue";
import Dynamic from "../components/dynamic.vue";
import { getDictsData } from "@/utils/getDicts";
let definedRef = ref();
let alarmRef = ref();
let alertRelayRef = ref();
let props = defineProps({
  indexId: {
    type: String,
    default: "",
  },
  data: {
    type: Array,
    default: () => {
      return {};
    },
  },
  editable: {
    type: Boolean,
    default: true,
  },
});
let MergeEventAction = ref();
let AlertAction = ref();
let AlertRelayAction = ref();
let state = reactive({
  formData: {},
  titData: [
    { label: "关联日志重定义", value: 1, show: true },
    { label: "生成告警", value: 2, show: true, checked: true }, //默认选中
    { label: "告警转发", value: 3, show: true, checked: false },
  ],
  definedOptions: [],
  messageList: [],
  eventRedefine: {},
  alertRedefine: {},
});
/*
关联日志名称和告警名称 在新建时有默认值，生成告警默认选中状态
*/
// 事件重定义
let definedForm = reactive([
  {
    formType: "input",
    prop: "ceventname",
    label: "关联日志名称",
    value: state.eventRedefine.label,
    valueText: state.eventRedefine.value,
    itemWidth: "calc((97% - 70px) )",
    isDel: true,
  },
]);
// 生成告警
let alarmForm = reactive([
  {
    formType: "input",
    prop: "ceventname",
    label: "告警名称",
    value: state.alertRedefine.label,
    valueText: state.alertRedefine.value,
    itemWidth: "calc((97% - 70px) )",
    isDel: true,
  },
]);

// 告警转发
let forwardForm = reactive([
  {
    formType: "select",
    prop: "",
    label: "转发配置",
    value: "",
    itemWidth: "calc((97% - 70px) /2)",
    isDel: true,
    valueText: "${rulename}",
    sime: true,
    dictName: "config_analysisrule_alertRelayType",
    onChange(val) {
      if (val) {
        let res = getMessageList(val);
        state.messageList = res;
      }
    },
  },
  {
    formType: "tree",
    prop: "",
    label: "",
    value: "",
    itemWidth: "calc((97% - 70px) /2)",
    isDel: true,
    valueText: "${rulename}",
    treeData: state.messageList,
  },
]);
// 切换
function changebox(item) {
  if (item.value == 3 && !state.titData[1].checked) {
    state.titData[2].checked = false;
    ElMessage.warning("请先选择生成告警");
    return;
  }
  if (item.value == 2 && !state.titData[1].checked) {
    state.titData[2].checked = false;
  }
}
// 切换
function changefold(item) {
  item.show = !item.show;
}

watch(
  () => props.indexId,
  (val) => {
    if (val) {
      getDataDicts(val);
    } else {
      state.titData[1].checked = true;
    }
  },
  { immediate: true }
);
let echoDataDone = false;

//回显数据
watch(
  () => props.data,
  (val) => {
    getEchoData(val);
  },
  {
    deep: true,
    immediate: true,
  }
);
// 回显数据
function getEchoData(data) {
  echoDataDone = false;
  if (data.indexId == props.indexId) {
    // 事件重定义
    MergeEventAction.value = data.actionsArray ? data.actionsArray[0]["properties"] : [];
    AlertAction.value = data.actionsArray ? data.actionsArray[1]["properties"] : [];
    AlertRelayAction.value = data.actionsArray ? data.actionsArray[2]["properties"] : [];

    state.titData[1].checked = data.actionsArray[1].isChecked ? data.actionsArray[1].isChecked : false;
    state.titData[2].checked = data.actionsArray[2].isChecked ? data.actionsArray[2].isChecked : false;
    definedForm = MergeEventAction.value;
    alarmForm = AlertAction.value;
  } else {
    state.formData = {};
  }
  setTimeout(() => {
    echoDataDone = true;
  }, 300);
}
// 获取添加字段项
function getDataDicts(val) {
  getFilesDicts(val)
    .then((res) => {
      state.definedOptions = res.data;
    })
    .catch(() => {});
}
// 动作总数据
function getFormData() {
  if (!echoDataDone) {
    return {
      MergeEventAction: props.data.actionsArray ? props.data.actionsArray[0]["properties"] : [],
      AlertAction: props.data.actionsArray ? props.data.actionsArray[1]["properties"] : [],
      AlertRelayAction: props.data.actionsArray ? props.data.actionsArray[2]["properties"] : [],

      AlertActionIsChecked: props.data.actionsArray ? props.data.actionsArray[1].isChecked : false,
      AlertRelayActionIsChecked: props.data.actionsArray ? props.data.actionsArray[2].isChecked : false,
    };
  }
  let listData = {
    MergeEventAction: definedRef.value != undefined ? definedRef.value.list : [],
    AlertAction: alarmRef.value != undefined ? alarmRef.value.list : [],
    AlertRelayAction: alertRelayRef.value != undefined ? alertRelayRef.value.list : [],
    AlertActionIsChecked: state.titData[1].checked,
    AlertRelayActionIsChecked: state.titData[2].checked,
  };

  return listData;
}

// 事件重定义
getDictsFn("1", "config_analysisrule_defaultMergeLogContent");
// 告警重定义：
getDictsFn("2", "config_analysisrule_defaultAlertContent");
// 获取字典
function getDictsFn(type, dictName) {
  getDictsData(dictName).then((res) => {
    if (type == "1") {
      state.eventRedefine = res[0];
      if (props.data && !props.data.actionsArray && definedForm.length > 0) {
        definedForm[0].value = state.eventRedefine.label;
        definedForm[0].valueText = state.eventRedefine.value;
      }
    }
    if (type == "2") {
      state.alertRedefine = res[0];
      if (props.data && !props.data.actionsArray && alarmForm.length > 0) {
        alarmForm[0].value = state.alertRedefine.label;
        alarmForm[0].valueText = state.alertRedefine.value;
      }
    }
  });
}
//父组件可以调用的方法
defineExpose({
  getFormData,
});
let { formData, titData, definedOptions } = toRefs(state);
</script>

<style lang="scss" scoped>
.tit {
  height: 62px;
  line-height: 62px !important;
  display: flex;
  justify-content: space-between;
  vertical-align: middle;
  .el-icon {
    cursor: pointer;
    line-height: inherit;
  }
  .el-checkbox {
    display: inline-block;
  }
}
.bg {
  padding: 20px;
  background: rgba(249, 249, 249, 1);
}
:deep .el-select {
  width: 100% !important;
}
.movement-relative {
  position: relative;
}
</style>
