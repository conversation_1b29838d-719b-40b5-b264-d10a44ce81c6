<template>
  <el-form :model="formData" ref="aggregaRuleFormRef" label-width="120px" label-position="left" class="jh" :rules="rules">
    <el-row :gutter="4">
      <el-col :span="12">
        <el-form-item label="查询时间范围:" prop="queryValue">
          <div class="info-wrapper">
            <el-tooltip effect="dark" :content="tipsData['queryTips']" placement="top">
              <el-icon><warning /></el-icon>
            </el-tooltip>
            <el-input-number
              controls-position="right"
              v-model="formData.queryValue"
              :min="1"
              :max="999999"
              :placeholder="editable ? '请输入时间范围' : ' '"
              @blur="salaryChanges"
              :disabled="!editable"
            />
            <el-select v-model="formData.queryType" placeholder="" class="time" :disabled="!editable">
              <el-option v-for="item in timeRecord" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="触发动作计数:" prop="countNumber">
          <div class="info-wrapper">
            <el-tooltip effect="dark" :content="tipsData['countTips']" placement="top">
              <el-icon><warning /></el-icon>
            </el-tooltip>
            <el-input-number
              controls-position="right"
              v-model="formData.countNumber"
              :min="1"
              :max="999999"
              :placeholder="editable ? '请输入触发动作计数' : ' '"
              @blur="salaryChanges"
              :disabled="!editable"
            />
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="4">
      <el-col :span="12" v-for="(item, index) in formList" :key="index">
        <xel-form-item
          collapse-tags
          v-model="formData[item.prop]"
          v-bind="item"
          :placeholder="editable ? item.placeholder : ' '"
          @update:text="getSelectLabel"
        >
        </xel-form-item
      ></el-col>
    </el-row>
    <el-row :gutter="4">
      <el-col :span="12">
        <el-input type="textarea" :clearable="true" v-model="state.formData.sameFieldsTextArray" rows="6" disabled></el-input>
      </el-col>
      <el-col :span="12">
        <el-input type="textarea" :clearable="true" v-model="state.formData.differentFieldsTextArray" rows="6" disabled></el-input>
      </el-col>
    </el-row>
    <el-row :gutter="4" class="runfreq">
      <el-col :span="$globalWindowSize == 'L' ? 12 : 15">
        <el-form-item label="运行频率间隔:" prop="intervalValue">
          <div class="info-wrapper">
            <el-input-number
              controls-position="right"
              v-model="formData.intervalValue"
              :min="1"
              :max="999999"
              :placeholder="editable ? '请输入运行频率间隔' : ' '"
              @blur="salaryChanges"
              :disabled="!editable"
            />
            <el-select v-model="formData.intervalType" placeholder="" class="time" :disabled="!editable">
              <el-option v-for="item in timeRecord" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="4" class="runInterval">
      <el-col :span="1">
        <el-checkbox v-model="formData.isTriggerControl" :true-label="1" :false-label="0" :disabled="!editable"></el-checkbox>
      </el-col>
      <el-col :span="$globalWindowSize == 'L' ? 12 : 15">
        <el-form-item label="相同字段触发动作时间间隔:" :label-width="$globalWindowSize == 'S' ? '92px' : '120px'">
          <div :class="[$globalWindowSize == 'S' ? 'info-wrapper11' : 'info-wrapper']">
            <el-input-number
              controls-position="right"
              v-model="formData.triggerControlValue"
              :min="1"
              :max="999999"
              :placeholder="editable ? '请输入时间间隔' : ' '"
              @blur="salaryChanges"
              :disabled="!editable"
            />
            <el-select v-model="formData.triggerControlType" placeholder="" class="time" :disabled="!editable">
              <el-option v-for="item in timeRecord" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="$globalWindowSize == 'S' ? 8 : 6">
        <el-input-number
          controls-position="right"
          v-model="formData.triggerControlCount"
          :min="1"
          :max="999999"
          :placeholder="editable ? '请输入触发条数' : ' '"
          @blur="salaryChanges"
          :disabled="!editable"
        />
      </el-col>
    </el-row>
    <el-row :gutter="6">
      <el-col :span="1">
        <el-checkbox v-model="formData.isDelay" :true-label="1" :false-label="0" :disabled="!editable"></el-checkbox>
      </el-col>
      <el-col :span="$globalWindowSize == 'L' ? 12 : 15">
        <el-form-item label="触发延迟:" :label-width="$globalWindowSize == 'S' ? '68px' : '100px'">
          <div class="info-wrapper">
            <el-tooltip effect="dark" :content="tipsData['delayTips']" placement="top">
              <el-icon><warning /></el-icon>
            </el-tooltip>
            <el-input-number
              controls-position="right"
              v-model="formData.delayValue"
              :min="1"
              :max="999999"
              :placeholder="editable ? '请输入触发延迟' : ' '"
              @blur="salaryChanges"
              :disabled="!editable"
            />
            <el-select v-model="formData.delayType" placeholder="" class="time" :disabled="!editable">
              <el-option v-for="item in timeRecord" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script setup>
import { ElMessage } from "element-plus";
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import { getDictsData } from "@/utils/getDicts";
import { getFilesDicts } from "@/api/sime/config/alarm";
const $globalWindowSize = window.$globalWindowSize;

//定义props属性
const props = defineProps({
  indexId: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  editable: {
    type: Boolean,
    default: true,
  },
});

let aggregaRuleFormRef = ref();
let rules = reactive({
  queryValue: [
    {
      required: true,
      message: "请填写时间范围",
      trigger: "blur",
    },
  ],
  countNumber: [
    {
      required: true,
      message: "请填写触发动作计数",
      trigger: "blur",
    },
  ],
  intervalValue: [
    {
      required: true,
      message: "请填写运行频率间隔",
      trigger: "blur",
    },
  ],
});
let state = reactive({
  formData: {
    isTriggerControl: 0,
    isDelay: 0,
    queryValue: null, //查询时间范围
    queryType: "", //查询时间范围类型
    countNumber: null, //触发动作计数
    sameFieldsArray: [], // 相同字段
    differentFieldsArray: [], // 不同字段
    intervalValue: null, //运行频率间隔
    intervalType: "", //intervalType
    triggerControlValue: null, //触发间隔
    triggerControlCount: null, //触发次数
    triggerControlType: "", //触发间隔时间类型
    delayValue: null, //延迟时间
    delayType: "", //延迟时间类型
    sameFieldsTextArray: [],
    differentFieldsTextArray: [],
  },
  timeRecord: [],
  tipsData: {},
  options: [],
});

watch(
  () => props.indexId,
  (val) => {
    if (val) {
      getDataDicts(val);
    }
  },
  { immediate: true }
);

//回显数据
watch(
  () => props.data,
  (val) => {
    get_time_list();
    if (val.indexId && props.indexId && val.indexId == props.indexId) {
      for (let key in state.formData) {
        state.formData[key] = val[key];
      }
      if (!state.formData.isTriggerControl) {
        state.formData.triggerControlValue = null;
        state.formData.triggerControlCount = undefined;
      }
      if (!state.formData.isDelay) {
        state.formData.delayValue = undefined;
      }
    } else {
      state.formData = {
        isTriggerControl: 0,
        isDelay: 0,
        queryValue: 1, //查询时间范围
        queryType: "", //查询时间范围类型
        countNumber: 1, //触发动作计数
        sameFieldsArray: [], // 相同字段
        differentFieldsArray: [], // 不同字段
        intervalValue: 1, //运行频率间隔
        intervalType: "", //intervalType
        triggerControlValue: 24, //触发间隔
        triggerControlCount: undefined, //触发次数
        triggerControlType: "", //触发间隔时间类型
        delayValue: undefined, //延迟时间
        delayType: "", //延迟时间类型
        sameFieldsTextArray: [],
        differentFieldsTextArray: [],
      };
      get_time_list("init");
    }
  },
  { immediate: true }
);
// 获取字段项
function getDataDicts(val) {
  getFilesDicts(val)
    .then((res) => {
      formList[0].options = res.data.map((item) => {
        return {
          label: item.alias,
          value: item.field,
        };
      });
      formList[1].options = formList[0].options;
      state.options = formList[0].options;
    })
    .catch(() => {});
}
// get_time_list();
// 运行记录时间范围字典类型
async function get_time_list(type) {
  let res = await getDictsData("config_analysisrule_timetype");
  state.timeRecord = res;

  if (type) {
    // 默认选择分
    state.formData.queryType = res[1].value;
    state.formData.intervalType = res[1].value;
    state.formData.triggerControlType = res[2].value;
    state.formData.delayType = res[1].value;
  }
}
get_tips_data();
// 规则信息提示字典类型
async function get_tips_data() {
  let res = await getDictsData("config_analysisrule_tips");
  res.forEach((item) => {
    state.tipsData[item.value] = item.label;
  });
}
// 弹框内容
let formList = reactive([
  {
    formType: "select",
    prop: "sameFieldsArray",
    label: "相同字段",
    options: state.options,
    multiple: true,
    disabled: !props.editable,
    placeholder: "请选择相同字段",
    filterable: true,
    onChange: (val) => {
      let isExistValue = [];
      isExistValue = state.formData.differentFieldsArray.filter((dItem) => val.indexOf(dItem) > -1);
      if (isExistValue.length > 0) {
        ElMessage.warning("不同字段中已经选择");
        state.formData.sameFieldsArray.splice((item) => item === val);
      } else {
        let nameData = state.options.filter((item) => val.indexOf(item.value) > -1);
        state.formData.sameFieldsTextArray = nameData.map((nItem) => {
          return nItem.label;
        });
      }
    },
  },
  {
    formType: "select",
    prop: "differentFieldsArray",
    label: "不同字段",
    options: state.options,
    placeholder: "请选择不同字段",
    multiple: true,
    disabled: !props.editable,
    filterable: true,
    onChange: (val) => {
      let isExistValue = [];
      isExistValue = state.formData.sameFieldsArray.filter((dItem) => val.indexOf(dItem) > -1);
      if (isExistValue.length > 0) {
        ElMessage.warning("相同字段中已经选择");
        state.formData.differentFieldsArray.splice((item) => item === val);
      } else {
        let nameData = state.options.filter((item) => val.indexOf(item.value) > -1);
        state.formData.differentFieldsTextArray = nameData.map((nItem) => {
          return nItem.label;
        });
      }
    },
  },
]);
// 动作总数据
function getAggFormData() {
  let aggData = {};
  aggregaRuleFormRef.value.validate((valid) => {
    if (valid) {
      aggData = { ...state.formData, validate: true };
    }
  });
  return aggData;
}
//父组件可以调用的方法
defineExpose({
  getAggFormData,
});
let { formData, timeRecord, tipsData, options } = toRefs(state);
</script>

<style lang="scss" scoped>
.jh {
  :deep .el-form-item__label {
    padding: 0 4px 0 0;
  }
  :deep .el-input__inner {
    /*font-size: 6px;*/
  }
}
.info-wrapper {
  display: flex;
  width: 100%;
  .el-icon {
    display: inline-block;
    line-height: 34px;
    margin-right: 4px;
  }
  .time {
    margin-left: 10px;
    width: 50%;
  }
}
.info-wrapper11 {
  display: flex;
  .input {
    width: 155px;
  }
  .time {
    margin-left: 5px;
    width: 50%;
  }
}
.runInterval {
  :deep .el-form-item__label {
    line-height: 1.5;
  }
}
.runfreq {
  padding-top: 20px;
  margin-bottom: 30px;
  border: 1px dotted rgba(222, 222, 222, 1);
}
:deep(.el-input-number.is-controls-right) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
