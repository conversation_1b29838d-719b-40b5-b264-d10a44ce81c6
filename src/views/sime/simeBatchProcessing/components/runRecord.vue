<template>
  <el-card class="bg-p-border-new">
    <div class="margin-bottom20 m0-new" v-if="type != 'test'">
      <el-button
        @click="changeTime(item)"
        class="search-button"
        v-for="item in timeRecord"
        :key="item.value"
        :class="{ active: item.value == isActive }"
      >
        <icon n="icon-date" :size="12" v-if="item.value == 'yesterday'"></icon>
        <icon n="icon-riqi-2" :size="12" v-if="item.value == 'today'"></icon>
        <icon n="icon-riqi" :size="12" v-if="item.value == 'near7day'"></icon>
        {{ item.label }}
      </el-button>
      <el-button class="search-button">
        <span class="autoText">自动刷新</span><el-switch @change="openAutoRefresh" v-model="autoSwitch" />
      </el-button>
    </div>
    <common-search
      v-if="type != 'test'"
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="isDetail ? [] : searchState.formList"
      @search="search"
      @reset="reset"
    >
      <template #form>
        <xel-form-item
          label="时间范围"
          type="datetimerange"
          form-type="daterange"
          label-width="73px"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model:start="searchState.startTime"
          v-model:end="searchState.endTime"
        ></xel-form-item
      ></template>
    </common-search>
    <xel-table v-if="pageType" ref="tableRef2" :columns="columns" :data="tableData" :pagination="false">
      <template #status="scope">
        <span class="marker" v-if="scope.row.status == 1"></span>
        <span class="marker red" v-else></span>
      </template>
      <template #start-end-time="scope">
        <span>{{ scope.row.startTime }}-{{ scope.row.endTime }}</span>
      </template>
      <template #trace="scope">
        <ul class="action-btns-ul">
          <li v-if="scope.row.alerts" @click="traceList(scope)">
            <el-tooltip content="追溯" placement="top" effect="light">
              <el-icon>
                <component :is="'position'" />
              </el-icon>
            </el-tooltip>
          </li>
        </ul>
      </template>
    </xel-table>
    <xel-table
      v-if="!pageType && searchState.timeWindow != ''"
      ref="tableRef3"
      :columns="columns"
      :load-data="getTableData"
      :defaultParams="{ timeWindow: searchState.timeWindow, ruleIds: isDetail ? ruleIds : searchState.data.ruleIds.join(',') }"
    >
      <template #status="scope">
        <span class="marker" v-if="scope.row.status == 1"></span>
        <span class="marker red" v-else></span>
      </template>

      <template #start-end-time="scope">
        <span>{{ scope.row.startTime }}-{{ scope.row.endTime }}</span>
      </template>
      <template v-slot:tableFooter>
        <p>
          <span>任务总数:{{ taskCount }}</span>
          <span class="point">成功:{{ succesTask }}</span>
          <span class="point point-red"
            >失败:<span>{{ failTask }}</span></span
          >
        </p>
      </template>
      <template #trace="scope">
        <ul class="action-btns-ul">
          <li v-if="scope.row.alerts" @click="traceList(scope)">
            <el-tooltip content="追溯" placement="top" effect="light">
              <el-icon>
                <component :is="'position'" />
              </el-icon>
            </el-tooltip>
          </li>
        </ul>
      </template>
    </xel-table>
  </el-card>
  <xel-dialog title="追溯" ref="traceDialog" @close="colseTraceDialog" width="60%" :ishiddenDialog="true">
    <Trace v-if="traceShow" :alerts="alerts.data" :indexId="alerts.indexId"></Trace>
  </xel-dialog>
</template>
<script setup>
import { getTreeData, batchRuleList as getTableData, batchRuleCount, testRunRule } from "@/api/sime/config/batchEngine";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
import Trace from "./trace.vue";
onActivated(() => {
  search(false);
});
import { getDictsData } from "@/utils/getDicts";
//定义props属性
const props = defineProps({
  pageType: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "",
  },
  groupId: {
    type: String,
    default: "",
  },
  ruleIds: {
    type: String,
    default: "",
  },
  isDetail: {
    type: Boolean,
    default: false,
  },
  tableData: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let isActive = ref("");
// 任务总数
let taskCount = ref(0);
let succesTask = ref(0);
let failTask = ref(0);
// // 获取任务数量
async function getTaskCount(data) {
  let res = await batchRuleCount(data);
  taskCount.value = res.data.totle > 0 ? res.data.totle : 0;
  succesTask.value = res.data.sucess > 0 ? res.data.sucess : 0;
  failTask.value = res.data.fail > 0 ? res.data.fail : 0;
}
let tableRef3 = ref();
//  是否自动刷新
let autoSwitch = ref(false);
var refreshFn = null;
function openAutoRefresh() {
  clearInterval(refreshFn);
  if (autoSwitch.value) {
    refreshFn = setInterval(search, 3000);
  }
}

//搜索相关
let searchState = reactive({
  data: {
    groupId: props.groupId,
    ruleIds: [],
    conditions: "",
  },
  menuData: [
    {
      lable: "状态:",
      prop: "status",
      sime: true,
      dictName: "batchengine_rulehistory_status",
    },
  ],
  timeWindow: "",
  startTime: "",
  endTime: "",
  formList: [
    {
      formType: "tree",
      prop: "ruleIds",
      label: "规则",
      multiple: true,
      disabledKey: "isGroupNode",
      treeOptions: {
        loadData: getTreeData, //接口名称
        params: {},
      },
      popWidth: "300px",
      treeProps: {
        id: "id",
        label: "name",
        children: "children",
      },
    },
    {
      prop: "conditions",
      label: "条件",
      labelWidth: "4em",
      itemWidth: "30%",
    },
  ],
});
// 获取规则
getRulesData();
async function getRulesData() {
  let res = await getTreeData();
  // state.treeData = res.rows;
  searchState.formList[0].treeData = res.data;
}
function search(initPageNum = true) {
  if (tableRef3.value) {
    tableRef3.value.reload(
      {
        ...searchState.data,
        ruleIds: props.isDetail ? props.ruleIds : searchState.data.ruleIds.join(","),
        startTime: searchState.startTime,
        endTime: searchState.endTime,
        timeWindow: searchState.timeWindow,
      },
      initPageNum
    );
  }

  getTaskCount({
    ...searchState.data,
    ruleIds: props.isDetail ? props.ruleIds : searchState.data.ruleIds.join(","),
    startTime: searchState.startTime,
    endTime: searchState.endTime,
    timeWindow: searchState.timeWindow,
  });
}
function reset() {
  searchState.data = {
    groupId: props.groupId,
    ruleIds: [],
  };
  searchState.startTime = "";
  searchState.endTime = "";
  search();
}
//搜索结束
// 获取
function changeTime(data) {
  isActive.value = data.value;
  searchState.timeWindow = data.value;
  searchState.startTime = "";
  searchState.endTime = "";
  search();
}
// 列表配置项
const columns = [
  {
    prop: "status",
    label: "状态",
    slotName: "status",
  },
  {
    prop: "ruleName",
    label: "规则名称",
  },
  {
    prop: "runTime",
    label: "运行时间",
  },
  {
    prop: "time",
    label: "数据范围",
    slotName: "start-end-time",
  },
  {
    prop: "message",
    label: "执行信息",
  },
  {
    prop: "costTime",
    label: "耗时(毫秒)",
  },
  {
    label: "操作",
    slotName: "trace",
  },
];
function traceList(scope) {
  alerts.data = JSON.parse(scope.row.alerts);
  alerts.indexId = scope.row.indexId;
  traceShow.value = true;
  traceDialog.value.open();
}
let state = reactive({
  timeRecord: [],
  treeData: [],
});
get_time_list();
// 运行记录时间范围字典类型
async function get_time_list() {
  let res = await getDictsData("batchengine_rulehistory_timewindow");
  state.timeRecord = res;
  changeTime(state.timeRecord[1]);
}

let { timeRecord, treeData } = toRefs(state);
// 追溯
let traceDialog = ref();
let alerts = reactive({
  data: [],
  indexId: "",
});
let traceShow = ref(false);
function colseTraceDialog() {
  alerts.data = [];
  alerts.indexId = "";
  traceShow.value = false;
}
</script>
<style scoped lang="scss">
.marker {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 100px;
  background-color: rgba(80, 195, 12, 1);
}
.red {
  background-color: rgba(249, 121, 121, 1);
}
.autoText {
  margin-right: 12px;
}
.point {
  color: rgba(80, 195, 12, 1);
}
.point-red {
  color: rgba(249, 121, 121, 1);
}
.point:before {
  vertical-align: middle;
  margin: 0px 4px 0 10px;
  display: inline-block;
  content: "";
  width: 16px;
  height: 16px;
  border-radius: 100px;
  background: rgba(80, 195, 12, 1);
}
.point-red:before {
  background: rgba(249, 121, 121, 1);
}

.active {
  color: $color;
  //
  border-color: #fadcc3;
  outline: none;
  background-color: #fdf3eb;
}
</style>
