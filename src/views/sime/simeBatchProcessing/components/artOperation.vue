<template>
  <component
    :is="isDialog ? xelDialog : divComponent"
    :size="'large'"
    title="人工运行"
    ref="dialogRef"
    @submit="submitForm"
    @close="closeDialog(false)"
    :dataObject="dataObject"
    :ishiddenDialog="true"
  >
    <el-row class="sime-layout-wrapper">
      <el-col :span="18">
        <el-form ref="formRef" :model="formData">
          <el-form-item
            prop="time"
            label="时间范围"
            :rules="[
              {
                required: true,
                message: '请选择时间范围',
                trigger: 'blur',
              },
            ]"
          >
            <el-date-picker
              v-model="formData.time"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :offset="2" :span="4">
        <el-button @click="runFn('test')" :loading="testloading"> 测试 </el-button>
        <el-button @click="runFn('run')" v-if="isDialog" :loading="runloading"> 运行 </el-button>
      </el-col>
    </el-row>
    <el-row>
      <p class="info">仅展示有告警的记录</p>
      <el-switch v-model="hasAlarm" :loading="alarmLoading" :before-change="beforeAlarmChange" />
    </el-row>
    <RunRecord v-if="isshow" type="test" :pageType="pageType" :tableData="tableData"></RunRecord>
  </component>
</template>
<script setup>
import xelDialog from "@/xelComponents/xelDialog.vue";
import divComponent from "./divComponent.vue";
import RunRecord from "./runRecord.vue";
import { ref, reactive, onMounted, toRefs, watch } from "vue";
import runRecordVue from "./runRecord.vue";
import { testRunRule, runSingle } from "@/api/sime/config/batchEngine";
import { ElMessageBox, ElMessage } from "element-plus";
const emit = defineEmits("close", "getUserInfoData");
//定义props属性
const props = defineProps({
  editInfo: {
    type: Object,
    default() {
      return {};
    },
  },
  id: {
    type: String,
    default: "",
  },
  multipleSelection: {
    type: Array,
    default: () => {
      return [];
    },
  },
  isDialog: {
    type: Boolean,
    default: true,
  },
  pageType: {
    type: Boolean,
    default: false,
  },
  dataObject: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let isshow = ref(false);
// 测试是否有告警
let hasAlarm = ref(false);
let alarmLoading = ref(false);
let testloading = ref(false);
let runloading = ref(false);
let formRef = ref();
let ruleForm = reactive({});
// 打开弹框
let dialogRef = ref();
let state = reactive({
  searchState: {
    startTime: "",
    endTime: "",
    ruleIds: "",
  },
  formData: {
    time: "",
  },
});
let tableData = ref([]);
let testObject = {};

onMounted(() => {
  if (props.isDialog) {
    dialogRef.value.open();
  }
});
// 刷选列表有告警的数据
let initTableData = [];
function beforeAlarmChange() {
  alarmLoading.value = true;
  return new Promise((resolve) => {
    setTimeout(() => {
      if (!hasAlarm.value) {
        tableData.value = initTableData.filter((item) => {
          return item.alerts;
        });
      } else {
        tableData.value = initTableData;
      }

      alarmLoading.value = false;
      ElMessage.success("操作成功");
      return resolve(true);
    }, 1000);
  });
}
//列表重置
function resetFormData() {
  state.searchState = {
    startTime: "",
    endTime: "",
    ruleIds: "",
  };
}

// 关闭取消方法
function closeDialog() {
  resetFormData();
  dialogRef.value.close();
  emit("closeDialog");
}
// 提交
function submitForm(close, loading) {
  ruleForm.value.validate((valid) => {
    if (valid) {
    } else {
      return false;
    }
  });
}
let isPerform = true;
watch(
  () => props.dataObject.formData,
  (val) => {
    if (isPerform) {
      isPerform = false;
    }
    setTimeout(() => {
      isPerform = true;
    }, 500);
  },
  { immediate: true, deep: true }
);
/**没有用到的的多余代码 */
/* let conditions = [];
// // 条件
watch(
  () => props.dataObject.conditionsObject,
  (val) => {
    if (val.getList()) {
      conditions = val.getList();
      console.log("conditions: ", conditions);
    }
  },
  { deep: true }
); */
// // 聚合
let aggData = {};
watch(
  () => props.dataObject.aggData,
  (val) => {
    if (val.getAggFormData()) {
      aggData = val.getAggFormData();
    }
  },
  { deep: true }
);
// // 动作
let actionData = reactive({});
watch(
  () => props.dataObject.moveData,
  (val) => {
    if (val.getFormData()) {
      actionData = val.getFormData();
    }
  },
  { deep: true }
);

// 测试 / 运行
function runFn(type) {
  formRef.value.validate((valid) => {
    if (valid) {
      type == "test" ? (testloading.value = true) : (runloading.value = true);
      let ruleIds = props.multipleSelection.map((v) => v.ruleId).join();
      let params = {
        ruleIds: ruleIds,
        startTime: state.formData.time[0],
        endTime: state.formData.time[1],
        isTest: type == "test" ? true : false,
      };
      if (!props.isDialog) {
        testObject = { ...props.dataObject.formData, groupId: props.dataObject.groupId };
        /*testObject.conditionsObject = conditions[0];*/
        delete testObject.conditions;
        // 动作
        let MergeEventAction = []; //事件重定义
        let AlertAction = []; //生成告警
        let AlertRelayAction = []; //告警转换
        if (Object.keys(actionData).length != 0) {
          actionData.MergeEventAction.forEach((mItem) => {
            mItem.forEach((mv) => {
              MergeEventAction.push({
                formType: mv.formType,
                field: mv.prop,
                fieldText: mv.label,
                value: mv.value,
                valueText: mv.valueText,
                dictionaryType: mv.dictName ? mv.dictName : "",
              });
            });
          });
          actionData.AlertAction.forEach((mItem) => {
            mItem.forEach((mv) => {
              AlertAction.push({
                formType: mv.formType,
                field: mv.prop,
                fieldText: mv.label,
                value: mv.value,
                valueText: mv.valueText,
                dictionaryType: mv.dictName ? mv.dictName : "",
              });
            });
          });
          // 告警转发勾选了，但是没填值

          let isEmpty = false;
          actionData.AlertRelayAction.forEach((mv) => {
            if (!mv.selectInfo.field || !mv.treeInfo.value) {
              isEmpty = true;
            }
            AlertRelayAction.push({
              field: mv.selectInfo.field,
              fieldText: mv.selectInfo.fieldText,
              value: mv.treeInfo.value,
              valueText: mv.treeInfo.valueText,
            });
          });
          if (isEmpty || (actionData.AlertRelayActionIsChecked && actionData.AlertRelayAction.length == 0)) {
            ElMessage.warning("告警转发配置不完整，请确认");
            return;
          }
          testObject.actionsArray = [
            { action: "MergeLogAction", properties: MergeEventAction, isChecked: true },
            { action: "AlertAction", properties: AlertAction, isChecked: actionData.AlertActionIsChecked },
            { action: "AlertRelayAction", properties: AlertRelayAction, isChecked: actionData.AlertRelayActionIsChecked },
          ];
        }
        testObject.actions = JSON.stringify(testObject.actionsArray); // 动作数据
        let data = { startTime: state.formData.time[0], endTime: state.formData.time[1], isTest: true };
        runSingle(data, Object.assign(testObject, aggData))
          .then((res) => {
            ElMessage.success("操作成功");
            isshow.value = true;
            tableData.value = res.data.rows;
            initTableData = res.data.rows;
          })
          .finally(() => {
            testloading.value = false;
            runloading.value = false;
          });
        return;
      } else {
        testRunRule(params)
          .then((res) => {
            ElMessage.success("操作成功");
            isshow.value = true;
            tableData.value = res.data.rows;
            initTableData = res.data.rows;
          })
          .finally(() => {
            testloading.value = false;
            runloading.value = false;
          });
      }
    }
  });
}

let { searchState, formData } = toRefs(state);
</script>

<style lang="scss" scoped>
.inlineRun {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.info {
  margin: 0 10px 15px 0;
}
</style>
