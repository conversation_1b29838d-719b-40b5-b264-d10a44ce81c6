<template>
  <xel-dialog title="关系拓扑图" ref="dialogRef" width="1100px" ishiddenDialog="true">
    <section>
      <section class="legend-box">
        <div class="green-sign">过滤器</div>
        <div class="blue-sign">分析规则</div>
      </section>
      <span class="remarks-text">注：★代表当前节点 </span>
    </section>
    <div style="width: 1050px; height: 560px" ref="grid" id="diagramId"></div>
  </xel-dialog>
</template>

<script setup>
import { ref, nextTick } from "vue";
import * as echarts from "echarts";
// 弹出窗
const dialogRef = ref();
const props = defineProps({
  diagramData: {
    type: Object,
    default() {
      return {};
    },
  },
});
// 关系图
const grid = ref();
let myChart = null;
const option = ref({
  title: {
    show: false,
    text: "关系拓扑图",
  },
  legend: [
    {
      data: [{ name: "过滤器" }, { name: "规则" }],
      show: true,
    },
  ],
  tooltip: {},
  animationDurationUpdate: 1500,
  animationEasingUpdate: "quinticInOut",
  series: [
    {
      type: "graph",
      layout: "force",
      animation: false,
      symbolSize: 100,
      edgeSymbol: ["circle", "arrow"],
      edgeSymbolSize: [4, 10],
      force: {
        repulsion: 350, //节点之间的斥力因子
        edgeLength: 350, //两个节点之间的距离
        layoutAnimation: "move",
      },
      // roam: "scale",
      label: {
        show: true,
        //换行显示
        formatter: function (e) {
          var maxNameLength = 6; // 每行最多显示的字符数
          var name = e.name;
          var nameLength = name.length;

          // 如果字符串长度不超过最大字符数，直接返回原字符串
          if (nameLength <= maxNameLength) {
            return name;
          }
          // 计算需要多少行来显示整个字符串
          var numRows = Math.ceil(nameLength / maxNameLength) > 3 ? 3 : Math.ceil(nameLength / maxNameLength); //超过三行，则显示三行
          var formattedString = "";
          // 循环处理每一行
          for (var i = 0; i < numRows; i++) {
            var start = i * maxNameLength; // 当前行的起始位置
            var end = Math.min(start + maxNameLength, nameLength); // 当前行的结束位置，不超过字符串总长度
            var rowContent = name.slice(0, maxNameLength * 3).substring(start, end); // 截取当前行的内容
            if (i > 1) {
              //多余两行，则显示省略号
              rowContent = "...";
            }

            // 如果不是最后一行，则在末尾添加换行符
            if (i < numRows - 1) {
              rowContent += "\n";
            }
            formattedString += rowContent; // 拼接当前行的内容到格式化后的字符串中
          }
          return formattedString;
        },
      },
      data: [],
      links: [],
      lineStyle: {
        normal: {
          opacity: 0.9,
          width: 2,
          curveness: 0.5,
        },
      },
      categories: [{ name: "main" }, { name: "server" }, { name: "ip" }, { name: "domain" }, { name: "soft" }, { name: "port" }, { name: "dept" }],
      emphasis: {
        focus: "adjacency",
        lineStyle: {
          width: 10,
        },
      },
    },
  ],
});
// 获取数据
function getData() {
  let data = props.diagramData;
  if (data.length > 0) {
    const { dataSelf, linksSelf } = flattenDiagramData(data);
    myChart = echarts.init(grid.value);
    option.value.series[0].data = dataSelf;
    option.value.series[0].links = linksSelf;
    option.value.tooltip = {
      trigger: "item",
      formatter: function (params) {
        if (params.data.name) {
          // 如果是节点，则显示节点名
          return params.data.name;
        } else {
          // 否则显示线数据：节点名 -> 目标节点名
          const target = dataSelf.find((item) => item.id === params.data.target);
          const source = dataSelf.find((item) => item.id === params.data.source);
          return source.name + " -> " + target.name;
        }
      },
    };
    myChart.setOption(option.value);
    if (linksSelf.length > 0) {
      mouseup();
    }
  }
}
// 获取全部数据和去重后的数据 节点集合
function flattenDiagramData(diagramData) {
  let dataSelf = []; // 用来存储全部节点数据
  let linksSelf = []; // 用来存储全部路线数据
  // 递归函数来遍历 props.diagramData 数组 处理数据
  function traverseNodes(nodes, fromNodeId, isFromId = false) {
    nodes.forEach((node) => {
      const nodeData = {
        id: node.id,
        name: (node.centerNode ? "★" : "") + node.name,
        centerNode: node.centerNode,
        category: node.type == 1 ? 1 : 0, // 根据层级设置category 0为分析规则（蓝色：主节点），1为过滤器（绿色）
        draggable: true,
        label: {
          color: "#fff",
          fontSize: node.centerNode ? 13 : 12,
          fontWeight: node.centerNode ? "bold" : "normal",
          fontStyle: node.centerNode ? "italic" : "normal",
          lineHeight: 16,
        },
      };

      let existingNodeData = dataSelf.find((item) => item.id === node.id); // 检查id是否已存在于dataSelf中
      if (existingNodeData) {
        if (node.centerNode) {
          // 如果存在，则将当前主节点的label属性覆盖到dataSelf中
          existingNodeData.name = "★" + existingNodeData.name;
          existingNodeData.label = {
            fontSize: 13,
            fontWeight: "bold",
            fontStyle: "italic",
          };
        }
      } else {
        dataSelf.push(nodeData); // 如果不存在，添加到dataSelf
      }
      const linkData = {
        source: isFromId ? fromNodeId : node.id,
        target: isFromId ? node.id : fromNodeId,
      };
      if (fromNodeId) {
        // 有流出节点时将当前节点路线添加到linksSelf
        linksSelf.push(linkData);
      }
      // 如果节点有toNode属性且是数组，则递归遍历toNode数组
      if (Array.isArray(node.toNode)) {
        traverseNodes(node.toNode, node.id);
      }
      // 如果node.centerNode为true--主节点，则遍历fromNode，添加流出节点
      if (node.centerNode && Array.isArray(node.fromNode)) {
        traverseNodes(node.fromNode, node.id, true);
      }
    });
  }
  traverseNodes(diagramData, null);
  return {
    dataSelf,
    linksSelf,
  };
}
// 拖动节点
function mouseup() {
  myChart.on("mouseup", function (params) {
    var option = myChart.getOption();
    option.series[0].data[params.dataIndex].x = params.event.offsetX;
    option.series[0].data[params.dataIndex].y = params.event.offsetY;
    option.series[0].data[params.dataIndex].fixed = true;
    myChart.setOption(option);
  });
}

function open() {
  dialogRef.value.open();
  nextTick(() => {
    getData();
  });
}
defineExpose({
  open,
});
//点击节点跳转 后期加
// if (NodeList.value.length == 0) {
// getClick();
//   }
// function getClick() {
//     myChart.on("click", function (param) {
//       //获取节点点击的数组序号
//       if (param.dataType == "node") {//点击了节点
//         dialogOpen(param.data.id);
//       }
//     });
// }
// const NodeList = ref([]);
// function dialogOpen(queryFilterIds) {
//   NodeList.value = [];
//   // 获取过滤器信息
//   getFilterRefInfo({ queryFilterIds }).then((res) => {
//     NodeList.value = res.data;
//       getData(NodeList.value);
//   });
// }
</script>

<style lang="scss" scoped>
.legend-box {
  display: flex;
  .green-sign,
  .blue-sign {
    margin: 0 70px 10px 0;
    position: relative;
    padding-top: 4px;
  }
  .green-sign::before,
  .blue-sign::before {
    content: " ";
    border-radius: 50%;
    position: absolute;
    width: 22px;
    height: 22px;
    right: -33px;
    top: 2px;
  }
  .green-sign::before {
    background-color: #90cd74;
  }
  .blue-sign::before {
    // border-radius: 30%;
    background-color: #5470c6;
  }
}
.remarks-text {
  font-size: 13px;
  color: #000;
  font-weight: bold;
}
</style>
