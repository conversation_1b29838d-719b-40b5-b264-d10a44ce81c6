<template>
  <section>
    <span @click="changefold()" class="item-fold">
      <el-icon v-if="isShow"><arrow-right /></el-icon>
      <el-icon v-else><arrow-down /></el-icon>
    </span>
    <el-form v-if="isShow" ref="dynamicValidateFormRef" :label-width="labelWidth" label-position="right" class="">
      <!-- 每一行 -->
      <div v-for="(item, rowIndex) in rows" :key="item">
        <xel-form-item
          v-for="(domain, index) in item"
          v-model="domain.value"
          @update:text="domain.valueText = $event"
          @change="changeValue(domain.formType, $event, domain)"
          :key="index"
          v-bind="domain"
          class="xelItem"
          :disabled="!editable"
        >
        </xel-form-item>
        <el-button
          v-show="item.length > 0"
          @click.prevent="removeDomain(rowIndex)"
          icon="el-icon-delete"
          class="delBtn pull-right"
          :disabled="!editable"
        ></el-button>
      </div>
      <el-form-item>
        <el-popover :width="500" placement="bottom" trigger="click" :disabled="!editable">
          <template #reference>
            <div class="dynamic-add pointer">
              <el-icon :size="18"><plus /></el-icon>
              添加{{ dynamicName }}
            </div>
          </template>
          <el-select
            :disabled="!editable"
            filterable
            placeholder="Select"
            :modelValue="modelValue"
            @update:modelValue="$emit('update:modelValue', $event)"
          >
            <el-option v-for="item in options" :key="item.field" :label="item.alias" :value="item.field" @click="addDomain(item)"> </el-option>
          </el-select>
        </el-popover>
      </el-form-item>
    </el-form>
  </section>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onMounted, watch } from "vue";
import { getIndexLoadFields } from "../../../../api/sime/config/log";
import { parseTime } from "@/utils/ruoyi";
const emit = defineEmits(["delete", "update:modelValue", "changeTree", "update:start", "update:end", "changeOptions"]);
let props = defineProps({
  dynamicName: {
    type: String,
    default: "",
  },
  formList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  labelWidth: {
    type: String,
    default: "auto",
  },
  dataRows: {
    type: Array,
    default: () => {
      return [];
    },
  },
  options: {
    type: Array,
    default: () => {
      return [];
    },
  },
  editable: {
    type: Boolean,
    default: true,
  },
});
let value1 = ref("");
let dynamicValidateFormRef = ref();
let listStr = JSON.stringify(props.formList);
let rows = reactive([JSON.parse(listStr)]);
//回显已有的数据

watch(
  () => props.dataRows.length,
  (val) => {
    if (val > 0) {
      echoMovemoment(props.dataRows);
    }
  },
  { immediate: true, deep: true }
);
//根据数据，回显对应的conditionList
function echoMovemoment(list) {
  let arr = [];
  for (let item of list) {
    let formInfo = {
      formType: item.formType,
      prop: item.field,
      label: item.fieldText,
      value: item.value,
      valueText: item.valueText,
      itemWidth: "calc((97% - 50px) )",
    };
    if (item.formType == "select") {
      arr.push({ ...formInfo, sime: true, dictName: item.dictionaryType });
    } else if (item.formType == "date") {
      arr.push({ ...formInfo, value: item.value, type: "datetime", valueFormat: "x", format: "YYYY-MM-DD HH:mm:ss" });
    } else {
      arr.push(formInfo);
    }
    //
  }
  rows.splice(0, 1);
  arr.forEach((item) => {
    rows.push([item]);
  });
}
let valueText = ref("");
function changeValue(valuetype, val, domain) {
  if (!val) return;

  switch (valuetype) {
    case "input":
      domain.valueText = val;
      break;
    case "date":
      domain.valueText = parseTime(val);
      break;
  }
  return domain;
}

function addDomain(data) {
  let arr = [];
  let formInfo = {
    formType: "input",
    prop: data.field,
    label: data.alias,
    value: "",
    valueText: "",
    itemWidth: "calc((97% - 50px) )",
    disabled: !props.editable,
  };
  if (data.dictionaryType) {
    arr.push({ ...formInfo, formType: "select", sime: true, dictName: data.dictionaryType, disabled: !props.editable });
  } else if (data.field.indexOf("time") != -1) {
    arr.push({ ...formInfo, formType: "date", type: "datetime", valueFormat: "x", format: "YYYY-MM-DD HH:mm:ss", disabled: !props.editable });
  } else {
    arr.push({ ...formInfo, disabled: !props.editable });
  }

  rows.push(JSON.parse(JSON.stringify(arr)));
}
function removeDomain(index) {
  rows.splice(index, 1);
}

const isShow = ref(true);
function changefold() {
  isShow.value = !isShow.value;
}
defineExpose({
  list: rows,
});
</script>
<style lang="scss" scoped>
:deep {
  .xelItem {
    // display: block;

    // margin-right: 1.5%;
    &:last-of-type {
      margin-right: 0;
    }
  }
  .delBtn {
    // margin-left: 10px;
    margin: -50px 10px 0 0;
  }
}
.dynamic-add {
  width: calc(100% - 70px);
}
.el-select {
  width: 100% !important;
}
.item-fold {
  position: absolute;
  right: 0;
  top: -40px;
}
</style>
