<template>
  <p v-if="dynamicName" class="title-bottom-line">{{ dynamicName }}</p>
  <section>
    <el-form ref="dynamicValidateFormRef" label-position="left" class="" label="转发配置">
      <!-- 每一行 -->
      <el-row :gutter="20" v-for="(item, rowIndex) in rows" :key="item" :label-width="labelWidth">
        <!-- 每一行中的表单项列表 1或者3 -->
        <el-col :span="12">
          <el-form-item label="转发配置">
            <el-select
              :clearable="true"
              v-model="item.selectInfo.field"
              :placeholder="editable ? '请选择' : ' '"
              :disabled="!editable"
              @change="getTreeData(rowIndex, item.selectInfo)"
            >
              <el-option v-for="fItem in forwarData" :key="fItem.value" :label="fItem.label" :value="fItem.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label=" ">
            <xel-select-tree
              :multiple="false"
              label=""
              :data="item.treeData"
              v-model="item.treeInfo.value"
              :treeProps="treeProps"
              :disabled="!editable"
              @update:modelValue="selectTree(rowIndex)"
            />
          </el-form-item>
        </el-col>
        <!-- tree -->
        <el-col :span="2">
          <el-button :disabled="!editable" @click.prevent="removeDomain(rowIndex)" icon="el-icon-delete" class="delBtn pull-right"></el-button>
        </el-col>
      </el-row>
      <el-form-item>
        <div v-if="editable" class="dynamic-add pointer" @click="addDomain">
          <el-icon :size="18"><plus /></el-icon>
          添加{{ dynamicName }}
        </div>
        <div class="dynamic-add pointer" v-else>
          <el-icon :size="18"><plus /></el-icon>
          添加{{ dynamicName }}
        </div>
      </el-form-item>
    </el-form>
  </section>
</template>
<script setup>
import { getMessageList } from "@/api/sime/config/batchEngine.js";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onMounted, watch } from "vue";
import { getDictsData } from "@/utils/getDicts";
let props = defineProps({
  dynamicName: {
    type: String,
    default: "",
  },
  formList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  labelWidth: {
    type: String,
    default: "100px",
  },
  dataRows: {
    type: Array,
    default: () => {
      return [];
    },
  },
  editable: {
    type: Boolean,
    default: true,
  },
});
let state = reactive({
  forwarData: [],
  rows: [
    {
      selectInfo: {
        field: "",
        fieldText: "",
      },
      treeInfo: { value: "", valueText: "" },
      treeData: [],
    },
  ],
  treeProps: {
    id: "id",
    label: "name",
    children: "children",
  },
});
let { forwarData, rows, treeProps } = toRefs(state);
let selResult = ref(false);
let treeResult = ref(false);
// 获取转发配置字典项
get_forward_list();
// 转发配置项
async function get_forward_list() {
  let res = await getDictsData("config_analysisrule_alertRelayType");
  state.forwarData = res;
}

let dynamicValidateFormRef = ref();

//回显已有的数据

watch(
  () => props.dataRows.length,
  (val) => {
    echoIpRows(props.dataRows);
  },
  {
    immediate: true,
    deep: true,
  }
);
function echoIpRows(list) {
  list.forEach(async (item, index) => {
    if (item.field && item.value) {
      state.rows.push({
        selectInfo: {
          field: item.field,
          fieldText: item.fieldText,
        },
        treeInfo: { value: item.value, valueText: item.valueText },
        treeData: await getEchoTreeData(item.field),
      });
    }
  });
  state.rows.splice(0, 1);
}
// 获取相应树数据
async function getEchoTreeData(field) {
  let res = await getMessageList(field, {});
  return res.data;
}
// 获取相应的树的数据
async function getTreeData(index, data) {
  if (data.field) {
    let res = await getMessageList(data.field, {});
    state.rows[index].treeInfo = {
      value: "",
      valueText: "",
    };
    state.rows[index].treeData = res.data;
    data.fieldText = data.field ? state.forwarData.find((ele) => ele.value === data.field).label : "";
  }
}
// 深度比较两个对象是否相同

function deepEqual(obj1, obj2) {
  if (obj1 === obj2) {
    // 本身就是内存中的同一个对象
    return true;
  } else if (typeof obj1 === "object" && obj1 != null && typeof obj2 === "object" && obj2 != null) {
    if (Object.keys(obj1).length !== Object.keys(obj2).length) {
      // key的数量不同 则两个对象一定不同
      return false;
    }
    for (let prop in obj1) {
      if (Object.prototype.hasOwnProperty.call(obj2, prop)) {
        // obj1中的某个key在obj2中也有
        // 由于对象可以多层嵌套  所以需要递归比较
        // 递归到某个层级返回false的时候  整体结果就是false
        if (!deepEqual(obj1[prop], obj2[prop])) return false;
      } else {
        return false;
      }
    }
    return true;
  } else {
    return false;
  }
}
function selectTree(curIndex) {
  selResult.value = false;
  treeResult.value = false;
  for (let index = 0; index < state.rows.length; index++) {
    selResult.value = deepEqual(state.rows[index].selectInfo, state.rows[curIndex].selectInfo);
    if (selResult.value && curIndex != index) {
      treeResult.value = state.rows[index].treeInfo.value == state.rows[curIndex].treeInfo.value;
    }
    if (selResult.value && treeResult.value) {
      state.rows[curIndex].selectInfo = {};
      state.rows[curIndex].treeInfo = {};
      ElMessage({
        type: "warning",
        message: "告警转发配置存在重复",
      });
      return;
    }
  }
}
// 添加
function addDomain() {
  let isEmpty = false;
  state.rows.forEach((item, index) => {
    if (!item.selectInfo.field || !item.treeInfo.value) {
      isEmpty = true;
    }
  });
  if (isEmpty) {
    ElMessage.warning("告警转发配置不完整，请确认");
    return;
  }
  state.rows.push({
    selectInfo: {
      field: "",
      fieldText: "",
    },
    treeInfo: { value: "", valueText: "" },
    treeData: [],
  });
}
function removeDomain(index) {
  // selResult.value = false;
  // treeResult.value = false;
  state.rows.splice(index, 1);
  // let curIndex = state.rows.length - 1;
  // for (let index = 0; index < state.rows.length; index++) {
  //   selResult.value = deepEqual(state.rows[index].selectInfo, state.rows[curIndex].selectInfo);
  //   if (selResult.value && curIndex != index) {
  //     treeResult.value = state.rows[index].treeInfo.value == state.rows[curIndex].treeInfo.value;
  //   }
  //   if (selResult.value && treeResult.value) {
  //     state.rows[curIndex].isRepeat = true;
  //     ElMessage({
  //       type: "warning",
  //       message: "告警转发存在重复",
  //     });
  //     return;
  //   } else {
  //     state.rows[curIndex].isRepeat = false;
  //   }
  // }
}

defineExpose({
  list: rows,
});
</script>
<style lang="scss" scoped>
:deep {
  .xelItem {
    display: inline-flex;
    margin-right: 1.5%;
    &:last-of-type {
      margin-right: 0;
    }
  }
  .delBtn {
    margin-left: 10px;
  }
}
.dynamic-add {
  width: calc(100% - 70px);
}
</style>
