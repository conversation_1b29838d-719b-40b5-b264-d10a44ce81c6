<template>
  <!-- 天狼星 - 系统维护 - 背景 -->
  <div class="cardTitDiy">
    <p>{{ title }}</p>
    <div class="mt5">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default() {
      return "未知名称";
    },
  },
});
</script>

<style scoped lang="scss">
.cardTitDiy {
  background: #f9f9f9;
  border-radius: 20px;
  width: 100%;
  padding: 15px;
  display: inline-block;
  > p {
    font-weight: bold;
  }
}
</style>
