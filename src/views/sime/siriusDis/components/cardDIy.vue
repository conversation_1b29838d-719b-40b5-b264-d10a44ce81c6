<template>
  <!-- 自定义 - 卡片 -->
  <div class="cardDiv">
    <el-row :gutter="24">
      <el-col :span="7">
        <div class="tap-tubiao">
          <icon :n="itemData.icon" :size="30" />
        </div>
      </el-col>
      <el-col :span="17">
        <p class="tap-title">{{ itemData.title }}</p>
        <p class="tap-number">{{ itemData.number || 0 }}</p>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
defineProps({
  itemData: {
    type: Object,
    default() {
      return {
        icon: "icon-IP1",
        title: "未知数据",
        number: 0,
      };
    },
  },
});
</script>

<style scoped lang="scss">
.cardDiv {
  display: inline-block;
  margin: 5px;
  width: 280px;
  border-radius: 20px;
  padding-left: 10px;
  padding-bottom: 10px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.12);
}
</style>
