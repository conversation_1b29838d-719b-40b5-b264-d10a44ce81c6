<template>
  <xel-dialog
    :title="isAdd ? '新增白名单' : '编辑白名单'"
    ref="dialogRef"
    @submit="saveSub"
    :show-submit="true"
    buttonCancel="关闭"
    @closed="closeDia"
  >
    <el-form :model="formData" ref="ruleFormRef" label-width="80px" size="mini">
      <!-- 基本信息 -->
      <div>
        <p class="title-bottom-line">基本信息</p>
        <el-row :gutter="20">
          <el-col :span="11" v-for="item in formList" :key="item.prop">
            <xel-form-item v-model="formData[item.prop]" v-bind="item"></xel-form-item>
          </el-col>
        </el-row>
        <p class="infoP">以上基本信息，必须有一个不为空</p>
      </div>
    </el-form>
  </xel-dialog>
</template>

<script>
export default {
  name: "addSiriusDia",
};
</script>

<script setup>
import { ref, reactive, toRefs } from "vue";
import { ElMessage } from "element-plus";
import { rules } from "@/xelComponents/utils/formValidator";
import { addWhiteList, editWhiteList } from "@/api/sime/siriusDis/index";

const emits = defineEmits(["close"]);

let dialogRef = ref();
let isAdd = ref(true);
let nodeId = ref("");

let state = reactive({
  formData: {
    /* 基本信息 */
    id: "",
    rule: "",
    sip: "",
    dip: "",
    dport: undefined,
    domain: "",
    realSip: "",
    url: "",
  },
  saveLoading: false,
});
let { formData, saveLoading } = toRefs(state);

/* 基本信息 */
let formList = ref([
  {
    formType: "input",
    prop: "rule",
    label: "告警名",
    size: "mini",
    type: "text",
  },
  {
    formType: "input",
    prop: "sip",
    label: "源IP",
    size: "mini",
    type: "text",
  },
  {
    formType: "input",
    prop: "dip",
    label: "目的IP",
    size: "mini",
    type: "text",
  },
  {
    formType: "number",
    prop: "dport",
    label: "目的端口",
    size: "mini",
    required: false,
    min: 0,
    max: 65535,
    precision: "0",
    placeholder: "目的端口",
  },
  {
    formType: "input",
    prop: "domain",
    label: "HOST",
    size: "mini",
    type: "text",
  },
  {
    formType: "input",
    prop: "realSip",
    label: "XFF",
    size: "mini",
    type: "text",
  },
  {
    formType: "input",
    prop: "url",
    label: "URL",
    size: "mini",
    type: "textarea",
  },
]);

/* 确定按钮事件 */
let ruleFormRef = ref();
const saveSub = (close, loading) => {
  ruleFormRef.value.validate((valid) => {
    /* 验证源IP */
    let sipVal = rules.IP(state.formData.sip);
    if (state.formData.sip && !sipVal.result) {
      ElMessage.info("请输入合法的源IP地址");
      return false;
    }
    /* 验证目的IP */
    let dipVal = rules.IP(state.formData.dip);
    if (state.formData.dip && !dipVal.result) {
      ElMessage.info("请输入合法的目的IP地址");
      return false;
    }

    if (valid && validFun()) {
      loading();
      let API = state.formData.id ? editWhiteList : addWhiteList;
      API(nodeId.value, state.formData)
        .then((res) => {
          ElMessage.success("操作成功");
          close();
        })
        .catch(() => {
          close(false);
        });
    }
  });
};

/* 自定义校验 */
const validFun = () => {
  let sum = 0;
  for (let val in state.formData) {
    if ((state.formData[val] === "" || state.formData[val] === undefined) && val !== "id") {
      sum += 1;
    }
  }
  let isTrue = sum < formList.value.length;
  if (!isTrue) ElMessage.info("基本信息必须有一个不为空");
  return isTrue;
};

/*
 * 打开弹窗
 * type 是否为新增
 * nId nodeId
 * data 编辑回填数据
 * */
function open(type, nId, data) {
  isAdd.value = type;
  nodeId.value = nId;
  if (!type) {
    for (let a in state.formData) {
      state.formData[a] = data[a];
    }
  }
  dialogRef.value.open();
}

/* 关闭弹窗 - 回调 */
const closeDia = () => {
  state.formData = {
    id: "",
    rule: "",
    sip: "",
    dip: "",
    dport: undefined,
    domain: "",
    realSip: "",
    url: "",
  };
  emits("close");
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.infoP {
  padding-left: 20px;
  font-size: 12px;
  color: #515a6e;
  &::before {
    content: "*";
    color: var(--el-color-danger);
  }
}
</style>
