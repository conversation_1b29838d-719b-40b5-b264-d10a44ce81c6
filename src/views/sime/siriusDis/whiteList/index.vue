<template>
  <!-- 天狼星白名单 -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="$store.state.siem.activeName">
      <el-tab-pane v-for="item in $store.state.siem.tabsList" :key="item.id" :label="item.name" :name="item.id">
        <WhiteListCom :nodeId="item.id" v-if="item.id === $store.state.siem.activeName" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script>
export default {
  name: "Sirius-whiteList",
};
</script>

<script setup>
import { onMounted, ref } from "vue";
import WhiteListCom from "./whiteListCom.vue";
import { useStore } from "vuex";

/* 默认数据项 */
const store = useStore();

/* 获取节点 数据 */
const getNodeTypeList = () => {
  store.commit("setTabsList");
};
onMounted(() => {
  getNodeTypeList();
});
</script>

<style scoped></style>
