<template>
  <!-- 天狼星白名单 -->
  <!-- 查询体 -->
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button :disabled="delList.length <= 0" v-hasPermi="'nodemanager:sirius:deleteWhiteList'" type="button" @click="batchDelete()">
      <el-icon :size="12">
        <delete />
      </el-icon>
      批量删除
    </el-button>

    <el-button size="small" class="search-button" v-hasPermi="'nodemanager:sirius:addWhiteList'" @click="handAdd">
      <el-icon :size="12">
        <plus />
      </el-icon>
      新增白名单
    </el-button>
  </common-search>

  <!-- 数据 -->
  <xel-table
    ref="tableRef"
    :checkbox="hasPermi('nodemanager:soscan:deleteTask')"
    row-key="id"
    @selection-change="handleSelectionChange1"
    :columns="columns"
    :pagination="false"
    :data="tableData"
  />

  <!-- 分页 -->
  <xel-pagination ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />

  <!-- 新增与编辑 -->
  <AddSiriusDia ref="AddSiriusDiaRef" @close="search" />
</template>

<script setup>
import { computed, reactive, ref, watch } from "vue";
import AddSiriusDia from "./components/addSiriusDia.vue";
import { getWhiteLists, deleteWhiteList } from "@/api/sime/siriusDis/index";
import { ElMessage, ElMessageBox } from "element-plus";
import hasPermi from "@/utils/hasPermi";

/* 默认传参 */
let props = defineProps({
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
});

/* 基础数据 */
let tableRef = ref();

/* 新增规则 */
const handAdd = () => {
  AddSiriusDiaRef.value.open(true, props.nodeId);
};

/* 查询相关 */
let searchState = reactive({
  data: {
    rule: "",
    sip: "",
    dip: "",
    dport: "",
    domain: "",
    realSip: "",
    url: "",
    currentPage: 1,
    pageSize: 10,
  },
  formList: [
    {
      formType: "input",
      prop: "rule",
      label: "告警名",
    },
    {
      formType: "input",
      prop: "sip",
      label: "源IP",
    },
    {
      formType: "input",
      prop: "dip",
      label: "目的IP",
    },
    {
      formType: "input",
      prop: "dport",
      label: "目的端口",
    },
    {
      formType: "input",
      prop: "domain",
      label: "HOST",
    },
    {
      formType: "input",
      prop: "realSip",
      label: "XFF",
    },
    {
      formType: "input",
      prop: "url",
      label: "URL",
    },
  ],
});

/* 查询 */
let tableData = ref([]);
let total = ref(0);

/* 查询 */
const search = () => {
  getWhiteLists(props.nodeId, searchState.data).then((res) => {
    if (res && res.data && res.data.list) {
      total.value = res.data.list.total;
      tableData.value = res.data.list.list;
    } else {
      tableData.value = [];
      total.value = 0;
    }
  });
};
/* 重置 */
const paginationRef = ref();
const reset = () => {
  searchState.data = {
    rule: "",
    sip: "",
    dip: "",
    dport: "",
    domain: "",
    realSip: "",
    url: "",
    currentPage: 1,
    pageSize: 10,
  };
  paginationRef.value.resetPageNum();
  tableRef.value.table.clearSelection();
  search();
};

/* 批量删除 相关 */
let delList = ref([]);

/* 删除方法 */
const deleteData = (ids) => {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteWhiteList(props.nodeId, { ids }).then((res) => {
      ElMessage({ message: "删除成功", type: "success" });
      reset();
    });
  });
};

const handleSelectionChange1 = (val) => {
  delList.value = val.map((item) => {
    return item.id;
  });
};
const batchDelete = () => {
  deleteData(delList.value);
};

const AddSiriusDiaRef = ref();
/* 表格项 */
const columns = [
  {
    prop: "rule",
    label: "告警名",
  },
  {
    prop: "sip",
    label: "源IP",
  },
  {
    prop: "dip",
    label: "目的IP",
  },
  {
    prop: "dport",
    label: "目的端口",
  },
  {
    prop: "domain",
    label: "HOST",
  },
  {
    prop: "realSip",
    label: "XFF",
  },
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "addTime",
    label: "更新时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        hasPermi: "nodemanager:sirius:editWhiteList",
        onClick(scope) {
          AddSiriusDiaRef.value.open(false, props.nodeId, scope.row);
        },
      },
      {
        icon: "delete",
        hasPermi: "nodemanager:sirius:deleteWhiteList",
        title: "删除",
        onClick(scope) {
          deleteData([scope.row.id]);
        },
      },
    ],
  },
];

/* 分页事件 */
const changePagination = (val) => {
  searchState.data.currentPage = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

watch(
  () => props.nodeId,
  (val) => {
    if (val) {
      /*search();*/
    }
  },
  { immediate: true }
);
</script>

<style scoped></style>
