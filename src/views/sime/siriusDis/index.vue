<template>
  <!-- 天狼星 - 分析配置  -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tabsList" :key="item.id" :label="item.name" :name="item.id">
        <AnalysisConfig :nodeId="item.id" />
      </el-tab-pane>

      <!--      <el-tab-pane label="天狼星节点2 - 系统维护" name="tlx2">
        <SystemMain />
      </el-tab-pane>

      <el-tab-pane label="天狼星节点3 - 白名单" name="tlx3">
        <WhiteList />
      </el-tab-pane>-->
    </el-tabs>
  </el-card>
</template>

<script setup>
import { onMounted, ref, watch } from "vue";
import { getNodeList } from "@/api/sime/nodemanager/generalView";

import AnalysisConfig from "./analysisConfig/index.vue";
import SystemMain from "./systemMain/index.vue";
import WhiteList from "./whiteList/index.vue";
import { useStore } from "vuex";

/* 默认数据项 */
const store = useStore();

let activeName = ref("");
let tabsList = ref([]);

/* 获取节点 数据 */
const getNodeTypeList = () => {
  getNodeList("sirius").then((res) => {
    activeName.value = res.data.rows[0].id;
    tabsList.value = res.data.rows;
  });
};
onMounted(() => {
  getNodeTypeList();
});
</script>

<style scoped></style>
