<template>
  <!-- 天狼星 - 节点详情  -->
  <el-card>
    <!--    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>-->
    <el-tabs v-model="activeName">
      <el-tab-pane label="分析配置" name="AnalysisConfig" v-if="AnalysisConfigShow">
        <AnalysisConfig v-if="nodeId" :nodeId="nodeId" />
      </el-tab-pane>

      <el-tab-pane label="白名单" name="WhiteList" v-if="WhiteListShow">
        <WhiteListCom v-if="nodeId && activeName === 'WhiteList'" :nodeId="nodeId" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup>
import { onMounted, ref } from "vue";
import AnalysisConfig from "./analysisConfig/index.vue";
import WhiteListCom from "./whiteList/whiteListCom.vue";
import { useRoute } from "vue-router";
import hasPermi from "@/utils/hasPermi";

const route = useRoute();
/* 默认数据项 */
let activeName = ref("AnalysisConfig");
let AnalysisConfigShow = ref(false);
let WhiteListShow = ref(false);
let nodeId = ref("");

/* 获取节点 数据 */
const getNodeId = () => {
  nodeId.value = route.params.nodeId;
  hasPermi("nodemanager:sirius:getAnalysisConfig")
    ? (activeName.value = "AnalysisConfig")
    : hasPermi("nodemanager:sirius:getWhiteLists")
    ? (activeName.value = "WhiteList")
    : "";
  AnalysisConfigShow.value = hasPermi("nodemanager:sirius:getAnalysisConfig");
  WhiteListShow.value = hasPermi("nodemanager:sirius:getWhiteLists");
};
onMounted(() => {
  getNodeId();
});
</script>

<style scoped></style>
