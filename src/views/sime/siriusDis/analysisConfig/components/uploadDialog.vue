<template>
  <!-- 上传规则 -->
  <div class="upload-file">
    <el-button icon="el-icon-upload2" @click="handleImport">
      {{ props.btnName }}
    </el-button>
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body @closed="closeDia">
      <slot></slot>

      <el-upload
        ref="uploadRef"
        :limit="props.limit"
        :accept="props.accept"
        :headers="upload.headers"
        :action="'/outpost-api' + upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-exceed="handleExceedFile"
        :auto-upload="false"
        :on-change="changeFileList"
        :file-list="fileListSelf"
        :data="data"
        :on-remove="removeFile"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color: red">提示：仅允许导入{{ props.accept }}格式文件！</div>
      </el-upload>
      <div class="dialog-footer">
        <el-button @click="closeDia">取 消</el-button>
        <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
/*
 * 上传规则 组件 （整合上传组件，业务逻辑有不同）
 * 该组件用于，天狼星 上传规则
 * */
import { ref, reactive } from "vue";
import { getToken } from "@/utils";
import { ElMessageBox, ElMessage } from "element-plus";
import { saveFile, uploadRules } from "@/api/sime/siriusDis/index";

let props = defineProps({
  /*窗口标题名称*/
  diaName: {
    type: String,
    default: "导入数据",
  },
  /*按钮名称*/
  btnName: {
    type: String,
    default: "导入",
  },
  /*文件个数*/
  limit: {
    type: Number,
    default: 1,
  },
  /*文件大小限制： 单位 M */
  fileSize: {
    type: Number,
    default: 20,
  },
  /*文件格式*/
  accept: {
    type: String,
    default: ".zip",
  },
  /*上传模板地址*/
  importUrl: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
});
let upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { Authorization: getToken() },
  // 上传的地址
  /*url: process.env.VUE_APP_BASE_API + "/system/eventTemplate/importEventTemplate.do"*/
  // url: process.env.VUE_APP_BASE_API + this.importUrl,
  url: props.importUrl,
});
const emits = defineEmits(["updateData", "clickBtn"]);

/** 导入按钮操作 */
function handleImport() {
  upload.title = props.diaName;
  upload.open = true;
  emits("clickBtn");
}

// 文件上传中处理
function handleFileUploadProgress() {
  upload.isUploading = true;
}
// 文件上传成功处理
let uploadRef = ref();
function handleFileSuccess(response) {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value.clearFiles();
  fileListSelf.value = [];
  let msg = "";
  let title = "";

  if (response.code == 200) {
    if ("data" in response) {
      msg = response.data.msg + "<br/>" + response.data.message;
    } else {
      msg = response.msg;
    }
    if (response.data && !response.data.msg && !response.data.message) {
      msg = response.msg;
    }
    title = "导入结果";
  } else {
    if (typeof response.data == "object" && "message" in response.data) {
      msg = response.data.message;
    } else {
      msg = response.data || response.msg;
    }
    title = "导入失败";
  }

  ElMessageBox.alert("<div style='max-height:400px;overflow:auto;'>" + msg.split("您好").join("<br/>您好") + "</div>", title, {
    confirmButtonText: "关闭",
    dangerouslyUseHTMLString: true,
  });

  // this.getList();
  emits("updateData", response.data);
}
/* 提交上传文件 */
function submitFileForm() {
  if (fileListSelf.value.length === 0) {
    ElMessage.warning("请选择上传文件");
    return;
  }
  upload.isUploading = true;
  /*  先上传，获取文件名称 saveFile, 执行规则文件 uploadRules */
  let formData = new FormData();
  formData.append("nodeType", "sirius");
  formData.append("methodName", "uploadRules");
  /* 当前限制 1个文件，防止后续支持多文件上传 */
  fileListSelf.value.forEach((item) => {
    formData.append("files", item.raw);
  });
  saveFile(formData)
    .then((res) => {
      uploadRules(props.nodeId, { fileNames: res.data })
        .then((res1) => {
          ElMessageBox.alert("<div style='max-height:400px;overflow:auto;'>" + res1.data.result + "</div>", "导入结果", {
            confirmButtonText: "关闭",
            dangerouslyUseHTMLString: true,
          });
          emits("updateData");
          closeDia();
        })
        .catch(() => {
          upload.isUploading = false;
        });
    })
    .catch(() => {
      upload.isUploading = false;
    });
}
function closeDia() {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value && uploadRef.value.clearFiles();
  fileListSelf.value = [];
}
/*文件数量 限制文件*/
function handleExceedFile() {
  ElMessage(`当前限制选择 ${props.limit} 个文件，请移除当前文件，重新上传!`);
}

let fileListSelf = ref([]);
function changeFileList(file, fileList) {
  /*判断文件大小*/
  let size = file.size / 1024 / 1024 < props.fileSize;
  /*判断文件类型*/
  let filemsg = "." + file.name.substring(file.name.lastIndexOf(".") + 1);
  let isFilemsg = props.accept.indexOf(filemsg) !== -1;
  if (isFilemsg) {
    if (size) {
      fileListSelf.value.push(file);
    } else {
      fileList.splice(-1, 1);
      ElMessage(`文件（${file.name}）过大，请重新上传！（限制大小：${props.fileSize} M）`);
    }
  } else {
    fileList.splice(-1, 1);
    ElMessage(`文件（${file.name}）格式不正确，允许上传 ${props.accept} 格式文件！`);
  }
  emits("fileList", fileListSelf.value);

  /*fileListSelf.value = fileList;*/
}

/* 移除文件 */
function removeFile(file, fileList) {
  fileListSelf.value = fileList;
}
</script>

<style scoped>
.upload-file {
  display: inline-block;
  margin-left: 10px;
}
.dialog-footer {
  text-align: center;
  margin-top: 20px;
}
</style>
