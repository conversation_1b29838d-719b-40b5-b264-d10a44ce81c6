<template>
  <!--告警外发 -->
  <el-form :model="formData" ref="ruleFormRef" label-width="140px" size="mini">
    <el-row :gutter="4">
      <el-col :span="3" class="text-center">
        <el-switch v-model="formData.enable" :active-value="true" :inactive-value="false" />
        <span class="swSpan">是否启用</span>
      </el-col>

      <el-col :span="5">
        <xel-form-item label="目标地址" v-model="formData.server" formType="input" type="textarea" rows="1" placeholder="目标地址" :required="true" />
      </el-col>

      <el-col :span="5">
        <xel-form-item
          label="目标端口"
          v-model="formData.port"
          formType="number"
          controls-position="right"
          placeholder="目标端口"
          :min="0"
          :max="65535"
          :precision="0"
          :required="true"
        />
      </el-col>

      <el-col :span="6" class="text-center">
        <el-switch v-model="formData.enable_send_acc_log" :active-value="true" :inactive-value="false" />
        <span class="swSpan"> 是否发送网络访问日志 </span>
      </el-col>

      <el-col :span="5" class="text-right">
        <el-button type="primary" v-hasPermi="'nodemanager:sirius:updateAnalysisConfig'" @click="handApply" :loading="saveLoading"> 应用 </el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive, ref, toRefs } from "vue";
import { ElMessage } from "element-plus";
let emit = defineEmits(["handApply"]);

/* 基础数据 */
let data = reactive({
  formData: {
    enable: true,
    server: "",
    port: undefined,
    enable_send_acc_log: false,
  },
  saveLoading: false,
});
let { formData, saveLoading } = toRefs(data);

/* 应用按钮 */
const ruleFormRef = ref();
const handApply = () => {
  if (!formData.value.server) {
    ElMessage.info("目标地址不能为空");
    return false;
  }
  if (!formData.value.port) {
    ElMessage.info("目标端口不能为空");
    return false;
  }
  data.saveLoading = true;
  emit("handApply", formData.value);
};

defineExpose({
  saveLoading() {
    setTimeout(() => {
      data.saveLoading = false;
    }, 1000);
  },
  setFormData(val) {
    data.formData = val;
    data.formData.port = parseInt(val.port);
    /* 类型转换 */
    val.enable === "false" ? (data.formData.enable = false) : (data.formData.enable = true);
    val.enable_send_acc_log === "false" ? (data.formData.enable_send_acc_log = false) : (data.formData.enable_send_acc_log = true);
  },
});
</script>

<style scoped>
.swSpan {
  font-weight: bold;
  color: #848484;
  position: relative;
  top: 1px;
  left: 5px;
}
</style>
