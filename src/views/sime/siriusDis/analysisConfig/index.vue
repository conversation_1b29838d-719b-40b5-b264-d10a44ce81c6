<template>
  <!-- 天狼星节点  -->
  <div>
    <!-- 统计信息 -->
    <div class="mb20">
      <CardDIy v-for="item in cardData" :key="item.title" :itemData="item" />
    </div>

    <!-- 规则管理 -->
    <div class="mb20">
      <p class="title-bottom-line">规则管理</p>
      <UploadDialog class="upload-button" btnName="上传规则" size="70px" :nodeId="nodeId" />
    </div>

    <!-- 告警外发 -->
    <div class="mb20">
      <p class="title-bottom-line">告警外发</p>
      <AlarmSending ref="AlarmSendingRef" @handApply="handApply" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { getRuleCount, setAlertRelay, getAlertRelay } from "@/api/sime/siriusDis/index";
import CardDIy from "../components/cardDIy.vue";
import UploadDialog from "./components/uploadDialog.vue";
import AlarmSending from "./components/alarmSending.vue";
import { ElMessage } from "element-plus";

/* 默认传参 */
let props = defineProps({
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
});

/* 头部统计信息 */
const cardData = ref([
  {
    icon: "icon-nav_cxfx",
    title: "http引擎的规则",
    number: 0,
  },
  {
    icon: "icon-ceshi",
    title: "流引擎的规则",
    number: 0,
  },
]);

/* 获取引擎规则数量 */
const getListFun = () => {
  getRuleCount(props.nodeId, {}).then((res) => {
    cardData.value[0].number = res.data.httpEngine;
    cardData.value[1].number = res.data.streamEngine;
  });
};

/* 告警外发回填 */
const getAlertRelayFun = () => {
  getAlertRelay(props.nodeId, {}).then((res) => {
    AlarmSendingRef.value.setFormData(res.data);
  });
};

watch(
  () => props.nodeId,
  (val) => {
    if (val) {
      getListFun();
      getAlertRelayFun();
    }
  },
  { immediate: true }
);

/* 告警外发配置 */
const AlarmSendingRef = ref();
const handApply = (data) => {
  setAlertRelay(props.nodeId, data).then((res) => {
    ElMessage.success("操作成功");
    AlarmSendingRef.value.saveLoading();
  });
};
</script>

<style scoped lang="scss">
.mb20 {
  margin-bottom: 20px;
}
</style>
