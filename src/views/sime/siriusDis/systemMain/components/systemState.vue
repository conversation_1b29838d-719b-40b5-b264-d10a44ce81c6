<template>
  <!-- 系统状态  -->
  <CardTitDiy title="系统状态">
    <Progress />
    <Progress />
    <Progress />
    <Progress />
  </CardTitDiy>
</template>

<script setup>
import CardTitDiy from "../../components/cardTitDiy.vue";
import Progress from "./progress.vue";

defineProps({
  title: {
    type: String,
    default() {
      return "未知名称";
    },
  },
});
</script>

<style scoped lang="scss"></style>
