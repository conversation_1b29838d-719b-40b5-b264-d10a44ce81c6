<template>
  <!-- 网卡状态  -->
  <CardTitDiy title="网卡状态">
    <DiyTable :tableSys="tableSys" :tableData="tableData" />
  </CardTitDiy>
</template>

<script setup>
import CardTitDiy from "../../components/cardTitDiy.vue";
import DiyTable from "./diyTable.vue";
import { ref } from "vue";
defineProps({
  title: {
    type: String,
    default() {
      return "未知名称";
    },
  },
});

/* 表格配置 */
const tableSys = ref({
  tabTit1: "网卡名",
  tabTit2: "速率",
  tabKey1: "name",
  tabKey2: "num",
});

/* 表格数据 */
const tableData = ref([
  { name: "HTTP双向检测引擎", num: "3.3Kbits/s" },
  { name: "流检测引擎", num: "0Kbits/s" },
  { name: "kafka", num: "0Kbits/s" },
  { name: "redis", num: "0Kbits/s" },
  { name: "mysql", num: "0Kbits/s" },
]);
</script>

<style scoped></style>
