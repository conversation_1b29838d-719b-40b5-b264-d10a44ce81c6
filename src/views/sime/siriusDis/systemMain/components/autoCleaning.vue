<template>
  <!--自动清理 -->
  <el-form :model="formData" ref="ruleFormRef" label-width="140px" size="mini">
    <el-row :gutter="4">
      <el-col :span="5" class="formItem">
        <xel-form-item label="CPU阖值" v-model="formData.cpu" formType="number" placeholder="CPU阖值" :min="0" :max="100" :precision="0" />
        <span class="bfbSpan">%</span>
      </el-col>

      <el-col :span="5" class="formItem">
        <xel-form-item label="内存阖值" v-model="formData.inter" formType="number" placeholder="内存阖值" :min="0" :max="100" :precision="0" />
        <span class="bfbSpan">%</span>
      </el-col>

      <el-col :span="5" class="formItem">
        <xel-form-item label="磁盘阖值" v-model="formData.disk" formType="number" placeholder="磁盘阖值" :min="0" :max="100" :precision="0" />
        <span class="bfbSpan">%</span>
      </el-col>

      <el-col :span="9" class="text-right">
        <el-button type="primary" @click="handApply"> 应用 </el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive, toRefs } from "vue";

/* 基础数据 */
let data = reactive({
  formData: {
    cpu: 0,
    inter: 0,
    disk: 0,
  },
  saveLoading: false,
});
let { formData, saveLoading } = toRefs(data);

/* 应用按钮 */
const handApply = () => {
  console.log(formData.value);
};
</script>

<style scoped lang="scss">
.formItem {
  position: relative;
  .bfbSpan {
    position: absolute;
    right: -15px;
    top: 7px;
  }
}
</style>
