<template>
  <!-- 天狼星 - 状态 -->
  <div class="systemMainDiv mb20">
    <div>
      <SystemState />
    </div>
    <div>
      <NetworkState />
    </div>
    <div>
      <SubSystemState />
    </div>
  </div>
</template>

<script setup>
import SystemState from "./systemState.vue";
import NetworkState from "./networkState.vue";
import SubSystemState from "./subSystemState.vue";
</script>

<style scoped lang="scss">
.systemMainDiv {
  display: flex;
  justify-content: space-between;
  > div {
    width: 32%;
  }
}
</style>
