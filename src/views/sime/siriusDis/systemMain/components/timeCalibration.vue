<template>
  <!--时间校准 -->
  <el-form :model="formData" ref="ruleFormRef" label-width="140px" size="mini">
    <el-row :gutter="4">
      <el-col :span="7">
        <xel-form-item label="NTP服务器" v-model="formData.ntpServer" formType="input" />
      </el-col>

      <el-col :span="7">
        <xel-form-item
          label="手工设置"
          formType="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model:start="formData.startTime"
          v-model:end="formData.endTime"
        />
      </el-col>

      <el-col :span="10" class="text-right">
        <el-button type="primary" @click="handApply"> 应用 </el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { reactive, toRefs } from "vue";

/* 基础数据 */
let data = reactive({
  formData: {
    ntpServer: "",
    startTime: "",
    endTime: "",
  },
  saveLoading: false,
});
let { formData, saveLoading } = toRefs(data);

/* 应用按钮 */
const handApply = () => {
  console.log(formData.value);
};
</script>

<style scoped lang="scss"></style>
