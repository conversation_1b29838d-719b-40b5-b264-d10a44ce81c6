<template>
  <!-- 子系统状态 -->
  <CardTitDiy title="子系统状态">
    <DiyTable :tableSys="tableSys" :tableData="tableData" :statusCar="true" />
  </CardTitDiy>
</template>

<script setup>
import CardTitDiy from "../../components/cardTitDiy.vue";
import DiyTable from "./diyTable.vue";
import { ref } from "vue";
defineProps({
  title: {
    type: String,
    default() {
      return "未知名称";
    },
  },
});

/* 表格配置 */
const tableSys = ref({
  tabTit1: "系统名",
  tabTit2: "状态",
  tabKey1: "name",
  tabKey2: "status",
});

/* 表格数据 */
const tableData = ref([
  { name: "HTTP双向检测引擎", status: "宕机" },
  { name: "流检测引擎", status: "正在运行" },
  { name: "kafka", status: "正在运行" },
  { name: "redis", status: "正在运行" },
  { name: "mysql", status: "正在运行" },
]);
</script>

<style scoped></style>
