<template>
  <div class="diyTableDiv">
    <div class="diyTableItem">
      <div>{{ tableSys.tabTit1 }}</div>
      <div>{{ tableSys.tabTit2 }}</div>
    </div>

    <div class="diyTableItem" v-for="item in tableData" :key="item">
      <div>{{ item[tableSys.tabKey1] }}</div>
      <div v-if="statusCar">
        <el-tag effect="light" round type="success">
          {{ item[tableSys.tabKey2] }}
        </el-tag>
      </div>
      <div v-else>{{ item[tableSys.tabKey2] }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  /* 配置数据 */
  tableSys: {
    type: Object,
    default() {
      return {
        tabTit1: "列名1",
        tabTit2: "列名2",
        tabKey1: "name",
        tabKey2: "ws",
      };
    },
  },

  /* 状态显示 */
  statusCar: {
    type: Boolean,
    default() {
      return false;
    },
  },

  /* 表格数据 */
  tableData: {
    type: Array,
    default() {
      return [
        { name: "测试1", ws: 20 },
        { name: "测试1", ws: 20 },
        { name: "测试1", ws: 20 },
        { name: "测试1", ws: 20 },
        { name: "测试1", ws: 20 },
      ];
    },
  },
});
</script>

<style scoped lang="scss">
.diyTableDiv {
  margin-top: 15px;
  .diyTableItem {
    display: flex;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border-bottom: 1px solid #edeff2;
    > div {
      flex: 1;
      position: relative;
    }
  }
  .diyTableItem:first-child {
    background: #fff;
    border-bottom: 0;
    font-weight: bold;
  }
  .diyTableItem:last-child {
    border-bottom: 0;
  }
}
</style>
