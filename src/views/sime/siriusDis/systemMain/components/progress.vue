<template>
  <!-- 使用率 - 进度条 -->
  <div class="progressDiv">
    <div>
      {{ proData.title }}
    </div>
    <div>
      <el-progress :percentage="proData.percentage" :color="proColor" :stroke-width="strokeWidth" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  /* 进度条组件
   * proData 进度条数据
   * title - 标题
   * percentage - 数据
   * */
  proData: {
    type: Object,
    default() {
      return {
        title: "未知",
        percentage: 100,
      };
    },
  },

  /* 颜色 */
  proColor: {
    type: String,
    default() {
      return "#375BF4";
    },
  },

  /* 宽度 */
  strokeWidth: {
    type: Number,
    default() {
      return 10;
    },
  },
});
</script>

<style scoped lang="scss">
.progressDiv {
  margin: 38px 0;
  display: flex;
  justify-content: space-between;
  div:first-child {
    flex: 1;
    font-size: 12px;
    color: #5e656f;
  }
  div:last-child {
    flex: 3;
  }
}
</style>
