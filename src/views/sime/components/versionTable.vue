<!-- 版本管理 -->
<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>

    <xel-table ref="tableRef" :load-data="loadData" :columns="columns.concat(actionBtn)" :defaultParams="defaultParams"> </xel-table>
    <el-button class="but-right" @click="clickGoBack">返回</el-button>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, onActivated, h } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import backVersion from "./js/backVersion";
import { restartReceiver } from "@/api/sime/config/logAccept.js";
const router = useRouter();

const route = useRoute();

onActivated(() => {
  tableRef.value && tableRef.value.reload();
});
let props = defineProps({
  loadData: {
    type: Function,
    required: true,
  },
  columns: {
    type: Array,
    default: () => {
      return [];
    },
  },
  backApi: {
    type: Function,
    required: true,
  },
});
let tableRef = ref();
let defaultParams = { id: route.params.id };
let actionBtn = ref([
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "view",
        title: "详情",
        hasPermi:
          route.name == "ParseVersion"
            ? "config:parseRuleVersion:view"
            : route.name == "FilterVersion"
            ? "queryFilter:version:view"
            : "config:analysisRuleVersion:view",
        onClick(scope) {
          router.push({
            name: route.name + "Detail",
            params: { id: route.name == "ParseVersion" ? scope.row.versionId : scope.row.id },
            query: {
              indexId: scope.row.indexId,
              pId: route.params.id,
            },
          });
        },
      },
      {
        icon: "back",
        title: "回滚",
        hasPermi:
          route.name == "ParseVersion"
            ? "config:parseRuleVersion:rollback"
            : route.name == "FilterVersion"
            ? "config:queryFilter:update"
            : "config:analysisRule:update",
        onClick({ row }) {
          backVersion(props.backApi, route.params.id, route.name == "ParseVersion" ? row.versionId : row.id, () => {
            tableRef.value.reload();
            if (route.name == "ParseVersion") {
              isStart(row.versionId);
            }
          });
        },
      },
    ],
  },
]);
function isStart(id) {
  ElMessageBox({
    title: "警告",
    message: h("p", null, [h("p", null, "是否立即启用"), h("p", { style: "color: #E6A23C" }, "注意：选择否，则需要手动重启监听器")]),
    showCancelButton: true,
    confirmButtonText: "是",
    cancelButtonText: "否",
    showClose: false,
    closeOnClickModal: false,
  })
    .then(() => {
      let resetParams = {
        parseRuleList: [{ ruleId: id }],
      };
      restartReceiver(resetParams)
        .then((res) => {})
        .finally(() => {
          tableRef.value.reload();
        });
    })
    .catch(() => {});
}
function clickGoBack() {
  router.go(-1);
}
</script>

<style lang="scss" scoped></style>
