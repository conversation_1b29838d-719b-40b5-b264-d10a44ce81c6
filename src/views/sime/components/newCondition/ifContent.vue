<template>
  <div :key="updateKey" class="ifConDiv">
    <conditionResult v-if="!editable && ifList[0]" :list="getResultData(ifList)"></conditionResult>
    <!-- 增加 - 关闭按钮 -->
    <el-icon class="close" size="14" v-if="isShowClose" @click="closeFun"><close /></el-icon>
    <div v-for="(item, ifIndex) in ifList" :key="item.itemId" class="if-conent">
      <!-- <div v-if="item.judgmentBranch" v-show="false" class="if-title">
        {{ item.judgmentBranch }}
      </div> -->
      <div class="if-body-list">
        <div class="if-body">
          <div class="formula-item-if" @click.stop="item.conditionType == 'compare' ? clearChoseId(ifIndex, item.itemId) : ''">
            <FormulaItem
              :list="item"
              :level="1"
              :index="ifIndex"
              :condition-type="item.conditionType"
              :chose-id="choseId"
              :z-index="999"
              :editable="editable"
              :type-list="typeList"
              @click.stop
              @change-list="hasChangeMore"
              @del-list="delList"
              @is-chose="getChoseId"
              @validateDataFormat="validateDataFormat"
              :haveFilter="haveFilter"
              @deleteItemFun="deleteItemFun"
            ></FormulaItem>
          </div>
          <div v-if="editable && item.conditionType && item.conditionType == 'compare'" class="add-formula" @click.stop>
            <div v-if="hasSymbol" class="add-btn-filter-box" :class="!haveFilter ? 'noHaveFilter' : ''">
              <span
                class="add-btn"
                :style="{
                  top: !item.judgmentBranchChildren || item.judgmentBranchChildren.length <= 0 ? '14px' : '',
                }"
                @click="addFormulaItem(ifIndex)"
              >
                <el-icon><Plus /></el-icon>添加条件</span
              >
              <span
                v-if="haveFilter"
                class="add-btn add-filter"
                :style="{
                  top: !item.judgmentBranchChildren || item.judgmentBranchChildren.length <= 0 ? '14px' : '',
                }"
                @click="addFormulaItem(ifIndex, undefined, true)"
              >
                <el-icon><Filter /></el-icon>添加过滤器</span
              >
            </div>
            <el-popover v-else placement="top" :width="200" trigger="click">
              <template #reference>
                <span
                  :style="{
                    top: !item.judgmentBranchChildren || item.judgmentBranchChildren.length <= 0 ? '14px' : '12px',
                  }"
                  class="add-btn add-symbol"
                >
                  <el-icon><Plus /></el-icon>添加条件符号</span
                >
              </template>
              <div>
                <span class="node-mode" style="background: rgba(238, 245, 255, 1)" @click="addFormulaItem(ifIndex, 'and')">And</span>
                <span style="background: rgba(255, 216, 168, 1)" class="node-mode" @click="addFormulaItem(ifIndex, 'or')">Or</span>
                <span style="background: #f36c6e" class="node-mode" @click="addFormulaItem(ifIndex, 'not')">Not</span>
              </div>
            </el-popover>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { VueDraggableNext } from "vue-draggable-next";

import { setItemId } from "./commin";
import FormulaItem from "./formulaItem.vue";
import conditionResult from "@/views/sime/components/conditionResult.vue"; //封装后的条件语句

import { ElMessage } from "element-plus";
import useCondition from "./conditionEdit";
import { nextTick, watch } from "vue";
import { debounce } from "lodash";

interface IStringObject {
  [index: string]: any;
}

interface Props {
  list: any;
  index?: number;
  level?: number;
  contentChoseId?: string;
  editable?: boolean;
  typeList?: any;
  haveFilter?: boolean;
  isShowClose?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  conditionType: "",
  contentChoseId: "",
  editable: true,
  level: 1,
  index: 0,
  typeList: () => [],
  haveFilter: true,
  isShowClose: false,
});
const choseId = ref();
watch(
  () => props.contentChoseId,
  (val) => {
    choseId.value = val;
  },
  {
    deep: true,
    immediate: true,
  }
);
const choseNode = ref("");
const choseIndex = ref(-1);
const choseIdLevel = ref(0);
const ifList = ref(props.list);

watch(
  () => props.list,
  (val) => {
    if (!props.editable) {
      ifList.value = val;
    }
  }
);

// bugfix: 只有过滤器条件时，结果不显示的bug修复
onMounted(() => {
  if (props.level == 1) {
    emits("changeList", props.list, 0);
  }
});

//拖动后，单条and或者or,去掉操作符
const validateDataFormat = debounce(() => {
  handlerDragData(ifList.value);
  emits("changeList", ifList.value, props.index);
});

/* 删除项后，传递该项 */
const deleteItemFun = (data) => {
  emits("deleteItemFun", { data });
};

/* closeFun 关闭按钮 */
const closeFun = () => {
  emits("closeFun");
};

function handlerDragData(arr) {
  for (let [index, item] of arr.entries()) {
    if (["or", "and"].includes(item.nodeType) && item.judgmentBranch != "IF" && item.children.length == 1) {
      arr[index] = item.children[0];
      continue;
    } else {
      //拖拽后，判断not是否没有子节点，如果为空，则删除该节点
      if (item.nodeType == "not") {
        if (arr[index].children.length == 0) {
          arr.splice(index, 1);
        }
      }
    }
    if (item.children) {
      handlerDragData(item.children);
    }
  }
}

const canAdd = ref(false);
// 选中的内容
const emits = defineEmits(["changeList", "isChose", "changeIfId"]);
const hasSymbol = ref(true);
function getChoseId(itemId: string, node: string, level: number) {
  choseId.value = itemId;
  choseNode.value = node;
  choseIdLevel.value = level;
  if (node == "not" || node == "and" || node == "or") {
    hasSymbol.value = true;
  } else {
    hasSymbol.value = false;
  }
  emits("isChose", choseId.value);
}

function hasChangeMore(data: any, index: number) {
  if (props.level == 1) {
    judgeCanAddCondition(ifList.value, choseIfData.value.ifId);
    changeDisabled();
  }

  ifList.value[index] = data;
  emits("changeList", ifList.value, props.index);
}

function delList(index) {
  ifList.value.splice(index, 1);

  emits("changeList", ifList.value, props.index);
}

// 添加条件
async function addFormulaItem(index: number, nodeType?: string, isFilter?: boolean) {
  choseIndex.value = index;
  if (!choseId.value) {
    ElMessage.warning("请选择要添加的节点！");
    return false;
  }

  let oldItem = JSON.parse(JSON.stringify([ifList.value[choseIndex.value]]));
  if (oldItem && oldItem.length > 0) {
    findItemId(oldItem, choseId.value, nodeType ? nodeType : "", isFilter).then((res) => {
      oldItem = res;
    });
  }
  ifList.value[choseIndex.value] = oldItem[0];

  if (isFilter === true) {
    oldItem[0].name = "filter";
  }
  updateKey.value++;
  emits("changeList", ifList.value, props.index);

  nextTick(() => {
    if (choseNode.value == "isChildren") {
      choseId.value = "";
    }
  });
}
// 递归查询指定的itemId
//isFilter === true 添加过滤器
async function findItemId(newList: any, itemId: string, nodeType: string, isFilter?: boolean, parentData?: any) {
  for (let i = 0; i < newList.length; i++) {
    if (newList[i].itemId == itemId) {
      if (choseNode.value == "not" || choseNode.value == "and" || choseNode.value == "or") {
        newList[i].children.push({ itemId: setItemId(), name: isFilter === true ? "filter" : "" });

        break;
      } else if (choseNode.value == "isChildren") {
        const oldItem = JSON.parse(JSON.stringify(newList[i]));

        if (parentData.nodeType == "not" && nodeType == "not" && parentData.children.length == 1) {
          ElMessage.warning("请勿连续添加not条件符号");
          break;
        }
        newList[i] = {
          id: "",
          itemId: setItemId(),
          nodeType: nodeType,
          children: [],
          judgmentBranch: oldItem.judgmentBranch,
          conditionType: oldItem.conditionType,
          judgmentBranchChildren: oldItem.judgmentBranchChildren,
        };

        newList[i].children.push(oldItem);

        //可单独添加一个not条件
        if (!(nodeType == "not" && newList[i].children.length == 1)) {
          newList[i].children.push({ itemId: setItemId() });
        }

        break;
      }
    } else if (newList[i].children && newList[i].children.length > 0) {
      findItemId(newList[i].children, itemId, nodeType, isFilter, newList[i]).then((res) => {
        newList[i].children = res;
      });
    }
  }
  return Promise.resolve(newList);
}

// // 清除选中的itemId
function clearChoseId(index: number, itemId: string) {
  choseId.value = "parent" + itemId + index;
  choseIndex.value = index;
  hasSymbol.value = false;
  emits("isChose", choseId.value, "");
}
const choseIfData = ref<IStringObject>({});

function changeDisabled() {
  if (choseIfData.value.ifId) {
    if (choseIfData.value.level == 1) {
      isCanAdd();
    } else {
      const oldId = JSON.parse(JSON.stringify(choseIfData.value.ifId));
      const ifId = getParentId(ifList.value, oldId);
      isCanAdd(ifList.value, ifId, choseIfData.value.ifId);
    }
  }
}
// 判断当前选择节点可添加内容
const childDiabled = ref(false);
const elseifDisabled = ref(false);
function isCanAdd(list?: any, ifId?: string, oldIfId?: string) {
  if (choseIfData.value.level == 1) {
    ifList.value.forEach((item: any) => {
      if (item.itemId == choseIfData.value.ifId) {
        if (item.conditionType == "proportion") {
          childDiabled.value = true;
        } else {
          childDiabled.value = false;
        }
        if (item.judgmentBranchChildren && item.judgmentBranchChildren.length > 0) {
          childDiabled.value = true;
        }
      }
    });
  } else {
    for (let i = 0; i < list.length; i++) {
      if (list[i].itemId == ifId) {
        list[i].judgmentBranchChildren.forEach((item: any) => {
          if (item.itemId == oldIfId) {
            if (item.conditionType == "proportion") {
              childDiabled.value = true;
            } else {
              childDiabled.value = false;
            }
            if (item.judgmentBranchChildren && item.judgmentBranchChildren.length > 0) {
              childDiabled.value = true;
            }
          }
        });
      } else {
        if (list[i].judgmentBranchChildren && list[i].judgmentBranchChildren.length > 0) {
          isCanAdd(list[i].judgmentBranchChildren, ifId, oldIfId);
        }
      }
    }
  }
}
//获取父级ID
function getParentId(list: any, id: string) {
  let parentId = "";
  for (let i = 0; i < list.length; i++) {
    if (list[i].judgmentBranchChildren && list[i].judgmentBranchChildren.length > 0) {
      const findItem = list[i].judgmentBranchChildren.find((item: any) => {
        return item.itemId == id;
      });
      if (findItem) {
        parentId = list[i].itemId;
      } else {
        parentId = getParentId(list[i].judgmentBranchChildren, id);
      }
    }
    if (parentId) {
      break;
    }
  }
  return parentId;
}
// 添加内容
const updateKey = ref(0);

// 判断当前if 是否能添加条件
function judgeCanAddCondition(list: any, ifId: string) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].itemId == ifId) {
      if ((list[i].judgmentBranchChildren && list[i].judgmentBranchChildren.length > 0) || (list[i].children && list[i].children.length > 1)) {
        canAdd.value = true;
      } else {
        canAdd.value = false;
      }
      if (list[i].conditionType == "proportion") {
        canAdd.value = true;
      }
      break;
    } else {
      if (list[i].judgmentBranchChildren && list[i].judgmentBranchChildren.length > 0) {
        judgeCanAddCondition(list[i].judgmentBranchChildren, ifId);
      }
    }
  }
}
function findFormulaList(list: any, ifId: string, type: string) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].itemId == ifId) {
      if (list[i].judgmentBranchChildren) {
        list[i].judgmentBranchChildren.push({
          itemId: setItemId(),
          judgmentBranch: type == "childIf" ? "IF" : type,
          conditionType: "compare",
        });
      } else {
        list[i].judgmentBranchChildren = [];
        list[i].judgmentBranchChildren.push({
          itemId: setItemId(),
          judgmentBranch: type == "childIf" ? "IF" : type,
          conditionType: "compare",
          children: [
            {
              itemId: setItemId(),
            },
          ],
        });
      }
      list[i].score = "";
    } else {
      if (list[i].judgmentBranchChildren && list[i].judgmentBranchChildren.length > 0) {
        list[i].judgmentBranchChildren = findFormulaList(list[i].judgmentBranchChildren, ifId, type);
      }
    }
  }
  return list;
}
// 递归查找 ifId
function findIfId(list: any, ifId: string, type: string) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].itemId == ifId) {
      const obj = {
        children: list[i].children,
        itemId: list[i].itemId,
        conditionType: type,
        judgmentBranch: list[i].judgmentBranch,
        judgmentBranchChildren: list[i].judgmentBranchChildren,
      };
      list[i] = obj;
      break;
      // if (!list[i].conditionType) {
      //   const obj = {
      //     ...addData.value,
      //     conditionType: type,
      //     judgmentBranch: list[i].judgmentBranch,
      //     judgmentBranchChildren: list[i].judgmentBranchChildren,
      //   };
      //   list[i] = obj;
      // } else {
      //   ElMessage.warning("当前节点不可进行此操作！");
      //   break;
      // }
    } else {
      if (list[i].judgmentBranchChildren && list[i].judgmentBranchChildren.length) {
        list[i].judgmentBranchChildren = findIfId(list[i].judgmentBranchChildren, ifId, type);
      }
    }
  }
  return list;
}
// 详情中的结果展示
const { getResultData, emptyData } = useCondition();
</script>
<style lang="scss">
.ifConDiv {
  position: relative;
  .close {
    /*background: #eef5ff;*/
    border-radius: 20px;
    color: #9aa1b1;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: -10px;
    top: -20px;
  }
}
.mode-node + .hasChildren + .fromula-body {
  .item-wrapper {
    &:not(:last-child) {
      margin-bottom: 20px;
    }
    position: relative;
    &:nth-child(1)::before,
    &:last-child::before {
      content: " ";
      display: block;
      width: 5px;
      position: absolute;
      background: #fff;
      height: calc(50% - 0px);
      left: -58px;
      z-index: 1;
    }
    &:nth-child(1)::before {
      top: 0;
    }
    &:last-child::before {
      bottom: 0;
    }
  }
}
.if-conent {
  // display: flex;
  margin-top: 10px;
  position: relative;
  width: 100%;
  &:not(:last-child) {
    & > .if-title::after {
      content: " ";
      position: absolute;
      width: 2px;
      background: #dedede;
      height: calc(100% - 9px);
      left: 30px;
      top: 37px;
      z-index: 1;
    }
  }
  .if-body-list {
    // margin-left: 30px;
    margin-top: 10px;
  }
  .if-title {
    width: 70px;
    height: 36px;
    line-height: 32px;
    text-align: center;
    background: inherit;
    background-color: rgba(179, 198, 235, 1);
    box-sizing: border-box;
    border-width: 2px;
    border-style: solid;
    border-color: rgba(249, 245, 245, 1);
    border-radius: 18px;
    font-weight: 650;
    font-style: normal;
    font-size: 14px;
    color: #262f3d;
    cursor: pointer;
    z-index: 3;
  }
  .if-body {
    background-color: transparent;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-color: rgba(238, 240, 244, 1);
    padding: 0px 0 20px;
    border-radius: 5px;
    position: relative;
    & > .delete-if {
      position: absolute;
      right: 5px;
      top: 5px;
      font-size: 12px;
      cursor: pointer;
    }
    .parentChose {
      border-color: #548fe3;
      border-width: 1px;
      border-style: solid;
      border-radius: 8px;
    }
    .formula-item-if {
      padding: 20px 10px 20px 5px;
    }
    .then-class {
      .if-title {
        float: left;
      }
      width: 100%;
      padding-top: 20px;
      border-top: 2px solid #e4e4e4;
      & > .el-input {
        width: 300px;
        margin-left: 20px;
      }
    }
  }
}

.add-formula {
  position: relative;
  min-height: 28px;
  .el-icon {
    transform: translateY(2px);
  }
  &:before {
    content: "";
    position: absolute;
    height: 2px;
    width: 90%;
    background: #eee;
    left: 30px;
    top: 26px;
    z-index: 0;
  }
  .add-btn-filter-box {
    position: absolute;
    left: calc(50% - 120px);
    top: 12px;
    &.noHaveFilter {
      left: calc(50% - 60px);
    }
  }
  & > .add-btn-filter-box .add-btn {
    // position: absolute;
    display: inline-block;

    &.add-filter {
      margin-left: 10px;
    }
  }
  .add-symbol,
  .add-btn-filter-box .add-btn {
    width: 100px;
    background-color: rgba(238, 245, 255, 1);
    border-radius: 18px;
    text-align: center;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
    color: #144ec1;
    cursor: pointer;
  }
  .add-symbol {
    position: absolute;
    left: calc(50% - 50px);
  }
}
.has-if-parent::before {
  content: " ";
  position: absolute;
  width: 2px;
  background: #dedede;
  height: 21px;
  left: 30px;
  top: -21px;
  z-index: 1;
}
.node-mode {
  display: inline-block;

  margin-bottom: 20px;
  width: 50px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  border-radius: 18px;
  font-size: 14px;
  font-weight: 650;
  color: #262f3d;
  cursor: pointer;
  &:not(:last-child) {
    margin-right: 10px;
  }
}
.proportion {
  margin-top: 20px;
  border-top: 1px solid #e4e4e4;
  border-radius: 5px;
  .score-desc {
    margin-top: 15px;
  }
  .proportion-body {
    float: left;
    // display: flex;
    height: 60px;
    box-sizing: border-box;
    & > div {
      float: left;
    }
    line-height: 48px;
    .proportion-title {
      margin-top: 13px;
      width: 70px;
      height: 32px;
      line-height: 30px;
      text-align: center;
      background: inherit;
      background-color: #a2da8c;
      box-sizing: border-box;
      border-width: 2px;
      border-style: solid;
      border-color: #a2da8c;
      border-radius: 18px;
      font-weight: 650;
      font-style: normal;
      font-size: 14px;
      color: #262f3d;
      cursor: pointer;
      z-index: 3;
    }
    & > div {
      margin-right: 20px;
      margin-top: 5px;
    }
    .el-select {
      margin: 0px 20px;
    }
  }
}
.score-desc {
  width: 40%;
  text-align: right;
  & > div {
    float: right;
    width: 80px;
    text-align: center;
    line-height: 35px;
    font-weight: 500;
    font-size: 10px;
    color: #262824;
  }
  & > .el-input {
    float: right;
    width: calc(100% - 80px);
  }
}
</style>
<style scoped lang="scss">
:deep(.condition-result-wrapper.is-root) {
  width: calc(100% - 18px);
  background: #f4f7fb;
}
</style>
