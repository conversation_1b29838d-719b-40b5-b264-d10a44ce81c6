import { setItemId } from "./commin";

export default function (showCondition = ref("")) {
  //条件编辑
  const choseId = ref("");
  function getChoseId(id) {
    choseId.value = id;
  }
  function getIfId(data) {
    choseIfId.value = data.ifId;
  }
  const choseIfId = ref("");
  const emptyData = [
    {
      itemId: setItemId(),
      judgmentBranch: "IF",
      conditionType: "compare",
      nodeType: "and",
      children: [{ itemId: setItemId(), notetype: "condition" }],
    },
  ];
  const judgmentContent = ref(JSON.parse(JSON.stringify(emptyData)));
  let conditionData = ref([]);
  function changeConditionData(data) {
    conditionData.value = data;
  }
  // 获取条件数据,转换格式
  function getConditionsData(validate = true) {
    const obj = {
      name: "事件",
      notetype: "and",
      ...conditionData.value[0],
      children: JSON.parse(JSON.stringify(conditionData.value[0].children || [])),
    };
    const result = translateData(obj.children, validate); // false 未通过校验

    return result ? obj : false;
  }

  //提交时数据格式修改，验证条件信息是否填写完全
  function translateData(arr, validate = true) {
    for (let item of arr) {
      item.notetype = item.notetype || item.nodeType || "condition";

      //连接符
      if (["and", "or", "not"].includes(item.notetype)) {
        item.name = "";
      } else if (item.name == "filter") {
        //过滤器
        item.operator = "in";
        item.operatorText = "等于";
        item.nameText = "过滤器";
        item.notetype = "condition";
        item.valueText = item.valueText;
        item.value = item.value;
        item.name = "filter";

        if (!item.value && validate) return false;
      } else if (item.notetype == "condition") {
        //普通条件
        if (validate && (!item.name || !item.operator || (item.kong !== true && !item.value))) {
          return false; // 校验是否填写完成
        } else if (item.operator == "refRule" && (!item.refOperator || !item.refName)) {
          // 规则引用 校验是否填写完成
          return false;
        }
        if (item.operator == "refRule") {
          // 规则引用 改为多选，用逗号分隔
          if (item && Array.isArray(item.value)) {
            item.value = item.value.join(",");
          }
        }
        delete item.operatorList;
        delete item.valueList;
      }

      if (!item.notetype && validate) return false;

      delete item.nodeType;
      delete item.itemId;
      delete item.id;

      if (item.children && item.children.length) {
        const result = translateData(item.children);

        if (!result && validate) return false;
      }
    }

    return true;
  }

  //清空条件
  function clearConditions() {
    judgmentContent.value = JSON.parse(JSON.stringify(emptyData));
  }

  //编辑回显
  function echoConditions(objData) {
    // objData === '{}' 时，清空条件

    if (JSON.stringify(objData) === "{}") {
      return JSON.parse(JSON.stringify(emptyData));
    }
    const list = [
      {
        itemId: setItemId(),
        judgmentBranch: "IF",
        conditionType: "compare",
        nodeType: "and",
        children: [],
      },
    ];

    const children = echoTranslateCondition(objData.children);

    //兼容之前的数据，children大于1时，需要在外边嵌套一层and
    if (children.length > 1) {
      list[0].children = [{ name: "", notetype: "and", nodeType: "and", itemId: setItemId(), children }];
    } else {
      list[0].children = children;
    }

    return list;
  }
  function echoTranslateCondition(arr) {
    if (Array.isArray(arr)) {
      for (let item of arr) {
        item.itemId = setItemId();
        item.nodeType = item.notetype;
        if (item.children && item.children.length) {
          item.children = echoTranslateCondition(item.children);
        }
      }
      return arr;
    }
    return [];
  }

  //实时的条件，用于结果显示
  const judgmentContentRealTime = computed(() => {
    if (conditionData.value[0]) {
      return echoConditions(getConditionsData(false)) || [];
    }
    return [];
  });

  //详情展示时的数据，仅用于ifContent中的<result></result>
  function getResultData(ifContentData) {
    changeConditionData(ifContentData);

    const obj = echoConditions(getConditionsData(false));
    return obj[0].children;
  }

  return {
    choseId,
    getChoseId,
    choseIfId,
    getIfId,
    judgmentContent,
    changeConditionData,
    judgmentContentRealTime,
    getConditionsData,
    echoConditions,
    clearConditions,
    getResultData,
    emptyData,
  };
}
