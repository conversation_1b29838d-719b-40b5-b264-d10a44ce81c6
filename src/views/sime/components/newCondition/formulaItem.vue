<template>
  <!-- <div v-if="level == 3">
    {{ itemList }}
  </div> -->
  <div ref="formulaType" class="fornula-type">
    <div
      v-if="itemList.children && itemList.children.length > 1 && itemList.nodeType"
      class="mode-node"
      :style="{
        background: itemList.nodeType == 'and' ? 'rgba(238, 245, 255, 1)' : itemList.nodeType == 'or' ? 'rgba(255, 216, 168, 1)' : '#f36c6e',
        border: itemList.itemId == choseId ? '2px solid #548fe3' : '',
        'z-index': zIndex,
      }"
      :class="hasParent ? 'to-left' : ''"
      @click="choseItemId(itemList.itemId, itemList.nodeType)"
    >
      {{ itemList.nodeType }}
      <el-icon class="del-symbol-icon" v-if="editable" @click.stop="deleteList(itemList)"><CloseBold /></el-icon>
    </div>
    <!-- 单个not条件 -->
    <div
      v-else-if="itemList.children && itemList.children.length > 0 && itemList.nodeType == 'not'"
      class="mode-node"
      :style="{
        background: itemList.nodeType == 'and' ? 'rgba(238, 245, 255, 1)' : itemList.nodeType == 'or' ? 'rgba(255, 216, 168, 1)' : '#f36c6e',
        border: itemList.itemId == choseId ? '2px solid #548fe3' : '',
        'z-index': zIndex,
      }"
      :class="hasParent ? 'to-left' : ''"
      @click="choseItemId(itemList.itemId, itemList.nodeType)"
    >
      {{ itemList.nodeType }}
      <el-icon class="del-symbol-icon" v-if="editable" @click.stop="deleteList(itemList)"><CloseBold /></el-icon>
    </div>
    <div v-if="itemList.children && itemList.children.length > 1" class="hasChildren"></div>
    <div
      v-if="itemList.children && itemList.children.length > 0"
      class="fromula-body"
      :style="{
        'margin-left':
          itemList.children && (itemList.children.length > 1 || (itemList.nodeType == 'not' && itemList.children.length > 0)) ? '80px' : '0px',
        width:
          itemList.children && (itemList.children.length > 1 || (itemList.nodeType == 'not' && itemList.children.length > 0))
            ? 'calc(100% - 80px)'
            : '100%',
      }"
    >
      <VueDraggableNext :disabled="!editable || level < 2" v-bind="dragOptions" :list="itemList.children" :group="{ name: 'g1' }" @end="onEnd">
        <div class="item-wrapper" v-for="(item, itemIndex) in itemList.children" :key="item.itemId">
          <!-- 条件 -->
          <FormulaEdit
            v-if="!item.children"
            :data="item"
            :index="itemIndex"
            :condition-type="conditionType"
            :level="level + 1"
            :chose-id="choseId"
            :has-parent="level == 1 ? false : true"
            :editable="editable"
            :type-list="typeList"
            @data-change="getDataChange"
            @isChose="isChoseItemId"
            @deleteItemFun="deleteItemFun"
            :haveFilter="haveFilter"
          ></FormulaEdit>

          <div v-if="!item.children"></div>
          <!-- and | or |not 条件符号 -->
          <FormulaItem
            v-else
            :list="item"
            :z-index="zIndex - 1"
            :level="level + 1"
            :index="itemIndex"
            :has-parent="level == 1 ? false : true"
            :condition-type="conditionType"
            :chose-id="choseId"
            :editable="editable"
            :type-list="typeList"
            @isChose="isChoseItemId"
            @changeList="toParent"
            @delList="delListToparent"
            @validateDataFormat="onEnd"
            :haveFilter="haveFilter"
            @deleteItemFun="deleteItemFun"
          ></FormulaItem>
        </div>
      </VueDraggableNext>
    </div>

    <div
      v-else
      class="fromula-body"
      :style="{
        'margin-left': itemList.children && itemList.children.length > 1 ? '80px' : '0px',
        width: itemList.children && itemList.children.length > 1 ? 'calc(100% - 80px)' : '100%',
      }"
    ></div>
  </div>
</template>
<script setup lang="ts">
import FormulaItem from "./formulaItem.vue";
import FormulaEdit from "./formulaEdit.vue";
import { VueDraggableNext } from "vue-draggable-next";

import { setItemId } from "./commin";

interface IStringObject {
  [index: string]: any;
}
interface Props {
  list: IStringObject;
  index?: number;
  hasParent?: boolean;
  conditionType?: string;
  choseId?: string;
  zIndex: number;
  level: number;
  editable: boolean;
  typeList: any;
  haveFilter: boolean;
}
const emits = defineEmits(["changeList", "isChose", "delList", "validateDataFormat", "deleteItemFun"]);
const props = withDefaults(defineProps<Props>(), {
  index: -1,
  hasParent: true,
  conditionType: "compare",
  choseId: "",
  haveFilter: true,
});
const itemList = ref(props.list);

// 选中条件
function isChoseItemId(itemId: string, node: string, level: number) {
  emits("isChose", itemId, node, level);
}

/* 删除项 */
const deleteItemFun = (data) => {
  emits("deleteItemFun", data);
};

// 选中节点（符号）名称
function choseItemId(itemId: string, node: string) {
  emits("isChose", itemId, node, props.level);
}
function getDataChange(data: any, index: number) {
  if (index >= 0) {
    if (data) {
      itemList.value.children[index] = data;
    } else {
      itemList.value.children.splice(index, 1);

      //同时删除itemList 判断是否是not只有一个条件
      if (itemList.value.nodeType == "not" && itemList.value.children.length == 0) {
        itemList.value = {
          itemId: setItemId(),
        };
      } else if (itemList.value.children.length == 1 && props.level !== 1) {
        if (itemList.value.nodeType !== "not") {
          const data = JSON.parse(JSON.stringify(itemList.value.children[0]));
          itemList.value = data;
        }
      }
    }
  } else {
    if (data.children) {
      delete data.children;
    }
    itemList.value = data;
    //删除唯一一条not条件时需要重制为空条件
    if (!data) {
      itemList.value = {
        itemId: setItemId(),
      };
    }
  }

  emits("changeList", itemList.value, props.index);
}
function toParent(data: any, index: number) {
  if (data) {
    if (index >= 0) {
      itemList.value.children[index] = data;
    } else {
      itemList.value = data;
    }
  } else {
    if (index >= 0) {
      itemList.value.children.splice(index, 1);
    } else {
      itemList.value = undefined;
    }
  }
  emits("changeList", itemList.value, props.index);
}

const dragOptions = {
  group: "nested",
  ghostClass: "ghost",
  animation: 150,
  fallbackOnBody: true,
  swapThreshold: 0.65,
};

//删除条件符号
function deleteList(data) {
  emits("delList", data, props.index);
}

function delListToparent(data, index) {
  if (itemList.value.children.find((item) => item.itemId == data.itemId)) {
    if (itemList.value.judgmentBranch !== "IF") {
      itemList.value.children.splice(index, 1);
      if (itemList.value.children.length == 1) {
        itemList.value.children.push({ itemId: setItemId(), notetype: "condition" });
      }
    } else {
      itemList.value.children = [{ itemId: setItemId(), notetype: "condition" }];
    }
  }
}

function onEnd() {
  emits("validateDataFormat");
}
</script>
<style lang="scss">
.fornula-type {
  position: relative;
  & > .mode-node {
    width: 50px;
    text-align: center;
    height: 48px;
    line-height: 45px;
    position: absolute;
    top: calc(50% - 24px);

    box-sizing: border-box;
    border-radius: 18px;
    border: 2px solid rgba(255, 255, 255, 1);
    font-size: 14px;
    font-weight: 650;
    color: #262f3d;
    z-index: 23;
  }
  .hasChildren {
    position: absolute;
    background: #dedede;
    height: calc(100% - 48px);
    width: 2px;
    left: 24px;
    top: 24px;
    z-index: 1;
  }
  & > .fromula-body {
    width: calc(100% - 80px);
    margin-left: 80px;
  }
}
.to-left::after {
  content: " ";
  position: absolute;
  width: 56px;
  background: #dedede;
  height: 2px;
  left: -58px;
  top: 22px;
  z-index: -1;
}
.del-symbol-icon {
  position: absolute;
  right: 2px;
  top: 2px;
  opacity: 0.3;
  cursor: pointer;
  display: none;
  &:hover {
    opacity: 0.8;
  }
}
.mode-node:hover {
  .del-symbol-icon {
    display: block;
  }
}
</style>
