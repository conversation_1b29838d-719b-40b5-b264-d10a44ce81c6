<template>
  <div class="item" :class="{ 'is-filter-item': newData.name == 'filter', 'is-detail': !editable }">
    <div v-if="hasParent" class="item-line"></div>
    <div
      class="item-content"
      :style="{
        border: newData.itemId && newData.itemId == choseId ? '1px solid #548fe3' : '',
      }"
      @click="conditionType == 'compare' && editable ? choseItem() : ''"
    >
      <template v-if="editable">
        <el-popover placement="top-start" :width="80" trigger="hover" :content="toggleText">
          <template #reference v-if="haveFilter">
            <div class="item-icon" @click.stop="toggleCondiType">
              <el-icon><Switch /></el-icon>
            </div>
          </template>
        </el-popover>

        <!-- 增加 haveFilter参数，满足当前业务，是否有过滤器按钮 -->
        <div class="item-icon" v-if="!haveFilter">
          <el-icon><Rank /></el-icon>
        </div>

        <!--  @click.stop -->
        <div class="form-list">
          <!-- 普通条件 -->
          <edit-condition
            v-if="newData.name != 'filter'"
            :data="newData"
            :type-list="typeList"
            @finish="
              (a, b) => {
                finishCon(a, b);
              }
            "
          ></edit-condition>
          <!-- 过滤器选择 -->
          <div v-if="newData.name == 'filter'" class="filter-box">
            <xel-select-tree
              :key="filterList"
              v-model="newData.value"
              v-model:text="newData.valueText"
              placeholder="请选择过滤器"
              :data="filterList"
              :tree-props="{ children: 'children', label: 'name' }"
              :multiple="false"
              pop-width="300px"
              @nodeClick="
                (data) => {
                  nodeClick(data);
                }
              "
            ></xel-select-tree>
          </div>
        </div>

        <!-- 删除按钮 -->
        <div v-if="hasParent" class="del-icon">
          <el-icon @click.stop="deleteItem"><DeleteFilled /></el-icon></div
      ></template>
      <template v-else>
        <!--仅展示 -->
        <!-- 条件语句 -->
        <div v-if="newData.notetype == 'condition' && newData.name != 'filter'" class="cond-data" @click="editCond(item)">
          {{ newData.nameText }}

          <span v-if="!['isnull', 'notnull'].includes(newData.operator)" class="margin-right20 margin-left20"> {{ newData.operatorText }}</span>
          <div class="break value-text">
            {{ newData.valueText }}
          </div>
          <span v-if="newData.operator == 'isnull'"> 为空</span>
          <span v-else-if="newData.operator == 'notnull'"> 不为空</span>
          <span v-if="newData.operator == 'refRule' && newData.refOperator" class="margin-right20 margin-left20"> {{ newData.refOperatorText }}</span>
          <div v-if="newData.operator == 'refRule' && newData.refName" class="margin-left20">{{ newData.refNameText }}</div>
        </div>
        <!-- 过滤器语句 -->
        <div v-if="newData.notetype == 'condition' && newData.name == 'filter'" class="cond-data" @click="editCond(item)">
          过滤器 ：
          <span class="margin-right20 margin-left20"> {{ newData.valueText }}</span>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
// import useFormulaDict from "@/store/modules/formula";
import { ref, watch, watchEffect, inject } from "vue";
import editCondition from "../editCondition.vue";
import { ElMessage } from "element-plus";
import dicsStore from "@/store/modules/dictsData";
if (!dicsStore.state.numberFlag || !dicsStore.state.stringFlag || !dicsStore.state.ruleFlag) {
  dicsStore.actions.getFilterOperatorNumRes();
  dicsStore.actions.getFilterOperatorStrRes();
  dicsStore.actions.getRuleOperatorRes();
}
interface IStringObject {
  [index: string]: any;
}

interface Props {
  data: any;
  index?: number;
  hasParent?: boolean;
  conditionType: string;
  choseId?: string;
  level: number;
  editable: boolean;
  typeList: any;
  haveFilter: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  index: -1,
  hasParent: true,
  choseId: "",
  haveFilter: true,
});

const newData = ref({ value: "", valueText: "", ...props.data });

const emits = defineEmits(["dataChange", "isChose", "deleteItem", "deleteItemFun"]);
// 数据发生变化后传值
function choseItem() {
  emits("isChose", props.data.itemId, "isChildren", props.level);
}
// 删除当前条件内容
function deleteItem() {
  emits("deleteItemFun", newData.value);
  newData.value = "";
}

//监听编辑后值变化  并传值
watch(
  () => newData.value,
  (val) => {
    emits("dataChange", newData.value, props.index);
  },
  {
    deep: true,
  }
);

function finishCon(a, b) {
  newData.value = {
    ...newData.value,
    ...b,
  };
}

//过滤器使用start

let filterList = inject("filterList");

//切换索引表时，清空过滤器
if (filterList) {
  watch(
    () => filterList.value,
    (val) => {
      if (val && val.length > 0 && newData.value.name == "filter" && newData.value.value != "") {
        //递归查询是否存在当前选中的过滤器
        const ifExitFilterFlag = ifExitFilter(val, newData.value.value);

        if (!ifExitFilterFlag) {
          newData.value.value = newData.value.valueText = "";
        }
      }
    },
    {
      deep: true,
    }
  );
}

//判断过滤器是否存在
function ifExitFilter(arr, id) {
  let flag = false;
  for (const item of arr) {
    if (item.id == id) {
      flag = true;
      return flag;
    } else if (item.children && item.children.length > 0) {
      flag = ifExitFilter(item.children, id);
      if (flag) {
        return flag;
      }
    }
  }
  return flag;
}

let filterId = inject("filterId");

function nodeClick(data) {
  if (data.id == filterId.value) {
    ElMessage.warning("过滤器不能选择自身");
    nextTick(() => {
      newData.value.value = newData.value.valueText = "";
    });
  }
}

//过滤器使用end
let condiType = ref("condition"); //condition | filter
const toggleText = computed(() => {
  return condiType.value == "condition" ? "切换为过滤器" : "切换为条件";
});
function toggleCondiType() {
  condiType.value = condiType.value == "condition" ? "filter" : "condition";
  newData.value = {
    name: condiType.value == "filter" ? "filter" : "",
    value: "",
    valueText: "",
    itemId: newData.value.itemId,
  };
}
</script>
<style scoped lang="scss">
.value-text {
  width: 38%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.is-filter-item {
  .item-content {
    :deep(.custom-select) {
      border-color: #a0cfff;
      color: #409eff;
    }
  }
}
.item-content {
  position: relative;
  height: 48px;
  background-color: #fff;
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(238, 240, 244, 1);
  border-radius: 5px;
  line-height: 48px;
  padding: 0px 30px 0 26px;

  z-index: 2;
  display: flex;
  width: 100%;
  &:before,
  &:after {
    width: 1px;
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 30px;
    background: #eee;
  }
  &:after {
    right: 35px;
    left: auto;
  }
  .item-icon {
    width: 12px;
    height: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    margin-right: 5px;
    margin-top: 16px;
    margin-left: -14px;
    margin-right: 14px;
    flex-shrink: 0;
    transform: translateX(-3px);
    cursor: pointer;
    color: #9aa1b1;
  }
  .del-icon {
    position: absolute;
    right: 10px;
    color: #9aa1b1;
    cursor: pointer;
  }
}
.item {
  position: relative;
  &.is-detail {
    .item-cont {
      cursor: default;
    }
    .item-content {
      &::before,
      &:after {
        display: none;
      }
      .cond-data {
        width: 100%;
        background: #fff;
        padding: 8px;
        display: inline-flex;
        // vertical-align: text-bottom;
        align-items: center;
        border-radius: $radiusS;

        span {
          color: #409eff;
        }
      }
    }
  }
}
.item-line {
  content: " ";
  position: absolute;
  width: 60px;
  height: 2px;
  left: -56px;
  top: 22px;
  z-index: 1;
  background: #dedede;
}
.form-list {
  /*display: flex;*/
  align-items: center;
  width: calc(100% - 20px);
  .el-select,
  .el-input,
  .el-button {
    margin-right: 20px;
  }
}
.edit-end {
  max-width: 150px;
  padding: 0px 10px;
  height: 32px;
  line-height: 32px;
  display: inline-block;
  text-align: left;
  margin-right: 20px;
  border: 1px solid #a0cfff;
  background-color: #ecf5ff;
  border-radius: 20px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 20px;
}
.calculate {
  width: 100%;
  .calculate-body {
    float: left;
    display: flex;
    .calculate-title {
      margin-top: 5px;
      width: 70px;
      height: 32px;
      line-height: 30px;
      text-align: center;
      background: inherit;
      background-color: #a2da8c;
      box-sizing: border-box;
      border-width: 2px;
      border-style: solid;
      border-color: #a2da8c;
      border-radius: 18px;
      font-weight: 650;
      font-style: normal;
      font-size: 14px;
      color: #262f3d;
      cursor: pointer;
      z-index: 3;
    }
    .el-button,
    .edit-end {
      margin-top: 5px;
      margin-left: 10px;
    }
  }
  .score-desc {
    width: 40%;
    text-align: right;
    margin-top: 4px;
    & > div {
      float: right;
      width: 80px;
      text-align: center;
      line-height: 35px;
      font-weight: 500;
      font-size: 10px;
      color: #262824;
    }
    & > .el-input {
      float: right;
      width: calc(100% - 80px);
      margin-right: 0px;
    }
  }
}
:deep(.edit-condition-wrapper) {
  display: flex;
  justify-content: space-between;
  .label-text {
    display: none;
  }
  .form-item {
    width: 92%;
  }
  > p {
    width: 100%;
  }
}
.filter-box {
  width: 100%;
  max-width: 420px;
  transform: translateY(2px);
}
</style>
