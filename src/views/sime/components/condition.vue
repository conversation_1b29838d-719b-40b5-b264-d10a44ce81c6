<template>
  <div class="condDiv">
    <!-- 增加 - 关闭按钮 -->
    <el-icon class="close" size="14" v-if="isShowClose" @click="closeFun"><close /></el-icon>
    <!-- 条件图标，模板 -->
    <ul class="filter-icon-ul">
      <li v-for="item in labelList" :key="item.icon" @click="addLabel(item)">
        <div class="icon-box" :style="{ background: item.color }">
          <icon :n="item.icon" :size="24"> </icon>
        </div>
        <span :style="{ color: item.color }"> {{ item.label }}</span>
      </li>
    </ul>
    <!-- 条件编辑 -->
    <section class="condition-wrapper">
      <!-- 条件语句 -->
      <condition-result v-if="conditionList[0].children.length && showResult" :list="conditionList[0].children"></condition-result>
      <condition-item
        :editable="editable"
        :list="conditionList"
        :type-list="typeList"
        :level-index-arr="[]"
        @changeCurrentIndexArr="changeCurrentIndexArr"
        @delItemFun="delItemFun"
      ></condition-item>
    </section>
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";

import conditionItem from "./conditonItem.vue"; //条件编辑
import conditionResult from "./conditionResult.vue"; //瓶装后的条件语句

import { v4 as uuidv4 } from "uuid";
import handlerConData from "../utils/handlerConData";
let emit = defineEmits(["getConditionList", "delItemFun", "closeFun"]);

let props = defineProps({
  /* 是否显示关闭按钮 */
  isShowClose: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  /* 新增 - 调整，labelList改为可配置 */
  labelList: {
    type: Array,
    default: () => {
      return [
        {
          notetype: "event",
          icon: "icon-IF",
          label: "事件",
          color: import.meta.env.VITE_COLOR,
        },
        {
          notetype: "or",
          icon: "icon-or",
          label: "OR",
          color: "#409EFF",
          bgColor: "#ECF5FF",
        },
        {
          notetype: "and",
          icon: "icon-and",
          label: "AND",
          color: "#67C23A",
          bgColor: "#F0F9EB",
        },
        {
          notetype: "not",
          icon: "icon-not",
          label: "NOT",
          color: "#F56C6B",
          bgColor: "#FEF0F0",
        },
        {
          notetype: "condition",
          icon: "icon-condition",
          label: "条件",
          color: "#E6A23C",
          bgColor: "#f0f1f4",
          editStatus: true,
          kong: false,
          data: {
            nameText: "", //filledText
            operatorText: "", //操作汉字
            valueText: "",
          },
        },
        {
          notetype: "condition",
          name: "filter",
          icon: "icon-guolv",
          label: "过滤器",
          operatorText: "等于",
          operator: "in",
          color: "#909399",
          bgColor: "#F4F4F5",
          editStatus: true,
          value: "",
          valueText: "",
        },
      ];
    },
  },

  /* 新增 - 是否显示 result 回显数据 */
  showResult: {
    type: Boolean,
    default() {
      return true;
    },
  },

  //回显的数据
  data: {
    type: Array,
    default: () => {
      return [];
    },
  },
  //条件编辑的操作列表
  typeList: {
    type: Array,
    default() {
      return [];
    },
  },
  //是否可编辑
  editable: {
    type: Boolean,
    default: true,
  },
});

//notetype:"and",name:'',
/*let labelList = [
  {
    notetype: "event",
    icon: "icon-IF",
    label: "事件",
    color: import.meta.env.VITE_COLOR,
  },
  {
    notetype: "or",
    icon: "icon-or",
    label: "OR",
    color: "#409EFF",
    bgColor: "#ECF5FF",
  },
  {
    notetype: "and",
    icon: "icon-and",
    label: "AND",
    color: "#67C23A",
    bgColor: "#F0F9EB",
  },
  {
    notetype: "not",
    icon: "icon-not",
    label: "NOT",
    color: "#F56C6B",
    bgColor: "#FEF0F0",
  },
  {
    notetype: "condition",
    icon: "icon-condition",
    label: "条件",
    color: "#E6A23C",
    bgColor: "#f0f1f4",
    editStatus: true,
    kong: false,
    data: {
      nameText: "", //filledText
      operatorText: "", //操作汉字
      valueText: "",
    },
  },
  {
    notetype: "condition",
    name: "filter",
    icon: "icon-guolv",
    label: "过滤器",
    operatorText: "等于",
    operator: "in",
    color: "#909399",
    bgColor: "#F4F4F5",
    editStatus: true,
    value: "",
    valueText: "",
  },
];*/
let state = reactive({
  conditionList: [
    {
      notetype: "event",
      icon: "icon-IF",
      label: "事件",
      color: import.meta.env.VITE_COLOR,
      children: [],
    },
  ],
  currentIndexArr: [0],
});
let { conditionList, currentIndexArr } = toRefs(state);

watch(
  () => conditionList.value,
  (val) => {
    emit("getConditionList", val);
  },
  { deep: true }
);

/* closeFun 关闭按钮 */
const closeFun = () => {
  emit("closeFun");
};

//添加
function addLabel(item) {
  if (!props.editable) {
    return;
  }
  if (item.notetype == "event") return;
  let addEmpty = {
    ...item,
    uuid: uuidv4(),
    children: [],
  };
  if (state.currentIndexArr.length > 1) {
    let list = getListByIndexArr(state.currentIndexArr);
    list.push(addEmpty);
  } else {
    state.conditionList[0].children.push(addEmpty);
  }
}

//赋值索引数据
function changeCurrentIndexArr(arr) {
  state.currentIndexArr = arr;
}

/* 新编辑器删除 */
function delItemFun(data) {
  emit("delItemFun", data);
}

//根据索引数组获取列表数据 [0,1,2]
function getListByIndexArr(indexArr) {
  let arr = JSON.parse(JSON.stringify(indexArr));
  if (arr.length == 1) {
    return state.conditionList[0].children;
  } else {
    let list = getListByIndexArr(arr.slice(0, arr.length - 1));

    let lastIndex = arr.pop();

    return list[lastIndex].children;
  }
}

//处理条件列表
function hanlerList(list) {
  let arr = [];
  for (let item of list) {
    let { notetype } = item;
    //判断是否是逻辑运算符
    if (notetype == "event") {
      let childrenList = hanlerList(item.children);
      if (!childrenList) {
        return false;
      }
      arr.push({
        name: "事件",
        notetype: "and",
        children: childrenList,
      });
    } else if (["and", "or", "not"].includes(notetype)) {
      let childrenList = hanlerList(item.children);
      if (!childrenList) {
        return false;
      }
      arr.push({
        name: "",
        notetype: notetype,
        children: childrenList,
      });
    } else if (notetype == "condition") {
      if (item.editStatus) {
        ElMessage.warning("请保存条件！");
        return false;
      } else {
        if (item.name != "filter") {
          arr.push({ ...item.data, notetype: item.notetype });
        } else {
          arr.push({
            name: "filter",
            operator: "in",
            value: item.value,
            notetype: "condition",
            nameText: "过滤器",
            operatorText: "等于",
            valueText: item.valueText,
          });
        }
      }
    }
  }
  return arr;
}

//回显数据
echoData();
function echoData() {
  watch(
    () => props.data,
    (val) => {
      state.conditionList = [
        {
          notetype: "event",
          icon: "icon-IF",
          label: "事件",
          color: import.meta.env.VITE_COLOR,
          children: [],
        },
      ];
      if (val.children && val.children.length > 0) {
        state.conditionList[0].children = echoConditionList(val.children);
      }
      nextTick(() => {
        state.currentIndexArr = [0];
        document.querySelector(".icon-box.event-icon").click();
      });
    },
    { deep: true, immediate: true }
  );
}
//根据数据，回显对应的conditionList
function echoConditionList(list) {
  let arr = [];
  for (let item of list) {
    
    let copyLabelItem = {};
    if (item.name == "filter") {
      copyLabelItem = JSON.parse(JSON.stringify(props.labelList.find((l) => l.name == "filter")));
    } else {
      copyLabelItem = JSON.parse(JSON.stringify(props.labelList.find((l) => l.notetype == item.notetype)));
    }
    copyLabelItem.uuid = uuidv4();
    if (copyLabelItem.notetype != "condition") {
      copyLabelItem.children = [];
      if (item.children.length > 0) {
        copyLabelItem.children = echoConditionList(item.children);
      }
    } else {
      if (copyLabelItem.name == "filter") {
        copyLabelItem.value = item.value;
        copyLabelItem.valueText = item.valueText;
      } else {
        let conditionData = handlerConData(item, props.typeList);
        copyLabelItem.data = conditionData;
        copyLabelItem.kong = conditionData.kong;
      }
      copyLabelItem.editStatus = false;
      
    }
    arr.push(copyLabelItem);
    
  }

  return arr;
}

defineExpose({
  getList: () => {
    return hanlerList(state.conditionList);
  },
});
</script>

<style lang="scss" scoped>
.condDiv {
  position: relative;
  .close {
    /*background: #eef5ff;*/
    border-radius: 20px;
    color: #9aa1b1;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: -10px;
    top: -13px;
  }
}

.filter-icon-ul {
  display: flex;
  justify-content: space-between;
  li {
    // margin-right: 40px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}
.condition-wrapper {
  background: #f9f9f9;
  padding: 20px 24px;
  margin-top: 25px;
  border-radius: $radiusL;
  max-height: calc(100vh - 320px);
  overflow-y: auto;
}
:deep(.icon-box) {
  width: 36px;
  height: 36px;
  border-radius: $radiusS;
  margin-right: 9px;
  color: #fff;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
</style>
