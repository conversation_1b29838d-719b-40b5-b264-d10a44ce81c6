<template>
  <ul class="condition-item-ul">
    <vue-draggable-next :disabled="!editable" v-bind="dragOptions" :list="list" :group="{ name: levelIndexArr.length ? 'g1' : 'g0' }" @end="onEnd">
      <li v-for="(item, index) in list" :key="index" :class="{ 'has-children': item.children && item.children.length > 0 }" :data-uuid="item.uuid">
        <div
          class="icon-box"
          v-focus
          :class="{ pointer: ['or', 'and', 'not', 'event'].includes(item.notetype), 'event-icon': item.notetype == 'event' }"
          :style="{ background: item.bgColor ? item.bgColor : item.color, color: item.color }"
          @click="saveCurrentIndex(item, index, $event)"
        >
          <icon :n="item.icon" :size="24" :style="{ color: item.bgColor ? item.color : '#fff' }"> </icon>
          <!-- 删除 -->
          <el-icon class="close" size="12" @click.stop="delItemByIndex(index, item)"><close /></el-icon>
        </div>
        <!-- 条件编辑 -->
        <div
          class="condition-form-wrapper flex"
          style="width: calc(100% - 50px)"
          v-if="item.notetype == 'condition' && item.editStatus == true && item.name != 'filter'"
        >
          <edit-condition
            :typeList="typeList"
            :data="item.data"
            @finish="
              (a, b) => {
                finishCon(item, a, b);
              }
            "
          ></edit-condition>
          <el-button v-if="editable" type="primary" style="transform: scale(0.9)" @click="saveCond(item)">确定</el-button>
        </div>

        <!-- 过滤器 -->
        <div class="filter-condition-wrapper" v-else-if="item.name == 'filter'" v-show="item.editStatus == true">
          <xel-select-tree
            v-model="item.value"
            v-model:text="item.valueText"
            :data="filterList"
            :tree-props="{ children: 'children', label: 'name' }"
            :multiple="false"
            pop-width="300px"
            @nodeClick="
              (data) => {
                nodeClick(data, item);
              }
            "
          ></xel-select-tree>
          <el-button type="primary" style="transform: scale(0.9)" @click="saveFilter(item)">确定</el-button>
        </div>
        <!-- 条件语句 -->
        <div
          v-if="item.notetype == 'condition' && !item.editStatus && item.name != 'filter'"
          class="cond-data"
          :class="{ 'ref-rule': item.data.operator == 'refRule' }"
          @click="editCond(item)"
        >
          <span>{{ item.data.nameText }}</span>
          <span class="margin-right20 margin-left20"> {{ item.data.operatorText }}</span>
          <div class="break">{{ item.data.valueText }}</div>
          <span v-if="item.data.operator == 'isnull'"> 为空</span>
          <span v-if="item.data.operator == 'notnull'"> 不为空</span>
          <span v-if="item.data.refOperator" class="margin-right20 margin-left20"> {{ item.data.refOperatorText }}</span>
          <div v-if="item.data.refName" class="margin-left20">{{ item.data.refNameText }}</div>
        </div>
        <!-- 过滤器语句 -->
        <div v-if="item.notetype == 'condition' && !item.editStatus && item.name == 'filter'" class="cond-data" @click="editCond(item)">
          过滤器 ：
          <span class="margin-right20 margin-left20"> {{ item.valueText }}</span>
        </div>
        <condition-item
          v-if="item.children && item.children.length > 0"
          :editable="editable"
          :list="item.children"
          :type-list="typeList"
          :level-index-arr="levelIndexArr.concat(index)"
          @changeCurrentIndexArr="changeCurrentIndexArrSelf"
          @delItemByIndex="
            (_index, _item) => {
              delItemByIndexParent(item.children, _index, _item);
            }
          "
          @delItemFun="delItemFun"
        >
        </condition-item>
      </li>
    </vue-draggable-next>
  </ul>
</template>
<script>
export default {
  //添加逻辑运算符 直接选中
  directives: {
    focus: {
      // 指令的定义
      mounted(el) {
        if (el.classList.contains("pointer")) {
          el.click();
        }
      },
    },
  },
};
</script>
<script setup>
import { VueDraggableNext } from "vue-draggable-next";
import conditionItem from "./conditonItem.vue";
import { inject, nextTick } from "vue";
import { ElMessage } from "element-plus";
import editCondition from "./editCondition.vue";

import dicsStore from "@/store/modules/dictsData";
if (!dicsStore.state.numberFlag || !dicsStore.state.stringFlag || !dicsStore.state.ruleFlag) {
  dicsStore.actions.getFilterOperatorNumRes();
  dicsStore.actions.getFilterOperatorStrRes();
  dicsStore.actions.getRuleOperatorRes();
}
//过滤器使用start
let filterList = inject("filterList");

let filterId = inject("filterId");
//过滤器使用end

let props = defineProps({
  list: {
    type: Array,
    default() {
      return [];
    },
  },
  levelIndexArr: {
    type: Array,
    default() {
      return [];
    },
  },
  //条件编辑的操作列表
  typeList: {
    type: Array,
    default() {
      return [];
    },
  },
  //是否可编辑
  editable: {
    type: Boolean,
    default: true,
  },
});

let emit = defineEmits(["addBrotherByIndex", "addChildByIndex", "changeCurrentIndexArr", "delItemByIndex", "delItemFun"]);

const dragOptions = {
  group: "nested",
  ghostClass: "ghost",
  animation: 150,
  fallbackOnBody: true,
  swapThreshold: 0.65,
};

function saveCurrentIndex(item, index, $event) {
  if (!props.editable) {
    return;
  }
  //选中样式设置
  let activeItem = document.querySelector(".icon-box-active");
  if (activeItem) {
    activeItem.classList.remove("icon-box-active");
  }
  let arr = [0];

  if (["and", "or", "not", "event"].includes(item.notetype)) {
    if (item.notetype != "event") {
      arr = props.levelIndexArr.concat(index);
    }
    $event.currentTarget.classList.add("icon-box-active");
  }
  emit("changeCurrentIndexArr", arr);
}

function changeCurrentIndexArrSelf(arr) {
  emit("changeCurrentIndexArr", arr);
}
function delItemFun(arr) {
  emit("delItemFun", arr);
}

//拖拽相关
function onEnd() {}

//条件编辑

function finishCon(item, result, data) {
  item.finished = result;

  if (result) {
    item.data = {
      name: data.name, //字段名 filed
      operator: data.operator, //操作
      value: data.value, //输入框值
      nameText: data.nameText, //filledText
      operatorText: data.operatorText, //操作汉字
      valueText: data.valueText,
      content: data.content,
      nameType: data.nameType,
      refOperator: data.refOperator,
      refOperatorText: data.refOperatorText,
      refName: data.refName,
      refNameText: data.refNameText,
    };
  }
}
function saveCond(item) {
  /* 判断 能否继续 */
  if (item.data.value === "" && item.data.operatorText !== "为空" && item.data.operatorText !== "不为空") {
    ElMessage.warning("条件不完整，请填写");
    return false;
  }
  if (item.finished) {
    item.editStatus = false;
  }
  /* 看不懂原逻辑，直接覆盖 */
  /*if (item.finished) {
    item.editStatus = false;
  } else {
    ElMessage.warning("条件不完整，请填写");
  }*/
}
function editCond(item) {
  if (!props.editable) return;
  item.editStatus = true;
}

//删除
function delItemByIndex(index, item) {
  if (!props.editable) {
    return;
  }
  emit("delItemByIndex", index, item);
}
function delItemByIndexParent(list, index, item) {
  if (!props.editable) {
    return;
  }

  list.splice(index, 1);
  emit("delItemFun", item);
  //删除当前选中and或or 设置当前选中为根节点
  nextTick(() => {
    let activeItem = document.querySelector(".icon-box-active");
    if (!activeItem) {
      emit("changeCurrentIndexArr", [0]);
      document.querySelector(".event-icon").classList.add("icon-box-active");
    }
  });
}

function saveFilter(item) {
  if (item.value == "") {
    ElMessage.warning("请选择过滤器！");
    return;
  }
  if ("filterOptions" in item) {
    delete item.filterOptions;
  }
  item.editStatus = false;
}

function nodeClick(data, item) {
  if (data.id == filterId.value) {
    ElMessage.warning("过滤器不能选择自身");
    nextTick(() => {
      item.value = item.valueText = "";
    });
  }
}
</script>

<style lang="scss" scoped>
.condition-item-ul {
  li {
    margin-top: 18px;
    position: relative;
    z-index: 1;
    .condition-item-ul {
      padding-left: 46px;
      position: relative;
      &::before {
        content: "";
        display: block;
        background: #dedede;
        position: absolute;
        left: 64px;
        width: 1px;

        top: 30px;
        bottom: 30px;
      }
      li:last-child.has-children {
        & > .condition-item-ul::after {
          content: "";
          display: block;
          background: #f9f9f9;
          position: absolute;
          left: 18px;
          width: 1px;
          top: -18px;
          bottom: 0px;
        }
      }
      .icon-box-active {
        border: 1px solid currentColor;
      }

      .icon-box:hover {
        .close {
          display: block;
        }
      }
    }
    &.has-children {
      &::before,
      &::after {
        content: "";
        display: block;
        background: #dedede;
        position: absolute;
      }
      &::before {
        width: 28px;
        height: 1px;
        left: 36px;
        top: 18px;
      }
      &::after {
        width: 1px;
        height: 36px;
        left: 64px;
        top: 18px;
      }
    }
  }
  .close {
    position: absolute;
    right: -4px;
    top: -5px;
    background: #fff;
    color: #7d7d7d;
    border-radius: 2px;
    display: none;
    cursor: pointer;
  }
  .flex-form {
    display: inline-flex;
    vertical-align: text-bottom;
    :deep(.el-form-item) {
      margin-bottom: 0;
    }
  }
  .cond-data {
    background: #fff;
    padding: 8px;
    display: inline-flex;
    vertical-align: text-bottom;
    border-radius: $radiusS;
    cursor: pointer;
    span {
      color: #409eff;
    }
  }
  .icon-box-active.event-icon {
    border: 1px solid #cb7530;
  }
}
.condition-form-wrapper {
  display: inline-flex;
  width: calc(100% - 150px);
  min-width: 240px;
  vertical-align: bottom;
  margin-top: -2px;
}
:deep(.edit-condition-wrapper) {
  display: flex;
  .label-text {
    display: none;
  }
  .form-item {
    width: 92%;
  }
}
.filter-condition-wrapper {
  display: inline-block;
  vertical-align: bottom;
  margin-top: -2px;
  :deep(.custom-select) {
    display: inline-block;
    width: 200px;
    background: #fff;
  }
  .el-button {
    margin-left: 10px;
    position: relative;
    top: -2px;
  }
}
.ref-rule {
  span {
    white-space: nowrap;
    .margin-right20 {
      margin-right: 5px;
    }
    .margin-left20 {
      margin-left: 5px;
    }
  }
}
</style>
