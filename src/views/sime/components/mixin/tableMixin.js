import { computed, ref } from "vue";

export default function (popsHeight) {
  let tableHeight = ref("");

  tableHeight = computed(() => {
    // 样式
    let height = "";
    // 获取滚动条高度（宽度）
    let scrollBarHeight = getScrollbarWidth();

    // 当屏幕宽度小于1600时候减去滚动条的高度
    if (document.body.clientWidth < 1600) {
      let hVal = popsHeight + scrollBarHeight;
      height = `calc(100vh - ${hVal}px)`;
    } else {
      height = `calc(100vh - ${popsHeight}px)`;
    }
    return { height };
  });
  console.log("tableHeight: ", tableHeight);
  return { tableHeight };
}

function getScrollbarWidth() {
  var odiv = document.createElement("div"), //创建一个div
    styles = {
      width: "100px",
      height: "100px",
      overflowY: "scroll", //让div有滚动条
    },
    i,
    scrollbarWidth;
  for (i in styles) odiv.style[i] = styles[i];
  document.body.appendChild(odiv); //把div添加到body中
  scrollbarWidth = odiv.offsetWidth - odiv.clientWidth; //相减
  odiv.remove(); //移除创建的div
  return scrollbarWidth; //返回滚动条宽度
}
