<!-- 条件语句 -->
<template>
  <div class="condition-result-wrapper" ref="resultRef" :class="{ 'is-root': isRoot }" v-if="showList">
    <!-- 过滤器显示切换 -->
    <el-icon v-if="isRoot && $route.name != 'Analysis' && isShowEye" @click="changeFilterShow" title="切换过滤器显示" class="filter-show-change"
      ><View
    /></el-icon>
    <span v-for="(item, index) in list" :key="index" class="option-item">
      <template v-if="['and', 'or', 'not'].includes(item.notetype)">
        <span v-if="logic == 'and'" class="logic-icon" :class="item.notetype == 'not' ? 'not' : 'and'">
          {{ item.notetype == "not" ? "NOT" : "AND" }}
        </span>
        <template v-else-if="logic == 'or'">
          <template v-if="item.notetype == 'not'">
            <span class="logic-icon or"> OR </span>
            (
            <span class="not"> NOT </span>
          </template>
          <span v-else class="logic-icon or"> OR </span>
        </template>
        <template v-else>
          <span class="logic-icon not"> NOT </span>
        </template>
        <!-- <template v-if="logic == 'or' && item.notetype == 'not'">
          <span class="or"> OR </span>
          (
          <span class="not"> NOT </span>
        </template>
        <span v-else class="logic-icon" :class="logic && item.notetype != 'not' ? logic : item.notetype">
          {{ (logic && item.notetype != "not" ? logic : item.notetype).toUpperCase() }}
        </span> -->

        <span class="left-arrow">(</span>
        <condition-result :list="item.children" :is-root="false" :logic="item.notetype" :filterModel="filterModelParent"></condition-result>
        <span v-if="logic == 'or' && item.notetype == 'not'">)</span>
        )
      </template>

      <template v-else-if="item.notetype == 'condition'">
        <el-popover
          v-if="item.name == 'filter'"
          :placement="$wujie ? 'bottom-start' : 'top-start'"
          title="过滤器"
          trigger="hover"
          :width="filterModelParent == 1 ? '150px' : '300px'"
          @show="hideRestIcon"
        >
          <template v-if="filterModelParent == 1"> {{ item.valueText }} </template>
          <template v-else>
            <condition-result :list="item.filterOptions" :is-root="false" :filterModel="1" class="filter-options"></condition-result>
          </template>
          <template #reference>
            <span>
              <span class="logic-icon filter" :class="logic == 'or' ? 'or' : 'and'"> {{ (logic == "or" ? "or" : "and").toUpperCase() }} </span>
              <span class="filter-options margin-right10">
                <template v-if="filterModelParent == 1">
                  <condition-result :list="item.filterOptions" :is-root="false" :filterModel="filterModelParent"></condition-result>
                </template>
                <template v-else> 过滤器: {{ item.valueText }} </template>
              </span>
            </span>
          </template>
        </el-popover>

        <span v-else>
          <span class="logic-icon" :class="logic == 'or' ? 'or' : 'and'"> {{ (logic == "or" ? "or" : "and").toUpperCase() }} </span>

          <span class="name-text-span">{{ item.data ? item.data.nameText : item.nameText }}</span>
          <span class="operator-text">{{ item.data ? item.data.operatorText : item.operatorText }}</span>

          <!-- 新增 -  引用资源类型详情展示 -->
          <!-- 触发条件：  operatorText = '引用资源'  or  operator = 'refSource'  -->
          <span v-if="(item.data && item.data.operator === 'refSource') || item.operator === 'refSource'" class="filter-options">
            <RefSourceDetail :detail="item" :isShow="filterShowModel === 1" />
          </span>
          <!-- 引用规则 -->
          <span v-if="(item.data && item.data.operator === 'refRule') || item.operator === 'refRule'" class="filter-options">
            {{ item.data ? item.data.valueText : item.valueText }}:
            <span class="operator-text">{{ item.data ? item.data.refOperatorText : item.refOperatorText }}</span>
            <span>{{ item.data ? item.data.refNameText : item.refNameText }}</span>
          </span>

          <span v-else>
            {{ item.data ? item.data.valueText : item.valueText }}
            <!-- <span class="operator-text" v-if="(item.data && item.data.operator == 'isnull') || item.operator == 'isnull'"> 为空</span> -->
            <!-- <span class="operator-text" v-if="(item.data && item.data.operator == 'notnull') || item.operator == 'notnull'"> 不为空</span> -->
          </span>
        </span>
      </template>
    </span>
  </div>
</template>
<script setup>
import { watch, onUpdated, onMounted, ref, computed } from "vue";
import conditionResult from "./conditionResult.vue";
import RefSourceDetail from "./refSourceDetail.vue";

import { getSourceInfo } from "@/api/sime/config/filter";
import { useRoute } from "vue-router";
import { debounce } from "lodash";

const route = useRoute();

let props = defineProps({
  list: {
    type: Array,
    default: () => {
      return [];
    },
  },
  isRoot: {
    type: Boolean,
    default: true,
  },
  /* 是够显示眼睛 */
  isShowEye: {
    type: Boolean,
    default: true,
  },
  //链接逻辑符
  logic: {
    type: String,
    default: "and",
  },
  filterModel: {
    type: Number,
    default: 2,
  },
});

//初始列表展示过滤器条件
for (let item of props.list) {
  if (item.name == "filter" && item.value) {
    getSourceInfoFn(item);
  }
}
//新增过滤器展示条件
let showList = ref(true);
let oldList = [];
watch(
  () => props.list,
  debounce((val, oldVal) => {
    showList.value = false;
    for (let item of val) {
      if (item.name == "filter" && item.value) {
        const valueId = item.value;
        console.log(oldList);
        const selectedItem = oldList.find((item) => item.value === valueId);

        if (selectedItem && selectedItem.filterOptions && !item.filterOptions) {
          item.filterOptions = selectedItem.filterOptions;
        } else if (!item.filterOptions) {
          getSourceInfoFn(item);
        }
      }
    }
    showList.value = true;
  }, 500),
  { deep: true }
);

function getSourceInfoFn(item) {
  getSourceInfo(item.value).then(({ data }) => {
    item.filterOptions = data && data.conditionsObject ? data.conditionsObject.children : [];
    oldList = JSON.parse(JSON.stringify(props.list));
  });
}

onUpdated(() => {
  hideFirstAnd();
  hideRestIcon();
});
onMounted(() => {
  hideFirstAnd();
  hideRestIcon();
});
function hideFirstAnd() {
  let firstAnd = document.querySelector(".is-root .and:not(.left-arrow + .condition-result-wrapper .logic-icon)");

  if (firstAnd) {
    firstAnd.style.display = "none";
  }
}
let resultRef = ref();

function hideRestIcon() {
  //隐藏左括号后的所有逻辑符号
  let restIcon = resultRef.value.querySelector(".left-arrow + .condition-result-wrapper .logic-icon:not(.not)");

  if (restIcon) {
    restIcon.style.display = "none";
  }

  //隐藏过滤器的第一个and符号
  let filterFirstAnd = resultRef.value.querySelector(".option-item:first-of-type > .and:first-of-type");
  if (filterFirstAnd) {
    filterFirstAnd.style.display = "none";
  }
  //过滤器第一个符号
  let filterOptions = document.querySelectorAll(".filter-options");

  filterOptions.forEach((item) => {
    let firstLogic = item.querySelector(".logic-icon");
    if (firstLogic) {
      firstLogic.style.display = "none";
    }
  });
}

let filterShowModel = ref(2); // 1 显示条件 2 显示名称
function changeFilterShow() {
  filterShowModel.value = filterShowModel.value == 1 ? 2 : 1;
}

let filterModelParent = computed(() => {
  if (route.name != "Analysis") {
    return props.isRoot ? filterShowModel.value : props.filterModel;
  } else {
    return 1;
  }
});
</script>

<style lang="scss" scoped>
.condition-result-wrapper {
  display: inline;
  position: relative;
  line-height: 24px;
  &.is-root {
    display: block;
    background: #fff;
    // line-height: 20px;
    padding: 10px;
    border-radius: $radiusS;
  }
}

.operator-text {
  margin: 0 2px 0 2px;
  color: $color;
  & + span {
    word-break: break-all;
  }
}
.filter-options {
  background: #eaecf0;
  padding: 0 3px 0 4px;
  border-radius: 2px;
}

.logic-icon {
  margin: 0 7px;
  font-size: 15px;
}
.and {
  color: #67c23a;
}
.or {
  color: #409eff;
}
.not {
  color: #f56c6b;
}
.hide {
  display: none !important;
}
.filter-show-change {
  position: absolute;
  right: -18px;
  top: 5px;
  color: #888;
  cursor: pointer;
}
</style>
<style>
.el-popover .filter-options {
  background: #fff;
}
</style>
