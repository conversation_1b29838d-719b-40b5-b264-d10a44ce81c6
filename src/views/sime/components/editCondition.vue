<template>
  <section class="edit-condition-wrapper">
    <p>
      <span class="label-text">类型</span>

      <el-select v-model="name" class="form-item" filterable @change="changeType" placeholder="请选择类型" :teleported="false" @click.stop>
        <el-option v-for="typeItem in typeList" :key="typeItem.field" :label="typeItem.alias" :value="typeItem.field"></el-option>
      </el-select>
    </p>
    <p>
      <span class="label-text">操作</span>
      <el-select v-model="operator" filterable class="form-item" placeholder="请选择操作" @change="changeOperator" @click.stop>
        <el-option v-for="childItem in operatorListHandled" :key="childItem.value" :label="childItem.label" :value="childItem.value"></el-option>
      </el-select>
    </p>
    <p>
      <span class="label-text">条件</span>

      <!-- <el-select
        v-if="operator == 'refRule'"
        class="form-item"
        :disabled="kong"
        v-model="value"
        placeholder="请选择规则"
        filterable
        @change="changeValue('select', $event)"
        @click.stop
      >
        <el-option v-for="(valueItem, valueIndex) in refRuleList" :key="valueIndex" :label="valueItem.label" :value="valueItem.value"></el-option>
      </el-select> -->
      <xel-select-tree
        v-if="operator == 'refRule'"
        ref="treeDeptRef"
        :data="treeList"
        multiple
        v-model="value"
        v-model:text="valueText"
        label="规则"
        pop-width="350px"
        :collapseTag="true"
        :treeProps="{ children: 'children', label: 'name', value: 'id' }"
        @checkChange="changeValue('select', $event, 'rule')"
      >
      </xel-select-tree>
      <el-select
        v-else-if="operator == 'refSource'"
        class="form-item"
        :disabled="kong"
        v-model="value"
        placeholder="请选择条件值"
        filterable
        @change="changeValue('select', $event)"
        @click.stop
      >
        <el-option v-for="(valueItem, valueIndex) in refSourceList" :key="valueIndex" :label="valueItem.label" :value="valueItem.value"></el-option>
      </el-select>
      <el-input
        v-else-if="valueType == 'input'"
        :type="isNumberType(name) ? 'number' : 'input'"
        class="form-item"
        :disabled="kong"
        v-model="value"
        placeholder="请输入条件内容"
        @input="changeValue('input', $event)"
        @click.stop
      ></el-input>
      <el-select
        v-else-if="valueType === 'select'"
        class="form-item"
        :disabled="kong"
        v-model="value"
        placeholder="请选择条件值"
        filterable
        @click.stop
        @change="changeValue('select', $event)"
      >
        <el-option v-for="(valueItem, valueIndex) in valueList" :key="valueIndex" :label="valueItem.label" :value="valueItem.value"></el-option>
      </el-select>

      <el-date-picker
        class="form-item"
        v-else-if="valueType === 'datetimerange'"
        v-model="value"
        :disabled="kong"
        type="datetimerange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="YYYY-MM-DD HH:mm:ss"
        :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
        :popper-options="datePopperOptions"
        @change="changeValue('datetimerange', $event)"
        @click.stop
      >
      </el-date-picker>
      <el-date-picker
        class="form-item"
        v-else-if="valueType === 'datetime'"
        :disabled="kong"
        v-model="value"
        type="datetime"
        format="YYYY-MM-DD HH:mm:ss"
        placeholder="请选择时间"
        :popper-options="datePopperOptions"
        @change="changeValue('time', $event)"
        @click.stop
      >
      </el-date-picker>
    </p>
    <template v-if="operator == 'refRule'">
      <p>
        <span class="label-text">操作</span>
        <el-select
          v-model="refOperator"
          filterable
          class="form-item"
          placeholder="请选择操作"
          @change="(val) => changeRefValue('refOperator', val)"
          @click.stop
        >
          <el-option
            v-for="childItem in ruleOperatorListHandled"
            :key="childItem.value"
            :label="childItem.label"
            :value="childItem.value"
          ></el-option>
        </el-select>
      </p>
      <p>
        <span class="label-text">类型</span>
        <el-select
          v-model="refName"
          class="form-item"
          filterable
          placeholder="请选择类型"
          @change="(val) => changeRefValue('refName', val)"
          :teleported="false"
          @click.stop
        >
          <el-option v-for="typeItem in typeList" :key="typeItem.field" :label="typeItem.alias" :value="typeItem.field"></el-option>
        </el-select>
      </p>
    </template>
  </section>
</template>
<script setup>
import { ref, watchEffect, nextTick, onMounted, onBeforeUnmount, computed, inject, watch } from "vue";
import { parseTime } from "@/utils/ruoyi";
import { getDictsData } from "@/utils/getDicts";
import { getIpList, getPortList, getCustomList, getTimeList } from "@/api/sime/config/resource";
import { getTreeData } from "@/api/sime/config/batchEngine";
import { useRoute } from "vue-router";
import xelSelectTree from "@/xelComponents/xelSelectTree.vue";

import dicsStore from "@/store/modules/dictsData";
if (!dicsStore.state.numberFlag || !dicsStore.state.stringFlag || !dicsStore.state.ruleFlag) {
  dicsStore.actions.getFilterOperatorNumRes();
  dicsStore.actions.getFilterOperatorStrRes();
  dicsStore.actions.getRuleOperatorRes();
}
const route = useRoute();

let props = defineProps({
  //操作列表数组
  typeList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  //回显数据
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },

  /* 新增 - 隐藏引用资源字段
   * 根据hidField值判断是否包含隐藏字段
   * 当前只针对隐藏引用资源字段
   * */
  hidField: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

let emits = defineEmits(["finish"]);

//类型
let name = ref("");
let nameText = ref("");

//操作
let operatorList = ref([]);
let operator = ref("");
let operatorText = ref("");
let kong = ref(false);

let operatorListHandled = computed(() => {
  /*if (route.name == "Analysis") {
    return operatorList.value.filter((item) => item.value != "refSource");
  } else {
    return operatorList.value;
  }*/
  // ruleIndexId === undefined 非分析规则页面，不显示“引用规则”
  // if (ruleIndexId === undefined) {
  //   return operatorList.value.filter((item) => item.value !== "refRule");
  // }
  return operatorList.value;
});

//值
let valueType = ref("input");
let valueList = ref([]);
let value = ref("");
let valueText = ref("");
let content = ref("");
let nameType = ref("");
const numberOperatorsList = computed(() => dicsStore.state.numberOperators);
const stringOperatorsList = computed(() => dicsStore.state.stringOperators);
const ruleOperatorsObj = computed(() => dicsStore.state.ruleOperatorObj);

onMounted(() => {
  resetData();
  let val = props.data;

  //回显数据
  if (val.name) {
    changeType(val.name).then(async () => {
      await changeOperator(val.operator);

      if (val.operator == "refSource") {
        changeValue("select", val.value);
      } else if (val.operator == "refRule") {
        changeValue("select", val.value, "refRule", val.valueText); //checkRule
      } else {
        changeValue(val.valueType, val.value);
      }
      echoData(val);
    });
  }
});

//全部填写完成后，emit(finish) 参数：true 三个值
let effect = watchEffect(() => {
  let flag = false;

  //防止多次触发
  if (flag) return;
  flag = true;
  setTimeout(() => {
    flag = false;
  }, 300);
  if (name.value && operator.value) {
    nextTick(() => {
      emits("finish", true, {
        name: name.value,
        operator: operator.value,
        value: value.value,
        nameText: nameText.value,
        operatorText: operatorText.value,
        valueText: valueText.value,
        operatorList: operatorList.value,
        valueType: valueType.value,
        valueList: valueList.value,
        kong: kong.value,
        content: content.value,
        nameType: nameType.value,
        refOperator: refOperator.value,
        refOperatorText: refOperatorText.value,
        refName: refName.value,
        refNameText: refNameText.value,
      });
    });
  } else {
    nextTick(() => {
      emits("finish", false, { name: name.value });
    });
  }
});
onBeforeUnmount(() => {});

let activeType = ref("");
let hisType = ref("");
let hisValue = ref("");
let selectTypeValue = ref();
async function changeType(val) {
  name.value = val;
  let selectType = props.typeList.find((item) => item.field == val);

  if (!selectType) return;

  /*resetData(false);*/

  let field = val;
  nameText.value = selectType.alias;
  let str = field.substr(0, 1);
  let str2 = field.substr(field.length - 4);

  /* 是否隐藏引用资源字段 */
  let isHid = false;
  props.hidField.forEach((item) => {
    if (field.indexOf(item) === 1) {
      isHid = true;
    }
  });

  //操作选项
  let res = [];
  if (str === "d" || str === "l" || str === "i") {
    activeType.value = "config_filterOperator_number";
    res = numberOperatorsList.value;
  } else {
    activeType.value = "config_filterOperator_string";
    res = stringOperatorsList.value;
  }

  /* 如何包含，隐藏引用资源字段 */
  if (isHid) {
    res = res.filter((item) => {
      return item.value !== "refSource";
    });
  }
  operatorList.value = res;

  //条件选项
  if (selectType.dictDataList && selectType.dictDataList.length > 0) {
    selectTypeValue.value = 1;
    valueList.value = selectType.dictDataList.map((item) => {
      return {
        label: item.dictLabel,
        value: item.dictValue,
      };
    });
    valueType.value = "select";
  } else if (selectType.dictionaryType) {
    selectTypeValue.value = 2;
    let res = await getDictsData(selectType.dictionaryType);
    valueList.value = res;
    valueType.value = "select";
  } else {
    selectTypeValue.value = 3;
    if (str2 === "time") {
      if (valueType.value === "datetimerange") {
        valueType.value = "datetimerange";
      } else {
        valueType.value = "datetime";
      }
    } else {
      valueType.value = "input";
    }
  }

  /*
   * 判断是否需要重置
   * 1、根据字典判断 - 字典改变即重置
   * hisType：历史字典值
   * activeType：当前字典值
   *
   * 2、根据条件输入类型
   * hisValue：历史类型
   *
   * 3、条件 - 选择值发生变化
   * selectTypeValue ！=== 3
   * */

  if (hisType.value !== activeType.value || hisValue.value !== valueType.value) {
    resetData(false, false);
  }
  if (selectTypeValue.value !== 3) {
    value.value = "";
    valueText.value = "";
  }
  hisType.value = activeType.value;
  hisValue.value = valueType.value;

  emitVal();
  return true;
}
watch(
  () => operator.value,
  (val, old) => {
    //切换规则和引用资源时，清空条件值
    if (old === "refRule" || old === "refSource" || val == "refRule" || val == "refSource") {
      value.value = ""; //操作和条件值互相独立，更改不影响
      valueText.value = "";
    }
  }
);

// 操作切换
async function changeOperator(val) {
  let selectOperator = operatorList.value.find((item) => item.value == val);

  if (!selectOperator) return;
  operator.value = val;
  operatorText.value = selectOperator.label;
  content.value = "";
  nameType.value = "";
  if (val === "isnull" || val === "notnull") {
    kong.value = true;
  } else {
    kong.value = false;
  }
  if (valueType.value === "datetime" || valueType.value === "datetimerange") {
    if (selectOperator.label === "属于" || selectOperator.label === "不属于") {
      valueType.value = "datetimerange";
    } else {
      valueType.value = "datetime";
    }
  }

  if (valueType.value == "select") {
    operatorText.value = selectOperator.label;
    operator.value = selectOperator.value;
  }

  /* 初始化一个历史记录 */
  if (hisValue.value) {
    hisValue.value = valueType.value;
  }
  //操作选择引用资源，条件值选择
  if (val == "refSource") {
    await getRefSourceList();
  }
  //操作选择引用规则，规则选择
  if (val == "refRule") {
    await getRefRuleList();
  }

  return true;
}

//获取资源引用列表
let refSourceList = ref([]);
async function getRefSourceList() {
  let api = null;
  refSourceList.value = [];
  if (name.value.includes("time")) {
    api = getTimeList;
    nameType.value = "time";
  } else if (name.value.endsWith("ip")) {
    api = getIpList;
    nameType.value = "ip";
  } else if (name.value.includes("port")) {
    api = getPortList;
    nameType.value = "port";
  } else {
    api = getCustomList;
    nameType.value = "other";
  }
  let res = await api();
  refSourceList.value = res.data.rows.map((item) => {
    return { value: item.id, label: item.name, content: item.content, nameType: nameType.value };
  });
}

function emitVal() {
  nextTick(() => {
    emits("finish", true, {
      name: name.value,
      operator: operator.value,
      value: value.value,
      nameText: nameText.value,
      operatorText: operatorText.value,
      valueText: valueText.value,
      operatorList: operatorList.value,
      valueType: valueType.value,
      valueList: valueList.value,
      kong: kong.value,
      content: content.value,
      nameType: nameType.value,
      refOperator: refOperator.value,
      refOperatorText: refOperatorText.value,
      refName: refName.value,
      refNameText: refNameText.value,
    });
  });
}
function changeValue(valuetype, val, type) {
  let idList = []; //分析规则id列表
  let nameList = []; //分析规则名称列表
  switch (valuetype) {
    case "input":
      valueText.value = val;
      emitVal();
      break;
    case "select":
      if (type) {
        //引用规则，多选
        if (type == "rule") {
          let checkedNodes = JSON.parse(JSON.stringify(val));
          if (checkedNodes && checkedNodes.length > 0) {
            checkedNodes.forEach((item) => {
              if (!item.isGroupNode) {
                idList.push(item.id);
                nameList.push(item.name);
              }
            });
            value.value = idList;
            valueText.value = nameList.join(",");
          } else {
            value.value = "";
            valueText.value = "";
          }
        }
      }
      changeSelectValue(val);
      emitVal();
      break;
    case "datetimerange":
      chagedatetimerange(val);
      emitVal();
      break;
    case "time":
      nextTick(() => {
        value.value = value.value.getTime();
      });
      valueText.value = parseTime(val);
      emitVal();
      break;
  }
}
//value 下拉框改变
function changeSelectValue(val) {
  if (operator.value == "refSource") {
    if (refSourceList.value.length > 0) {
      let selectValue = refSourceList.value.find((item) => item.value == val);
      valueText.value = selectValue.label;
      content.value = selectValue.content;
      nameType.value = selectValue.nameType;
    }
  } else if (operator.value == "refRule") {
    console.log("无操作");
  } else {
    if (valueList.value.length > 0) {
      let selectValue = valueList.value.find((item) => item.value == val);
      valueText.value = selectValue.label;
    }
  }
}

// 二次条件中时间单位选择
function chagedatetimerange(val) {
  //todo 时间格式错误

  if (val.length > 0) {
    valueText.value = parseTime(val[0]) + "-" + parseTime(val[1]);
    nextTick(() => {
      if (!parseInt(val[0])) {
        value.value = [val[0].getTime(), val[1].getTime()];
      }
    });
  } else {
    valueText.value = "";
  }
}

//清空值
/* updata 清空时 是否清空特殊字段 */
function resetData(resetName = true, updata = true) {
  if (resetName) name.value = "";
  if (updata) {
    operatorList.value = [];
    valueType.value = "input";
    nameText.value = "";
  }
  /*operatorList.value = [];*/
  operator.value = "";
  operatorText.value = "";
  /*valueType.value = "input";*/
  value.value = "";
  valueText.value = "";
  kong.value = false;
  refOperator.value = "";
  refOperatorText.value = "";
  refName.value = "";
  refNameText.value = "";
}

function echoData(data) {
  name.value = data.name;
  nameText.value = data.nameText;
  operator.value = data.operator;
  operatorText.value = data.operatorText;
  value.value = data.value;
  valueText.value = data.valueText;
  refOperator.value = data.refOperator;
  refOperatorText.value = data.refOperatorText;

  refName.value = data.refName;
  refNameText.value = data.refNameText;
}
function isNumberType(name) {
  return name && (name.startsWith("i") || name.startsWith("l") || name.startsWith("d")) && !operatorText.value.includes("属于");
}

//弹出框的位置

const datePopperOptions = window.$wujie
  ? {
      modifiers: [
        {
          name: "flip",
          options: {
            fallbackPlacements: ["left-start"],
            allowedAutoPlacements: ["left-start"],
          },
        },
      ],
      flip: "left-start",
    }
  : {};

/*******引用规则*********/
const ruleIndexId = inject("ruleIndexId"); //分析规则页面provide索引
//获取规则引用列表
// let refRuleList = ref([]);
const treeList = ref([]);
async function getRefRuleList() {
  let type = null;
  console.log('(ruleIndexId.value ?? "") == "": ', ruleIndexId?.value);
  // if ((ruleIndexId?.value ?? "") == "") {
  //   treeList.value = [];
  //   // refRuleList.value = [];
  //   return;
  // }
  //
  if (name.value.startsWith("c")) {
    type = "ruleString";
  } else if (name.value.startsWith("i") || name.value.startsWith("l") || name.value.startsWith("d") || name.value.endsWith("time")) {
    type = "ruleNum";
  }
  getRuleOperatorList(type);
  let res1 = await getTreeData();
  treeList.value = res1.data; //disabled: 'disabled',
  // let res = await getAnalysisRuleList({ indexId: ruleIndexId.value });
  // refRuleList.value = res.data.map((item) => {
  //   return {
  //     value: item.ruleId,
  //     label: item.name,
  //   };
  // });
}
// 监听规则索引变化，获取规则引用列表，如果索引为空则清空列表
watch(
  () => ruleIndexId?.value,
  (val, oldVal) => {
    if ((val ?? "") != "") {
      getRefRuleList();
    } else if (oldVal) {
      // refRuleList.value = [];
      treeList.value = [];
    }
  }
);

// 编辑表单绑定的规则操作符和字段名称
const refOperator = ref("");
const refOperatorText = ref("");

const ruleOperatorList = ref([]);
const ruleOperatorListHandled = computed(() => {
  return ruleOperatorList.value;
});
// 获取规则操作符列表
async function getRuleOperatorList(type) {
  ruleOperatorList.value = ruleOperatorsObj.value[type]; //config_refRule_operator
}
const refName = ref("");
const refNameText = ref("");

// 规则操作符和字段名称改变时emit
function changeRefValue(keyName, value) {
  if (keyName == "refOperator") {
    refOperator.value = value;
    const selectedItem = ruleOperatorList.value.find((item) => item.value == value);
    if (selectedItem) {
      refOperatorText.value = selectedItem.label;
    }
  } else if (keyName == "refName") {
    refName.value = value;
    const selectedItem = props.typeList.find((item) => item.field == value);

    if (selectedItem) {
      refNameText.value = selectedItem.alias;
    }
  }
  emitVal();
}
</script>

<style lang="scss" scoped>
:deep(.form-item) {
  width: calc(100% - 40px);
  .el-input__inner {
    padding-left: 10px;
  }
}
:deep(.custom-select) {
  width: calc(100% - 15px);
  background-color: #fff;
  height: 32px;
  top: 2px;
  overflow: hidden;
}
</style>
