<template>
  <!-- 抽离 - 引用资源 - 详情展示组件 -->
  <el-popover placement="top-start" title="引用资源" :width="isShow ? '200px' : '300px'" trigger="hover">
    <template #default>
      <span v-if="!isShow">{{ tabData.join("、") }}</span>
      <span v-else>{{ detail.data ? detail.data.valueText : detail.valueText }}</span>
    </template>
    <template #reference>
      <span class="filter-options">
        引用资源：
        <span v-if="isShow">{{ tabData.join("、") }}</span>
        <span v-else>{{ detail.data ? detail.data.valueText : detail.valueText }}</span>
      </span>
    </template>
  </el-popover>
</template>

<script setup>
/*
 * 引用资源 - 详情展示组件
 * ** 引用资源相关内容在此调整
 * detail：所需数据项
 * isShow：切换显示状态 - 对应父级 filterShowModel === 1 ?
 * */
import { ref, watch } from "vue";

let props = defineProps({
  /* 数据格式 */
  detail: {
    type: Object,
    default: null,
  },

  /* isShow - 是否是展开 */
  isShow: {
    type: Boolean,
    default: false,
  },
});

/* 表格最终展示数据 */
const tabData = ref([]);

/* 数据监听 - 格式处理 */
watch(
  () => props.detail,
  (val) => {
    /*
     * 数据格式 - 增加类型 type，time格式与其他类型处理方式不同，单独处理
     * */
    let type = val.data ? val.data.nameType : val.nameType;
    let content = val.data ? val.data.content : val.content;

    if (content) {
      let arr = [];
      if (type === "time") {
        arr = JSON.parse(content);
      } else {
        arr = content.split(";");
      }
      let result = arr.map((item, index) => {
        return type === "time" ? formateTime(item) : item;
      });
      tabData.value = result;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

/* 原copy - 时间格式处理 */
function formateTime(data) {
  let name = "";
  let start = "";
  let end = "";
  let stime = "";
  let etime = "";
  let operator = data.operator == "nequal" ? "!" : "";

  switch (data.type) {
    case "year":
      name = "每年";
      start = data.start;
      end = data.end;
      stime = data.stime;
      etime = data.etime;
      break;
    case "month":
      name = "每月";
      start = data.start;
      end = data.end;
      stime = data.stime;
      etime = data.etime;
      break;
    case "week":
      name = "每周";
      start = data.start;
      end = data.end;
      stime = data.stime;
      etime = data.etime;
      break;
    case "day":
      name = "每天";
      start = data.stime;
      end = data.etime;
      break;
    case "time":
      name = "时间段";
      start = data.stime;
      end = data.etime;
      break;
  }
  let result = operator + " " + start + " 至 " + end;
  let result2 = operator + " " + name + " " + start + " 至 " + end;
  let result3 = operator + " " + name + " " + start + " 至 " + end + " 的 每天" + stime + " 至 " + etime;

  return data.type == "time" ? result : data.type == "day" ? result2 : result3;
}
</script>

<style scoped></style>
