import { ElMessageBox, ElMessage } from "element-plus";
import { h } from "vue";
import store from "@/store";
import router from "@/router";
import { restartReceiver } from "@/api/sime/config/logAccept.js";
export default function backVersion(backApi, id, versionId, reload) {
  ElMessageBox.confirm(`是否确认回滚到此版本？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let params = {
      id, //: route.params.id,
      versionId, //: row.id,
    };

    backApi(params).then(() => {
      window.location.href.includes("simeConfig/parseVersion") ? "" : ElMessage.success("操作成功！");

      //   tableRef.value.reload();
      if (reload) {
        reload();
      } else {
        store.commit("closeCurrentTab");

        let name = window.location.href.includes("simeConfig/filter")
          ? "FilterVersion"
          : window.location.href.includes("simeConfig/parseVersionDetail")
          ? "ParseVersion"
          : "RuleVersion";
        if (name == "ParseVersion") {
          isStart(name, id);
        } else {
          router.push({
            name,
            params: { id },
          });
        }
      }
    });
  });
}

function isStart(name, id) {
  ElMessageBox({
    title: "警告",
    message: h("p", null, [h("p", null, "是否立即启用"), h("p", { style: "color: #E6A23C" }, "注意：选择否，则需要手动重启监听器")]),
    showCancelButton: true,
    confirmButtonText: "是",
    cancelButtonText: "否",
    showClose: false,
    closeOnClickModal: false,
  })
    .then(() => {
      let resetParams = {
        parseRuleList: [{ ruleId: id }],
      };
      restartReceiver(resetParams)
        .then((res) => {})
        .finally(() => {
          store.commit("closeCurrentTab");
          //接口待定
          router.push({
            name,
            params: { id },
          });
        });
    })
    .catch(() => {
      router.push({
        name,
        params: { id },
      });
      store.commit("closeCurrentTab");
    });
}
