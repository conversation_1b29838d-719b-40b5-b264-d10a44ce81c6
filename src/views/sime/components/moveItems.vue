<!-- 移动 -->
<template>
  <xel-select-tree
    v-if="listSelf.length"
    :data="listSelf"
    :tree-props="{ children: 'children', label: 'name' }"
    :multiple="false"
    pop-width="300px"
    @nodeClick="nodeClick"
  >
    <el-button
      class="search-button"
      :disabled="multipleSelection.length == 0"
      v-hasPermi="
        router.currentRoute.name == 'Filter'
          ? 'config:queryFilter:update'
          : router.currentRoute.name == 'AnalyticRule'
          ? 'config:parseRule:move'
          : router.currentRoute.name == 'LogPermisson'
          ? 'config:logPermission:update'
          : 'config:analysisRule:update'
      "
    >
      <el-icon :size="12">
        <right />
      </el-icon>
      移动
    </el-button>
    <template #remark>
      <div class="color-soft margin-bottom10">请选择要移动至的{{ title }}</div>
    </template>
  </xel-select-tree>
</template>
<script setup>
import { ref, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const router = useRouter();
let props = defineProps({
  // 过滤、规则分组
  title: {
    type: String,
    default: "过滤器分组",
  },
  //下拉树的分组列表
  list: {
    type: Array,
    default: () => {
      return [];
    },
  },
  //选中数据
  multipleSelection: {
    type: Array,
    default() {
      return [];
    },
  },
  //移动api
  moveApi: {
    type: Function,
    required: true,
  },
  //选中数据id
  idKey: {
    type: String,
    default: "filterId",
  },
  currentGroupId: {
    type: String,
    default: "",
  },

  /* 增加 - 关键字 */
  apiKey: {
    type: String,
    default() {
      return "";
    },
  },
});

let emits = defineEmits(["update"]);

let listSelf = ref([]);

let watcher = watch(
  () => props.list,
  (val) => {
    if (val.length) {
      listSelf.value = handlerList(props.list[0].children);
      /*watcher();*/
    }
  },
  { deep: true }
);

//处理分组数据  只保留分组，去掉isDataNode属性
function handlerList(list) {
  let arr = [];
  let copyList = JSON.parse(JSON.stringify(list));
  for (let item of copyList) {
    if (!item.isDataNode) {
      delete item.isDataNode;
      arr.push(item);
    }
    if (item.children) {
      item.children = handlerList(item.children);
    }
  }
  return arr;
}
function nodeClick(data) {
  ElMessageBox.confirm(`是否确认将选中数据移动至分组 ${data.name}？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let idsStr = props.multipleSelection.map((item) => item[props.idKey]).join();
    let params = {
      groupId: data.id,
    };

    /* api 参数名称 */
    if (props.apiKey) {
      params[props.apiKey] = idsStr;
    } else {
      if (props.idKey == "id") {
        params.ids = idsStr;
      } else if (props.idKey == "ruleId") {
        params.ruleIds = idsStr;
      } else {
        params.queryFilterIds = idsStr;
      }
    }
    props.moveApi(params).then(() => {
      ElMessage.success("操作成功");
      emits("update");
    });
  });
}
</script>

<style lang="scss" scoped></style>
