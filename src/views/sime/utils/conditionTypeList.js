import { ref, nextTick, watch } from "vue";
import { filterIndexIdNames } from "@/api/sime/search/filter";
import { queryFilterSameIndex } from "@/api/sime/config/filter";
import { ElMessageBox } from "element-plus";

export default function conditionTypeList(state, editId, editable = true, isFilter = true, clearCondition = null) {
  let filterListSameIndexId = ref();

  let typeList = ref([]);
  let showCondition = ref(false);
  function getFilterTypeList(val) {
    showCondition.value = false;
    filterIndexIdNames(val).then(({ data }) => {
      typeList.value = data;
      nextTick(() => {
        showCondition.value = true;
      });
    });
    let params = {
      indexId: val,
    };

    if (isFilter) {
      params.filterId = editId.value;
    }
    //获取相同索引的过滤器
    queryFilterSameIndex(params).then(({ data }) => {
      filterListSameIndexId.value = data;
    });
  }

  //条件相关
  let lastIndexId = ""; //保存上次选中的索引表
  if (editable) {
    watch(
      () => state.formData.indexId,
      (val, oldVal) => {
        nextTick(() => {
          //新增选中
          if (val && oldVal == "") {
            getFilterTypeList(val, editId.value); //新增，获取选中的索引表操作
            lastIndexId = val;
          } else if (val && oldVal) {
            //切换索引表
            lastIndexId = oldVal;
          } else {
            lastIndexId == "";
          }
        });
      }
    );
  }

  //同一个过滤器下切换索引表
  function confirmIndexId() {
    if (lastIndexId == "") {
      return;
    }
    getFilterTypeList(state.formData.indexId, editId.value);
    return;
    // ElMessageBox.confirm("切换索引表会清空此过滤器的条件，确认切换?", "警告", {
    //   distinguishCancelAndClose: true,
    //   confirmButtonText: "确定",
    //   cancelButtonText: "取消",
    //   type: "warning",
    // })
    //   .then(() => {
    //     clearCondition && clearCondition();
    //     getFilterTypeList(state.formData.indexId, editId.value);
    //   })
    //   .catch(() => {
    //     state.formData.indexId = lastIndexId;
    //   });
  }

  function emptyLastIndexId() {
    lastIndexId = "";
  }

  return {
    filterListSameIndexId,
    typeList,
    showCondition,
    getFilterTypeList,
    confirmIndexId,
    emptyLastIndexId,
  };
}
