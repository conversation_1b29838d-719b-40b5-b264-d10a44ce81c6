//复制，移动，删除
import { ref, h } from "vue";

import { batchDelete } from "@/utils/delete";

import { ElMessageBox, ElMessage } from "element-plus";
import { useStore } from "vuex";
import store from "@/store/index";
import { getReceiverRoute, restartReceiver } from "@/api/sime/config/logAcceptState";
export default function (delItem, search, updateGroup, copyItem, idKey, router) {
  const routerName = router.currentRoute._value.name;

  const btnList = [
    {
      icon: "document-copy",
      title: "复制",
      hasPermi: routerName == "Filter" ? "config:queryFilter:add" : routerName == "AnalyticRule" ? "config:parseRule:add" : "config:analysisRule:add",
      onClick(scope) {
        copyItem(scope.row);
      },
    },

    {
      icon: "delete",
      title: "删除",
      hasPermi:
        routerName == "Filter"
          ? "config:queryFilter:delete"
          : routerName == "AnalyticRule"
          ? "config:parseRule:delete"
          : "config:analysisRule:delete",
      onClick(scope) {
        delFn([scope.row]);
      },
    },
    {
      icon: "operation",
      title: "历史版本",
      hasPermi:
        routerName == "Filter"
          ? "queryFilter:version:list"
          : routerName == "AnalyticRule"
          ? "config:parseRuleVersion:list"
          : "config:analysisRuleVersion:list",
      onClick({ row }) {
        let name = idKey == "parseId" ? "ParseVersion" : idKey == "ruleId" ? "RuleVersion" : "FilterVersion";
        router.push({ name, params: { id: idKey == "parseId" ? row["id"] : row[idKey] } });
      },
    },
  ];

  //删除，批量删除
  function delFn(rows) {
    console.log("rows: ", rows);
    batchDelete().then(() => {
      let ids = rows.map((item) => (idKey == "parseId" ? item["id"] : item[idKey]));

      delItem(ids.join()).then(() => {
        routerName == "AnalyticRule" ? "" : ElMessage.success("删除成功");
        //删除打开的tab编辑页面
        ids.forEach((item) => {
          store.commit("closeTabById", item);
        });

        if (routerName == "AnalyticRule") {
          isStart(ids);
        } else {
          search(false);
          updateGroup();
        }
      });
    });
  }

  //移动
  let multipleSelection = ref([]);
  function handleSelectionChange(val) {
    multipleSelection.value = val;
  }

  let str = ref("");
  function isStart(data) {
    let ruleIds = data.map((item) => {
      return {
        ruleId: item,
      };
    });
    ElMessageBox({
      title: "警告",
      message: h("p", null, [h("p", null, "是否立即启用"), h("p", { style: "color: #E6A23C" }, "注意：选择否，则需要手动重启监听器")]),
      showCancelButton: true,
      confirmButtonText: "是",
      cancelButtonText: "否",
      closeOnClickModal: false,
      showClose: false,
    })
      .then(() => {
        let resetParams = {
          parseRuleList: ruleIds,
        };

        /* 获取采集器 list - 遍历path */
        getReceiverRoute()
          .then((res) => {
            let sum = 0;
            str.value = "";
            res.data.forEach((item, index) => {
              /* 循环重启监听器
               * 成功：拼接成功 msg
               * 失败：拼接失败 msg
               * */
              restartReceiver(resetParams, item.path)
                .then((val) => {
                  str.value += `<p>${item.name}: ${val.msg}</p>`;
                })
                .catch((err) => {
                  str.value += `<p>${item.name}: ${err.msg}</p>`;
                })
                .finally((fval) => {
                  sum += 1;
                  if (res.data.length === sum && str.value) {
                    /* 提示信息 */
                    ElMessageBox.alert("<div style='max-height:400px;overflow:auto;'>" + str.value + "</div>", "提示", {
                      confirmButtonText: "关闭",
                      dangerouslyUseHTMLString: true,
                    });
                  }
                });
            });
          })
          .then(() => {
            /* 执行完毕 */
            search(false);
            updateGroup();
          });
      })
      .catch(() => {
        search(false);
        updateGroup();
      }); //路由跳转});
  }

  return { btnList, multipleSelection, handleSelectionChange };
}
