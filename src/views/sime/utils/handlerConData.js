//后台数据，处理为页面条件编辑组件需要的格式

import { getDictsData } from "@/utils/getDicts";

export default function handlerConData(filter, type_list) {
  let str = filter.name.substr(0, 1);
  let str2 = filter.name.substr(filter.name.length - 4);
  let dictName = "";
  if (str === "d" || str === "l" || str === "i") {
    dictName = "config_filterOperator_number";
  } else {
    dictName = "config_filterOperator_string";
  }
  let chose = {};
  let valueType = "";
  let kong = false;
  let value = filter.value;

  type_list.forEach((type) => {
    if (filter.name === type.field) {
      chose = type;
    }
  });
  if (chose.dictionaryType) {
    valueType = "select";
  } else {
    if (str2 === "time") {
      if (filter.operator === "ins" || filter.operator === "notins") {
        valueType = "datetimerange";

        if (typeof filter.value == "string") {
          value = filter.value.split(",");
        }
      } else {
        valueType = "datetime";
      }
    } else {
      valueType = "input";
    }
  }
  if (filter.operator === "isnull" || filter.operator === "notnull") {
    kong = true;
  }

  return {
    name: filter.name,
    operator: filter.operator,
    value: value,
    nameText: filter.nameText,
    operatorText: filter.operatorText,
    valueText: filter.valueText,
    valueType: valueType,
    kong: kong,
    content: filter.content,
    refOperator: filter.refOperator,
    refOperatorText: filter.refOperatorText,
    refName: filter.refName,
    refNameText: filter.refNameText,
  };
}
