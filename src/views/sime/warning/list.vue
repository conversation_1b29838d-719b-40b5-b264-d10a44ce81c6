<template>
  <!-- 异常警告  -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName">
      <el-tab-pane label="异常警告" name="anomalyWarning">
        <AnomalyWarning />
      </el-tab-pane>
      <el-tab-pane label="历史记录" name="historyAllList">
        <HistoryList type="All" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup>
import { ref } from "vue";
import AnomalyWarning from "./anomalyWarningCom/index.vue";
import HistoryList from "./anomalyWarningCom/historyList.vue";
/* 默认数据项 */
let activeName = ref("anomalyWarning");
</script>
<style scoped></style>
