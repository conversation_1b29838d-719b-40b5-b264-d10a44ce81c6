<template>
  <!-- 异常警告 - 历史记录 - 列表  -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <HistoryList />
    <el-button class="but-right" @click="clickGoBack">返回</el-button>
  </el-card>
</template>

<script setup>
import HistoryList from "./historyList.vue";
import { useRouter } from "vue-router";
const router = useRouter();
function clickGoBack() {
  router.go(-1);
}
</script>

<style scoped></style>
