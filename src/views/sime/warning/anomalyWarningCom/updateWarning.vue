<template>
  <!-- 异常警告 - 配置 -->
  <el-card class="bg-p-border-new">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>

    <el-form :model="formData" ref="ruleFormRef" label-width="140px" size="mini">
      <!-- 基本信息 -->
      <div>
        <p class="title-bottom-line">基本信息</p>
        <el-row :gutter="20">
          <el-col :span="11" v-for="item in formList" :key="item.prop">
            <xel-form-item v-model="formData[item.prop]" v-bind="item"></xel-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 配置参数 -->
      <div>
        <WarningTypeItem ref="WarningTypeItemRef" :itemData="formData" />
      </div>

      <!-- 触发控制 -->
      <div>
        <p class="title-bottom-line">触发控制</p>
        <el-row :gutter="4">
          <el-col :span="1" class="text-center">
            <el-checkbox v-model="formData.isTriggerControl" :true-label="1" :false-label="0" />
          </el-col>

          <el-col :span="20">
            <el-form-item label="启用触发控制" label-width="100px">
              <el-col :span="6">
                <el-input-number
                  controls-position="right"
                  v-model="formData.triggerControlTimeValue"
                  :min="1"
                  :max="999999"
                  :precision="0"
                  placeholder="时间值"
                />
              </el-col>

              <el-col :span="3">
                <xel-form-item
                  label=""
                  v-model="formData.triggerControlTimeType"
                  formType="select"
                  dictName="warning_config_triggerControlTimeType"
                  :sime="true"
                  :filterable="true"
                  placeholder="时间类型"
                />
              </el-col>

              <el-col :span="6">
                <el-input-number
                  controls-position="right"
                  v-model="formData.triggerControlCount"
                  :min="1"
                  :max="999999"
                  :precision="0"
                  placeholder="数量"
                />
                <span class="ml10">条警告</span>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="mt20 text-center">
      <el-button type="primary" @click="submit" :loading="saveLoading">确定</el-button>
      <el-button type="button" @click="cancel">取消</el-button>
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, toRefs } from "vue";
import { getWaringDetail, updateWaring } from "@/api/sime/warning/anomalyWarning";
import { listUser } from "@/api/sime/system/user";
import WarningTypeItem from "./warningTypeItem.vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";

/* 基础数据 */
const route = useRoute();
let state = reactive({
  formData: {
    /* 基本信息 */
    name: "",
    description: "",
    isAvailable: 1,
    warningKeepDays: 1,
    warningConfigUserRels: [],

    /* 触发控制 */
    isTriggerControl: 0,
    triggerControlTimeValue: 0,
    triggerControlTimeType: "",
    triggerControlCount: 0,
  },
  saveLoading: false,
});
let { formData, saveLoading } = toRefs(state);

/* 基本信息 */
let formList = ref([
  {
    formType: "input",
    prop: "name",
    label: "名称",
    size: "mini",
    disabled: true,
    type: "text",
  },
  {
    formType: "input",
    prop: "description",
    label: "描述",
    size: "mini",
    disabled: true,
    type: "text",
  },
  {
    formType: "radio",
    prop: "isAvailable",
    label: "状态",
    dictName: "warning_config_isAvailable",
    isNumber: true,
    sime: true,
    required: true,
  },
  {
    formType: "number",
    prop: "warningKeepDays",
    label: "历史记录储存天数",
    size: "mini",
    required: true,
    min: 0,
    max: 30,
    precision: "0",
  },
  {
    formType: "select",
    prop: "warningConfigUserRels",
    label: "发送通知",
    placeholder: "请选择通知接收人员",
    /*dictName: "warning_config_warningRelay",
    sime: true,*/
    required: false,
    multiple: true,
    filterable: true,
    clearable: true,
    isShow: true,
    options: [],
  },
]);

/* 获取用户 */
const getUserList = () => {
  listUser({ pageNum: 1, pageSize: 10000 }).then(({ data }) => {
    let userData = [];
    data.rows.forEach((item) => {
      userData.push({
        value: item.userId,
        label: item.nickName,
      });
    });
    formList.value.find((item) => item.prop === "warningConfigUserRels").options = userData;
  });
};

/* 获取详情 */
const getDetail = () => {
  getWaringDetail(route.params.id).then((res) => {
    /*
    * 调用用户列表接口，下拉列表，可多选，非必填
      根据 warningRelay 字段判断接口及返回格式
      为空时: 隐藏发送通知
      等于RelayToOutpostHandler时: 调用前哨的用户接
      等于RelayToSiemHandler时: 调用siem的用户接口
      * */
    getUserList();
    /* 根据warningRelay 判断是否显示 */
    if (res.data.warningRelay === null && res.data.warningRelay === "") {
      formList.value.find((item) => item.prop === "warningConfigUserRels").isShow = false;
    } else {
      formList.value.find((item) => item.prop === "warningConfigUserRels").isShow = true;
    }

    /* 回填数据 发送通知 */
    if (res.data.warningConfigUserRels === null) {
      res.data.warningConfigUserRels = [];
    } else {
      res.data.warningConfigUserRels = res.data.warningConfigUserRels.map((item) => item.userId);
    }
    state.formData = res.data;
  });
};
getDetail();

const store = useStore();
const router = useRouter();
/* 确定按钮事件 */
let WarningTypeItemRef = ref();
let ruleFormRef = ref();
const submit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid && triggerFun()) {
      saveLoading.value = true;
      let query = JSON.parse(JSON.stringify(formData.value));
      query.configs = WarningTypeItemRef.value.formData();
      query.warningConfigUserRels = [];
      formData.value.warningConfigUserRels.forEach((item) => {
        query.warningConfigUserRels.push({ userId: item });
      });
      updateWaring(query)
        .then((res) => {
          ElMessage.success("操作成功");
          cancel();
        })
        .catch(() => {
          saveLoading.value = false;
        });
    }
  });
};

/* 触发控制 - 校验 */
const triggerFun = () => {
  let isValid = true;
  if (formData.value.isTriggerControl === 1) {
    if (
      !(formData.value.triggerControlTimeValue >= 0) ||
      formData.value.triggerControlTimeType === null ||
      formData.value.triggerControlTimeType === undefined ||
      formData.value.triggerControlTimeType === "" ||
      !(formData.value.triggerControlCount >= 0)
    ) {
      ElMessage.info("请填写完整触发控制");
      isValid = false;
    }
  }
  return isValid;
};

/* 取消按钮 */
const cancel = () => {
  router.push({ name: "WarningList" });
  store.commit("closeCurrentTab");
};
</script>

<style scoped>
.text-center {
  position: relative;
  top: -2px;
}
</style>
