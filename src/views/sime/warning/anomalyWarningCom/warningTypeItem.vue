<template>
  <!-- 警告配置 - 配置参数 - 组件 -->
  <div>
    <!--    {{ itemData.configs }}
    {{ itemData.warningType }}-->
    <div v-if="itemData.configs !== '{}' && itemData.configs !== ''">
      <p class="title-bottom-line">配置参数</p>
      <el-form :model="formData" ref="formDataRef" label-width="140px" size="mini">
        <!-- 分析规则运行超时 - batch_rule_timeout -->
        <div v-if="itemData.warningType === 'batch_rule_timeout'">
          <el-form-item label="超时时间">
            <el-input-number controls-position="right" v-model="formData.timeoutValue" :min="1" :max="999999" :precision="0" placeholder="超时时间" />
            <span class="ml10">毫秒</span>
          </el-form-item>
        </div>

        <!-- 日志接收流量波动 - receiver_undulate -->
        <div v-else-if="itemData.warningType === 'receiver_undulate'">
          <el-form-item label="监控周期时间">
            <el-input-number
              controls-position="right"
              v-model="formData.timeCycle"
              :min="1"
              :max="999999"
              :precision="0"
              placeholder="监控周期时间"
            />
            <span class="ml10">分钟</span>
          </el-form-item>

          <el-form-item label="日志波动量">
            <el-input-number controls-position="right" v-model="formData.diff" :min="1" :max="999999" :precision="0" placeholder="日志波动量" />
            <span class="ml10">条</span>
          </el-form-item>

          <el-form-item label="监控延迟时间">
            <el-input-number
              controls-position="right"
              v-model="formData.timeDelay"
              :min="1"
              :max="999999"
              :precision="0"
              placeholder="监控延迟时间"
            />
            <span class="ml10">分钟</span>
          </el-form-item>
        </div>

        <!-- 日志接收流量低 - receiver_low_flow -->
        <div v-else-if="itemData.warningType === 'receiver_low_flow'">
          <el-form-item label="监控持续时间">
            <el-input-number
              controls-position="right"
              v-model="formData.timeCycle"
              :min="1"
              :max="999999"
              :precision="0"
              placeholder="监控持续时间"
            />
            <span class="ml10">分钟</span>
          </el-form-item>

          <el-form-item label="最低日志量">
            <el-input-number controls-position="right" v-model="formData.val" :min="1" :precision="0" placeholder="最低日志量" />
            <span class="ml10">条</span>
          </el-form-item>

          <el-form-item label="监控延迟时间">
            <el-input-number
              controls-position="right"
              v-model="formData.timeDelay"
              :min="1"
              :max="999999"
              :precision="0"
              placeholder="监控延迟时间"
              class="mr5"
            />
            <span class="ml10">分钟</span>
          </el-form-item>
        </div>

        <!-- 服务器运行状态 - running_status -->
        <div v-else-if="itemData.warningType === 'running_status'">
          <el-form-item label="cpu使用率">
            <el-input-number controls-position="right" v-model="formData.cpu" :min="1" :max="100" :precision="0" placeholder="cpu使用率" />
            <span class="ml10">%</span>
          </el-form-item>

          <el-form-item label="内存使用率">
            <el-input-number controls-position="right" v-model="formData.ram" :min="1" :max="100" :precision="0" placeholder="内存使用率" />
            <span class="ml10">%</span>
          </el-form-item>

          <el-form-item label="硬盘使用率">
            <el-input-number controls-position="right" v-model="formData.disk" :min="1" :max="100" :precision="0" placeholder="硬盘使用率" />
            <span class="ml10">%</span>
          </el-form-item>

          <el-form-item label="周期间隔">
            <el-input-number controls-position="right" v-model="formData.timeCycle" :min="1" :max="999999" :precision="0" placeholder="周期间隔" />
            <span class="ml10">秒</span>
          </el-form-item>

          <el-form-item label="触发周期数">
            <el-input-number
              controls-position="right"
              v-model="formData.cycle"
              :min="1"
              :max="999999"
              :precision="0"
              placeholder="触发周期数"
              class="mr5"
            />
            <span class="ml10">周期</span>
          </el-form-item>
        </div>

        <!-- 未解析日志 - log_parse_fail -->
        <div v-else-if="itemData.warningType === 'log_parse_fail'">
          <el-form-item label="未解析日志数量">
            <el-input-number controls-position="right" v-model="formData.val" :min="1" :precision="0" placeholder="未解析日志数量" />
            <span class="ml10">条</span>
          </el-form-item>

          <el-form-item label="监控周期时间">
            <el-input-number
              controls-position="right"
              v-model="formData.timeCycle"
              :min="1"
              :max="999999"
              :precision="0"
              placeholder="监控周期时间"
            />
            <span class="ml10">分钟</span>
          </el-form-item>
        </div>

        <!-- 日志接收流量控制 - receiver_traffic 新设备日志接入 - new_device_log -->
        <div v-else-if="itemData.warningType === 'receiver_traffic' || itemData.warningType === 'new_device_log'">
          <el-form-item label="监控周期时间">
            <el-input-number
              controls-position="right"
              v-model="formData.timeCycle"
              :min="1"
              :max="999999"
              :precision="0"
              placeholder="监控周期时间"
            />
            <span class="ml10">分钟</span>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
/*
 * 配置参数 - 组件
 * 分析规则运行超时 - batch_rule_timeout
 * 日志接收流量波动 - receiver_undulate
 * 日志接收流量低 - receiver_low_flow
 * 服务器运行状态 - running_status
 * 未解析日志 - log_parse_fail
 * 日志接收流量控制 - receiver_traffic
 * 新设备日志接入 - new_device_log
 * */
import { ref, watch } from "vue";
/* 参数传递 */
const props = defineProps({
  itemData: {
    type: Object,
    default() {
      return {};
    },
  },
});

/* 默认数据 */
const formData = ref({});
watch(
  () => props.itemData,
  (val) => {
    if (val.configs) {
      formData.value = JSON.parse(val.configs);
    }
  }
);

defineExpose({
  formData: () => {
    return JSON.stringify(formData.value);
  },
});
</script>

<style scoped>
.el-input-number {
  width: 160px;
}
</style>
