<template>
  <!-- 异常警告 - 列表  -->
  <!-- 查询体 -->
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset" />

  <!-- 数据 -->
  <xel-table ref="tableRef" :columns="columns" :load-data="getWaringList">
    <template #status="scope">
      <el-switch v-model="scope.row.isAvailable" :active-value="1" :inactive-value="0" @change="handStatus(scope.row)" />
    </template>
  </xel-table>
</template>

<script setup>
import { reactive, ref } from "vue";
import { getWaringList, updateWaringAvailable } from "@/api/sime/warning/anomalyWarning";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";

/* 基础数据 */
let tableRef = ref();
const router = useRouter();

/* 修改状态 */
const handStatus = (data) => {
  ElMessageBox.confirm(`是否确认${data.isAvailable == 1 ? "启用" : "停用"}该数据项？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(function () {
      let formData = new FormData();
      formData.append("id", data.id);
      formData.append("isAvailable", data.isAvailable);
      updateWaringAvailable(formData)
        .then(() => {
          ElMessage({
            type: "success",
            message: "操作成功",
          });
          search(false);
        })
        .catch(() => {
          data.isAvailable == 1 ? (data.isAvailable = 0) : (data.isAvailable = 1);
        });
    })
    .catch(() => {
      data.isAvailable == 1 ? (data.isAvailable = 0) : (data.isAvailable = 1);
    });
};

/* 查询相关 */
let searchState = reactive({
  data: {
    name: "",
    isAvailable: "",
  },
  menuData: [
    {
      lable: "是否可用：",
      prop: "isAvailable",
      options: [],
      dictName: "warning_config_isAvailable",
      sime: true,
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "名称",
    },
  ],
});
/* 查询 */
const search = (initPage = true) => {
  tableRef.value.reload(searchState.data, initPage);
};
/* 重置 */
const reset = () => {
  searchState.data = {
    name: "",
    isAvailable: "",
  };
  search();
};

/* 表格项 */
const columns = [
  {
    prop: "status",
    label: "",
    width: 100,
    slotName: "status",
  },
  {
    prop: "name",
    label: "名称",
    width: 300,
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "warningKeepDays",
    label: "存储天数",
    width: 200,
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Setting",
        hasPermi: "warning:config:update",
        title: "配置",
        onClick(scope) {
          router.push({
            name: "UpdateWarning",
            params: {
              id: scope.row.id,
            },
          });
        },
      },
      {
        icon: "View",
        hasPermi: "warning:config:view",
        title: "历史记录",
        onClick(scope) {
          router.push({
            name: "HistoryRecords",
            params: {
              warningType: scope.row.warningType,
            },
          });
        },
      },
    ],
  },
];
</script>

<style scoped></style>
