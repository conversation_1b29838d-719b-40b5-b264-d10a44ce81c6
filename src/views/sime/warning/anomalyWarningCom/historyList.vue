<template>
  <!-- 异常警告 - 历史记录 - 列表 (全部历史记录共用)  -->
  <!-- 查询体 -->
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <template #form>
      <xel-form-item
        label="时间范围"
        type="datetimerange"
        form-type="daterange"
        value-format="YYYY-MM-DD HH:mm:ss"
        v-model:start="searchState.startTime"
        v-model:end="searchState.endTime"
      />
    </template>
  </common-search>

  <!-- 数据 -->
  <xel-table ref="tableRef" :columns="columns" :defaultParams="defaultParams" :load-data="getMessageList" />
</template>

<script setup>
import { reactive, ref } from "vue";
import { getMessageList } from "@/api/sime/warning/anomalyWarning";
import { useRoute } from "vue-router";
import { selectDictLabel } from "@/utils/ruoyi2";
import { getDictsData } from "@/utils/getDicts";

const props = defineProps({
  /* 引用类型 */
  type: {
    type: String,
    default: () => {
      return "";
    },
  },
});

/* 获取字典 */
const list = ref([]);
const getPublic = () => {
  getDictsData("warning_config_warningType").then((res) => {
    list.value = res;
  });
};
getPublic();

/* 基础数据 */
let tableRef = ref();
const route = useRoute();

/* 列表参数 */
let defaultParams = {};
/* 判断是不是查询全部 */
if (props.type !== "All") {
  defaultParams.warningType = route.params.warningType;
}

/* 查询相关 */
let searchState = reactive({
  data: {
    description: "",
  },
  startTime: "",
  endTime: "",
  formList: [
    {
      formType: "input",
      prop: "description",
      label: "警告内容",
    },
  ],
});

/* 查询所有历史 - 增加下面查询 */
if (props.type === "All") {
  searchState.formList.push({
    formType: "select",
    prop: "warningType",
    label: "警告类型",
    dictName: "warning_config_warningType",
    sime: true,
  });
}

/* 查询 */
const search = (initPage = true) => {
  tableRef.value.reload(
    {
      ...searchState.data,
      startTime: searchState.startTime,
      endTime: searchState.endTime,
    },
    initPage
  );
};
/* 重置 */
const reset = () => {
  searchState.data = {
    description: "",
  };
  searchState.startTime = "";
  searchState.endTime = "";
  search();
};

/* 表格项 */
const columns = [
  {
    prop: "name",
    label: "警告名称",
    width: "250px",
  },
  {
    prop: "description",
    label: "警告内容",
  },
  {
    prop: "warningType",
    label: "警告类型",
    width: "250px",
    formatter(scope) {
      return selectDictLabel(list.value, scope.warningType);
    },
  },
  {
    prop: "warningTime",
    label: "警告产生时间",
    width: "150px",
  },
];
</script>

<style scoped></style>
