<template>
  <!-- SOSCAN - 界面 -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tabsList" :key="item.id" :label="item.name" :name="item.id">
        <SoscanItem :nodeId="item.id" v-if="item.id === activeName" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script>
export default {
  name: "Soscan-task",
};
</script>

<script setup>
import { onMounted, ref } from "vue";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
import { useStore } from "vuex";

import SoscanItem from "./soscanCom/soscanItem.vue";
/* 默认数据项 */
const store = useStore();

let activeName = ref("");
let tabsList = ref([]);

/* 获取节点 数据 */
const getNodeTypeList = () => {
  getNodeList("soscan").then((res) => {
    activeName.value = res.data.rows[0].id;
    tabsList.value = res.data.rows;
  });
};
getNodeTypeList();
/*onMounted(()=>{
  getNodeTypeList();
});*/
</script>

<style scoped></style>
