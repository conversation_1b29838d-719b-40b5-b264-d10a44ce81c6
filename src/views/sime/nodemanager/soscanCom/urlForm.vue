<template>
  <el-form :model="formData" ref="ruleFormRef" label-width="120px">
    <el-row>
      <el-col :span="21">
        <xel-form-item v-for="item in formList" :key="item.prop" v-model="formData[item.prop]" v-bind="item" />

        <!-- 动态URL -->
        <div v-for="(item, index) in formData.urls" :key="item" class="btnDiv">
          <el-form-item label="扫描URL" prop="title">
            <el-input v-model="formData.urls[index].url" placeholder="请输入扫描URL" clearable style="width: 100%" />
          </el-form-item>
          <el-button class="addURLBtn" @click="handBtn('add')" v-if="index === 0"> 新增 </el-button>
          <el-button class="addURLBtn" @click="handBtn('del', index)" v-else> 删除 </el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
export default {
  name: "urlForm",
};
</script>

<script setup>
import { reactive, ref } from "vue";

/* 表单 */
let formData = ref({
  smType: 1,
  topic_prefix: "",
  name: "",
  taskmode: "",
  description: "",
  profile: "",
  urls: [{ url: "" }],
  scriptMatchLevel: "MATCH_SKIP",
});

/* 表单项 */
let formList = reactive([
  {
    formType: "select",
    prop: "smType",
    label: "扫描方式",
    required: true,
    filterable: true,
    options: [
      { value: 1, label: "使用默认策略扫描" },
      { value: 2, label: "使用指定策略扫描" },
      { value: 3, label: "使用指定脚本扫描" },
    ],
    onChange(val) {
      formList.find((item) => item.prop === "profile").isShow = val === 2;
    },
  },
  {
    formType: "input",
    prop: "name",
    label: "任务名称",
    required: true,
  },
  {
    formType: "input",
    prop: "topic_prefix",
    label: "任务ID前缀",
    required: false,
  },
  {
    formType: "select",
    prop: "taskmode",
    label: "任务扫描模式",
    required: true,
    filterable: true,
    dictName: "nodemanager_soscan_taskMode",
    sime: true,
    options: [],
  },
  {
    formType: "input",
    prop: "description",
    label: "任务描述信息",
    required: false,
    type: "textarea",
  },
  {
    formType: "select",
    prop: "profile",
    label: "扫描策略",
    required: true,
    isShow: false,
    filterable: true,
    options: [],
  },
  {
    formType: "select",
    prop: "taskGroup",
    label: "策略脚本",
    isShow: false,
    required: true,
    options: [],
  },
]);

/* 新增与删除 */
const handBtn = (type, index) => {
  if (type === "del") {
    formData.value.urls.splice(index, 1);
  } else {
    formData.value.urls.push({ url: "" });
  }
};

let ruleFormRef = ref();
const submitForm = () => {
  let data = false;
  ruleFormRef.value.validate((valid) => {
    data = valid;
  });
  return data;
};

defineExpose({
  submitForm,
  getList() {
    return formData.value;
  },
});
</script>

<style scoped lang="scss">
.btnDiv {
  position: relative;
  .addURLBtn {
    position: absolute;
    right: -66px;
    top: 0;
  }
}
</style>
