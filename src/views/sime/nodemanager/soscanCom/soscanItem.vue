<template>
  <!-- SOSCAN - itme -->
  <div v-loading="loading">
    <el-row :gutter="20">
      <el-col :span="3">
        <el-tabs v-model="activeName" tab-position="left" style="height: 100%">
          <el-tab-pane v-for="item in tabsList" :key="item.dictValue" :label="item.dictLabel" :name="item.dictValue" />
        </el-tabs>
      </el-col>
      <el-col :span="21">
        <SoScanList
          v-if="activeName && nodeId"
          :nodeId="nodeId"
          :taskType="activeName"
          @loading="
            (val) => {
              loading = val;
            }
          "
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "soscanItem",
};
</script>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, watch, provide, onActivated } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();
import { getTreeData } from "@/api/sime/config/logPermisson";
import { getDicts } from "@/api/sime/config/dict";

import SoScanList from "./soScanList.vue";

const props = defineProps({
  /* 节点id */
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
});

/* tab 数据 */
const activeName = ref("");
const tabsList = ref([]);
let loading = ref(false);

/* 获取数据 */
const getPublic = () => {
  /* 任务类型 */
  getDicts("nodemanager_soscan_taskType").then((res) => {
    activeName.value = res.data[0].dictValue;
    tabsList.value = res.data;
  });
};
getPublic();
</script>

<style scoped lang="scss">
::v-deep .el-tabs--left .el-tabs__header.is-left {
  float: right;
}
</style>
