<template>
  <!-- SOSCAN - 关联资产 -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>

    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
      labelWidth="105px"
    />

    <!-- 'activemq_20221226101944_jmvytaax'   -->
    <xel-table ref="tableRef" :columns="columns" :default-params="{ taskID }" :load-data="getGatherAssets"> </xel-table>

    <!-- 关联资产-详情弹窗  -->
    <SoScanDetailDia ref="SoScanDetailDiaRef" taskType="associatedAssets" />
  </el-card>
</template>

<script>
export default {
  name: "associatedAssets",
};
</script>

<script setup>
import { reactive, ref } from "vue";
import { getGatherAssets } from "@/api/sime/nodemanager/soscanTask";

import SoScanDetailDia from "./soScanDetailDia.vue";
import { useRoute } from "vue-router";
const route = useRoute();
const taskID = route.params.taskID;

/* 搜索相关 */
let searchState = reactive({
  data: {
    ip: "",
    port: "",
    serviceName: "",
    location: "",
    url: "",
    channelType: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "ip",
      label: "IP",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "port",
      label: "端口",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "serviceName",
      label: "应用层协议",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "location",
      label: "地理位置",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "url",
      label: "URL",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "channelType",
      label: "资产来源类型",
      itemWidth: "20%",
    },
  ],
});

const SoScanDetailDiaRef = ref();
/* 列表相关 */
let columns = [
  {
    prop: "ip",
    label: "IP",
  },
  {
    prop: "port",
    label: "端口",
  },
  {
    prop: "serviceName",
    label: "应用层协议",
  },
  {
    prop: "location",
    label: "地理位置",
  },
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "channelType",
    label: "资产来源类型",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "View",
        title: "查看",
        onClick(scope) {
          SoScanDetailDiaRef.value.open(scope.row);
        },
      },
    ],
  },
];

const tableRef = ref();
const search = (initPageNum = true) => {
  tableRef.value.reload(searchState.data, initPageNum);
};
const reset = () => {
  searchState.data = {
    ip: "",
    port: "",
    serviceName: "",
    location: "",
    url: "",
    channelType: "",
  };
  search();
};
</script>

<style scoped></style>
