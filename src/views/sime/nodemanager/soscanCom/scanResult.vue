<template>
  <!-- SOSCAN - 扫描结果 -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>

    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
      labelWidth="105px"
    />

    <!--'Topic_ShengTing_202310171823_Weblogic' -->
    <xel-table ref="tableRef" :columns="columns" :default-params="{ taskID }" :load-data="getScanResult"> </xel-table>

    <!-- 扫描结果-详情弹窗  -->
    <SoScanDetailDia ref="SoScanDetailDiaRef" taskType="scanResult" />
  </el-card>
</template>

<script>
export default {
  name: "scanResult",
};
</script>

<script setup>
import { reactive, ref } from "vue";
import { getScanResult } from "@/api/sime/nodemanager/soscanTask";

import SoScanDetailDia from "./soScanDetailDia.vue";
import { useRoute } from "vue-router";
const route = useRoute();
const taskID = route.params.taskID;

/* 搜索相关 */
let searchState = reactive({
  data: {
    ip: "",
    assetEntity: "",
    location: "",
    net: "",
    channelType: "",
    procotol: "",
    serviceName: "",
    product: "",
    url: "",
    title: "",
    titleOnIp: "",
    vulnName: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "ip",
      label: "IP地址",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "assetEntity",
      label: "实体名称",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "location",
      label: "地理位置",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "net",
      label: "网络",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "channelType",
      label: "资产来源",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "procotol",
      label: "基础网络协议",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "product",
      label: "产品组件",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "url",
      label: "URL",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "title",
      label: "标题",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "titleOnIp",
      label: "同Ip标题",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "vulnName",
      label: "漏洞名称",
      itemWidth: "20%",
    },
    {
      formType: "input",
      prop: "serviceName",
      label: "服务名称/应用层协议",
      itemWidth: "30%",
      labelWidth: "11em",
    },
  ],
});

const SoScanDetailDiaRef = ref();
/* 列表相关 */
let columns = [
  {
    prop: "ip",
    label: "IP地址",
  },
  {
    prop: "assetEntity",
    label: "实体名称",
  },
  {
    prop: "location",
    label: "地理位置",
  },
  {
    prop: "net",
    label: "网络",
  },
  {
    prop: "channelType",
    label: "资产来源",
  },
  {
    prop: "procotol",
    label: "基础网络协议",
  },
  {
    prop: "serviceName",
    label: "服务名称/应用层协议",
  },
  {
    prop: "product",
    label: "产品组件",
  },
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "title",
    label: "标题",
  },
  {
    prop: "titleOnIp",
    label: "同Ip标题",
  },
  {
    prop: "vulnName",
    label: "漏洞名称",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "View",
        title: "查看",
        onClick(scope) {
          SoScanDetailDiaRef.value.open(scope.row);
        },
      },
    ],
  },
];

const tableRef = ref();
const search = (initPageNum = true) => {
  tableRef.value.reload(searchState.data, initPageNum);
};
const reset = () => {
  searchState.data = {
    ip: "",
    assetEntity: "",
    location: "",
    net: "",
    channelType: "",
    procotol: "",
    serviceName: "",
    product: "",
    url: "",
    title: "",
    titleOnIp: "",
    vulnName: "",
  };
  search();
};
</script>

<style scoped></style>
