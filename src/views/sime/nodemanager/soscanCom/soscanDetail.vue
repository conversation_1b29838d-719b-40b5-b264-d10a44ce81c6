<template>
  <!-- SOSCAN - SOSCAN详情 - 扫描详情 -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <SoscanItem :nodeId="nodeId" />
  </el-card>
</template>

<script>
export default {
  name: "soscanDetail",
};
</script>

<script setup>
import SoscanItem from "./soscanItem.vue";
import { useRoute } from "vue-router";
const route = useRoute();
const nodeId = route.params.nodeId;
</script>

<style scoped></style>
