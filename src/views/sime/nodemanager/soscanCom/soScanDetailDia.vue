<template>
  <xel-dialog :title="taskType === 'scanResult' ? '扫描结果' : '关联资产'" ref="dialogRef" width="70%" buttonCancel="关闭" :show-submit="false">
    <el-row :gutter="20">
      <el-col :span="12" v-for="item in taskType === 'scanResult' ? columns : colData" :key="item.prop">
        <div class="detailDiv">
          <span class="titleSpan">{{ item.label }}：</span>
          <span class="conSpan" :title="detailData[item.prop]">{{ detailData[item.prop] || "暂无" }}</span>
        </div>
      </el-col>
    </el-row>
  </xel-dialog>
</template>

<script>
export default {
  name: "SoScanDetailDia",
};
</script>

<script setup>
/*
 * SOSCAN - 扫描结果 - 弹窗
 * taskType
 *   scanResult(扫描结果)
 *   associatedAssets(关联资产)
 * */
import { ref } from "vue";
let props = defineProps({
  /* 任务类型 */
  taskType: {
    type: String,
    default() {
      return "scanResult";
    },
  },
});

/* 扫描结果 */
let columns = ref([
  {
    prop: "ip",
    label: "IP地址",
  },
  {
    prop: "hostname",
    label: "域名",
  },
  {
    prop: "assetEntity",
    label: "实体名称",
  },
  {
    prop: "administerEntity",
    label: "监管单位",
  },
  {
    prop: "location",
    label: "地理位置",
  },
  {
    prop: "net",
    label: "网络",
  },
  {
    prop: "channelName",
    label: "资产(指纹)识别脚本",
  },
  {
    prop: "channelType",
    label: "资产来源",
  },
  {
    prop: "port",
    label: "端口",
  },
  {
    prop: "procotol",
    label: "基础网络协议",
  },
  {
    prop: "server",
    label: "服务信息/端口Banner信息",
  },
  {
    prop: "tunnel",
    label: "tls/ssl隧道",
  },
  {
    prop: "serviceName",
    label: "服务名称/应用层协议",
  },
  {
    prop: "product",
    label: "产品组件",
  },
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "title",
    label: "标题",
  },
  {
    prop: "titleOnIp",
    label: "同Ip标题",
  },
  {
    prop: "retrieve",
    label: "检索语句",
  },
  {
    prop: "vulnurl",
    label: "漏洞URL",
  },
  {
    prop: "vulnName",
    label: "漏洞名称",
  },
  {
    prop: "level",
    label: "威胁级别",
  },
  {
    prop: "confidence",
    label: "置信率",
  },
  {
    prop: "weakpass",
    label: "弱口令",
  },
  {
    prop: "payloadInfo",
    label: "Payload数据",
  },
  {
    prop: "verifiedInfo",
    label: "已验证信息",
  },
  {
    prop: "requestData",
    label: "请求数据",
  },
  {
    prop: "extraInfo",
    label: "补充信息",
  },
  {
    prop: "scriptName",
    label: "扫描脚本名称",
  },
  {
    prop: "vulnDescp",
    label: "漏洞描述",
  },
  {
    prop: "reviewInfo",
    label: "脚本审核信息",
  },
  {
    prop: "sysUpdateTime",
    label: "时间戳",
  },
]);
/* 关联资产 */
let colData = ref([
  {
    prop: "ip",
    label: "IP",
  },
  {
    prop: "hostname",
    label: "域名",
  },
  {
    prop: "port",
    label: "端口",
  },
  {
    prop: "procotol",
    label: "基础协议",
  },
  {
    prop: "tunnel",
    label: "tls隧道",
  },
  {
    prop: "banner",
    label: "banner",
  },
  {
    prop: "server",
    label: "服务信息",
  },
  {
    prop: "serviceName",
    label: "应用层协议",
  },
  {
    prop: "net",
    label: "所属网络",
  },
  {
    prop: "product",
    label: "产品组件",
  },
  {
    prop: "location",
    label: "地理位置",
  },
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "title",
    label: "标题",
  },
  {
    prop: "titleOnIp",
    label: "同IP标题",
  },
  {
    prop: "channelType",
    label: "资产来源类型",
  },
  {
    prop: "retrieve",
    label: "检索语句",
  },
  {
    prop: "assetEntity",
    label: "资产实体",
  },
  {
    prop: "administerEntity",
    label: "监管单位",
  },
  {
    prop: "extraInfo",
    label: "补充信息",
  },
  {
    prop: "sysUpdateTime",
    label: "时间戳",
  },
]);

let dialogRef = ref();
let detailData = ref({});
function open(data) {
  detailData.value = data;
  dialogRef.value.open();
}

defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.detailDiv {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .titleSpan {
    flex: 1;
    font-weight: bold;
    display: inline-block;
    text-align: end;
    font-size: 15px;
  }
  .conSpan {
    position: relative;
    top: 2px;
    font-size: 15px;
    flex: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
</style>
