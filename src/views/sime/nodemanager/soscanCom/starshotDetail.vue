<template>
  <!-- starshot -->
  <el-card>
    <h3 class="conH3Tit">
      {{ "" || ($route.meta && $route.meta.title) }}
    </h3>
    <div class="starshot-wrapper">
      <el-row :gutter="20">
        <el-col :span="3" style="text-align: end">
          <el-tabs v-model="activeName" tab-position="left" style="height: 100%">
            <el-tab-pane v-for="item in tabsList" :key="item.path" :label="item.name" :name="item.path" />
          </el-tabs>
        </el-col>
        <el-col :span="21" class="ml-15-new">
          <SpecialMapping v-if="activeName == '1'" ref="specialMappingRef" class="starshot-tab-40" />
          <specialTesting v-if="activeName == '2'" ref="specialTestingRef" class="starshot-tab" />
          <globalSearch v-if="activeName == '3'" class="starshot-tab" />
          <FingerprintMaintenance v-if="activeName == '4'" class="starshot-tab" />
          <DetectionPackageMaintenance v-if="activeName == '5'" class="starshot-tab" />
          <PocAdministration v-if="activeName == '6'" class="starshot-tab-40" />
          <resourceAllocation v-if="activeName == '7'" class="starshot-tab" />
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script>
export default {
  name: "StarshotDetail",
};
</script>

<script setup>
import { ref, watch } from "vue";
import SpecialMapping from "../starshot/specialMapping/index.vue";
import specialTesting from "../starshot/specialTesting/index.vue";
import globalSearch from "../starshot/globalSearch/index.vue";
import resourceAllocation from "../starshot/resourceAllocation/index.vue";
import FingerprintMaintenance from "../starshot/fingerprintMaintenance/index.vue";
import DetectionPackageMaintenance from "../starshot/detectionPackageMaintenance/index.vue";
import PocAdministration from "../starshot/pocAdministration/index.vue";
import { useRoute } from "vue-router";
/* tab 数据 */
const activeName = ref("1");
const route = useRoute();
const specialMappingRef = ref(null);
const specialTestingRef = ref(null);
let tabsList = ref([
  {
    name: "资产测绘",
    path: "1",
  },
  {
    name: "专项检测",
    path: "2",
  },
  {
    name: "全局搜索",
    path: "3",
  },
  {
    name: "服务规则维护",
    path: "4",
  },
  {
    name: "Web规则维护",
    path: "5",
  },
  {
    name: "Poc管理",
    path: "6",
  },
  {
    name: "资源分配",
    path: "7",
  },
]);
function handleClick(val) {
  activeName.value = val.paneName;
}
// 返回当前页面时，调用对应页面的搜索方法
watch(
  () => route.name,
  (val) => {
    if (val == "StarshotDetail") {
      if (activeName.value == "1") {
        specialMappingRef.value?.search();
      } else if (activeName.value == "2") {
        specialTestingRef.value?.search();
      }
    }
  }
);
</script>

<style scoped lang="scss">
::v-deep .el-tabs--left .el-tabs__header.is-left {
  float: right;
}
::v-deep .starshot-tab-40 {
  margin-top: -40px;
}
::v-deep .starshot-tab {
  margin-top: -50px;
}
//节点管理--星梭详情
::v-deep.starshot-wrapper {
  .el-card.is-always-shadow {
    box-shadow: none !important;
  }
}
</style>
