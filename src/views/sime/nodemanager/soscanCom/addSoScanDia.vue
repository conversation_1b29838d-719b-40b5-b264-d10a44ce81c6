<template>
  <xel-dialog :title="title" ref="dialogRef" width="50%" buttonCancel="关闭" @submit="submitForm" @closed="closeDia">
    <el-form :model="formData" ref="ruleFormRef" label-width="120px" :rules="rules">
      <el-row>
        <el-col :span="21">
          <xel-form-item v-for="item in formList" :key="item.prop" v-model="formData[item.prop]" v-bind="item" />

          <el-form-item label="策略脚本" prop="scripts" v-if="formData.smType === 3">
            <el-cascader v-model="formData.scripts" :props="cascader" placeholder="请选择策略脚本" />
          </el-form-item>

          <!-- 动态URL -->
          <div v-if="taskType === 'url'">
            <div v-for="(item, index) in formData.urls" :key="item" class="btnDiv">
              <el-form-item label="扫描URL" prop="title">
                <el-input v-model="formData.urls[index].url" placeholder="请输入扫描URL" clearable style="width: 100%" />
              </el-form-item>
              <el-button class="addURLBtn" type="primary" @click="handBtn('add')" v-if="index === 0"> 新增 </el-button>
              <el-button class="addURLBtn" @click="handBtn('del', index)" v-else> 删除 </el-button>
            </div>
          </div>

          <!-- 动态 扫描规则 -->
          <div v-else>
            <div v-for="(item, index) in formData.rules" :key="item" class="btnDiv">
              <div class="rulesDiv">
                <div>
                  <el-form-item label="目标" prop="query">
                    <el-input v-model="formData.rules[index].query[0]" placeholder="目标" clearable style="width: 100%" />
                  </el-form-item>
                </div>

                <div>
                  <el-form-item label="来源" label-width="60px" prop="source">
                    <el-input v-model="formData.rules[index].source" placeholder="来源" clearable style="width: 100%" />
                  </el-form-item>
                </div>

                <div>
                  <el-form-item label="附加条件" label-width="80px" prop="conditions">
                    <el-input v-model="formData.rules[index].conditions" placeholder="附加条件" clearable style="width: 100%" />
                  </el-form-item>
                </div>
              </div>

              <el-button class="addURLBtn" type="primary" @click="handRules('add')" v-if="index === 0"> 新增 </el-button>
              <el-button class="addURLBtn" @click="handRules('del', index)" v-else> 删除 </el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </xel-dialog>
</template>

<script>
export default {
  name: "AddSoScanDia",
};
</script>

<script setup>
/*
 * SOSCAN - 弹窗
 * */
import { ref, computed, reactive, nextTick } from "vue";

import {
  postGetAllProfile,
  getProfilesByName,
  psotScanUrls,
  psotScanUrlsWithProfile,
  psotScanUrlsWithScripts,
  psotScanTopicWithProfile,
  psotScanTopicWithScripts,
  addTask,
} from "@/api/sime/nodemanager/soscanTask";

import { ElMessage } from "element-plus";

let props = defineProps({
  /* 弹框标题 */
  title: {
    type: String,
    default() {
      return "新增";
    },
  },
  /* 节点id */
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
  /* 任务类型 */
  taskType: {
    type: String,
    default() {
      return "";
    },
  },
});

const emits = defineEmits(["close"]);
const options = ref([]);

const rules = {
  scripts: [{ required: true, message: "请选择策略脚本", trigger: "change" }],
};

/* cascader 配置项 */
const cascader = {
  lazy: true,
  multiple: true,
  lazyLoad(node, resolve) {
    const { level, value } = node;
    if (level === 0) {
      setTimeout(() => {
        resolve(options.value);
      }, 1000);
    } else if (level === 1) {
      getProfilesByName(props.nodeId, { profilenames: [value] }).then((res1) => {
        let data = [];
        res1.data.profiles[0].scripts.forEach((item) => {
          data.push({
            value: item.scriptName,
            label: item.scriptName,
            leaf: true,
          });
        });
        resolve(data);
      });
    }
  },
};

/* 表单数据 */
let formData = ref({
  smType: 1,
  /*topic_prefix: "outpost_Task_Urls_Test01",*/
  topic_prefix: "",
  name: "",
  taskmode: "TaskOnly",
  description: "",
  profile: "",
  /*scripts: ['fscan/apache-ofbiz-cve-2018-8033-xxe.yml'],*/
  scripts: [],
  urls: [{ url: "" }],
  rules: [{ query: [], source: "", conditions: "" }],
  scriptMatchLevel: "MATCH_SKIP",
});

/* 表单项 */
let formList = reactive([
  {
    formType: "select",
    prop: "smType",
    label: "扫描方式",
    required: true,
    filterable: true,
    options: [],
    onChange(val) {
      formList.find((item) => item.prop === "profile").isShow = val === 2;
    },
  },
  {
    formType: "input",
    prop: "name",
    label: "任务名称",
    required: true,
  },
  {
    formType: "input",
    prop: "description",
    label: "任务描述信息",
    required: false,
    type: "textarea",
  },
  {
    formType: "input",
    prop: "topic_prefix",
    label: "任务ID前缀",
    required: false,
  },
  {
    formType: "select",
    prop: "taskmode",
    label: "任务扫描模式",
    required: true,
    filterable: true,
    dictName: "nodemanager_soscan_taskMode",
    sime: true,
    options: [],
  },
  {
    formType: "select",
    prop: "profile",
    label: "扫描策略",
    required: true,
    isShow: false,
    filterable: true,
    options: [],
  },
  {
    formType: "select",
    prop: "taskGroup",
    label: "策略脚本",
    isShow: false,
    required: true,
    options: [],
  },
]);

/* 新增与删除 */
const handBtn = (type, index) => {
  if (type === "del") {
    formData.value.urls.splice(index, 1);
  } else {
    formData.value.urls.push({ url: "" });
  }
};
/* 新增与删除 */
const handRules = (type, index) => {
  if (type === "del") {
    formData.value.rules.splice(index, 1);
  } else {
    formData.value.rules.push({ query: [], source: "", conditions: "" });
  }
};

/* 确定按钮事件 */
let ruleFormRef = ref();
const submitForm = (close, loading) => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let query = JSON.parse(JSON.stringify(formData.value));
      delete query.smType;
      delete query.name;
      delete query.description;

      /* 处理数据格式 */
      if (formData.value.smType === 2) {
        delete query.scripts;
      } else if (formData.value.smType === 3) {
        delete query.profile;
        /* 级联数据处理为接口需要的格式 */
        query.scripts = query.scripts.map((item) => {
          return item[1];
        });
      } else {
        delete query.scripts;
        delete query.profile;
      }

      let data = [];
      formData.value.urls.forEach((item) => {
        data.push(item.url);
      });
      query.urls = data;

      /* URL 扫描方式 */
      let API = "";
      if (props.taskType === "url") {
        API = formData.value.smType === 1 ? psotScanUrls : formData.value.smType === 2 ? psotScanUrlsWithProfile : psotScanUrlsWithScripts;
        delete query.rules;
      } else {
        API = formData.value.smType === 2 ? psotScanTopicWithProfile : psotScanTopicWithScripts;
        delete query.urls;
      }

      API(props.nodeId, query)
        .then((res) => {
          let query2 = {
            name: formData.value.name,
            description: formData.value.description,
            taskType: props.taskType,
            nodeId: props.nodeId,
            taskId: res.data.taskid,
          };
          addTask(query2)
            .then((res1) => {
              close();
              emits("close");
              ElMessage.success("操作成功");
            })
            .catch(() => {
              close(false);
            });
        })
        .catch(() => {
          close(false);
        });
    }
  });
};

let dialogRef = ref();
function open() {
  /* 获取执行策略 */
  postGetAllProfile(props.nodeId, {}).then((res) => {
    res.data.profileName.forEach((item) => {
      formList
        .find((item) => item.prop === "profile")
        .options.push({
          value: item,
          label: item,
        });

      if (item !== "all") {
        options.value.push({
          value: item,
          label: item,
          leaf: false,
        });
      }
    });
  });

  if (props.taskType === "url") {
    formData.value.smType = 1;
    formList.find((item) => item.prop === "profile").isShow = false;
    formList.find((item) => item.prop === "smType").options = [
      { value: 1, label: "使用默认策略扫描" },
      { value: 2, label: "使用指定策略扫描" },
      { value: 3, label: "使用指定脚本扫描" },
    ];
  } else {
    formData.value.smType = 2;
    formList.find((item) => item.prop === "profile").isShow = true;
    formList.find((item) => item.prop === "smType").options = [
      { value: 2, label: "使用指定策略扫描" },
      { value: 3, label: "使用指定脚本扫描" },
    ];
  }
  dialogRef.value.open();
}
function closeDia() {
  formData.value = {
    smType: 1,
    /*topic_prefix: "outpost_Task_Urls_Test01",*/
    topic_prefix: "",
    name: "",
    taskmode: "TaskOnly",
    description: "",
    profile: "",
    /*scripts: ['fscan/apache-ofbiz-cve-2018-8033-xxe.yml'],*/
    scripts: [],
    urls: [{ url: "" }],
    rules: [{ query: [], source: "", conditions: "" }],
    scriptMatchLevel: "MATCH_SKIP",
  };
  formList.find((item) => item.prop === "profile").isShow = false;
}

defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.btnDiv {
  position: relative;
  .addURLBtn {
    position: absolute;
    right: -66px;
    top: 0;
  }
}
.rulesDiv {
  position: relative;
  display: flex;
  justify-content: space-between;
}
::v-deep .el-cascader {
  width: 100%;
}
</style>
