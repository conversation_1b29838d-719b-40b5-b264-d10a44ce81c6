<template>
  <!-- SOSCAN - 扫描任务 列表 -->
  <div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <template #form>
        <xel-form-item
          label="创建时间"
          type="datetimerange"
          form-type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model:start="searchState.data.startTime"
          v-model:end="searchState.data.endTime"
          itemWidth="41%"
        ></xel-form-item>
      </template>

      <el-button
        v-hasPermi="'nodemanager:soscan:deleteTask'"
        @click="batchDelFun(multipleSelection)"
        :disabled="multipleSelection.length === 0"
        class="search-button"
      >
        <icon n="icon-huabanfuben" :size="12"></icon>
        批量删除
      </el-button>
      <el-button @click="handAdd" class="search-button" v-hasPermi="'nodemanager:soscan:addTask'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>

    <xel-table
      ref="tableRef"
      :columns="columns"
      :checkbox="hasPermi('nodemanager:soscan:deleteTask')"
      :pagination="false"
      row-key="id"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <template #code="scope">
        <el-popover placement="right" :width="350" trigger="hover">
          <template #reference>
            <el-tag v-if="scope.row.code == 200" type="info">扫描中</el-tag>
            <el-tag v-else type="success">扫描结束</el-tag>
          </template>
          <p>脚本数：{{ scope.row.total }}</p>
          <p>目标总数：{{ scope.row.targets }}</p>
          <p>不可达目标数：{{ scope.row.failures }}</p>
          <p>可达目标数：{{ scope.row.accessibles }}</p>
          <p>重复目标数：{{ scope.row.repeats }}</p>
          <p>异常目标数：{{ scope.row.exceptions }}</p>
          <p>发现漏洞数：{{ scope.row.founds }}</p>
          <p>扫描完成数：{{ scope.row.scans }}</p>
          <p>需要扫描数：{{ scope.row.toscans }}</p>
          <p>状态信息：{{ scope.row.content }}</p>
        </el-popover>
      </template>
    </xel-table>

    <!-- 分页 -->
    <xel-pagination ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />

    <!-- SoScan 新增弹窗 -->
    <AddSoScanDia ref="AddSoScanDiaRef" :title="title" :nodeId="nodeId" :taskType="taskType" @close="search" />
  </div>
</template>

<script>
export default {
  name: "soScanList",
};
</script>

<script setup>
import { computed, reactive, ref, watch } from "vue";
import AddSoScanDia from "../soscanCom/addSoScanDia.vue";
import hasPermi from "@/utils/hasPermi";
import { getTaskList, getTaskListStatus, delTask } from "@/api/sime/nodemanager/soscanTask";
import { batchDelete } from "@/utils/delete";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const emits = defineEmits(["loading"]);
const props = defineProps({
  /* 节点id */
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
  /* 任务类型 */
  taskType: {
    type: String,
    default() {
      return "";
    },
  },
});

let tableRef = ref();
let tableData = ref([]);

/* 搜索相关 */
let searchState = reactive({
  data: {
    pageNum: 1,
    pageSize: 10,
    nodeId: props.nodeId,
    taskType: props.taskType,
    name: "",
    description: "",
    startTime: "",
    endTime: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "任务名称",
    },
    {
      prop: "description",
      label: "任务描述",
    },
  ],
});
const router = useRouter();
/* 列表相关 */
let columns = [
  {
    prop: "name",
    label: "任务名称",
  },
  {
    prop: "description",
    label: "任务描述",
  },
  {
    prop: "code",
    label: "扫描状态",
    slotName: "code",
  },
  {
    prop: "createByName",
    label: "创建人",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "ChatLineRound",
        title: "扫描结果",
        hasPermi: "nodemanager:soscan:taskList",
        onClick(scope) {
          router.push({
            name: "ScanResult",
            params: { taskID: scope.row.taskId },
          });
        },
      },
      {
        icon: "Connection",
        title: "关联资产",
        hasPermi: "nodemanager:soscan:taskList",
        onClick(scope) {
          router.push({
            name: "AssociatedAssets",
            params: { taskID: scope.row.taskId },
          });
        },
      },
    ],
  },
];

/* 获取表格数据 */
let total = ref(0);
const search = () => {
  emits("loading", true);
  getTaskList(searchState.data).then((res) => {
    tableData.value = [];
    total.value = res.data.total;
    if (res.data.total === 0) {
      emits("loading", false);
    }
    res.data.rows.forEach((item, index) => {
      getTaskListStatus(props.nodeId, { taskid: item.id }).then((res1) => {
        item.code = res1.data.code;
        item.content = res1.data.content;
        item.repeats = res1.data.repeats;
        item.failures = res1.data.failures;
        item.toscans = res1.data.toscans;
        item.accessibles = res1.data.accessibles;
        item.targets = res1.data.targets;
        item.exceptions = res1.data.exceptions;
        item.founds = res1.data.founds;
        item.total = res1.data.total;
        item.scans = res1.data.scans;
        tableData.value.push(item);
        if (index === res.data.rows.length - 1) {
          setTimeout(() => {
            emits("loading", false);
          }, 500);
        }
      });
    });
  });
};

/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

/* 批量删除 */
let multipleSelection = ref([]);
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};
function batchDelFun(rows) {
  batchDelete().then(() => {
    let ids = rows.map((item) => item.id);
    delTask(ids.join()).then(() => {
      ElMessage.success("删除成功");
      tableRef.value.table.clearSelection();
      search();
    });
  });
}

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = {
    pageNum: 1,
    pageSize: 10,
    nodeId: props.nodeId,
    taskType: props.taskType,
    name: "",
    description: "",
    startTime: "",
    endTime: "",
  };
  paginationRef.value.resetPageNum();
  search();
}

/* 新增相关 */
const AddSoScanDiaRef = ref();
let title = ref("");
const handAdd = () => {
  title.value = props.taskType === "url" ? "新增URL扫描任务" : "新增专项扫描任务";
  AddSoScanDiaRef.value.open();
};

watch(
  () => props.taskType,
  (val) => {
    if (val) {
      searchState.data.taskType = val;
      search();
    }
  }
);
</script>
<style scoped></style>
