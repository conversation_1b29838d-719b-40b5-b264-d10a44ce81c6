<template>
  <!-- 节点总览  -->
  <el-card v-loading="loading">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName" v-if="tabsList.length">
      <el-tab-pane v-for="item in tabsList" :key="item.nodeType" :label="item.nodeTypeText" :name="item.nodeType">
        <SiriusList :nodeType="item.nodeType" />
      </el-tab-pane>
    </el-tabs>

    <p v-else class="no-data">暂无数据</p>
  </el-card>
</template>

<script>
export default {
  name: "generalView",
};
</script>
<script setup>
import SiriusList from "./generalViewCom/siriusList.vue";
import { getNodeType } from "@/api/sime/nodemanager/generalView";
import { onMounted, ref } from "vue";
/* 基础数据 */
let loading = ref(false);
let activeName = ref("");
const tabsList = ref([]);

/* 获取tab 数据 */
const getNodeTypeList = () => {
  loading.value = true;
  getNodeType()
    .then((res) => {
      if (res && res.data && res.data.length) {
        activeName.value = res.data[0].nodeType;
        tabsList.value = res.data;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
onMounted(() => {
  getNodeTypeList();
});
</script>
<style scoped></style>
