<template>
  <!-- 节点方法 tab -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName">
      <el-tab-pane label="发送接口" name="send">
        <NodeList :nodeType="nodeType" type="send" />
      </el-tab-pane>
      <el-tab-pane label="接收接口" name="receive">
        <NodeList :nodeType="nodeType" type="receive" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script>
export default {
  name: "nodeMethod",
};
</script>

<script setup>
import NodeList from "./nodeMethodCom/nodeList.vue";
import { getDictsData } from "@/utils/getDicts";
import { onMounted, ref } from "vue";
/* 基础数据 */
let activeName = ref("send");

/* 获取字典 */
const nodeType = ref([]);
const getPublic = () => {
  getDictsData("nodemanager_nodeType").then((res) => {
    nodeType.value = res;
  });
};
onMounted(() => {
  getPublic();
});
</script>

<style scoped></style>
