<template>
  <!-- 天狼星 - 卡片组件 -->
  <div class="siriusCardDiv">
    <!-- 头部 - 标题  -->
    <div class="stitle">
      <span class="ellipsis" :class="{ cursorP: isHand }" :title="data.name" @click="handDetail(data)">
        {{ data.name }}
      </span>

      <!-- 编辑 -->
      <el-icon @click="handUpdata(data)" v-hasPermi="'nodemanager:node:update'">
        <edit />
      </el-icon>
    </div>
    <!-- 主体   -->
    <div class="sComDiv">
      <p class="ellipsis">
        <span class="staTitSpan">状态：</span>
        <span class="onlineSpan mr5" :class="data.status == 1 ? 'greenStat' : 'redStat'" />
        {{ data.status == 1 ? "在线" : "离线" }}
      </p>
      <p class="ellipsis" :title="data.description">
        <span class="staTitSpan">描述：</span>
        {{ data.description }}
      </p>
      <p>
        <span class="staTitSpan">IP：</span>
        {{ data.ip }}
      </p>
      <p class="no-border-card">
        <span class="staTitSpan">端口：</span>
        {{ data.port }}
      </p>

      <!--  按钮组    -->
      <div class="btnsDiv mt10 mb10">
        <el-button size="small" @click="handLook(data)">
          <el-icon :size="12"> <View /> </el-icon>
          查看状态
        </el-button>

        <el-button size="small" @click="handDelect(data)" v-hasPermi="'nodemanager:node:delete'">
          <el-icon :size="12"> <delete /> </el-icon>
          删除节点
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import hasPermi from "@/utils/hasPermi";
import { computed, ref } from "vue";

/* 默认传参 */
let props = defineProps({
  data: {
    type: Object,
    default() {
      return {
        name: "暂无数据",
      };
    },
  },
});

let emits = defineEmits(["handLook", "handDelect"]);
const router = useRouter();
/* 查看状态 */
const handLook = (data) => {
  emits("handLook", data);
};

/* 删除节点 */
const handDelect = (data) => {
  emits("handDelect", data);
};

/* 更新描述 */
const handUpdata = (data) => {
  emits("handUpdata", data);
};

/* 判断是否可以点查看详情 */
let isHand = computed(() => {
  if (props.data.status == 1) {
    if (props.data.nodeType === "sirius") {
      return hasPermi("nodemanager:sirius:getAnalysisConfig") || hasPermi("nodemanager:sirius:getWhiteLists");
    } else if (props.data.nodeType === "starshot") {
      return true;
    }
    {
      return hasPermi("nodemanager:soscan:taskList");
    }
  } else {
    return false;
  }
});
/* 查看详情 */
const handDetail = (data) => {
  if (isHand.value && data.status == 1) {
    if (data.nodeType === "sirius") {
      router.push({
        name: "SiriusDetail",
        params: {
          nodeId: data.id,
        },
      });
    } else if (data.nodeType === "starshot") {
      router.push({
        name: "StarshotDetail",
        params: {
          nodeId: data.id,
          nodeType: data.nodeType,
        },
      });
    } else {
      router.push({
        name: "SoScanDetail",
        params: {
          nodeId: data.id,
        },
      });
    }
  }
};
</script>

<style scoped lang="scss">
.cursorP {
  cursor: pointer;
  text-decoration: underline;
}
.siriusCardDiv {
  /*border: 1px solid #ccc;*/
  margin-bottom: 20px;
  font-size: 12px;

  /* 头部 - 标题 */
  .stitle {
    border-radius: 10px 10px 0 0;
    background: #ef8936;
    color: #fff;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    position: relative;
    > span {
      display: inline-block;
      width: 88%;
    }
    .el-icon {
      cursor: pointer;
      position: absolute;
      top: 12px;
      right: 15px;
      z-index: 99;
    }
  }
  /* 主体 */
  .sComDiv {
    background: #f8f6f7;
    border-radius: 0 0 10px 10px;
    padding: 10px;

    p {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px dashed #e3e3e3;
      .staTitSpan {
        display: inline-block;
        width: 50px;
        text-align: end;
        color: #b0b0b0;
        margin-right: 20px;
      }
    }

    /* 状态 */
    .onlineSpan {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 10px;
      background: #6cd400;
    }
    .redStat {
      background: red;
    }
    .greenStat {
      background: green;
    }
  }

  /* 按钮组 */
  .btnsDiv {
    text-align: center;
    .el-button:first-child {
      background: #eef5ff;
    }
  }
}
</style>
