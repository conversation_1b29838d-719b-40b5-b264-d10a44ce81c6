<template>
  <div ref="chinaChartRef" style="height: 100%; width: 100%"></div>
</template>

<script setup>
import { nextTick, onMounted, ref, watch } from "vue";
import * as echarts from "echarts";
/* 默认传参 */
let props = defineProps({
  /* 数值 */
  data: {
    type: Number,
    default() {
      return 0;
    },
  },

  /* 单位符号 */
  unitType: {
    type: String,
    default() {
      return "%";
    },
  },

  /* 刻度最大值 */
  maxNum: {
    type: Number,
    default() {
      return 100;
    },
  },
});
/* 初始图表 */
let chinaChartRef = ref();
const initChart = () => {
  let myChart = echarts.init(chinaChartRef.value);

  let option = {
    series: [
      {
        type: "gauge",
        max: props.maxNum,
        axisLine: {
          lineStyle: {
            width: 15,
            color: [
              [0.3, "#66E0E3"],
              [0.7, "#5474D8"],
              [1, "#EF8936"],
            ],
          },
        },
        pointer: {
          itemStyle: {
            color: "auto",
          },
        },
        axisTick: {
          distance: -15,
          length: 8,
          lineStyle: {
            color: "#fff",
            width: 1,
          },
        },
        /* 刻度 */
        splitLine: {
          distance: -50,
          length: 30,
          lineStyle: {
            color: "#fff",
            width: 2,
          },
        },
        axisLabel: {
          color: "inherit",
          distance: 40,
          fontSize: 12,
        },
        detail: {
          valueAnimation: true,
          formatter: "{value} " + props.unitType,
          color: "inherit",
        },
        data: [
          {
            value: props.data,
          },
        ],
      },
    ],
  };

  myChart.setOption(option);
};

onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

watch(
  () => props.data,
  () => {
    initChart();
  }
);
</script>

<style scoped lang="scss"></style>
