<template>
  <!-- 天狼星 - 卡片 - 容器 -->
  <div style="padding: 0 20px" v-loading="loading" element-loading-text="正在处理中..." class="bg-p-border-new">
    <el-row :gutter="20" v-if="cartData.length">
      <el-col :xs="6" :sm="6" :md="6" :lg="6" :xl="4" v-for="item in cartData" :key="item.path">
        <SiriusCard :data="item" @handLook="handLook" @handDelect="handDelect" @handUpdata="handUpdata" />
      </el-col>
    </el-row>

    <!-- 暂无数据  -->
    <div class="no-data" v-else>暂无数据</div>
  </div>

  <!-- 编辑节点名称、描述 -->
  <SiriusUpdataDia ref="SiriusUpdataDiaRef" @updateData="getNodeListFun" />

  <!-- 查看状态 - 抽屉 -->
  <el-drawer v-model="drawer" title="查看状态" size="85%" v-if="drawer">
    <div style="height: 85vh">
      <el-scrollbar ref="scrollRef" style="height: 100%">
        <SiriusStatus :activeData="activeData" />
      </el-scrollbar>
    </div>
  </el-drawer>
</template>

<script setup>
import SiriusCard from "./siriusCard.vue";
import SiriusStatus from "./siriusStatus.vue";
import SiriusUpdataDia from "./siriusUpdataDia.vue";
import { getNodeList, getNodeDetail, deleteNode, nodeExit } from "@/api/sime/nodemanager/generalView";
import { ref, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

/* 默认传参 */
let props = defineProps({
  nodeType: {
    type: String,
    default() {
      return "";
    },
  },
});

/* 卡片 数据集 */
const cartData = ref([]);

/* 获取数据 */
const getNodeListFun = () => {
  getNodeList(props.nodeType).then((res) => {
    cartData.value = res.data.rows;
  });
};

watch(
  () => props.nodeType,
  (val) => {
    if (val) {
      getNodeListFun();
    }
  },
  { immediate: true }
);

/* 抽屉状态 */
const drawer = ref(false);

/* 编辑节点名称、描述 - 组件事件 */
const SiriusUpdataDiaRef = ref();
const handUpdata = (data) => {
  SiriusUpdataDiaRef.value.open(data);
};

let activeData = ref({});
/* 查看状态 - 组件事件 */
const handLook = (data) => {
  activeData.value = data;
  drawer.value = true;
};

let loading = ref(false);

/* 删除节点 - 组件事件 */
const handDelect = (data) => {
  loading.value = true;
  /*  1.点击删除按钮时，先请求这个接口，在返回值中判断节点是在线还是离线状态 */
  getNodeDetail(data.id)
    .then((res) => {
      let id = res.data.id;
      let status = res.data.status;
      /* 2.为离线状态，弹出提示(该节点已离线，确定删除该节点吗?，点确定后调用该方法 */
      if (status == 0) {
        let text = "该节点已离线，确定删除该节点吗?";
        deleteFun(text, id);
      } else {
        nodeExit(id, {})
          .then((res1) => {
            /* result
             * true：继续执行删除 - 直接删除
             * false：提示 节点退出失败，是否强制删除该节点
             * */
            if (res1.code === 200 && res1.data.result) {
              deleteNodeFun(id);
            } else {
              let text = "节点退出失败，是否强制删除该节点？";
              deleteFun(text, id, true);
            }
          })
          .catch(() => {
            /* 接口异常 - 强制删除 */
            let text = "节点退出失败，是否强制删除该节点？";
            deleteFun(text, id, true);
          });
      }
    })
    .catch(() => {
      loading.value = false;
    });
};

/* 删除方法 */
const deleteNodeFun = (id) => {
  deleteNode(id)
    .then(() => {
      ElMessage.success({
        message: "操作成功",
      });
      getNodeListFun();
    })
    .finally(() => {
      loading.value = false;
    });
};

/* 删除方法 - 弹窗 */
const deleteFun = (text, id, isForce = false) => {
  ElMessageBox.confirm(`${text}`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: isForce ? "强制删除" : "确定",
    cancelButtonText: isForce ? "稍后重试" : "取消",
    type: "warning",
  })
    .then(() => {
      deleteNodeFun(id);
    })
    .catch(() => {
      getNodeListFun();
      loading.value = false;
    });
};
</script>

<style scoped></style>
