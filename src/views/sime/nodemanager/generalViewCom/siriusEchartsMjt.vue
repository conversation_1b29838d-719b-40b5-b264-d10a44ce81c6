<template>
  <!-- 面积图 - 图表组件 -->
  <div class="charDiv">
    <!-- 自定义 - 按钮组 -->
    <div class="btnSpan btnCon">
      <span class="btnSpan" :class="{ active: item.value === activeVal }" v-for="item in btnList" :key="item.value" @click="handSpan(item)">
        {{ item.name }}
      </span>
    </div>
    <!-- 暂无数据 -->
    <div class="no-data" v-if="isShow">暂无数据</div>
    <!-- 图形  -->
    <div ref="chinaChartRef" style="height: 300px; width: 100%; padding-top: 40px"></div>
  </div>
</template>

<script setup>
import { computed, nextTick, onMounted, ref, watch } from "vue";
import * as echarts from "echarts";

/* 传递参数 */
const prpos = defineProps({
  /* 默认时间 - 60（分钟）*/
  activeVal: {
    type: Number,
    default() {
      return 60;
    },
  },
  /* x轴数据 */
  xData: {
    type: Array,
    default() {
      /*return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']*/
      return [];
    },
  },
  /* y轴数据 */
  yData: {
    type: Array,
    default() {
      /*return [820, 932, 901, 934, 1290, 1330, 1320]*/
      return [];
    },
  },
});

let emits = defineEmits(["handBtnSpan"]);

/* 是否显示暂无数据 */
const isShow = computed(() => {
  return !(prpos.xData.length && prpos.xData.length);
});

/* 按钮list */
let btnList = ref([
  { name: "1小时", value: 60 },
  { name: "12小时", value: 720 },
  { name: "24小时", value: 1440 },
]);

/* span 点击事件 */
const handSpan = (data) => {
  emits("handBtnSpan", data.value);
};

/* 初始图表 */
let chinaChartRef = ref();
const initChart = () => {
  let myChart = echarts.init(chinaChartRef.value);
  let option = {
    color: "#94AAEA",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    grid: {
      left: "3%",
      right: "5%",
      bottom: "3%",
      top: "5%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      axisLabel: {
        rotate: "0",
      },
      data: prpos.xData,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        data: prpos.yData,
        type: "line",
        smooth: true,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "#C6D9FF",
            },
            {
              offset: 1,
              color: "#F3F7FF",
            },
          ]),
        },
      },
    ],
  };

  myChart.setOption(option);
};

onMounted(() => {
  nextTick(() => {
    initChart();
  });
});

watch(
  [() => prpos.xData, () => prpos.yData],
  (val) => {
    nextTick(() => {
      initChart();
    });
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
/* 自定义 - 按钮 */
.btnSpan {
  display: inline-flex;
  align-items: center;
  span {
    margin-right: 5px;
    width: 50px;
    height: 30px;
    border: 1px solid #ef8936;
    font-weight: bold;
    border-radius: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    &:last-child {
      margin-right: 0;
    }
    &.active {
      box-shadow: none;
      background: #ef8936;
      color: #fff;
    }
  }
}

/* 按钮布局 */
.charDiv {
  position: relative;
  .btnCon {
    position: absolute;
    right: 10px;
    z-index: 999;
  }
  .no-data {
    position: absolute;
    left: 50%;
    top: 30%;
  }
}
</style>
