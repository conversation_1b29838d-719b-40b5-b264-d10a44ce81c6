<template>
  <!-- 抽屉 - 查看状态组件 -->
  <div class="siriusStatusDiv">
    <p>
      <span>
        {{ activeData.name || "未知" }}
      </span>
    </p>

    <!-- 仪表区   -->
    <el-row :gutter="20">
      <!--  CPU利用率  -->
      <el-col :span="8">
        <el-card>
          <template #header> CPU利用率 </template>
          <SiriusEchartsYb :data="cpuData || 0" />
        </el-card>
      </el-col>

      <!--  内存利用率  -->
      <el-col :span="8">
        <el-card>
          <template #header> 内存利用率 </template>
          <SiriusEchartsYb :data="ramData || 0" />
        </el-card>
      </el-col>

      <!--  磁盘使用 (G)  -->
      <el-col :span="8">
        <el-card>
          <template #header> 硬盘使用率 </template>
          <SiriusEchartsYb :data="diskData || 0" />
        </el-card>
      </el-col>

      <!-- 线型面积图 -->
      <el-col :span="24">
        <el-tabs v-model="activeName">
          <el-tab-pane label="CPU" name="CPU">
            <el-col :span="24" style="width: 100%">
              <SiriusEchartsMjt @handBtnSpan="handBtnSpan" v-if="activeName === 'CPU'" :xData="time" :yData="cpuUsage" :activeVal="timeVal" />
            </el-col>
          </el-tab-pane>

          <el-tab-pane label="内存" name="ram">
            <el-col :span="24" style="width: 100%">
              <SiriusEchartsMjt @handBtnSpan="handBtnSpan" v-if="activeName === 'ram'" :xData="time" :yData="ramUsage" :activeVal="timeVal" />
            </el-col>
          </el-tab-pane>

          <el-tab-pane label="硬盘" name="disk">
            <el-col :span="24" style="width: 100%">
              <SiriusEchartsMjt @handBtnSpan="handBtnSpan" v-if="activeName === 'disk'" :xData="time" :yData="hardDiskUsage" :activeVal="timeVal" />
            </el-col>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import SiriusEchartsYb from "./siriusEchartsYb.vue";
import SiriusEchartsMjt from "./siriusEchartsMjt.vue";
import { getNodeMonitoringLine, getGaugeCpu, getGaugeRam, getGaugeHardDisk } from "@/api/sime/nodemanager/generalView";
import { ref, watch } from "vue";

/* 默认传参 */
let props = defineProps({
  activeData: {
    type: Object,
    default() {
      return {};
    },
  },
});

/* 基础数据 */
let activeName = ref("CPU");

let emits = defineEmits(["handLook", "handDelect"]);

/* 获取 - cpu */
let cpuData = ref(0);
const getGaugeCpuFun = (id) => {
  getGaugeCpu(id).then((res) => {
    cpuData.value = res.data;
  });
};

/* 获取 - 内存 */
let ramData = ref(0);
const getGaugeRamFun = (id) => {
  getGaugeRam(id).then((res) => {
    ramData.value = res.data;
  });
};

/* 获取 - 硬盘 */
let diskData = ref(0);
const getGaugeHardDiskFun = (id) => {
  getGaugeHardDisk(id).then((res) => {
    diskData.value = res.data;
  });
};

/* 获取 - 折线图 */
let timeVal = ref(60);

/*
 * 时间轴 time
 * CPU cpuUsage
 * 内存 ramUsage
 * 硬盘 hardDiskUsage
 * */
let time = ref([]);
let cpuUsage = ref([]);
let ramUsage = ref([]);
let hardDiskUsage = ref([]);
const getNodeMonitoringLineFun = (id) => {
  getNodeMonitoringLine(id, { minute: timeVal.value }).then((res) => {
    time.value = res.data.time;
    cpuUsage.value = res.data.cpuUsage;
    ramUsage.value = res.data.ramUsage;
    hardDiskUsage.value = res.data.hardDiskUsage;
  });
};

/* 回调 - 当前时间 */
const handBtnSpan = (data) => {
  timeVal.value = data;
  getNodeMonitoringLineFun(props.activeData.id);
};

watch(
  () => props.activeData,
  (val) => {
    getGaugeCpuFun(val.id);
    getGaugeRamFun(val.id);
    getGaugeHardDiskFun(val.id);
    getNodeMonitoringLineFun(val.id);
  },
  { immediate: true }
);

/* 查看状态 */
const handLook = (data) => {
  emits("handLook", data);
};

/* 删除节点 */
const handDelect = (data) => {
  emits("handDelect", data);
};
</script>

<style scoped lang="scss">
::v-deep .el-card__body {
  height: 400px;
}
.siriusStatusDiv {
  overflow: hidden;
  /* 状态值 */
  > p {
    height: 25px;
    line-height: 25px;
    border-bottom: 2px solid #f4f4f4;
    margin-bottom: 20px;
    span {
      border-bottom: 2px solid #ee822b;
      padding-bottom: 3px;
    }
  }
}
</style>
