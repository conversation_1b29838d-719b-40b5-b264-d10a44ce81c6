<template>
  <!-- 编辑节点名称、描述 组件 -->
  <xelDialog title="编辑节点" ref="dialogRef" width="500px" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleForm" label-width="auto">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item" />
    </el-form>
  </xelDialog>
</template>

<script setup>
import { reactive, ref, toRefs } from "vue";
import { updateNode } from "@/api/sime/nodemanager/generalView";
import { ElMessage } from "element-plus";

/* 弹窗相关 */
let dialogRef = ref();
let form = reactive({
  formData: {
    id: "",
    name: "",
    description: "",
  },
});
let { formData } = toRefs(form);
/* 表单内容 */
let formList = ref([
  {
    formType: "input",
    prop: "name",
    label: "节点名称",
    required: true,
  },
  {
    formType: "input",
    prop: "description",
    label: "描述",
    required: true,
    type: "textarea",
    autosize: { minRows: 2, maxRows: 10 },
  },
]);

let emits = defineEmits(["updateData"]);

/* 提交按钮 */
let ruleForm = ref();
const submitForm = async function (close, loading) {
  let valid = await ruleForm.value.validate();
  if (valid) {
    loading();
    updateNode(form.formData)
      .then((res) => {
        ElMessage.success({
          message: "操作成功",
        });
        emits("updateData");
        close();
      })
      .catch(() => {
        close(false);
      });
  }
};

/* 打开方法 */
const open = (data) => {
  form.formData.id = data.id;
  form.formData.name = data.name;
  form.formData.description = data.description;
  dialogRef.value.open();
};

/* 重置 */
const closeDialog = () => {
  form.formData = {
    id: "",
    name: "",
    description: "",
  };
};

defineExpose({
  open,
});
</script>

<style scoped lang="scss"></style>
