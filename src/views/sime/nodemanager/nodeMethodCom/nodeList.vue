<template>
  <!-- 发送接口（send），接收接口（receive） - 列表 -->
  <div>
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="search" @reset="reset" />

    <xel-table v-if="type" ref="tableRef" :columns="columns" :row-key="idKey" :defaultParams="{ type }" :load-data="getNodeMethodList">
      <template #status="scope">
        <el-switch v-model="scope.row.isAvailable" :active-value="1" :inactive-value="0" @change="updateStatus(scope.row)" />
      </template>
    </xel-table>
  </div>
</template>

<script setup>
import { getNodeMethodList, updateisAvailable } from "@/api/sime/nodemanager/nodeMethod";
import { reactive, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { selectDictLabel } from "@/utils/ruoyi2";

/* 传递参数 */
const props = defineProps({
  /* 节点类型 - tab
   * 发送接口（send），接收接口（receive）
   * */
  type: {
    type: String,
    default() {
      return "";
    },
  },
  /* 节点类型 - 字典 */
  nodeType: {
    type: Array,
    default() {
      return [];
    },
  },
});

/* 搜索相关 */
let tableRef = ref();

let searchState = reactive({
  data: {
    methodName: "",
    description: "",
    isAvailable: "",
    nodeType: "",
  },
  formList: [
    {
      formType: "select",
      prop: "nodeType",
      label: "节点类型",
      dictName: "nodemanager_nodeType",
      sime: true,
    },
    {
      formType: "input",
      prop: "methodName",
      label: "方法名",
    },
    {
      formType: "input",
      prop: "description",
      label: "方法描述",
    },
    {
      formType: "select",
      prop: "isAvailable",
      label: "状态",
      dictName: "nodemanager_method_isAvailable",
      sime: true,
    },
  ],
});

/* 列表基础数据 */
let columns = [
  {
    prop: "nodeType",
    label: "节点类型",
    formatter(scope) {
      return selectDictLabel(props.nodeType, scope.nodeType);
    },
  },
  {
    prop: "methodName",
    label: "方法名",
  },
  {
    prop: "action",
    label: "实现类",
  },
  {
    prop: "description",
    label: "方法描述",
  },
  {
    prop: "timeout",
    label: "超时时间（秒）",
    hide: props.type !== "send",
  },
  {
    prop: "leaseTime",
    label: "租约时间（秒）",
    hide: props.type !== "send",
  },
  {
    prop: "isAvailable",
    label: "状态",
    slotName: "status",
    width: 150,
  },
];

/* 查询事件 */
const search = (initPageNum = true) => {
  tableRef.value.reload({ ...searchState.data }, initPageNum);
};

/* 重置事件 */
const reset = () => {
  searchState.data = {
    methodName: "",
    description: "",
    isAvailable: "",
    nodeType: "",
  };
  search();
};

/* 修改 */
const updateStatus = (data) => {
  let prevenable = data.isAvailable == 1 ? 0 : 1;
  let text = prevenable == 0 ? "开启" : "关闭";
  ElMessageBox.confirm("确认要" + text + "该节点吗?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let formData = new FormData();
      formData.append("id", data.id);
      formData.append("isAvailable", prevenable == 0 ? 1 : 0);
      updateisAvailable(formData).then(() => {
        ElMessage({
          type: "success",
          message: "操作成功",
        });
        search();
      });
    })
    .catch(function () {
      data.isAvailable = data.isAvailable == 0 ? 1 : 0;
    });
};
</script>

<style scoped></style>
