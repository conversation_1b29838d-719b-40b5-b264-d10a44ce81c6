<template>
  <!-- 选择服务探测包 -->
  <xel-dialog title="选择服务探测包" ref="dialogRef" width="80%" buttonCancel="取消" buttonDetermine="清空" @submit="submit">
    <div style="padding: 0 20px">
      <common-search
        :labelWidth="94"
        v-model="searchState.data"
        :menu-data="searchState.menuData"
        :form-list="searchState.formList"
        @search="search"
        @reset="reset"
      >
        <template #form>
          <xel-form-item
            label="时间"
            type="datetimerange"
            form-type="daterange"
            value-format="YYYY-MM-DD HH:mm:ss"
            v-model:start="searchState.data.startTime"
            v-model:end="searchState.data.endTime"
          ></xel-form-item>
        </template>
      </common-search>
      <!--  - 服务探测包 列表 -->
      <xel-table ref="tableRef" :columns="columns" :pagination="false" :data="tableData">
        <template #enableSlot="{ row }"> {{ row.enable ? "启用" : "禁用" }} </template>
      </xel-table>
      <!-- 分页 -->
      <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </div>
  </xel-dialog>
</template>

<script setup>
import { reactive, ref } from "vue";
import { getServiceProbe } from "@/api/sime/starshot/detectionPackage.js";
const emits = defineEmits(["getCheck"]);
const props = defineProps({
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
});

let tableRef = ref();
let tableData = ref([]);
const dialogRef = ref(); // 弹窗
/* 搜索相关 */
let searchState = reactive({
  data: {
    enable: "",
    name: "",
    protocol: "",
    rarity: undefined,
    startTime: "",
    endTime: "",
    ports: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态",
      prop: "enable",
      options: [],
      dictName: "nodemanager_starshot_enable",
      sime: true,
    },
  ],
  formList: [
    {
      prop: "name",
      label: "探测包名称",
    },
    {
      prop: "protocol",
      label: "协议",
    },
    {
      prop: "ports",
      label: "端口",
    },
    {
      prop: "rarity",
      label: "优先级",
      formType: "number",
      max: 10000,
      min: 0,
      precision: 0,
      placeholder: "请输入优先级",
      controls: false,
    },
  ],
});
/* 列表相关 */
let columns = [
  {
    prop: "name",
    label: "探测包名称",
  },
  {
    prop: "data",
    label: "探测数据",
  },
  {
    prop: "protocol",
    label: "协议",
  },
  {
    prop: "ports",
    label: "端口",
  },
  {
    prop: "sSLPorts",
    label: "探测SSL端口",
  },
  {
    prop: "totalWaitMS",
    label: "等待响应时间ms",
    formType: "number",
  },
  {
    prop: "tCPWrappedMS",
    label: "TCPWrappedMS",
    formType: "number",
  },
  {
    prop: "rarity",
    label: "优先级",
  },
  {
    prop: "fallback",
    label: "回溯匹配",
  },
  {
    prop: "enable",
    label: "状态",
    slotName: "enableSlot",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Tools",
        title: "选中",
        onClick(scope) {
          emits("getCheck", scope.row);
          dialogRef.value.close();
        },
      },
    ],
  },
];
// 清空
function submit() {
  emits("getCheck", {
    name: null,
    probeId: null,
  });
  dialogRef.value.close();
}
/* 获取表格数据 */
let total = ref(0);
const search = () => {
  getServiceProbe(props.nodeId, searchState.data).then((res) => {
    tableData.value = [];
    total.value = res.data.list.data.total;
    tableData.value = res.data.list.data.rows;
  });
};

/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};
/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = {
    pageNum: 1,
    pageSize: 10,
  };
  paginationRef.value?.resetPageNum();
  search();
}
// 打开弹窗
function open() {
  dialogRef.value.open();
  search();
}
defineExpose({
  open,
});
</script>
<style scoped></style>
