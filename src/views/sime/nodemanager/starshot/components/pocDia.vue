<template>
  <!-- 选择poc 多选 -->
  <xelDialog title="添加POC" buttonDetermine="添加" ref="dialogRef" size="large" @submit="submit" @close="close">
    <section style="padding: 0 20px">
      <common-search
        :labelWidth="86"
        v-model="searchState.data"
        :menu-data="searchState.menuData"
        :form-list="searchState.formList"
        @search="search"
        @reset="reset"
      >
        <template #form>
          <xel-form-item label="更新时间" type="date" form-type="date" day="YYYY-MM-DD" v-model="searchState.data.updatedAtSelect"> </xel-form-item>
        </template>
      </common-search>
      <el-table :data="tableData" @selection-change="handleSelectionChange" row-key="pocPath">
        <el-table-column type="selection" width="55" :selectable="selectable" :reserve-selection="true" />
        <el-table-column v-for="item in columns" :key="item.prop" :label="item.label" :prop="item.prop" />
      </el-table>
      <!-- 分页 -->
      <xel-pagination
        ref="paginationRef"
        class="xel-table-pagination"
        :total="total"
        :pageSizes="[10, 20, 50, 100, 1000]"
        @change="changePagination"
      />
    </section>
  </xelDialog>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { ref, watch, watchEffect } from "vue";
import { pocDataInfoList } from "@/api/sime/starshot/poc.js";
const emits = defineEmits(["getCheck"]);
const props = defineProps({
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
});
let tableRef = ref();
let tableData = ref([]);
const pocList = ref([]); //
const dialogRef = ref(); // 弹窗
/* 搜索相关 */
let searchState = ref({
  data: {
    pocPath: "",
    description: "",
    transport: "",
    severity: "",
    updatedAtSelect: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [],
  formList: [
    {
      prop: "pocPath",
      label: "poc路径",
    },
    {
      prop: "description",
      label: "描述",
    },
    {
      prop: "severity",
      label: "威胁等级",
      formType: "select",
      options: [
        { label: "critical", value: "critical" },
        { label: "high", value: "high" },
        { label: "info", value: "info" },
        { label: "low", value: "low" },
        { label: "medium", value: "medium" },
        { label: "unknown", value: "unknown" },
      ],
    },
  ],
});
// poc管理列表
let columns = [
  {
    prop: "pocPath",
    label: "POC路径",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "severity",
    label: "威胁等级",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
  },
];
/* 获取表格数据 */
let total = ref(0);
function search() {
  pocDataInfoList(props.nodeId, searchState.value.data).then((res) => {
    total.value = res.data.list.data.total;
    tableData.value = res.data.list.data.rows;
    // 回显选中的数据
  });
}
/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.value.data = {
    pageNum: 1,
    pageSize: 10,
  };
  paginationRef.value?.resetPageNum();
  search();
}
/* 分页事件 */
const changePagination = (val) => {
  searchState.value.data.pageNum = val.pageNum;
  searchState.value.data.pageSize = val.pageSize;
  search();
};

let multiplePocs = ref([]);

/* 批量删除 */
let multipleSelection = ref([]);
function handleSelectionChange(val) {
  multipleSelection.value = val;
}
// 判断是否可选
function selectable(row) {
  return !multiplePocs.value.includes(row.pocPath);
}
// 清空
function submit() {
  if (multipleSelection.value.length == 0) {
    ElMessage.warning("请选择要添加的POC");
    return;
  }
  const rows = multipleSelection.value;
  let ids = rows.map((item) => item.pocPath);
  multiplePocs.value.push(...ids);
  pocList.value.push(...rows);
  nextTick(() => {
    emits("getCheck", multiplePocs.value, pocList.value);
    dialogRef.value.close();
  });
}
function close() {
  multiplePocs.value = [];
  pocList.value = [];
  searchState.value.data = {
    pageNum: 1,
    pageSize: 10,
  };
  dialogRef.value.close();
}
// 打开弹窗
function open(paths = []) {
  multiplePocs.value = paths;
  dialogRef.value.open();
  search();
}
defineExpose({
  open,
});
</script>
<style scoped>
::v-deep(.common-search-btns-item) {
  margin-left: auto;
}
</style>
