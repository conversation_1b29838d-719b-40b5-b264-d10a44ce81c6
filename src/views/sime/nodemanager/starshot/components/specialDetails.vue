<template>
  <el-card class="bg-p-border-new">
    <section class="flex justify-between details-starshot-title">
      <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
      <section class="flex">
        <el-button
          @click="handSubmit('asset')"
          type="primary"
          v-hasPermi="route.query.source == '1' ? 'nodemanager:starshottask:syncAssetsResults' : 'nodemanager:starshottopic:syncAssetsResults'"
          >同步资产至前哨</el-button
        >
        <el-button
          @click="handSubmit('vuln')"
          type="primary"
          v-hasPermi="route.query.source == '1' ? 'nodemanager:starshottask:syncVulnsResults' : 'nodemanager:starshottopic:syncVulnsResults'"
          >同步漏洞至SOSS</el-button
        >
        <el-button class="pull-right" @click="backList"
          ><el-icon><Back /></el-icon>返回{{ "" || ($route.meta && $route.meta.title.slice(0, 4)) }}</el-button
        >
      </section>
    </section>
    <div class="pie-box" v-show="false">
      <section>
        <div class="pie">
          <Pie></Pie>
        </div>
      </section>
      <img :src="arrow" alt="logo" />
      <section>
        <div class="pie">
          <Pie></Pie>
        </div>
      </section>
      <img :src="arrow" alt="logo" />
      <section>
        <div class="pie">
          <Pie></Pie>
        </div>
      </section>
      <img :src="arrow" alt="logo" />
      <section>
        <div class="pie">
          <Pie></Pie>
        </div>
      </section>
    </div>
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="存活资产" name="service" :lazy="true">
        <SurvivingAssets :taskid="taskid" :id="nodeId" :key="nodeId" :type="type + 'survivingAssets'" />
      </el-tab-pane>
      <el-tab-pane label="开放端口" name="web" :lazy="true"> <OpenPort :taskid="taskid" :id="nodeId" :type="type + 'openPort'" /> </el-tab-pane>
      <el-tab-pane label="资产指纹" name="web1" :lazy="true">
        <AssetFingerprint :taskid="taskid" :id="nodeId" :type="type + 'assetFingerprint'" />
      </el-tab-pane>
      <el-tab-pane label="资产漏洞" name="web2" :lazy="true">
        <AssetVulnerability :taskid="taskid" :id="nodeId" :type="type + 'assetVulnerability'" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup>
import arrow from "@/assets/imgs/starsthot-img.png";
import SurvivingAssets from "../globalSearch/survivingAssets.vue";
import OpenPort from "../globalSearch/openPort.vue";
import AssetFingerprint from "../globalSearch/assetFingerprint.vue";
import AssetVulnerability from "../globalSearch/assetVulnerability.vue";
import { ref, computed } from "vue";
import Pie from "./pie.vue";
import { ElMessage } from "element-plus";
import { syncTaskVulnsResults, syncTaskAssetsResults, syncTopicAssetsResults, syncTopicVulnsResults } from "@/api/sime/starshot/specialMapping.js";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
const store = useStore();
const activeName = ref("service");
const prop = defineProps({
  taskid: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  nodeId: {
    type: String,
    default: "",
  },
});
const route = useRoute();
const router = useRouter();
const isNode = computed(() => route.name.includes("Node"));
const nodeId = computed(() => {
  return route.params.nodeId;
});
function handSubmit(type) {
  let syncApi = null;
  const isAssetMapping = route.query.source === "1";

  if (type === "asset") {
    syncApi = isAssetMapping ? syncTopicAssetsResults : syncTaskAssetsResults;
  } else if (type === "vuln") {
    syncApi = isAssetMapping ? syncTopicVulnsResults : syncTaskVulnsResults;
  }
  syncApi({ taskid: route.query.taskid }).then((res) => {
    ElMessage.success("操作成功");
  });
}
// 返回
function backList() {
  if (route.query.source == "1") {
    let name = isNode.value ? "StarshotDetail" : "SpecialMapping";
    router.push({
      name,
      params: {
        nodeId: nodeId.value,
      },
    });
  } else {
    let name = isNode.value ? "StarshotDetail" : "SpecialTesting";
    router.push({
      name,
      params: {
        nodeId: nodeId.value,
      },
    });
  }
  store.commit("closeCurrentTab");
}
</script>

<style scoped lang="scss">
.pie-box {
  background: #ffffff;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  padding: 18px;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  section {
    width: 100%;
    height: 200px;
    flex: 1;
    .pie {
      width: 100%;
      height: 100%;
    }
  }

  img {
    width: 36px;
    height: 29px;
  }
}
.details-tabs {
  margin-top: 25px;
}
::v-deep .el-tabs__nav {
  display: flex;
  justify-content: space-around;
  width: 100%;
}
::v-deep .table-handler-btns {
  margin-bottom: 18px;
}
.justify-between {
  justify-content: space-between;
  margin-bottom: 10px;
  section {
    margin-bottom: 10px;
    margin-top: -10px;
  }
}
</style>
