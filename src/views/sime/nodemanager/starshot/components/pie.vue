<template>
  <div>
    <div class="piewrapper" ref="piePanel"></div>
  </div>
</template>

<script>
let waterChart = null;
export default {
  data() {
    return {};
  },

  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    this.getRateList();
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    resizeFnc() {
      waterChart.resize();
    },
    // 获取构成
    async getRateList() {
      //   const res = await selectAssetsVulnConstituteList({ businessId: id });
      const res = [
        { category: "category1", rate: 53 },
        { category: "category2", rate: 13 },
      ];
      const result = res.map((item) => {
        // if (parseFloat(item.rate) / 100) {
        return {
          name: item.category,
          value: item.rate,
        };
        // }
      });
      this.initWaterPanel(result);
    },
    initWaterPanel(data) {
      waterChart = this.$echarts.init(this.$refs.piePanel);
      var option = {
        title: {
          text: "资产漏洞构成",
          subtext: "",
          left: "center",
          top: "bottom",
          textStyle: {
            fontSize: 12,
            color: "#fff", // 主标题文字颜色
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{b} : ({d}%)",
        },
        grid: {
          left: "10",
          bottom: "0",
          containLabel: true,
        },
        color: ["#5470c6", "#73c0de"],
        labelLine: {
          normal: {
            length: 0,
          },
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            // itemStyle: {
            //   borderRadius: 8,
            //   fontSize: 10,
            // },
            data: data,
          },
        ],
      };
      let noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#848484",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (data) {
        waterChart.setOption(option);
      } else {
        waterChart.setOption(noData);
      }
    },
  },
};
</script>

<style scoped lang="scss">
$base-font-size: 16;
.piewrapper {
  height: 200px;
  width: 100%;
}
</style>
