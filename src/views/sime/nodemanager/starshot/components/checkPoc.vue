<template>
  <section>
    <section class="flex-end" :style="{ marginBottom: '10px' }">
      <el-button @click="getPoc">
        <el-icon :size="12"> <plus /> </el-icon>添加POC</el-button
      >
    </section>
    <xel-table ref="tableRef" :columns="columns" :pagination="false" :data="pocTableList"> </xel-table>
    <!-- 分页 -->
    <xel-pagination
      ref="paginationRef"
      class="xel-table-pagination"
      v-if="pocCheckList.length > 9"
      :total="pocCheckList.length"
      @change="changePagination"
    />
    <pocDia :nodeId="nodeId" ref="dialogPocRef" @getCheck="submitPocs" />
  </section>
</template>
<script setup>
import { ref, computed, watch } from "vue";
import pocDia from "./pocDia.vue";
const prop = defineProps({
  nodeId: {
    type: String,
    default: "",
  },
  pocCheckData: {
    type: Array,
    default: () => [],
  },
});
// poc管理列表
let columns = [
  {
    prop: "pocPath",
    label: "POC路径",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "severity",
    label: "威胁等级",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Delete",
        title: "删除",
        onClick(scope) {
          pocCheckList.value.splice(scope.$index, 1);
        },
      },
    ],
  },
];

const pocCheckList = ref([]); //poc列表
watch(
  () => prop.pocCheckData,
  (newVal) => {
    if (newVal) {
      pocCheckList.value = newVal;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

const checkPoc = computed(() => {
  return pocCheckList.value.map((item) => item.pocPath);
}); //poc弹框组件引用
const dialogPocRef = ref(null); //poc弹框
function getPoc() {
  dialogPocRef.value.open(checkPoc.value);
}
//poc管理列表
const pageNum = ref(1);
const pageSize = ref(10);

//  专项脚本 分页数据
const pocTableList = computed(() => {
  const start = (pageNum.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  const data = pocCheckList.value.slice(start, end);
  return data && data.length > 0 ? data : [];
});
function changePagination(page) {
  pageNum.value = page.pageNum;
  pageSize.value = page.pageSize;
}

function submitPocs(paths, data) {
  if (paths) {
    pocCheckList.value.push(...data);
  }
}
defineExpose({
  checkPoc,
});
</script>
<style scoped lang="scss"></style>
