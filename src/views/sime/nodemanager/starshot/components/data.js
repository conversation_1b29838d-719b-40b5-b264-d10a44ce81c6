// 启动/停止状态
export function startStopStatus(row, startApi, stopApi) {
  let mes = "终止";
  let api = startApi;
  let data = {
    taskid: row.taskid,
  };
  if (!row.status.toString()) {
    //null时可以启动
    mes = "启动";
  } else {
    mes = row.status != 1 ? "启动" : "终止";
    api = row.status != 1 ? startApi : stopApi;
  }
  return {
    mes,
    api,
    data,
  };
}
//效验端口或端口范围
export function validatePortOrRange(input) {
  function normalizePort(portString) {
    const port = parseInt(portString, 10); //返回整数
    return port.toString();
  }
  if (/^\d+$/.test(input)) {
    const normalizedInput = normalizePort(input);
    if (normalizedInput !== input) {
      return false;
    }
    const port = parseInt(input, 10);
    return port >= 1 && port <= 65535;
  }
  if (/^\d+-\d+$/.test(input)) {
    const [start, end] = input.split("-").map((numberString) => {
      const normalizedNumber = normalizePort(numberString);
      if (normalizedNumber !== numberString) {
        return null;
      }
      return parseInt(numberString, 10);
    });
    if (start === null || end === null) {
      return false;
    }
    return start >= 1 && start <= 65535 && end >= 1 && end <= 65535;
  }
  //return false;
}
