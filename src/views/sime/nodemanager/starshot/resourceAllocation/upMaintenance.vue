<template>
  <el-card class="bg-p-border-new" v-loading="!loading">
    <!-- <section class="but-section"><span>在线升级</span></section> -->
    <section class="but-section" @click="getUpgrade">
      <el-icon v-if="!loading"><Loading /></el-icon><span>离线升级</span>
    </section>

    <section class="section-box">
      <el-form :model="formData" ref="ruleFormRef" :style="{ width: '100%' }">
        <section class="flex input-item-m">
          <xel-form-item v-for="item in formList" :key="item.prop" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
          <!-- <el-input
            :style="{ width: '40%', marginLeft: '20px', marginRight: '20px' }"
            :clearable="true"
            :modelValue="formData.name"
            placeholder=""
            :disabled="true"
          ></el-input> -->
        </section>
        <el-form-item label="升级日志：">
          <el-input :disabled="true" :modelValue="formData.name" :rows="2" type="textarea" placeholder="请输入专项名称"></el-input>
        </el-form-item>
        <el-form-item label="系统信息：">
          <el-input :disabled="true" :modelValue="formData.name" :rows="2" type="textarea" placeholder="请输入专项名称"></el-input>
        </el-form-item>
      </el-form>
    </section>
  </el-card>
</template>
<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { postUpgrade } from "@/api/sime/starshot/resourceAllocation.js";
import { saveFile } from "@/api/sime/siriusDis/index";
const route = useRoute();
const nodeId = ref(route.params.nodeId);
// postUpgrade (nodeId,{fileNames:文件流})
const formData = ref({ file: null });
const logFileList = ref([]);
let formList = ref([
  {
    isShow: true,
    formType: "upload",
    prop: "file",
    btnName: "上传升级包",
    limit: 1,
    fileSize: 30,
    isAccept: false,
    fileListFa: [],
    //不确定的上传文件类型
    accept: ".dat, .txt,  .doc,  .docx,  .pdf,   .xlsx , .xls, .rar,  .7Z,  .zip,  .tar ",
    onFileList(list) {
      logFileList.value = list;
      formData.name = list[0].name;
      console.log("list: ", formData.value.file, list[0].name);
    },
  },
]);
const loading = ref(true);
function getUpgrade() {
  console.log("getUpgrade: ", logFileList.value, logFileList.value[0], logFileList.value.length, "222", formData.value.file);
  if (logFileList.value.length > 0) {
    loading.value = false;
    let params = new FormData();
    params.append("nodeType", "starshot");
    params.append("methodName", "uploadRules");
    params.append("files", logFileList.value[0].raw);
    console.log("logFileList.value[0].raw: ", logFileList.value[0].raw);
    saveFile(params).then((res) => {
      console.log("res: ", res);
      if (res && res.data) {
        formData.name = res.data;
        postUpgrade(nodeId.value, {
          fileNames: res.data,
        }).then((res1) => {
          console.log("res: 111111", res1);
          loading.value = true;
        });
      }
    });
  } else {
    console.log("没有文件");
  }

  /*  先上传，获取文件名称 saveFile, 执行规则文件 uploadRules */
}
</script>
<style scoped lang="scss">
.section-box {
  padding-top: 10px;
  .input-item-m {
    margin-bottom: 20px;
  }
}
.but-section {
  width: 270px;
  background: #eeeeee;
  border-radius: 18px;
  border: 1px solid #eaeaea;
  text-align: center;
  padding: 4px 0;
  margin-bottom: 10px;
  height: 30px;
  span {
    font-weight: 400;
    font-size: 14px;
    color: #262f3d;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
}
::v-deep(.upload-demo) {
  display: flex;
  align-items: center;
  .el-upload-list__item:first-child {
    margin: 0;
  }
}
</style>
