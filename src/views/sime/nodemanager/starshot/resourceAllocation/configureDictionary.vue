<template>
  <div>
    <div class="title-bottom-line">新增字典</div>
    <section>
      <el-form :model="formData" ref="ruleFormRef">
        <el-row :gutter="18">
          <el-col :span="8">
            <el-form-item label="字典名称:"> <el-input v-model="formData.name" /> </el-form-item> </el-col
          ><el-col :span="12">
            <el-form-item label="字典:"> <el-input v-model="formData.name" /> </el-form-item>
          </el-col>
          <el-col :span="4">
            <section>
              <el-button>取消</el-button>
              <el-button @click="submit" type="primary">新增</el-button>
            </section></el-col
          >
        </el-row>
      </el-form>
    </section>
    <div class="title-bottom-line">字典列表</div>
    <section>
      <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      </common-search>
      <xel-table
        ref="tableRef"
        :columns="columns"
        :checkbox="true"
        :pagination="false"
        row-key="id"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
      </xel-table>
      <!-- 分页 -->
      <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </section>
    <div class="title-bottom-line">设置字典</div>
    <el-form :model="formData" ref="ruleFormRef">
      <el-row :gutter="18">
        <template v-for="(item, index) in items" :key="index">
          <el-col :span="6">
            <el-form-item :label="item.label" label-width="120px">
              <el-select :style="{ width: '100%', marginBottom: '10px' }" clearable v-model="item.name" placeholder="请选择脚本">
                <el-option v-for="item in optionsSelf" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
    <section class="flex-end">
      <el-button>取消</el-button>
      <el-button type="primary">保存</el-button>
    </section>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
const formData = ref({ name: "" });
let tableData = ref([]);
let formList = ref([
  {
    prop: "product",
    label: "字典名称",
  },
  {
    prop: "company",
    label: "字典",
  },
]);
/* 搜索相关 */
let searchState = reactive({
  data: {
    name: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [],
  formList: [
    {
      prop: "name",
      label: "字典名称",
    },
  ],
});
/* 列表相关 */
let columns = [
  {
    prop: "name",
    label: "字典名称",
  },
  {
    prop: "pattern",
    label: "字典内容",
  },
];
const items = ref([
  //1
  {
    label: "Ftp用户字典:",
    select: null,
  },
  {
    label: "Ftp密码字典:",
    select: null,
  },
  {
    label: "Mysql用户字典:",
    select: null,
  },
  {
    label: "Mysql密码字典:",
    select: null,
  },
  //2
  {
    label: "Orcl用户字典:",
    select: null,
  },
  {
    label: "Orcl密码字典:",
    select: null,
  },
  {
    label: "Smb用户字典:",
    select: null,
  },
  {
    label: "Smb密码字典:",
    select: null,
  },
  //3
  {
    label: "Ssh用户字典:",
    select: null,
  },
  {
    label: "Ssh密码字典:",
    select: null,
  },
  {
    label: "Rdp用户字典:",
    select: null,
  },
  {
    label: "Rdp密码字典:",
    select: null,
  },
  //4
  {
    label: "PQ用户字典:",
    select: null,
  },
  {
    label: "PQ密码字典:",
    select: null,
  },
  {
    label: "用户字典:",
    select: null,
  },
  {
    label: "密码字典:",
    select: null,
  },
]);
const optionsSelf = ref([
  { value: "1", label: "测试" },
  { value: "2", label: "测试1" },
  { value: "3", label: "测试2" },
  { value: "4", label: "测试3" },
  { value: "5", label: "测试4" },
  { value: "6", label: "测试5" },
  { value: "7", label: "测试6" },
]);
let multipleSelection = ref([]);
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};
/* 获取表格数据 */
let total = ref(0);
// 搜索
const search = () => {};
/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = { pageNum: 1, pageSize: 10 };
  paginationRef.value?.resetPageNum();
  search();
}
/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};
function submit() {
  console.log(formData.value);
}
</script>
<style scoped lang="scss"></style>
