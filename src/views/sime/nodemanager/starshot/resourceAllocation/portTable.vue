<template>
  <div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <!-- 分页 -->
    <el-table :data="tableList" @selection-change="handleSelectionChange" row-key="groupName">
      <el-table-column v-if="checkbox" type="selection" width="55" :reserve-selection="true" />
      <template v-for="item in columns" :key="item.prop">
        <el-table-column
          v-if="item.slotName == 'action'"
          :label="item.label"
          :prop="checkbox ? '' : item.prop"
          :width="checkbox ? '0px' : item.width"
        >
          <template #default="scope">
            <slot name="btnSlot" :row="scope.row" />
          </template>
        </el-table-column>
        <el-table-column v-else :show-overflow-tooltip="true" :label="item.label" :prop="item.prop" />
      </template>
    </el-table>
    <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { getPortConfigList } from "@/api/sime/starshot/resourceAllocation.js";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
const route = useRoute();
const props = defineProps({
  butList: {
    type: Array,
    default: () => [],
  },
  checkbox: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
  },
});
const nodeId = computed(() => props.id);
let tableData = ref<any>([]); //表格数据
let total = ref(0); //总条数
/* 搜索相关 */
let searchState = reactive({
  data: {
    groupName: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [],
  formList: [
    {
      prop: "groupName",
      label: "端口组名称",
    },
  ],
});
/* 列表相关 */
let columns = ref([
  {
    prop: "groupName",
    label: "端口组名称",
  },
  {
    prop: "port",
    label: "端口",
  },
  ...props.butList,
]);
//分页表格数据
const tableList = computed(() => {
  let list = tableData.value.slice((searchState.data.pageNum - 1) * searchState.data.pageSize, searchState.data.pageNum * searchState.data.pageSize);
  return list;
});
// 搜索
function search() {
  nodeId.value &&
    getPortConfigList(nodeId.value, searchState.data).then((res) => {
      /* 获取表格数据 */
      total.value = res.data.list.length;
      tableData.value = res.data.list;
    });
}

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = { pageNum: 1, pageSize: 10 };
  paginationRef.value?.resetPageNum();
  search();
}
/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

//刷新数据
function refresh() {
  ElMessage.success("操作成功");
  search();
}
let multipleSelection = ref([]);
const handleSelectionChange = (row) => {
  multipleSelection.value = row;
};

search();
// const
defineExpose({
  refresh,
  checkRow: computed(() => {
    return multipleSelection.value;
  }),
});
</script>
