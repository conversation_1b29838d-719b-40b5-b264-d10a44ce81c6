<template>
  <!-- 资源配置 -- 配置端口组/配置Poc组/配置字典/参数配置/升级维护 列表 -->
  <el-card>
    <h3 class="conH3Tit">
      {{ title }}
    </h3>
    <!-- <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="配置端口组" name="portGroups" :lazy="true"> -->
    <portGroups :id="nodeId" :key="nodeId" />
    <!-- </el-tab-pane> -->
    <!-- <el-tab-pane label="配置字典" name="web1" :lazy="true"> <configureDictionary /> </el-tab-pane> -->
    <!-- <el-tab-pane label="升级维护" name="upMaintenance" :lazy="true"> <upMaintenance /> </el-tab-pane> -->
    <!-- </el-tabs> -->
  </el-card>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import portGroups from "./portGroups.vue";
// import configureDictionary from "./configureDictionary.vue";
// import upMaintenance from "./upMaintenance.vue";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
import { useRoute } from "vue-router";
const activeName = ref("portGroups");
const route = useRoute();
const title = computed(() => {
  if (route.name == "StarshotDetail") {
    return "";
  } else {
    return route.meta && route.meta.title;
  }
});
const nodeId = ref<any>("");
const getNodeListFun = () => {
  if (route.params.nodeId) {
    nodeId.value = route.params.nodeId;
  } else {
    getNodeList("starshot").then((res) => {
      if (res.data.total > 0) {
        nodeId.value = res.data.rows[0].id;
      }
    });
  }
};
getNodeListFun();
</script>
<style scoped lang="scss"></style>
