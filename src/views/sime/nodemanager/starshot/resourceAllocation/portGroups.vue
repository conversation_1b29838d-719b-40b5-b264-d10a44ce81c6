<template>
  <div>
    <section v-hasPermi="'nodemanager:starshot:addPortConfig'" class="bg-p-border-new">
      <div class="title-bottom-line title">新增端口组</div>
      <section>
        <el-form>
          <el-row :gutter="18">
            <el-col :span="16">
              <el-form-item label="端口组名称:" prop="groupName" label-width="90px">
                <el-input v-model="groupName" placeholder="请输入端口组名称" :maxlength="50" />
              </el-form-item> </el-col
            ><el-col :span="20">
              <el-form-item label="端口/端口段:" prop="portList">
                <section class="port-box">
                  <template v-for="(item, index) in portList" :key="index">
                    <section class="port-icon">
                      <el-input class="relative" v-model="item.port" :controls="false" placeholder="请输入端口/端口段" />
                      <el-icon class="del-icon" :size="18" @click="getRemovePort(index)" v-show="portList && portList.length - 1 != 0"
                        ><RemoveFilled
                      /></el-icon>
                      <el-icon class="plus-icon" v-show="portList && portList.length - 1 == index" :size="18" @click="getAddPort"
                        ><CirclePlusFilled
                      /></el-icon>
                    </section>
                  </template>
                </section>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <section class="flex-end">
                <el-button @click="getAdd" type="primary">新增</el-button>
                <el-button @click="cancel">取消</el-button>
              </section></el-col
            >
          </el-row>
        </el-form>
      </section>
    </section>
    <section class="bg-p-border-new mt15-new">
      <div class="title-bottom-line">端口组列表</div>
      <PortTable ref="portTableRef" :butList="btnList" :id="nodeId">
        <template #btnSlot="{ row }">
          <xel-handle-btns ref="btnsRef" :btn-list="getButList(row)"></xel-handle-btns>
        </template>
      </PortTable>
    </section>
    <xel-dialog title="编辑端口组" ref="dialogRef" @submit="getEdit">
      <el-form :model="formEditData" label-width="140px" style="margin-right: 25px">
        <el-form-item label="端口组名称：">
          <el-input :clearable="true" v-model="formEditData.groupName" placeholder="请输入端口组名称"></el-input> </el-form-item
        ><el-form-item label="端口：">
          <section class="tags-box">
            <el-tag v-for="tag in portTags" :key="tag" size="default" closable :disable-transitions="false" @close="handleClose(tag)">
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputVisible"
              ref="InputRef"
              v-model="inputValue"
              style="width: 100px"
              :controls="false"
              placeholder="端口/端口段"
              @keyup.enter="handleInputConfirm"
              @blur="handleInputConfirm"
            />

            <el-button v-else class="button-new-tag" size="small" @click="showInput"> + 添加端口/端口段 </el-button>
          </section>
        </el-form-item>
      </el-form>
    </xel-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { portConfigAdd, portConfigEdit, deletePortConfig } from "@/api/sime/starshot/resourceAllocation.js";
import { useRoute } from "vue-router";
import { batchDelete } from "@/utils/delete";
import { ElMessage } from "element-plus";
import PortTable from "./portTable.vue";
const route = useRoute();
const prop = defineProps({
  id: {
    type: String,
  },
});
const nodeId = computed(() => prop.id);
const dialogRef = ref<any>(null);
const groupName = ref(""); //新增--端口组名称
const portList = ref([{ port: null }]); //新增--端口列表
let portTableRef = ref<any>([]); //表格组件
const formEditData = ref({ groupName: "", originalGroupName: "" }); //编辑弹框--表单数据
const portTags = ref([]); //编辑弹框，端口标签列表
const inputVisible = ref(false); //编辑弹框--是否显示输入框
const InputRef = ref<any>(null); //编辑弹框--端口输入框引用
const inputValue = ref(""); //编辑弹框--端口输入框值
const btnList = ref([
  {
    label: "操作",
    slotName: "action",
    width: "140px",
  },
]);
function getButList(row) {
  return [
    {
      icon: "Edit",
      title: "编辑",
      hasPermi: "nodemanager:starshot:editPortConfig",
      onClick() {
        // const row = JSON.parse(JSON.stringify(row));
        formEditData.value.groupName = row.groupName; //原端口组名称
        formEditData.value.originalGroupName = row.groupName; //原端口组名称
        portTags.value = row.port.split(",");
        nextTick(() => {
          dialogRef.value?.open();
        });
      },
    },
    {
      icon: "delete",
      title: "删除",
      hasPermi: "nodemanager:starshot:deletePortConfig",
      onClick() {
        batchDelete().then(() => {
          deletePortConfig({ ports: row.groupName, nodeId: nodeId.value }).then(() => {
            portTableRef.value?.refresh();
          });
        });
      },
    },
  ];
}
//端口组名称验证
function restrictToEnglish(value) {
  if (/[^a-zA-Z0-9]/.test(value)) {
    ElMessage.warning("端口组名称只能输入英文字符和数字");
    return false;
  }
  return true;
}
//验证端口/端口段
function validatePortOrRange(input) {
  function normalizePort(portString) {
    const port = parseInt(portString, 10); //返回整数
    return port.toString();
  }
  if (/^\d+$/.test(input)) {
    const normalizedInput = normalizePort(input);
    if (normalizedInput !== input) {
      return false;
    }
    const port = parseInt(input, 10);
    return port >= 1 && port <= 65535;
  }
  if (/^\d+-\d+$/.test(input)) {
    const [start, end] = input.split("-").map((numberString) => {
      const normalizedNumber = normalizePort(numberString);
      if (normalizedNumber !== numberString) {
        return null;
      }
      return parseInt(numberString, 10);
    });
    if (start === null || end === null) {
      return false;
    }
    return start >= 1 && start <= 65535 && end >= 1 && end <= 65535;
  }
  return false;
}
//新增-- 删除单个端口
function getRemovePort(index) {
  portList.value.splice(index, 1);
}
//新增-- 添加单个端口
function getAddPort() {
  portList.value.push({ port: null });
}
/*新建-- 取消 */
function cancel() {
  groupName.value = "";
  portList.value = [{ port: null }];
}
/* 新建--保存 */
function getAdd() {
  if (groupName.value === "" || portList.value.length == 0) {
    ElMessage.warning("请输入端口组名称和端口");
  } else {
    if (restrictToEnglish(groupName.value)) {
      let portsArray = [] as any;
      for (let v of portList.value) {
        if (v.port) {
          if (validatePortOrRange(v.port)) {
            portsArray.push(v.port);
          } else {
            ElMessage.warning("请输入正确的端口/端口段");
            return;
          }
        }
      }
      portsArray = portsArray.filter((element, index, array) => {
        return array.indexOf(element) === index;
      });
      if (portsArray.length == 0) {
        ElMessage.warning("请输入端口/端口段");
        return;
      }
      const portsString = portsArray.join(",");
      const data = {
        groupName: groupName.value,
        port: portsString,
      };
      portConfigAdd(nodeId.value, data).then(() => {
        cancel();
        portTableRef.value?.refresh();
      });
    } else {
      return;
    }
  }
}

//编辑弹框--关闭标签
const handleClose = (tag: string) => {
  portTags.value.splice(portTags.value.indexOf(tag), 1);
};

//编辑--输入框确认
const handleInputConfirm = () => {
  if (inputValue.value) {
    portTags.value.push(inputValue.value);
  }
  inputVisible.value = false;
  inputValue.value = "";
};
//编辑--添加单个端口
const showInput = () => {
  inputVisible.value = true;
  nextTick(() => {
    InputRef.value!.input!.focus();
  });
};
/* 编辑--保存 */
function getEdit(close, load) {
  const formData = JSON.parse(JSON.stringify(formEditData.value));
  if (formData.groupName === "") {
    ElMessage.warning("请输入端口组名称");
  } else {
    if (restrictToEnglish(formData.groupName)) {
      let portsArray = [] as any;
      for (let v in portTags.value) {
        if (validatePortOrRange(portTags.value[v])) {
          portsArray.push(portTags.value[v]);
        } else {
          ElMessage.warning("请输入正确的端口/端口段");
          return;
        }
      }
      portsArray = portsArray.filter((element, index, array) => {
        return array.indexOf(element) === index;
      });
      if (portsArray.length == 0) {
        ElMessage.warning("请输入端口/端口段");
        return;
      }
      const portsString = portsArray.join(",");
      const data = {
        groupName: formData.groupName,
        originalGroupName: formData.originalGroupName,
        port: portsString,
        nodeId: nodeId.value,
      };
      load();
      portConfigEdit(nodeId.value, data).then(() => {
        portTableRef.value?.refresh();
        close();
      });
    } else {
      return;
    }
  }
}
//刷新数据
</script>
<style scoped lang="scss">
.port-box {
  display: grid;
  grid-template-columns: repeat(4, 175px);
  gap: 12px 12px;
  & > section:last-child {
    width: 193px;
  }
  .port-icon {
    display: flex;
    align-items: center;
    .el-input {
      margin-right: 8px;
    }
  }
  .del-icon {
    margin-right: 8px;
    color: #aca4a4;
    cursor: pointer;
  }
  .plus-icon {
    color: #ef8936;
    cursor: pointer;
  }
}
::v-deep(.title-bottom-line) {
  font-size: 14px;
  font-weight: 500;
}
.tags-box {
  display: flex;
  gap: 10px;
  flex-wrap: wrap; //换行
  .el-tag {
    height: 30px;
    margin-bottom: 4px;
  }
  .el-button--small {
    height: 30px;
  }
}
</style>
