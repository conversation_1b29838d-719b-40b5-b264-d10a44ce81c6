<template>
  <!-- 全局搜索 -- 存活资产/开放端口/资产指纹/资产漏洞 列表 -->
  <el-card>
    <h3 class="conH3Tit">
      {{ title }}
    </h3>
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="存活资产" name="SurvivingAssets" :lazy="true">
        <SurvivingAssets :id="nodeId" :key="nodeId" :type="type + 'SurvivingAssets'" />
      </el-tab-pane>
      <el-tab-pane label="开放端口" name="OpenPort" :lazy="true"> <OpenPort :id="nodeId" :type="type + 'OpenPort'" /> </el-tab-pane>
      <el-tab-pane label="资产指纹" name="AssetFingerprint" :lazy="true">
        <AssetFingerprint :id="nodeId" :type="type + 'AssetFingerprint'" />
      </el-tab-pane>
      <el-tab-pane label="资产漏洞" name="AssetVulnerability" :lazy="true">
        <AssetVulnerability :id="nodeId" :type="type + 'AssetVulnerability'" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import SurvivingAssets from "./survivingAssets.vue";
import OpenPort from "./openPort.vue";
import AssetFingerprint from "./assetFingerprint.vue";
import AssetVulnerability from "./assetVulnerability.vue";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
import { useRoute, useRouter } from "vue-router";
const activeName = ref("SurvivingAssets");
const route = useRoute();
const type = computed(() => (route.name == "StarshotDetail" ? "nodeSurvivingAssets" : "detail"));
const title = computed(() => {
  if (route.name == "StarshotDetail") {
    return "";
  } else {
    return route.meta && route.meta.title;
  }
});
const nodeId = ref<any>("");
const getNodeListFun = () => {
  if (route.params.nodeId) {
    nodeId.value = route.params.nodeId;
  } else {
    getNodeList("starshot").then((res) => {
      if (res.data.total > 0) {
        nodeId.value = res.data.rows[0].id;
      }
    });
  }
};
getNodeListFun();
</script>
<style scoped lang="scss"></style>
