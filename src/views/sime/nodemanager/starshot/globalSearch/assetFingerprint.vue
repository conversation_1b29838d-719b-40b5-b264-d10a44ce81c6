<template>
  <!-- 全局搜索 - 资产指纹 列表 -->
  <div>
    <TableExport
      :tableApi="getAssetFingersList"
      :id="nodeId"
      :itemColumns="columns"
      :searchObj="searchState"
      :type="type"
      :hasPermi="'nodemanager:assetFingers:export'"
      urlExport="/nodemanager/starshot/assetFingers/export"
    />
  </div>
</template>
<script setup>
import { reactive, ref, watch, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import TableExport from "./tableExport.vue";
import { getAssetFingersList } from "@/api/sime/starshot/globalSearch.js";
const props = defineProps({
  taskid: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "assetFingerprint",
  },
  id: {
    type: String,
  },
});
const route = useRoute();
const nodeId = computed(() => props.id);
let searchState = reactive({
  data: {
    title: "",
    ip: "",
    port: "",
    products: "",
    banner: "",
    pageNum: 1,
    pageSize: 10,
    nodeId: nodeId.value,
    taskid: props.taskid,
    source: route.query.source,
  },
  menuData: [],
  formList: [
    {
      prop: "taskName",
      label: "任务名称",
      labelWidth: 86,
    },
    {
      prop: "ip",
      label: "IP",
      // labelWidth: 40,
    },
    {
      prop: "port",
      label: "Port",
    },
    {
      prop: "service",
      label: "服务",
      labelWidth: 86,
    },
    {
      prop: "title",
      label: "标题",
    },
    {
      prop: "products",
      label: "产品",
    },
    {
      prop: "banner",
      label: "响应",
      labelWidth: 86,
    },
  ],
});
/* 列表相关 */
let columns = [
  {
    prop: "taskName",
    label: "任务名称",
    disabled: true,
  },
  {
    prop: "taskid",
    label: "任务ID",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "assetentity",
    label: "归属实体",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "refassetentity",
    label: "关联实体",
    disabled: true,
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "netarea",
    label: "区域网络",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "ip",
    label: "IP",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "port",
    label: "Port",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "protocol",
    label: "开放协议",
    disabled: true,
  },
  {
    prop: "tunnel",
    label: "ssl plain",
    minWidth: "110",
    disabled: true,
  },
  {
    prop: "service",
    label: "服务",
    disabled: true,
  },
  {
    prop: "products",
    label: "产品",
    disabled: true,
  },
  {
    prop: "os",
    label: "操作系统",
    disabled: true,
  },
  {
    prop: "fingerextrainfo",
    label: "指纹附加信息",
    minWidth: "110",
    disabled: true,
  },
  {
    prop: "extradata",
    label: "附加信息",
  },
  {
    prop: "targeturl",
    label: "目标Url",
    disabled: true,
    sortable: "custom",
    minWidth: "95",
  },
  {
    prop: "title",
    label: "标题",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "statuscode",
    label: "响应码",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "contenttype",
    label: "内容类型",
    disabled: true,
  },
  {
    prop: "contentlength",
    label: "内容长度",
    disabled: true,
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "iconhash",
    label: "IconHash",
    sortable: "custom",
    minWidth: "105",
  },
  {
    prop: "icp",
    label: "icp",
    sortable: "custom",
  },
  {
    prop: "isp",
    label: "isp",
    sortable: "custom",
  },
  {
    prop: "city",
    label: "城市",
    sortable: "custom",
  },
  {
    prop: "province",
    label: "省份",
    sortable: "custom",
  },
  {
    prop: "country",
    label: "国家",
    sortable: "custom",
  },

  {
    prop: "fingertag",
    label: "指纹标签",
  },
  {
    prop: "category",
    label: "category",
  },
  {
    prop: "channelname",
    label: "channelname",
    minWidth: "110",
  },
  {
    prop: "identifier",
    label: "指纹识别链",
  },
  {
    prop: "parentcategory",
    label: "parentcategory",
    minWidth: "114",
  },
  {
    prop: "rspurl",
    label: "响应URL",
  },
  {
    prop: "tlsdatastr",
    label: "证书",
  },
  {
    prop: "hostname",
    label: "主机名/域名",
    minWidth: "120",
    sortable: "custom",
  },
  {
    prop: "cname",
    label: "CName",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "banner",
    label: "tcp响应banner",
    minWidth: "120",
  },
  {
    prop: "bodystr",
    label: "web响应内容",
    minWidth: "120",
  },
  {
    prop: "headerstr",
    label: "响应头",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    sortable: "custom",
    minWidth: "100",
  },
];
</script>
<style scoped lang="scss"></style>
