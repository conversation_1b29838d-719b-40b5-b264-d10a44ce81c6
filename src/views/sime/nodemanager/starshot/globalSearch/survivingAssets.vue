<template>
  <div>
    <!-- 全局搜索 - 存活资产 列表 -->
    <TableExport
      :tableApi="getAssetHostsList"
      :id="nodeId"
      :itemColumns="columns"
      :searchObj="searchState"
      :type="type"
      :hasPermi="'nodemanager:assetHosts:export'"
      urlExport="/nodemanager/starshot/assetHosts/export"
    />
  </div>
</template>
<script setup>
import { reactive, ref, watch, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import TableExport from "./tableExport.vue";
import { getAssetHostsList } from "@/api/sime/starshot/globalSearch.js";
const props = defineProps({
  id: {
    type: String,
    default: "",
  },
  taskid: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "survivingAssets",
  },
});

const route = useRoute();
const nodeId = computed(() => props.id);
const searchState = reactive({
  data: {
    hostname: "",
    ip: "",
    assetentity: "",
    pageNum: 1,
    pageSize: 10,
    nodeId: nodeId.value,
    taskid: props.taskid,
    source: route.query.source,
  },
  menuData: [],
  formList: [
    {
      prop: "taskName",
      label: "任务名称",
      labelWidth: 86,
    },
    {
      prop: "ip",
      label: "IP",
      labelWidth: 40,
    },
    {
      prop: "hostname",
      label: "主机名/域名",
      labelWidth: 100,
    },
    {
      prop: "assetentity",
      label: "归属实体",
      labelWidth: 86,
    },
  ],
});
/* 列表相关 */
const columns = [
  {
    prop: "taskName",
    label: "任务名称",
    disabled: true,
  },
  {
    prop: "taskid",
    label: "任务ID",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "assetentity",
    label: "归属实体",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "refassetentity",
    label: "关联实体",
    sortable: "custom",
    disabled: true,
    minWidth: "100",
  },
  {
    prop: "netarea",
    label: "区域网络",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "ip",
    label: "IP",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "city",
    label: "城市",
    sortable: "custom",
  },
  {
    prop: "province",
    label: "省份",
    sortable: "custom",
  },
  {
    prop: "country",
    label: "国家",
    sortable: "custom",
  },
  {
    prop: "icp",
    label: "icp",
    sortable: "custom",
  },
  {
    prop: "isp",
    label: "isp",
    sortable: "custom",
  },
  {
    prop: "hostname",
    label: "主机名/域名",
    disabled: true,
    minWidth: "120",
    sortable: "custom",
  },
  {
    prop: "cname",
    label: "CName",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "extradata",
    label: "附加信息",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    sortable: "custom",
    disabled: true,
    minWidth: "100",
  },
];
</script>
<style scoped lang="scss"></style>
