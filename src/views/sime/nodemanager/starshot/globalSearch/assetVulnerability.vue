<template>
  <div>
    <!-- 全局搜索 - 资产漏洞 列表 -->
    <TableExport
      :tableApi="getVulnsList"
      :id="nodeId"
      :itemColumns="columns"
      :searchObj="searchState"
      :type="type"
      :isVuln="true"
      :hasPermi="'nodemanager:vulns:export'"
      urlExport="/nodemanager/starshot/vulns/export"
    />
  </div>
</template>
<script setup>
import { reactive, ref, watch, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import TableExport from "./tableExport.vue";
import { getVulnsList } from "@/api/sime/starshot/globalSearch.js";
const props = defineProps({
  taskid: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "assetVulnerability",
  },
  id: {
    type: String,
  },
});
const route = useRoute();
const nodeId = computed(() => props.id);
let searchState = reactive({
  data: {
    title: "",
    ip: "",
    port: "",
    products: "",
    vulname: "",
    vulextrainfo: "",
    pageNum: 1,
    pageSize: 10,
    nodeId: nodeId.value,
    taskid: props.taskid,
    source: route.query.source,
  },
  menuData: [],
  formList: [
    {
      prop: "taskName",
      label: "任务名称",
    },
    {
      prop: "ip",
      label: "IP",
    },
    {
      prop: "service",
      label: "服务",
    },
    {
      prop: "vulextrainfo",
      label: "漏洞利用信息",
    },
    {
      prop: "port",
      label: "Port",
    },
    {
      prop: "title",
      label: "标题",
    },
    {
      prop: "products",
      label: "产品",
    },
    {
      prop: "vulname",
      label: "漏洞",
    },
  ],
});

/* 列表相关 */
let columns = [
  {
    prop: "taskName",
    label: "任务名称",
    disabled: true,
  },
  {
    prop: "taskid",
    label: "任务ID",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "assetentity",
    label: "归属实体",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "refassetentity",
    label: "关联实体",
    disabled: true,
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "netarea",
    label: "区域网络",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "ip",
    label: "IP",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "port",
    label: "Port",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "service",
    label: "服务",
    disabled: true,
  },
  {
    prop: "products",
    label: "产品",
    disabled: true,
  },
  {
    prop: "vulnurl",
    label: "漏洞URL",
    disabled: true,
    sortable: "custom",
    minWidth: "95",
  },
  {
    prop: "pocid",
    label: "POC ID",
  },
  {
    prop: "pocname",
    label: "POC名称",
  },
  {
    prop: "vulname",
    label: "漏洞名称",
    disabled: true,
    sortable: "custom",
    minWidth: "95",
  },
  {
    prop: "level",
    label: "漏洞等级",
    disabled: true,
    sortable: "custom",
    minWidth: "95",
  },
  {
    prop: "weakpass",
    label: "弱密码",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "confidence",
    label: "置信度",
  },
  {
    prop: "vulextrainfo",
    label: "漏洞利用信息",
    minWidth: "105",
    disabled: true,
  },
  {
    prop: "verifiedinfo",
    label: "验证信息",
    disabled: true,
  },
  {
    prop: "payloadinfo",
    label: "Payload信息",
    hide: true,
  },
  {
    prop: "vulnInfo",
    label: "漏洞信息",
  },
  {
    prop: "tags",
    label: "漏洞标签",
    sortable: "custom",
    minWidth: "95",
  },
  {
    prop: "rule",
    label: "查询规则",
  },
  {
    prop: "description",
    label: "漏洞描述",
  },
  {
    prop: "mitigation",
    label: "缓解措施",
  },
  {
    prop: "reference",
    label: "漏洞关联地址或文档",
    minWidth: "135",
  },
  {
    prop: "requestdata",
    label: "请求数据",
    hide: true,
  },
  {
    prop: "responsedata",
    label: "响应数据",
    hide: true,
  },
  {
    prop: "reviewer",
    label: "Poc审核人",
  },
  {
    prop: "reviewremark",
    label: "Poc审核备注",
    sortable: "custom",
    minWidth: "115",
  },
  {
    prop: "os",
    label: "操作系统",
  },
  {
    prop: "protocol",
    label: "开放协议",
  },
  {
    prop: "tunnel",
    label: "ssl plain",
  },
  {
    prop: "fingerextrainfo",
    label: "指纹附加信息",
    minWidth: "110",
  },
  {
    prop: "extradata",
    label: "附加信息",
  },
  {
    prop: "targeturl",
    label: "目标URL",
    sortable: "custom",
    minWidth: "95",
  },
  {
    prop: "title",
    label: "标题",
    disabled: true,
    sortable: "custom",
  },
  {
    prop: "statuscode",
    label: "目标响应码",
    sortable: "custom",
    minWidth: "110",
  },
  {
    prop: "contenttype",
    label: "目标响应类型",
    sortable: "custom",
    minWidth: "120",
  },
  {
    prop: "contentlength",
    label: "目标响应长度",
    sortable: "custom",
    minWidth: "120",
  },
  {
    prop: "iconhash",
    label: "iconhash",
    sortable: "custom",
    minWidth: "105",
  },
  {
    prop: "icp",
    label: "icp",
    sortable: "custom",
  },
  {
    prop: "isp",
    label: "isp",
    sortable: "custom",
  },
  {
    prop: "city",
    label: "城市",
    sortable: "custom",
  },
  {
    prop: "province",
    label: "省份",
    sortable: "custom",
  },
  {
    prop: "country",
    label: "国家",
    sortable: "custom",
  },
  {
    prop: "fingertag",
    label: "指纹标签",
  },
  {
    prop: "category",
    label: "category",
  },
  {
    prop: "channelname",
    label: "channelname",
    minWidth: "110",
  },
  {
    prop: "identifier",
    label: "指纹识别链",
    hide: true,
  },
  {
    prop: "parentcategory",
    label: "parentcategory",
    minWidth: "114",
  },
  {
    prop: "rspurl",
    label: "响应URL",
  },
  {
    prop: "headerstr",
    label: "http响应头",
    hide: true,
  },
  {
    prop: "tlsdatastr",
    label: "证书",
    hide: true,
  },
  {
    prop: "hostname",
    label: "主机名/域名",
    minWidth: "120",
    sortable: "custom",
  },
  {
    prop: "cname",
    label: "CName",
    sortable: "custom",
    minWidth: "100",
  },
  {
    prop: "bodystr",
    label: "web响应内容",
    minWidth: "120",
    hide: true,
  },
  {
    prop: "banner",
    label: "tcp响应banner",
    minWidth: "120",
    hide: true,
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    sortable: "custom",
    minWidth: "100",
  },
];
</script>
<style scoped lang="scss"></style>
