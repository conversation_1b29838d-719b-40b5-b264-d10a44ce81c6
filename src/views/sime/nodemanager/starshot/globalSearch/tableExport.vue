<template>
  <!-- 全局搜索  列表 -->
  <div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <!-- 选择展示哪一列 -->
      <el-button class="search-button" :disabled="total == 0" v-hasPermi="hasPermi" @click="clickCheck('export')">
        <el-icon :size="12"> <Download /> </el-icon>导出
      </el-button>
      <el-button class="search-button" @click="clickCheck('check')"> 选择列 </el-button>
    </common-search>
    <div v-if="showColumns.length > 0" class="bg-p-border-new mt15-new">
      <xel-table
        ref="tableRef"
        v-loading="loading"
        :key="showColumns"
        :columns="showColumns"
        :pagination="false"
        :data="tableData"
        @sort-change="chageTable"
      >
      </xel-table>
      <!-- 分页 -->
      <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </div>
    <!-- 选择列 -->
    <xel-dialog
      :title="isExport ? '导出' : '选择列'"
      ref="dialogRef"
      width="50%"
      buttonCancel="取消"
      :buttonDetermine="isExport ? '导出' : '确定'"
      @submit="enableSubmit"
      @close="cancel"
    >
      <section v-if="isExport" style="padding: 0 15px 0 20px">
        <el-form label-width="130px">
          <el-form-item label="导出类型：" style="width: 100%">
            <el-radio-group v-model="allExportValue" style="width: 100%">
              <el-radio :label="true">导出全部</el-radio>
              <el-radio :label="false">导出选中</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="!allExportValue" label="选择导出列：" style="width: 100%">
            <el-checkbox-group v-model="checkedExportValue">
              <VueDraggableNext class="dragArea list-group choseFiled w-full" :list="checkedExportList" group="site" :sort="true">
                <el-checkbox v-for="(item, index) in checkedExportList" :key="index" :label="item.prop">{{ item.label }}</el-checkbox>
              </VueDraggableNext>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </section>
      <section v-else style="padding: 0 10px 0 30px">
        <el-checkbox-group v-model="checkedValue">
          <VueDraggableNext class="dragArea list-group choseFiled w-full" :list="checkedList" group="site" :sort="true">
            <el-checkbox v-for="(item, index) in checkedList" :key="index" :disabled="item.disabled" :label="item.prop">{{ item.label }}</el-checkbox>
          </VueDraggableNext>
        </el-checkbox-group>
      </section>
    </xel-dialog>
    <xel-dialog title="查看详情" ref="dialogDetailsRef" height="450px" width="70%" :showCancel="false" :showSubmit="false">
      <el-form :model="detailsDate" label-position="left" class="base-info-form">
        <template v-for="(item, key) in detailsColumns" :key="key">
          <el-form-item v-if="!item.slotName" :label="item.label + ':'" :labelWidth="176">
            <span> {{ detailsDate[item.prop] }} </span>
          </el-form-item>
        </template>
      </el-form>
    </xel-dialog>
  </div>
</template>

<script setup name="">
import { reactive, ref, watch, onMounted, computed, nextTick } from "vue";
import { VueDraggableNext } from "vue-draggable-next";
import { download } from "@/plugins/request";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
import { getVulnsDetail } from "@/api/sime/starshot/globalSearch.js";
const route = useRoute();
const props = defineProps({
  taskid: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "survivingAssets",
  },
  id: {
    type: String,
  },
  tableApi: {
    type: Function,
    default: () => {},
  },
  hasPermi: {
    type: String,
    default: "",
  },
  itemColumns: {
    type: Array,
    default: () => [],
  },
  searchObj: {
    type: Object,
    default: () => {},
  },
  urlExport: {
    type: String,
    default: "",
  },
  isVuln: {
    type: Boolean,
    default: false,
  }, // 是否是漏洞详情
});
const nodeId = computed(() => props.id);
let tableRef = ref();
let tableData = ref([]);

/* 搜索相关 */
let searchState = reactive(props.searchObj);
const dialogDetailsRef = ref();
const detailsDate = ref();
let btnList = ref([
  {
    label: "操作",
    prop: "action",
    disabled: true,
    slotName: "actionBtns",
    fixed: "right",
    btnList: [
      {
        icon: "view",
        title: "详情",
        onClick(scope) {
          if (props.isVuln) {
            // 资产漏洞
            getVulnsDetail({ id: scope.row.id }).then((res) => {
              detailsDate.value = res.data;
              dialogDetailsRef.value?.open();
            });
          } else {
            detailsDate.value = scope.row;
            dialogDetailsRef.value?.open();
          }
        },
      },
    ],
  },
]);
//排序
const sortObj = ref({});
function chageTable(column) {
  if (column.order) {
    sortObj.value = {
      order: column.prop,
      by: column.order === "ascending" ? "asc" : "desc",
    };
  } else {
    sortObj.value = {};
  }
  search();
}

const showColumns = ref([]); // 展示的列
const detailsColumns = ref(props.itemColumns); // 详情展示的列

const localCheckedValue = ref([]); // 选中列 本地存储
const checkedList = ref(props.itemColumns);
const checkedValue = ref([]);

// 点击选择列
const dialogRef = ref();
const isExport = ref(false);
const checkedExportValue = ref([]);
const checkedExportList = ref(checkedList.value.filter((item) => !item.hide));
const allExportValue = ref(true);
// 打开选择列
function clickCheck(type) {
  if (type == "check") {
    isExport.value = false;
  } else {
    // 点击导出选择列
    isExport.value = true;
    allExportValue.value = true;
    checkedExportValue.value = [];
  }
  checkedList.value = JSON.parse(localStorage.getItem(props.type + "list"));
  checkedExportList.value = checkedList.value.filter((item) => !item.hide);
  nextTick(() => {
    dialogRef.value?.open();
  });
}
// 本地存储
onMounted(() => {
  // 如果是漏洞详情，先过滤掉 hide=true 的列
  if (props.isVuln) {
    checkedList.value = props.itemColumns.filter((item) => !item.hide);
  }

  // localStorage.clear(); //清除缓存
  const storedValue = localStorage.getItem(props.type);
  const listValue = localStorage.getItem(props.type + "list");
  const data = JSON.parse(storedValue);
  const list = JSON.parse(listValue);
  if (data && data.length > 0) {
    localCheckedValue.value = data;
    checkedValue.value = localCheckedValue.value;
    if (list && list.length > 0) {
      checkedList.value = list;
    }
  } else {
    //没有缓存时默认展示固定字段
    checkedList.value.forEach((item) => {
      if (item.disabled) {
        localCheckedValue.value.push(item.prop);
      }
    });
    checkedValue.value = localCheckedValue.value;
  }
  getShowColumns();
});
watch(
  () => localCheckedValue.value,
  (newValue) => {
    localStorage.setItem(props.type, JSON.stringify(newValue));
    localStorage.setItem(props.type + "list", JSON.stringify(checkedList.value));
    getShowColumns();
  },
  {
    deep: true,
  }
);

// 获取展示列
function getShowColumns() {
  // 创建一个映射表，用于快速查找props.itemColumns中的元素
  const itemColumnMap = new Map(props.itemColumns.map((item) => [item.prop, item]));
  // 按照localCheckedValue.value的顺序筛选并排序
  const filteredItems = localCheckedValue.value.map((checkedProp) => itemColumnMap.get(checkedProp)).filter((item) => item);

  // 按照localCheckedValue.value的顺序对filteredItems进行排序
  const sortedItems = filteredItems.sort((a, b) => {
    const indexA = localCheckedValue.value.indexOf(a.prop);
    const indexB = localCheckedValue.value.indexOf(b.prop);
    return indexA - indexB;
  });
  // 合并btnList.value
  showColumns.value = [...sortedItems, ...btnList.value];
}
const exportFileds = computed(() => {
  return checkedExportList.value
    .map((item) => {
      if (checkedExportValue.value.includes(item.prop)) {
        return item.prop;
      }
    })
    .filter((item) => item !== undefined);
});
// 选择列 保存
function enableSubmit(close, load) {
  if (isExport.value) {
    let url = props.urlExport;
    let params = {
      nodeId: nodeId.value,
      ...searchState.data,
    };
    delete params.pageNum;
    delete params.pageSize;
    if (allExportValue.value) {
      params.fileds = checkedList.value.map((item) => item.prop);
    } else {
      if (exportFileds.value.length == 0) {
        ElMessage.warning("请先选择导出列");
        return;
      } else {
        params.fileds = exportFileds.value;
      }
    }
    load();
    console.log(" 60000 * 3: ", 60000 * 3);
    download(url, "导出文件", null, "post", params, 60000 * 3).finally(() => {
      close();
    });
  } else {
    getDraggableChecked();
    close();
  }
}
function getDraggableChecked() {
  let valueList = [];
  checkedList.value.forEach((v) => {
    if (checkedValue.value.includes(v.prop)) {
      valueList.push(v.prop);
      return v.prop;
    }
  });
  localCheckedValue.value = valueList;
}
// 取消选择列
function cancel() {
  checkedValue.value = localCheckedValue.value;
}
/* 获取表格数据 */
let total = ref(0);
const loading = ref(false);
// 搜索
function search() {
  if (nodeId.value) {
    loading.value = true;
    props.tableApi({ ...searchState.data, ...sortObj.value }).then((res) => {
      total.value = res.data.total;
      tableData.value = res.data.rows;
      loading.value = false;
    });
  }
}
// 分页
const paginationRef = ref();
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};
/* 重置 */
function reset() {
  searchState.data = { pageNum: 1, pageSize: 10, nodeId: nodeId.value, taskid: props.taskid, source: route.query.source };
  paginationRef.value?.resetPageNum();
  search();
}
search();
</script>
<style lang="scss" scoped>
.base-info-form {
  margin: 0 20px;
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    // margin-left: 20px;
  }
}
</style>
