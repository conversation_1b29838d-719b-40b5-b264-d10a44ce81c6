<template>
  <!-- 指纹维护 - web探测包 列表 -->
  <div>
    <common-search
      v-model="searchState.data"
      :labelWidth="48"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <template #form>
        <xel-form-item
          label="时间"
          type="datetimerange"
          form-type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model:start="searchState.data.startTime"
          v-model:end="searchState.data.endTime"
        ></xel-form-item>
      </template>
      <el-button v-if="!checkbox" @click="handAdd" class="search-button" v-hasPermi="'nodemanager:starshot:addWebFinger'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <section class="bg-p-border-new mt15-new">
      <el-table :data="tableData" @selection-change="handleSelectionChange" row-key="id">
        <!-- 专项任务新增弹框时使用 -->
        <el-table-column v-if="checkbox" type="selection" width="55" :selectable="selectable" :reserve-selection="true" />
        <template v-for="item in columns" :key="item.prop">
          <el-table-column
            v-if="item.slotName == 'action'"
            :label="checkbox ? '' : item.label"
            :prop="checkbox ? '' : item.prop"
            :width="checkbox ? '0px' : item.width"
          >
            <template #default="scope">
              <xel-handle-btns v-if="!checkbox" ref="btnsRef" :btn-list="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
            </template>
          </el-table-column>
          <el-table-column v-else :width="item.width" :label="item.label" :prop="item.prop" :show-overflow-tooltip="true">
            <template #default="scope">
              <span v-if="item.slotName == 'enableSlot'"> {{ scope.row.enable ? "启用" : "禁用" }}</span>
              <span v-else-if="item.slotName == 'checkerSlot'"> {{ getLabel(scope.row.checker) }}</span>
              <span v-else> {{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <!-- 分页 -->
      <xel-pagination ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </section>
    <!--  新增弹窗 -->
    <AddWebDia ref="addWebDiaRef" :title="title" :nodeId="nodeId" :id="webId" @submit="search" />
  </div>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import AddWebDia from "./addWebDia.vue";
import { getWebFinger, updateWebFinger } from "@/api/sime/starshot/fingerprint.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from "vue-router";
import { getDicts } from "@/api/sime/config/dict";
const route = useRoute();
const props = defineProps({
  // 是否显示复选框  专项任务新增弹框时使用
  checkbox: {
    type: Boolean,
    default: false,
  },
  // 已选择的数据  专项任务新增弹框时使用
  checkList: {
    type: Array,
    default: () => [],
  },
  id: {
    type: String,
  },
});
const nodeId = computed(() => props.id);
let tableRef = ref();
let tableData = ref([]);
const webId = ref(null);
const checkerList = ref([]);
function getCheckerList() {
  getDicts("nodemanager_starshot_webFingerChecker").then((res) => {
    checkerList.value = res.data;
  });
}
getCheckerList();
// 获取字典 Label
function getLabel(value) {
  const data = checkerList.value.find((item) => item.dictValue == value);
  return data ? data.dictLabel : "";
}
/* 搜索相关 */
let searchState = reactive({
  data: {
    enable: "",
    tag: "",
    company: "",
    product: "",
    category: "",
    startTime: "",
    endTime: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态",
      prop: "enable",
      options: [],
      dictName: "nodemanager_starshot_enable",
      sime: true,
    },
  ],
  formList: [
    {
      prop: "tag",
      label: "标签",
    },
    {
      prop: "product",
      label: "产品",
    },
    {
      prop: "company",
      label: "公司",
    },
    {
      prop: "category",
      label: "类别",
    },
  ],
});
/* 列表相关 */
let columns1 = [
  {
    prop: "level",
    label: "等级",
  },
  {
    prop: "softHard",
    label: "SoftHard",
  },
  {
    prop: "tag",
    label: "标签",
  },
  {
    prop: "checker",
    label: "检查器",
    slotName: "checkerSlot",
  },
  {
    prop: "product",
    label: "产品",
  },
  {
    prop: "company",
    label: "公司",
  },
  {
    prop: "category",
    label: "类别",
  },
  {
    prop: "parentCategory",
    label: "父类",
  },
  {
    prop: "condition",
    label: "匹配条件",
  },
  {
    prop: "enable",
    label: "状态",
    slotName: "enableSlot",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    width: "150",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "action",
    width: "190px",
  },
];
let columns2 = [
  {
    prop: "tag",
    label: "标签",
  },

  {
    prop: "product",
    label: "产品",
  },
  {
    prop: "company",
    label: "公司",
  },
  {
    prop: "category",
    label: "类别",
  },
  {
    prop: "enable",
    label: "状态",
    slotName: "enableSlot",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    width: "150",
  },
];
const columns = props.checkbox ? columns2 : columns1;

function getBtnList(row) {
  return [
    {
      icon: "Edit",
      title: "编辑",
      hasPermi: "nodemanager:starshot:editWebFinger",
      onClick() {
        title.value = "编辑";
        webId.value = row.id;
        nextTick(() => {
          addWebDiaRef.value.open();
        });
      },
    },
    {
      icon: "document-copy",
      title: "复制",
      hasPermi: "nodemanager:starshot:addWebFinger",
      onClick() {
        title.value = "复制";
        webId.value = row.id;
        nextTick(() => {
          addWebDiaRef.value.open("copy");
        });
      },
    },
    {
      icon: "CircleCheck",
      title: "启用",
      hide: row.enable ? true : false,
      hasPermi: "nodemanager:starshot:updateWebFingerEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
    {
      icon: "Remove",
      title: "禁用",
      hide: row.enable ? false : true,
      hasPermi: "nodemanager:starshot:updateWebFingerEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
  ];
}
//修改状态
function enableRemoveCircleCheck(id, enable) {
  const mes = enable ? "禁用" : "启用";
  ElMessageBox.confirm(`确认${mes}选中的数据项？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    updateWebFinger(nodeId.value, {
      id: id,
      enable: enable,
    }).then(() => {
      ElMessage.success(mes + "成功");
      search();
    });
  });
}
/* 获取表格数据 */
let total = ref(0);
const search = () => {
  if (nodeId.value) {
    getWebFinger(nodeId.value, searchState.data).then((res) => {
      tableData.value = [];
      total.value = res.data.list.data.total;
      tableData.value = res.data.list.data.rows;
    });
  }
};

/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = {
    pageNum: 1,
    pageSize: 10,
  };
  paginationRef.value?.resetPageNum();
  search();
}

/* 新增相关 */
const addWebDiaRef = ref();
let title = ref("");
const handAdd = () => {
  title.value = "新建";
  webId.value = null;
  nextTick(() => {
    addWebDiaRef.value.open();
  });
};
search();

// 选择框
let multipleSelection = ref([]);
const handleSelectionChange = (row) => {
  multipleSelection.value = row;
};

// 判断是否可选
function selectable(row) {
  return !props.checkList.includes(row.id);
}
defineExpose({
  search,
  checkRow: computed(() => {
    return multipleSelection.value;
  }),
});
</script>
<style scoped></style>
