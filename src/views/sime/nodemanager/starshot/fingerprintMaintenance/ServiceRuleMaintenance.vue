<template>
  <el-tabs v-model="activeName" class="demo-tabs">
    <el-tab-pane label="服务指纹" name="serviceFinger" :lazy="true">
      <ServiceFinger />
    </el-tab-pane>
    <el-tab-pane label="服务探测包" name="service" :lazy="true">
      <ServiceProbe />
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import { ref } from "vue";
import ServiceFinger from "./serviceFinger.vue";
import WebFinger from "./webFinger.vue";
const activeName = ref("serviceFinger");
</script>
<style scoped lang="scss"></style>
