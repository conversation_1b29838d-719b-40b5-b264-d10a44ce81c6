<template>
  <xel-dialog :title="title + '服务指纹'" ref="dialogRef" width="50%" buttonCancel="取消" buttonDetermine="保存" @submit="submit" @close="cancel">
    <el-form style="padding-right: 40px" :model="formData" ref="ruleFormRef" label-width="140px">
      <xel-form-item
        class="item-flex"
        label="探测包"
        v-model="formData.serviceProbeName"
        width="85%"
        :disabled="disabledProbe"
        placeholder="请选择探测包"
        :required="true"
      >
        <el-button class="ml-4" @click="getCheckProbe">选择</el-button>
        <el-button class="ml-4" @click="handServiceAdd">新建</el-button>
      </xel-form-item>
      <xel-form-item v-for="(item, index) in formList1" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xel-dialog>
  <!--  选择探测包弹窗 -->
  <detectionDia ref="detectionDiaRef" :nodeId="nodeId" :id="fingerId" @close="search" @getCheck="getCheck" />
  <!--  新增探测包弹窗 -->
  <addServiceDia ref="addFingerDiaRef" title="新增" :nodeId="nodeId" :id="null" @submit="submitService" />
</template>

<script setup>
import { editServiceFinger, detailsServiceFinger, addServiceFinger } from "@/api/sime/starshot/fingerprint.js";
import { ElMessage } from "element-plus";
import { ref } from "vue";
import detectionDia from "../components/detectionDia.vue";
import addServiceDia from "../detectionPackageMaintenance/addServiceDia.vue";

let props = defineProps({
  title: {
    type: String,
    default() {
      return "新建";
    },
  },
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
  /* 任务类型 */
  id: {
    type: String,
    default() {
      return "";
    },
  },
});
const detectionDiaRef = ref(null); //选择探测包弹窗
const disabledProbe = ref(true); //是否禁用
const addFingerDiaRef = ref(null); //新增探测包弹窗
//打开选择探测包弹窗
function getCheckProbe() {
  detectionDiaRef.value.open();
}
const formData = ref({});

let formList1 = ref([
  {
    prop: "service",
    label: "服务名称",
    required: true,
    maxlength: "100",
  },
  {
    formType: "input",
    prop: "pattern",
    required: true,
    label: "匹配Pattern",
    type: "textarea",
  },
  {
    formType: "input",
    prop: "versionInfo",
    required: true,
    label: "版本信息",
    type: "textarea",
  },
  {
    formType: "switch",
    prop: "state",
    label: "启用",
    sime: true,
    dictName: "nodemanager_starshot_enable",
  },
  {
    formType: "select",
    prop: "patternmode",
    label: "匹配模式",
    sime: true,
    dictName: "nodemanager_starshot_serviceFingerPatternmode",
  },
]);
let dialogRef = ref();
//选择探测包后，回显
function getCheck(row) {
  formData.value.serviceProbeName = row.name;
  formData.value.probeId = row.probeId;
}
const ruleFormRef = ref(null);
const emit = defineEmits(["submit"]);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.parse(JSON.stringify(formData.value));
      data.enable = data.state ? true : false;
      const api = props.title === "编辑" ? editServiceFinger : addServiceFinger;
      const mes = props.title === "编辑" ? "修改成功" : "添加成功";
      if (!formData.value.probeId) {
        ElMessage.warning("请选择探测包");
        return false;
      }

      const param = {
        service: data.service,
        pattern: data.pattern,
        versionInfo: data.versionInfo,
        probeId: data.probeId,
        enable: data.enable,
        patternmode: data.patternmode ? data.patternmode : "",
      };
      if (props.title == "编辑") {
        param.id = data.id;
      }
      api(props.nodeId, param).then((res) => {
        ElMessage.success(mes);
        dialogRef.value.close();
        emit("submit");
      });
    } else {
      return;
    }
  });
}

function open(name) {
  if (props.id) {
    detailsServiceFinger(props.nodeId, {
      id: props.id,
    }).then((res) => {
      formData.value = res.data.serviceFinger;
      formData.value.state = res.data.serviceFinger.enable ? 1 : 0;
      if (name) {
        formData.value.service = res.data.serviceFinger.service + "--复制";
      }
    });
  }
  dialogRef.value.open();
}
/* 新增探测包弹窗 */
// 打开新增服务探测包弹窗
const handServiceAdd = () => {
  addFingerDiaRef.value?.open();
};
// 关闭新增服务探测包弹窗
function cancel() {
  formData.value = {};
}
// 新建服务探测包回调
function submitService(row) {
  formData.value.serviceProbeName = row.name;
  formData.value.probeId = row.probeId;
}
defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.detailDiv {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .titleSpan {
    flex: 1;
    font-weight: bold;
    display: inline-block;
    text-align: end;
    font-size: 15px;
  }
  .conSpan {
    position: relative;
    top: 2px;
    font-size: 15px;
    flex: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.btn {
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
}
.ml-4 {
  margin-left: 4px;
}
::v-deep(.item-flex) {
  .el-form-item__content {
    display: flex;
    justify-content: space-between;
  }
}
</style>
