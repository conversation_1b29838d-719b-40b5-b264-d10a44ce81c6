<template>
  <!-- 指纹维护 - 服务探测包 列表 -->
  <div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <template #form>
        <xel-form-item
          label="更新时间"
          type="datetimerange"
          form-type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model:start="searchState.data.startTime"
          v-model:end="searchState.data.endTime"
        ></xel-form-item>
      </template>

      <el-button @click="handAdd" class="search-button" v-hasPermi="'nodemanager:starshot:addServiceFinger'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>

    <section class="bg-p-border-new mt15-new">
      <xel-table ref="tableRef" v-loading="loading" :columns="columns" :checkbox="false" :pagination="false" :data="tableData">
        <template #enableSlot="{ row }"> {{ row.enable ? "启用" : "禁用" }} </template>
        <template #patternmodeSlot="{ row }"> {{ row.patternmode == "i" ? "忽略大小写" : row.patternmode == "s" ? "换行匹配" : "" }} </template>
        <template #action="scope">
          <!-- 操作列按钮组插槽 -->
          <xel-handle-btns ref="btnsRef" :btn-list="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
        </template>
      </xel-table>

      <!-- 分页 -->
      <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </section>
    <!--  新增弹窗 -->
    <addFingerDia ref="addFingerDiaRef" :title="title" :nodeId="nodeId" :id="fingerId" @submit="search" />
  </div>
</template>

<script setup>
import { nextTick, reactive, ref, computed } from "vue";
import addFingerDia from "./addFingerDia.vue";
import { getServiceFinger, updateServiceFinger } from "@/api/sime/starshot/fingerprint.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from "vue-router";
const route = useRoute();
const prop = defineProps({
  id: {
    type: String,
  },
});
const nodeId = computed(() => prop.id);
let tableRef = ref();
let tableData = ref([]);
const fingerId = ref(null); //新增弹窗id
/* 搜索相关 */
let searchState = reactive({
  data: {
    enable: "",
    service: "",
    startTime: "",
    endTime: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态",
      prop: "enable",
      options: [],
      dictName: "nodemanager_starshot_enable",
      sime: true,
    },
  ],
  formList: [
    {
      prop: "service",
      label: "服务名称",
    },
  ],
});

/* 列表相关 */
let columns = [
  {
    prop: "service",
    label: "服务名称",
  },
  {
    prop: "pattern",
    label: "Pattern",
  },
  {
    prop: "versionInfo",
    label: "版本信息",
  },
  {
    prop: "serviceProbeName",
    label: "探测包名称",
  },
  {
    prop: "patternmode",
    label: "匹配模式",
    slotName: "patternmodeSlot",
  },
  {
    prop: "enable",
    label: "状态",
    slotName: "enableSlot",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    width: "150px",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "action",
    width: "190px",
  },
];
// 操作按钮
function getBtnList(row) {
  return [
    {
      icon: "Edit",
      title: "编辑",
      hasPermi: "nodemanager:starshot:editServiceFinger",
      onClick() {
        title.value = "编辑";
        fingerId.value = row.id;
        nextTick(() => {
          addFingerDiaRef.value.open();
        });
      },
    },
    {
      icon: "document-copy",
      title: "复制",
      hasPermi: "nodemanager:starshot:addServiceFinger",
      onClick() {
        title.value = "复制";
        fingerId.value = row.id;
        nextTick(() => {
          addFingerDiaRef.value.open("copy");
        });
      },
    },
    {
      icon: "CircleCheck",
      title: "启用",
      hide: row.enable ? true : false,
      hasPermi: "nodemanager:starshot:updateServiceFingerEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
    {
      icon: "Remove",
      title: "禁用",
      hide: row.enable ? false : true,
      hasPermi: "nodemanager:starshot:updateServiceFingerEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
  ];
}
//修改状态
function enableRemoveCircleCheck(id, enable) {
  const mes = enable ? "禁用" : "启用";
  ElMessageBox.confirm(`确认${mes}选中的数据项？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    updateServiceFinger(nodeId.value, {
      id: id,
      enable: enable,
    }).then(() => {
      ElMessage.success(mes + "成功");
      search();
    });
  });
}
/* 获取表格数据 */
let total = ref(0);
const loading = ref(false);
const search = () => {
  if (nodeId.value) {
    loading.value = true;
    getServiceFinger(nodeId.value, searchState.data).then((res) => {
      tableData.value = [];
      total.value = res.data.list.data.total;
      tableData.value = res.data.list.data.rows;
      loading.value = false;
    });
  }
};

/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = { pageNum: 1, pageSize: 10 };
  paginationRef.value?.resetPageNum();
  search();
}

/* 新增相关 */
const addFingerDiaRef = ref();
let title = ref("");
const handAdd = () => {
  title.value = "新建";
  fingerId.value = null;
  nextTick(() => {
    addFingerDiaRef.value.open();
  });
};
reset();
</script>
<style scoped></style>
