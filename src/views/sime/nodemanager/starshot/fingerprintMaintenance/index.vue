<template>
  <!-- 服务规则维护 -->
  <el-card>
    <h3 class="conH3Tit">
      {{ title }}
    </h3>
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane v-if="ifServiceFinger" label="服务指纹" name="serviceFinger" :lazy="true">
        <ServiceFinger :id="nodeId" :key="nodeId" />
      </el-tab-pane>
      <el-tab-pane v-if="ifWebFinger" label="服务探测包" name="webFinger" :lazy="true">
        <serviceProbe :id="nodeId" :key="nodeId" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";
import ServiceFinger from "./serviceFinger.vue";
// import WebFinger from "./webFinger.vue";
import serviceProbe from "../detectionPackageMaintenance/serviceProbe.vue";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
import { useRoute } from "vue-router";
import hasPermi from "@/utils/hasPermi.js";
const route = useRoute();
const title = computed(() => {
  if (route.name == "StarshotDetail") {
    return "";
  } else {
    return route.meta && route.meta.title;
  }
});
const ifServiceFinger = computed(() => {
  return route.name == "StarshotDetail" ? true : hasPermi("nodemanager:starshot:listServiceFinger");
});
const ifWebFinger = computed(() => {
  return route.name == "StarshotDetail" ? true : hasPermi("nodemanager:starshot:listServiceProbe");
});
const activeName = ref(ifServiceFinger.value ? "serviceFinger" : "webFinger");
const nodeId = ref<any>("");
const getNodeListFun = () => {
  if (route.params.nodeId) {
    nodeId.value = route.params.nodeId;
  } else {
    getNodeList("starshot").then((res) => {
      if (res.data.total > 0) {
        nodeId.value = res.data.rows[0].id;
      }
    });
  }
};
getNodeListFun();
</script>
<style scoped lang="scss"></style>
