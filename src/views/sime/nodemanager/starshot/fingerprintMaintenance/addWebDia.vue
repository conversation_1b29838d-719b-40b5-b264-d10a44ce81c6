<template>
  <xel-dialog :title="title + 'WEB指纹'" ref="dialogRef" width="50%" buttonCancel="取消" buttonDetermine="保存" @submit="submit" @close="cancel">
    <el-form style="padding-right: 40px" :model="formData" ref="ruleFormRef" label-width="140px">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xel-dialog>
</template>

<script setup>
import { editWebFinger, detailsWebFinger, addWeb<PERSON>inger } from "@/api/sime/starshot/fingerprint.js";
import { ElMessage } from "element-plus";
import { ref } from "vue";

let props = defineProps({
  title: {
    type: String,
    default() {
      return "新建";
    },
  },
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
  /* 任务类型 */
  id: {
    type: String,
    default() {
      return "";
    },
  },
});

let formList = ref([
  {
    prop: "level",
    label: "等级",
    dictName: "nodemanager_starshot_webFingerLevel",
    sime: true,
    options: [],
    formType: "select",
    required: true,
  },
  {
    prop: "product",
    label: "产品",
    required: true,
  },
  {
    prop: "softHard",
    label: "SoftHard",
    formType: "number",
    max: 1000,
    min: 0,
    precision: 0,
    placeholder: "请输入SoftHard",
  },
  {
    prop: "company",
    label: "公司",
  },
  {
    prop: "checker",
    label: "检查器",
    dictName: "nodemanager_starshot_webFingerChecker",
    sime: true,
    options: [],
    formType: "select",
  },
  {
    prop: "category",
    label: "类别",
  },
  {
    prop: "tag",
    label: "标签",
  },
  {
    prop: "parentCategory",
    label: "父类",
  },
  {
    formType: "input",
    prop: "condition",
    required: true,
    label: "匹配条件",
    type: "textarea",
    rows: 5,
  },
  {
    formType: "switch",
    prop: "state",
    label: "启用",
    switchOptions: {
      activeValue: true,
      inactiveValue: false,
    },
  },
]);
const formData = ref({ level: "7" });

let dialogRef = ref();
const emit = defineEmits(["submit"]);
const ruleFormRef = ref(null);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.parse(JSON.stringify(formData.value));
      data.enable = data.state ? true : false;
      const api = props.title === "编辑" ? editWebFinger : addWebFinger;
      const mes = props.title === "编辑" ? "修改成功" : "添加成功";
      const param = {
        level: data.level,
        product: data.product,
        softHard: data.softHard,
        company: data.company,
        checker: data.checker,
        category: data.category,
        tag: data.tag,
        parentCategory: data.parentCategory,
        condition: data.condition,
        enable: data.enable,
      };
      if (props.title == "编辑") {
        param.id = data.id;
      }
      api(props.nodeId, param).then((res) => {
        ElMessage.success(mes);
        dialogRef.value.close();
        emit("submit");
      });
    } else {
      return;
    }
  });
}

function open(name) {
  formData.value = {};
  if (props.id) {
    detailsWebFinger(props.nodeId, {
      id: props.id,
    }).then((res) => {
      formData.value = res.data.webFinger;
      formData.value.state = res.data.webFinger.enable ? 1 : 0;
      if (name) {
        formData.value.condition = res.data.webFinger.condition + "--复制";
      }
    });
  } else {
    formData.value.level = "7";
  }
  dialogRef.value.open();
}
// 关闭弹窗
function cancel() {
  formData.value = {};
}
defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.detailDiv {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .titleSpan {
    flex: 1;
    font-weight: bold;
    display: inline-block;
    text-align: end;
    font-size: 15px;
  }
  .conSpan {
    position: relative;
    top: 2px;
    font-size: 15px;
    flex: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.btn {
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
}
.ml-4 {
  margin-left: 4px;
}
::v-deep(.item-flex) {
  .el-form-item__content {
    display: flex;
    justify-content: space-between;
  }
}
</style>
