<template>
  <el-card>
    <h3 class="conH3Tit">
      {{ title }}
    </h3>
    <el-row class="sime-layout-wrapper mr-30-new" :class="title.length > 0 ? '' : 'm-15'">
      <el-col :span="5" class="mr-15-new tree-search-new">
        <el-tree
          style="height: 100%"
          ref="treeRef"
          node-key="groupId"
          :expand-on-click-node="false"
          :default-expanded-keys="expandKeys"
          v-loading="loading"
          :data="treeList"
          :props="{ children: 'children', label: 'groupName', value: 'groupId' }"
          @node-click="nodeClick"
          @node-expand="expandClick"
        >
          <template #default="scope">
            <span :title="scope.node.label" class="custom-tree-node">
              <el-icon class="folder" v-if="scope.data.isDataNode === false" :size="14"><folder-opened /></el-icon>
              <span class="node-txt ellipsis">{{ scope.node.label }}</span>
            </span>
            <span class="el-tree-node__label" v-if="scope.node.data.ifShow && ifShowAdd">
              <el-icon :size="14" @click="modNode(scope.node.data, 'add')" title="添加">
                <plus />
              </el-icon>
            </span>
            <span class="el-tree-node__label" v-if="!scope.node.data.ifShow && ifIconShow">
              <el-icon :size="14" v-hasPermi="'nodemanager:starshot:addTreeNode'" @click="modNode(scope.node.data, 'add')" title="添加">
                <plus />
              </el-icon>
              <el-icon :size="14" v-hasPermi="'nodemanager:starshot:editTreeNode'" @click="modNode(scope.node.data, 'edit')" title="编辑">
                <Edit />
              </el-icon>
              <el-icon :size="14" v-hasPermi="'nodemanager:starshot:deleteTreeNode'" @click="delNode(scope.node.data)" title="删除">
                <Delete />
              </el-icon>
            </span>
          </template>
        </el-tree>
      </el-col>
      <el-col :span="1" class="tree-wrapper"> </el-col>

      <el-col :span="18" class="ml-15-new">
        <common-search
          :two="true"
          :labelWidth="82"
          v-model="searchState.data"
          :menu-data="searchState.menuData"
          :form-list="searchState.formList"
          @search="search"
          @reset="reset"
        >
          <el-button
            v-hasPermi="'nodemanager:starshot:move'"
            @click="getMoveDia(null)"
            :disabled="multipleSelection.length == 0"
            class="search-button"
          >
            <el-icon :size="12">
              <Setting />
            </el-icon>
            批量移动
          </el-button>
          <template #form>
            <xel-form-item label="更新时间" type="date" form-type="date" day="YYYY-MM-DD" v-model="searchState.data.updatedAtSelect"> </xel-form-item>
          </template>
        </common-search>
        <section class="bg-p-border-new mt15-new">
          <el-table ref="tableRef" :data="pocList" v-loading="tableLoading" @selection-change="handleSelectionChange" row-key="id">
            <el-table-column type="selection" width="55" :selectable="selectable" :reserve-selection="true" />
            <template v-for="item in columns" :key="item.prop">
              <el-table-column v-if="item.slotName == 'action'" :label="item.label" :prop="item.prop">
                <template #default="scope">
                  <xel-handle-btns v-if="!checkbox" ref="btnsRef" :btn-list="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
                </template>
              </el-table-column>
              <!-- :show-overflow-tooltip="!$wujie" -->
              <el-table-column v-else :label="item.label" :prop="item.prop"></el-table-column>
            </template>
          </el-table>
          <!-- <xel-table
            ref="tableRef"
            v-loading="tableLoading"
            :checkbox="true"
            row-key="id"
            :columns="columns"
            :pagination="false"
            :data="pocList"
            @selection-change="handleSelectionChange"
          >
          </xel-table> -->
          <!-- 分页 -->
          <xel-pagination
            ref="paginationRef"
            class="xel-table-pagination"
            :total="total"
            :pageSizes="[10, 20, 50, 100, 1000]"
            @change="changePagination"
          />
        </section>
      </el-col>
      <xelDialog
        :title="treeDiaTitle"
        ref="dialogRef"
        :size="size"
        :showSubmit="showNode"
        :showCancel="showNode"
        @submit="saveNodeItem"
        @close="colseItem"
      >
        <el-form :model="formData" :label-position="showNode ? 'right' : 'left'" :class="showNode ? '' : 'base-info-form'">
          <template v-if="showNode">
            <el-form-item v-if="isMoveShow" label="移动分组" prop="parentId">
              <tree-select
                v-model:value="formData.groupId"
                :options="treeList"
                :objMap="{ children: 'children', label: 'groupName', value: 'groupId' }"
                placeholder="选择移动分组"
              />
            </el-form-item>
            <xel-form-item v-else class="item-flex" label="分组名称" v-model="formData.groupName"> </xel-form-item>
          </template>
          <template v-else v-for="(item, key) in detailsItem" :key="key">
            <el-form-item :label="item + ':'" :labelWidth="136">
              <span v-if="item == '是否外带' || item == '是否经过验证' || item == '是否go代理类型'"> {{ whetherObj[detailsData[key]] }} </span>
              <span v-else> {{ detailsData[key] }} </span>
            </el-form-item>
          </template>
        </el-form>
      </xelDialog>
    </el-row>
  </el-card>
</template>
<script setup>
import { ref, nextTick, reactive, computed } from "vue";
import { batchDelete } from "@/utils/delete";
import { getPocTree, delPocTree, detailsPocDataInfo, pocDataInfoList, addPocTree, detailsPocTree, pocDataInfo } from "@/api/sime/starshot/poc.js";
import { ElMessage } from "element-plus";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
import hasPermi from "@/utils/hasPermi.js";
import TreeSelect from "@/components/sime/TreeSelect/index.vue";

const detailsData = ref({});
const route = useRoute();
const title = computed(() => {
  if (route.name == "StarshotDetail") {
    return "";
  } else {
    return route.meta && route.meta.title;
  }
});
const ifIconShow = ref(
  hasPermi("nodemanager:starshot:addTreeNode") || hasPermi("nodemanager:starshot:editTreeNode") || hasPermi("nodemanager:starshot:deleteTreeNode")
);
const ifShowAdd = ref(hasPermi("nodemanager:starshot:addTreeNode"));
/* 获取数据 */
const starshotId = ref("");
const getNodeListFun = () => {
  getNodeList("starshot").then((res) => {
    if (res.data.total > 0) {
      starshotId.value = res.data.rows[0].id;
      setTimeout(() => {
        search();
        getData();
      }, 100);
    }
  });
};
getNodeListFun();
const nodeId = computed(() => {
  if (route.params.nodeId) {
    return route.params.nodeId;
  } else if (starshotId.value) {
    return starshotId.value;
  } else {
    return null;
  }
});

//节点表单数据
const formData = ref({
  groupName: "",
});
const treeList = ref([]); //树列表
const expandKeys = ref([]); //展开的节点
const whetherObj = ref({
  true: "是",
  false: "否",
});
const treeDiaTitle = ref(""); //树节点新增-编辑弹框标题
const size = ref("small"); //树节点新增-编辑弹框大小
let loading = ref(false); //树加载状态
let treeRef = ref(); //树组件的ref
let nodeCheckId = ref(); //选中的节点id
/* 搜索相关 */
let searchState = reactive({
  data: {
    pocPath: "",
    description: "",
    transport: "",
    severity: "",
    updatedAtSelect: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [],
  formList: [
    {
      prop: "pocPath",
      label: "poc路径",
    },
    {
      prop: "description",
      label: "描述",
    },
    {
      prop: "severity",
      label: "威胁等级",
      formType: "select",
      options: [
        { label: "critical", value: "critical" },
        { label: "high", value: "high" },
        { label: "info", value: "info" },
        { label: "low", value: "low" },
        { label: "medium", value: "medium" },
        { label: "unknown", value: "unknown" },
      ],
    },
  ],
});
//poc管理详情
const detailsItem = ref({
  pocPath: "POC路径",
  author: "POC作者",
  executer: "执行引擎",
  severity: "威胁等级",
  transport: "传输层协议",
  verified: "是否经过验证",
  oob: "是否外带",
  name: "漏洞名称",
  reference: "漏洞POC相关链接",
  description: "漏洞描述",
  impact: "漏洞影响",
  remediation: "修复建议",
  gopoc: "是否go代理类型",
  cVSSMetrics: "CVSS",
  cvssScore: "CVSS分值",
  cVEID: "CVEID",
  cWEID: "CWEID",
  cPE: "CPE",
});
// poc管理列表
let columns = [
  {
    prop: "pocPath",
    label: "POC路径",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "severity",
    label: "威胁等级",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
  },
  {
    label: "操作",
    prop: "action",
    fixed: "right",
    slotName: "action",
    width: "130px",
  },
];
function getBtnList(row) {
  return [
    {
      icon: "view",
      title: "查看",
      hasPermi: "nodemanager:starshot:getPocDataInfoByPath",
      onClick() {
        treeDiaTitle.value = "查看POC管理";
        size.value = "middle";
        showNode.value = false;
        detailsPocDataInfo(nodeId.value, {
          id: row.id,
        }).then((res) => {
          detailsData.value = res.data.pocDataInfo;
          for (let key in res.data.pocDataInfo) {
            detailsData.value[key] = res.data.pocDataInfo[key];
          }
          nextTick(() => {
            dialogRef.value.open();
          });
        });
      },
    },
    {
      //pocDataInfo
      icon: "Setting",
      title: "移动",
      hasPermi: "nodemanager:starshot:move",
      onClick(scope) {
        getMoveDia(scope.row);
      },
    },
  ];
}
// 列表多选
let multipleSelection = ref([]);
function handleSelectionChange(val) {
  multipleSelection.value = val;
}
// 移动poc弹框数据
const isMoveShow = ref(false);
function getMoveDia(row) {
  showNode.value = true;
  treeDiaTitle.value = "移动POC管理分组";
  size.value = "small";
  isMoveShow.value = true;
  if (row) {
    formData.value.pocPath = row.pocPath;
    formData.value.id = row.id;
    formData.value.groupId = row.groupId; //所在组id
  } else {
    let ids = [];
    let pocPaths = [];
    multipleSelection.value.forEach((item) => {
      ids.push(item.id);
      pocPaths.push(item.pocPath);
    });
    formData.value.id = ids.join(",");
    formData.value.pocPath = pocPaths.join(",");
  }
  nextTick(() => {
    dialogRef.value.open();
  });
}
// poc管理list
const pocList = ref([]);
const total = ref(0); //分页总数
const tableLoading = ref(false);
// 搜索
function search(data = { groupId: nodeCheckId.value }) {
  if (nodeId.value) {
    tableLoading.value = true;
    pocDataInfoList(nodeId.value, { ...searchState.data, ...data }).then((res) => {
      total.value = res.data.list.data.total;
      pocList.value = res.data.list.data.rows;
      tableLoading.value = false;
    });
  }
}
search();
/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = {
    pageNum: 1,
    pageSize: 10,
  };
  paginationRef.value?.resetPageNum();
  search();
}
/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};
onBeforeRouteLeave((to, from, next) => {
  sessionStorage.setItem("expandPocNode", "");
  next();
});
getData();
// 获取树数据
const tableRef = ref();
function getData(id, echoSelectedNode = false) {
  if (nodeId.value) {
    loading.value = true;
    getPocTree(nodeId.value).then((data) => {
      console.log("tableRef.value 根据id获取node数据: ", tableRef.value);
      treeList.value = data.data.list;
      treeList.value[0]["ifShow"] = true; //第一级不显示操作按钮
      loading.value = false;
      // 是否已经有展开的节点
      let getExpandNode = sessionStorage.getItem("expandPocNode") ? JSON.parse(sessionStorage.getItem("expandPocNode")) : "";
      if (getExpandNode.length > 0) {
        let arr = [];
        getExpandNode.forEach((gItem, gIndex) => {
          arr.push(gItem.groupId);
        });
        arr.push(treeList.value[0].groupId);
      } else {
        expandKeys.value.push(treeList.value[0].groupId);
      }
      setTimeout(() => {
        if (id) {
          //编辑节点，返回时选中刚才的分组
          if (echoSelectedNode && currentClickNode) {
            nodeClick(currentClickNode);
          } else {
            currentClickNode = null;

            treeRef.value.setCurrentKey(id);
            //根据id获取node数据
            if (echoSelectedNode) {
              let node = treeRef.value.getNode(id);
              if (node && node.data) {
                nodeClick(node.data);
              }
            }
          }
        }
      }, 200);
    });
  }
}
let showNode = ref(false);
let dialogRef = ref();

// 点击箭头方法，存取展开节点
function expandClick(node) {
  let expandNode = [];
  expandNode.push({
    id: node.id,
    name: node.name,
    parentId: node.parentId,
  });
  sessionStorage.setItem("expandPocNode", JSON.stringify(expandNode));
}
// 点击节点的方法
let currentClickNode = null; //保存当前点击的节点
function nodeClick(node) {
  if (node.isGroupNode) {
    currentClickNode = JSON.parse(JSON.stringify(node));
  } else {
    currentClickNode = null;
  }
  nodeCheckId.value = node.groupId;
  // getData(node.groupId);
  search();
}

// 打开节点弹框
function saveNodeItem() {
  if (formData.value.type) {
    if (formData.value.groupName) {
      let api = formData.value.type === "add" ? addPocTree : detailsPocTree;
      let data =
        formData.value.type === "add"
          ? {
              parentId: formData.value.nodeId, //add
            }
          : {
              groupId: formData.value.nodeId, //edit
            };
      api(nodeId.value, {
        groupName: formData.value.groupName,
        ...data,
      }).then(() => {
        ElMessage.success("操作成功");
        // state.groupId = formData.value.groupId;
        getData(formData.value.nodeId);
        colseItem();
      });
    } else {
      ElMessage.warning("请输入分组名称");
    }
  } else {
    if (formData.value.id && formData.value.groupId) {
      pocDataInfo(nodeId.value, {
        groupId: formData.value.groupId,
        id: formData.value.id,
        pocPath: formData.value.pocPath,
      }).then(() => {
        ElMessage.success("操作成功");
        // getData(formData.value.nodeId);
        tableRef.value.clearSelection();
        colseItem();
        search();
      });
    } else {
      ElMessage.warning("请选择移动的分组名称");
    }
  }
}
// 添加--编辑---节点
async function modNode(node, type) {
  if (node) {
    showNode.value = true;
    formData.value.nodeId = node.groupId;
    formData.value.type = type;
    isMoveShow.value = false;
    size.value = "small";
    if (type === "add") {
      treeDiaTitle.value = "添加POC分组";
    } else {
      treeDiaTitle.value = "编辑POC分组";
      formData.value.groupName = node.groupName;
    }
    nextTick(() => {
      dialogRef.value.open();
    });
  }
}
// 删除节点
function delNode(node) {
  batchDelete().then(() => {
    delPocTree(nodeId.value, {
      groupId: node.groupId,
    }).then(() => {
      // treeRef.value.remove(node.id);
      treeRef.value.setCurrentKey(node.groupId);
      ElMessage.success("删除成功");
      // state.groupId = node.groupId;
      getData();
    });
  });
}
// 关闭弹窗
function colseItem() {
  formData.value = {};
  dialogRef.value.close();
}
</script>

<style lang="scss" scoped>
.base-info-form {
  margin: 0 20px;
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    // margin-left: 20px;
  }
}
.detail-label {
  font-size: 14px;
}
:deep(.my-label) {
  background: var(--el-color-success-light-9) !important;
}
:deep(.my-content) {
  background: var(--el-color-danger-light-9);
}
:deep.el-descriptions .detail-waps {
  .el-descriptions__label {
    font-weight: 500;
    color: #262824;
    transform: scale(0.95);
    transform-origin: left bottom;
    margin-bottom: 6px;
    font-size: 14px;
  }
}
:deep.detail-wrapper .detail-item .detail-value {
  color: #6e6e6e;
  word-break: break-all;
  white-space: pre-wrap;
  font-size: 14px;
}
.sime-layout-wrapper {
  height: max-content;
}
.m-15 {
  margin-left: -10px;
}
.tree-wrapper {
  position: relative;
  min-height: 100%;
  &::after {
    content: "";
    display: block;
    width: 1px;
    height: 100%;
    position: absolute;
    right: 50%;
    top: 0;
    bottom: 0;
    background: #ededed;
  }
}
// 选中节点样式
:deep .el-tree-node__content {
  height: 32px;
  line-height: 32px;
  position: relative;
}
:deep .is-current > .el-tree-node__content {
  // width: 138px;
  background: $bgColor;
  border-radius: 0px 100px 100px 0px;
}
:deep .is-current > .el-tree-node__content,
:deep .is-current > .el-tree-node__content > .el-tree-node__expand-icon :not(.is-leaf) {
  color: $color;
}
:deep .is-current > .el-tree-node__content:hover .el-tree-node__label {
  background: rgba(255, 255, 255);
  color: $color;
}
.custom-tree-node {
  // width: calc(100% - 100px);
  width: 100%;
  display: inline-block;
}
.el-tree-node__label {
  display: none;
  .el-icon {
    transform: translateY(2px);
    margin-right: 8px;
    &:hover {
      // color: $color;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
.el-tree-node__content:hover .el-tree-node__label {
  margin-top: 4px;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
  background: rgba(177, 177, 177);
  border-radius: 10px;
  color: #fff;
  position: absolute;
  top: 0px;
  right: 10px;
  display: inline-block;
}

.node-txt {
  display: inline-block;
  // width: calc(100% - 130px);
  margin-right: 8px;
  vertical-align: bottom;
}
.folder {
  margin-top: 10px;
  margin-right: 4px;
}
</style>
