<template>
  <xel-dialog
    :title="type == 'detail' ? '查看绑定SOSS' : '绑定至SOSS'"
    ref="dialogRef"
    height="450px"
    :size="type == 'bind' || type == 'start' ? 'large' : 'small'"
    buttonCancel="取消"
    buttonDetermine="同步"
    :showCancel="false"
    :showSubmit="false"
  >
    <!-- 未绑定 -->
    <section style="padding: 0 20px" v-if="type == 'bind' || type == 'start'">
      <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
        <el-button @click="handAdd" class="search-button" v-hasPermi="'nodemanager:starshot:addStarshotTopic'">
          <el-icon :size="12">
            <plus />
          </el-icon>
          新增
        </el-button>
      </common-search>
      <!-- :load-data="getStarshotTestTask" -->
      <xel-table ref="tableRef" v-loading="loading" :columns="columns" :data="tableData" :pagination="false">
        <template #taskTypeSlot="{ row }"> {{ row.taskType && row.taskType == "6" ? "专项安全测试任务" : "" }} </template>
        <template #actualSlot="{ row }">
          <span v-if="row.beginTime && row.endTime"> {{ row.beginTime }} 至 {{ row.endTime }} </span>
          <span v-else-if="row.beginTime"> {{ row.beginTime }} 至 --- </span>
          <span v-else-if="row.endTime"> --- 至 {{ row.endTime }} </span>
        </template>
      </xel-table>
    </section>
    <!-- 已绑定，查看数据 -->
    <section style="padding: 0 20px" v-else>
      <el-form :model="formData" class="base-info-form">
        <template v-for="(item, key) in detailsItem" :key="key">
          <el-form-item :label="item + ':'" :labelWidth="136">
            <span> {{ detailsData[key] }} </span>
          </el-form-item>
        </template>
      </el-form>
    </section>
    <xel-dialog title="新建SOSS任务" ref="dialogAddRef" width="50%" buttonCancel="取消" buttonDetermine="保存" @submit="submitAdd">
      <el-form style="padding: 0 25px 0 0" :model="formData" ref="ruleFormRef" class="form-box" label-width="140px">
        <template v-for="(item, index) in formList" :key="index">
          <el-form-item v-if="item.prop == 'unit'" :span="13" label="责任单位:"> 责任单位 </el-form-item>
          <xel-form-item v-else :span="13" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
        </template>
      </el-form>
    </xel-dialog>
  </xel-dialog>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, watch } from "vue";
import hasPermi from "@/utils/hasPermi.js";
import {
  getStarshotTestTask,
  bindingSossTask,
  getStarshotTestMatrix,
  getStarshotDeliveryManager,
  getDeptId,
  saveStarshotTestTask,
  getStarshotTestTaskById,
  syncTaskVulnsResults,
  syncTaskAssetsResults,
  syncTopicAssetsResults,
  syncTopicVulnsResults,
} from "@/api/sime/starshot/specialMapping.js";
let props = defineProps({
  type: {
    type: String,
    default() {
      return "";
    }, //bind 绑定,detail 查看,sync 同步(已取消），start 启动
  },
  taskId: {
    type: String,
    default() {
      return "";
    },
  },
  source: {
    //漏洞同步 false时显示按钮
    type: String,
    default() {
      return ""; //资产测绘:1，专项任务:2
    },
  },
  assetStatus: {
    //资产同步
    type: Boolean,
    default() {
      return false;
    },
  },
  vulnStatus: {
    //syncVulnStatus
    type: Boolean,
    default() {
      return false;
    },
  },
});
/* 搜索相关 */
let searchState = reactive({
  data: { taskName: "", pageNum: 1, pageSize: 10, deptId: 1001 },
  menuData: [],
  formList: [
    {
      prop: "taskName",
      label: "任务名称",
    },
  ],
});
const tableData = ref([]);
const columns = ref([
  {
    prop: "taskType",
    label: "任务类型",
    slotName: "taskTypeSlot",
  },
  {
    prop: "taskName",
    label: "任务名称",
  },
  {
    prop: "aa", //beginTime  endTime
    label: "测试窗口",
    slotName: "actualSlot",
  },
  {
    prop: "matrixName",
    label: "默认测试矩阵",
  },
  {
    label: "操作",
    // fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Tools",
        title: "选中",
        onClick({ row }) {
          // 确定要绑定吗，绑定之后不更改
          ElMessageBox.confirm(`确认绑定选中的数据项？绑定之后不可更改。`, "警告", {
            distinguishCancelAndClose: true,
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            bindingSossTask({
              starshotTaskId: props.taskId,
              source: props.source,
              id: row.id,
            }).then(() => {
              ElMessage.success("操作成功");
              handSubmit("asset", ifAssetShow.value);
              handSubmit("vuln", ifVulnShow.value);
              nextTick(() => {
                dialogRef.value.close();
                if (props.type == "start") {
                  emit("submit", "start"); //需要打开启动弹框
                } else {
                  emit("submit");
                }
              });
            });
          });
        },
      },
    ],
  },
]);
let total = ref(0);
const loading = ref(false);
const search = () => {
  loading.value = true;
  getStarshotTestTask({
    ...searchState.data,
    taskTypeArray: searchState.data.taskTypeArray ? searchState.data.taskTypeArray : "",
    deptId: searchState.data.deptId,
  }).then((res) => {
    total.value = res.data.total;
    tableData.value = res.data.rows;
    loading.value = false;
  });
};
const reset = () => {
  searchState.data = { taskName: "", pageNum: 1, pageSize: 10 };
  search();
};
const formData = ref({});
const testMatrixOptions = ref([]);
function getDelivery() {
  // 获取交付经理
  getStarshotDeliveryManager().then((res) => {
    deliveryManagerOptions.value = res.data.data.analystList.map((v) => {
      return {
        label: v.nickName,
        value: v.userId,
      };
    });
    if (detailsData.value && detailsData.value.deliveryManager) {
      detailsData.value.deliveryManagerUserName = deliveryManagerOptions.value.find((item) => item.value == detailsData.value.deliveryManager).label;
    }
  });
}
function getOptions() {
  // 获取测试矩阵列表
  getStarshotTestMatrix().then((res) => {
    testMatrixOptions.value = res.data.data.rows.map((v) => {
      return {
        label: v.matrixName,
        value: v.id,
      };
    });
  });
  // 获取查询测试资产 -单位
  getDeptId().then((res) => {
    formData.value = res.data;
    searchState.data.deptId = res.data.customerNo; //查询
  });
  getDelivery();
}
const deliveryManagerOptions = ref([]);

// 任务起止
function getTimeRange(val) {
  if (val && val.length > 0) {
    formData.value.beginTime = val[0];
    formData.value.endTime = val[1];
  } else {
    formData.value.beginTime = "";
    formData.value.endTime = "";
  }
}
const formList = ref([
  {
    prop: "customerName",
    label: "责任单位", //禁填
    disabled: true,
  },
  {
    prop: "taskName",
    label: "任务名称",
    required: true,
  },
  //时间待定，
  {
    prop: "timeRange",
    label: "测试起止时间",
    formType: "daterange",
    type: "daterange", //年月日  beginTime  endTime
    day: true,
    onChange: (val) => {
      formData.value.timeRange = val;
      getTimeRange(val);
    },
    required: true,
  },

  {
    prop: "deliveryManager",
    label: "交付经理", //选择
    formType: "select",
    required: true,
    options: deliveryManagerOptions.value,
    filterable: true,
  },

  {
    prop: "testMatrix",
    label: "测试矩阵", //单选框
    formType: "select",
    required: true,
    options: testMatrixOptions.value,
  },
  {
    prop: "taskType",
    label: "任务类型", //单选框
    formType: "radio",
    options: [{ label: "专项安全测试任务", value: "6" }],
    required: true,
    dictName: "nodemanager_starshot_sossTaskType",
    sime: true,
  },
  {
    formType: "input",
    prop: "description",
    label: "任务说明",
    type: "textarea",
    rows: 3,
  },
]);
const detailsItem = ref({
  customName: "责任单位",
  taskName: "任务名称",
  beginTime: "测试开始时间",
  endTime: "测试截止时间",
  deliveryManagerUserName: "交付经理",
  matrixName: "测试矩阵",
  taskType: "任务类型",
  description: "任务说明",
});
const detailsData = ref({});

let dialogRef = ref();
const dialogAddRef = ref();
function handAdd() {
  dialogAddRef.value.open();
  formList.value.find((v) => {
    if (v.prop == "deliveryManager") {
      v.options = deliveryManagerOptions.value;
    }
    if (v.prop == "testMatrix") {
      v.options = testMatrixOptions.value;
    }
  });
}

const ruleFormRef = ref();
function submitAdd() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.parse(JSON.stringify(formData.value));
      const params = {
        starshotTaskId: props.taskId,
        taskName: data.taskName,
        taskType: data.taskType,
        testMatrix: data.testMatrix,
        beginTime: data.beginTime,
        endTime: data.endTime,
        description: data.description,
        deliveryManager: data.deliveryManager,
        deptId: data.customerNo,
        source: props.source,
      };
      // 确定要绑定吗，绑定之后不更改
      ElMessageBox.confirm(`确认保存并绑定当前SOSS任务？绑定之后不可更改。`, "警告", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        saveStarshotTestTask(params).then((res) => {
          ElMessage.success("操作成功");
          handSubmit("asset", ifAssetShow.value);
          handSubmit("vuln", ifVulnShow.value);
          nextTick(() => {
            dialogAddRef.value.close();
            dialogRef.value.close();
            if (props.type == "start") {
              emit("submit", "start");
            } else {
              emit("submit");
            }
          });
        });
      });
    }
  });

  // dialogRef.value.close();
}
const ifAssetShow = ref(hasPermi(props.source == "1" ? "nodemanager:starshottask:syncAssetsResults" : "nodemanager:starshottopic:syncAssetsResults"));
const ifVulnShow = ref(hasPermi(props.source == "1" ? "nodemanager:starshottask:syncVulnsResults" : "nodemanager:starshottopic:syncVulnsResults"));

const emit = defineEmits(["submit"]);
function handSubmit(type, ifShow) {
  if (ifShow) {
    const isAssetTask = props.source === "1";
    const apiMap = {
      asset: isAssetTask ? syncTopicAssetsResults : syncTaskAssetsResults,
      vuln: isAssetTask ? syncTopicVulnsResults : syncTaskVulnsResults,
    };

    const api = apiMap[type];
    api({ taskid: props.taskId }, true);
  }
}
function open() {
  formData.value = {};
  if (props.type == "bind" || props.type == "start") {
    getOptions();
    search();
  } else {
    getStarshotTestTaskById({ id: props.taskId }).then((res) => {
      detailsData.value = res.data;
      detailsData.value.taskType = res.data.taskType == "6" ? "专项安全测试任务" : "";
      getDelivery();
    });
  }
  nextTick(() => {
    dialogRef.value.open();
  });
}

defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.detailDiv {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .titleSpan {
    flex: 1;
    font-weight: bold;
    display: inline-block;
    text-align: end;
    font-size: 15px;
  }
  .conSpan {
    position: relative;
    top: 2px;
    font-size: 15px;
    flex: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.btn {
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
}
.ml-4 {
  margin-left: 4px;
}
::v-deep(.item-flex) {
  .el-form-item__content {
    display: flex;
    justify-content: space-between;
  }
}
.base-info-form {
  margin: 0 20px;
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    margin-bottom: 10px;
    padding-bottom: 10px;
  }
}
</style>
