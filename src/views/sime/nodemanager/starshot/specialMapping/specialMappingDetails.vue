<template>
  <div>
    <SpecialDetails :taskid="taskid" :nodeId="nodeId" :type="name" />
  </div>
</template>
<script setup>
import { ref } from "vue";
import SpecialDetails from "../components/specialDetails.vue";
import { useRoute } from "vue-router";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
const route = useRoute();
const taskid = ref(route.query.taskid);
const name = ref(route.name);
const nodeId = ref("");
const getNodeListFun = () => {
  if (route.params.nodeId) {
    nodeId.value = route.params.nodeId;
  } else {
    getNodeList("starshot").then((res) => {
      if (res.data.total > 0) {
        nodeId.value = res.data.rows[0].id;
      }
    });
  }
};
getNodeListFun();
</script>
<style scoped lang="scss"></style>
