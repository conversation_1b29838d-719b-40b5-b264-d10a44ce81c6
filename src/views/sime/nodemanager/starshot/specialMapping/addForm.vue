<template>
  <el-card class="bg-p-border-new">
    <h3 class="conH3Tit">
      {{ "" || ($route.meta && $route.meta.title) }}
    </h3>
    <section class="section-box2">
      <el-form :model="formData" ref="ruleFormRef" label-width="170px" style="width: 85%" label-position="right">
        <template v-for="(item, index) in formList1" :key="index">
          <!-- 高级参数设定 -->
          <el-form-item v-if="item.prop == 'customParameters'" label="高级参数设定:" prop="portList">
            <section class="port-box">
              <template v-for="(item, index) in customParametersList" :key="index">
                <section class="flex flex-between relative">
                  <el-select
                    :style="{ width: '49%', marginBottom: '10px' }"
                    clearable
                    filterable
                    v-model="item.param"
                    placeholder="请选择参数名"
                    @change="changeParameter($event, index, optionsSelf)"
                  >
                    <el-option v-for="ite in optionsSelf" :key="ite.dictValue" :label="ite.dictLabel" :value="ite.dictLabel"></el-option>
                  </el-select>
                  <el-input
                    :style="{ width: '49%', marginBottom: '10px' }"
                    class="ml-10"
                    :clearable="true"
                    v-model="item.value"
                    placeholder="请输入参数值"
                  ></el-input>
                  <section class="buts-m">
                    <el-button
                      class=""
                      :style="{ marginBottom: '10px' }"
                      :disabled="customParametersList && customParametersList.length == 1"
                      @click="deleteFun(index)"
                      >删除</el-button
                    >
                    <el-button type="primary" class="buts-m" v-show="customParametersList && customParametersList.length - 1 == index" @click="getAdd"
                      >新增</el-button
                    >
                  </section>
                </section>
                <span v-if="item.remarks" class="remarks-span">提示： {{ item.remarks }} </span>
              </template>
            </section>
          </el-form-item>
          <el-form-item v-else-if="item.prop == 'hostsSlot'" label="IP/IP:PORT/IP段:" label-position="top">
            <section class="hosts-urls">
              <el-form-item style="width: 45%" label="" label-position="top">
                <el-input v-model="formData.hosts" type="textarea" :rows="5" placeholder="请输入IP/IP:PORT/IP段，格式：换行分隔"> </el-input>
              </el-form-item>
              <el-form-item style="width: 53%" label="URL:" label-width="50px" label-position="top">
                <el-input v-model="formData.urls" type="textarea" :rows="5" placeholder="请输入URL，格式：换行分隔"> </el-input>
              </el-form-item>
            </section>
          </el-form-item>
          <template v-else-if="item.prop == 'switchSlot'">
            <section class="switch-box">
              <xel-form-item v-model="formData.ondns1" formType="switch" label="启用DNS查询"> </xel-form-item>
              <xel-form-item v-model="formData.nobr1" formType="switch" label="禁用服务弱口令检测"> </xel-form-item>
              <xel-form-item v-model="formData.sntypeFlag1" formType="switch" label="禁用探测常用端口"> </xel-form-item>
              <xel-form-item v-model="formData.pocnameonly1" formType="switch" label="自定义探测POC" @change="pocnameonlyChange"> </xel-form-item>
            </section>
          </template>
          <!-- 选择poc -->
          <template v-else-if="item.prop == 'poc' && !item.hide">
            <div class="poc-box">
              <p>选择POC</p>
              <checkPoc :nodeId="nodeId" ref="checkPocRef" :pocCheckData="pocCheckList" />
            </div>
          </template>
          <template v-else-if="item.prop == 'pocname' && !item.hide">
            <div class="poc-box">
              <p>自定义探测POC</p>
              <el-button @click="getPocGroup" class="after-port">引用POC组</el-button>
              <el-input
                style="margin-top: 10px"
                v-model="formData.pocname"
                type="textarea"
                :rows="5"
                placeholder="请输入自定义探测POC，格式：英文逗号分隔"
              >
              </el-input>
            </div>
            <!-- <el-form-item label="自定义探测POC:" label-width="170px" label-position="right">
              <el-button @click="getPocGroup" class="after-port">引用POC组</el-button>
              <el-input v-model="formData.pocname" type="textarea" :rows="5" placeholder="请输入自定义探测POC，格式：英文逗号分隔"> </el-input>
            </el-form-item> -->
          </template>
          <!-- <el-form-item label="POC：">
            <checkPoc :nodeId="nodeId" ref="checkPocRef" :pocCheckData="pocCheckList" />
          </el-form-item> -->
          <!-- 扫描端口 -->
          <template v-else-if="item.prop == 'ports'">
            <el-form-item label="扫描端口:" label-width="170px" label-position="right">
              <el-button class="after-port" @click="getPorts">引用端口</el-button>
              <el-tag
                v-for="tag in portGroupTags"
                :key="tag"
                class="port-group-tags"
                size="default"
                closable
                :disable-transitions="false"
                @close="handleClose(tag)"
              >
                {{ tag }}
              </el-tag>
              <el-input v-model="formData.ports" type="textarea" :rows="15" placeholder="请输入扫描端口，格式：英文逗号分隔"> </el-input>
            </el-form-item>
          </template>
          <xel-form-item v-else-if="!item.hide" v-model="formData[item.prop]" v-bind="item">
            <!-- 参数模版展示提示 -->
            <template v-if="item.remarks">
              <span class="remarks-span">
                {{ item.remarks }}
              </span>
            </template>
            <!-- 扫描模式展示提示 -->
            <template v-if="item.scanchainRemark">
              <span v-if="scanchainRemarks[formData[item.prop]]" class="remarks-span"> 提示：{{ scanchainRemarks[formData[item.prop]] }} </span>
            </template>
          </xel-form-item>
        </template>
      </el-form>
      <section class="flex-center" style="margin-top: 20px">
        <el-button @click="cancel">取消</el-button>
        <el-button v-if="ifBtnShow" :style="{ display: ifBtnShow ? 'block' : 'none' }" type="primary" @click="submit">保存</el-button>
      </section>
    </section>
    <xel-dialog :title="isReferencePort ? '引用端口' : '引用POC组'" ref="dialogRef" @submit="getEdit">
      <PortTable style="padding: 0 20px" v-if="isReferencePort" ref="portTableRef" :id="nodeId" :checkbox="true" />
      <section v-else style="padding: 0 20px">
        <el-form>
          <el-form-item label="POC组：">
            <tree-select
              v-model:value="pocValue"
              :options="treeList"
              :objMap="{ children: 'children', label: 'groupName', value: 'groupId' }"
              placeholder="选择POC组"
          /></el-form-item>
        </el-form>
      </section>
    </xel-dialog>
  </el-card>
</template>

<script setup>
import { validatePortOrRange } from "../components/data.js";
import PortTable from "../resourceAllocation/portTable.vue";
import { ElMessage } from "element-plus";
import { ref, computed, watchEffect } from "vue";
import { useRouter, useRoute } from "vue-router";
import { specialTaskEdit, specialTaskDetail, specialTaskAdd } from "@/api/sime/starshot/specialMapping.js";
import { pocDataInfoList } from "@/api/sime/starshot/poc.js";
import { getPortConfigList } from "@/api/sime/starshot/resourceAllocation.js";
import { getDicts } from "@/api/sime/config/dict";
import { useStore } from "vuex";
import { rules } from "@/xelComponents/utils/formValidator";
import { getPocTree } from "@/api/sime/starshot/poc.js";
import TreeSelect from "@/components/sime/TreeSelect/index.vue";
import checkPoc from "../components/checkPoc.vue";
const store = useStore();
const prop = defineProps({
  type: {
    type: String,
  },
});
const router = useRouter();
const route = useRoute();
const isNode = computed(() => route.name.includes("Node"));
const ifBtnShow = computed(() => !route.query.syncSoss);
const nodeId = computed(() => {
  return route.params.nodeId;
});
const taskid = computed(() => {
  return route.query.taskid;
});
//扫描端口下拉列表
const customParametersList = ref([{ param: null, value: "", remarks: "" }]); //高级参数设定
const optionsPorts = ref([]); //高级参数设定--参数名
function getportList() {
  getPortConfigList(nodeId.value, {}).then((res) => {
    console.log(res);
    optionsPorts.value = res.data.list.map((item) => {
      return { label: item.groupName, value: item.groupName };
    });
    formList1.value[1].options = optionsPorts.value;
  });
}
getportList();
const optionsSelf = ref([]); //高级参数设定--参数名
// 获取字典列表 高级参数设定--参数名
function getDictsList() {
  getDicts("nodemanager_starshot_taskCustomParam").then((res) => {
    optionsSelf.value = res.data;
  });
}
getDictsList();
const treeList = ref([]);
function getData() {
  if (nodeId.value) {
    getPocTree(nodeId.value).then((data) => {
      treeList.value = data.data.list;
    });
  }
}
getData();
const pocCheckList = ref([]);
if (prop.type != "add" && taskid.value) {
  specialTaskDetail({ taskid: taskid.value, nodeId: nodeId.value }).then((res) => {
    formData.value = res.data;
    // formData.value.enable1 = res.data.enable ? 1 : 0; //是否启用
    formData.value.pocnameonly1 = res.data.pocnameonly ? 1 : 0; //自定义探测Poc
    formData.value.ondns1 = res.data.ondns ? 1 : 0; //启用DNS查询
    formData.value.nobr1 = res.data.nobr ? 1 : 0; //服务弱口令检测
    formData.value.sntypeFlag1 = res.data.sntypeFlag ? 1 : 0; //禁用探测常用端口
    customParametersList.value =
      res.data.customParametersVos && res.data.customParametersVos.length > 0 ? res.data.customParametersVos : [{ param: null, value: "" }]; //高级参数设定
    // formData.value.custom1 = res.data.user || res.data.password ? 1 : 0; //自定义弱口令爆破參数
    // customShow(formData.value.custom1, "user", "password");
    portGroupTags.value = res.data.portGroup ? res.data.portGroup.split(",") : [];
    pocnameonlyChange(formData.value.pocnameonly, "pocname");
    pocCheckList.value = res.data?.pocDataInfos ? res.data.pocDataInfos : [];
    //customParametersVos:
    // 扫描端口,判断是否是自定义端口
    if (prop.type == "copy") {
      formData.value.entity = res.data.entity + "--复制";
    }
  });
}
const ruleFormRef = ref(null);
//默认端口
const defaultPorts =
  "21,22,80,81,135,139,443,445,1433,1521,2022,2222,2375,2379,3306,3390,5432,6379,7001,8000,8848,8080,8089,9000,9200,9999,10250,10255,10050,10051,11211,22022,27017,27018,30000,33389,33890,33899,50070,50080,60010,60030";
const bigdataPorts =
  "80,2181,2379,3000,4040,5601,6379,7180,8080,8088,8081,8161,8443,8888,9000,9083,9200,9300,9876,9877,9090,10002,10050,10051,10911,10912,10909,11211,12379,15601,15672,20080,20880,20881,20888,25000,25010,25020,27017,27018,30000,50070,50080,60010,60030";
const k8sPorts = "2375,7777,9000,10250,10255";
const webPorts =
  "80,81,82,83,84,85,86,87,88,89,90,91,92,98,99,443,800,801,808,880,888,889,1000,1010,1080,1081,1082,1099,1118,1888,2008,2020,2100,2375,2379,3000,3008,3128,3505,5555,6080,6648,6868,7000,7001,7002,7003,7004,7005,7007,7008,7070,7071,7074,7078,7080,7088,7200,7680,7687,7688,7777,7890,8000,8001,8002,8003,8004,8006,8008,8009,8010,8011,8012,8016,8018,8020,8028,8030,8038,8042,8044,8046,8048,8053,8060,8069,8070,8080,8081,8082,8083,8084,8085,8086,8087,8088,8089,8090,8091,8092,8093,8094,8095,8096,8097,8098,8099,8100,8101,8108,8118,8161,8172,8180,8181,8200,8222,8244,8258,8280,8288,8300,8360,8443,8448,8484,8800,8834,8838,8848,8858,8868,8879,8880,8881,8888,8899,8983,8989,9000,9001,9002,9008,9010,9043,9060,9080,9081,9082,9083,9084,9085,9086,9087,9088,9089,9090,9091,9092,9093,9094,9095,9096,9097,9098,9099,9100,9200,9443,9448,9800,9981,9986,9988,9998,9999,10000,10001,10002,10004,10008,10010,10250,12018,12443,14000,16080,18000,18001,18002,18004,18008,18080,18082,18088,18090,18098,19001,20000,20720,21000,21501,21502,28018,20880";
const formData = ref({
  scanchain: "auto",
  entity: "",
  paratpls: "internet",
  ports: `${defaultPorts},${bigdataPorts},${k8sPorts},${webPorts}`,
  sntypeFlag1: 0,
  hosts: "",
  urls: "",
});
let formList1 = ref([
  {
    prop: "entity",
    label: "任务名称",
    required: true,
    maxlength: "100",
  },
  {
    formType: "input",
    prop: "ports",
    label: "扫描端口",
    type: "textarea",
    rows: 15,
    // required: true,
    placeholder: "请输入扫描端口，格式：英文逗号分隔",
  },
  //扫描目标：IP/IP:PORT/IP段，URL 必须填写一个

  {
    prop: "hostsSlot",
    label: "IP/IP:PORT/IP段/IP掩码",
  },
  //扫描设置
  {
    prop: "scanchain",
    label: "扫描模式",
    formType: "select",
    required: true,
    options: [
      { label: "icmp", value: "icmp" },
      { label: "portscan", value: "portscan" },
      { label: "finger", value: "finger" },
      { label: "auto", value: "auto" },
      // { label: "fixedport", value: "fixedport" },
    ],
    scanchainRemark: true,
  },
  {
    prop: "paratpls",
    label: "参数模版",
    formType: "select",
    required: true,
    options: [
      { label: "互联网", value: "internet" },
      // { label: "互联网同步es", value: "internet_toes" },
      { label: "局域网", value: "intranet" },
      // { label: "局域网同步es", value: "intranet_toes" },
    ],
    remarks: "提示：根据此值查找预置参数值自动设置相关扫描参数",
  },
  {
    prop: "customParameters",
    label: "高级参数设定", //选择框：参数名，输入框：参数值，新增按钮
    //提示：
  },
  {
    prop: "user",
    label: "爆破用户名",
  },
  {
    prop: "password",
    label: "爆破密码",
  },
  {
    prop: "switchSlot",
    label: "启用DNS查询",
  },
  {
    //选择 pocnameonly 时展示
    formType: "input",
    prop: "pocname",
    label: "自定义探测POC",
    type: "textarea",
    rows: 5, //新增 删除 表单
    hide: true, //自定义探测Poc
    placeholder: "请输入自定义探测POC，格式：英文逗号分隔",
    //提示：
  },
  {
    prop: "poc",
    label: "POC",
    hide: true, //自定义探测Poc
    formType: "input",
  },
  // {
  //   formType: "switch",
  //   prop: "custom1",
  //   label: "自定义弱口令爆破參数",
  //   onChange: (val) => {
  //     customShow(val, "user", "password");
  //   },
  // },
]);
//自定义弱口令爆破参数 控制显示
function customShow(val, name1, name2) {
  if (val) {
    checkItemHide(false, name1);
    checkItemHide(false, name2);
  } else {
    checkItemHide(true, name1);
    checkItemHide(true, name2);
  }
}
function pocnameonlyChange(val) {
  customShow(val, "pocname", "poc");
}
// 展示隐藏表单项
function checkItemHide(ifShow, prop) {
  const index = formList1.value.findIndex((item) => item.prop == prop);
  formList1.value[index].hide = ifShow;
} //禁用表单项
function checkItemDisabled(ifShow, prop) {
  const index = formList1.value.findIndex((item) => item.prop == prop);
  formList1.value[index].disabled = ifShow;
}
//扫描模式备注
const scanchainRemarks = {
  icmp: "仅进行icmp存活探测",
  portscan: "扫描链执行 icmp->portscan",
  finger: "扫描链执行 icmp-portscan->finger",
  auto: "扫描链执行 icmp->portscan->finger->poc扫描",
  fixedport: "icmp -> 指定存活端口(不进行端口扫描，指定-p为存活端口)->finger->poc扫描",
};
//高级参数设定
//选择参数名,显示备注
// key=value
function changeParameter(val, index, data) {
  const item = data.find((v) => v.dictLabel == val);
  customParametersList.value[index].remarks = item ? item.remark : ""; //取消时也注销备注
}
//高级参数设定--新增单条
function getAdd() {
  customParametersList.value.push({ param: null, value: "" });
}
//高级参数设定--删除单条
function deleteFun(index) {
  customParametersList.value.splice(index, 1);
}
const portTableRef = ref(null); //端口组件列表
const dialogRef = ref(null); //端口弹框
const isReferencePort = ref(""); //是否是引用端口
const pocValue = ref("");

function getPocGroup() {
  isReferencePort.value = false;
  pocValue.value = "";
  dialogRef.value?.open();
}
function getPorts() {
  dialogRef.value?.open();
  isReferencePort.value = true;
}
const portGroupTags = ref([]); //端口组标签列表
//删除端口组
function handleClose(tag) {
  portGroupTags.value.splice(portGroupTags.value.indexOf(tag), 1);
}
function getEdit() {
  if (isReferencePort.value) {
    const rows = portTableRef.value?.checkRow;
    portGroupTags.value = [];
    const ports = [];
    for (let item of rows) {
      if (item.port && item.port.split(",").length > 500) {
        portGroupTags.value.push(item.groupName);
      } else {
        ports.push(item.port);
      }
    }
    // const inputValue = formData.value.ports.trim();
    // if (inputValue && (inputValue.length != 0 || inputValue.indexOf(",") > -1)) {
    //   formData.value.ports = `${inputValue},${ports}`;
    // } else {
    formData.value.ports = ports.join(",");
    // }
  } else {
    getPocList();
  }
  dialogRef.value?.close();
}
//获取poc列表
function getPocList() {
  if (pocValue.value) {
    pocDataInfoList(nodeId.value, { groupId: pocValue.value }).then((res) => {
      formData.value.pocname = res.data.list.data.rows.map((item) => item.pocPath).join(",");
    });
  }
}
//取消
function cancel() {
  let name = isNode.value ? "StarshotDetail" : "SpecialMapping";
  router.push({
    name,
    params: {
      nodeId: nodeId.value,
    },
  });
  store.commit("closeCurrentTab");
}

//验证IP/IP:PORT/IP段
function validateIPOrRange(val) {
  // 正则表达式来匹配IPv4地址加端口（IP:PORT）
  const ipPortRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):(\d{1,5})$/;
  // 正则表达式来匹配IPv6地址加端口（IP:PORT）
  const ipv6PortRegex =
    /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|(([0-9a-fA-F]{1,4}:){1,7}|:):(([0-9a-fA-F]{1,4}:){1,6}|:)|(([0-9a-fA-F]{1,4}:){1,5}([0-9a-fA-F]{1,4}:){1,}|:):(([0-9a-fA-F]{1,4}:){1,4}([0-9a-fA-F]{1,4}:){1,}|:)|(([0-9a-fA-F]{1,4}:){1,3}([0-9a-fA-F]{1,4}:){2,}|:):(([0-9a-fA-F]{1,4}:){1,2}([0-9a-fA-F]{1,4}:){1,}|:)|(([0-9a-fA-F]{1,4}:){1,1}([0-9a-fA-F]{1,4}:){3,}|:)):([0-9]{1,5})$/;
  // 正则表达式来简单匹配IP段（CIDR），例如 ***********-***********
  // 注意：这个正则表达式不会验证CIDR掩码的有效性，只是检查格式
  const ipRangeRegex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)-(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  // 添加 IP/MASK 格式验证的正则表达式
  const ipMaskRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/([0-9]|[1-2][0-9]|3[0-2])$/;

  if (rules.IP(val).result) {
    return val;
  } else if (rules.IPV6(val).result) {
    return val;
  } else if (ipPortRegex.test(val)) {
    return val;
  } else if (ipv6PortRegex.test(val)) {
    return val;
  } else if (ipRangeRegex.test(val)) {
    return val;
  } else if (ipMaskRegex.test(val)) {
    // 添加 IP/MASK 格式验证
    return val;
  } else {
    return false;
  }
}
//验证url 前缀限制http https
function validateUrl(val) {
  const urlRegex = /^(https?):\/\/.*$/;
  return urlRegex.test(val);
}
//效验 扫描端口，IP/IP:PORT/IP段，URL
function getValidate() {
  let ifTrue = true;
  // 端口/端口段 验证
  let listPorts = [];
  let listHosts = [];
  let listUrls = [];
  const ports = JSON.parse(JSON.stringify(formData.value.ports));
  const portsArr = ports.split(",");
  if (ports.length > 0) {
    for (let i of portsArr) {
      if (!validatePortOrRange(i)) {
        ElMessage.warning(i + "格式错误:请输入正确的扫描端口格式，格式：英文逗号分隔");
        ifTrue = false;
        return false;
      } else {
        listPorts.push(i);
      }
    }
  } else {
    listPorts = [""];
  }
  if (ifTrue) {
    const hosts = JSON.parse(JSON.stringify(formData.value.hosts));
    if (hosts) {
      const hostsArr = hosts.split("\n");
      for (let i of hostsArr) {
        i = i.trim();
        if (!validateIPOrRange(i)) {
          ElMessage.warning(i + "格式错误:请输入正确的IP/IP:PORT/IP段格式，并用换行分隔");
          ifTrue = false;
          return false;
        } else {
          listHosts.push(i);
        }
      }
    }
    const urls = JSON.parse(JSON.stringify(formData.value.urls));
    if (urls && ifTrue) {
      const urlsArr = urls.split("\n");
      for (let i of urlsArr) {
        i = i.trim();
        if (!validateUrl(i)) {
          ElMessage.warning(i + "格式错误:请输入正确的URL格式，并用换行分隔");
          ifTrue = false;
          return false;
        } else {
          listUrls.push(i);
        }
      }
    }
  }
  return ifTrue ? { listPorts, listHosts, listUrls } : false;
}

// 选择poc
const checkPocRef = ref(null);
//提交
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let pocDataInfos = [];
      // IP/IP:PORT/IP段，URL最少填写一项
      if (!formData.value.hosts && !formData.value.urls) {
        ElMessage.warning("IP/IP:PORT/IP段，URL最少填写一项");
        return;
      }
      if (!formData.value.ports && portGroupTags.value.length == 0) {
        ElMessage.warning("请输入扫描端口，格式：英文逗号分隔");
        return;
      }

      const isParametersVos = verification(); ////高级参数设定-- 改成key=value 拼接数组
      if (!isParametersVos) {
        return;
      }
      const data = JSON.parse(JSON.stringify(formData.value));
      //端口/端口段验证
      const isPorts = getValidate();
      console.log("isPorts: ", isPorts);
      if (isPorts) {
        data.ports = isPorts.listPorts.join(",");
        data.hosts = isPorts.listHosts.join(",").replace(/,/g, "\n"); //将data.hosts中,替换成\n
        data.urls = isPorts.listUrls.join(",").replace(/,/g, "\n");
      } else {
        return;
      }
      if (data.pocnameonly1 == 1) {
        pocDataInfos = checkPocRef.value?.checkPoc;
        if (!data.pocname && !pocDataInfos.length) {
          ElMessage.warning("请输入自定义探测POC");
          return;
        }
      }
      const api = prop.type == "edit" ? specialTaskEdit : specialTaskAdd;
      const param = {
        nodeId: nodeId.value,
        entity: data.entity,
        ports: data.ports,
        portGroup: portGroupTags.value.join(","),
        hosts: data.hosts,
        urls: data.urls,
        scanchain: data.scanchain,
        paratpls: data.paratpls,
        customParametersVos: isParametersVos, ////高级参数设定-- 改成key=value 拼接数组
        pocnameonly: data.pocnameonly1 == 1 ? true : false, //自定义探测Poc
        pocname: data.pocnameonly1 == 1 ? data.pocname : "",
        quotePoc: pocDataInfos.join(","), //引用poc
        ondns: data.ondns1 == 1 ? true : false, //启用DNS查询
        nobr: data.nobr1 == 1 ? true : false, //服务弱口令检测
        sntypeFlag: data.sntypeFlag1 == 1 ? true : false, //禁用探测常用端口
        user: data.user,
        password: data.password,
      };
      if (prop.type == "edit") {
        param.taskid = data.taskid;
      } else {
        param.enable = false; //是否启用
      }
      api(param).then(() => {
        ElMessage.success("操作成功");
        cancel();
      });
    } else {
      return false;
    }
  });
}
function verification() {
  //高级参数设定
  const parametersList = customParametersList.value.filter((item) => item.param || item.value);
  const array = []; //高级参数设定-- 参数名=参数值 拼接数组
  const paramArray = new Set(); // 使用Set来存储唯一的来源 参数名
  for (const item of parametersList) {
    if (!item.param) {
      ElMessage.warning("请选择高级参数设定--参数名");
      return false;
    } else {
      const inputValue = item.value.trim(); // 移除参数值两端的空格
      if (inputValue === "" || !inputValue) {
        ElMessage.warning("请输入高级参数设定--参数值");
        return false;
      } else if (paramArray.has(item.param)) {
        ElMessage.warning("不能选择高级参数设定--参数值");
        return false;
      } else {
        paramArray.add(item.param); // 将参数名添加到Set中
        array.push({ param: item.param, value: inputValue }); // 将处理过的添加到数组中
      }
    }
  }

  return array; // 返回验证通过的数组
}
</script>

<style scoped lang="scss">
// .el-form-item
::v-deep(.hosts-urls) {
  display: flex;
  justify-content: space-between;
  .el-form-item {
    // display: grid;
  }
}
::v-deep(.switch-box) {
  display: flex;
  justify-content: space-between;
}
::v-deep(.item-flex) {
  .el-form-item__content {
    display: flex;
    justify-content: space-between;
  }
}
.input-after-span {
  position: absolute;
  right: -40px;
}
.after-port {
  float: right;
  margin: 0 0 10px 10px;
}
.after-poc {
  position: absolute;
  right: -104px;
}
.port-group-tags {
  margin: 0 8px 10px 0;
}
.buts-m {
  position: absolute;
  bottom: auto;
  right: -70px;
}
.remarks-span {
  color: #808388;
  font-size: 13px;
}
.poc-box {
  width: 106%;
  position: relative;
  right: -6%;
  p {
    position: relative;
    top: 40px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebedf1;
    font-size: 14px;
    color: #848484;
    line-height: 32px;
  }
  :deep(.el-button) {
    margin-bottom: 10px;
    position: relative;
    right: 0;
  }
}
</style>
