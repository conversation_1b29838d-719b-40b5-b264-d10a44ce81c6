<template>
  <xel-dialog :title="title + 'WEB探测包'" ref="dialogRef" width="50%" buttonCancel="取消" buttonDetermine="保存" @submit="submit" @close="cancel">
    <el-form style="padding-right: 40px" :model="formData" ref="ruleFormRef" label-width="140px">
      <template v-for="(item, index) in formList" :key="index">
        <el-form-item v-if="item.prop == 'fingerIds'" class="label-topicIds" label="匹配探测指纹:" label-width="140px">
          <section class="tags-box">
            <el-tag v-for="(tag, index) in fingerTags" :key="index" size="default" closable :disable-transitions="false" @close="handleClose(index)">
              {{ tag.product }}
            </el-tag>
          </section>
          <el-button class="ml-4" @click="getFingerDia">选择</el-button>
        </el-form-item>
        <xel-form-item v-else v-model="formData[item.prop]" v-bind="item"></xel-form-item> </template
    ></el-form>
  </xel-dialog>
  <xel-dialog title="选择探测指纹" width="1084px" ref="dialogWebFingerRef" @submit="submitWebFinger">
    <webFinger style="padding: 0 20px" ref="webFingerRef" :id="nodeId" :checkbox="true" :checkList="checkList" />
  </xel-dialog>
</template>

<script setup>
import { editWebProbe, detailsWebProbe, addWebProbe } from "@/api/sime/starshot/detectionPackage.js";
import { getWebFinger } from "@/api/sime/starshot/fingerprint.js";
import { ElMessage } from "element-plus";
import { ref, computed, nextTick } from "vue";
import webFinger from "../fingerprintMaintenance/webFinger.vue";

let props = defineProps({
  title: {
    type: String,
    default() {
      return "新建";
    },
  },
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
  /* 任务类型 */
  id: {
    type: String,
    default() {
      return "";
    },
  },
  type: {
    type: String,
  },
});
const dialogWebFingerRef = ref(null); // 探测指纹弹框

const webFingerRef = ref(null);
const fingerTags = ref([]); //探测指纹
// 打开探测指纹弹框
function getFingerDia() {
  nextTick(() => {
    dialogWebFingerRef.value.open();
  });
}
//编辑弹框--关闭标签
function handleClose(index) {
  fingerTags.value.splice(index, 1);
}
const checkList = computed(() => fingerTags.value.map((v) => v.id));
function submitWebFinger() {
  fingerTags.value.push(...webFingerRef.value.checkRow);
  dialogWebFingerRef.value.close();
}
const formData = ref({ fingerIds: [] });

let formList = ref([
  {
    prop: "probName",
    label: "探测包名称",
    required: true,
    maxlength: "100",
  },
  {
    prop: "level",
    label: "优先级",
    formType: "number",
    max: 10000,
    min: 0,
    precision: 0,
    placeholder: "请输入优先级",
  },
  {
    prop: "method",
    label: "请求方法",
    required: true,
  },
  {
    prop: "path",
    label: "URI",
    required: true,
  },
  {
    prop: "fingerIds",
    label: "匹配探测指纹",
    formType: "select",
    options: [],
    multiple: true,
  },
  {
    formType: "input",
    prop: "header",
    label: "请求头",
    type: "textarea",
    rows: 5,
  },
  {
    formType: "switch",
    prop: "state",
    label: "启用",
    switchOptions: {
      activeValue: true,
      inactiveValue: false,
    },
  },
]);
const optionsSelf = ref([]);
// 获取字典列表
function getWebFingerList() {
  getWebFinger(props.nodeId, {
    pageNum: 1,
    pageSize: 99,
  }).then((res) => {
    optionsSelf.value = res.data.list.data.rows.map((v) => {
      return {
        value: v.id,
        label: v.product,
      };
    });
    console.log("formList.value: ", formList.value);
    const index = formList.value.findIndex((item) => item.prop == "fingerIds");
    formList.value[index].options = optionsSelf.value;
  });
}
let dialogRef = ref();
const emit = defineEmits(["submit"]);
const ruleFormRef = ref(null);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.parse(JSON.stringify(formData.value));
      data.enable = data.state ? true : false;
      const api = props.title === "编辑" ? editWebProbe : addWebProbe;
      const msg = props.title === "编辑" ? "修改成功" : "添加成功";
      const webProbsFingerList = fingerTags.value.map((v) => {
        if (v.id) {
          return {
            webFingersId: v.id,
            product: v.product,
          };
        } else {
          return {
            product: v.product,
          };
        }
      });
      const param = {
        probName: data.probName,
        level: data.level,
        method: data.method,
        path: data.path,
        header: data.header,
        enable: data.enable,
        webProbsFingers: webProbsFingerList,
      };
      if (props.title == "编辑") {
        param.id = data.id;
      }
      api(props.nodeId, param).then((res) => {
        ElMessage.success(msg);
        dialogRef.value.close();
        emit("submit");
      });
    } else {
      return;
    }
  });
}

function open(name) {
  fingerTags.value = [];
  if (props.id) {
    detailsWebProbe(props.nodeId, {
      id: props.id,
    }).then((res) => {
      formData.value = res.data.webProbe;
      formData.value.state = res.data.webProbe.enable ? 1 : 0;
      formData.value.fingerIds = res.data.webProbe.fingerIds;
      fingerTags.value = res.data.webProbe.webProbsFingers.map((v) => {
        return {
          id: v.webFingersId,
          product: v.product,
        };
      });
      if (name) {
        formData.value.probName = res.data.webProbe.probName + "--复制";
      }
    });
  }
  dialogRef.value.open();
  nextTick(() => {
    getWebFingerList();
  });
}
// 关闭弹窗
function cancel() {
  formData.value = {};
}
defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.detailDiv {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .titleSpan {
    flex: 1;
    font-weight: bold;
    display: inline-block;
    text-align: end;
    font-size: 15px;
  }
  .conSpan {
    position: relative;
    top: 2px;
    font-size: 15px;
    flex: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.btn {
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
}
.ml-4 {
  margin-left: 4px;
}
::v-deep(.item-flex) {
  .el-form-item__content {
    display: flex;
    justify-content: space-between;
  }
}
.tags-box {
  display: flex;
  gap: 10px;
  flex-wrap: wrap; //换行
  .el-tag {
    height: 30px;
    margin-bottom: 4px;
  }
  .el-button--small {
    height: 30px;
  }
}
</style>
