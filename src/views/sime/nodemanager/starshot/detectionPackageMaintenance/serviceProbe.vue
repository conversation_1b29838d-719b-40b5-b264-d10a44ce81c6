<template>
  <!-- 探测包维护 - 服务探测包 列表 -->
  <div>
    <common-search
      v-model="searchState.data"
      :labelWidth="90"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <template #form>
        <xel-form-item
          label="时间"
          type="datetimerange"
          form-type="daterange"
          value-format="YYYY-MM-DD HH:mm:ss"
          v-model:start="searchState.data.startTime"
          v-model:end="searchState.data.endTime"
        ></xel-form-item>
      </template>
      <el-button @click="handAdd" class="search-button" v-hasPermi="'nodemanager:starshot:addServiceProbe'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <section class="bg-p-border-new mt15-new">
      <xel-table ref="tableRef" v-loading="loading" :columns="columns" :pagination="false" :data="tableData">
        <template #enableSlot="{ row }"> {{ row.enable ? "启用" : "禁用" }} </template>
        <template #action="scope">
          <!-- 操作列按钮组插槽 -->
          <xel-handle-btns ref="btnsRef" :btn-list="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
        </template>
      </xel-table>

      <!-- 分页 -->
      <xel-pagination ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </section>
    <!--  新增弹窗 -->
    <addServiceDia ref="addFingerDiaRef" :title="title" :nodeId="nodeId" :id="probeId" @submit="search" />
  </div>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import addServiceDia from "./addServiceDia.vue";
import { getServiceProbe, updateServiceProbe } from "@/api/sime/starshot/detectionPackage.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from "vue-router";
const route = useRoute();
const prop = defineProps({
  id: {
    type: String,
  },
});
const nodeId = computed(() => prop.id);
let tableRef = ref();
let tableData = ref([]);
const probeId = ref(null);

/* 搜索相关 */
let searchState = reactive({
  data: {
    enable: "",
    name: "",
    protocol: "",
    rarity: undefined,
    startTime: "",
    endTime: "",
    ports: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态",
      prop: "enable",
      options: [],
      dictName: "nodemanager_starshot_enable",
      sime: true,
    },
  ],
  formList: [
    {
      prop: "name",
      label: "探测包名称",
    },
    {
      prop: "protocol",
      label: "协议",
    },
    {
      prop: "ports",
      label: "端口",
    },
    {
      prop: "rarity",
      label: "优先级",
      formType: "number",
      max: 10000,
      min: 0,
      precision: 0,
      placeholder: "请输入优先级",
      controls: false,
    },
  ],
});
/* 列表相关 */
let columns = [
  {
    prop: "name",
    label: "探测包名称",
  },
  {
    prop: "data",
    label: "探测数据",
  },
  {
    prop: "protocol",
    label: "协议",
  },
  {
    prop: "ports",
    label: "端口",
  },
  {
    prop: "sSLPorts",
    label: "探测SSL端口",
  },
  {
    prop: "totalWaitMS",
    label: "等待响应时间",
  },
  {
    prop: "tCPWrappedMS",
    label: "TCPWrappedMS",
  },
  {
    prop: "rarity",
    label: "优先级",
  },
  {
    prop: "fallback",
    label: "回溯匹配",
  },
  {
    prop: "enable",
    label: "状态",
    slotName: "enableSlot",
  },
  {
    prop: "updatedAt",
    label: "更新时间",
    width: "150px",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "action",
    width: "190px",
  },
];
// 操作按钮
function getBtnList(row) {
  return [
    {
      icon: "Edit",
      title: "编辑",
      hasPermi: "nodemanager:starshot:editServiceProbe",
      onClick() {
        title.value = "编辑";
        probeId.value = row.id;
        nextTick(() => {
          addFingerDiaRef.value.open();
        });
      },
    },
    {
      icon: "document-copy",
      title: "复制",
      hasPermi: "nodemanager:starshot:addServiceProbe",
      onClick() {
        title.value = "复制";
        probeId.value = row.id;
        nextTick(() => {
          addFingerDiaRef.value.open("--复制");
        });
      },
    },
    {
      icon: "CircleCheck",
      title: "启用",
      hide: row.enable ? true : false,
      hasPermi: "nodemanager:starshot:updateServiceProbeEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
    {
      icon: "Remove",
      title: "禁用",
      hide: row.enable ? false : true,
      hasPermi: "nodemanager:starshot:updateServiceProbeEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
  ];
}
//修改状态
function enableRemoveCircleCheck(id, enable) {
  const mes = enable ? "禁用" : "启用";
  ElMessageBox.confirm(`确认${mes}选中的数据项？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    updateServiceProbe(nodeId.value, {
      id: id,
      enable: enable,
    }).then(() => {
      ElMessage.success(mes + "成功");
      search();
    });
  });
}
/* 获取表格数据 */
let total = ref(0);
const loading = ref(false);
const search = () => {
  if (nodeId.value) {
    loading.value = true;
    getServiceProbe(nodeId.value, searchState.data).then((res) => {
      total.value = res.data.list.data.total;
      tableData.value = res.data.list.data.rows;
      loading.value = false;
    });
  }
};

/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = { pageNum: 1, pageSize: 10 };
  paginationRef.value?.resetPageNum();
  search();
}
/* 新增相关 */
const addFingerDiaRef = ref();
let title = ref("");
const handAdd = () => {
  title.value = "新建";
  probeId.value = null;
  nextTick(() => {
    addFingerDiaRef.value.open();
  });
};
search();
</script>
<style scoped>
::v-deep(.el-input__inner) {
  text-align: left;
}
</style>
