<template>
  <!-- 探测包维护 - web探测包 列表 -->
  <div>
    <common-search
      v-model="searchState.data"
      :labelWidth="106"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <el-button @click="handAdd" class="search-button" v-hasPermi="'nodemanager:starshot:addWebProbe'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <section class="bg-p-border-new mt15-new">
      <xel-table ref="tableRef" v-loading="loading" :columns="columns" :pagination="false" :data="tableData">
        <template #enableSlot="{ row }"> {{ row.enable ? "启用" : "禁用" }} </template>
        <template #action="scope">
          <!-- 操作列按钮组插槽 -->
          <xel-handle-btns ref="btnsRef" :btn-list="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
        </template>
      </xel-table>

      <!-- 分页 -->
      <xel-pagination ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </section>
    <!-- SoScan 新增弹窗 -->
    <AddWebDia ref="addWebDiaRef" :title="title" :nodeId="nodeId" :id="probId" @submit="search" />
  </div>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import AddWebDia from "./addWebDia.vue";
import { getWebProbe, updateWebProbe } from "@/api/sime/starshot/detectionPackage.js";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute } from "vue-router";
const route = useRoute();
const prop = defineProps({
  id: {
    type: String,
  },
});
const nodeId = computed(() => prop.id);

let tableRef = ref();
let tableData = ref([]);
const probId = ref(null);

/* 搜索相关 */
let searchState = reactive({
  data: {
    enable: "",
    probName: "",
    match: "",
    path: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态",
      prop: "enable",
      options: [],
      sime: true,
      dictName: "nodemanager_starshot_enable",
    },
  ],
  formList: [
    {
      prop: "probName",
      label: "探测包名称",
    },
    {
      prop: "path",
      label: "URI",
    },
    {
      prop: "match",
      label: "匹配探测指纹",
    },
  ],
});

/* 列表相关 */
let columns = [
  {
    prop: "probName",
    label: "探测包名称",
  },
  {
    prop: "match",
    label: "匹配探测指纹",
  },
  {
    prop: "path",
    label: "URI",
  },
  {
    prop: "method",
    label: "请求方法",
  },

  {
    prop: "header",
    label: "请求头",
  },
  {
    prop: "level",
    label: "优先级",
  },
  {
    prop: "enable",
    label: "状态",
    slotName: "enableSlot",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "action",
    width: "190px",
  },
];
// 操作按钮
function getBtnList(row) {
  return [
    {
      icon: "Edit",
      title: "编辑",
      hasPermi: "nodemanager:starshot:editWebProbe",
      onClick() {
        title.value = "编辑";
        probId.value = row.id;
        nextTick(() => {
          addWebDiaRef.value.open();
        });
      },
    },
    {
      icon: "document-copy",
      title: "复制",
      hasPermi: "nodemanager:starshot:addWebProbe",
      onClick() {
        title.value = "复制";
        probId.value = row.id;
        nextTick(() => {
          addWebDiaRef.value.open("copy  ");
        });
      },
    },
    {
      icon: "CircleCheck",
      title: "启用",
      hide: row.enable ? true : false,
      hasPermi: "nodemanager:starshot:updateWebProbeEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
    {
      icon: "Remove",
      title: "禁用",
      hide: row.enable ? false : true,
      hasPermi: "nodemanager:starshot:updateWebProbeEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
  ];
}
//修改状态
function enableRemoveCircleCheck(id, enable) {
  const mes = enable ? "禁用" : "启用";
  ElMessageBox.confirm(`确认${mes}选中的数据项？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    updateWebProbe(nodeId.value, {
      id: id,
      enable: enable,
    }).then(() => {
      ElMessage.success(mes + "成功");
      search();
    });
  });
}
/* 获取表格数据 */
let total = ref(0);
const loading = ref(false);
const search = () => {
  if (nodeId.value) {
    loading.value = true;
    getWebProbe(nodeId.value, searchState.data).then((res) => {
      total.value = res.data.list.data.total;
      tableData.value = res.data.list.data.rows;
      loading.value = false;
    });
  }
};

/* 分页事件 */
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = {
    pageNum: 1,
    pageSize: 10,
  };
  paginationRef.value?.resetPageNum();
  search();
}

/* 新增相关 */
const addWebDiaRef = ref();
let title = ref("");
const handAdd = () => {
  title.value = "新建";
  probId.value = null;
  nextTick(() => {
    addWebDiaRef.value.open();
  });
};

reset();
</script>
<style scoped></style>
