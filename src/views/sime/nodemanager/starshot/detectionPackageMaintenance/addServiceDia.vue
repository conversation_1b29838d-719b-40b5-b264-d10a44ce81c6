<template>
  <xel-dialog :title="title + '服务探测包'" ref="dialogRef" width="50%" buttonCancel="取消" buttonDetermine="保存" @submit="submit" @close="cancel">
    <el-form style="padding-right: 40px" :model="formData" ref="ruleFormRef" label-width="140px">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xel-dialog>
</template>

<script setup>
import { addServiceProbe, editServiceProbe, detailsServiceProbe } from "@/api/sime/starshot/detectionPackage.js";
import { validatePortOrRange } from "../components/data.js";
import { ElMessage } from "element-plus";
import { ref } from "vue";

let props = defineProps({
  title: {
    type: String,
    default() {
      return "新建";
    },
  },
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
  /* 任务类型 */
  id: {
    type: String,
    default() {
      return "";
    },
  },
});

const ruleFormRef = ref(null);

let formList = ref([
  {
    prop: "name",
    label: "探测包名称",
    required: true,
    maxlength: "100",
  },
  {
    prop: "protocol",
    label: "协议",
    formType: "select",
    required: true,
    options: [
      { label: "TCP", value: "TCP" },
      { label: "UDP", value: "UDP" },
    ],
  },
  {
    formType: "input",
    prop: "ports",
    label: "端口",
    type: "textarea",
    rows: 6,
    placeholder: "请输入端口，格式：用英文逗号分隔",
  },
  {
    formType: "input",
    prop: "sSLPorts",
    label: "探测SSL端口",
    type: "textarea",
    rows: 6,
    placeholder: "探测SSL端口，格式：用英文逗号分隔",
  },
  {
    prop: "totalWaitMS",
    label: "等待响应时间ms",
    formType: "number",
    max: 100000,
    min: 0,
    precision: 0,
    placeholder: "请输入等待响应时间ms",
  },
  {
    prop: "rarity",
    label: "优先级",
    formType: "number",
    max: 10000,
    min: 0,
    precision: 0,
    placeholder: "请输入优先级",
  },
  {
    prop: "fallback",
    label: "回溯匹配",
  },
  {
    prop: "tCPWrappedMS",
    label: "TCPWrappedMS",
    formType: "number",
    max: 100000,
    min: 0,
    precision: 0,
    placeholder: "请输入TCPWrappedMS",
  },
  {
    formType: "input",
    prop: "data",
    required: true,
    label: "探测数据",
    type: "textarea",
    rows: 5,
  },
  {
    formType: "switch",
    prop: "state",
    label: "启用",
    switchOptions: {
      activeValue: true,
      inactiveValue: false,
    },
  },
]);
const formData = ref({ protocol: "TCP", ports: "", sSLPorts: "" });

let dialogRef = ref();

function getValidate() {
  let ifTrue = true;
  // 端口/端口段 验证
  let listPorts = [];
  let listSSLPorts = [];
  const ports = JSON.parse(JSON.stringify(formData.value.ports));
  if (ports.length > 0) {
    const portsArr = ports.split(",");
    for (let i of portsArr) {
      if (!validatePortOrRange(i)) {
        ElMessage.warning(i + "格式错误:请输入正确的端口格式，并用英文逗号分隔");
        ifTrue = false;
        return false;
      } else {
        listPorts.push(i);
      }
    }
  }

  if (ifTrue) {
    const sSLPorts = JSON.parse(JSON.stringify(formData.value.sSLPorts));
    if (sSLPorts.length > 0) {
      const sSLPortsArr = sSLPorts.split(",");
      for (let i of sSLPortsArr) {
        if (!validatePortOrRange(i)) {
          ElMessage.warning(i + "格式错误:请输入正确的探测SSL端口格式，并用英文逗号分隔");
          ifTrue = false;
          return false;
        } else {
          listSSLPorts.push(i);
        }
      }
    }
  }
  return ifTrue ? { listPorts, listSSLPorts } : false;
}
const emit = defineEmits(["submit"]);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.parse(JSON.stringify(formData.value));
      data.enable = data.state ? true : false;
      delete data.state;
      if (formData.value.ports || formData.value.sSLPorts) {
        const isPorts = getValidate();
        if (isPorts) {
          data.ports = isPorts.listPorts.join(",");
          data.sSLPorts = isPorts.listSSLPorts.join(",");
        } else {
          return;
        }
      }
      const api = props.title === "编辑" ? editServiceProbe : addServiceProbe;
      const msg = props.title === "编辑" ? "修改成功" : "添加成功";

      const param = {
        name: data.name,
        protocol: data.protocol,
        ports: data.ports,
        sSLPorts: data.sSLPorts,
        totalWaitMS: data.totalWaitMS,
        rarity: data.rarity,
        fallback: data.fallback,
        tCPWrappedMS: data.tCPWrappedMS,
        data: data.data,
        enable: data.enable,
      };
      if (props.title == "编辑") {
        param.id = data.id;
      }
      api(props.nodeId, param).then((res) => {
        ElMessage.success(msg);
        dialogRef.value.close();
        emit("submit", { ...data, probeId: res.data.id }); //从新建服务指纹入口保存时需要id和name
      });
    } else {
      return;
    }
  });
}
function open(name) {
  formData.value = {
    protocol: "TCP",
    ports: "",
    sSLPorts: "",
  };
  if (props.id) {
    detailsServiceProbe(props.nodeId, {
      id: props.id,
    }).then((res) => {
      formData.value = res.data.serviceProbe;
      formData.value.state = res.data.serviceProbe.enable ? 1 : 0;
      formData.value.ports = res.data?.ports ? res.data.ports : "";
      formData.value.sSLPorts = res.data?.sSLPorts ? res.data.sSLPorts : "";
      if (name) {
        formData.value.name = res.data.serviceProbe.name + name;
      }
    });
  } else {
    formData.value.protocol = "TCP";
  }
  dialogRef.value.open();
}
// 关闭弹窗
function cancel() {
  formData.value = {};
}
defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.detailDiv {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .titleSpan {
    flex: 1;
    font-weight: bold;
    display: inline-block;
    text-align: end;
    font-size: 15px;
  }
  .conSpan {
    position: relative;
    top: 2px;
    font-size: 15px;
    flex: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.btn {
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
}
.ml-4 {
  margin-left: 4px;
}
::v-deep(.item-flex) {
  .el-form-item__content {
    display: flex;
    justify-content: space-between;
  }
}
</style>
