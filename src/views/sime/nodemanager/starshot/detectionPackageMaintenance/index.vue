<template>
  <!-- web规则维护 -->
  <el-card>
    <h3 class="conH3Tit">
      {{ title }}
    </h3>
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane v-if="ifWebFinger" label="WEB指纹" name="webFinger" :lazy="true">
        <WebFinger :id="nodeId" :key="nodeId" />
      </el-tab-pane>
      <el-tab-pane v-if="ifWebProbe" label="WEB探测包" name="webProbe" :lazy="true">
        <WebProbe :id="nodeId" :key="nodeId" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script setup lang="ts">
import { computed, ref } from "vue";
import WebProbe from "./webProbe.vue";
import WebFinger from "../fingerprintMaintenance/webFinger.vue";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
import { useRoute } from "vue-router";
import hasPermi from "@/utils/hasPermi.js";
const route = useRoute();
const title = computed(() => {
  if (route.name == "StarshotDetail") {
    return "";
  } else {
    return route.meta && route.meta.title;
  }
});
const ifWebFinger = computed(() => {
  return route.name == "StarshotDetail" ? true : hasPermi("nodemanager:starshot:listWebFinger");
});
const ifWebProbe = computed(() => {
  return route.name == "StarshotDetail" ? true : hasPermi("nodemanager:starshot:listWebProbe");
});
const activeName = ref(ifWebFinger.value ? "webFinger" : "webProbe");
const nodeId = ref<any>("");
const getNodeListFun = () => {
  if (route.params.nodeId) {
    nodeId.value = route.params.nodeId;
  } else {
    getNodeList("starshot").then((res) => {
      if (res.data.total > 0) {
        nodeId.value = res.data.rows[0].id;
      }
    });
  }
};
getNodeListFun();
</script>
<style scoped lang="scss"></style>
