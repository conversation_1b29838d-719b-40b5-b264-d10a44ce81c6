<template>
  <!-- 专项检测 -- 专项任务 列表 -->
  <div>
    <common-search
      :labelWidth="102"
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <template #form>
        <xel-form-item label="计划开始时间" form-type="date" day="YYYY-MM-DD" v-model="searchState.data.planStartTime"></xel-form-item>
      </template>

      <el-button @click="handAdd" class="search-button" v-hasPermi="'nodemanager:starshot:addStarshotTopic'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>

    <section class="bg-p-border-new mt15-new">
      <xel-table
        ref="tableRef"
        v-loading="loading"
        :columns="columns"
        :pagination="false"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <template #nameSlot="{ row }">
          <div :class="ifShow ? 'xel-clickable' : ''" @click="clickName(row)">
            {{ row.name }}
          </div>
        </template>
        <template #planSlot="{ row }">
          <span v-if="row.planStartTime && row.planEndTime"> {{ row.planStartTime }} 至 {{ row.planEndTime }} </span>
        </template>
        <template #vulnSlot="{ row }"> {{ syncStatus[row.syncVulnStatus] }} </template>
        <template #assetSlot="{ row }"> {{ syncStatus[row.syncAssetStatus] }} </template>
        <template #actualSlot="{ row }">
          <span v-if="row.actualStartTime && row.actualEndTime"> {{ row.actualStartTime }} 至 {{ row.actualEndTime }} </span>
          <span v-else-if="row.actualStartTime"> {{ row.actualStartTime }} 至 --- </span>
          <span v-else-if="row.actualEndTime"> --- 至 {{ row.actualEndTime }} </span>
        </template>
        <!-- （0排队中 1进行中 2已结束 3已终止 4失败） -->
        <template #statusSlot="{ row }"> {{ statusStr[row.status] }} </template>
        <template #enableSlot="{ row }"> {{ row.enable ? "启用" : "禁用" }} </template>
        <template #syncSossSlot="{ row }">
          <span v-if="row.syncSoss == null">- </span
          ><span v-else>
            {{ row.syncSoss ? "绑定" : "未绑定" }}
          </span>
        </template>
        <template #action="scope">
          <!-- 操作列按钮组插槽 -->
          <xel-handle-btns ref="btnsRef" :btn-list="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
        </template>
      </xel-table>

      <!-- 分页 -->
      <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </section>
    <!--  同步至SOSS任务 -->
    <synchronizeSossTasks
      ref="synchronizeSossTasksRef"
      :type="sossType"
      :taskId="taskid"
      :assetStatus="assetStatus"
      :vulnStatus="vulnStatus"
      source="2"
      @submit="submitSoss"
    />

    <!--  新增弹窗 -->
    <newTaskDia ref="newTaskDiaRef" :title="title" :nodeId="nodeId" :id="fingerId" :isDetails="isDetails" @close="search" />
    <!-- 启用/禁用 -->
    <xel-dialog title="启用定时任务" ref="dialogRef" width="50%" buttonCancel="取消" buttonDetermine="启用" @submit="enableSubmit">
      <section class="flex-center">
        <div class="block mb-8">
          <span class="demonstration">计划启停时间： </span>
          <el-date-picker
            v-model="planTime"
            type="datetimerange"
            range-separator=" - "
            start-placeholder="计划启用时间"
            end-placeholder="计划停用时间"
            value-format="YYYY-MM-DD HH:mm"
            format="YYYY-MM-DD HH:mm"
            :default-time="defaultTime"
            :disabledDate="disabledDate"
          />
        </div>
        <span class="remarks-span">提示：计划启用时间不能小于当前时间，计划停用时间不能与计划启用时间相同。 </span>
      </section>
    </xel-dialog>
    <!-- 未绑定启动弹框 -->
    <xel-dialog title="警告" ref="dialogStartRef" width="33%" buttonCancel="取消" buttonDetermine="确定" @submit="enableRemoveCircleCheck(startRow)">
      <div class="block flex-center">
        <span class="demonstration">当前数据项没有绑定至SOSS，确认直接启动选中的数据项？ </span>
      </div>
      <template #otherButton>
        <el-button type="primary" @click="getBindSoss(taskid)" v-hasPermi="'nodemanager:topic:syncsoss'">绑定至SOSS</el-button>
      </template>
    </xel-dialog>
  </div>
</template>

<script setup>
import { nextTick, reactive, ref, computed } from "vue";
import synchronizeSossTasks from "../specialMapping/synchronizeSossTasks.vue";
import newTaskDia from "./newTaskDia.vue";
import { getTopicList, topicDelete, startTopic, stopTopic, updateTopicEnable } from "@/api/sime/starshot/specialTesting.js";
import { startStopStatus } from "../components/data.js";
import { batchDelete } from "@/utils/delete";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import hasPermi from "@/utils/hasPermi.js";
import { getDicts } from "@/api/sime/config/dict";
const route = useRoute();
const router = useRouter();
const isNode = computed(() => route.name == "StarshotDetail");
const prop = defineProps({
  id: {
    type: String,
  },
});
const nodeId = computed(() => prop.id);
let tableRef = ref();
let tableData = ref([]);
const fingerId = ref(null); //新增弹窗id
const isDetails = ref(true); //编辑弹窗是否显示按钮
const planTime = ref([]); //计划启停时间
const taskid = ref(null); //任务id
const assetStatus = ref(null); //资产状态
const vulnStatus = ref(null); //漏洞状态
let dialogRef = ref();
const statusStr = {
  0: "排队中",
  1: "进行中",
  2: "已结束",
  3: "已终止",
  4: "失败",
};
const syncStatus = {
  0: "成功",
  1: "失败",
  2: "同步中",
  3: "未同步",
};
const defaultTime = [new Date(2000, 1, 1, 0, 0), new Date(2000, 2, 1, 23, 59)]; // '12:00:00', '08:00:00'
const synchronizeSossTasksRef = ref();
const sossType = ref("");
const dialogStartRef = ref(); //未绑定启动弹框
const startRow = ref(); //未绑定启动数据项
/* 搜索相关 */ //
let searchState = reactive({
  data: {
    status: "",
    name: "",
    topicInfoName: "",
    planStartTime: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "任务状态",
      prop: "status",
      options: [],
      dictName: "nodemanager_starshot_taskStatus",
      sime: true,
    },
  ],
  formList: [
    {
      prop: "name",
      label: "任务名称",
    },
    {
      prop: "topicInfoName",
      label: "策略名称",
    },
  ],
});

/* 列表相关 */
let columns = [
  {
    prop: "name",
    label: "任务名称",
    slotName: "nameSlot",
  },
  {
    prop: "topicInfoName",
    label: "任务策略",
  },
  {
    prop: "topicInfoDesc",
    label: "策略描述",
  },
  {
    prop: "planStartTime",
    label: "计划启停时间",
    slotName: "planSlot",
    width: "138px",
  },
  {
    prop: "actualStartTime",
    label: "实际启停时间",
    slotName: "actualSlot",
    width: "138px",
  },
  {
    prop: "syncVulnStatus",
    label: "漏洞结果同步",
    slotName: "vulnSlot",
  },
  {
    prop: "syncAssetStatus",
    label: "资产结果同步",
    slotName: "assetSlot",
  },
  {
    prop: "status",
    label: "任务状态",
    slotName: "statusSlot",
  },
  {
    prop: "enable",
    label: "定时任务状态",
    slotName: "enableSlot",
  },
  {
    prop: "syncSoss",
    label: "绑定状态",
    slotName: "syncSossSlot",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "action",
    width: "400px",
  },
];
const ifSossBind = ref(false);
function getCheckerList() {
  getDicts("nodemanager_starshot_standalone").then((res) => {
    console.log("res: ", res);
    ifSossBind.value = res.data[0].dictValue == "0"; //0:未绑定，1：已绑定
  });
}
getCheckerList();
function getBtnList(row) {
  if (row && row.taskid) {
    let status = row.status !== null ? row.status.toString() : null;
    let syncAssetStatus = row.syncAssetStatus.toString() == "0" || row.syncAssetStatus.toString() == "2";
    let syncVulnStatus = row.syncVulnStatus.toString() == "0" || row.syncVulnStatus.toString() == "2";
    return [
      {
        icon: "view",
        title: "结果详情",
        hasPermi: "nodemanager:starshot:result",
        onClick() {
          let name = isNode.value ? "NodeSpecialTaskDetails" : "SpecialTaskDetails";
          router.push({
            name,
            params: {
              nodeId: nodeId.value,
            },
            query: {
              taskid: row.taskid,
              source: "2", //1-资产测绘 2-专项检测
            },
          });
        },
      },
      {
        icon: "Edit",
        title: "编辑",
        hide: row.syncSoss,
        hasPermi: "nodemanager:starshot:editStarshotTopic",
        onClick() {
          if (status != 1) {
            title.value = "编辑";
            fingerId.value = row.taskid;
            isDetails.value = row.syncSoss;
            nextTick(() => {
              newTaskDiaRef.value.open();
            });
          } else {
            ElMessage.warning("该任务进行中，无法编辑");
          }
        },
      },
      {
        icon: "document-copy",
        title: "复制",
        hasPermi: "nodemanager:starshot:addStarshotTopic",
        onClick() {
          title.value = "复制";
          fingerId.value = row.taskid;
          isDetails.value = false;
          nextTick(() => {
            newTaskDiaRef.value.open("copy");
          });
        },
      },
      {
        // status为null时可以启动，不能终止
        // status=1的时候不能启动
        // status=0,2,3,4不能终止
        icon: "VideoPlay",
        title: "启动",
        hide: status != 1 ? false : true,
        hasPermi: "nodemanager:starshot:topic",
        onClick() {
          if (!ifSossBind.value && !row.syncSoss) {
            taskid.value = row.taskid;
            startRow.value = row;
            sossType.value = "start";
            nextTick(() => {
              dialogStartRef.value.open();
            });
            //未绑定
          } else {
            enableRemoveCircleCheck(row);
          }
        },
      },
      {
        icon: "VideoPause",
        title: "终止",
        hide: status != 1 ? true : false,
        hasPermi: "nodemanager:starshot:topic",
        onClick() {
          enableRemoveCircleCheck(row);
        },
      },
      {
        icon: "CircleCheck",
        title: "启用定时任务",
        hide: row.enable ? true : false,
        hasPermi: "nodemanager:starshot:updateStarshotTopicEnable",
        onClick() {
          planTime.value = row.planStartTime && row.planEndTime ? [row.planStartTime, row.planEndTime] : [];
          taskid.value = row.taskid;
          currentDate.value = getCurrentDate(); // 获取前一天的时间戳
          getCurrentTime();
          nextTick(() => {
            dialogRef.value.open();
          });
        },
      },
      {
        icon: "Remove",
        title: "禁用定时任务",
        hide: row.enable ? false : true,
        hasPermi: "nodemanager:starshot:updateStarshotTopicEnable",
        onClick() {
          ElMessageBox.confirm(`确认禁用选中的数据项？`, "警告", {
            distinguishCancelAndClose: true,
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            updateTopicEnable({
              taskid: row.taskid,
              planStartTime: row.planStartTime,
              planEndTime: row.planEndTime,
              enable: row.enable,
            }).then(() => {
              ElMessage.success("禁用成功");
              search();
            });
          });
        },
      },
      {
        icon: "Delete",
        title: "删除",
        hasPermi: "nodemanager:starshot:deleteStarshotTopic",
        onClick() {
          if (status == 1) {
            ElMessage.warning("任务正在进行中，不能进行删除操作");
            return;
          }
          batchDelFun(row);
        },
      },
      {
        icon: "Link",
        title: "绑定至SOSS",
        hasPermi: "nodemanager:topic:syncsoss",
        hide: ifSossBind.value ? true : row.syncSoss,
        onClick() {
          taskid.value = row.taskid;
          sossType.value = "bind";
          getBindSoss();
        },
      },
      {
        // 同步状态：false---同步至soss按钮，true---状态为已同步，
        icon: "Connection",
        title: "查看绑定SOSS",
        hasPermi: "nodemanager:topic:selectsoss",
        hide: !row.syncSoss,
        onClick() {
          taskid.value = row.taskid;
          sossType.value = "detail";
          handSOSS("detail");
        },
      },
      // {
      //   icon: "Refresh",
      //   title: "同步",
      //   hide: row.syncSoss ? syncAssetStatus && syncVulnStatus : true,
      //   onClick() {
      //     // handSOSS();
      //     taskid.value = row.taskid;
      //     sossType.value = "sync";
      //     assetStatus.value = !syncAssetStatus;
      //     vulnStatus.value = !syncVulnStatus;
      //     handSOSS("detail");
      //   },
      // },
    ];
  } else {
    return [];
  }
}
//打开绑定SOSS
function getBindSoss() {
  dialogStartRef.value.close();
  handSOSS();
}
//绑定soss回调
function submitSoss(type) {
  search();
  if (type == "start" && startRow.value) {
    enableRemoveCircleCheck(startRow.value);
  }
}
//同步
const probeId = ref(null);
function handSOSS() {
  probeId.value = null;
  nextTick(() => {
    synchronizeSossTasksRef.value.open();
  });
}
//点击名称
const ifShow = ref(hasPermi("nodemanager:starshot:editStarshotTopic"));

function clickName(row) {
  if (ifShow.value) {
    let status = row.status !== null ? row.status.toString() : null;
    if (status != 1) {
      title.value = "编辑";
      fingerId.value = row.taskid;
      isDetails.value = row.syncSoss;
      nextTick(() => {
        newTaskDiaRef.value.open();
      });
    } else {
      ElMessage.warning("该任务进行中，无法编辑");
    }
  }
}

//修改状态 终止/启动
function enableRemoveCircleCheck(row) {
  const { mes, api, data } = startStopStatus(row, startTopic, stopTopic);
  ElMessageBox.confirm(`确认${mes}选中的数据项？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    api({ nodeId: nodeId.value, ...data }).then(() => {
      dialogStartRef.value.close();
      ElMessage.success(mes + "成功");
      search();
    });
  });
}
const currentDate = ref();
const currentTime = ref();
//获取前一天的时间戳
function getCurrentDate() {
  const date = new Date(); // 获取当前日期和时间
  date.setDate(date.getDate() - 1); // 设置为前一天
  const currentTime = date.getTime(); // 获取前一天的时间戳
  return currentTime;
}
//获取当前时间
function getCurrentTime() {
  const date = new Date(); // 获取当前日期和时间
  currentTime.value = date.getHours() + ":" + date.getMinutes() + 2;
}
//
function disabledDate(time, cal) {
  // 判断time是否小于当前时间的前一天
  return time.getTime() < currentDate.value;
}
// 启用
function enableSubmit() {
  if (planTime.value && planTime.value.length > 0) {
    if (planTime.value[0] === planTime.value[1]) {
      ElMessage.warning("开始时间不能等于结束时间");
    } else {
      updateTopicEnable({
        taskid: taskid.value,
        planStartTime: planTime.value[0],
        planEndTime: planTime.value[1],
        enable: false,
      }).then(() => {
        ElMessage.success("启用成功");
        search();
        dialogRef.value.close();
      });
    }
  } else {
    ElMessage.warning("请选择启用停用时间");
  }
}
/* 获取表格数据 */
let total = ref(0);
const loading = ref(false);
const search = () => {
  if (nodeId.value) {
    loading.value = true;
    getTopicList({
      nodeId: nodeId.value,
      ...searchState.data,
    }).then((res) => {
      total.value = res.data.total;
      tableData.value = res.data.rows;
      loading.value = false;
    });
  }
};
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

/* 批量删除 */
let multipleSelection = ref([]);
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};
function batchDelFun(rows) {
  batchDelete().then(() => {
    topicDelete(rows.taskid.toString()).then(() => {
      ElMessage.success("删除成功");
      search();
    });
  });
}

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = { pageNum: 1, pageSize: 10, nodeId: nodeId.value };
  paginationRef.value?.resetPageNum();
  search();
}

/* 新增相关 */
const newTaskDiaRef = ref();
let title = ref("");
const handAdd = () => {
  title.value = "新建";
  fingerId.value = null;
  isDetails.value = false;
  nextTick(() => {
    newTaskDiaRef.value.open();
  });
};
reset();
</script>
<style scoped>
.remarks-span {
  margin-top: 10px;
  color: #808388;
}
.flex-center {
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-bottom: 8px;
}
</style>
