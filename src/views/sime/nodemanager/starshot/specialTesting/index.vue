<template>
  <!-- 专项检测 -- 专项策略/专项任务 列表 -->
  <el-card>
    <h3 class="conH3Tit">
      {{ title }}
    </h3>
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane v-if="ifSpecialTask" label="专项任务" name="specialTask" :lazy="true">
        <SpecialTask :id="nodeId" :key="nodeId" />
      </el-tab-pane>
      <el-tab-pane v-if="ifSpecialStrategy" label="专项策略" name="specialStrategy" :lazy="true">
        <SpecialStrategy :id="nodeId" ref="specialStrategyRef" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "SpecialTesting",
};
</script>
<script setup>
import { ref, watch, computed } from "vue";
import SpecialStrategy from "./specialStrategy.vue";
import SpecialTask from "./specialTask.vue";
import { getNodeList } from "@/api/sime/nodemanager/generalView";
import { useRoute } from "vue-router";
import hasPermi from "@/utils/hasPermi.js";
const specialStrategyRef = ref(null);
const route = useRoute();
const title = computed(() => {
  if (route.name == "StarshotDetail") {
    return "";
  } else {
    return route.meta && route.meta.title;
  }
});
// 路由变化时，刷新策略列表
watch(
  () => route.name,
  (val) => {
    if (val == "SpecialTesting" && activeName.value == "specialStrategy") {
      search();
    }
  }
);
const ifSpecialTask = computed(() => {
  return route.name == "StarshotDetail" ? true : hasPermi("nodemanager:starshot:listStarshotTopic");
});
const ifSpecialStrategy = computed(() => {
  return route.name == "StarshotDetail" ? true : hasPermi("nodemanager:starshot:listTopicInfo");
});
const activeName = ref(ifSpecialTask.value ? "specialTask" : "specialStrategy");
const search = () => {
  specialStrategyRef.value?.search();
};
const nodeId = ref("");
const getNodeListFun = () => {
  if (route.params.nodeId) {
    nodeId.value = route.params.nodeId;
  } else {
    getNodeList("starshot").then((res) => {
      if (res.data.total > 0) {
        nodeId.value = res.data.rows[0].id;
      }
    });
  }
};
getNodeListFun();
defineExpose({
  search,
});
</script>
<style scoped lang="scss"></style>
