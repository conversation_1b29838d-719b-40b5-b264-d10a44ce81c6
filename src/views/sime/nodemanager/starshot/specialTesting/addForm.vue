<template>
  <el-card class="bg-p-border-new">
    <h3 class="conH3Tit">
      {{ "" || ($route.meta && $route.meta.title) }}
    </h3>
    <section class="section-box2">
      <el-form :model="formData" ref="ruleFormRef" label-width="140px" :rules="rules" :style="{ width: '85%' }">
        <el-row :gutter="18">
          <el-col :span="24">
            <el-form-item label="专项策略key：" prop="name">
              <el-input :clearable="true" v-model="formData.name" :maxlength="100" placeholder="请输入专项策略key"></el-input>
            </el-form-item> </el-col
          ><el-col :span="24">
            <el-form-item label="专项策略名称：" prop="cname">
              <el-input :clearable="true" v-model="formData.cname" :maxlength="100" placeholder="请输入专项策略名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <section class="relative">
              <el-form-item label="专项策略描述：">
                <el-input :clearable="true" v-model="formData.desc" :rows="3" type="textarea" placeholder="请输入专项策略描述"></el-input>
              </el-form-item>
              <!-- <section class="flex-end2 but-m">
                <el-button type="primary" class="">测试</el-button>
              </section> -->
            </section>
          </el-col>
          <el-col :span="24">
            <el-form-item label="标签：">
              <el-input :clearable="true" v-model="formData.tag" placeholder="请输入标签"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="资产查询规则：" prop="searchRulesList">
              <template v-for="(item, index) in formData.searchRulesList" :key="index">
                <section class="flex flex-between relative">
                  <el-select :style="{ width: '49%', marginBottom: '10px' }" clearable v-model="item.source" placeholder="请选择来源">
                    <el-option v-for="ite in optionsSelf" :key="ite.value" :label="ite.label" :value="ite.value"></el-option>
                  </el-select>
                  <el-icon
                    v-if="
                      item.source == 'soss' ||
                      item.source == 'starshot' ||
                      item.source == 'scanxes' ||
                      item.source == 'fofa' ||
                      item.source == 'quake' ||
                      item.source == 'hunter'
                    "
                    style="margin: 0 0 10px; cursor: pointer"
                    @click="getClue(index)"
                    :size="20"
                    ><QuestionFilled
                  /></el-icon>
                  <el-input
                    :style="{ width: '48%', marginBottom: '10px' }"
                    class="ml-10"
                    :clearable="true"
                    v-model="item.rule"
                    placeholder="请输入规则"
                  ></el-input>
                  <section class="buts-m">
                    <el-button
                      class=""
                      :style="{ marginBottom: '10px' }"
                      :disabled="formData.searchRulesList && formData.searchRulesList.length == 1"
                      @click="deleteFun(index)"
                      >删除</el-button
                    >
                    <el-button
                      type="primary"
                      class="buts-m"
                      v-show="formData.searchRulesList && formData.searchRulesList.length - 1 == index"
                      @click="getAdd"
                      >新增</el-button
                    >
                  </section>
                </section>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="专项脚本组：">
              <!--  专项脚本组,脚本 二填一 -->
              <el-input
                v-if="pocCheckList && pocCheckList.length > 0"
                class="ml-10"
                :disabled="pocCheckList && pocCheckList.length > 0"
                placeholder="选择专项脚本组"
              ></el-input>
              <tree-select
                v-else
                v-model:value="formData.groupId"
                :options="treeData"
                :objMap="{ children: 'children', label: 'groupName', value: 'groupId' }"
                placeholder="选择专项脚本组"
              />
              <!-- 多选 -->
              <!-- <div class="el-tree-select">
                <el-select
                  style="width: 100%"
                  :disabled="pocCheckList && pocCheckList.length > 0"
                  v-model="groupNames"
                  ref="treeSelect"
                  :clearable="true"
                  multiple
                  placeholder="选择专项脚本组"
                >
                  <el-option value="groupId" label="groupName">
                    <el-tree
                      id="tree-option"
                      ref="selectTree"
                      :data="treeData"
                      :props="{ children: 'children', label: 'groupName', value: 'groupId' }"
                      node-key="groupId"
                      :expand-on-click-node="false"
                      :default-expand-all="true"
                      :show-checkbox="true"
                      :check-strictly="true"
                      :default-checked-keys="defaultCheckedKeys"
                      @check-change="handleChange"
                    ></el-tree>
                  </el-option>
                </el-select>
              </div> -->
            </el-form-item> </el-col
          ><el-col :span="24">
            <div class="poc-box">
              <p>专项脚本</p>
              <checkPoc :nodeId="nodeId" ref="checkPocRef" :pocCheckData="pocCheckList" />
            </div>
            <!-- <el-form-item label="专项脚本：">
              <checkPoc :nodeId="nodeId" ref="checkPocRef" :pocCheckData="pocCheckList" />
            </el-form-item> -->
          </el-col>
        </el-row>
      </el-form>
      <section class="flex-center">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit">保存</el-button>
      </section>
    </section>
    <clueDia :source="sourceValue" :title="title" ref="dialogClueRef" />
  </el-card>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { getDicts } from "@/api/sime/config/dict";
import { topicInfoAdd, topicInfoEdit, topicInfoDetail } from "@/api/sime/starshot/specialTesting.js";
import { getPocTree } from "@/api/sime/starshot/poc.js";
import checkPoc from "../components/checkPoc.vue";
import clueDia from "./clueDia.vue";
import TreeSelect from "@/components/sime/TreeSelect/index.vue";
import { useStore } from "vuex";
const isNode = computed(() => route.name.includes("Node"));
const store = useStore();
const prop = defineProps({
  type: {
    type: String,
  },
});
const router = useRouter();
const route = useRoute();
const nodeId = computed(() => {
  return route.params.nodeId;
});
const topicKey = computed(() => {
  return route.query.topicKey;
});
const treeData = ref([]); // poc管理组树形数据
const pocCheckList = ref([]); //选择poc列表
function getData() {
  getPocTree(nodeId.value).then((data) => {
    treeData.value = data.data.list;
    if (prop.type != "add" && topicKey.value) {
      topicInfoDetail(nodeId.value, { id: topicKey.value }).then((res) => {
        console.log("res: ", res);
        formData.value = res.data.topicInfo;
        formData.value.searchRulesList = res.data.topicInfo.searchRulesList ? res.data.topicInfo?.searchRulesList : [{ source: null, rule: "" }];
        //回显选择的poc列表
        pocCheckList.value = res.data.topicInfo?.pocDataInfos ? res.data.topicInfo.pocDataInfos : [];
        if (prop.type == "copy") {
          formData.value.name = formData.value.name + "Copy";
        }
      });
    }
  });
}
getData();

const ruleFormRef = ref(null);
const optionsSelf = ref([]);

// 获取字典列表
function getDictsList() {
  getDicts("nodemanager_starshot_topicRuleSource").then((res) => {
    optionsSelf.value = res.data.map((v) => {
      return {
        value: v.dictValue,
        label: v.dictLabel,
      };
    });
  });
}
getDictsList();

const formData = ref({
  name: "",
  cname: "",
  desc: "",
  tag: "",
  groupId: undefined,
  searchRulesList: [
    {
      source: null,
      rule: "",
    },
  ],
});
const rules = ref({
  name: [{ required: true, message: "请输入专项策略key", trigger: "blur" }],
  cname: [{ required: true, message: "请输入专项策略名称", trigger: "blur" }],
  searchRulesList: [{ required: true, trigger: "blur", message: "请输入资产查询规则" }],
});
const groupIds = ref([]); // 脚本组id
const groupNames = ref([]); // 脚本组名称
function handleChange(node, isCheck) {
  if (isCheck) {
    groupIds.value.push(node.groupId);
    groupNames.value.push(node.groupName);
  } else {
    const index = groupIds.value.findIndex((item) => item == node.groupId);
    groupIds.value.splice(index, 1);
    groupNames.value.splice(index, 1);
  }
}

function getAdd() {
  formData.value.searchRulesList.push({ source: null, rule: "" });
}
function deleteFun(index) {
  formData.value.searchRulesList.splice(index, 1);
}
function cancel() {
  let name = isNode.value ? "StarshotDetail" : "SpecialTesting";
  router.push({
    name,
    params: {
      nodeId: nodeId.value,
    },
  });
  store.commit("closeCurrentTab");
}
//专项策略key 验证
function getRestrict(value) {
  if (/[^a-zA-Z0-9\/]/.test(value)) {
    // 允许英文字符、数字和斜杠符号
    ElMessage.warning("专项策略key只能输入英文字符，数字和符号/");
    return false;
  }
  return true;
}
// 选择poc
const checkPocRef = ref(null);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (!getRestrict(formData.value.name)) return;
      const rulesList = verification(); //资产查询规则校验
      if (!rulesList) return;
      const pocDataInfos = checkPocRef.value?.checkPoc; //选择poc
      if (formData.value.tag || formData.value.groupId || pocDataInfos.length > 0) {
        if (pocDataInfos.length > 0) {
          formData.value.scripts = pocDataInfos.join(","); // poc 脚本
          formData.value.groupId = undefined;
        } else {
          formData.value.scripts = undefined;
        }
        const data = JSON.parse(JSON.stringify(formData.value));
        const api = prop.type == "edit" ? topicInfoEdit : topicInfoAdd;
        const params = {
          name: data.name,
          cname: data.cname,
          desc: data.desc ? data.desc : "",
          tag: data.tag ? data.tag : "",
          groupId: data.groupId ? data.groupId : "",
          scripts: data.scripts ? data.scripts : "",
          searchRulesList: rulesList,
        };
        if (prop.type == "edit") {
          params.id = data.id;
        }
        api(nodeId.value, params).then(() => {
          ElMessage.success("操作成功");
          cancel();
        });
      } else {
        ElMessage.warning("标签和脚本请至少填写一个");
        return;
      }
    } else {
      return false;
    }
  });
}

function verification() {
  const rulesList = formData.value.searchRulesList.filter((item) => item.source || item.rule);
  if (rulesList.length === 0) {
    ElMessage.warning("资产查询规则不能为空");
    return false;
  }

  const array = []; // 存储验证通过的规则
  const sources = new Set(); // 使用Set来存储唯一的来源
  const rule = new Set(); // 使用Set来存储唯一的来源

  for (const item of rulesList) {
    if (!item.source) {
      ElMessage.warning("请选择资产查询规则--来源");
      return false;
    } else {
      const trimmedRule = item.rule.trim(); // 移除规则两端的空格
      if (trimmedRule === "" || !trimmedRule) {
        ElMessage.warning("请输入资产查询规则--规则");
        return false;
      } else if (sources.has(item.source) && rule.has(item.rule)) {
        ElMessage.warning("不能输入重复的资产查询规则--来源+规则");
        return false;
      } else {
        sources.add(item.source); // 将来源添加到Set中
        rule.add(item.rule); // 将规则添加到Set中
        array.push({ source: item.source, rule: trimmedRule }); // 将处理过的规则添加到数组中
      }
    }
  }

  return array; // 返回验证通过的规则数组
}
const dialogClueRef = ref(null);
const sourceValue = ref(null);
function getClue(index) {
  sourceValue.value = formData.value.searchRulesList[index].source;
  if (sourceValue.value) {
    dialogClueRef.value.open();
  }
}
</script>

<style scoped lang="scss">
.section-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.but-m {
  position: absolute;
  bottom: 0;
  right: -70px;
}
.buts-m {
  position: absolute;
  bottom: auto;
  right: -70px;
}
.but-box {
  margin: 6px 0 0;
}
.poc-box {
  width: 106%;
  position: relative;
  right: -6%;
  p {
    position: relative;
    top: 40px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebedf1;
    font-size: 14px;
    color: #848484;
    line-height: 32px;
  }
  :deep(.el-button) {
    margin-bottom: 10px;
    position: relative;
    right: 0;
  }
}
</style>
