<template>
  <xel-dialog
    :title="(isDetails ? '查看' : title) + '任务'"
    ref="dialogRef"
    width="50%"
    :ishiddenDialog="isDetails"
    buttonCancel="取消"
    buttonDetermine="保存"
    @submit="submit"
    @close="cancel"
  >
    <el-form style="padding: 0 30px" :model="formData" ref="ruleFormRef" label-width="140px">
      <template v-for="(item, index) in formList1" :key="index">
        <el-form-item v-if="item.prop == 'topicIds'" class="label-topicIds" label="专项策略:" prop="groupName" label-width="140px">
          <section class="tags-box">
            <el-tag
              v-for="(tag, index) in topicTags"
              :key="index"
              size="default"
              :closable="!isDetails"
              :disable-transitions="false"
              @close="handleClose(index)"
            >
              {{ tag.name }}
            </el-tag>
          </section>
          <el-button class="ml-4" v-if="!isDetails" @click="getTopicDia">选择</el-button>
        </el-form-item>
        <xel-form-item v-else-if="!isDetails" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
        <el-form-item v-else-if="isDetails" :label="item.label + ':'" label-width="140px">
          <span v-if="item.prop == 'gahteronly1'"> {{ formData[item.prop] == 0 ? "否" : "是" }} </span>
          <span v-else> {{ formData[item.prop] }} </span>
        </el-form-item>
      </template>
    </el-form>
  </xel-dialog>
  <xel-dialog title="选择专项策略" size="large" ref="dialogSpecialStrategyRef" @submit="getEdit">
    <specialStrategy style="padding: 0 20px" ref="specialStrategyRef" :id="nodeId" :checkbox="true" :checkList="checkList" />
  </xel-dialog>
</template>

<script setup>
import specialStrategy from "../specialTesting/specialStrategy.vue";
import { ElMessage } from "element-plus";
import { ref, nextTick } from "vue";
import { topicAdd, topicEdit, topicDetail } from "@/api/sime/starshot/specialTesting.js";
import { getTopicInfoList } from "@/api/sime/starshot/specialTesting.js";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
let props = defineProps({
  title: {
    type: String,
    default() {
      return "新建";
    },
  },
  nodeId: {
    type: String,
    default() {
      return "";
    },
  },
  /* 任务类型 */
  id: {
    type: String,
    default() {
      return "";
    },
  },
  isDetails: {
    type: Boolean,
    default() {
      return true; //是否是详情
    },
  },
});
const optionsSelf = ref([]);
// 获取字典列表
function getDictsList() {
  getTopicInfoList(props.nodeId, {
    pageNum: 1,
    pageSize: 100,
  }).then((res) => {
    optionsSelf.value = res.data.list.data.rows.map((v) => {
      return {
        value: v.id,
        label: v.name,
      };
    });
    formList1.value[1].options = optionsSelf.value;
  });
}
const formData = ref({ planTime: [], planStartTime: "", planEndTime: "" });
const topicTags = ref([]); // 端口组名称
let formList1 = ref([
  {
    prop: "name",
    label: "任务名称",
    required: true,
  },
  {
    prop: "topicIds",
    label: "专项策略",
    required: true,
    formType: "select",
    options: [],
    multiple: true,
  },
  {
    formType: "switch",
    prop: "gahteronly1",
    label: "是否仅收集资产",
  },
]);

// 专项策略弹框
const dialogSpecialStrategyRef = ref();
const specialStrategyRef = ref();
//编辑弹框--关闭标签
function handleClose(index) {
  topicTags.value.splice(index, 1);
}
// 打开专项策略弹框
function getTopicDia() {
  nextTick(() => {
    dialogSpecialStrategyRef.value.open();
  });
}
const checkList = computed(() => topicTags.value.map((v) => v.id));
function getEdit() {
  topicTags.value.push(...specialStrategyRef.value.checkRow);
  dialogSpecialStrategyRef.value.close();
}
const emit = defineEmits(["close"]);
//选择探测包后，回显
function getCheck(row) {
  formData.value.serviceProbeName = row.name;
  formData.value.probeId = row.probeId;
}
const ruleFormRef = ref(null);
let dialogRef = ref();
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (checkList.value.length == 0) {
        ElMessage.warning("请选择专项策略");
        return;
      }
      const data = JSON.parse(JSON.stringify(formData.value));
      const api = props.title === "编辑" ? topicEdit : topicAdd;
      const mes = props.title === "编辑" ? "修改成功" : "添加成功";
      const params = {
        name: data.name,
        topicId: checkList.value.join(","),
        gahteronly: data.gahteronly1 ? true : false,
        // syncSoss: data.syncSoss1 ? true : false,
        planStartTime: data.planStartTime ? data.planStartTime : "",
        planEndTime: data.planEndTime ? data.planEndTime : "",
        nodeId: props.nodeId,
      };
      if (props.title == "编辑") {
        params.taskid = data.taskid;
      } else {
        params.enable = false;
      }
      api(params).then((res) => {
        ElMessage.success(mes);
        dialogRef.value.close();
        emit("close");
      });
    } else {
      return;
    }
  });
}

function open(name) {
  topicTags.value = [];
  if (props.id) {
    topicDetail({
      taskid: props.id,
      nodeId: props.nodeId,
    }).then((res) => {
      if (res.data) {
        formData.value = res.data;
        formData.value.gahteronly1 = res.data.gahteronly ? 1 : 0;
        // formData.value.syncSoss1 = res.data.syncSoss ? 1 : 0;
        formData.value.topicIds = res.data.topicId.split(",");
        topicTags.value = res.data.topicInfoVoList;
        if (name) {
          formData.value.name = res.data.name + "--复制";
        }
      }
    });
  }
  dialogRef.value.open();
  nextTick(() => {
    getDictsList();
  });
}
/* 新增探测包弹窗 */
// 打开新增服务探测包弹窗
// const handServiceAdd = () => {
//   router.push({ name: "SpecialStrategyAdd", params: { nodeId: props.nodeId } });
// };
// 新建服务探测包回调
function submitService(row) {
  formData.value.serviceProbeName = row.name;
  formData.value.probeId = row.probId;
}
// 关闭弹窗
function cancel() {
  formData.value = { planTime: [], planStartTime: "", planEndTime: "" };
}
defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.detailDiv {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .titleSpan {
    flex: 1;
    font-weight: bold;
    display: inline-block;
    text-align: end;
    font-size: 15px;
  }
  .conSpan {
    position: relative;
    top: 2px;
    font-size: 15px;
    flex: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
.ml-4 {
  margin-left: 4px;
}
::v-deep(.item-flex) {
  .el-form-item__content {
    display: flex;
    justify-content: space-between;
  }
}
.tags-box {
  display: flex;
  gap: 10px;
  flex-wrap: wrap; //换行
  .el-tag {
    height: 30px;
    margin-bottom: 4px;
  }
  .el-button--small {
    height: 30px;
  }
}
//专项策略 必填样式
::v-deep(.label-topicIds) {
  .el-form-item__label::before {
    content: "*";
    color: var(--el-color-danger);
    margin-right: 4px;
  }
}
</style>
