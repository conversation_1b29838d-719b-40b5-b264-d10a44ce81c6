<template>
  <xelDialog :ishiddenDialog="true" :title="title + '提示'" ref="dialogRef">
    <!-- Sossmysql -->
    <section v-if="source == 'soss'">
      <span class="title"
        >支持soss的资产库，其规则格式为：domain={客户名称} 或
        notdomain={客户名称}，domain表示取某个客户的资产，notdomain表示取全部客户资产但不取某个客户的资产</span
      >
      <p>示例：</p>
      <span> 示例1： domain=数字政府,山东省大数据局 </span>
      <span> 示例2： notdomain=枣庄网安,省厅,烟台市委网络安全和信息化委员会办公室 </span>
    </section>
    <!-- Starshotsqlite -->
    <section v-else-if="source == 'starshot'">
      <span class="title"
        >支持Starshot的Sqlite资产测绘库，其规则格式为： assets={taskid} 或 assets={service} 或 assets={fingerextrainfo}或notassets={taskid} 或
        notassets={service} 或 notassets={fingerextrainfo} ，taskid为扫描任务的id，service为服务名，fingerextrainfo为指纹的附加信息</span
      >
      <p>示例：</p>
      <span> 示例1： assets=445 </span>
      <span> 示例2： assets=microsoft-ds </span>
      <span> 示例3： assets=task20241227 </span>
    </section>
    <!-- AssetsElastic /ScanXElastic -->
    <section v-else-if="source == 'scanxes'">
      <span class="title">按照ES查询规则查询</span>
      <p>示例：</p>
      <span> 示例1： (web.header:"X-Confluence-Request-Time") OR (web.content:"atlassian") </span>
      <span> 示例2： jboss </span>
      <span> 示例3： port:8848 </span>
      <span> 示例4： web.title:"企业信息系统门户" </span>
    </section>
    <!-- Fofa -->
    <section v-else-if="source == 'fofa'">
      <span class="title">按照Fofa查询规则查询，常用查询语句。ip="*******" 通过单一IPv4地址进行查询。https://fofa.info/</span>
      <p>示例：</p>
      <span> 示例1： ip="*******"</span>
      <span> 示例2： "*************/24"</span>
      <span> 示例3： port:8848 || domain="qq.com" </span>
      <span> 示例4： app="Microsoft-Exchange" && title= </span>
      <span>
        示例5: protocol="http" || icp="京ICP证030173号" || province="山东" || city="济南" || title="济南" || header="elastic" || body="网络空间测绘"
        || product="NGINX" || icp="京ICP证030173号"
      </span>
    </section>
    <!-- Quake -->
    <section v-else-if="source == 'quake'">
      <span class="title"
        >按照quake查询规则查询。https://quake.360.net/quake/#/help?id=5eb238f110d2e850d5c6aec8&title=%E6%A3%80%E7%B4%A2%E5%85%B3%E9%94%AE%E8%AF%8D</span
      >
      <p>示例：</p>
      <span>
        示例1： 广东：查询在 China 或者 United States 且不在 广东省 的所有 3389 端口 port: 3389 AND (country: China OR country: "United State") AND
        NOT province_cn</span
      >
      <span> 示例2： 查询 80 端口且返回数据包不包括 baidu 字样的服务 port: 80 AND NOT data: baidu</span>
      <span> 示例3： Apache 服务器产品 app:"Apache" </span>
      <span> 示例4： domain:"360.cn" </span>
      <span> 示例5： response:"奇虎科技" </span>
      <span> 示例6：quake 国家、省、城市，支持中文和英文 province:"Sichuan" || province_cn:"四川" || city:"Chengdu" </span>
      <span> 示例7： title:"后台" || http_path:"/admin" || status_code:200 || body:"奇虎" || icp:"京ICP备08010314号" </span>
    </section>
    <!-- Hunter -->
    <section v-else-if="source == 'hunter'">
      <span class="title">按照Hunter查询规则查询。https://hunter.qianxin.com/</span>
      <p>示例：</p>
      <xel-table ref="tableRef" :columns="columns" :pagination="false" :data="hunterTableList"> </xel-table>
    </section>
    <section v-else>
      <span class="title">无提示</span>
    </section>
  </xelDialog>
</template>
<script setup lang="ts">
const prop = defineProps({
  source: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
});
const columns = [
  {
    prop: "title",
    label: "示例",
  },
  {
    prop: "text",
    label: "说明",
  },
];
// Hunter
const hunterTableList = [
  {
    title: 'ip="*******"',
    text: "搜索IP为 ”*******”的资产",
  },
  {
    title: 'ip="*************/24"',
    text: '搜索网段为"*************"的C段资产',
  },
  {
    title: 'ip.port="80"',
    text: "搜索开放端口为”80“的资产",
  },
  {
    title: 'ip.country="中国" 或 ip.country="CN"',
    text: "搜索IP对应主机所在国为”中国“的资产",
  },
  {
    title: 'ip.province="江苏"',
    text: "搜索IP对应主机在江苏省的资产",
  },
  {
    title: 'ip.city="北京"',
    text: "搜索IP对应主机所在城市为”北京“市的资产",
  },
  {
    title: 'ip.isp="电信"',
    text: "搜索运营商为”中国电信”的资产",
  },
  {
    title: 'app="Hikvision 海康威视 Firmware 5.0+" && ip.ports="8000"',
    text: "检索使用了Hikvision且ip开放8000端口的资产",
  },
  {
    title: 'domain="qianxin"',
    text: '搜索域名包含"qianxin"的网站',
  },
  {
    title: 'header.server=="Microsoft-IIS/10"',
    text: "搜索server全名为“Microsoft-IIS/10”的服务器",
  },
  {
    title: 'header.content_length="691"',
    text: "搜索HTTP消息主体的大小为691的网站",
  },
  {
    title: 'header.status_code="402"',
    text: "搜索HTTP请求返回状态码为”402”的资产",
  },
  {
    title: 'header="elastic"',
    text: "搜索HTTP响应头中含有”elastic“的资产",
  },
  {
    title: 'web.title="北京"',
    text: "从网站标题中搜索“北京”",
  },
  {
    title: 'web.body="网络空间测绘"',
    text: "搜索网站正文包含”网络空间测绘“的资产",
  },
  {
    title: 'icp.name="奇安信"',
    text: "搜索ICP备案单位名中含有“奇安信”的资产",
  },
  {
    title: 'protocol="http"',
    text: "搜索协议为”http“的资产",
  },
  {
    title: 'protocol.transport="udp"',
    text: "搜索传输层协议为”udp“的资产",
  },
  {
    title: 'protocol.banner="nginx"',
    text: '查询端口响应中包含"nginx"的资产',
  },
  {
    title: 'app.name="小米 Router"',
    text: "搜索标记为”小米 Router“的资产",
  },
  {
    title: 'cert="baidu"',
    text: "搜索证书中带有baidu的资产",
  },
];
// 打开弹窗
const dialogRef = ref(); // 弹窗
function open() {
  dialogRef.value.open();
}
defineExpose({
  open,
});
</script>
<style scoped lang="scss">
section {
  margin: 0 20px;
}
p {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-top: 20px;
  margin-bottom: 15px;
}
.title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
span {
  color: #333;
  display: list-item;
  margin-bottom: 10px;
  &::marker {
    content: "";
  }
}
</style>
