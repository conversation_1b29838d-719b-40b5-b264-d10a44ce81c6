<template>
  <!-- 专项检测 - 专项策略 列表 -->
  <div>
    <common-search
      :labelWidth="106"
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <el-button v-if="!checkbox" @click="handAdd" class="search-button" v-hasPermi="'nodemanager:starshot:addTopicInfo'">
        <el-icon :size="12">
          <plus />
        </el-icon>
        新增
      </el-button>
    </common-search>
    <section class="bg-p-border-new mt15-new">
      <el-table :data="tableData" v-loading="loading" @selection-change="handleSelectionChange" row-key="id">
        <!-- 专项任务新增弹框时使用 -->
        <el-table-column v-if="checkbox" type="selection" width="55" :selectable="selectable" :reserve-selection="true" />
        <template v-for="item in columns" :key="item.prop">
          <el-table-column
            v-if="item.slotName == 'action'"
            :label="checkbox ? '' : item.label"
            :prop="checkbox ? '' : item.prop"
            :width="checkbox ? '0px' : item.width"
          >
            <template #default="scope">
              <xel-handle-btns v-if="!checkbox" ref="btnsRef" :btn-list="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.prop == 'cname'" :label="item.label" :prop="item.prop">
            <template #default="scope">
              <span> {{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column v-else :label="item.label" :prop="item.prop" :show-overflow-tooltip="true">
            <template #default="scope">
              <span v-if="item.slotName == 'enableSlot'"> {{ scope.row.enable ? "启用" : "禁用" }}</span>
              <span v-else> {{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <!-- 分页 -->
      <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
    </section>
  </div>
</template>

<script setup>
import { nextTick, reactive, ref, computed } from "vue";
import { getTopicInfoList, deleteTopicInfo, topicInfoEnable } from "@/api/sime/starshot/specialTesting.js";
import { batchDelete } from "@/utils/delete";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
const router = useRouter();
const isNode = computed(() => route.name == "StarshotDetail");
const props = defineProps({
  id: {
    type: String,
  }, // 是否显示复选框  专项任务新增弹框时使用
  checkbox: {
    type: Boolean,
    default: false,
  },
  // 已选择的数据  专项任务新增弹框时使用
  checkList: {
    type: Array,
    default: () => [],
  },
});
const nodeId = computed(() => props.id);
let tableRef = ref();
let tableData = ref([]);
const loading = ref(false);
/* 搜索相关 */
let searchState = reactive({
  data: {
    enable: "",
    name: "",
    cname: "",
    desc: "",
    startTime: "",
    endTime: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [
    {
      lable: "状态",
      prop: "enable",
      options: [],
      dictName: "nodemanager_starshot_enable", //
      sime: true,
    },
  ],
  formList: [
    {
      prop: "name",
      label: "专项策略key",
    },
    {
      prop: "cname",
      label: "专项策略名称",
    },
    {
      prop: "desc",
      label: "专项策略描述",
    },
  ],
});

/* 列表相关 */
let columns = [
  {
    prop: "name",
    label: "专项策略key",
  },
  {
    prop: "cname",
    label: "专项策略名称",
  },
  {
    prop: "desc",
    label: "专项策略描述",
  },
  {
    prop: "enable",
    label: "状态",
    slotName: "enableSlot",
  },
  {
    prop: "updatedAt",
    label: "时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "action",
    width: "240px",
  },
];
function getBtnList(row) {
  return [
    {
      icon: "Edit",
      title: "编辑",
      hasPermi: "nodemanager:starshot:editTopicInfo",
      onClick({ row }) {
        let name = isNode.value ? "NodeSpecialStrategyEdit" : "SpecialStrategyEdit";
        router.push({ name, params: { nodeId: nodeId.value }, query: { topicKey: row.id } });
      },
    },
    {
      icon: "document-copy",
      title: "复制",
      hasPermi: "nodemanager:starshot:addTopicInfo",
      onClick({ row }) {
        let name = isNode.value ? "NodeSpecialStrategyAdd" : "SpecialStrategyAdd";
        router.push({ name, params: { nodeId: nodeId.value }, query: { topicKey: row.id } });
      },
    },
    {
      icon: "CircleCheck",
      title: "启用",
      hide: row.enable ? true : false,
      hasPermi: "nodemanager:starshot:updateTopicInfoEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },
    {
      icon: "Remove",
      title: "禁用",
      hide: row.enable ? false : true,
      hasPermi: "nodemanager:starshot:updateTopicInfoEnable",
      onClick() {
        enableRemoveCircleCheck(row.id, row.enable);
      },
    },

    {
      icon: "Delete",
      title: "删除",
      hasPermi: "nodemanager:starshot:deleteTopicInfo",
      onClick({ row }) {
        batchDelFun(row.id);
      },
    },
  ];
}
//修改状态
function enableRemoveCircleCheck(id, enable) {
  const mes = enable ? "禁用" : "启用";
  ElMessageBox.confirm(`确认${mes}选中的数据项？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    topicInfoEnable(nodeId.value, {
      id: id,
      enable: enable,
    }).then(() => {
      ElMessage.success(mes + "成功");
      search();
    });
  });
}
let total = ref(0);
function search() {
  // /专项策略
  /* 获取表格数据 */
  if (nodeId.value) {
    loading.value = true;
    getTopicInfoList(nodeId.value, searchState.data).then((res) => {
      total.value = res.data.list.data.total;
      tableData.value = res.data.list.data.rows;
      loading.value = false;
    });
  }
}
const changePagination = (val) => {
  searchState.data.pageNum = val.pageNum;
  searchState.data.pageSize = val.pageSize;
  search();
};

/* 删除 */
function batchDelFun(id) {
  batchDelete().then(() => {
    deleteTopicInfo({ topicId: id, nodeId: nodeId.value }).then(() => {
      ElMessage.success("删除成功");
      search();
    });
  });
}

/* 重置 */
const paginationRef = ref();
function reset() {
  searchState.data = { pageNum: 1, pageSize: 10 };
  paginationRef.value?.resetPageNum();
  search();
}

search();
/* 新增相关 */
const handAdd = () => {
  let name = isNode.value ? "NodeSpecialStrategyAdd" : "SpecialStrategyAdd";
  router.push({ name, params: { nodeId: nodeId.value } });
};

// 选择框
let multipleSelection = ref([]);
const handleSelectionChange = (row) => {
  multipleSelection.value = row;
};

// 判断是否可选
function selectable(row) {
  return !props.checkList.includes(row.id);
}
defineExpose({
  search,
  checkRow: computed(() => {
    return multipleSelection.value;
  }),
});
</script>
<style scoped></style>
