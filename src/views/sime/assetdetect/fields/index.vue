<template>
  <!-- 空间资产测绘 - 字段管理 - 列表 -->
  <el-row :gutter="20">
    <el-col :span="3">
      <el-tabs v-model="activeName" tab-position="left" style="height: 100%">
        <el-tab-pane label="字段配置" name="FieldsConfig" />
        <el-tab-pane label="扩展字段配置" name="ExtendFields" />
      </el-tabs>
    </el-col>
    <el-col :span="21" class="bg-p-border-new">
      <FieldsConfig v-if="activeName === 'FieldsConfig'" />
      <ExtendFields v-if="activeName === 'ExtendFields'" />
    </el-col>
  </el-row>
</template>

<script setup>
import { ref } from "vue";
import FieldsConfig from "./fieldsConfig.vue";
import ExtendFields from "./extendFields.vue";
const activeName = ref("FieldsConfig");
</script>

<style lang="scss" scoped>
::v-deep .el-tabs--left .el-tabs__header.is-left {
  float: right;
}
</style>
