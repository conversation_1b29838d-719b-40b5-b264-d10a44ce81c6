<template>
  <!-- 空间资产测绘 - 扩展字段管理 - 列表 -->
  <div>
    <el-form ref="ruleFormRef" :model="ruleForm">
      <div :style="tableHeight">
        <el-table :data="ruleForm.tableData" :border="parentBorder" ref="tableRef" height="100%" v-loading="loading">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="字段名" prop="field">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.field'" :rules="rules.field">
                <el-input v-model="ruleForm.tableData[scope.$index].field" maxlength="20" placeholder="请输入字段名" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="中文名" prop="alias">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.alias'" :rules="rules.alias">
                <el-input v-model="scope.row.alias" placeholder="请输入中文名" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="源字段" prop="srcField">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.srcField'" :rules="rules.srcField">
                <el-select :placeholder="'请选择源字段'" style="width: 100%" v-model="scope.row.srcField" clearable filterable>
                  <el-option v-for="item in options" :key="item.field" :label="item.alias" :value="item.field" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="默认展示" prop="isShow" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.isShow" :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column label="默认开启" prop="status" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column label="实现类" prop="implClass">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.implClass'" :rules="rules.implClass">
                <el-select :placeholder="'请选择实现类'" style="width: 100%" v-model="scope.row.implClass" clearable filterable>
                  <el-option v-for="item in impOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="作用时间" prop="actionTime">
            <template #default="scope">
              <el-select :placeholder="'请选择作用时间'" style="width: 100%" v-model="scope.row.actionTime" filterable>
                <el-option label="存储时" :value="1" />
                <el-option label="查询时" :value="0" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="btns" width="120px" v-hasPermi="'assetdetect:dataField:update'">
            <template #default="scope">
              <xel-handle-btns :btnList="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <FieldsTipsCom />

    <div class="mt20 text-center" v-hasPermi="'assetdetect:dataField:update'">
      <el-button @click="addField" style="min-width: 200px">
        <el-icon :size="12"> <plus /> </el-icon>添加字段
      </el-button>
    </div>

    <div class="text-right">
      <el-button @click="getTableData">取消</el-button>
      <el-button type="primary" v-hasPermi="'assetdetect:dataField:update'" @click="submit" :loading="saveLoading">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import FieldsTipsCom from "@/views/sime/components/fieldsTipsCom.vue";
import { getDataFieldList, getDataFieldExtraList, postDataFieldExtraSave } from "@/api/sime/assetdetect/fields";
import tableMixin from "../../components/mixin/tableMixin";
import { getDictsData } from "@/utils/getDicts";
let { tableHeight } = tableMixin(340);
let state = reactive({
  ruleForm: {
    tableData: [],
  },
  options: [],
  impOptions: [],
  loading: false,
});
let { options, ruleForm, loading, impOptions } = toRefs(state);

/* 获取公共数据 */
const getPublic = () => {
  /* 源字段 */
  getDataFieldList()
    .then((res) => {
      state.options = res.data;
    })
    .catch(() => {});
  /* 实现类 */
  getDictsData("assetdetect_extraFieldImplclass").then((res) => {
    state.impOptions = res;
  });
};
getPublic();
import { fieldsValid } from "@/utils/fieldsValid";
const rules = reactive({
  field: [
    {
      required: true,
      /*message: "请输入字段名",*/
      validator: fieldsValid,
      trigger: ["blur", "change"],
    },
  ],
  alias: [
    {
      required: true,
      message: "请输入中文名",
      trigger: ["blur", "change"],
    },
  ],
  srcField: [
    {
      required: true,
      message: "请选择源字段",
      trigger: ["blur", "change"],
    },
  ],
  implClass: [
    {
      required: true,
      message: "请选择实现类",
      trigger: ["blur", "change"],
    },
  ],
});

/* 按钮组 */
function getBtnList(row) {
  return [
    {
      icon: "delete",
      title: "删除",
      onClick(scope) {
        delFn(scope.row);
      },
    },
  ];
}

//获取表格数据
getTableData();
function getTableData() {
  state.ruleForm.tableData = [];
  state.loading = true;
  getDataFieldExtraList()
    .then((res) => {
      state.ruleForm.tableData = res.data;
    })
    .finally(() => {
      state.loading = false;
    });
}

//删除
function delFn(rows) {
  let index = state.ruleForm.tableData.indexOf(rows);
  state.ruleForm.tableData.splice(index, 1);
}

// 添加字段 新增一行
let tableRef = ref();
function addField() {
  state.ruleForm.tableData.push({
    field: "",
    alias: "",
    srcField: "",
    actionTime: 0,
    implClass: "",
    isShow: 0,
    status: 0,
  });
  nextTick(() => {
    document.getElementsByTagName("tr")[state.ruleForm.tableData.length - 1].scrollIntoView(true);
  });
}

// 保存
let ruleFormRef = ref();
let saveLoading = ref(false);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true;
      postDataFieldExtraSave(state.ruleForm.tableData)
        .then((res) => {
          getTableData();
        })
        .finally(() => {
          saveLoading.value = false;
        });
    }
  });
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small .el-form-item__content) {
  /* line-height: 32px; */
  margin-top: 18px;
}
</style>
