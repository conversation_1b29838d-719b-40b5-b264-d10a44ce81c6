<template>
  <!-- 空间资产测绘 - 字段管理 - 列表 -->
  <div class="fieldsDiv">
    <el-form ref="ruleFormRef" :model="ruleForm">
      <div :style="tableHeight">
        <el-table
          :data="ruleForm.tableData"
          :border="parentBorder"
          ref="tableRef"
          height="100%"
          v-loading="loading"
          @sortChange="sortChange"
          row-key="orderNum"
          class-name="drag-index-table"
        >
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="字段名" prop="field">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.field'" :rules="rules.field">
                <el-input v-model="ruleForm.tableData[scope.$index].field" maxlength="20" placeholder="请输入字段名" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="中文名" prop="alias">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.alias'" :rules="rules.alias">
                <el-input v-model="scope.row.alias" placeholder="请输入中文名" />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="源字段" prop="srcField">
            <template #default="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.srcField'" :rules="rules.srcField">
                <el-input v-model="scope.row.srcField" placeholder="请输入源字段" />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column label="字典" prop="dictType">
            <template #default="scope">
              <el-select :placeholder="'请选择字典'" style="width: 100%" v-model="scope.row.dictType" clearable filterable>
                <el-option v-for="item in options" :key="item.dictType" :label="item.dictName" :value="item.dictType"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="是否展示" prop="isShow" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.isShow" :active-value="1" :inactive-value="0" />
            </template>
          </el-table-column>
          <el-table-column label="统计字段" prop="isAggs" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.isAggs" :active-value="1" :inactive-value="0" @change="changeAggs(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="btns" width="120px" v-hasPermi="'assetdetect:dataField:update'">
            <template #default="scope">
              <xel-handle-btns :btnList="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>

    <FieldsTipsCom />

    <div class="mt20 text-center" v-hasPermi="'assetdetect:dataField:update'">
      <el-button @click="addField" style="min-width: 200px">
        <el-icon :size="12"> <plus /> </el-icon>添加字段
      </el-button>
    </div>

    <div class="text-right">
      <el-button @click="getTableData">取消</el-button>
      <el-button type="primary" v-hasPermi="'assetdetect:dataField:update'" @click="submit" :loading="saveLoading">确定</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import FieldsTipsCom from "@/views/sime/components/fieldsTipsCom.vue";
import { getDataFieldList, getFieldAvailableByCnd, postDataFieldSave } from "@/api/sime/assetdetect/fields";
import tableMixin from "../../components/mixin/tableMixin";
import Sortable from "sortablejs";
import { ElMessage } from "element-plus";
let { tableHeight } = tableMixin(340);
let state = reactive({
  ruleForm: {
    tableData: [],
  },
  options: [], //字典类型
  loading: false,
});
let { options, ruleForm, loading } = toRefs(state);
// 获取字典
getDataDicts();
function getDataDicts() {
  getFieldAvailableByCnd()
    .then((res) => {
      state.options = res.data;
    })
    .catch(() => {});
}
// 列表配置项
const columns = [
  {
    prop: "field",
    label: "字段名",
    slotName: "field",
  },
  {
    prop: "fieldText",
    label: "字段别名",
    slotName: "fieldText",
  },
  {
    prop: "dictType",
    label: "字典",
    slotName: "dictType",
  },
  {
    prop: "defaultValue",
    label: "默认值",
    slotName: "defaultValue",
  },
  {
    prop: "important",
    label: "重要字段",
    slotName: "important",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "btns",
  },
];

/* 统计字段 - 切换 */
const changeAggs = (rows) => {
  if (rows.isAggs === 0) return;
  ruleForm.value.tableData.forEach((item) => {
    if (rows.id !== item.id) item.isAggs = 0;
  });
};
import { fieldsValid } from "@/utils/fieldsValid";
const rules = reactive({
  field: [
    {
      required: true,
      /*message: "请输入字段名",*/
      validator: fieldsValid,
      trigger: ["blur", "change"],
    },
  ],
  alias: [
    {
      required: true,
      message: "请输入中文名",
      trigger: ["blur", "change"],
    },
  ],
  srcField: [
    {
      required: false,
      message: "请输入源字段",
      trigger: ["blur", "change"],
    },
  ],
});

/* 按钮组 */
function getBtnList(row) {
  return [
    {
      icon: "delete",
      title: "删除",
      onClick(scope) {
        delFn(scope.row);
      },
    },
  ];
}

//获取表格数据
getTableData();
function getTableData() {
  state.ruleForm.tableData = [];
  state.loading = true;
  getDataFieldList()
    .then((res) => {
      state.ruleForm.tableData = res.data;
    })
    .finally(() => {
      state.loading = false;
      rowDrop();
    });
}

//删除
function delFn(rows) {
  let index = state.ruleForm.tableData.indexOf(rows);
  state.ruleForm.tableData.splice(index, 1);
}

// 添加字段 新增一行
let tableRef = ref();
function addField() {
  state.ruleForm.tableData.push({
    field: "",
    alias: "",
    dictType: "",
    isShow: 0,
    isAggs: 0,
    orderNum: state.ruleForm.tableData.length,
  });
  nextTick(() => {
    document.getElementsByTagName("tr")[state.ruleForm.tableData.length - 1].scrollIntoView(true);
  });
}

// 保存
let ruleFormRef = ref();
let saveLoading = ref(false);
function submit() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let isAggsList = state.ruleForm.tableData.filter((item) => {
        return item.isAggs == 1;
      });
      if (isAggsList.length === 0) {
        ElMessage.info("必须有且只有一个统计字段");
        return false;
      }
      saveLoading.value = true;
      let params = state.ruleForm.tableData.map((tItem, index) => {
        return {
          ...tItem,
          orderNum: index + 1,
        };
      });
      postDataFieldSave(params)
        .then((res) => {
          getTableData();
        })
        .finally(() => {
          saveLoading.value = false;
        });
    }
  });
}

function rowDrop() {
  // 此时找到的元素是要拖拽元素的父容器
  const tbody = document.querySelector(".fieldsDiv .el-table__body-wrapper tbody");
  Sortable.create(tbody, {
    //  指定父元素下可被拖拽的子元素
    draggable: ".el-table__row",
    handle: "td:first-child",
    onEnd({ newIndex, oldIndex }) {
      const currRow = state.ruleForm.tableData.splice(oldIndex, 1)[0];
      state.ruleForm.tableData.splice(newIndex, 0, currRow);
    },
  });
}
let lastParams = {}; //排序使用
let sortParams = {};
//排序
function sortChange({ prop, order }) {
  sortParams.orderByColumn = prop;
  sortParams.isAsc = order == "descending" ? "desc" : "asc";
  getTableData({ ...lastParams, ...sortParams });
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small .el-form-item__content) {
  /* line-height: 32px; */
  margin-top: 18px;
}
</style>
