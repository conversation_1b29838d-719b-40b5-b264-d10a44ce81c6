<template>
  <!-- 空间资产测绘 - tab  -->
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName">
      <el-tab-pane label="空间资产测绘" name="spaceAsset">
        <SpaceAsset :activeName="activeName" />
      </el-tab-pane>
      <el-tab-pane label="测绘数据管理" name="surveyingData">
        <SurveyingData />
      </el-tab-pane>
      <el-tab-pane label="字段管理" name="fields">
        <Fields />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup>
import { ref } from "vue";
import SpaceAsset from "./spaceAsset/index.vue";
import SurveyingData from "./surveyingData/index.vue";
import Fields from "./fields/index.vue";

/* 默认数据项 */
let activeName = ref("spaceAsset");
</script>
<style scoped></style>
