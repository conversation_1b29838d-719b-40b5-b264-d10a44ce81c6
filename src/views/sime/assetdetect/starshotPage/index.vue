<template>
  <!-- 空间资产测绘 - tab  -->
  <div>
    <section class="mb10 flex-end">
      <el-button type="warning" :disabled="loading" plain @click="resetSearchValue('reset')"> 重置 </el-button>
      <el-button type="warning" :disabled="loading" plain @click="openChoseDialog"> 选择展示列 </el-button>
    </section>
    <section>
      <p v-if="headerSearchTrueData.length > 0" class="checkFont">全部结果</p>
      <div v-if="headerSearchTrueData.length > 0" class="searchList">
        <div v-for="item in headerSearchTrueData" :key="item.prop">
          <span>{{ item.label }}：{{ item.searchLabel }}</span>
          <span>
            <el-icon><CloseBold style="color: currentColor; cursor: pointer; top: 2px" @click="resetNowFileld(item.prop)" /></el-icon>
          </span>
        </div>
      </div>
    </section>
    <el-table
      ref="tablePageRef"
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      class="mt10 table-page-warp"
      table-layout="fixed"
      :border="true"
      :resizable="true"
    >
      <!--  -->
      <el-table-column v-for="item in showfields" :key="item.prop" :min-width="getStrSize(item.label, item.prop)">
        <template #header>
          <section class="flex popover-box">
            <span> {{ item.label }}</span>
            <el-popover v-model:visible="item.searchShow" placement="bottom" :width="400" trigger="click">
              <template #reference>
                <section class="flex">
                  <el-icon
                    :style="{ color: item.searchStatus == '1' ? 'var(--el-color-primary)' : '' }"
                    class="searchStatus"
                    @click.stop="searchSelectOptions(item)"
                    ><Filter
                  /></el-icon>
                </section>
              </template>
              <template #default>
                <div class="search-box">
                  <div v-if="item.prop == 'createTime'">
                    <el-date-picker
                      v-model="headerSearchData[item.prop]"
                      style="width: 100%"
                      :type="'daterange'"
                      :value-format="'YYYY-MM-DD'"
                      :format="'YYYY-MM-DD'"
                      range-separator=" - "
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      :default-time="undefined"
                    />
                  </div>
                  <div v-else>
                    <el-input
                      v-model="headerSearchData[item.prop]"
                      :placeholder="'请输入' + item.label"
                      :type="item.props ? item.props.type : ''"
                      clearable
                      @keyup.enter="item.props && item.props.type == 'textarea' ? null : searchHeaderValue(item)"
                    ></el-input>
                  </div>
                  <div class="text-center" style="margin-top: 20px">
                    <el-button type="primary" @click="searchHeaderValue(item)">查询</el-button>
                    <el-button @click="resetNowFileld(item.prop)">重置</el-button>
                  </div>
                </div>
              </template>
            </el-popover>
            <section
              class="caret-wrapper-section"
              :style="
                sortObj.order === item.prop
                  ? {
                      opacity: 1,
                    }
                  : {}
              "
            >
              <span class="caret-wrapper-icon">
                <el-icon
                  @click="changeTable(item, 'asc')"
                  class="caret-top"
                  :size="14"
                  :style="{
                    color: sortObj && sortObj.order === item.prop && sortObj.by === 'asc' ? 'var(--el-color-primary)' : '',
                  }"
                >
                  <CaretTop />
                </el-icon>
                <el-icon
                  @click="changeTable(item, 'desc')"
                  class="caret-bottom"
                  :size="14"
                  :style="{
                    color: sortObj && sortObj.order === item.prop && sortObj.by === 'desc' ? 'var(--el-color-primary)' : '',
                  }"
                >
                  <CaretBottom />
                </el-icon>
              </span>
            </section>
          </section>
        </template>
        <template #default="{ row }">
          <!-- 目标/响应URL -->
          <div v-if="item.prop == 'targeturl'">
            <div v-if="row[item.prop]" class="xel-clickable" @click="goToAssetDetail(row[item.prop], 'url')">
              <el-icon style="margin-right: 5px; top: 3px"><Paperclip /></el-icon>{{ row[item.prop] }}
            </div>
          </div>
          <!-- 关联台账 -->
          <div v-else-if="item.prop == 'assetSort'">
            <div style="text-align: left">
              <div v-if="row.bussinessAssetName" class="xel-clickable" style="margin-bottom: 5px" @click="goToAssetDetail(row.bussinessAssetId, 1)">
                <el-icon style="margin-right: 5px; top: 3px"><Share /></el-icon> {{ row.bussinessAssetName }}
              </div>
              <div v-if="row.resourceAssetName" class="xel-clickable" @click="goToAssetDetail(row.resourceAssetId, 2)">
                <el-icon style="margin-right: 5px; top: 3px"><Share /></el-icon> {{ row.resourceAssetName }}
              </div>
            </div>
          </div>
          <!-- 服务 -->
          <div v-else-if="item.prop == 'service'">
            <div v-if="row[item.prop]" class="tag-text" :class="row.serviceHigh ? 'tag-text-high' : 'tag-text-other'">
              {{ row[item.prop] }}
            </div>
          </div>
          <div v-else>
            <span>
              {{ row[item.prop] }}
            </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" :width="63">
        <template #default="{ row }">
          <xel-handle-btns :btn-list="btnList" :scope="row"></xel-handle-btns>
        </template>
      </el-table-column>
    </el-table>
    <xel-pagination v-if="total > 0" ref="paginationRef" class="xel-table-pagination" :total="total" @change="changePagination" />
  </div>
  <xel-dialog title="查看详情" ref="dialogDetailsRef" height="450px" width="70%" :showCancel="false" :showSubmit="false">
    <el-form v-show="true" :inline="true" :model="detailsDate" label-position="left" class="base-info-form">
      <el-row :gutter="20">
        <template v-for="(item, key) in detailsColumn" :key="key">
          <el-col :span="item.span">
            <el-form-item v-if="!item.slotName" :label="item.label + ':'" :labelWidth="126" :style="{ width: '99%' }">
              <span v-if="item.prop == 'service'">
                <div v-if="detailsDate[item.prop]" class="tag-text" :class="detailsDate.serviceHigh ? 'tag-text-high' : 'tag-text-other'">
                  {{ detailsDate[item.prop] }}
                </div>
              </span>
              <span v-else-if="item.prop == 'assetSort'">
                <div>
                  <div v-if="detailsDate.bussinessAssetName" style="margin-bottom: 5px">
                    {{ detailsDate.bussinessAssetName }}
                  </div>
                  <div v-if="detailsDate.resourceAssetName">{{ detailsDate.resourceAssetName }}</div>
                </div>
              </span>
              <span v-else> {{ detailsDate[item.prop] }} </span>
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>
  </xel-dialog>
  <xel-dialog ref="exportRef" title="选择展示列" @submit="exportEventSubmit">
    <p class="checkFont">
      已选定字段
      <el-popover placement="right-start" :width="200" trigger="hover">
        <div class="margin-inner">可拖拽进行排序</div>
        <template #reference>
          <el-icon><QuestionFilled /></el-icon>
        </template>
      </el-popover>
    </p>
    <section style="padding: 0 20px">
      <VueDraggableNext class="dragArea list-group w-full choseFiled" :list="column" group="site" :sort="true">
        <div v-for="item in column" :key="item.prop" class="pointer" @click="addToChose(item, 'remove')">
          <span v-if="item.hide" style="background: none !important">
            <span>{{ item.label }}</span>
            <el-icon :style="{ color: 'var(--primary-red-color)', top: '2px', left: '2px' }"><Close /></el-icon>
          </span>
        </div>
      </VueDraggableNext>
    </section>
    <p class="checkFont">可选字段</p>
    <div style="padding: 0px 20px">
      <div>
        <div class="filedList">
          <div v-for="item in column" :key="item.prop" @click="addToChose(item, 'add')">
            <span v-if="!item.hide">
              <span>{{ item.label }}</span>
              <el-icon :style="{ color: 'var(--primary-green-color)', top: '2px', left: '2px' }"><Check /></el-icon>
            </span>
          </div>
        </div>
      </div>
    </div>
  </xel-dialog>
</template>

<script setup>
import { VueDraggableNext } from "vue-draggable-next";
import { ref, onMounted, watchEffect } from "vue";
import { selectStarshotAsset } from "@/api/sime/starshot/globalSearch";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
// 表格展示的列内容

const isSortTop = ref(false);
const tableData = ref([]);
const total = ref(0);
const pageSize = ref(10);
const pageNum = ref(1);
const dialogDetailsRef = ref(null);
const detailsDate = ref({});
// 列表查询遮挡
const loading = ref(false);
const searchData = ref({});
// 保存表头选择或输入的值
const headerSearchData = ref({ startTime: "", endTime: "" });
// 搜索时传的值
const headerSearchTrueData = ref([]);
// 展开选择弹窗
const chosefieldDialog = ref(false);
const column1 = ref([
  {
    prop: "ip",
    label: "IP地址",
    hide: true,
    span: 12,
  },
  {
    prop: "port",
    label: "端口",
    hide: true,
    span: 12,
  },
  {
    prop: "service",
    label: "服务",
    hide: true,
    span: 12,
  },
  {
    prop: "targeturl",
    label: "目标Url",
    hide: true,
    span: 12,
  },
  {
    prop: "title", //
    label: "系统标题",
    hide: true,
    span: 24,
  },
  {
    prop: "products",
    label: "产品",
    hide: true,
    span: 24,
  },
  {
    prop: "fingertag",
    label: "指纹标签",
    hide: true,
    span: 24,
  },
  {
    prop: "os",
    label: "操作系统",
    hide: true,
    span: 12,
  },
  {
    prop: "deptName",
    label: "相关单位",
    hide: true,
    span: 12,
  },
  {
    prop: "network",
    label: "网络信息",
    span: 12,
    hide: true,
  },
  {
    prop: "country",
    label: "国家",
    span: 12,
    hide: true,
  },
  {
    prop: "assetSort",
    label: "关联台账",
    hide: true,
  },
  {
    prop: "createTime",
    label: "更新时间",
    hide: true,
  },
  {
    prop: "taskName",
    label: "任务名称",
    hide: true,
    span: 12,
  },
]);
const column = ref([
  {
    prop: "ip",
    label: "IP地址",
    hide: false,
    span: 12,
  },
  {
    prop: "port",
    label: "端口",
    hide: false,

    span: 12,
  },
  {
    prop: "service",
    label: "服务",
    hide: false,

    span: 12,
  },
  {
    prop: "targeturl",
    label: "目标Url",
    hide: false,

    span: 12,
  },
  {
    prop: "title", //
    label: "系统标题",
    hide: false,

    span: 24,
  },
  {
    prop: "products",
    label: "产品",
    hide: false,

    span: 24,
  },
  {
    prop: "fingertag",
    label: "指纹标签",
    hide: false,

    span: 24,
  },
  {
    prop: "os",
    label: "操作系统",
    hide: false,

    span: 12,
  },
  {
    prop: "deptName",
    label: "相关单位",
    hide: false,

    span: 12,
  },
  {
    prop: "network",
    label: "网络信息",
    span: 12,
    hide: false,
  },
  {
    prop: "country",
    label: "国家",
    span: 12,
    hide: false,
  },
  {
    prop: "city",
    label: "城市",
    span: 12,
    hide: false,
  },
  {
    prop: "province",
    label: "省份",
    span: 12,
    hide: false,
  },
  {
    prop: "assetSort",
    label: "关联台账",
    hide: false,
  },
  {
    prop: "createTime",
    label: "更新时间",
  },
  {
    prop: "taskName",
    label: "任务名称",
    hide: false,

    span: 12,
  },
  {
    prop: "protocol",
    label: "Portocol", //开放协议
    hide: false,
    span: 12,
  },
  {
    prop: "hostname",
    label: "HostName", //主机名/域名
    labelWidth: 100,
    hide: false,
    span: 12,
  },
  {
    prop: "cname",
    label: "CName",
    span: 12,
    hide: false,
  },
  {
    prop: "headerstr",
    label: "响应头",
    hide: false,
  },
  {
    prop: "contentlength",
    label: "内容长度",
    hide: false,
  },
  {
    prop: "iconhash",
    label: "IconHash",
    hide: false,
  },
]);
const detailsColumn = ref(JSON.parse(JSON.stringify(column.value)));
/*

*/
// 计算字符串长度
function getStrSize(value, field) {
  if (["url", "domain", "updateTime"].includes(field)) {
    return 9 * 17 + 30;
  }
  if (["service", "assetSort", "products", "fingertag"].includes(field)) {
    return 220;
  }
  if (["action"].includes(field)) {
    return 60;
  }
  const charCount = value.split("").reduce((prev, curr) => {
    if (/[a-z]|[0-9]|[,;.!@#-+/\\$%^*()<>?:"'{}~]/i.test(curr)) {
      return prev + 2;
    }
    return prev + 3;
  }, 0);

  // 向上取整，防止出现半个字的情况
  return Math.ceil(charCount / 2) * 17 + 50;
}
const sortObj = ref({});
function changeTable(row, val) {
  console.log("val: ", val, "------", row, sortObj.value);
  // 点击排序按钮时，切换选中/未选中状态
  if (sortObj.value.order === row.prop && sortObj.value.by === val) {
    // 已选中，再次点击取消选中
    sortObj.value = {};
  } else {
    // 选中当前
    sortObj.value = {
      order: row.prop,
      by: val,
    };
  }
  search();
}
function changePagination(val) {
  console.log("val: ", val);
  pageNum.value = val.pageNum;
  pageSize.value = val.pageSize;
  search();
}
const showfields = ref([]);
const btnList = ref([
  {
    icon: "view",
    title: "详情",
    onClick(row) {
      console.log("row: ", row);
      console.log("详情");
      detailsDate.value = row;
      dialogDetailsRef.value.open();
    },
  },
]);
/*

*/
function searchTable(params) {
  console.log(params);
}
// 重置搜索值
function resetSearchValue() {
  headerSearchData.value = {};
  headerSearchTrueData.value = [];
  sortObj.value = {};
  showfields.value.forEach((item) => {
    item.searchStatus = "0";
  });
  search();
}
// 搜索时，添加到showfields
function searchSelectOptions(item) {
  if (item) {
    showfields.value.forEach((list) => {
      if (item.prop == list.prop) {
        item.searchShow = !item.searchShow;
      } else {
        list.searchShow = false;
      }
    });
  } else {
    showfields.value.forEach((list) => {
      list.searchShow = false;
    });
  }
}
// 搜索时，添加到headerSearchTrueData
function searchHeaderValue(item) {
  let flag = true;
  console.log("headerSearchTrueData.value: ", headerSearchData.value);
  if (!headerSearchData.value[item.prop]) {
    ElMessage.warning("请先输入搜索条件");
    return;
  }
  // 如果存在，则更新
  headerSearchTrueData.value.forEach((list) => {
    if (list.prop == "createTime") {
      if (item.prop == "startTime" || item.prop == "endTime") {
        flag = false;
        list.value = headerSearchData.value[item.prop].join(",");
        list.searchLabel = getSearchLabel(item, headerSearchData.value[item.prop].join(","));
        list.startTime = headerSearchData.value[item.prop][0];
        list.endTime = headerSearchData.value[item.prop][1];
      }
    } else if (list.prop == "assetSort") {
      if (item.prop == "bussinessAssetName" || item.prop == "resourceAssetName") {
        flag = false;
        list.value = headerSearchData.value[item.prop];
        list.searchLabel = getSearchLabel(item, headerSearchData.value[item.prop]);
        list.bussinessAssetName = headerSearchData.value[item.prop];
        list.resourceAssetName = headerSearchData.value[item.prop];
      }
    } else {
      if (list.prop == item.prop) {
        flag = false;
        list.value = headerSearchData.value[item.prop];
        list.searchLabel = getSearchLabel(item, headerSearchData.value[item.prop]);
      }
    }
  });
  if (flag) {
    // 不存在，则添加
    let value = headerSearchData.value[item.prop];
    if (typeof value == "string") {
      value = [value];
    }
    let searchItem = {
      ...item,
      value,
      searchLabel: getSearchLabel(item, headerSearchData.value[item.prop]),
    };
    if (item.prop == "createTime" && value.length > 0) {
      searchItem = {
        ...item,
        value,
        startTime: headerSearchData.value.createTime[0],
        endTime: headerSearchData.value.createTime[1],
        searchLabel: getSearchLabel(item, headerSearchData.value[item.prop].join("--")),
      };
      headerSearchData.value["startTime"] = headerSearchData.value.createTime[0];
      headerSearchData.value["endTime"] = headerSearchData.value.createTime[1];
    } else if (item.prop == "assetSort") {
      headerSearchData.value["bussinessAssetName"] = headerSearchData.value.assetSort;
      headerSearchData.value["resourceAssetName"] = headerSearchData.value.assetSort;
    }
    console.log("searchItem: ", searchItem);
    headerSearchTrueData.value.push(searchItem);
  }
  item.searchShow = false;
  item.searchStatus = "1";
  console.log("headerSearchData.value: ", headerSearchData.value);
  search();
}

function getSearchLabel(item, value) {
  console.log("item: ", item, value);
  return value;
}
// 搜索
function search() {
  loading.value = true;
  const params = { ...JSON.parse(JSON.stringify(headerSearchData.value)) };
  delete params.createTime;
  delete params.assetSort;
  selectStarshotAsset({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    ...params,
    ...sortObj.value,
  })
    .then((res) => {
      tableData.value = res.data.data.rows;
      total.value = res.data.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
search();
function resetNowFileld(prop) {
  if (prop == "createTime") {
    delete headerSearchData.value.startTime;
    delete headerSearchData.value.endTime;
    delete headerSearchData.value.createTime;
  } else if (prop == "assetSort") {
    delete headerSearchData.value.bussinessAssetName;
    delete headerSearchData.value.resourceAssetName;
    delete headerSearchData.value.assetSort;
  } else {
    delete headerSearchData.value[prop];
  }
  let num = -1;
  headerSearchTrueData.value.forEach((item, index) => {
    if (item.prop == prop) {
      num = index;
    }
  });
  if (num >= 0) {
    headerSearchTrueData.value.splice(num, 1);
  }
  showfields.value.map((item) => {
    if (item.prop == prop) {
      item.searchStatus = "0";
    }
  });
  search();
}
const exportRef = ref(null);
const exportList = ref([]);
function openChoseDialog(val) {
  exportRef.value.open();
}
// 添加到已选定字段/ 移除选项
function addToChose(item, type) {
  changeShow(item, type);
}

function changeShow(item, type) {
  column.value.splice(column.value.indexOf(item), 1);
  column.value.push({
    prop: item.prop,
    label: item.label,
    hide: type == "add" ? true : false,
  });
}
const tablePageRef = ref(null);
function exportEventSubmit() {
  //存到本地缓存中
  const items = column.value.filter((item) => item.hide);
  if (items.length > 0) {
    showfields.value = [];
    localStorage.setItem("starshotAssetColumn", JSON.stringify(items));
    nextTick(() => {
      showfields.value = JSON.parse(JSON.stringify(items));
      exportRef.value.close();
    });
  } else {
    ElMessage.warning("请至少选择一个字段");
  }
}
const localItems = localStorage.getItem("starshotAssetColumn");
if (localItems) {
  showfields.value = JSON.parse(localItems);
  changeColumn();
} else {
  showfields.value = JSON.parse(JSON.stringify(column1.value));
  changeColumn();
}
function changeColumn() {
  const propIndexMap = new Map(showfields.value.map((item, index) => [item.prop, index]));
  column.value = column.value
    .map((item) => ({
      ...item,
      hide: propIndexMap.has(item.prop),
    }))
    .sort((a, b) => (propIndexMap.get(a.prop) ?? Infinity) - (propIndexMap.get(b.prop) ?? Infinity));
}
const router = useRouter();

// 添加跳转方法
const goToAssetDetail = (id, assetType) => {
  if (assetType == "url") {
    window.open(id, "_blank");
  } else {
    const activeMenu = assetType == 1 ? "/assetsManage/assetComputed" : "/assetsManage/assetBusiness";
    const params = {
      name: "DetailAsset",
      params: {
        type: "product",
        id: id,
      },
      query: {
        activeMenu: activeMenu,
        assetType: assetType,
      },
    };
    window.$wujie?.bus.$emit("openDetailAsset", params);
  }
};
</script>
<style scoped lang="scss">
/*资产列表动态列页面*/
.choseFiled,
.filedList {
  display: flex;
  flex-wrap: wrap;
  > div > span {
    display: inline-block;
    width: auto;
    padding: 5px 10px;
    border-radius: 10px;
    border: 1px solid #e4e4e4;
    margin-bottom: 10px;
    margin-right: 10px;
  }
  margin-bottom: 10px;
}
.checkFont,
.checkFont2 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}
.checkFont2 {
  font-weight: 400;
  &::before {
    content: " | ";
    color: #ccc;
    font-weight: bold;
    font-size: 16px;
    display: inline-block;
    margin-right: 5px;
  }
}
.margin-inner {
  margin: -13px;
  border-radius: 5px;
  background-color: #ffffdd;
  padding: 15px;
}
.searchList {
  display: flex;
  flex-wrap: wrap;
  > div {
    padding: 5px 10px;
    border-radius: 10px;
    border: 1px solid #e4e4e4;
    margin-bottom: 10px;
    margin-right: 10px;
    > span:nth-child(2) {
      margin-left: 5px;
      color: var(--primary-red-color);
    }
  }
}
.popover-box {
  &:hover {
    .caret-wrapper-section {
      display: block !important;
      opacity: 1;
    }
  }
  &::after {
    content: "";
    display: block;
    width: 1px;
    height: 100%;
    background-color: var(--el-border-color);
    position: absolute;
    right: 1px;
    top: 2px;
  }
}
.searchStatus {
  color: var(--text-color);
  margin-left: 6px;
  cursor: pointer;
  top: 3px;
}

.tree-table-wrapper .select-tree-wrapper-l1 {
  left: 0;
}
::v-deep(.sortable-ghost) {
  background: none !important;
}
.caret-wrapper-section {
  opacity: 0;
}
.caret-wrapper-icon {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  height: 14px;
  width: 24px;
  vertical-align: middle;
  overflow: initial;
  position: relative;
  top: -5px;
  left: -2px;
  color: var(--el-text-color-placeholder);
  .caret-top {
    z-index: 99;
    height: 10px;
    position: absolute;
    left: 7px;
    width: 15px;
    height: 10px;
    cursor: pointer;
  }
  .caret-bottom {
    position: absolute;
    left: 7px;
    top: 6px;
    cursor: pointer;
    width: 15px;
    height: 10px;
  }
}
.base-info-form {
  margin: 0 20px;
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    margin-bottom: 10px;
    padding-bottom: 10px;
  }
}
.tag-text {
  border-radius: 5px;
  text-align: center;
  border: 1px solid #e4e4e4;
  //宽度根据内容自适应
  width: fit-content;
  padding: 0 10px;
}
.tag-text-high {
  background-color: #f56c6c;
  color: #fff;
}
.tag-text-other {
  background-color: #e0e3e8;
  color: #262f3d;
}
//隐藏 border=true 表格的边框
::v-deep(.el-table) {
  border-right: none;
  border-left: none;
  --el-table-border: none; //border=true
  &::before {
    border-bottom: none;
  }
  &::after {
    background: none;
  }
  & > .el-table__row {
    border-bottom: 1px solid #ebedf1; //保留行的下边框
  }
}

::v-deep(.el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell) {
  border-bottom: none;
}
::v-deep(.el-table .cell) {
  position: relative !important;
  padding: 10px !important;
  text-align: center !important;
  display: flex !important;
  justify-content: center;
  .action-btns-ul li {
    & > .el-icon {
      left: -3px;
    }
  }
}

::v-deep(.el-table .el-table__body td, .el-table .el-table__header th) {
  border-top: 1px solid #ebeef5; /* 修改单元格底部边框 */
}

::v-deep(.el-table .el-table__body tr:last-child td) {
  border-bottom: 1px solid #ebeef5;
}
</style>
