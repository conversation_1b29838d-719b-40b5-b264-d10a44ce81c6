<template>
  <div style="height: 100%">
    <div>
      <p class="pull-left fieldTitle">数据信息</p>
      <p class="pull-left change" @click="changeShowOrHide">
        <span>
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-yincang"></use>
          </svg>
        </span>
        <span v-show="fieldisnull">显示空字段</span>
        <span v-show="!fieldisnull">隐藏空字段</span>
      </p>
      <div class="clearfix"></div>
    </div>
    <div class="margin-top20" ref="table_content">
      <el-table :data="state.detail" stripe :show-header="false" border>
        <el-table-column label="字段名" prop="fieldName" />
        <el-table-column label="值" prop="value">
          <template #default="scope">
            <div style="white-space: pre-wrap" v-text="$globalShowOriginStr(scope.row.value)"></div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
let props = defineProps({
  /* 日志详情 */
  logDetail: {
    type: Object,
    default() {
      return {};
    },
  },
  /* 日志类型对应字段列表 */
  type_list: {
    type: Array,
    default() {
      return [];
    },
  },

  /* 新增参数 - 供事件引用使用 类型 = event */
  formType: {
    type: String,
    default() {
      return "";
    },
  },
});

let state = reactive({
  detail: [],
});

/* 显示隐藏空字段 */
let fieldisnull = ref(false);
function changeShowOrHide() {
  fieldisnull.value = !fieldisnull.value;
  getLogDetail();
}
async function getLogDetail() {
  let arr = props.logDetail;
  state.detail = [];
  props.type_list.forEach((item) => {
    /* 事件原始日志查看 */
    if (props.formType === "event") {
      if (fieldisnull.value && arr[item.value] !== "" && arr[item.value] !== null && arr[item.value] !== undefined) {
        state.detail.push({
          fieldName: item.name,
          value: arr[item.value + "Text"] || arr[item.value],
        });
      } else if (!fieldisnull.value) {
        state.detail.push({
          fieldName: item.name,
          value: arr[item.value + "Text"] || arr[item.value],
        });
      }
      return false;
    }

    /* 空间资产测绘 */
    if (fieldisnull.value && arr[item.field] !== "" && arr[item.field] !== null && arr[item.field]) {
      state.detail.push({
        fieldName: item.alias,
        value: arr[item.field + "Text"] || arr[item.field],
      });
    } else if (!fieldisnull.value) {
      state.detail.push({
        fieldName: item.alias,
        value: arr[item.field + "Text"] || arr[item.field],
      });
    }
  });
}

onMounted(() => {
  getLogDetail(props.logDetail);
});
</script>

<style lang="scss" scoped>
.fieldTitle {
  font-weight: 600;
  font-size: 18px;
}
.change {
  color: #127dca;
  border: 1px solid #127dca;
  padding: 3px 5px;
  border-radius: 3px;
  margin-left: 10px;
  cursor: pointer;
}
.margin-top20 {
  height: 85vh;
  overflow-y: auto;
  padding-bottom: 50px;
}
</style>
