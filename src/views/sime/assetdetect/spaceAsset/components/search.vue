<template>
  <div class="conH3Tit siem-search-conH3Tit p0-new bg-p-border-new m0-new">
    <div class="pull-right">
      <el-button size="mini" @click="reset()" :loading="$store.state.siem.btnDis">重置</el-button>
      <el-button type="primary" size="mini" @click="saveFastFilter">保存查询</el-button>

      <!-- 新增 - 二次筛选 - 修改 -->
      <el-dropdown>
        <el-button size="mini" @click="changeErciFilter">
          <el-icon><search /></el-icon> 二次筛选
          <el-icon><ArrowDown /></el-icon>
        </el-button>

        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-for="item in dropOptin" :key="item.value" :disabled="dropActive === item.value" @click="handleCommand(item.value)">
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-popover placement="left" :width="300" trigger="click" :disabled="state.search_template_list.length == 0">
        <template #reference>
          <el-button size="mini" @click="getTemplateListLength">
            <el-icon><search /></el-icon> 快速查询
          </el-button>
        </template>
        <el-scrollbar height="300px">
          <ul class="search_template_list">
            <li
              v-for="item in state.search_template_list"
              :key="item.id"
              @click.stop="choseTemplate(item)"
              @mouseenter="item.showDelete = true"
              @mouseleave="item.showDelete = false"
            >
              {{ item.name }}
              <el-icon
                v-show="item.showDelete"
                :size="10"
                class="pull-right"
                color="#ba271d"
                style="margin-right: 20px; margin-top: 13px; background: #efdfe1; color: #ba271d; border-radius: 3px"
                @click.stop="deleteFilter(item)"
                ><close
              /></el-icon>
            </li>
          </ul>
          <p v-if="state.search_template_list.length == 0" class="text-center">暂无数据</p>
        </el-scrollbar>
      </el-popover>
    </div>
    <div class="clearfix"></div>
  </div>
  <div class="searchBody bg-p-border-new">
    <el-form ref="search_form" label-width="120px">
      <el-row>
        <el-col class="titleCol"> 网络区域及索引 </el-col>
        <el-col :span="6" v-for="item in indexRecord.slice(0, 4)" :key="item.id">
          <el-form-item>
            <template #label>
              <span style="display: inline-block; width: 110px" class="ellipsis">
                {{ item.name }}
              </span>
            </template>
            <el-select v-model="item.indexId" clearable filterable>
              <el-option v-for="item in item.indexRecords" :key="item.id" :label="item.indexName" :value="item.indexName" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-collapse-transition style="width: 100%">
          <el-row v-show="linkAllShow">
            <el-col :span="6" v-for="item in indexRecord.slice(4)" :key="item.id">
              <el-form-item label-width="120px">
                <template #label>
                  <span style="display: inline-block; width: 110px" class="ellipsis">
                    {{ item.name }}
                  </span>
                </template>
                <el-select v-model="item.indexId" clearable filterable>
                  <el-option v-for="item in item.indexRecords" :key="item.id" :label="item.indexName" :value="item.indexName" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-transition>
        <el-col>
          <el-link type="primary" :underline="false" @click="linkAllShow = !linkAllShow" class="linkAll" v-if="indexRecord.length > 4">
            {{ linkAllShow ? "收起更多" : "展开更多" }}
          </el-link>
          <el-form-item label="筛选条件">
            <condition-result
              :isShowEye="false"
              v-if="
                (conditionList.length && dropActive === 1) ||
                (showCondition && judgmentContentRealTime[0] && judgmentContentRealTime[0].children.length && dropActive === 2)
              "
              :list="dropActive === 1 ? conditionList : arrFitFun(judgmentContentRealTime[0].children, conditionList)"
              style="margin-top: 2px; padding: 2px"
            />
            <condition-result
              :isShowEye="false"
              v-else-if="ConditionList && ConditionList.length && assetdetectVersion == 1"
              :list="ConditionList"
              style="margin-top: 2px; padding: 2px"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" v-show="erciFilter">
          <!-- {{ filterType.add_list }} -->
          <el-form-item label="二次筛选">
            <!--  原简单查询 -->
            <div class="two_filter_list" v-if="dropActive === 1">
              <div class="two_filter" v-for="(item, index) in filterType.add_list" :key="item.id">
                <span class="colse_filter">
                  <span @click="resetErci(index)">
                    <el-icon color="#D4D4D4"><refresh-right /></el-icon>
                  </span>
                  <span @click="remove_add_filter(index)">
                    <el-icon color="#D4D4D4"><close-bold /></el-icon>
                  </span>
                </span>
                <edit-condition
                  v-if="showEditCondition"
                  :type-list="type_list"
                  :data="item"
                  :key="index + delKey"
                  :hidField="['time']"
                  @finish="
                    (a, b) => {
                      changeValue(index, a, b);
                    }
                  "
                ></edit-condition>
              </div>
              <span class="add_filter" @click="add_filter">
                <el-icon>
                  <Plus />
                </el-icon>
              </span>
            </div>

            <!-- 新增 - 新查询  -->
            <div class="newConDiv" v-else>
              <div class="newConComDiv" v-show="showCon">
                <template v-if="assetdetectVersion == 1">
                  <condition
                    v-if="showCondition"
                    ref="conditionRef"
                    :showResult="false"
                    :data="conditionsObject"
                    :type-list="type_list"
                    :labelList="labelList"
                    @getConditionList="getConditionList"
                    @delItemFun="delItemFun"
                    :isShowClose="true"
                    @closeFun="changeErciFilter"
                  />
                </template>

                <template v-else-if="assetdetectVersion == 2">
                  <conditionNew
                    v-if="showCondition"
                    :list="judgmentContent"
                    :content-chose-id="choseId"
                    :chose-if-id="choseIfId"
                    :type-list="type_list"
                    @change-list="changeConditionData"
                    :haveFilter="false"
                    @deleteItemFun="delItemFun"
                    :isShowClose="true"
                    @closeFun="changeErciFilter"
                  />
                </template>
              </div>
              <span class="lookBtn" @click="showCon = !showCon">
                <svg class="icon" aria-hidden="true" v-if="!showCon">
                  <use xlink:href="#icon-chakan"></use>
                </svg>
                <svg class="icon" aria-hidden="true" v-else>
                  <use xlink:href="#icon-yincang"></use>
                </svg>
              </span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="">
            <el-button type="primary" size="mini" @click="searchTable" :loading="$store.state.siem.btnDis"> 查询 </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>

  <!-- 保存查询 - 弹窗 -->
  <xelDialog title="快速查询模板" ref="dialogRef" width="500px" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleForm" label-width="auto">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item" />
    </el-form>
  </xelDialog>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted, watch, computed, getCurrentInstance } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import editCondition from "@/views/sime/components/editCondition.vue";
import { batchDelete } from "@/utils/delete";
import conditionResult from "@/views/sime/components/conditionResult.vue"; //条件语句

/* 新查询 */
import conditionNew from "@/views/sime/components/newCondition/ifContent.vue";
import useCondition from "@/views/sime/components/newCondition/conditionEdit";
import useSiemRuleEditorVersion from "@/utils/siemRuleEditorVersion";
import conditionTypeList from "@/views/sime/utils/conditionTypeList";
import condition from "@/views/sime/components/condition.vue";
import dicsStore from "@/store/modules/dictsData";
if (!dicsStore.state.numberFlag || !dicsStore.state.stringFlag || !dicsStore.state.ruleFlag) {
  dicsStore.actions.getFilterOperatorNumRes();
  dicsStore.actions.getFilterOperatorStrRes();
  dicsStore.actions.getRuleOperatorRes();
}
const { assetdetectVersion } = useSiemRuleEditorVersion();

import {
  getIndexRecord,
  searchTemplateList,
  saveFilterTemplate,
  updateSearchTemplate,
  deleteSearchTemplate,
} from "@/api/sime/assetdetect/assetdetectList";

/* 查询条件 */
let linkAllShow = ref(false);

let indexRecord = ref([]);
const getIndexRecordFun = (type) => {
  getIndexRecord().then((res) => {
    res.data.forEach((item) => {
      item.indexId = item.indexRecords[0].indexName;
    });
    indexRecord.value = res.data;
    if (type !== "reset") return;
    searchTable();
  });
};
getIndexRecordFun();

//暴露给父组件的函数，用于刷新区域标签列表
defineExpose({
  reloadTags: getIndexRecordFun,
});
/* 条件 list */
let labelList = ref([
  {
    notetype: "event",
    icon: "icon-IF",
    label: "事件",
    color: import.meta.env.VITE_COLOR,
  },
  {
    notetype: "or",
    icon: "icon-or",
    label: "OR",
    color: "#409EFF",
    bgColor: "#ECF5FF",
  },
  {
    notetype: "and",
    icon: "icon-and",
    label: "AND",
    color: "#67C23A",
    bgColor: "#F0F9EB",
  },
  {
    notetype: "not",
    icon: "icon-not",
    label: "NOT",
    color: "#F56C6B",
    bgColor: "#FEF0F0",
  },
  {
    notetype: "condition",
    icon: "icon-condition",
    label: "条件",
    color: "#E6A23C",
    bgColor: "#f0f1f4",
    editStatus: true,
    kong: false,
    data: {
      nameText: "", //filledText
      operatorText: "", //操作汉字
      valueText: "",
    },
  },
]);

const ConditionList = ref([]);
const getConditionList = (data) => {
  let targetData = data[0].children.length ? data[0].children : data;
  ConditionList.value = [...conditionList.value, ...targetData];
};

/* 删除 */
const delItemFun = (data) => {
  if (data && !data.data) return;
  let dataIndex = filterType.add_list.findIndex((item) => {
    return (
      item.name === data.data.name &&
      item.nameText === data.data.nameText &&
      item.operator === data.data.operator &&
      item.operatorText === data.data.operatorText &&
      item.value === data.data.value &&
      item.valueText === data.data.valueText
    );
  });
  if (dataIndex !== -1) {
    remove_add_filter(dataIndex);
  }

  let dataIndexs =
    conditionsObject.value &&
    conditionsObject.value.children &&
    conditionsObject.value.children.findIndex((item) => {
      return (
        item.name === data.data.name &&
        item.nameText === data.data.nameText &&
        item.operator === data.data.operator &&
        item.operatorText === data.data.operatorText &&
        item.value === data.data.value &&
        item.valueText === data.data.valueText
      );
    });
  if (dataIndexs !== -1) {
    conditionsObject.value && conditionsObject.value.children && conditionsObject.value.children.splice(dataIndexs, 1);
  }
};
let props = defineProps({
  type_list: {
    type: Array,
    default() {
      return [];
    },
  },
  field_add_list: {
    type: Object,
    default() {
      return {};
    },
  },
});

/* 新查询 - 相关 */
let showCon = ref(true);
let conditionsObject = ref();
let newForm = ref({
  formData: {
    indexId: "",
  },
});

let { filterListSameIndexId, typeList, showCondition, confirmIndexId, emptyLastIndexId } = conditionTypeList(
  newForm.value,
  10000,
  true,
  false,
  () => {
    conditionsObject.value = {};
  }
);

//条件编辑
const { choseId, getChoseId, choseIfId, getIfId, judgmentContent, changeConditionData, judgmentContentRealTime, getConditionsData, echoConditions } =
  useCondition(showCondition);

/* 新增 - 二次筛选 - 相关*/
let dropActive = ref(1);
let dropOptin = ref([
  { value: 1, name: "简单筛选" },
  { value: 2, name: "高级筛选" },
]);
/* 切换事件 */
const handleCommand = (data) => {
  dropActive.value = data;
  if (!erciFilter.value) {
    changeErciFilter();
  } else {
    erciFilter.value = true;
  }
  /* 切换后，清除原选项 */
  resetTwoQuery();
};

/* 数据适配方法 */
const arrFitFun = (nArr, oArr) => {
  /*if(oArr.length === 0) return [...oArr];*/
  if (oArr.length === 0) return [...nArr];
  if (nArr[0].name || nArr[0].children) {
    return [...oArr, ...nArr];
  } else {
    return [...oArr];
  }
};

watch(
  () => props.field_add_list,
  (newVal, oldVal) => {
    let data = JSON.parse(JSON.stringify(newVal));
    let selectType = props.type_list.find((item) => item.field == newVal.name);
    if (selectType.dictionaryType) {
      data.value = data.value.toString();
    }
    if (JSON.stringify(data) !== "{}") {
      let i = 0;
      filterType.add_list.forEach((item) => {
        if (
          data.name === item.name &&
          data.operator === item.operator &&
          data.operatorText === item.operatorText &&
          data.nameText === item.nameText &&
          data.value === item.value
        ) {
          i = i + 1;
        }
      });

      if (i <= 0) {
        showEditCondition.value = false;
        if (filterType.add_list.length > 0) {
          let oldArr = filterType.add_list[filterType.add_list.length - 1];
          if (oldArr.name === "" && oldArr.operator === "" && oldArr.value === "") {
            filterType.add_list.splice(filterType.add_list.length - 1, 1);
          }
        }

        filterType.add_list.push(data);
        /*
         * 新增
         * filterType.add_list 保持原规则 与 原校验方式
         * dropActive = 2 新编辑器
         * 数据处理 为 新编辑器格式
         * */
        if (dropActive.value === 2) {
          let newData = JSON.parse(JSON.stringify(data));
          newData.content = "";
          newData.notetype = "condition";
          newData.valueType = "input";

          /* 老编辑器 */
          if (assetdetectVersion.value == 1) {
            let getLists = conditionRef.value.getList();
            getLists[0].children.push(newData);
            conditionsObject.value = getLists[0];
          } else {
            let newObject = getConditionsData();
            let newD = null;
            if (!newObject) {
              newD = {
                name: "事件",
                notetype: "and",
                children: [newData],
              };
            } else {
              newObject.children.push(newData);
              newD = newObject;
            }
            showCondition.value = false;
            setTimeout(() => {
              judgmentContent.value = echoConditions(newD);
              showCondition.value = true;
            }, 100);
          }
        }

        setTimeout(() => {
          showEditCondition.value = true;
          erciFilter.value = true;
          showCon.value = true;
          ElMessage.success("已添加二次筛选条件");
        }, 500);
      } else {
        ElMessage.warning("请勿重复添加二次筛选条件");
      }
    }
  }
);

// 判断模板列表长度
function getTemplateListLength() {
  if (state.search_template_list.length == 0) {
    ElMessage.warning("无快速查询信息");
  }
}

let conditionList = computed(() => {
  let list = [];
  let ss = JSON.parse(JSON.stringify(state.filter_text1_body));

  if (ss && ss.children && ss.children.length) {
    list.push({ name: "filter", notetype: "condition", filterOptions: ss.children });
  }

  /* 简单筛选 */
  if (dropActive.value === 1) {
    for (let item of filterType.add_list.filter((l) => l.name)) {
      list.push({
        ...item,
        notetype: "condition",
      });
    }
  }
  return list;
});

// 传值
let emits = defineEmits(["search", "filterTypeList"]);
let state = reactive({
  search_form_data: {
    time_list: [],
    CY_time_list: [],
    filter_list: [],
    all_filter_list: [],
    timeSwitch: "0",
    time: "",
    timeType: "0",
  },
  search_template_list: [],
  search_data: {
    indexId: "",
    filtersArray: [],
  },
  filterId: "",
  filter_text1_body: {},
});
let filterType = reactive({
  type_list: props.type_list,

  add_list: [
    {
      name: "",
      operator: "",
      value: "",
      nameText: "",
      operatorText: "",
      valueText: "",
      operatorList: [],
      valueType: "input",
      kong: false,
    },
  ],
});
// 查询
function searchTable() {
  let arr = [];
  let noGo = false;
  filterType.add_list.forEach((item) => {
    if (item.name !== "" && item.operator !== "") {
      if (item.operator === "isnull" || item.operator === "notnull") {
        arr.push(item);
      } else if (item.value !== "") {
        arr.push(item);
      } else {
        ElMessage.warning("请输入搜索条件！");
        noGo = true;
      }
    }
  });
  if (noGo) {
    return false;
  }
  state.search_data.filtersArray = arr;
  let data = chuli(state.search_data);

  /* 修改 - 接口数据格式变更，增加字段 */
  let newData = addNewWord(data);
  if (!newData) return;
  emits("search", newData);
}

/* 新增 - 字段 */
let conditionRef = ref();
const addNewWord = (data) => {
  let newData = JSON.parse(JSON.stringify(data));
  newData.filtersType = dropActive.value;

  if (dropActive.value === 1) {
    newData.filters = JSON.stringify(data.filtersArray);
  } else {
    if (erciFilter.value) {
      if (assetdetectVersion.value == 1) {
        let conditions = conditionRef.value ? conditionRef.value.getList() : [];
        if (!conditions) return;
        if (conditions && conditions.length && conditions[0].children.length) {
          newData.filters = JSON.stringify(conditions[0]);
        } else {
          newData.filters = "";
        }
      } else {
        let newObject = getConditionsData();
        if (!newObject) {
          ElMessage.warning("请完善条件!");
          return false;
        }
        !newObject ? (newData.filters = "") : (newData.filters = JSON.stringify(newObject));
      }
    }
  }

  /* 网络标签 */
  /*newData = {...newData, ...indexRecordFun()}*/
  let indexR = indexRecordFun();
  if (!indexR) return false;
  newData = { ...newData, ...indexR };
  delete newData.filtersArray;
  return newData;
};

/* 清除二次筛选 */
const resetTwoQuery = () => {
  showCondition.value = false;
  showCon.value = false;
  filterType.add_list = [
    {
      name: "",
      operator: "",
      value: "",
      nameText: "",
      operatorText: "",
      valueText: "",
      valueType: "input",
      kong: false,
    },
  ];

  /*if(state.search_data.indexId) {
    newForm.value.formData.indexId = state.search_data.indexId;
    confirmIndexId(state.search_data.indexId);
  }*/
  judgmentContent.value = echoConditions({});
  conditionsObject.value = {};
  setTimeout(() => {
    showCondition.value = true;
    showCon.value = true;
  }, 100);
};

// 重置
function reset() {
  state.search_data = {
    indexId: "",
    filtersArray: [],
  };
  state.search_form_data.time_list = [];
  state.search_form_data.timeSwitch = "0";
  state.search_form_data.time = null;
  state.search_form_data.timeType = "0";
  state.filterId = "";
  state.filter_text1_body = {};
  filterType.type_list = [];
  resetTwoQuery();
  erciFilter.value = false;
  getIndexRecordFun("reset");
}

/* 网络标签 - 处理方法 */
const indexRecordFun = () => {
  let indexData = {};
  indexRecord.value.forEach((item) => {
    if (item.indexId) {
      indexData = { ...indexData, [item.id]: item.indexId };
    }
  });
  if (JSON.stringify(indexData) !== "{}") {
    let newData = {};
    newData.networkareaIndexs = JSON.stringify(indexData);
    return newData;
  } else {
    ElMessage.info("网络区域及索引不能为空");
    return false;
  }
};

// 重置二次查询条件
function resetErci(index) {
  showEditCondition.value = false;

  setTimeout(() => {
    filterType.add_list[index] = {
      name: "",
      operator: "",
      value: "",
      nameText: "",
      operatorText: "",
      valueText: "",
      valueType: "input",
      kong: false,
    };

    showEditCondition.value = true;
  }, 100);
}
// 删除快速查询
function deleteFilter(item) {
  batchDelete().then(() => {
    deleteSearchTemplate(item.id).then(() => {
      ElMessage.success("操作成功");
      getSearchTemplateList();
    });
  });
}

// 筛选条件
let filter_text1 = ref("");
let filter_text2 = ref("");
watch(
  () => state.filter_text1_body,
  (newVal, prevCount) => {
    if (JSON.stringify(newVal) !== "{}") {
      filter_text1.value = pinjieSX(newVal);
    } else {
      filter_text1.value = "";
    }
  }
);
watch(
  () => filterType.add_list,
  (addList, prevCount) => {
    let str = "";
    if (addList.length > 0) {
      addList.forEach((item, index) => {
        if (item.name !== "" && item.operator !== "") {
          str = str + "AND (" + item.nameText + " " + item.operatorText + " " + item.valueText + ")";
        }
      });
      filter_text2.value = str;
    }
  },
  { deep: true }
);
// 拼接筛选条件
function pinjieSX(node) {
  if (node.children) {
    let chd = node.children;
    let type = node.notetype;
    let c = "";
    for (let i = 0; i < chd.length; i++) {
      let sc = pinjieSX(chd[i]);
      if (sc !== null) {
        if (c.length > 0) {
          let s = "or" === type ? "OR" : "AND";
          c = c + " " + s + " ";
        }
        c = c + sc;
      }
    }
    if (c.toString().trim().length === 0) {
      return null;
    } else {
      let str = "not" === type ? "NOT" : "";
      str = str + "(" + c.toString() + ")";
      return str;
      // return ("not".equals(type) ? "not " : "") + "(" + c.toString() + ")";
    }
  } else {
    return node.nameText + " " + node.operatorText + "  " + node.valueText;
  }
}

let filter_select = ref();
let filter_text_show = ref(false);

// 二次筛选
let erciFilter = ref(false);
async function changeErciFilter() {
  if (!erciFilter.value) {
    showEditCondition.value = false;
    erciFilter.value = true;
    setTimeout(() => {
      showEditCondition.value = true;
    }, 100);
  } else {
    erciFilter.value = false;
  }
  resetTwoQuery();
}
// 二次筛选type
function changeValue(index, result, data) {
  if (!filterType.add_list[index]) return;
  if (result) {
    filterType.add_list[index] = JSON.parse(JSON.stringify(data));
  }
  filterType.add_list[index].finished = result;
}
// 添加多条二次筛选
function add_filter() {
  let unfinishIndex = filterType.add_list.findIndex((item) => !item.finished);
  if (unfinishIndex > -1) {
    ElMessage.warning("请先填写已添加的二次筛选条件");
    return;
  }
  filterType.add_list.push({
    name: "",
    operator: "",
    value: "",
    nameText: "",
    operatorText: "",
    valueText: "",
    valueType: "input",
    kong: false,
  });
}
let delKey = ref(false);
function remove_add_filter(index) {
  filterType.add_list.splice(index, 1);
  delKey.value = !delKey.value;

  setTimeout(() => {}, 500);
}
// 保存的数据处理
/* 扩展 - 增加参数 verify 用于提交是检验格式 - 不影响以前逻辑默认 false */
function chuli(cpData, verify = false) {
  /* 看不懂原逻辑，增加深拷贝，防止数据混乱 */
  let data = JSON.parse(JSON.stringify(cpData));
  if (data.filtersArray.length > 0 && dropActive.value === 1) {
    data.filtersArray.forEach((item, index) => {
      if (item.valueType === "input") {
        /*item.valueText = item.value;*/
      }
      if (item.valueType === "datetime") {
        //? 筛选条件不显示时间戳，屏蔽重置valueText
        // item.valueText = item.value;
      }
      if (item.valueType === "datetimerange") {
        // item.valueText = item.value[0] + "," + item.value[1];
        // item.value = item.valueText;
      }
      if (item.name === "" && item.nameText === "") {
        ElMessage.warning("请完善第" + (index + 1) + "条筛选条件");
        if (verify) {
          data = false;
        }
        return false;
      }
      if (item.operator === "") {
        ElMessage.warning("请完善第" + (index + 1) + "条筛选条件");
        if (verify) {
          data = false;
        }
        return false;
      }

      if (item.operator !== "isnull" && item.operator !== "notnull") {
        if (item.value === "" && item.valueText === "") {
          ElMessage.warning("请完善第" + (index + 1) + "条筛选条件");
          if (verify) {
            data = false;
          }
          return false;
        }
      }
      delete item.valueType;
      delete item.kong;
    });
  }
  return data;
}

/* 查询保存弹窗相关 */
let dialogRef = ref();
let form = reactive({
  formData: {
    type: 1,
  },
});
let { formData } = toRefs(form);
/* 表单内容 */
let formList = ref([
  {
    formType: "input",
    prop: "name",
    label: "模板名称",
    required: true,
  },
]);

/* 保存查询 - 按钮事件 */
function saveFastFilter() {
  /* 根据是否存在 id 判断 */
  if (state.search_data.id) {
    form.formData.name = state.search_data.name;
    formList.value.push({
      formType: "radio",
      prop: "type",
      label: "类型",
      required: true,
      hide: true,
      options: [
        { value: 0, label: "新增" },
        { value: 1, label: "修改" },
      ],
    });
  }
  dialogRef.value.open();
}
/* 修改类型 - 清空 name*/
watch(
  () => form.formData.type,
  (val) => {
    if (val === 0) {
      form.formData.name = "";
    }
  }
);

/* 提交按钮 */
let ruleForm = ref();
const submitForm = async function (close, loading) {
  let valid = await ruleForm.value.validate();
  if (valid) {
    /* 数据逻辑处理 */
    state.search_data.name = form.formData.name;
    let data = null;
    if (erciFilter.value === true) {
      state.search_data.filtersArray = JSON.parse(JSON.stringify(filterType.add_list));
    }

    /* 增加 - 数据验证 - 格式不正确返回 false */
    data = chuli(state.search_data, true);
    if (!data) {
      close();
      return false;
    }

    let arr1 = [];
    let arr2 = [];
    props.type_list.forEach((fields) => {
      if (fields.isShow === 1) {
        arr1.push(fields.field);
        arr2.push(fields.alias);
      }
    });
    data.fieldsArray = arr1;
    data.fieldsTextArray = arr2;

    loading();
    let API = form.formData.type === 0 ? saveFilterTemplate : updateSearchTemplate;
    if (form.formData.type === 0) delete data.id;
    /* 修改 - 接口数据格式变更，增加字段 */
    let newData = addNewWord(data);
    if (!newData) return;

    API(newData)
      .then((res) => {
        if (res.code === 200) {
          ElMessage.success("保存快速查询模板成功");
          close();
          getSearchTemplateList();
        }
      })
      .catch(() => {
        close(false);
      });
  }
};
/* 重置 */
const closeDialog = () => {
  form.formData = {
    type: 1,
  };
  formList.value = [
    {
      formType: "input",
      prop: "name",
      label: "模板名称",
      required: true,
    },
  ];
};

// 获取快速查询列表
function getSearchTemplateList() {
  searchTemplateList().then((res) => {
    state.search_template_list = res.data.rows;
    state.search_template_list.forEach((item) => {
      item.showDelete = false;
    });
  });
}
getSearchTemplateList();
// 选择快速查询模板
let showEditCondition = ref(true);

watch(
  () => props.type_list,
  (val) => {
    if (showEditCondition && showEditCondition.value != "undefined") showEditCondition.value = true;
  },
  { immediate: true }
);

async function choseTemplate(item) {
  let count = 0;
  state.search_form_data.filter_list.forEach((filter) => {
    if (filter.filterId == item.filterId) {
      count = count + 1;
    }
  });
  state.search_data.id = item.id;
  state.search_data.name = item.name;
  state.search_data.filterId = item.filterId;
  /*state.search_data.indexId = item.queryFilter.indexId;*/

  /*emits("filterTypeList", state.search_data.indexId, item.fieldsArray);*/
  if (count <= 0) {
    state.filterId = item.filterId;
  }
  /*state.filter_text1_body = JSON.parse(item.queryFilter.conditions);*/

  /* 网络区域回填 */
  let networkareaIndexs = JSON.parse(item.networkareaIndexs);
  for (let i in networkareaIndexs) {
    indexRecord.value.forEach((v) => (v.id === i ? (v.indexId = networkareaIndexs[i]) : (v.indexId = "")));
  }

  /* 二次筛选回填 */
  if (item.filtersType == 1) {
    let filtersArray = JSON.parse(item.filters);
    if (filtersArray.length > 0) {
      erciFilter.value = true;
      dropActive.value = 1;
      filtersArray.forEach((filter, index) => {
        let str = filter.name.substr(0, 1);
        let str2 = filter.name.substr(filter.name.length - 4);
        let chose = {};
        let valueType = "";
        let kong = false;
        let value = filter.value;
        props.type_list.forEach((type) => {
          if (filter.name === type.field) {
            chose = type;
          }
        });
        if (chose.dictionaryType) {
          valueType = "select";
        } else {
          if (str2 === "time") {
            if (filter.operatorText === "属于" || filter.operatorText === "不属于") {
              valueType = "datetimerange";
              value = filter.value.split(",");
            } else {
              valueType = "datetime";
            }
          } else {
            valueType = "input";
          }
        }
        // alert(valueType);
        showEditCondition.value = false;
        if (filter.operatorText === "为空" || filter.operatorText === "不为空") {
          kong = true;
        }
        if (index === 0) {
          filterType.add_list = [];
        }
        filterType.add_list.push({
          name: filter.name,
          operator: filter.operator,
          value: value,
          nameText: filter.nameText,
          operatorText: filter.operatorText,
          valueText: filter.valueText,
          valueType: valueType,
          kong: kong,
        });
      });

      setTimeout(() => {
        showEditCondition.value = true;
      }, 1000);
    }
  } else {
    showCondition.value = false;
    erciFilter.value = true;
    showCon.value = true;
    dropActive.value = 2;
    let filters = JSON.parse(item.filters);
    setTimeout(() => {
      if (assetdetectVersion.value == 1) {
        conditionsObject.value = filters;
      } else {
        judgmentContent.value = echoConditions(filters);
      }
      showCondition.value = true;
    }, 500);
  }
}
// 字段统计点击过滤后添加二次查询条件
function field_add_list(data) {}
</script>

<style lang="scss" scoped>
.linkAll {
  float: right;
  top: -15px;
}
.el-dropdown {
  .el-button {
    margin: 0 10px;
  }
}

.newConDiv {
  position: relative;
  .newConComDiv {
    width: 80%;
    display: inline-block;
    background: #eeeeee;
    border-radius: 8px;
    padding: 20px;
    margin-right: 10px;
    /* 原-编辑，样式微调 */
    ::v-deep .condition-item-ul li .condition-item-ul {
      line-height: 15px;
    }
    ::v-deep .condition-wrapper {
      padding-top: 5px;
    }
  }
  .lookBtn {
    position: absolute;
    /*right: -40px;
    top: 10px;*/
    width: 32px;
    height: 32px;
    line-height: 32px;
    background: #ffe7d4;
    border-radius: 8px;
    border: 1px solid #ef8936;
    text-align: center;
    cursor: pointer;
    color: #ef8936;
    svg {
      font-size: 18px;
    }
  }
}

/* 新增- 覆盖新查询样式 - 不影响原效果 */
::v-deep .if-conent .item-wrapper:nth-child(1)::before,
::v-deep .if-conent .item-wrapper:last-child::before {
  background: #eeeeee !important;
}
::v-deep .add-formula:before {
  background: #dedede;
}

.searchBody {
  /*margin-top: 20px;*/
  .titleCol {
    font-weight: bold;
    margin-bottom: 10px;
    color: #848484;
  }
}
.CY_time_list {
  > span {
    float: left;
    padding: 0px 10px;
    border: 1px solid $borderColor;
    border-radius: $raduisM;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $fontColor;
    font-weight: 400;
    cursor: pointer;
  }
  > span.chose {
    border-color: $color;
    color: $color;
  }
}
.filter_list {
  width: calc(100% - 300px);
  > span {
    float: left;
    // height: 36px;
    // line-height: 36px;
    padding: 0px 10px;
    border-radius: $raduisM;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: $fontColor;
    font-weight: 400;
    cursor: pointer;
  }
  > span.chose {
    color: #2797ff;
  }
}
.more_filter {
  font-size: 14px;
  color: #333333;
  position: relative;
  text-align: right;
  > span {
    cursor: pointer;
  }
  > div {
    position: absolute;
    right: 55px;
    top: 0px;
    height: 0px;
    width: 280px;
    overflow: hidden;
    transition: height 0.5s;
    -moz-transition: height 0.5s; /* Firefox 4 */
    -webkit-transition: height 0.5s; /* Safari 和 Chrome */
    -o-transition: height 0.5s;
  }
}
.two_filter_list {
  > .two_filter {
    width: 23%;
    float: left;
    margin-right: 1%;
    margin-bottom: 20px;
    background: #eee;
    border-radius: $radiusS;
    padding: 20px 10px;
    position: relative;
    :deep(.edit-condition-wrapper) {
      > p {
        &:not(:last-child) {
          margin-bottom: 10px;
        }
        > span {
          display: inline-block;
          width: 40px;
          color: $fontColor;
          font-weight: 400;
          font-size: 14px;
        }
      }
    }
    > .colse_filter {
      position: absolute;
      right: 5px;
      top: -3px;
      cursor: pointer;
    }
    > .reset_filter {
      position: absolute;
      right: 15px;
      top: -3px;
      cursor: pointer;
    }
  }
  > .add_filter {
    width: 24px;
    height: 24px;
    background: #eeeeee;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: 300;
    border: 1px solid #cccccc;
    cursor: pointer;
    color: #a5a5a5;
    margin-right: 5px;
  }
}
:deep(.el-input--small .el-input__inner) {
  // color: #37383b;
}
.search_template_list {
  > li {
    min-height: 30px;
    line-height: 30px;
    // border-bottom: 1px dashed #ccc;
    cursor: pointer;
  }
}
:deep(.condition-result-wrapper.is-root) {
  padding: 2px;
}
.siemSearchClass2 {
  max-width: 65%;
}

@media screen and (max-width: 1570px) {
  .siemSearchClass2 {
    max-width: 100%;
  }
  .siemSearchClass1 {
    max-width: 100%;
  }
}
</style>
