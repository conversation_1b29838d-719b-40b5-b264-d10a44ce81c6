<template>
  <!-- 测绘数据管理 - 上传测绘结果 -->
  <xelDialog title="上传测绘结果" ref="dialogRef" width="500px" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleForm" label-width="auto">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item" />
    </el-form>
  </xelDialog>
</template>

<script setup>
import { ref } from "vue";
import { postIndexRecord, getNetworkAreaList } from "@/api/sime/assetdetect/surveyingData";
import { ElMessage } from "element-plus";

/* 弹窗相关 */
let dialogRef = ref();
let formData = ref({
  networkAreaId: "",
  indexName: "",
  description: "",
  importFile: [],
});

/* 表单内容 */
let formList = ref([
  {
    formType: "select",
    prop: "networkAreaId",
    filterable: true,
    label: "网络区域",
    required: true,
    seleteCode: {
      code: getNetworkAreaList,
      resKey: "",
      label: "name",
      value: "id",
      params: {},
    },
  },
  {
    formType: "input",
    prop: "indexName",
    label: "索引名称",
    placeholder: "请输入索引名称, 且必须以asset_开头",
    required: true,
    maxlength: "128",
    rules: [
      {
        validator: (rule, value, callback) => {
          const regex = /^asset_[0-9a-z_]{1,}$/;
          if (!regex.test(formData.value.indexName)) {
            callback(new Error("索引名称必须asset_开头且仅可使用数字、小写字母、下划线"));
          } else {
            callback();
          }
        },
      },
    ],
  },
  {
    formType: "input",
    prop: "description",
    label: "描述",
    required: false,
    type: "textarea",
    maxlength: "4096",
    autosize: { minRows: 2, maxRows: 10 },
  },
  {
    formType: "upload",
    prop: "importFile",
    label: "测绘结果",
    required: true,
    fileSize: 999999,
    isAccept: false,
    fileListFa: [],
    onFileList(list) {
      formData.value.importFile = list;
    },
  },
]);

let emits = defineEmits(["updateData"]);

/* 提交按钮 */
let ruleForm = ref();
const submitForm = async function (close, loading) {
  let valid = await ruleForm.value.validate();
  if (valid) {
    loading();
    let formD = new FormData();
    for (let i in formData.value) {
      if (i === "importFile") {
        formD.append(i, formData.value[i][0].raw);
      } else {
        formD.append(i, formData.value[i]);
      }
    }
    postIndexRecord(formD)
      .then((res) => {
        ElMessage.success({
          message: "操作成功",
        });
        emits("updateData");
        close();
      })
      .catch(() => {
        close(false);
      });
  }
};

/* 打开方法 */
const open = () => {
  dialogRef.value.open();
};

/* 重置 */
const closeDialog = () => {
  formData.value = {
    networkAreaId: "",
    indexName: "",
    description: "",
    importFile: [],
  };
  formList.value[3].fileListFa = [];
};

defineExpose({
  open,
});
</script>

<style scoped lang="scss"></style>
