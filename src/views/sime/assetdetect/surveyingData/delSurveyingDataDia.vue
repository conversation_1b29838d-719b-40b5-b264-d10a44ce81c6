<template>
  <!-- 测绘数据管理 - 删除 -->
  <xelDialog title="警告" ref="dialogRef" width="420px" @submit="submitForm" @close="closeDialog">
    <div class="diaCon">
      <div class="titleDiv"><i class="el-icon-warning"></i> 确定删除索引名称为 {{ formData.indexName }} 的记录吗?</div>
      <div class="titleDiv">
        <el-checkbox v-model="checked" />
        同时删除该记录下的资产数据
      </div>
    </div>
  </xelDialog>
</template>

<script setup>
import { ref } from "vue";
import { deleteIndexRecord } from "@/api/sime/assetdetect/surveyingData";
import { ElMessage } from "element-plus";

/* 弹窗相关 */
let dialogRef = ref();
let formData = ref({});
let checked = ref(true);

let emits = defineEmits(["updateData"]);

/* 提交按钮 */
const submitForm = async function (close, loading) {
  loading();
  deleteIndexRecord({
    id: formData.value.id,
    deleteIndex: checked.value,
  })
    .then((res) => {
      ElMessage.success({
        message: "操作成功",
      });
      emits("updateData");
      close();
    })
    .catch(() => {
      close(false);
    });
};

/* 打开方法 */
const open = (data) => {
  formData.value = data;
  dialogRef.value.open();
};

/* 重置 */
const closeDialog = () => {
  formData.value = {};
  checked.value = true;
};

defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.diaCon {
  text-align: center;
  color: var(--el-messagebox-content-color);
  font-size: var(--el-messagebox-content-font-size);
  .titleDiv {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    .el-icon-warning {
      margin-right: 5px;
      --el-messagebox-color: var(--el-color-warning);
      color: var(--el-messagebox-color);
      font-size: 24px !important;
    }
    .el-checkbox {
      margin-right: 5px;
    }
  }
}
</style>
