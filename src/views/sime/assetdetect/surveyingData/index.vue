<template>
  <!-- 测绘数据管理 - 列表  -->
  <!-- 查询体 -->
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button class="search-button" @click="handAddData" v-hasPermi="'assetdetect:indexRecord:add'">
      <el-icon :size="12">
        <plus />
      </el-icon>
      上传测绘结果
    </el-button>
  </common-search>

  <!-- 数据 -->
  <xel-table ref="tableRef" :columns="columns" :load-data="getIndexRecordList">
    <template #actionBtn="scope">
      <xel-handle-btns :btnList="getBtnList(scope.row)" :scope="scope" />
    </template>
  </xel-table>
  <div class="bg-p-border-new mt15-new">
    <div class="flex title title-bottom-line margin-top20">
      <p>区域标签</p>
      <span style="font-size: 12px; color: #909399; margin-left: 20px">可对标签进行编辑，排序，删除</span>
    </div>
    <xel-tag
      form-type="tag"
      class="margin-top10"
      prop="tagList"
      v-model="networkAreaList"
      btn-text="区城标签"
      msg-text="组件"
      :save-api="postNetworkArea"
      :isDraggable="true"
      name-key="name"
      :asyncDel="true"
      @del="delTag"
      @change="addAreaFun"
      @reload="reloadList"
    />
  </div>

  <!-- 测绘数据管理 - 增加弹窗 -->
  <AddSurveyingDataDia ref="AddSurveyingDataDiaRef" @updateData="search" />
  <!-- 测绘数据管理 - 删除弹窗 -->
  <DelSurveyingDataDia ref="DelSurveyingDataDiaRef" @updateData="search" />
</template>

<script setup>
import { reactive, ref } from "vue";
import AddSurveyingDataDia from "./addSurveyingDataDia.vue";
import DelSurveyingDataDia from "./delSurveyingDataDia.vue";
import { getIndexRecordList, postNetworkArea, getNetworkAreaList, deleteNetworkArea } from "@/api/sime/assetdetect/surveyingData";
import { ElMessage, ElMessageBox } from "element-plus";

/* 基础数据 */
let tableRef = ref();
let AddSurveyingDataDiaRef = ref();
let DelSurveyingDataDiaRef = ref();

/* 上传测绘结果 - 事件 */
const handAddData = () => {
  AddSurveyingDataDiaRef.value.open();
};

let networkAreaList = ref([]);

/* 查询相关 */
let searchState = reactive({
  data: {
    indexName: "",
    networkAreaIds: [],
    status: "",
    startTimeStr: [],
  },
  menuData: [
    {
      lable: "状态：",
      prop: "status",
      options: [],
      dictName: "assetdetect_indexRecord_status",
      sime: true,
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "indexName",
      label: "索引名称",
    },
    {
      formType: "select",
      prop: "networkAreaIds",
      label: "网络区域",
      multiple: true,
      options: [],
    },
    {
      formType: "date",
      type: "datetimerange",
      prop: "startTimeStr",
      label: "上传时间",
    },
  ],
});
/* 查询 */
const search = (initPage = true) => {
  getNetworkAreaListFun();
  let params = { ...searchState.data, startTime: "", endTime: "" };
  if (searchState.data.startTimeStr && searchState.data.startTimeStr.length > 0) {
    params.startTime = searchState.data.startTimeStr[0];
    params.endTime = searchState.data.startTimeStr[1];
  }
  delete params.startTimeStr;
  if (params.networkAreaIds.length) {
    params.networkAreaIds = params.networkAreaIds.toString();
  }
  tableRef.value.reload(params, initPage);
};

/* 获取 区城标签 */
const getNetworkAreaListFun = () => {
  getNetworkAreaList().then((res) => {
    networkAreaList.value = res.data;
    searchState.formList[1].options = res.data.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  });
};
getNetworkAreaListFun();
//编辑/移动区域标签后刷新 区域标签和表格
function reloadList(data) {
  search();
}
const addAreaFun = (item, viewAdd) => {
  viewAdd(item);
  getNetworkAreaListFun();
  searchState.data.networkAreaIds = [];
  ElMessage.success("操作成功");
};

/* 删除 区城标签 */
const delTag = (data, viewDel) => {
  ElMessageBox.confirm(`确定删除该标签么？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      deleteNetworkArea(data.id).then((res) => {
        viewDel(data);
        getNetworkAreaListFun();
        searchState.data.networkAreaIds = [];
        ElMessage.success("操作成功");
      });
    })
    .catch(() => {});
};

/* 重置 */
const reset = () => {
  searchState.data = {
    indexName: "",
    networkAreaIds: [],
    status: "",
    startTimeStr: [],
  };
  search();
};

/* 表格项 */
const columns = [
  {
    prop: "networkAreaName",
    label: "网络区域",
  },
  {
    prop: "indexName",
    label: "索引名称",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    prop: "statusComment",
    label: "状态",
  },
  {
    prop: "createByName",
    label: "上报人",
  },
  {
    prop: "createTime",
    label: "上传时间",
  },
  {
    prop: "assetCount",
    label: "资产数据量",
  },
  {
    label: "操作",
    width: "100",
    slotName: "actionBtn",
  },
];

/* 按钮组 */
function getBtnList(row) {
  return [
    {
      hasPermi: "assetdetect:indexRecord:delete",
      icon: "delete",
      title: "删除",
      onClick(scope) {
        DelSurveyingDataDiaRef.value.open(scope.row);
      },
    },
  ];
}
</script>

<style scoped>
.title {
  align-items: center;
}
</style>
