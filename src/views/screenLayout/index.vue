<template>
  <div class="ScaleBox" ref="ScaleBox">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: "XelScaleBox",
  props: {
    width: {
      type: Number,
      default: 1920,
    },
    height: {
      type: Number,
      default: 1080,
    },
  },
  data() {
    return {};
  },
  mounted() {
    this.setScale();
    window.addEventListener("resize", this.debounce(this.setScale));
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.debounce(this.setScale));
  },
  methods: {
    setScale() {
      if (document.documentElement.clientWidth < 1500) {
        let designWidth = 1920; //设计稿的宽度，根据实际项目调整
        let designHeight = 1080; //设计稿的高度，根据实际项目调整
        console.log(
          document.documentElement.clientWidth / document.documentElement.clientHeight < designWidth / designHeight,
          document.documentElement.clientHeight / designHeight
        );

        let scale =
          document.documentElement.clientWidth / document.documentElement.clientHeight < designWidth / designHeight
            ? document.documentElement.clientWidth / designWidth
            : document.documentElement.clientHeight / designHeight;
        this.$refs.ScaleBox.style.transform = `scale(${scale}) translate(-50% , -50%)`;
      }
    },
    debounce(fn, delay) {
      const delays = delay || 500;
      let timer;
      return function () {
        const th = this;
        const args = arguments;
        if (timer) {
          clearTimeout(timer);
        }
        timer = setTimeout(function () {
          timer = null;
          fn.apply(th, args);
        }, delays);
      };
    },
  },
};
</script>

<style lang="scss">
$design_width: 1920px; //设计稿的宽度，根据实际项目调整
$design_height: 1080px; //设计稿的高度，根据实际项目调整
.ScaleBox {
  height: 100vh;
  width: 100vw;
  display: inline-block;
  transform-origin: 0 0;
  position: absolute;
  left: 50%;
  top: 50%;
}
</style>
