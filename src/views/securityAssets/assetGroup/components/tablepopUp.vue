<template>
  <div>
    <p>{{ typeObject[type].text }}</p>
    <el-divider></el-divider>
    <common-search
      v-model="typeObject[type].search.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <el-button @click="newlyAdded" v-if="editFlag">
        <el-icon :size="12">
          <plus />
        </el-icon>
        添加{{ typeObject[type].text }}
      </el-button>
    </common-search>
    <!--表格 -->
    <xel-table
      v-if="type !== 'terminal'"
      ref="tableRef"
      :columns="typeObject[type].columns"
      :load-data="typeObject[type].URL"
      :pageSize="5"
      :pageSizes="[5, 10, 20, 50]"
      :default-params="{
        id: sizeId,
      }"
    >
      <template #ipPort="{ row }">
        <ip-port class="ip-port-list" :list="row[type == 'business' ? 'portsServiceList' : 'resourceServers']"></ip-port>
      </template>
    </xel-table>
    <!-- 
     -->
    <xel-table
      v-else
      ref="tableRef"
      :columns="typeObject[type].columns"
      :load-data="typeObject[type].URL"
      :pageSize="5"
      :pageSizes="[5, 10, 20, 50]"
      :default-params="{
        id: sizeId,
      }"
    >
    </xel-table>
    <!-- 弹框 -->
    <xelDialog
      :title="'添加' + typeObject[type].text"
      ref="dialog"
      size="large"
      @submit="typeObject[type].submitForm"
      @close="closeDialog"
      width="$globalWindowSize == 'S' ? '1300px' : '1500px'"
      @selection-change="handleSelectionChange"
    >
      <common-search
        v-model="typeObject[type].oNsearch.data"
        :menu-data="searchName.menuData"
        :form-list="searchName.formList"
        @search="inSearch"
        @reset="inReset"
      ></common-search>
      <!-- 列表 -->
      <xel-table
        v-if="type !== 'terminal'"
        ref="oncolumnsRef"
        :columns="typeObject[type].oncolumns"
        :load-data="typeObject[type].noURL"
        @selection-change="handleSelectionChange"
        :checkbox="true"
        row-key="id"
        :default-params="typeObject[type].paramsId"
      >
        <template #ipPort="{ row }">
          <ip-port class="ip-port-list" :list="row[type == 'business' ? 'portsServiceList' : 'resourceServers']"></ip-port>
        </template>
      </xel-table>
      <xel-table
        v-else
        ref="oncolumnsRef"
        :columns="typeObject[type].oncolumns"
        :load-data="typeObject[type].noURL"
        @selection-change="handleSelectionChange"
        :checkbox="true"
        row-key="id"
        :default-params="typeObject[type].paramsId"
      >
      </xel-table>
    </xelDialog>
  </div>
</template>
<script setup>
import {
  getGroupAssetsList,
  getGroupAssetsBasicList,
  getGroupAssetsTerminalList,
  getRightGroupBusiness,
  getRightGroupResource,
  getRightGroupTerminal,
  saveAddBusiness,
  saveAddResource,
  saveAddTerminal,
  delAssetsGroupBusinessRel,
  delAssetsGroupTerminalRel,
  delAssetsGroupResourceRel,
} from "@/api/securityAssets/assetGroup";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import { batchDelete } from "@/utils/delete";
const $globalWindowSize = window.$globalWindowSize;

let tableRef = ref();
let oncolumnsRef = ref();

// 业务系统对象搜索
let searchState = reactive({
  data: {
    searchValue: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "searchValue",
      label: "关键字",
    },
  ],
});
let searchName = reactive({
  data: {
    hostName: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: props.type == "business" ? "name" : "hostName",
      label: "关键字",
    },
  ],
});

// 业务系统配置项
const businesScolumns = [
  {
    prop: "name",
    label: "业务系统对象名称",
  },
  {
    prop: "domain",
    label: "域名",
  },
  {
    prop: "ipStr",
    label: "服务IP及端口",
    slotName: "ipPort",
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: !props.editFlag,
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row.id, "1");
        },
      },
    ],
  },
];
watch(
  () => props.editFlag,
  (val) => {
    resourceScolumns[3].btnList[0].hide = terminalScolumns[3].btnList[0].hide = businesScolumns[3].btnList[0].hide = !val;
  }
);
const onBusinesScolumns = [
  {
    prop: "name",
    label: "系统名称",
  },
  {
    prop: "domain",
    label: "URL",
  },
  {
    prop: "ipStr",
    label: "服务IP及端口",
    slotName: "ipPort",
  },
];
// 计算设备配置项
const resourceScolumns = [
  {
    prop: "name",
    label: "计算设备对象名称",
  },
  {
    prop: "hostName",
    label: "主机名称",
  },
  {
    prop: "ipStr",
    label: "服务IP及端口",
    slotName: "ipPort",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: !props.editFlag,
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row.id, "2");
        },
      },
    ],
  },
];
const onResourceScolumns = [
  {
    prop: "name",
    label: "计算设备对象名称",
  },
  {
    prop: "hostName",
    label: "主机名称",
  },
  {
    prop: "ipStr",
    label: "服务IP及端口",
    slotName: "ipPort",
  },
];
// 终端资产配置项
const terminalScolumns = [
  {
    prop: "name",
    label: "终端资产对象名称",
  },
  {
    prop: "hostName",
    label: "主机名称",
  },
  {
    prop: "ips",
    label: "局域网IP",
    formatter(row, column) {
      let ass = [];

      row.ipList = row.ipList || [];
      row.ipList.forEach((item) => {
        ass.push(item.ip);
      });
      return ass.join("，");
    },
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: !props.editFlag,
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row.id, "3");
        },
      },
    ],
  },
];
const onTerminalScolumns = [
  {
    prop: "name",
    label: "终端资产对象名称",
  },
  {
    prop: "hostName",
    label: "主机名称",
  },
  {
    prop: "terminalIps",
    label: "局域网IP22",
    formatter(row, column) {
      let ass = [];
      row.terminalIps.forEach((item) => {
        ass.push(item.ip);
      });
      return ass.join("，");
    },
  },
];
//全部配置项
let typeObject = reactive({
  business: {
    text: "业务系统对象",
    URL: getGroupAssetsList,
    columns: businesScolumns,
    search: {
      data: {
        searchValue: "",
      },
    },
    noURL: getRightGroupBusiness,
    oncolumns: onBusinesScolumns,
    submitForm: businessForm,
    oNsearch: {
      data: {
        name: "",
      },
    },
    paramsId: {
      groupId: props.sizeId,
    },
  },
  resource: {
    text: "计算设备对象",
    URL: getGroupAssetsBasicList,
    columns: resourceScolumns,
    search: {
      data: {
        searchValue: "",
      },
    },
    noURL: getRightGroupResource,
    oncolumns: onResourceScolumns,
    submitForm: resourceForm,
    oNsearch: {
      data: {
        searchValue: "",
      },
    },
    paramsId: {
      assetsGroupId: props.sizeId,
    },
  },
  terminal: {
    text: "终端资产对象",
    URL: getGroupAssetsTerminalList,
    columns: terminalScolumns,
    search: {
      data: {
        searchValue: "",
      },
    },
    noURL: getRightGroupTerminal,
    oncolumns: onTerminalScolumns,
    submitForm: terminalForm,
    oNsearch: {
      data: {
        searchValue: "",
      },
    },
    paramsId: {
      assetsGroupId: props.sizeId,
    },
  },
  multipleSelection: [],
});

let props = defineProps({
  type: {
    type: String,
    default: "",
  },
  sizeId: {
    type: String,
    default: "",
  },
  editFlag: {
    type: Boolean,
    default: true,
  },
});
let dialog = ref();
let dialogTitle = ref("");
// 添加资产弹框按钮
function newlyAdded() {
  dialog.value.open();
  typeObject.multipleSelection = [];
}
// 添加业务资产对象多选
function handleSelectionChange(val) {
  typeObject.multipleSelection = val;
}
// 添加业务资产对象
function businessForm() {
  let ids = typeObject.multipleSelection.map((item) => {
    return item["id"];
  });
  saveAddBusiness({ businessStr: ids.join(","), id: props.sizeId }).then((res) => {
    dialog.value.close();
    oncolumnsRef.value.reload();
    search(false);
  });
}
// 添加基础资产
function resourceForm() {
  let ids = typeObject.multipleSelection.map((item) => {
    return item["id"];
  });
  saveAddResource({ resourceStr: ids.join(","), id: props.sizeId }).then((res) => {
    dialog.value.close();
    oncolumnsRef.value.reload();
    search(false);
  });
}
// 添加终端资产
function terminalForm() {
  let ids = typeObject.multipleSelection.map((item) => {
    return item["id"];
  });
  saveAddTerminal({ terminalStr: ids.join(","), id: props.sizeId }).then((res) => {
    dialog.value.close();
    oncolumnsRef.value.reload();
    search(false);
  });
}
// 删除
function delFn(uid, on) {
  let delAssets = "";
  if (on == "1") {
    delAssets = delAssetsGroupBusinessRel;
  } else if (on == "2") {
    delAssets = delAssetsGroupResourceRel;
  } else if (on == "3") {
    delAssets = delAssetsGroupTerminalRel;
  }
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delAssets({ id: uid, groupId: props.sizeId }).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      search(false);
      oncolumnsRef.value.reload();
    });
  });
}
// 搜索按钮
function search(initPage = true) {
  tableRef.value.reload(typeObject[props.type].search.data, initPage);
}
// 重置按钮
function reset() {
  typeObject[props.type].search.data = {
    searchValue: "",
  };
  search();
}
inSearch;
// 搜索按钮
function inSearch() {
  oncolumnsRef.value.reload(typeObject[props.type].oNsearch.data);
}
// 重置按钮
function inReset() {
  if (props.type == "business") {
    typeObject[props.type].oNsearch.data = {
      name: "",
    };
  } else {
    typeObject[props.type].oNsearch.data = {
      hostName: "",
    };
  }
  oncolumnsRef.value.reload(typeObject[props.type].oNsearch.data, true);
}
</script>
<style lang="scss" scoped>
.ip-port-list {
  width: 200px !important;
}
</style>
