<template>
  <el-card style="width: 100%">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <div class="text-right margin-bottom10">
      <backbutton text="资产组" name="assetGroup"></backbutton>
    </div>
    <el-divider v-if="$route.params.id == 'add' && !isShow"></el-divider>
    <mouse-display
      v-if="$route.params.id != 'add' || isShow"
      :playName="'资产组名称'"
      :show-submit="$route.params.id == 'add' || formData.groupName != initGroupName"
      @submit="saveAsset"
      @close="formData.groupName = initGroupName"
    >
      <el-form :model="formData" ref="ruleFormRef" label-width="0px" size="mini">
        <xel-form-item
          v-for="(item, index) in formList"
          width="380px"
          :key="index"
          v-model="formData[item.prop]"
          v-bind="item"
          label=""
        ></xel-form-item>
      </el-form>
      <template #display>
        {{ initGroupName }}
      </template>
    </mouse-display>
    <div v-else>
      <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in formList" width="380px" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
      <div class="margin-bottom20 text-right">
        <el-button type="primary" round @click="saveAsset()">保存资产组</el-button>
      </div>
    </div>
    <div v-if="isShow">
      <tablepop-up type="business" :sizeId="groupId" :editFlag="editFlag"></tablepop-up>
      <tablepop-up type="resource" :sizeId="groupId" :editFlag="editFlag"></tablepop-up>
      <tablepop-up type="terminal" :sizeId="groupId" :editFlag="editFlag"></tablepop-up>
    </div>
    <el-divider></el-divider>
  </el-card>
</template>
<script>
export default {
  name: "",
};
</script>
<script setup>
import { saveForm, checkName, selectGroupById } from "@/api/securityAssets/assetGroup";
import tablepopUp from "./components/tablepopUp.vue";
import { ref, reactive, toRefs, nextTick, watch } from "vue";
import { ElMessage, ElNotification } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import mouseDisplay from "@/views/securityAssets/businessAssets/components/mouseDisplay.vue"; //鼠标移入编辑组件

import { useStore } from "vuex";

const store = useStore();

const router = useRouter();

const route = useRoute();
let isShow = ref(false);
let ruleFormRef = ref();
// 搜索
let searchState = reactive({
  data: {
    value: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "value",
      label: "资产组名称",
    },
  ],
});
// 表格
// 列表配置项
const columns = [
  {
    prop: "roleName",
    label: "资产组名称",
    // click() {
    //   router.push({ name: "newAssetGroup" });
    // },
  },
  {
    prop: "roleName",
    label: "业务系统对象",
  },
  {
    prop: "roleName",
    label: "计算设备对象",
  },
  {
    prop: "roleName",
    label: "终端资产对象",
  },
  {
    prop: "roleName",
    label: "相关待整改中高危漏洞",
  },
  {
    prop: "roleName",
    label: "相关待处置安全事件",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "edit",
        title: "修改",
        // onClick(scope) {
        //   dialogTitle.value = "修改" + dialogText.value;
        //   modifyButton(scope.row[idKey]);
        // },
      },
      {
        icon: "delete",
        title: "删除",
        // onClick(scope) {
        //   delFn([scope.row]);
        // },
      },
    ],
  },
];
// 新增项
let formList = reactive([
  {
    formType: "input",
    prop: "groupName",
    label: "资产组名称",
    required: true,
  },
]);
// 请求参数
let formData = reactive({
  groupName: "",
  id: "",
});
let groupId = ref("");
let groupName = ref("");

// 新增方法
function saveAsset(close, load) {
  if (route.params.id != "add") {
    formData.id = route.params.id;
  } else {
    formData.id = "";
  }
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      load && load();
      checkName(formData)
        .then((res) => {
          if (res.data.result == true) {
            saveForm(formData).then((res) => {
              isShow.value = true;
              groupId.value = res.data.groupId;
              initGroupName.value = formData.groupName;
              ElMessage.success("操作成功");
              close && close();
              if (route.params.id == "add") {
                store.commit("closeCurrentTab");
                router.push({
                  name: "AssetGroupDetail",
                  params: {
                    id: res.data.groupId,
                  },
                });
              }
            });
          } else if (res.data.result == false) {
            ElMessage.error("资产名称重复");
            close && close(false);
          }
        })
        .finally(close && close(false));
    }
  });
}
// 监听是编辑或新增
let initGroupName = ref("");
let editFlag = ref(false);
watch(
  () => route.params.id,
  (val) => {
    if (route.name == "AssetGroupDetail" && val != "add") {
      groupId.value = route.params.id;
      selectGroupById({ id: route.params.id }).then((res) => {
        initGroupName.value = formData.groupName = res.data.groupName;
        editFlag.value = res.data.editFlag == "Y";
      });
      isShow.value = true;
    } else {
      groupId.value = "";
    }
  },
  { immediate: true }
);
</script>
<style scoped lang="scss"></style>
