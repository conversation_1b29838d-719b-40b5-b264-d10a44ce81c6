<template>
  <div style="display: flex">
    <el-card style="width: 100%">
      <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
      <statistics :border="true" :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '资产组总数' }]"></statistics>
      <common-search
        v-model="searchState.data"
        labelWidth="150px"
        :menu-data="searchState.menuData"
        :form-list="searchState.formList"
        @search="search"
        @reset="reset"
      >
        <el-button @click="addAsset" class="search-button" v-hasPermi="'assets:group:add'">
          <el-icon :size="12">
            <plus />
          </el-icon>
          新建资产组
        </el-button>
      </common-search>
      <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange"> </xel-table>
      <!-- 弹窗内容 -->
      <xel-dialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
        <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
          <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
        </el-form>
      </xel-dialog>
      <!-- 详情弹框 -->
      <detail-dialog ref="detailDialogRef" :list="detailList" :data="detailInfo"></detail-dialog>
    </el-card>
  </div>
</template>
<script>
export default {
  name: "AssetGroup",
};
</script>
<script setup>
import { selectPage as getTableData, delAssetsGroup } from "@/api/securityAssets/assetGroup";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { timeDisplay, showTimeline, closeTimeline } from "@/utils/timelineDisplay.js";
import { batchDelete } from "@/utils/delete";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
let dialogText = ref("字典类型");
let idKey = "roleId";
let tableRef = ref();
let timelineRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    groupName: "",
    createName: "",
    businessName: "",
    resourceName: "",
    terminalName: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "groupName",
      label: "资产组名称",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "input",
      prop: "businessName",
      label: "业务系统对象名称",
    },
    {
      formType: "input",
      prop: "resourceName",
      label: "计算设备对象名称",
    },
    {
      formType: "input",
      prop: "terminalName",
      label: "终端资产对象名称",
    },
  ],
});
// 搜索按钮
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
// 重置按钮
function reset() {
  searchState.data = {
    groupName: "",
    createName: "",
    businessName: "",
    resourceName: "",
    terminalName: "",
  };
  search();
}

let state = reactive({
  formData: {}, //新增编辑表单
  multipleSelection: [],
});
let { formData } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  state.formData = {
    name: "",
  };
}

// 列表配置项
const columns = [
  {
    prop: "groupName",
    label: "资产组名称",
    width: $globalWindowSize == "S" ? "280" : "350",
    click(scope) {
      router.push({
        name: "AssetGroupDetail",
        params: {
          id: scope.row.id,
        },
      });
    },
  },
  {
    prop: "busiCount",
    label: "业务系统对象",
  },
  {
    prop: "resCount",
    label: "计算设备对象",
  },
  {
    prop: "terminalCount",
    label: "终端资产对象",
  },
  {
    prop: "vulnCount",
    label: "相关待整改中高危漏洞",
  },
  {
    prop: "eventCount",
    label: "相关待处置安全事件",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",

    btnList: [
      {
        icon: "delete",
        title: "删除",
        hasPermi: "'assets:group:remove' && '	assets:group:delOwn'",
        onClick(scope) {
          delFn(scope.row.id);
        },
      },
    ],
  },
];
//删除，批量删除
function delFn(rows) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delAssetsGroup({ id: rows }).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      tableRef.value.table.clearSelection();

      search(false);
    });
  });
}
// 添加资产按钮
function addAsset() {
  router.push({
    name: "NewAssetGroup",
    params: {
      id: "add",
    },
  });
}
// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  resetFormData();
  popupBox();
}
let editId = ref("");
// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "字典类型",
    required: true,
  },
  {
    formType: "radio",
    prop: "resource",
    label: "状态",
    required: true,
    options: [],
    dictName: "sys_normal_disable",
  },
]);
// 弹框确定按钮

// 提交
// function submitForm(close, loading) {
//   ruleFormRef.value.validate((valid) => {
//     if (valid) {
//       loading();
//       let addFn = editId.value ? updateItem : addItem;
//       addFn(state.formData)
//         .then((res) => {
//           search(false);
//           close();
//         })
//         .catch(() => {
//           close(false);
//         });
//     } else {
//       return false;
//     }
//   });
// }
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
  ruleFormRef.value.resetFields();
}
</script>
<style scoped lang="scss"></style>
