<template>
  <div class="inDiv title-bottom-line">
    <p>{{ buttName }}</p>
    <el-button size="mini" @click="jump">{{ total }}</el-button>
  </div>
  <section>
    <slot> <xel-table ref="tableRef" :columns="columns" :data="onData" :pagination="false"> </xel-table></slot>
  </section>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { getResourceVulnList, getResourceEventList } from "@/api/securityAssets/assetGroup";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
let props = defineProps({
  buttName: {
    type: String,
    default: "",
  },
  assetsId: {
    type: String,
    default: "",
  },

  getTableData: {
    type: String,
    default: "",
  },
  columns: {
    type: Array,
    default: () => {
      return [];
    },
  },
  onData: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let total = ref("");
// 漏洞
function resourceVulnList(vulnList) {
  getResourceVulnList(vulnList).then((res) => {
    total.value = res.data.total;
  });
}
// 事件
function ResourceEventList(vulnList) {
  getResourceEventList(vulnList).then((res) => {
    total.value = res.data.total;
  });
}

if (props.buttName == "未整改漏洞") {
  let vulnList = reactive({
    pageNum: 1,
    pageSize: 10,
    assetsId: props.assetsId,
  });
  resourceVulnList(vulnList);
} else if (props.buttName == "已整改漏洞") {
  let vulnList = reactive({
    pageNum: 1,
    pageSize: 10,
    assetsId: props.assetsId,
    space1: "done",
  });
  resourceVulnList(vulnList);
} else if (props.buttName == "处置中事件") {
  let vulnList = reactive({
    pageNum: 1,
    pageSize: 10,
    id: props.assetsId,
  });

  ResourceEventList(vulnList);
} else if (props.buttName == "已处置事件") {
  let vulnList = reactive({
    pageNum: 1,
    pageSize: 10,
    id: props.assetsId,
    spare1: "done",
  });
  ResourceEventList(vulnList);
}
// 点击数量跳转
function jump() {
  let params = {
    id: props.assetsId,
    assetType: route.params.type,
  };

  if (props.buttName.includes("漏洞")) {
    params.pageType = 0;
  } else {
    params.pageType = 1;
  }

  if (props.buttName.includes("已")) {
    params.spare1 = 1;
  } else {
    params.spare1 = 0;
  }
  router.push({ name: "JumpPage", params });
}
// assetGroup
</script>

<style scoped lang="scss">
.inDiv {
  display: flex;
  p {
    margin-right: 10px;
    margin-top: 2px;
  }
}
</style>
