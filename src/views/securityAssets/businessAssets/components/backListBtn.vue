<template>
  <div class="margin-bottom20 text-right" style="margin-top: -30px">
    <el-button v-if="typeSelf == 2 && changeStatus" @click="change">
      <el-icon :size="12"><position /></el-icon>
      资产变更</el-button
    >
    <el-button v-if="delStatus" n @click="del">
      <el-icon :size="12"><delete /></el-icon>
      删除</el-button
    >

    <el-button @click="backList" class="search-button">
      <icon n="icon-fanhui"></icon>
      返回资产列表
    </el-button>
  </div>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";
const store = useStore();

import getRouteName from "./backList.js";
import { deleteAssetsBusiness } from "@/api/securityAssets/business";
import { deleteAssetsResources } from "@/api/securityAssets/assetsList";
import { deleteTerminal } from "@/api/securityAssets/terminal";

let props = defineProps({
  type: {
    type: [Number, null],
  },
  delStatus: {
    type: Boolean,
    default: false,
  },
  changeStatus: {
    type: Boolean,
    default: false,
  },
});

let typeSelf = props.type ? props.type : Number((route.query && route.query.type) || route.params.type || 0);

function backList() {
  if (route.name == "ChangeAsset") {
    typeSelf = 2;
  }

  store.commit("closeCurrentTab");

  router.push(getRouteName(typeSelf));
}

function del() {
  ElMessageBox.confirm(`确认删除资产？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let id = route.params.id;

    let delApi = typeSelf == 0 ? deleteAssetsBusiness : typeSelf == 1 ? deleteAssetsResources : deleteTerminal;
    delApi({ id }).then(() => {
      ElMessage.success("删除成功");
      backList();
    });
  });
}

function change() {
  router.push({
    name: "ChangeAsset",
    params: {
      terminalId: route.params.id,
    },
  });
}

defineExpose({ backList });
</script>

<style lang="scss" scoped></style>
