<template>
  <div class="title-bottom-line" @mouseover="mouseover" @mouseout="mouseout">
    <p>{{ playName }}</p>
    <xel-handle-btns v-show="ass" class="edit-btn" :btn-list="btnList"></xel-handle-btns>
  </div>
  <section>
    <div v-if="buttonDisplay">
      <slot> </slot>
      <div class="btn-box text-right">
        <el-button v-show="showSubmit" type="primary" @click="submit" :loading="loading">确定</el-button>
        <el-button @click="close()">{{ showSubmit ? "取消" : "关闭" }}</el-button>
      </div>
    </div>
    <div v-else class="display-box" @mouseover="mouseover" @mouseout="mouseout">
      <slot name="display"></slot>
    </div>
  </section>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
let props = defineProps({
  playName: {
    type: String,
    default: "",
  },
  showSubmit: {
    type: Boolean,
    default: false,
  },
  disaboy: {
    type: <PERSON><PERSON>an,
    default: false,
  },
});

let emit = defineEmits(["edit", "submit", "close"]);

let ass = ref(false);
let buttonDisplay = ref(false);
function mouseover() {
  if (props.disaboy == true) {
    return false;
  }
  if (!buttonDisplay.value) {
    ass.value = true;
  }
}
function mouseout() {
  ass.value = false;
}
function edit() {
  buttonDisplay.value = true;
}

let btnList = [
  {
    icon: "edit",
    title: "编辑",
    onClick() {
      edit();
    },
  },
];

//确定按钮
let loading = ref(false);
function submit() {
  emit("submit", close, showLoad);
}
function close(closeStatus = true) {
  loading.value = false;

  if (closeStatus) {
    buttonDisplay.value = false;
    emit("close");
  }
}
function showLoad() {
  loading.value = true;
}
</script>
<style scoped lang="scss">
.title-bottom-line {
  position: relative;
  margin-bottom: 0;
  & + section > div {
    word-break: break-word;
    padding-top: 15px;
  }
}
.display-box {
}
.edit-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  transform: scale(0.8);
}
</style>
