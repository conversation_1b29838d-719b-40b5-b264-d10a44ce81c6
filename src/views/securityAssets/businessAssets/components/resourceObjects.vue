<template>
  <div class="inDiv title-bottom-line">
    <div class="ass">
      <p>关联业务系统对象</p>
      <el-button size="mini" @click="jump">{{ total }}</el-button>
    </div>
    <el-button @click="systemObject" class="search-button" v-if="assetsUpdate">
      <el-icon :size="12">
        <plus />
      </el-icon>
      添加关联业务系统对象
    </el-button>
  </div>
  <section>
    <xel-table ref="tableRef" :columns="oNcolumns" :data="rows" resKey="businessList" :pagination="false"> </xel-table>
  </section>

  <xel-dialog title="添加关联业务系统对象" ref="dialogRef" :width="$globalWindowSize == 'S' ? '1300px' : '1500px'" @submit="submitDialog">
    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      label-width="120px"
      @search="search"
      @reset="reset"
    ></common-search>
    <xel-table
      ref="tableding"
      :columns="columns"
      :load-data="getRightBusiness"
      @selection-change="handleSelectionChange"
      :checkbox="true"
      row-key="id"
    >
    </xel-table>
  </xel-dialog>
</template>
<script setup>
import {
  getRalationBusiness,
  getLeftResources,
  getRightBusiness,
  saveRalationBusinesse,
  deleteRalationBusiness,
  getRelevantBusiness,
} from "@/api/securityAssets/assetsList";
import { ElMessageBox, ElMessage } from "element-plus";
import JumpTable from "./jumpTable.vue";
import { ref, reactive, toRefs, nextTick, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
const $globalWindowSize = window.$globalWindowSize;

const router = useRouter();
const route = useRoute();
let props = defineProps({
  onName: {
    type: String,
    default: "",
  },
  assetsUpdate: {
    type: Boolean,
    default: false,
  },
  assetsDelete: {
    type: Boolean,
    default: false,
  },
});
onActivated(() => {
  relevantBusiness();
});
let tableRef = ref();
let tableding = ref();

let dialogRef = ref();
// 列表配置项
let oNcolumns = reactive([
  {
    prop: "systemName",
    label: "业务系统名称",
  },
  {
    prop: "domain",
    label: "域名",
  },
  {
    prop: "ip",
    label: "服务IP",
  },

  {
    prop: "port",
    label: "服务端口",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: props.assetsUpdate,
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
]);
watch(
  () => props.assetsDelete,
  (val) => {
    oNcolumns[4].btnList[0].hide = !val;
  }
);
// 添加
function systemObject() {
  dialogRef.value.open();
  searchState.data.searchValue = "";
  state.multipleSelection = [];
  getLeftResources({ assetsId: props.onName }).then((res) => {
    searchState.formList[0].options = res.data.map((item) => {
      return {
        value: item.id,
        label: item.ip + " " + item.port + " " + item.serviceAgreementName,
      };
    });
    searchState.data.resourcesId = res.data[0].id;
    setTimeout(() => {
      search();
    }, 200);
  });
}
//搜索相关

let searchState = reactive({
  data: {
    resourcesId: "",
    searchValue: "",
  },
  menuData: [],
  formList: [
    {
      formType: "select",
      prop: "resourcesId",
      label: "计算设备资产",
      multiple: false,
      options: [],
      clearable: false,
    },
    {
      formType: "input",
      prop: "searchValue",
      label: "系统名称",
    },
  ],
});
// 搜索按钮
function search(initPageNum = true) {
  tableding.value.reload(searchState.data, initPageNum);
}
// 重置按钮
function reset(initPageNum = true) {
  searchState.data = {
    ...searchState.data,

    searchValue: "",
    pageNum: 1,
    pageSize: 10,
  };
  tableding.value.reload(searchState.data, initPageNum);
}
// 列表配置项
let columns = reactive([
  {
    prop: "systemName",
    label: "系统名称",
  },
  {
    prop: "ip",
    label: "互联网IP",
  },
  {
    prop: "port",
    label: "互联网端口",
  },

  {
    prop: "serverAgreement",
    label: "服务协议",
  },
]);
// // 多选数据
let state = reactive({ multipleSelection: [] });
function handleSelectionChange(val) {
  state.multipleSelection = val;
}
// 弹框确定按钮
let emits = defineEmits(["update"]);
function submitDialog() {
  let ids = state.multipleSelection.map((item) => {
    return item["id"];
  });
  // 关联业务系统资产
  saveRalationBusinesse({ businessId: ids.join(","), resourcesId: searchState.data.resourcesId }).then((res) => {
    dialogRef.value.close();
    relevantBusiness();
    emits("update");
  });
}
// 取消关联业务系统资产
function delFn(val) {
  let ids = val.relId;
  let ida = val.id;
  let assetsId = val.assetsId;
  let systemName = val.systemName;
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteRalationBusiness({ id: ida, relId: ids, resourcesId: assetsId, systemName: systemName }).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      relevantBusiness();
      emits("update");
    });
  });
}
// 获取数量
let total = ref();
let rows = ref([]);

function relevantBusiness() {
  let ass = {
    pageSize: 5,
    pageNum: 1,
    id: props.onName,
  };
  getRelevantBusiness(ass).then((res) => {
    total.value = res.data.total;
    rows.value = res.data.rows;
  });
}
relevantBusiness();
// 点击数量跳转
function jump() {
  let params = {
    id: props.onName,
    assetType: route.params.type,
  };
  router.push({ name: "JumpService", params });
}
</script>
<style scoped lang="scss">
.inDiv {
  display: flex;
  justify-content: space-between;
}
.ass {
  display: flex;
  p {
    margin-right: 10px;
    margin-top: 2px;
  }
  el-button {
    margin-right: 10px;
  }
}
</style>
