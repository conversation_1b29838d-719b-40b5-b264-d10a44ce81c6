<!-- 资产基本信息表单 -->
<template>
  <el-form v-loading="!(status && groupStatus)" :model="formData" ref="ruleFormRef" label-width="6em" size="mini">
    <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    <xel-form-item :key="assetsPersonForm.options" v-model="formData['assetsPerson']" v-bind="assetsPersonForm"></xel-form-item>
  </el-form>
</template>
<script setup>
import { ref, reactive, toRefs, computed, watch } from "vue";
import { selectPage } from "@/api/securityAssets/assetGroup";
import { getCanSelectPersonByDept } from "@/api/securityAssets/business";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { assetTypeList } from "@/config/constant";

let assetTypeListSelf = assetTypeList;
//待确认资产新增
if (route.params.confirmId) {
  if (route.params.type == 0) {
    assetTypeListSelf = assetTypeList.filter((assetType) => assetType.value == 0);
  } else {
    assetTypeListSelf = assetTypeList.filter((assetType) => assetType.value != 0);
  }
}
// 工作台新增资产
if (route.params.assetId) {
  assetTypeListSelf = assetTypeList.filter((assetType) => assetType.value != 0);
}
let props = defineProps({
  editInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let newEditInfo = ref(JSON.parse(JSON.stringify(props.editInfo)));
//是否是变更资产页面
let isChangeAsset = computed(() => {
  return route.name == "ChangeAsset";
});
let isEdit = computed(() => {
  return !!newEditInfo.value.id;
});
let status = ref(isEdit.value ? false : true);
let groupStatus = ref(isEdit.value ? false : true);

let state = reactive({
  formData: {
    name: "",
    type: Number(route.query.type || route.params.type || 0),
    assetsGroupId: [],
    hostName: "",
    level: "",
    deptId: "",
    assetsPerson: "",
  },
});
//变更资产：计算设备资产
if (isChangeAsset.value) {
  state.formData.type = 1;
}

//工作台新增资产
if (route.params.assetType == 2) {
  state.formData.type = 2;
}

let { formData } = toRefs(state);

let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "资产名称",
    required: true,
  },
  {
    isShow: !isEdit.value || isChangeAsset.value,
    formType: "select",
    prop: "type",
    label: "资产类型",
    clearable: false,
    disabled: isChangeAsset.value, //变更资产 不能修改类型
    options: assetTypeListSelf,
    onChange(val) {
      if (val == 0) {
        formList[3].isShow = false;
      } else {
        formList[3].isShow = true;
      }
    },
  },
  {
    formType: "select",
    prop: "assetsGroupId",
    label: "资产组",
    options: [],
    multiple: true,
  },
  {
    isShow: state.formData.type == 1 || state.formData.type == 2,
    formType: "input",
    prop: "hostName",
    label: "主机名称",
    required: true,
  },
  {
    formType: "select",
    prop: "level",
    label: "等保级别",
    dictName: "grade_protection_level",
  },
  {
    formType: "deptTree",
    prop: "deptId",
    label: "责任主体",
    multiple: false,
    onCurrentChange(val) {
      getCanSelectPersonByDeptFn(val);
    },
  },
]);
let assetsPersonForm = reactive({
  formType: "select",
  multiple: true,
  prop: "assetsPerson",
  label: "责任人",
  options: [],
});

//获取资产组
selectPage({ pageNum: 1, pageSize: 100 }).then((res) => {
  let oldAsset = [];
  if (newEditInfo.value.assetsGroupId) {
    oldAsset = JSON.parse(JSON.stringify(newEditInfo.value.assetsGroupId));
  }

  let list = [];
  formList[2].options = [];
  res.data.rows.forEach((item) => {
    oldAsset.forEach((asset, index) => {
      if (asset == item.id) {
        list.push(item.id);
      }
    });
    formList[2].options.push({
      value: item.id,
      label: item.groupName,
    });
  });
  console.log(newEditInfo.value);
  let intersection = (nums1, nums2) => {
    return [...new Set(nums1)].filter((item) => {
      return nums2.includes(item);
    });
  };
  let arr = intersection(oldAsset, list);
  newEditInfo.value.assetsGroupId = arr;
  state.formData.assetsGroupId = arr;
  groupStatus.value = true;
});

//获取责任人
function getCanSelectPersonByDeptFn(info) {
  state.formData.assetsPerson = [];
  getCanSelectPersonByDept({ deptId: info.id }).then(({ data }) => {
    assetsPersonForm.options = data.userList.map((item) => {
      return {
        value: item.user_id,
        label: item.nick_name,
      };
    });
    status.value = true;
  });
}

//编辑  回显信息

function echoData() {
  if (isEdit.value) {
    for (let attr in state.formData) {
      if (Array.isArray(newEditInfo.value[attr])) {
        state.formData[attr] = JSON.parse(JSON.stringify(newEditInfo.value[attr]));
      } else {
        if (newEditInfo.value[attr]) {
          state.formData[attr] = newEditInfo.value[attr];
        }
      }
    }
    //责任主体和责任人

    if (newEditInfo.value.deptId) {
      getCanSelectPersonByDeptFn({ id: newEditInfo.value.deptId });
      state.formData["assetsPerson"] = newEditInfo.value["personList"] ? newEditInfo.value["personList"].map((item) => item.userId) : [];
      // state.formData["assetsPerson"] = newEditInfo.value["personList"].map((item) => item.userId);
    } else {
      status.value = true;
    }
  }
}
echoData();
let wacher = watch(
  () => newEditInfo.value,
  () => {
    echoData();
    wacher();
  }
);

let ruleFormRef = ref();

defineExpose({
  ruleFormRef: ruleFormRef,
  formData: state.formData,
});
</script>

<style lang="scss" scoped></style>
