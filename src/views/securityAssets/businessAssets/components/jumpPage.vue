<template>
  <el-card>
    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      label-width="120px"
      @search="search"
      @reset="reset"
    ></common-search>
    <xel-table v-if="loadApi" ref="tableRef" :columns="loadColumns" :defaultParams="defaultParams" :load-data="loadApi" :checkbox="false">
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "JumpPage",
};
</script>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, onActivated } from "vue";
onActivated(() => {
  search(false);
});

import { useStore } from "vuex";
const store = useStore();

/*import { loopholeColumns, eventColumns } from "./columns";*/
import { selectAssetsUnCorrectVulnList, selectAssetsCorrectVulnList, selectBusinessEventList } from "@/api/securityAssets/business";
import { getResourceVulnList, getResourceEventList } from "@/api/securityAssets/assetGroup";
import { getTerminalEventList } from "@/api/securityAssets/terminal";
import { useRouter, useRoute } from "vue-router";

const route = useRoute();
const router = useRouter();

/* 增加链接 - 漏洞 */
let loopholeColumns = ref([
  {
    prop: "title",
    label: "漏洞标题",
    click(val) {
      /* 业务系统资产 - 业务系统漏洞详情 */
      if (route.params.assetType === "0") {
        router.push({
          name: "VulnBusinessDetail",
          params: {
            id: val.row.id,
          },
        });
      } else {
        /* 计算设备资产 - 基础资源漏洞详情 */
        router.push({
          name: "VulnBasicDetail",
          params: {
            id: val.row.id,
          },
        });
      }
    },
  },
  {
    prop: "levelName",
    label: "漏洞级别",
  },
  {
    prop: "submitTime",
    label: "扫描时间",
  },
]);

/* 增加链接 - 事件 */
let eventColumns = ref([
  {
    prop: "title",
    label: "标题",
    click(val) {
      router.push({
        name: "EventDetail",
        params: {
          id: val.row.id,
        },
      });
    },
  },
  {
    prop: "levelName",
    label: "事件级别",
  },
  {
    prop: "createName",
    label: "分析师",
  },

  {
    prop: "createTime",
    label: "创建时间",
  },
]);

let { id, spare1, assetType, pageType } = route.params;
let tableRef = ref();
// 搜索按钮
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
// 重置按钮
function reset() {
  searchState.data = {
    title: "",
    eventTitle: "",
    pageNum: 1,
    pageSize: 10,
  };
  tableRef.value.reload(searchState.data);
}
// 搜索配置项
let searchState = reactive({
  labelName: "",
  data: {
    title: "",
    eventTitle: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: pageType == 0 ? "title" : "eventTitle",
      label: "漏洞名称",
    },
  ],
});

let loadApi = ref();
let labelName = ref("");
let defaultParams = ref("");
let loadColumns = ref(loopholeColumns.value);
//id:资产id  pageType:0 漏洞 1：事件 assetType: 0|| 1 ||2  spare1:标识 1:已处置 0未处置
onActivated(() => {
  setMenuIndex();
});
function setMenuIndex() {
  let pageParentName = {
    0: "BusinessAssets",
    1: "Underlying",
    2: "Terminal",
  };
  store.commit("setDetailParentMenu", { detailName: route.name, parentName: pageParentName[assetType] });
}

//assetType 0  && pageType ==0 && spare1 ==0
if (assetType == 0 && pageType == 0 && spare1 == 0) {
  // 业务资产 未整改漏洞
  loadApi.value = selectAssetsUnCorrectVulnList;
  defaultParams.value = { id: route.params.id };
} else if (assetType == 0 && pageType == 0 && spare1 == 1) {
  // 业务资产 已整改漏洞
  loadApi.value = selectAssetsCorrectVulnList;
  defaultParams.value = { id: route.params.id };
} else if (assetType == 0 && pageType == 1) {
  // 业务资产 事件
  loadApi.value = selectBusinessEventList;
  searchState.formList[0].label = "事件名称";
  loadColumns.value = eventColumns.value;
  if (spare1 == 1) {
    defaultParams.value = { id: route.params.id, spare1: "done" };
  } else {
    defaultParams.value = { id: route.params.id };
  }
} else if (assetType == 1 && pageType == 0) {
  // 计算设备资产 漏洞
  loadApi.value = getResourceVulnList;
  if (spare1 == 1) {
    defaultParams.value = { assetsId: route.params.id, space1: "done" };
  } else {
    defaultParams.value = { assetsId: route.params.id };
  }
} else if (assetType == 1 && pageType == 1) {
  // 计算设备资产 事件
  searchState.formList[0].label = "事件名称";
  loadColumns.value = eventColumns.value;
  loadApi.value = getResourceEventList;
  if (spare1 == 1) {
    defaultParams.value = { id: route.params.id, spare1: "done" };
  } else {
    defaultParams.value = { id: route.params.id };
  }
} else if (assetType == 2 && pageType == 1) {
  // 终端资产 事件
  searchState.formList[0].label = "事件名称";
  loadColumns.value = eventColumns.value;
  loadApi.value = getTerminalEventList;
  if (spare1 == 1) {
    defaultParams.value = { id: route.params.id, spare1: "done" };
  } else {
    defaultParams.value = { id: route.params.id };
  }
}
</script>
<style scoped lang="scss"></style>
