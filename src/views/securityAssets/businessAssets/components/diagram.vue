<template>
  <span v-if="props.openBig" class="openBig" @click="openBigGrid()">
    <svg class="icon" aria-hidden="true">
      <use xlink:href="#icon-chakan"></use>
    </svg>
  </span>
  <div :class="props.openBig === true ? 'content' : 'content2'" ref="grid"></div>
  <div v-if="props.openBig">
    <xel-dialog title="资产关联关系" ref="dialogRef" size="large" ishiddenDialog="true" @submit="submitForm">
      <Diagram v-if="JSON.stringify(diagramData) !== '{}'" :diagramData="diagramData"></Diagram>
    </xel-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, nextTick, computed, onMounted, watch } from "vue";
import Diagram from "./diagram.vue"; // 关系图
import * as echarts from "echarts";
let props = defineProps({
  diagramData: {
    type: Object,
    default() {
      return {};
    },
  },
  openBig: {
    type: Boolean,
    default: false,
  },
});

// 弹出窗
let dialogRef = ref();
function openBigGrid() {
  dialogRef.value.open();
}
// 关系图
let grid = ref();

let myChart = null;
function getData() {
  let data = props.diagramData;
  const dataslef = [];
  const linkslef = [];
  if (data) {
    data.resultData.forEach((item) => {
      if (item.type === "main") {
        const strs = item.name.split("");
        let str = "";
        let h = 100;
        if (item.name.length > 13) {
          h = 150;
        }
        for (let j = 1; j <= strs.length; j++) {
          str += strs[j - 1];
          if (!(j % 13)) {
            str += "\n";
          }
        }
        dataslef.push({
          id: item.id,
          name: str,
          symbolSize: h,
          draggable: true,
          category: 1,
        });
      } else if (item.type === "server") {
        const strs = item.name.split("");
        let str = "";
        let h = 100;
        if (item.name.length > 13) {
          h = 150;
        }
        for (let j = 1; j <= strs.length; j++) {
          str += strs[j - 1];
          if (!(j % 13)) {
            str += "\n";
          }
        }
        dataslef.push({
          id: item.id,
          name: str,
          symbolSize: h,
          draggable: true,
          category: 0,
        });
      } else if (item.type === "ip") {
        dataslef.push({
          id: item.id,
          name: item.name,
          symbol: "roundRect",
          symbolSize: [120, 30],
          category: 4,
        });
      } else if (item.type === "domain") {
        const domain = item.name;
        const strs = domain.split("");
        let str = "";
        let h = 1;
        for (let j = 1; j <= strs.length; j++) {
          str += strs[j - 1];
          if (!(j % 30)) {
            h++;
            str += "\n";
          }
        }
        let height = 30;
        if (h > 1) {
          height = 20 * h;
        }
        let width = 260;
        if (domain > 30) {
          width = 280;
        }
        dataslef.push({
          id: item.id,
          name: str,
          symbol: "roundRect",
          symbolSize: [width, height],
          category: 2,
        });
      } else if (item.type === "soft") {
        const soft = item.name;
        const strs = soft.split("");
        let str = "";
        let h = 1;
        for (let j = 1; j <= strs.length; j++) {
          str += strs[j - 1];
          if (!(j % 30)) {
            h++;
            str += "\n";
          }
        }
        let height = 30;
        if (h > 1) {
          height = 20 * h;
        }
        let width = 200;
        if (soft.length > 30) {
          width = 220;
        }
        dataslef.push({
          id: item.id,
          name: str,
          symbol: "roundRect",
          symbolSize: [width, height],
          category: 3,
        });
      } else if (item.type === "port") {
        dataslef.push({
          id: item.id,
          name: item.name,
          symbol: "roundRect",
          symbolSize: [100, 30],
          category: 3,
        });
      } else {
        const dept = item.name;
        if (dept) {
          const strs = dept.split("");
          let str = "";
          let h = 1;
          for (let j = 1; j <= strs.length; j++) {
            str += strs[j - 1];
            if (!(j % 10)) {
              h++;
              str += "\n";
            }
          }
          let height = 30;
          if (h > 1) {
            height = 20 * h;
          }
          let width = 120;
          if (dept.length > 10) {
            width = 150;
          }
          dataslef.push({
            id: item.id,
            name: str,
            symbol: "roundRect",
            symbolSize: [width, height],
            category: 2,
          });
        }
      }
    });
    const linkChildren = data.resultLink.childrenList;
    linkChildren.forEach((item) => {
      linkslef.push({
        source: data.resultLink.id,
        target: item.id,
        value: "",
      });
      if (item.childrenList) {
        const linkGrand = item.childrenList;
        for (let j = 0; j < linkGrand.length; j++) {
          linkslef.push({
            source: item.id,
            target: linkGrand[j].id,
            value: "",
          });
        }
      }
      if (item.cliList) {
        const chiChild = item.cliList;
        for (let v = 0; v < chiChild.length; v++) {
          linkslef.push({
            source: item.id,
            target: chiChild[v]["id"],
            value: "",
          });
          if (chiChild[v].childrenList) {
            const linkGread = chiChild[v].childrenList;
            for (let g = 0; g < linkGread.length; g++) {
              linkslef.push({
                source: chiChild[v]["id"],
                target: linkGread[g]["id"],
                value: "",
              });
            }
          }
        }
      }
    });
    drawLine(dataslef, linkslef);
  }
}
function drawLine(dataslef, linkslef) {
  myChart = echarts.init(grid.value);
  let option = {
    title: {
      show: false,
      text: "资产关联关系",
    },
    legend: [
      {
        data: [{ name: "main" }, { name: "server" }, { name: "ip" }, { name: "domain" }, { name: "soft" }, { name: "port" }, { name: "dept" }],
        show: false,
      },
    ],
    tooltip: { trigger: "item", showContent: false, formatter: "{a} : {b}" },
    animationDurationUpdate: 1500,
    animationEasingUpdate: "quinticInOut",
    series: [
      {
        type: "graph",
        layout: "force",
        animation: false,
        force: {
          repulsion: 1000,
          edgeLength: 50,
          layoutAnimation: false,
        },
        symbolSize: 20,
        roam: true,
        label: {
          normal: {
            show: true,
          },
        },
        data: dataslef,
        links: linkslef,
        lineStyle: {
          normal: {
            opacity: 0.9,
            width: 2,
            curveness: 0.5,
          },
        },
        categories: [{ name: "main" }, { name: "server" }, { name: "ip" }, { name: "domain" }, { name: "soft" }, { name: "port" }, { name: "dept" }],
      },
    ],
  };
  myChart.setOption(option);
}

onMounted(() => {
  window.addEventListener("resize", function () {
    myChart.resize();
  });
  if (JSON.stringify(props.diagramData) !== "{}") {
    getData();
    if (props.openBig === false) {
      myChart.resize();
    }
  }
});
defineExpose({
  getData,
});
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
}
.content2 {
  width: 900px;
  height: 500px;
}
.openBig {
  position: absolute;
  top: 20px;
  right: 20px;
  color: green;
  cursor: pointer;
}
</style>
