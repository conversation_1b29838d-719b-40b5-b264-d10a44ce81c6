<template>
  <!-- 资产详情 -->
  <el-card :class="{ 'un-update': !updateStatus }">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <back-list-btn ref="backListRef" :del-status="delStatus" :change-status="changeStatus"></back-list-btn>

    <el-row :gutter="20" :class="{ 'un-updated': updateStatus }">
      <el-col :span="pageType == 2 ? 24 : 16" class="mouse">
        <mouse-display :playName="'摘要'" @submit="saveBaseInfo" :show-submit="true">
          <div class="margin-top20">
            <base-info-form ref="baseInfoRef" :edit-info="state.resource"></base-info-form>
          </div>
          <template #display>
            <el-form ref="form" label-width="120px" label-position="left" class="base-info-form">
              <el-row :gutter="70" class="base-info-box">
                <el-col :span="12">
                  <el-form-item label="资产对象名称：">{{ resource.name }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="资产对象类型 ：">{{ assetTypeText }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="创建人：">{{ resource.createName }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="创建时间：">{{ resource.createTime }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="更新时间：">{{ resource.updateTime }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="资产组：">{{ resource.assetsGroupName }}</el-form-item>
                </el-col>
                <el-col :span="12" v-if="pageType == 1 || pageType == 2">
                  <el-form-item label="主机名称：">{{ resource.hostName }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="等级保护级别：">{{ resource.levelName }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="责任主体：">{{ resource.deptName || resource.assetsDeptName }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="责任人：">{{ userNames }}</el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
        </mouse-display>
        <mouse-display v-if="pageType == 0" :playName="'系统入口'" @close="updateUrl">
          <div>
            <dynamic-item
              ref="dynamicRef"
              :form-list="formListURL"
              dynamicName="系统入口"
              type="domain"
              :edit-api="saveEditDomain"
              :add-api="saveDomain"
              :del-api="deleteDomain"
              :can-transfer="true"
              add-width="80%"
              :allow-empty="false"
              @transfer="openTransfer"
              @update="getBusinessDetailSetTimeout"
            ></dynamic-item>
          </div>
          <template #display>
            <ul class="url-list">
              <li v-for="item in URLList" :key="item.id"><span class="item-label">系统入口</span> {{ item.domain }}</li>
            </ul>
          </template>
        </mouse-display>
        <mouse-display v-if="pageType == 1 || pageType == 0" playName="局域网 IP 、端口与服务组" @close="updateLocal">
          <div>
            <!-- 编辑局域网ip -->
            <dynamic-item
              ref="dynamicLocalRef"
              :form-list="formListIpLocal"
              dynamicName="局域网IP、端口与服务组"
              title="局域网"
              :edit-key="pageType == 1 ? 'resourceServers' : 'portsServiceList'"
              :edit-api="pageType == 1 ? saveAssetsResourceServer : saveEditPortServer"
              :add-api="pageType == 1 ? saveBasicPortServer : savePortServer"
              :del-api="pageType == 1 ? deleteAssetsResourceServer : deletePortServer"
              :can-transfer="pageType == 1"
              :allow-empty="pageType == 0"
              add-width="calc( 100% - 170px ) "
              @transfer="openTransfer"
            ></dynamic-item>
          </div>
          <template #display>
            <ip-port :list="localIpList" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
          </template>
        </mouse-display>
        <mouse-display v-if="pageType == 0" playName="互联网 IP 、端口与服务组" @close="updateNetwork">
          <div>
            <!-- 编辑互联网ip -->
            <dynamic-item
              :form-list="formListIpNetwork"
              edit-key="portsServiceList"
              dynamicName="互联网IP、端口与服务组"
              title="互联网"
              :edit-api="saveEditPortServer"
              :add-api="savePortServer"
              :del-api="deletePortServer"
              :network-type="1"
            ></dynamic-item>
          </div>
          <template #display>
            <ip-port :list="networkIpList" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
          </template>
        </mouse-display>
        <mouse-display v-if="pageType == 1" :playName="'基础资源软件'" @close="updateSoft">
          <div>
            <!-- 编辑基础资源软件 -->
            <dynamic-soft :is-edit="true" :dataList="resourceSoftwares" :edit-api="saveSoftware" :del-api="deleteSoftware"></dynamic-soft>
            <!-- 编辑其他应用组件 -->
            <xel-tag
              class="margin-top10"
              v-model="resourceAssemblys"
              btn-text="其他应用组件"
              msg-text="组件"
              :save-api="saveAssemblyByAssemblyName"
              name-key="name"
              :async-del="true"
              @del="delAssembly"
              @change="insertAssemblyFn"
            ></xel-tag>
          </div>
          <template #display>
            <ul class="url-list soft-ul">
              <li v-for="item in resourceSoftwares" :key="item.id">
                <div>
                  <span class="item-label">类型：</span><span class="ellipse">{{ item.softwareType }}</span>
                </div>
                <div>
                  <span class="item-label">软件：</span><span class="ellipse">{{ item.softwareValue }}</span>
                </div>
                <div>
                  <span class="item-label">版本：</span><span class="ellipse">{{ item.softwareEdition }}</span>
                </div>
              </li>
            </ul>
            <div class="margin-top20 flex">
              <span class="item-label">其他应用组件：</span>

              <span class="no-data" v-if="resourceAssemblys.length == 0">暂无</span>
              <ul v-else class="flex resourceAssemblys">
                <li v-for="item in resourceAssemblys" :key="item.id" class="margin-right20 margin-bottom5">
                  <el-tag>{{ item.assemblyName }}</el-tag>
                </li>
              </ul>
            </div>
          </template>
        </mouse-display>
        <!-- 终端资产 -->
        <mouse-display v-if="pageType == 2" :playName="'局域网IP'" @close="updateTerIp">
          <dynamic-item
            ref="dynamicTerIpRef"
            :form-list="formListTerIp"
            dynamicName="局域网IP"
            type="terIp"
            :edit-api="saveAssetsTerminalIp"
            :add-api="saveAddTerminalIp"
            :del-api="deleteTerminalIp"
            :can-transfer="true"
            @transfer="openTransfer"
          ></dynamic-item>
          <template #display>
            <ul class="url-list">
              <li v-for="item in terIpList" :key="item.id"><span class="item-label">IP</span> {{ item.ip }}</li>
            </ul>
          </template>
        </mouse-display>
        <div v-if="pageType == 1">
          <jumpTable :buttName="'未整改漏洞'" :columns="loopholeC" :onData="state.resourceVulnList" :assetsId="assetsId"></jumpTable>
          <jumpTable :buttName="'处置中事件'" :columns="eventC" :onData="state.eventList" :assetsId="assetsId"> </jumpTable>
          <jumpTable :buttName="'已整改漏洞'" :columns="loopholeC" :onData="state.vulnList" :assetsId="assetsId"></jumpTable>
          <jumpTable :buttName="'已处置事件'" :columns="eventC" :onData="state.disposedEvents" :assetsId="assetsId"> </jumpTable>
          <resource-objects
            :onName="assetsId"
            :assetsUpdate="updateStatus"
            :assetsDelete="delStatus"
            @update="updateBusinessAssets"
          ></resource-objects>
        </div>
        <div v-else>
          <businesslist
            v-if="pageType == 0"
            :buttName="'未整改漏洞'"
            :columns="loopholeC"
            :onData="state.unCorrectVulnList"
            :assetsId="assetsId"
            ref="busines"
          >
          </businesslist>
          <businesslist :buttName="'处置中事件'" :getTableData="pageType" :columns="eventC" :onData="state.eventUnder" :assetsId="assetsId">
          </businesslist>
          <businesslist
            v-if="pageType == 0"
            ref="busines1"
            :buttName="'已整改漏洞'"
            :columns="loopholeC"
            :onData="state.correctVulnList"
            :assetsId="assetsId"
          >
          </businesslist>
          <businesslist :buttName="'已处置事件'" :getTableData="pageType" :columns="eventC" :onData="state.handleEventList" :assetsId="assetsId">
          </businesslist>
          <basicAssets
            v-if="pageType == 0"
            :onData="state.resourceList"
            :idName="assetsId"
            :assetsUpdate="updateStatus"
            :assetsDelete="delStatus"
            @update="updateBasicAssets"
          ></basicAssets>
        </div>
      </el-col>
      <el-col v-if="pageType == 0 || pageType == 1" :span="8" ref="scrollBody">
        <div :class="grid_change ? 'grid_change' : ''" class="grid-content" ref="gridContent">
          <p class="title">资产关联关系</p>
          <div class="content">
            <Diagram v-if="diagramDataShow" ref="diagram" :diagramData="state.diagramData" :openBig="true"></Diagram>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 资产列表弹框 -->
    <xel-dialog ref="dialogRef" :title="`${pageText}列表`" :ishiddenDialog="true" size="large">
      <!-- 业务系统资产 -->
      <business-list v-if="pageType == 0" :is-dialog="true" :current-id="assetsId" :params="switchParams" @submit="transferUrl"></business-list>
      <!-- 计算设备资产 -->
      <computed-list v-if="pageType == 1" :is-dialog="true" :current-id="assetsId" :params="switchParams" @submit="transferIp"></computed-list>
      <!-- 终端资产 -->
      <terminal-list v-if="pageType == 2" :is-dialog="true" :current-id="assetsId" :params="switchParams" @submit="transferTerIp"></terminal-list>
    </xel-dialog>
  </el-card>
</template>
<script>
export default {
  name: "AssetDetails",
};
</script>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, onMounted, watch, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import store from "../../../store";
const route = useRoute();
const router = useRouter();
let busines = ref();
let busines1 = ref();
import { assetTypeList } from "@/config/constant";

import getRouteName from "./components/backList.js";
import backListBtn from "./components/backListBtn.vue";
import baseInfoForm from "./components/baseInfoForm.vue"; //基本信息表单组件
import mouseDisplay from "./components/mouseDisplay.vue"; //鼠标移入编辑组件
import JumpTable from "./components/jumpTable.vue"; //漏洞，事件表格
import ResourceObjects from "./components/resourceObjects.vue"; //关联业务系统对象
import Diagram from "./components/diagram.vue"; // 关系图

//弹框内的资产列表
import businessList from "./index.vue";
import computedList from "../underlying/index.vue";
import terminalList from "../terminal/index.vue";

//业务系统资产接口
import {
  getDetailBusiness,
  saveEditPortServer,
  savePortServer,
  deletePortServer,
  saveEditDomain,
  saveDomain,
  deleteDomain,
  updateBusiness,
  transferDomain,
  getBusinessEchartsInfo,
} from "@/api/securityAssets/business";

//计算设备资产接口
import {
  assetsDetail,
  eventAndVuln,
  saveAssetsResourceServer,
  deleteAssetsResourceServer,
  saveBasicPortServer,
  saveSoftware,
  deleteSoftware,
  deleteAssemblys,
  insertAssemblys,
  saveAssemblyByAssemblyName,
  saveEditAssetsResource,
  transferIpApi,
  getResourcesEchartsInfo,
} from "@/api/securityAssets/assetsList";

import { getdetail } from "@/api/securityAssets/business";

//终端资产接口
import {
  getTerminalDetail,
  saveAssetsTerminalIp,
  saveAddTerminalIp,
  deleteTerminalIp,
  transferTerminalIp,
  updateTerminal,
} from "@/api/securityAssets/terminal";

import { loopholeColumns, eventColumns } from "./components/columns";

import backList from "./components/backList"; //返回列表

/* 增加链接 - 漏洞 */
let loopholeC = ref([
  {
    prop: "title",
    label: "漏洞标题",
    click(val) {
      /* 业务系统资产 - 业务系统漏洞详情 */
      if (route.params.type === "0") {
        router.push({
          name: "VulnBusinessDetail",
          params: {
            id: val.row.id,
          },
        });
      } else {
        /* 计算设备资产 - 基础资源漏洞详情 */
        router.push({
          name: "VulnBasicDetail",
          params: {
            id: val.row.id,
          },
        });
      }
    },
  },
  {
    prop: "levelName",
    label: "漏洞级别",
  },
  {
    prop: "submitTime",
    label: "扫描时间",
  },
]);

/* 增加链接 - 事件 */
let eventC = ref([
  {
    prop: "title",
    label: "标题",
    click(val) {
      router.push({
        name: "EventDetail",
        params: {
          id: val.row.id,
        },
      });
    },
  },
  {
    prop: "levelName",
    label: "事件级别",
  },
  {
    prop: "createName",
    label: "分析师",
  },

  {
    prop: "createTime",
    label: "创建时间",
  },
]);

let pageType = Number(route.query.type || route.params.type || 0); //当前页面的资产类型
let pageText = pageType == 0 ? "业务系统资产" : pageType == 1 ? "计算设备资产" : "终端资产";

onActivated(() => {
  setMenuIndex();
  updateBasicAssets();
  updateBusinessAssets();
});
function setMenuIndex() {
  let pageParentName = {
    0: "BusinessAssets",
    1: "Underlying",
    2: "Terminal",
  };
  store.commit("setDetailParentMenu", { detailName: route.name, parentName: pageParentName[pageType] });
}

// 业务系统资产组件
import Businesslist from "./business/businesslist.vue";
import BasicAssets from "./business/basicAssets.vue";

let assetsId = route.params.id;

switch (pageType) {
  case 0:
    getBusinessDetail();
    break;
  case 1:
    getAssetsDetail();
    break;
  case 2:
    getDetailTerminal();
    break;
}

//转移资产列表组件中查询参数
//业务系统资产转移域名：spare3：transfer    spare4:要转移的域名的id
//计算设备资产转移ip：spare3：要转移的ip
//终端资产转移ip：spare3：要转移的ip
let switchParams = computed(() => {
  let obj = {};
  if (pageType == 0) {
    obj = { spare3: "transfer", spare4: currentDomain.value };
  } else {
    obj = { spare3: currentDomain.value };
  }
  return obj;
});

let state = reactive({
  resource: {},
  resourceVulnList: [],
  eventList: [],
  vulnList: [],
  disposedEvents: [],
  unCorrectVulnList: [],
  correctVulnList: [],
  eventUnder: [],
  handleEventList: [],
  resourceList: [],
  diagramData: {},
});

//资产类型
let assetTypeText = computed(() => {
  return assetTypeList.find((item) => item.value == pageType).label;
});
//主体人
let userNames = computed(() => {
  return state.resource.personList ? state.resource.personList.map((item) => item.userName).join() : "";
});

let URLList = ref([]); //域名 展示
let formListURL = ref([]); //域名表单
let networkIpList = ref([]); //局域网ip列表 展示
let formListIpNetwork = ref([]); //互联网ip列表
let terIpList = ref([]); //终端ip 展示
let formListTerIp = ref([]); //终端ip

function getBusinessDetailSetTimeout() {
  setTimeout(() => {
    getBusinessDetail();
    busines.value.resourceVulnList();
    busines1.value.resourceVulnList();
  }, 1000 * 1);
}
//获取业务系统资产
function getBusinessDetail() {
  getDetailBusiness({ id: assetsId }).then(({ data }) => {
    setUpdateDeleteStatus(data); //资产是否可编辑和删除
    state.resource = { ...data.business, personList: data.business.auList }; //基本信息+责任人
    state.resource.assetsGroupId = data.business.groupList.map((item) => item.groupId);
    state.resource.assetsGroupName = data.business.groupList.map((item) => item.groupName).join();
    // 未整改漏洞
    state.unCorrectVulnList = data.business.unCorrectVulnList;
    // 未整改漏洞
    state.correctVulnList = data.business.correctVulnList;
    // 处置中的事件
    state.eventUnder = data.business.eventList;
    // 未处置的事件
    state.handleEventList = data.business.handleEventList;
    // 计算设备资产
    state.resourceList = data.business.resourceList;
    //回显编辑表单 局域网ip
    let localList = data.business.portsServiceList.filter((item) => item.networkType == 0);
    setLocalIpList(localList);

    let networkList = data.business.portsServiceList.filter((item) => item.networkType == 1);
    setLocalIpList(networkList, true);

    setURLIpList(data.business.domainList);
  });
}

// let assetsUpdate = ref(true); //是否可编辑资产
let localIpList = ref([]); //局域网ip列表 展示
let formListIpLocal = ref([]); //局域网ip列表
let resourceSoftwares = ref([]); //基础软件列表
let formListSoft = ref([]); //基础软件列表表单列表
let resourceAssemblys = ref([]); //组件列表

// 查询计算设备详情
function getAssetsDetail() {
  assetsDetail({ id: route.params.id }).then((res) => {
    resourcesEchartsInfo();
    setUpdateDeleteStatus(res.data); //是否可编辑

    state.resource = res.data.resource; //基本信息

    state.resource.assetsGroupId = res.data.resource.groupList.map((item) => item.groupId);
    state.resource.assetsGroupName = res.data.resource.groupList.map((item) => item.groupName).join();

    state.resourceVulnList = res.data.resource.resourceVulnList;
    state.eventList = res.data.resource.eventList;
    let resource = res.data.resource;
    resourceSoftwares.value = resource.resourceSoftwares; //基础资源软件
    //其他业务组件
    resourceAssemblys.value = resource.resourceAssemblys.map((item) => {
      return {
        ...item,
        name: item.assemblyName,
      };
    });

    //回显编辑表单 局域网ip
    setLocalIpList(resource.resourceServers);
  });

  // 查询已整改漏洞和已处置事件
  let ass = {
    id: route.params.id,
    spare1: "done",
    row: 5,
  };
  eventAndVuln(ass).then((res) => {
    state.disposedEvents = res.data.eventList;
    state.vulnList = res.data.vulnList;
  });
}

//获取终端资产详情
function getDetailTerminal() {
  getTerminalDetail({ id: route.params.id }).then(({ data }) => {
    setUpdateDeleteStatus(data, true); //资产是否可编辑和删除

    state.resource = { ...data.terminal }; //基本信息+责任人

    state.resource.assetsGroupId = data.terminal.groupList.map((item) => item.groupId);
    state.resource.assetsGroupName = data.terminal.groupList.map((item) => item.groupName).join();

    // 处置中的事件
    state.eventUnder = data.terminal.eventList;
    // 未处置的事件
    state.handleEventList = data.terminal.handleEventList;

    //ip
    setTerIpList(data.terminal.terminalIps);
  });
}

let updateStatus = ref(false); //是否可编辑
let delStatus = ref(false); //是否可删除
let changeStatus = ref(false); //终端资产是否可变更

function setUpdateDeleteStatus(data, isTerminal = false) {
  updateStatus.value = data.assetsUpdate == "Y";

  delStatus.value = data.assetsDelete == "Y";
  if (isTerminal) {
    changeStatus.value = data.assetsChange == "Y";
  }
}

//设置域名表单项
function setURLIpList(list) {
  let urlForm = {
    formType: "input",
    prop: "domain",
    label: "",
    itemWidth: "calc(100% - 220px)",
    value: "",
    id: "",
    placeholder: "请输入系统入口",
  };
  formListURL.value = list.map((item) => {
    return [
      {
        ...urlForm,
        value: item.domain,
        id: item.id,
      },
    ];
  });

  URLList.value = list;
  if (list.length == 0) {
    formListURL.value = [[{ ...urlForm }]];
  }
}

//设置终端资产的ip
function setTerIpList(list) {
  if (list.length > 0) {
    formListTerIp.value = list.map((item) => {
      return [
        {
          formType: "input",
          prop: "ip",
          label: "IP",
          itemWidth: "calc(100% - 220px)",

          value: item.ip,
          id: item.id,
          placeholder: "请输入局域网IP",
        },
      ];
    });
    terIpList.value = list;
  } else {
    formListTerIp.value = [
      [
        {
          formType: "input",
          prop: "ip",
          label: "IP",
          itemWidth: "calc(100% - 220px)",

          value: "",
          id: "",
          placeholder: "请输入局域网IP",
        },
      ],
    ];
    terIpList.value = [];
  }
}

//局域网表单项列表
function setLocalIpList(list, isNetwork = false) {
  let ipFormList = [
    {
      formType: "input",
      prop: "ipStr",
      label: "IP地址",
      itemWidth: "calc((100% - 170px) / 3)",
      ruleName: "ip",
      id: "",
    },
    {
      formType: "number",
      prop: "port",
      label: "端口",
      itemWidth: "calc((100% - 170px) / 3)",
      min: 0,
      max: 65535,
      precision: "0",
      id: "",
    },
    {
      formType: "select",
      prop: "serverAgreement",
      label: "服务协议",
      dictName: "service_agreement",
      filterable: true,
      itemWidth: "calc((100% - 170px) / 3)",
      id: "",
    },
  ];
  //编辑数据
  let editList = list.map((item) => {
    let copyList = JSON.parse(JSON.stringify(ipFormList));
    return [
      {
        ...copyList[0],
        value: item.ip,
        id: item.id,
      },
      {
        ...copyList[1],
        value: Number(item.port),
        id: item.id,
      },
      {
        ...copyList[2],
        value: item.serverAgreement,
        id: item.id,
      },
    ];
  });

  //展示数据
  let viewList = list.map((item) => {
    return {
      ...item,
      type: item.serviceAgreementName,
    };
  });
  if (editList.length == 0) {
    editList = [JSON.parse(JSON.stringify(ipFormList))];
  }
  if (!isNetwork) {
    formListIpLocal.value = editList;
    localIpList.value = viewList;
  } else {
    formListIpNetwork.value = editList;
    networkIpList.value = viewList;
  }
}
//基础资源软件表单列表
function setSoftList(list) {}

let { resource } = toRefs(state);

let form = reactive({});

//编辑
//修改基本信息
let baseInfoRef = ref();
let baseFormData = computed(() => {
  if (baseInfoRef.value) {
    return baseInfoRef.value.formData;
  } else {
    return {};
  }
});
function saveBaseInfo(close, showLoad) {
  let ruleFormRef = baseInfoRef.value.ruleFormRef;
  ruleFormRef.validate((valid) => {
    if (valid) {
      showLoad();
      let updateFn = pageType == 0 ? updateBusiness : pageType == 1 ? saveEditAssetsResource : updateTerminal;
      updateFn({
        ...baseInfoRef.value.formData,
        id: state.resource.id,
        assetsGroupId: baseInfoRef.value.formData.assetsGroupId.join(),
        assetsPerson: baseInfoRef.value.formData.assetsPerson.join(),
      })
        .then((res) => {
          ElMessage.success("保存成功");
          updateBaseInfo();
          close();
        })
        .finally(() => {
          close(false);
        });
    }
  });
}

//删除组件
function delAssembly(item, viewDel) {
  console.log(item);
  deleteAssemblys({ assetsId: route.params.id, assemblyId: item.assemblyId }).then((res) => {
    viewDel(item);
    ElMessage.success("操作成功");
  });
}
//关联组件和资产
function insertAssemblyFn(item, viewAdd) {
  insertAssemblys({ assetsId: route.params.id, assemblyId: item.id }).then(() => {
    viewAdd(item);
    getAssetsDetail();
    ElMessage.success("操作成功");
  });
}

//刷新
//刷新基本信息
function updateBaseInfo() {
  if (pageType == 0) {
    getDetailBusiness({ id: route.params.id }).then(({ data }) => {
      setUpdateDeleteStatus(data);
      state.resource = { ...data.business, personList: data.business.auList }; //基本信息+责任人
      state.resource.assetsGroupId = data.business.groupList.map((item) => item.groupId);
      state.resource.assetsGroupName = data.business.groupList.map((item) => item.groupName).join();
    });
  } else if (pageType == 1) {
    assetsDetail({ id: route.params.id }).then((res) => {
      setUpdateDeleteStatus(res.data);
      // assetsUpdate.value = res.data.assetsUpdate; //是否可编辑

      state.resource = res.data.resource; //基本信息
      state.resource.assetsGroupId = res.data.resource.groupList && res.data.resource.groupList.map((item) => item.id); //责任人
      state.resource.assetsGroupName = res.data.resource.groupList.map((item) => item.groupName).join();
    });
  } else if (pageType == 2) {
    getTerminalDetail({ id: route.params.id }).then(({ data }) => {
      setUpdateDeleteStatus(data, true);
      state.resource = { ...data.terminal }; //基本信息+责任人
      state.resource.assetsGroupId = data.terminal.groupList.map((item) => item.groupId);
      state.resource.assetsGroupName = data.terminal.groupList.map((item) => item.groupName).join();
    });
  }
}

//刷新URL
function updateUrl() {
  getDetailBusiness({ id: route.params.id }).then(({ data }) => {
    setUpdateDeleteStatus(data);
    setURLIpList(data.business.domainList);
    businessEchartsInfo();
  });
}

//刷新终端资产Ip
function updateTerIp() {
  getTerminalDetail({ id: route.params.id }).then(({ data }) => {
    setUpdateDeleteStatus(data, 2);
    setTerIpList(data.terminal.terminalIps);
  });
}

//刷新局域网ip端口
function updateLocal() {
  if (pageType == 0) {
    getDetailBusiness({ id: route.params.id }).then(({ data }) => {
      setUpdateDeleteStatus(data);
      let localList = data.business.portsServiceList.filter((item) => item.networkType == 0);
      setLocalIpList(localList);
      businessEchartsInfo();
    });
  } else if (pageType == 1) {
    assetsDetail({ id: route.params.id }).then(({ data }) => {
      setUpdateDeleteStatus(data);
      setLocalIpList(data.resource.resourceServers);
      resourcesEchartsInfo();
    });
  }
}
//刷新互联网ip端口
function updateNetwork() {
  getDetailBusiness({ id: route.params.id }).then(({ data }) => {
    setUpdateDeleteStatus(data);
    let networkList = data.business.portsServiceList.filter((item) => item.networkType == 1);
    setLocalIpList(networkList, true);
    businessEchartsInfo();
  });
}

//刷新软件
function updateSoft() {
  assetsDetail({ id: route.params.id }).then(({ data }) => {
    setUpdateDeleteStatus(data);
    let resource = data.resource;
    resourceSoftwares.value = resource.resourceSoftwares;
    resourceAssemblys.value = resource.resourceAssemblys.map((item) => {
      return {
        ...item,
        name: item.assemblyName,
      };
    });
  });
}
//刷新关联业务对象
let diagram = ref();
function updateBasicAssets() {
  if (pageType == 0) {
    getDetailBusiness({ id: route.params.id }).then(({ data }) => {
      // 计算设备资产
      state.resourceList = data.business.resourceList;
    });
    businessEchartsInfo();
  }
}
// 关联业务系统资产后刷新数据
function updateBusinessAssets() {
  if (pageType == 1) {
    resourcesEchartsInfo();
  }
}
//转移
let dialogRef = ref();
let currentDomain = ref("");
let currentIndex = ref(-1);
let dynamicRef = ref();
let dynamicTerIpRef = ref();
let dynamicLocalRef = ref();

//转移域名
let transferReturnList = false; //最后一个资产转移后回到列表页
let backListRef = ref();
function openTransfer(item, index, returnList) {
  transferReturnList = returnList;
  currentIndex.value = index;
  dialogRef.value.open();
  currentDomain.value = item[0].prop == "ipStr" || item[0].prop == "ip" ? item[0].value : item[0].id;
}

function transferUrl(id) {
  ElMessageBox.confirm(`确定要转移到此资产吗？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let params = {
      id,
      domain: currentDomain.value,
    };
    transferDomain(params).then(({ data }) => {
      ElMessage.success("转移成功");
      if (transferReturnList) {
        backListRef.value.backList();
        return;
      }
      getBusinessDetail();
      dialogRef.value.close();
      if (data.deleteCount) {
        router.push(getRouteName(pageType));
      } else {
        dynamicRef.value.del(currentIndex.value);
      }
    });
  });
}

//转移计算设备ip
function transferIp(id) {
  transferIpFn(id, 1);
}

//终端资产转移ip
function transferTerIp(id) {
  transferIpFn(id, 2);
}

function transferIpFn(id, type) {
  ElMessageBox.confirm(`是否确认转移该IP？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let params = {
      id,
      spare3: currentDomain.value,
    };
    let apiFn = type == 1 ? transferIpApi : transferTerminalIp;
    apiFn(params).then(({ data }) => {
      ElMessage.success("转移成功");
      if (transferReturnList) {
        backListRef.value.backList();
        return;
      }
      dialogRef.value.close();
      if (data.deleteCount) {
        router.push(getRouteName(pageType));
      } else {
        if (type == 1) {
          dynamicLocalRef.value.delByIp(currentIndex.value);
        } else {
          dynamicTerIpRef.value.del(currentIndex.value);
        }
      }
    });
  });
}

// 资产关系图
let gridContent = ref();
let scrollBody = ref();
onMounted(() => {
  if (scrollBody.value) {
    window.addEventListener("scroll", windowScroll);
  }
});
let grid_change = ref(false);
function windowScroll() {
  let body = scrollBody.value.$el.getBoundingClientRect().top;
  let toTop = gridContent.value.getBoundingClientRect().top;
  if (toTop <= 20 && body <= 0) {
    grid_change.value = true;
  } else {
    grid_change.value = false;
  }
}
// 获取业务系统资产关系图
let diagramDataShow = ref(false);
async function businessEchartsInfo() {
  state.diagramData = {};
  diagramDataShow.value = false;
  let res = await getBusinessEchartsInfo({ id: assetsId });
  state.diagramData = res.data;
  setTimeout(() => {
    diagramDataShow.value = true;
  }, 200);
}
// 获取计算设备资产关系图
async function resourcesEchartsInfo() {
  state.diagramData = {};
  diagramDataShow.value = false;
  let res = await getResourcesEchartsInfo({ id: assetsId });
  state.diagramData = res.data;
  setTimeout(() => {
    diagramDataShow.value = true;
  }, 200);
  // console.info(state.diagramData);
}
switch (pageType) {
  case 0:
    businessEchartsInfo();
    break;
  case 1:
    break;
  case 2:
    break;
}
</script>
<style scoped lang="scss">
.grid-content {
  background: #f9f9f9;
  height: 954px;
  width: 100%;
  border-radius: $radiusL;
  > .title {
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    font-family: "PingFangSC-Regular", "PingFang SC";
    font-weight: 400;
    color: $fontColor;
    padding: 10px 10px;
  }
  > .content {
    height: 894px;
    margin-top: 20px;
  }
}
.grid_change {
  position: fixed;
  top: 20px;
  width: 26%;
}
.mouse {
  padding: 20px;
}
.url-list {
  margin-top: -17px;
  li {
    padding: 15px 0;
    border-bottom: 1px solid #ebedf1;
  }
}
.soft-ul {
  li {
    width: 100%;
    display: flex;
    div:first-child {
      width: 33%;
    }
    div:nth-child(2) {
      width: 41%;
    }
  }
}
.un-update {
  :deep(.edit-btn) {
    display: none !important;
  }
}
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
  }
}
span.item-label {
  display: inline-block;
  width: 120px;
  color: $fontColorSoft;
}
.ellipse {
  width: calc(100% - 120px);
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:deep {
  .title-bottom-line {
    margin-bottom: 0;
    & + section {
      padding-top: 15px;
    }
  }
}
.resourceAssemblys {
  width: calc(100% - 8em);
  flex-wrap: wrap;
}
</style>
