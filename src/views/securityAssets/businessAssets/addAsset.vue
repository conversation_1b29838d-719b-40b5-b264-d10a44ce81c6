<!-- 新增各种类型的资产 -->
<!-- 终端资产变更为计算设备资产 -->
<!-- 待确认资产->新建资产 -->
<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <back-list-btn :type="(baseFormData.value && baseFormData.value.type) || (route.query && route.query.type)"></back-list-btn>
    <div class="ionDiv">
      <div class="tit pointer" @click="changeFold()">
        <p class="careful">注意事项</p>
        <div>
          <el-icon :size="12" v-if="!isFlod"> <ArrowUp /> </el-icon>
          <el-icon :size="12" v-else> <ArrowDown /> </el-icon>
        </div>
      </div>

      <p v-show="!isFlod" class="content">新建资产前请认真阅读此说明并严格执行，资产对象分为业务系统对象、计算设备对象和终端资产对象三类：</p>
      <p v-show="!isFlod" class="content">
        1、业务系统对象为业务系统的逻辑形态，承载业务系统管理关系和应用漏洞、事件；2、计算设备对象以操作系统为单元，承载相应管理关系和操作系统与通用软件漏洞、事件；3、终端资产对象以个人计算机终端为基础管理单元，主要管理IP等关键资产属性及其相关事件、漏洞
      </p>
    </div>
    <el-row :gutter="80">
      <el-col :span="8">
        <p class="title-bottom-line">资产基本信息</p>
        <base-info-form ref="baseInfoRef" :edit-info="terminalInfo"></base-info-form>
      </el-col>
      <el-col :span="16">
        <div v-if="baseFormData.type == 0">
          <!--业务系统资产相关  url-->
          <dynamic ref="dynamicURLRef" dynamicName="系统入口" label-width="6em" :formList="formListURL" :data-rows="urlRows" />
        </div>
        <!-- 添加局域网IP、端口与服务组 -->
        <dynamic
          v-if="baseFormData.type == 0 || baseFormData.type == 1"
          ref="dynamicIpRef"
          dynamicName="局域网IP、端口与服务组"
          label-width="6em"
          :formList="isConfirmBusiness ? formListIpHasValue1 : formListIp"
          :data-rows="ipRows"
        ></dynamic>
        <dynamic
          v-if="baseFormData.type == 0"
          ref="dynamicIpRef2"
          dynamicName="互联网IP、端口与服务组"
          label-width="6em"
          :formList="isConfirmBusiness ? formListIpHasValue2 : formListIp"
          :data-rows="internetIpRows"
        ></dynamic>

        <!-- 计算设备资产相关 -->
        <div v-if="baseFormData.type == 1">
          <!-- 基础资源软件 -->
          <p class="title-bottom-line">基础资源软件</p>
          <section>
            <dynamic-soft ref="dynamicSoftRef" dynamicName=""></dynamic-soft>
            <!-- 其他应用组件 -->
            <xel-tag
              form-type="tag"
              class="margin-top10"
              prop="tagList"
              v-model="assemblyList"
              btn-text="其他应用组件"
              msg-text="组件"
              :save-api="saveAssemblyByAssemblyName"
              name-key="name"
            ></xel-tag>
          </section>
        </div>
        <!-- 终端资产局域网ip -->
        <dynamic
          v-if="baseFormData.type == 2"
          ref="dynamicTerIpRef"
          dynamicName="局域网IP"
          label-width="6em"
          :formList="formListTerIp"
          :data-rows="terIpRows"
        ></dynamic>
      </el-col>
    </el-row>

    <el-divider></el-divider>
    <div class="margin-bottom20 text-right">
      <el-button @click="submit" type="primary" class="search-button" :loading="loading"> 保存 </el-button>
    </div>
  </el-card>
</template>
<script>
export default {
  name: "AddAsset",
};
</script>
<script setup>
import { saveAssemblyByAssemblyName, insertAssemblys, saveBasic } from "@/api/securityAssets/assetsList";
import { addAssetsBusiness } from "@/api/securityAssets/business";
import { addTerminal, getTerminalDetail } from "@/api/securityAssets/terminal";

import { openIpNotConfirm, openDomainNotConfirm } from "@/api/securityAssets/confirm";
import { selectAssetsByIpUuid, saveBasicAsset, saveWorkbenchTerminalAsset } from "@/api/workSpace/asset";

import { rules } from "@/xelComponents/utils/formValidator";

import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, onActivated } from "vue";

import { getDicts } from "@/api/system/dict/data";
import { validaDomain } from "@/utils/ruoyi";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";
const store = useStore();

onActivated(() => {
  setMenuIndex();
});
function setMenuIndex() {
  let pageType = -1;
  if (route.name == "AddAsset") {
    pageType = route.query.type || 0;
  } else if (route.name == "ChangeAsset") {
    pageType = 3;
  } else if (route.name == "ConfirmAdd") {
    pageType = 4;
  } else if (route.name == "WorkBenchConfirmAdd") {
    pageType = 5;
  }
  let pageParentName = {
    0: "BusinessAssets", //query判断
    1: "Underlying",
    2: "Terminal",
    3: "Terminal", //资产变更route.name == ChangeAsset
    4: "Confirm", //待确认资产新增 route.name ==  ConfirmAdd
    5: "Workbench", //工作台新增 route.name ==  WorkBenchConfirmAdd
  };
  if (pageType > -1) {
    store.commit("setDetailParentMenu", { detailName: route.name, parentName: pageParentName[pageType] });
  }
}

//基本信息表单组件
import baseInfoForm from "./components/baseInfoForm.vue";
import backListBtn from "./components/backListBtn.vue";
import getRouteName from "./components/backList.js";

//是否是变更资产页面
let isChangeAsset = computed(() => {
  return route.name == "ChangeAsset";
});

let isFlod = ref(false);
//
changeFold(localStorage.getItem("isFlod"));
// 是否折叠
function changeFold(val) {
  isFlod.value = !isFlod.value;
  if (val === true) {
    isFlod.value = true;
  }

  if (isFlod.value) {
    localStorage.setItem("isFlod", isFlod.value);
  }
}

//添加ip
let rowsIp = reactive([0]);
let formListIp = reactive([
  {
    formType: "input",
    prop: "ipStr",
    label: "IP地址",
    itemWidth: "calc((97% - 70px) / 3 )",
    value: "",
    ruleName: "ip",
  },
  {
    formType: "number",
    prop: "port",

    label: "端口",
    itemWidth: "calc((97% - 70px) / 3 )",
    min: 0,
    max: 65535,
    precision: "0",
    value: "",
  },
  {
    formType: "select",
    prop: "serverAgreement",

    label: "服务协议",
    dictName: "service_agreement",
    filterable: true,
    itemWidth: "calc((97% - 70px) / 3 )",

    value: "",
  },
]);

//URL
let formListURL = [
  {
    formType: "input",
    prop: "domain",
    label: "系统入口",
    itemWidth: "calc(100% - 70px)",

    value: "",
    /*placeholder: "必须以http://或https://开头",*/
    placeholder: "请输入系统入口",
    onChange: (val) => {
      validaDomain(val, domainDic.value);
    },
  },
];

//终端资产局域网ip
let formListTerIp = [
  {
    formType: "input",
    prop: "dipomain",
    label: "局域网IP",
    itemWidth: "calc(100% - 70px)",
    value: "",
    placeholder: "请输入局域网IP地址",
    onChange: (val) => {
      validaTerIp(val);
    },
  },
];

//基础资源软件
let formListSoft = reactive([
  {
    label: "",
    prop: "softwareEdition",
  },
]);

//其他应用组件
let assemblyList = ref([]);

//保存
let loading = ref(false);
let dynamicIpRef = ref();
let dynamicIpRef2 = ref();
let dynamicSoftRef = ref();
let baseInfoRef = ref();
let baseFormData = computed(() => {
  if (baseInfoRef.value) {
    return baseInfoRef.value.formData;
  } else {
    return {};
  }
});
//业务系统入口
let dynamicURLRef = ref();

//终端资产局域网IP
let dynamicTerIpRef = ref();

function submit() {
  let ruleFormRef = baseInfoRef.value.ruleFormRef;
  ruleFormRef.validate((valid) => {
    if (valid) {
      //验证判断
      //* type ==0 url格式且必填 && 局域网ip格式 && 互联网ip格式
      //* type ==1 局域网ip格式且必填 && 基础资源软件版本号必填
      //* type ==2  局域网Ip必填并验证格式
      if (
        (baseFormData.value.type == 0 &&
          validaDomainList(dynamicURLRef.value.list) &&
          validIpList(dynamicIpRef.value.list, "局域网") &&
          validIpList(dynamicIpRef2.value.list, "互联网")) ||
        (baseFormData.value.type == 1 && validIpList(dynamicIpRef.value.list, "局域网") && validSoftList(dynamicSoftRef.value.list)) ||
        (baseFormData.value.type == 2 && validaTerIpList(dynamicTerIpRef.value.list))
      ) {
        loading.value = true;
        let apiData = {};
        if (baseFormData.value.type == 0) {
          apiData = saveBusinessAsset();
        } else if (baseFormData.value.type == 1) {
          /* 增加一层数据格式判断 */
          if (handleData(dynamicSoftRef.value.list)) {
            apiData = saveComputedAsset();
          } else {
            return false;
          }
        } else if (baseFormData.value.type == 2) {
          apiData = saveTerminalAsset();
        }

        //工作台新增资产
        if (isWorkBench) {
          if (baseFormData.value.type == 1) {
            /* 增加一层数据格式判断 */
            if (handleData(dynamicSoftRef.value.list)) {
              apiData = saveWorkBenchComputedAsset();
            } else {
              return false;
            }
          } else if (baseFormData.value.type == 2) {
            apiData = saveWorkBenchTerminalAsset();
          }
        }
        let sendData = { ...apiData.data };
        if (route.params.confirmId) {
          sendData.notConfirmId = route.params.confirmId;
          if (isWorkBench) {
            sendData.notConfirmId = "";
          }
        }
        apiData
          .api(sendData)
          .then((res) => {
            ElMessage.success("操作成功");
            if (isWorkBench) {
              router.push({
                name: "Workbench",
              });
            } else {
              router.push(getRouteName(baseFormData.value.type));
            }
            store.commit("closeCurrentTab");
          })
          .finally(() => {
            loading.value = false;
          });
      }
    }
  });
}

//新增业务系统资产
function saveBusinessAsset() {
  let portsServiceList1 = handlerServersList(dynamicIpRef.value.list, 0);
  let portsServiceList2 = handlerServersList(dynamicIpRef2.value.list, 1);

  return {
    data: {
      //基本信息
      ...baseFormData.value,
      assetsGroupId: baseFormData.value.assetsGroupId.join(),
      assetsPerson: baseFormData.value.assetsPerson.join(),
      //ip，端口，服务协议json串
      portsServiceList: portsServiceList1.concat(portsServiceList2),
      domain: dynamicURLRef.value.list.map((item) => item[0].value).join(),
    },
    api: addAssetsBusiness,
  };
}

/* 新增 - 基础资源软件 数据*/
const resourceSoftData = ref([]);

//新增计算设备资产
function saveComputedAsset() {
  let data = {
    //基本信息
    ...baseFormData.value,
    assetsGroupId: baseFormData.value.assetsGroupId.join(),

    assetsPerson: baseFormData.value.assetsPerson.join(),
    //ip，端口，服务协议json串
    resourceServers: handlerServersList(dynamicIpRef.value.list, 0),
    //基础资源软件信息json串
    /*resourceSoftwares: handleData(dynamicSoftRef.value.list),*/
    resourceSoftwares: resourceSoftData.value,
    //组件id，多个组件id之间用英文逗号隔开
    assembly: assemblyList.value.map((item) => item.id).join(),
    // assetsSource: "create",
  };

  if (isChangeAsset.value) {
    data.changeFlg = "Y";
    data.terminalId = route.params.terminalId;
  }
  return {
    data,
    api: saveBasic,
  };
}

//新增终端资产
function saveTerminalAsset() {
  return {
    data: {
      //基本信息
      ...baseFormData.value,
      assetsGroupId: baseFormData.value.assetsGroupId.join(),
      assetsPerson: baseFormData.value.assetsPerson.join(),
      ipList: dynamicTerIpRef.value.list.map((item) => {
        return {
          ip: item[0].value,
        };
      }),
    },
    api: addTerminal,
  };
}

//工作台新增计算设备资产
function saveWorkBenchComputedAsset() {
  let baseInfo = baseFormData.value;
  return {
    data: {
      ipUuid: route.params.id,
      name: baseInfo.name,
      hostName: baseInfo.hostName,
      assetsGroupId: baseInfo.assetsGroupId.join(),
      level: baseInfo.level,
      deptId: baseInfo.deptId,
      assetsPerson: baseInfo.assetsPerson.join(","),
      portService: JSON.stringify(handlerServersList(dynamicIpRef.value.list, 0)),

      /*resourceSoftwares: handleData(dynamicSoftRef.value.list),*/
      resourceSoftwares: resourceSoftData.value,
      assembly: assemblyList.value.map((item) => item.id).join(),
    },
    api: saveBasicAsset,
  };
}

/* 增加 - 后端增加需求，数据格式处理 */
function handleData(data) {
  resourceSoftData.value = [];
  let isData = true;
  if (data.length) {
    /* 对数据进行处理，增加中间值，resourceSoftData */
    for (let i = 0; i < data.length; i++) {
      if (data[i].softwareType === "" && data[i].softwareEdition === "") {
      } else if (data[i].softwareType === "" || data[i].softwareEdition === "") {
        ElMessage.warning(`请填写完整基础资源软件信息`);
        isData = false;
        break;
      } else {
        resourceSoftData.value.push(data[i]);
      }
    }
  }
  return isData;
}

//工作台新增终端资产
function saveWorkBenchTerminalAsset() {
  let baseInfo = baseFormData.value;

  return {
    data: {
      ipUuid: route.params.id,
      name: baseInfo.name,

      hostName: baseInfo.hostName,
      assetsGroupId: baseInfo.assetsGroupId.join(),
      level: baseInfo.level,
      deptId: baseInfo.deptId,
      assetsPerson: baseInfo.assetsPerson.join(","),
      portService: JSON.stringify(
        dynamicTerIpRef.value.list.map((item) => {
          return {
            ip: item[0].value,
          };
        })
      ),
    },
    api: saveWorkbenchTerminalAsset,
  };
}

//ip,端口，协议保存数据
function handlerServersList(list, networkType) {
  let arr = list.filter((item) => item[0].value);
  return arr.map((item) => {
    return {
      ip: item[0].value,
      ipStr: item[0].value,
      port: item[1].value,
      serverAgreement: item[2].value,
      networkType: networkType,
    };
  });
}

//验证ip表单列表
function validIpList(list, text) {
  let isEmpty = true; //必须写一个ip
  for (let i = 0; i < list.length; i++) {
    if (list[i].find((item) => item.value)) {
      isEmpty = false;
      for (let j = 0; j < list[i].length; j++) {
        if (!list[i][j].value && list[i][j].value !== 0) {
          /* 局域网IP、端口与服务组 服务协议改为非必填 */
          if (list[i][j].prop === "serverAgreement") {
            return true;
          }

          ElMessage.warning(`${text}${list[i][j].label}不能为空!`);
          return false;
        }
        if (list[i][j].ruleName == "ip") {
          let { result, errMsg } = rules.IP(list[i][j].value);
          if (!result) {
            ElMessage.warning(`${text}IP地址格式不正确`);
            return false;
          }
        }
      }
    }
  }
  if (baseFormData.value.type == 1 && isEmpty) {
    ElMessage.warning(`局域网IP不能为空`);
    return false;
  }
  return true;
}

//验证基础资源软件
function validSoftList(list) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].softwareValue && !list[i].softwareEdition) {
      ElMessage.warning(`版本号不能为空`);
      return false;
    } else if (list[i].softwareEdition && !list[i].softwareValue) {
      ElMessage.warning(`基础资源软件不能为空`);
      return false;
    }
  }
  return true;
}

/* 新增 - 获取验证字典 */
const domainDic = ref([]);
const getDicFun = () => {
  getDicts("asset_business_domain_agreement").then((res) => {
    domainDic.value = res.data.map((item) => {
      return item.dictLabel;
    });
  });
};
getDicFun();
//业务系统入口
//验证URL格式
function validaDomainList(list) {
  for (let i = 0; i < list.length; i++) {
    if (!validaDomain(list[i][0].value, domainDic.value)) {
      return false;
    }
  }
  return true;
}
/*function validaDomain(val) {
  let status = true;
  if (val) {
    /!* 新增 - 根据字典值 判断格式 *!/
    let length = domainDic.value.length;
    let star = 0;
    let end = 0;
    for (let i=0; i<length; i++) {
      let k = domainDic.value[i]+"://";
      if (!val.startsWith(k)) star += 1;
      if (val.endsWith(k)) end += 1;
    }
    /!*
    * star === 字典的length 说明校验都没有通过
    * star < 字典的length 说明校验通过
    * end >0 说明格式不正确
    * *!/
    if(star === length) {
      const strType = domainDic.value.filter(Boolean).join("://, ")+"://";
      ElMessage.warning(`系统入口必须以${ strType }开头`);
      status = false;
    } else if(star < length && end > 0) {
      ElMessage.warning("系统入口格式不正确");
      status = false;
    }
  }
  return status;
}*/

//终端资产局域网
//验证
function validaTerIpList(list) {
  let isEmpty = list.filter((item) => item[0].value).length == 0;
  if (isEmpty) {
    ElMessage.warning("局域网IP不能为空！");

    return false;
  }
  for (let i = 0; i < list.length; i++) {
    if (!validaTerIp(list[i][0].value)) {
      return false;
    }
  }
  return true;
}
function validaTerIp(val) {
  let { result, errMsg } = rules.IP(val);
  if (!result) {
    ElMessage.warning(`局域网IP地址格式不正确`);
    return false;
  }
  return true;
}

//变更资产相关
let terminalInfo = ref({});
setChangeInfo();
function setChangeInfo() {
  let terminalId = route.params.terminalId;

  if (!terminalId) return;
  //资产类型不可编辑

  //获取终端资产详情
  getTerminalDetail({ id: terminalId }).then(({ data }) => {
    //回显基本信息
    terminalInfo.value = { ...data.terminal }; //基本信息+责任人
    terminalInfo.value.assetsGroupId = data.terminal.groupList.map((item) => item.groupId);
    terminalInfo.value.assetsGroupName = data.terminal.groupList.map((item) => item.groupName).join();

    //回显编辑表单 局域网ip
    setLocalIpList(data.terminal.terminalIps);
  });
}

//根据 ip地址设置输入框的值
let ipRows = reactive([]);
let internetIpRows = reactive([]); //互联网ip
function setLocalIpList(list, disabled = false, isInternet = false) {
  for (let item of list) {
    let copyFormList = JSON.parse(JSON.stringify(formListIp));

    copyFormList[0].value = item.ip;
    if (disabled && item.ip) {
      copyFormList[0].disabled = true;
    }

    copyFormList[1].value = item.port ? Number(item.port) : "";
    copyFormList[2].value = item.serviceId ? item.serviceId : "";
    if (isInternet) {
      internetIpRows.push(copyFormList);
    } else {
      ipRows.push(copyFormList);
    }
  }
}

//待确认资产新建资产相关
let confirmBusiness = ref({}); //保存待确认资产的ip信息
//是否是待确认资产-新增
let isConfirmBusiness = route.name == "ConfirmAdd" && route.params.type == 0;
//是否是工作台待确认资产的添加
let isWorkBench = route.name == "WorkBenchConfirmAdd";

//待确认资产,新增业务系统资产不能编辑ip,只能添加端口
let formListIpHasValue1 = computed(() => {
  return [{ ...formListIp[0], value: confirmBusiness.value.lanIp }, formListIp[1], formListIp[2]];
});
let formListIpHasValue2 = computed(() => {
  return [{ ...formListIp[0], value: confirmBusiness.value.internetIp }, formListIp[1], formListIp[2]];
});

getConfirmDetail();
function getConfirmDetail() {
  if (isConfirmBusiness) {
    formListIp[0].disabled = true; //待确认资产,新增业务系统资产不能编辑ip,只能添加端口
  }
  let { confirmId, type } = route.params;
  if (!confirmId) return;
  let getDetailFn = type == 1 ? openIpNotConfirm : openDomainNotConfirm;
  if (isWorkBench) {
    getDetailFn = selectAssetsByIpUuid;
  }
  getDetailFn({ id: confirmId, ipUuid: confirmId }).then(({ data }) => {
    if (type == 1) {
      if (isWorkBench) {
        setLocalIpList(data, true);
        setTerIpList(data, true);
      } else {
        setLocalIpList([{ ip: data.ip }], true);
        setTerIpList([{ ip: data.ip }], true);
      }
    } else {
      confirmBusiness.value = {
        ...data,
      };
      setUrlList([{ domain: data.domain }], true);
      setLocalIpList([{ ip: data.lanIp }], true);
      setLocalIpList([{ ip: data.internetIp }], true, true);
    }
  });
}

let terIpRows = reactive([]);
function setTerIpList(list, disabled = false) {
  let pushIps = []; //去重
  for (let item of list) {
    if (pushIps.includes(item.ip)) continue;
    let copyFormList = JSON.parse(JSON.stringify(formListTerIp));

    copyFormList[0].value = item.ip;
    if (disabled) {
      copyFormList[0].disabled = true;
    }
    terIpRows.push(copyFormList);
    pushIps.push(item.ip);
  }
}

let urlRows = reactive([]);
function setUrlList(list, disabled = false) {
  for (let item of list) {
    let copyFormList = JSON.parse(JSON.stringify(formListURL));

    copyFormList[0].value = item.domain;
    if (disabled) {
      copyFormList[0].disabled = true;
    }
    urlRows.push(copyFormList);
  }
}
</script>
<style scoped lang="scss">
.ionDiv {
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: $radiusS;
  .tit {
    display: flex;
    width: 100%;
    justify-content: space-between;
    :deep .el-icon {
      cursor: pointer;
    }
  }
}
.careful {
  font-size: 10px;
  color: #303846;
  font-weight: 400;
  margin: 10px 0 10px 0;
}
.content {
  font-size: 10px;
  color: $fontColorSoft;
  margin: 10px 0 10px 0;
}
</style>
