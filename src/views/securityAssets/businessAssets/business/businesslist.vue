<template>
  <div class="inDiv title-bottom-line">
    <p>{{ buttName }}</p>
    <el-button size="mini" @click="jump">{{ total }}</el-button>
  </div>
  <section>
    <slot> <xel-table ref="tableRef" :columns="columns" :data="onData" :pagination="false"> </xel-table></slot>
  </section>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { selectAssetsUnCorrectVulnList, selectAssetsCorrectVulnList, selectBusinessEventList } from "@/api/securityAssets/business.js";
import { getTerminalEventList } from "@/api/securityAssets/terminal.js";
import { ref, reactive, toRefs, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
let props = defineProps({
  buttName: {
    type: String,
    default: "",
  },
  assetsId: {
    type: String,
    default: "",
  },

  getTableData: {
    type: String,
    default: "",
  },
  columns: {
    type: Array,
    default: () => {
      return [];
    },
  },
  onData: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
// 列表的数量
let total = ref("");
function resourceVulnList() {
  let vulnList = {
    id: props.assetsId,
    pageSize: 10,
    pageNum: 1,
  };

  if (props.buttName == "未整改漏洞") {
    selectAssetsUnCorrectVulnList(vulnList).then((res) => {
      total.value = res.data.total;
    });
  } else if (props.buttName == "已整改漏洞") {
    selectAssetsCorrectVulnList(vulnList).then((res) => {
      total.value = res.data.total;
    });
  } else if (props.buttName == "处置中事件") {
    let val = {
      id: props.assetsId,
      pageSize: 10,
      pageNum: 1,
    };
    if (props.getTableData == 2) {
      getTerminalEventList(val).then((res) => {
        total.value = res.data.total;
      });
    } else {
      selectBusinessEventList(val).then((res) => {
        total.value = res.data.total;
      });
    }
  } else if (props.buttName == "已处置事件") {
    let val = {
      id: props.assetsId,
      pageSize: 10,
      pageNum: 1,
      spare1: "done",
    };
    if (props.getTableData == 2) {
      getTerminalEventList(val).then((res) => {
        total.value = res.data.total;
      });
    } else {
      selectBusinessEventList(val).then((res) => {
        total.value = res.data.total;
      });
    }
  }
}
resourceVulnList();
// 点击数量跳转
function jump() {
  let params = {
    id: props.assetsId,
    assetType: route.params.type,
  };

  if (props.buttName.includes("漏洞")) {
    params.pageType = 0;
  } else {
    params.pageType = 1;
  }

  if (props.buttName.includes("已")) {
    params.spare1 = 1;
  } else {
    params.spare1 = 0;
  }
  router.push({ name: "JumpPage", params });
}

defineExpose({
  resourceVulnList,
});
</script>

<style scoped lang="scss">
.inDiv {
  display: flex;
  p {
    margin-right: 10px;
    margin-top: 2px;
  }
}
</style>
