<template>
  <el-card>
    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      label-width="140px"
      @search="search"
      @reset="reset"
    ></common-search>
    <xel-table v-if="loadApi" ref="tableRef" :columns="loadColumns" :defaultParams="{ id }" :load-data="loadApi" :checkbox="false"> </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "JumpService",
};
</script>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { useRouter, useRoute } from "vue-router";
import { selectRalationResourcesList, deleteRalationResource } from "@/api/securityAssets/business";
import { getRelevantBusiness, deleteRalationBusiness } from "@/api/securityAssets/assetsList";

const route = useRoute();
let { id, assetType } = route.params;

import { useStore } from "vuex";

const store = useStore();

onActivated(() => {
  setMenuIndex();
});
function setMenuIndex() {
  let pageParentName = {
    0: "BusinessAssets",
    1: "Underlying",
    2: "Terminal",
  };
  store.commit("setDetailParentMenu", { detailName: route.name, parentName: pageParentName[assetType] });
}

let tableRef = ref();
// 搜索按钮
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
// 重置按钮
function reset() {
  searchState.data = {
    spare2: "",
    searchValue: "",
    spare1: "",
    pageNum: 1,
    pageSize: 10,
  };
  tableRef.value.reload(searchState.data);
}
// 计算资产列表
let columns = reactive([
  {
    prop: "systemName",
    label: "业务系统名称",
  },
  {
    prop: "domain",
    label: "域名",
  },
  {
    prop: "ip",
    label: "服务IP",
  },

  {
    prop: "port",
    label: "服务端口",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
]);
// 搜索配置项
let searchState = reactive({
  labelName: "",
  data: {
    spare2: "",
    spare1: "",
    searchValue: "",
    pageNum: 1,
    pageSize: 10,
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "spare1",
      label: "计算设备对象名称",
    },
    {
      formType: "input",
      prop: "spare2",
      label: "主机名称",
    },
  ],
});
// 业务系统列表配置项
let oNcolumns = reactive([
  {
    prop: "businessIp",
    label: "互联网IP/局域网IP",
  },
  {
    prop: "businessPort",
    label: "端口",
  },
  {
    prop: "resourceName",
    label: "计算设备对象名称",
  },

  {
    prop: "resourceHost",
    label: "主机名称",
  },
  {
    prop: "resourceIp",
    label: "局域网IP",
  },
  {
    prop: "resourcePort",
    label: "局域网端口",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          ondelFn(scope.row);
        },
      },
    ],
  },
]);
let loadApi = ref();
let loadColumns = ref("");
if (assetType == 0) {
  // 业务系统资产
  loadColumns.value = oNcolumns;
  loadApi.value = selectRalationResourcesList;
} else if (assetType == 1) {
  // 计算设备资产 事
  searchState.formList = [
    {
      formType: "input",
      prop: "searchValue",
      label: "业务系统名称",
    },
  ];
  searchState;
  loadColumns.value = columns;
  loadApi.value = getRelevantBusiness;
}
// 计算设备资产取消关联业务系统资产
function delFn(val) {
  let ids = val.relId;
  let ida = val.id;
  let assetsId = val.assetsId;
  let systemName = val.systemName;
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteRalationBusiness({ id: ida, relId: ids, resourcesId: assetsId, systemName: systemName }).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      search(false);
    });
  });
}
// 业务系统取消关联业务系统资产
function ondelFn(val) {
  let ida = val.id;
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteRalationResource({ id: ida }).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      search(false);
    });
  });
}
</script>
<style scoped lang="scss"></style>
