<template>
  <div class="inDiv title-bottom-line">
    <div class="ass">
      <p>关联计算设备对象</p>
      <el-button size="mini" @click="jump">{{ total }}</el-button>
    </div>
    <el-button @click="systemObject" class="search-button" v-if="assetsUpdate">
      <el-icon :size="12">
        <plus />
      </el-icon>
      添加关联计算设备对象
    </el-button>
  </div>
  <section>
    <xel-table ref="tableRef" :data="onData" :columns="oNcolumns" resKey="businessList" :pagination="false"> </xel-table>
  </section>

  <xel-dialog title="添加关联计算设备对象" ref="dialogRef" :width="$globalWindowSize == 'S' ? '1300px' : '1500px'" @submit="submitDialog">
    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      label-width="120px"
      @search="search"
      @reset="reset"
    ></common-search>
    <xel-table
      ref="tableding"
      :columns="columns"
      :load-data="getRightResources"
      @selection-change="handleSelectionChange"
      :checkbox="true"
      row-key="id"
    >
    </xel-table>
  </xel-dialog>
</template>
<script setup>
import {
  getBusinessLeftList,
  getRightResources,
  saveRalationResources,
  deleteRalationResource,
  selectRalationResourcesList,
} from "@/api/securityAssets/business";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
const $globalWindowSize = window.$globalWindowSize;
const router = useRouter();
const route = useRoute();
let props = defineProps({
  idName: {
    type: String,
    default: "",
  },
  onData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  assetsUpdate: {
    type: Boolean,
    default: false,
  },
  assetsDelete: {
    type: Boolean,
    default: false,
  },
});
onActivated(() => {
  getselectRalation();
});
let emits = defineEmits(["update"]);

let tableRef = ref();
let tableding = ref();

let dialogRef = ref();
// 列表配置项
let oNcolumns = reactive([
  {
    prop: "businessIp",
    label: "互联网IP/局域网IP",
    width: $globalWindowSize == "L" ? "" : "130",
  },
  {
    prop: "businessPort",
    label: "端口",
  },
  {
    prop: "resourceName",
    label: "计算设备对象名称",
    width: $globalWindowSize == "L" ? "" : "130",
  },

  {
    prop: "resourceHost",
    label: "主机名称",
  },
  {
    prop: "resourceIp",
    label: "局域网IP",
  },
  {
    prop: "resourcePort",
    label: "局域网端口",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: !props.assetsDelete,
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
]);
watch(
  () => props.assetsDelete,
  (val) => {
    oNcolumns[6].btnList[0].hide = !val;
  }
);
// 添加
function systemObject() {
  dialogRef.value.open();
  searchState.data.searchValue = "";
  state.multipleSelection.value = [];
  getBusinessLeftList({ assetsId: props.idName }).then((res) => {
    searchState.formList[0].options = res.data.businessLeftList.map((item) => {
      return {
        value: item.id,
        label: item.ip + " " + item.port + " " + item.serviceAgreementName,
      };
    });
    searchState.data.businessId = res.data.businessLeftList[0].id;
    setTimeout(() => {
      search();
    }, 200);
  });
}
//搜索相关

let searchState = reactive({
  data: {
    businessId: "",
    searchValue: "",
  },
  menuData: [],
  formList: [
    {
      formType: "select",
      prop: "businessId",
      label: "业务系统对象",
      multiple: false,
      options: [],
      clearable: false,
    },
    {
      formType: "input",
      prop: "searchValue",
      label: "主机名称",
    },
  ],
});
// 搜索按钮
function search(initPageNum = true) {
  tableding.value.reload(searchState.data, initPageNum);
}
// 重置按钮
function reset(initPageNum = true) {
  searchState.data = {
    ...searchState.data,
    searchValue: "",
    pageNum: 1,
    pageSize: 10,
  };
  tableding.value.reload(searchState.data, initPageNum);
}
// 列表配置项
let columns = reactive([
  {
    prop: "hostName",
    label: "主机名称",
  },
  {
    prop: "ip",
    label: "局域网IP",
  },
  {
    prop: "port",
    label: "局域网端口",
  },

  {
    prop: "serviceAgreementName",
    label: "服务协议",
  },
]);
// // 多选数据
let state = reactive({ multipleSelection: [] });
function handleSelectionChange(val) {
  state.multipleSelection = val;
}
// 弹框确定按钮
function submitDialog(close, load) {
  let ids = state.multipleSelection.map((item) => {
    return item["id"];
  });
  load();
  // 关联业务系统资产
  saveRalationResources({ resourcesId: ids.join(","), businessId: searchState.data.businessId })
    .then((res) => {
      dialogRef.value.close();
      emits("update");
      getselectRalation();
      close();
    })
    .catch(() => {
      close(false);
    });
}
// 取消关联业务系统资产
function delFn(val) {
  let ida = val.id;

  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteRalationResource({ id: ida }).then((res) => {
      ElMessage({
        message: "删除成功",
        type: "success",
      });
      emits("update");
      getselectRalation();
    });
  });
}
// 查询已关联计算设备数量
let total = ref("");
let vulnList = {
  id: props.idName,
  pageSize: 10,
  pageNum: 1,
};
//获取关联业务系统对象总数
function getselectRalation(params) {
  selectRalationResourcesList(vulnList).then((res) => {
    total.value = res.data.total;
  });
}
getselectRalation();
// 点击数量跳转
function jump() {
  let params = {
    id: props.idName,
    assetType: route.params.type,
  };
  router.push({ name: "JumpService", params });
}
</script>
<style scoped lang="scss">
.inDiv {
  display: flex;
  justify-content: space-between;
}
.ass {
  display: flex;
  p {
    margin-right: 10px;
    margin-top: 2px;
  }
  el-button {
    margin-right: 10px;
  }
}
</style>
