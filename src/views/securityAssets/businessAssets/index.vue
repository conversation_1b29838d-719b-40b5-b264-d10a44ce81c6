<template>
  <div style="display: flex">
    <el-card style="width: 100%">
      <h3 v-if="!isDialog" class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
      <statistics
        v-if="!isDialog || isDialogType == 'associated'"
        :border-line="isDialogType == 'associated' ? false : true"
        :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '业务系统资产总数' }]"
      ></statistics>
      <common-search
        v-model="searchState.data"
        :menu-data="searchState.menuData"
        :hide-options="props.isDialog"
        :form-list="searchState.formList"
        @search="search"
        @reset="reset"
        label-width="140px"
      >
        <template v-if="!isDialog">
          <el-button @click="addAsset" class="search-button" v-hasPermi="'assets:add'">
            <el-icon :size="12">
              <plus />
            </el-icon>
            添加资产
          </el-button>
          <xel-upload-dialog
            v-hasPermi="'assets:add'"
            size="70px"
            class="button"
            exportUrl="/system/assetsBusiness/downloadAssetsBusinessTemplate"
            importUrl="/system/assetsBusiness/uploadBusiness"
            :data="uploadParams"
            @click="resetCanCover"
            @updateData="search"
          >
            <el-checkbox v-model="canCover">是否覆盖资产数据</el-checkbox>
          </xel-upload-dialog>
          <el-button v-if="false" @click="showTimeline" class="search-button">
            <el-icon :size="12">
              <refresh />
            </el-icon>
            资产状态流
          </el-button>
        </template>
      </common-search>

      <xel-table
        ref="tableRef"
        :columns="isDialogType != 'associated' ? columns : columnsEvent"
        :load-data="getTableData"
        :defaultParams="queryParams || params || offQuery"
        @selection-change="handleSelectionChange"
      >
      </xel-table>
      <!-- 弹窗内容 -->
      <xel-dialog :title="dialogTitle" ref="dialogRef" size="small" @submit="submitForm" @close="closeDialog">
        <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
          <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
        </el-form>
      </xel-dialog>
      <!-- 详情弹框 -->
      <detail-dialog ref="detailDialogRef" :list="detailList" :data="detailInfo"></detail-dialog>
    </el-card>
    <el-card style="width: 10%; margin-left: 30px" v-if="timeDisplay"><timeline :activities="activities" ref="timelineRef"></timeline></el-card>
  </div>
</template>
<script>
export default {
  name: "BusinessAssets",
};
</script>
<script setup>
import { getRole as getDetail, addRole as addItem, updateRole as updateItem, delRole as delItem } from "@/api/system/role";
import { queryList as getTableData } from "@/api/securityAssets/business";
import { getCanSelectPersonByDept } from "@/api/securityAssets/business";
import formatterIP from "@/utils/formatterIP";
import { ref, reactive, toRefs, nextTick, computed, onActivated } from "vue";
let firstIn = true;
onMounted(() => {
  setTimeout(() => {
    firstIn = false;
  }, 100);
});
onActivated(() => {
  if (!firstIn) {
    search(false);
  }
});
import { useRouter, useRoute } from "vue-router";
//导入相关
import uploadCover from "@/views/securityAssets/mixins/uploadCover";
let { canCover, uploadParams, resetCanCover } = uploadCover();

const router = useRouter();
const route = useRoute();
let props = defineProps({
  isDialog: {
    type: Boolean,
    default: false,
  },
  currentId: {
    type: String,
    default: "",
  },
  isDialogType: {
    type: String,
    default: "switch",
  },
  assetsInfos: {
    type: String,
    default: "",
  },
  params: {
    type: [Object, null],
    default: () => {
      return null;
    },
  },
  addEvent: {
    type: String,
    default: "",
  },
});

let emits = defineEmits(["submit", "connect"]);

let dialogText = ref("字典类型");
let idKey = "roleId";
let tableRef = ref();
let timelineRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    level: "",
    name: "",
    domain: "",
    createName: "",
    createTimeStr: "",
    portService: "",
    ips: "",
    ports: "",
    assetsPerson: "",
    deptId: "",
    highAssets: "",
    dayCount: "",
  },
  menuData: [
    {
      lable: "等级保护级别",
      prop: "level",
      options: [],
      dictName: "grade_protection_level",
    },
    {
      lable: "资产危险级别",
      prop: "highAssets",
      options: [{ value: 1, label: "中高危" }],
    },
    {
      lable: "资产危险发现时间",
      prop: "dayCount",
      options: [],
      dictName: "vuln_findTime",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "业务系统名称",
    },
    {
      formType: "input",
      prop: "domain",
      label: "系统入口",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "date",
      type: "datetimerange",
      prop: "createTimeStr",
      label: "创建日期",
    },
    {
      formType: "select",
      prop: "portService",
      filterable: true,

      label: "服务协议",
      dictName: "service_agreement",
    },
    {
      formType: "input",
      prop: "ips",
      label: "IP",
    },
    {
      formType: "input",
      prop: "ports",
      label: "端口号",
    },
    {
      formType: "input",
      prop: "assetsPerson",
      label: "责任人",
    },
    {
      formType: "deptTree",
      prop: "deptId",
      label: "责任主体",
      multiple: false,
      onCurrentChange(val) {
        getCanSelectPersonByDeptFn(val);
      },
    },
  ],
});
let queryParams = route.query.priority ? { highAssets: route.query.priority, dayCount: 6 } : null;

/* 新增参数 - 划分综合服务 */
/* { spare1: addEvent !== '' ? '' : route.params.id, assetsInfos: assetsInfos } */
let offQuery =
  route.name == "OffAddEvent" || route.name == "OffEventDetail"
    ? { spare2: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos }
    : { spare1: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos };

getQueryPriority();
//获取路由中的query参数
function getQueryPriority() {
  if (!searchState.data.highAssets && route.query.priority) {
    searchState.data.highAssets = route.query.priority;
    searchState.data.dayCount = 6;
    let _location = window.location.href.split("?")[0];
    window.history.replaceState({}, "", _location);
  }
}
// 搜索方法
const globalSearchObj = ref({
  globalSearch: null,
}); //全局搜索
function search(initPageNum = true, data = {}, isGlobalSearch = true) {
  if (isGlobalSearch) {
    if (data.globalSearch) {
      globalSearchObj.value = data;
    }
  } else {
    globalSearchObj.value = {};
  }
  let params = { ...searchState.data, beginTimeStr: "", endTimeStr: "", ...globalSearchObj.value };

  if (searchState.data.createTimeStr && searchState.data.createTimeStr.length > 0) {
    params.beginTimeStr = searchState.data.createTimeStr[0];
    params.endTimeStr = searchState.data.createTimeStr[1];
  }
  params.assetsInfos = props.assetsInfos;

  delete params.createTimeStr;
  setTimeout(() => {
    tableRef.value.reload(params, initPageNum);
  }, 500);
}
// 重置方法
function reset() {
  searchState.data = {
    level: "",
    name: "",
    domain: "",
    createName: "",
    createTimeStr: "",
    portService: "",
    ips: "",
    ports: "",
    assetsPerson: "",
    deptId: "",
    highAssets: "",
    dayCount: "",
  };
  search();
}
//事件流
let activities = reactive([
  {
    content: "Event start",
    timestamp: "2018-04-15",
  },
  {
    content: "Approved",
    timestamp: "2018-04-13",
  },
  {
    content: "Success",
    timestamp: "2018-04-11",
  },
]);

let state = reactive({
  formData: {}, //新增编辑表单
  multipleSelection: [],
});
let { formData } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  state.formData = {
    name: "",
  };
}

let columnWidth = {
  domains: "",
};
if (!props.isDialog) {
  let sizeWidth = {
    S: "480",
    M: "580",
    L: "780",
  };
  columnWidth.domains = sizeWidth[$globalWindowSize];
}
// 列表配置项
const columns = [
  {
    prop: "name",
    label: "业务系统名称",
    sortable: "custom",
    click({ row }) {
      !props.isDialog && router.push({ name: "AssetDetails", params: { id: row.id, type: 0 } });
    },
  },
  {
    prop: "domains",
    label: "系统入口",
    sortable: "custom",
    formatter: formatterIP,
    width: columnWidth.domains,
  },
  {
    prop: "relevantResourcesCount",
    label: "相关计算设备对象",
  },
  {
    prop: "relevantVulnCount",
    label: "相关待整改中高危漏洞",
  },
  {
    prop: "relevantEventCount",
    label: "相关待处置安全事件",
  },
  {
    hide: !props.isDialog,
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: props.isDialogType != "switch",
        icon: "switch",
        disabledId: props.currentId,
        title: "转移",
        onClick(scope) {
          emits("submit", scope.row.id);
        },
      },
      {
        hide: props.isDialogType != "associated",
        icon: "Connection",
        title: "关联",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "bussiness",
          };
          emits("connect", data);
        },
      },
      {
        hide: props.isDialogType != "workbench",
        isFont: "icon-hebingdanyuange",
        title: "合并",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "bussiness",
          };
          emits("connect", data);
        },
      },
    ],
  },
];
// 事件资产配置项
const columnsEvent = [
  {
    prop: "name",
    label: "业务系统名称",
    sortable: "custom",
    click({ row }) {
      !props.isDialog && router.push({ name: "AssetDetails", params: { id: row.id, type: 0 } });
    },
  },
  {
    prop: "domains",
    label: "系统入口",
  },
  {
    prop: "ips",
    label: "服务IP",
  },
  {
    prop: "ports",
    label: "服务端口",
  },
  {
    hide: !props.isDialog,
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: props.isDialogType != "associated",
        icon: "Connection",
        title: "关联",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "bussiness",
          };
          emits("connect", data);
        },
      },
    ],
  },
];
// 添加资产按钮
function addAsset() {
  router.push({ name: "AddAsset", query: { type: 0 } });
}
// 列表操作方法
// 新增按钮
function newlyAdded() {
  dialogTitle.value = "添加" + dialogText.value;
  resetFormData();
  popupBox();
}
let editId = ref("");
// 修改按钮
function modifyButton(id) {
  editId.value = id;
  getDetail(id).then(({ data }) => {
    for (let key in data) {
      state.formData[key] = data[key];
    }
    popupBox();
  });
}

// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "字典类型",
    required: true,
  },
  {
    formType: "radio",
    prop: "resource",
    label: "状态",
    required: true,
    options: [],
    dictName: "sys_normal_disable",
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let addFn = editId.value ? updateItem : addItem;
      addFn(state.formData)
        .then((res) => {
          search(false);
          close();
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
//列表重置
function closeDialog() {
  resetFormData();
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
  ruleFormRef.value.resetFields();
}

//详情相关
let detailDialogRef = ref();
let detailInfo = reactive({});
let detailList = [
  {
    prop: "name",
    label: "名称",
  },
  {
    prop: "jobMessage",
    label: "日志信息",
    width: "100%", //可以自定义宽度
  },
];
//获取责任人
function getCanSelectPersonByDeptFn(info) {
  getCanSelectPersonByDept({ deptId: info.id }).then(({ data }) => {
    let selectPersonItem = formList.find((item) => item.prop == "assetsPerson");
    if (selectPersonItem) {
      selectPersonItem.options = data.userList;
    }
  });
}

defineExpose({
  getListData: search,
});
</script>
<style scoped lang="scss">
.button {
  margin-right: 10px;
}
</style>
