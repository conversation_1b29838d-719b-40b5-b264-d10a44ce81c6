<template>
  <div style="display: flex">
    <el-card style="width: 100%">
      <h3 v-if="!isDialog" class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
      <statistics
        v-if="!isDialog || isDialogType == 'associated'"
        :border-line="isDialogType == 'associated' ? false : true"
        :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '终端资产对象总数' }]"
      ></statistics>
      <common-search
        v-model="searchState.data"
        :menu-data="searchState.menuData"
        :form-list="searchState.formList"
        :hide-options="props.isDialog"
        @search="search"
        @reset="reset"
        label-width="120px"
      >
        <template v-if="!isDialog">
          <el-button @click="addAsset" class="search-button" v-hasPermi="'assets:add'">
            <el-icon :size="12">
              <plus />
            </el-icon>
            添加资产
          </el-button>
          <xel-upload-dialog
            v-hasPermi="'assets:add'"
            size="70px"
            class="button"
            exportUrl="/system/assetsTerminal/downloadAssetsTerminalTemplate"
            importUrl="/system/assetsTerminal/uploadTerminal"
            :data="uploadParams"
            @click="resetCanCover"
            @updateData="search"
          >
            <el-checkbox v-model="canCover">是否覆盖资产数据</el-checkbox>
          </xel-upload-dialog>
          <el-button v-if="false" @click="showTimeline" class="search-button">
            <el-icon :size="12">
              <refresh />
            </el-icon>
            资产状态流
          </el-button>
        </template>
      </common-search>
      <xel-table
        ref="tableRef"
        :columns="isDialogType != 'associated' ? columns : columnsEvent"
        :load-data="getTableData"
        @selection-change="handleSelectionChange"
        :defaultParams="params || offQuery"
      >
      </xel-table>
    </el-card>
  </div>
</template>
<script>
export default {
  name: "Terminal",
};
</script>
<script setup>
import { getTerminallist as getTableData } from "@/api/securityAssets/terminal";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { timeDisplay, showTimeline, closeTimeline } from "@/utils/timelineDisplay.js";
import formatterIP from "@/utils/formatterIP";
import { batchDelete } from "@/utils/delete";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
//导入相关
import uploadCover from "@/views/securityAssets/mixins/uploadCover";
let { canCover, uploadParams, resetCanCover } = uploadCover();

let props = defineProps({
  isDialog: {
    type: Boolean,
    default: false,
  },
  currentId: {
    type: String,
    default: "",
  },
  isDialogType: {
    type: String,
    default: "switch",
  },
  assetsInfos: {
    type: String,
    default: "",
  },
  params: {
    type: [Object, null],
    default: () => {
      return null;
    },
  },
  addEvent: {
    type: String,
    default: "",
  },
});

let emits = defineEmits(["submit"]);

/* 新增参数 - 划分综合服务 */
/* { spare1: addEvent !== '' ? '' : route.params.id, assetsInfos: assetsInfos } */
let offQuery =
  route.name == "OffAddEvent" || route.name == "OffEventDetail"
    ? { spare2: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos }
    : { spare1: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos };

let idKey = "roleId";
let tableRef = ref();
let timelineRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    name: "",
    hostName: "",
    createName: "",
    createTime: [],
    ips: "",
    deptId: "",
    assetsPerson: [],
  },
  menuData: [
    {
      lable: "等级保护级别：",
      prop: "level",
      options: [],
      dictName: "grade_protection_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "终端资产名称",
    },
    {
      formType: "input",
      prop: "hostName",
      label: "主机名称",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "date",
      type: "datetimerange",
      prop: "createTime",
      label: "创建时间",
    },
    {
      formType: "input",
      prop: "ips",
      label: "IP",
    },

    {
      formType: "deptTree",
      prop: "deptId",
      label: "责任主体",
      multiple: false,
      onCurrentChange(val) {
        getCanSelectPersonByDeptFn(val);
      },
    },

    {
      formType: "input",
      prop: "assetsPerson",
      label: "责任人",
    },
  ],
});
//搜索按钮方法
const globalSearchObj = ref({
  globalSearch: null,
}); //全局搜索
function search(initPageNum = true, data = {}, isGlobalSearch = true) {
  if (!tableRef.value) return;
  if (isGlobalSearch) {
    if (data.globalSearch) {
      globalSearchObj.value = data;
    }
  } else {
    globalSearchObj.value = {};
  }
  searchState.data.createTime;
  let params = {
    ...searchState.data,
    ...globalSearchObj.value,
    beginTimeStr: searchState.data.createTime && searchState.data.createTime.length > 0 ? searchState.data.createTime[0] : "",
    endTimeStr: searchState.data.createTime && searchState.data.createTime.length > 0 ? searchState.data.createTime[1] : "",
  };

  delete params.createTime;
  params.assetsInfos = props.assetsInfos;
  setTimeout(() => {
    tableRef.value.reload(params, initPageNum);
  }, 500);
}
// 重置按钮方法
function reset() {
  searchState.data = {
    name: "",
    hostName: "",
    createName: "",
    createTime: [],
    ips: "",
    deptId: "",
    assetsPerson: [],
  };
  search();
}
//时间流
let activities = reactive([
  {
    content: "Event start",
    timestamp: "2018-04-15",
  },
  {
    content: "Approved",
    timestamp: "2018-04-13",
  },
  {
    content: "Success",
    timestamp: "2018-04-11",
  },
]);

// 列表配置项
const columns = [
  {
    prop: "name",
    label: "终端资产名称",
    click(scope) {
      !props.isDialog &&
        router.push({
          name: "AssetDetails",
          params: {
            id: scope.row.id,
            type: 2,
          },
        });
    },
  },
  {
    prop: "hostName",
    label: "主机名称",
    sortable: "custom",
  },
  {
    prop: "ips",
    label: "IP地址",
    formatter: formatterIP,
    width: props.isDialog ? "" : $globalWindowSize == "S" ? 350 : $globalWindowSize == "M" ? 500 : 600,
  },
  {
    prop: "relevantEventCount",
    label: "相关待处置安全事件",
    width: props.isDialog ? "" : $globalWindowSize == "S" ? 180 : $globalWindowSize == "M" ? 210 : 300,
  },

  {
    hide: !props.isDialog,

    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: props.isDialogType != "switch",
        icon: "switch",
        disabledId: props.currentId,
        title: "转移",
        onClick(scope) {
          emits("submit", scope.row.id);
        },
      },
      {
        hide: props.isDialogType != "associated",
        icon: "Connection",
        title: "关联",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "bussiness",
          };
          emits("connect", data);
        },
      },
      {
        hide: props.isDialogType != "workbench",
        isFont: "icon-hebingdanyuange",
        title: "合并",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "terminal",
          };
          emits("connect", data);
        },
      },
    ],
  },
];
// 事件资产列表配置项
const columnsEvent = [
  {
    prop: "name",
    label: "终端资产名称",
    click(scope) {
      !props.isDialog &&
        router.push({
          name: "AssetDetails",
          params: {
            id: scope.row.id,
            type: 2,
          },
        });
    },
  },
  {
    prop: "hostName",
    label: "主机名称",
    sortable: "custom",
  },
  {
    prop: "ips",
    label: "IP地址",
    formatter: formatterIP,
  },

  {
    hide: !props.isDialog,

    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnsWidth: 64,
    btnList: [
      {
        hide: props.isDialogType != "associated",
        icon: "Connection",
        title: "关联",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "bussiness",
          };
          emits("connect", data);
        },
      },
    ],
  },
];
// 添加资产按钮
function addAsset() {
  router.push({ name: "AddAsset", query: { type: 2 } });
}

//获取责任人
function getCanSelectPersonByDeptFn(info) {
  // getCanSelectPersonByDept({ deptId: info.id }).then(({ data }) => {
  //   let selectPersonItem = formList.find((item) => item.prop == "assetsPerson");
  //   if (selectPersonItem) {
  //     selectPersonItem.options = data.userList;
  //   }
  // });
}
defineExpose({
  getListData: search,
});
</script>
<style scoped lang="scss">
.button {
  margin-right: 10px;
}
</style>
