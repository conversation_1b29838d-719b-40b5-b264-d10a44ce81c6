<template>
  <div style="display: flex">
    <el-card style="width: 100%">
      <h3 v-if="!isDialog" class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
      <statistics
        v-if="!isDialog || isDialogType == 'associated'"
        :border-line="isDialogType == 'associated' ? false : true"
        :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '计算设备资产总数' }]"
      ></statistics>
      <common-search
        v-model="searchState.data"
        :menu-data="searchState.menuData"
        :form-list="searchState.formList"
        :hide-options="props.isDialog"
        @search="search"
        @reset="reset"
        label-width="140px"
      >
        <template v-if="searchState.data.softwareType" #form>
          <xel-form-item prop="softwareValue" label="基础资源软件" form-type="no">
            <load-select
              :load-data="selectSoftListByPage"
              :params="{ parentId: searchState.data.softwareType }"
              v-model="searchState.data.softwareValue"
              placeholder="请输入基础资源软件"
              style="width: 100%"
            ></load-select>
          </xel-form-item>
          <xel-form-item v-model="searchState.data.softwareEdition" formType="input" prop="softwareEdition" label="基础软件版本"></xel-form-item>
        </template>
        <template v-if="!isDialog">
          <el-button @click="addAsset" class="search-button" v-hasPermi="'assets:add'">
            <el-icon :size="12">
              <plus />
            </el-icon>
            添加资产
          </el-button>
          <xel-upload-dialog
            v-hasPermi="'assets:add'"
            size="70px"
            class="button"
            exportUrl="/system/assetsBasic/downloadTemplate"
            importUrl="/system/assetsBasic/uploadResource"
            :data="uploadParams"
            @click="resetCanCover"
            :key="$route.name"
            @updateData="search"
          >
            <el-checkbox v-model="canCover">是否覆盖资产数据</el-checkbox>
          </xel-upload-dialog>
          <el-button v-if="false" @click="showTimeline" class="search-button">
            <el-icon :size="12">
              <refresh />
            </el-icon>
            资产状态流
          </el-button>
        </template>
      </common-search>
      <xel-table
        ref="tableRef"
        :columns="isDialogType != 'associated' ? columns : columnsEvent"
        :load-data="getTableData"
        @selection-change="handleSelectionChange"
        :defaultParams="queryParams || params || offQuery"
      >
      </xel-table>
    </el-card>
  </div>
</template>
<script>
export default {
  name: "Underlying",
};
</script>
<script setup>
import { getRole as getDetail, addRole as addItem, updateRole as updateItem, delRole as delItem } from "@/api/system/role";
import { getCanSelectPersonByDept } from "@/api/securityAssets/business";
// import { selectSoftType } from "@/api/securityAssets/assetsList";
import { getAssetsResourcesList as getTableData, selectSoftTypeListByPage, selectSoftListByPage } from "@/api/securityAssets/assetsList";
import { ElMessageBox, ElMessage } from "element-plus";
import formatterIP from "@/utils/formatterIP";
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
let firstIn = true;
onMounted(() => {
  setTimeout(() => {
    firstIn = false;
  }, 100);
});
onActivated(() => {
  if (!firstIn) {
    search(false);
  }
});
import { timeDisplay, showTimeline, closeTimeline } from "@/utils/timelineDisplay.js";
import { batchDelete } from "@/utils/delete";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
//导入相关
import uploadCover from "@/views/securityAssets/mixins/uploadCover";
let { canCover, uploadParams, resetCanCover } = uploadCover();

let props = defineProps({
  isDialog: {
    type: Boolean,
    default: false,
  },
  currentId: {
    type: String,
    default: "",
  },
  isDialogType: {
    type: String,
    default: "switch",
  },
  assetsInfos: {
    type: String,
    default: "",
  },
  params: {
    type: [Object, null],
    default: () => {
      return null;
    },
  },
  addEvent: {
    type: String,
    default: "",
  },
});

let emits = defineEmits(["submit"]);

let idKey = "roleId";
let tableRef = ref();
let timelineRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    level: "",
    name: "",
    hostName: "",
    createTimeStr: "",
    createName: "",
    ips: "",
    deptId: "",
    ports: "",
    assetsPerson: "",
    softwareEdition: "",
    softwareValue: "",
    assembly: "",
    softwareType: "", //软件类别id
    highAssets: "",
    dayCount: "",
  },
  menuData: [
    {
      lable: "等级保护级别",
      prop: "level",
      options: [],
      dictName: "grade_protection_level",
    },
    {
      lable: "资产危险级别",
      prop: "highAssets",
      options: [{ value: 1, label: "中高危" }],
    },
    {
      lable: "资产危险发现时间",
      prop: "dayCount",
      options: [],
      dictName: "vuln_findTime",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "计算设备资产名称",
    },
    {
      formType: "input",
      prop: "hostName",
      label: "主机名称",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "date",
      type: "datetimerange",
      prop: "createTimeStr",
      label: "创建日期",
    },
    {
      formType: "input",
      prop: "ips",
      label: "IP",
    },
    {
      formType: "deptTree",
      prop: "deptId",
      label: "责任主体",
      multiple: false,
      onCurrentChange(val) {
        getCanSelectPersonByDeptFn(val);
      },
    },
    {
      formType: "select",
      prop: "portService",
      label: "服务协议",
      filterable: true,

      dictName: "service_agreement",
    },
    {
      formType: "input",
      prop: "ports",
      label: "端口号",
    },
    {
      formType: "input",
      prop: "assetsPerson",
      label: "责任人",
    },
    {
      formType: "input",
      prop: "assembly",
      label: "基础软件组件",
    },
    {
      formType: "select",
      prop: "softwareType",
      label: "基础软件类别",
      multiple: false,
      filterable: true,
      // 调字典接口
      seleteCode: {
        code: selectSoftTypeListByPage,
        resKey: "softTypeList",
        // 传递取值的字段名
        label: "name",
        value: "id",
        params: {},
      },
      onChange(val) {
        searchState.formList[9].isshow = true;
        searchState.data.softwareValue = "";
        // basicSoftwareCategory(val);
      },
    },
  ],
});

let queryParams = route.query.priority ? { highAssets: route.query.priority, dayCount: 6 } : null;

/* 新增参数 - 划分综合服务 */
/* { spare1: addEvent !== '' ? '' : route.params.id, assetsInfos: assetsInfos } */
let offQuery =
  route.name == "OffAddEvent" || route.name == "OffEventDetail"
    ? { spare2: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos }
    : { spare1: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos };

getQueryPriority();
//获取路由中的query参数
function getQueryPriority() {
  if (!searchState.data.highAssets && route.query.priority) {
    searchState.data.highAssets = route.query.priority;
    searchState.data.dayCount = 6;

    let _location = window.location.href.split("?")[0];
    window.history.replaceState({}, "", _location);
  }
}

//搜索按钮方法
const globalSearchObj = ref({
  globalSearch: null,
}); //全局搜索
function search(initPageNum = true, data = {}, isGlobalSearch = true) {
  if (!tableRef.value) return;
  if (isGlobalSearch) {
    if (data.globalSearch) {
      globalSearchObj.value = data;
    }
  } else {
    globalSearchObj.value = {};
  }
  let params = { ...searchState.data, beginTimeStr: "", endTimeStr: "", ...globalSearchObj.value };
  if (searchState.data.createTimeStr && searchState.data.createTimeStr.length > 0) {
    params.beginTimeStr = searchState.data.createTimeStr[0];
    params.endTimeStr = searchState.data.createTimeStr[1];
  }
  params.assetsInfos = props.assetsInfos;
  delete params.createTimeStr;
  setTimeout(() => {
    tableRef.value.reload(params, initPageNum);
  }, 500);
}
// 重置按钮方法
function reset() {
  searchState.data = {
    level: "",
    name: "",
    hostName: "",
    createTimeStr: "",
    createName: "",
    ips: "",
    deptId: "",
    ports: "",
    assetsPerson: "",
    softwareEdition: "",
    softwareValue: "",
    assembly: "",
    softwareType: "", //软件类别id
    highAssets: "",
    dayCount: "",
  };
  search();
}
//时间流
let activities = reactive([
  {
    content: "Event start",
    timestamp: "2018-04-15",
  },
  {
    content: "Approved",
    timestamp: "2018-04-13",
  },
  {
    content: "Success",
    timestamp: "2018-04-11",
  },
]);

let columnNumWidth = "";

if (!props.isDialog) {
  columnNumWidth = $globalWindowSize == "S" ? "140" : $globalWindowSize == "M" ? "170" : "210";
}

// 列表配置项
const columns = [
  {
    prop: "name",
    label: "计算设备资产名称",

    click(scope) {
      !props.isDialog &&
        router.push({
          name: "AssetDetails",
          params: {
            id: scope.row.id,
            type: 1,
          },
        });
    },
  },
  {
    prop: "hostName",
    label: "主机名称",
    sortable: "custom",
  },
  {
    prop: "ips",
    label: "IP地址",
    formatter: formatterIP,
  },
  {
    prop: "relevantBusinessCount",
    label: "相关业务系统对象",
    width: columnNumWidth,
  },
  {
    prop: "relevantVulnCount",
    label: "相关待整改中高危漏洞",
    width: columnNumWidth,
  },
  {
    prop: "relevantEventCount",
    label: "相关待处置安全事件",
    width: columnNumWidth,
  },
  {
    hide: !props.isDialog,

    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        hide: props.isDialogType != "switch",
        icon: "switch",
        disabledId: props.currentId,
        title: "转移",
        onClick(scope) {
          emits("submit", scope.row.id);
        },
      },
      {
        hide: props.isDialogType != "associated",
        icon: "Connection",
        title: "关联",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "basic",
          };
          emits("connect", data);
        },
      },
      {
        hide: props.isDialogType != "workbench",
        isFont: "icon-hebingdanyuange",
        title: "合并",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "basic",
          };
          emits("connect", data);
        },
      },
    ],
  },
];
// 事件资产配置项
const columnsEvent = [
  {
    prop: "name",
    label: "计算设备对象",
    click(scope) {
      !props.isDialog &&
        router.push({
          name: "AssetDetails",
          params: {
            id: scope.row.id,
            type: 1,
          },
        });
    },
  },
  {
    prop: "hostName",
    label: "主机名称",
    sortable: "custom",
  },
  {
    prop: "ips",
    label: "局域网IP",
    formatter: formatterIP,
  },
  {
    prop: "ports",
    label: "局域网端口",
  },
  {
    hide: !props.isDialog,

    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnsWidth: 64,
    btnList: [
      {
        hide: props.isDialogType != "associated",
        icon: "Connection",
        title: "关联",
        onClick(scope) {
          let data = {
            id: scope.row.id,
            type: "basic",
          };
          emits("connect", data);
        },
      },
    ],
  },
];
// 添加资产按钮
function addAsset() {
  router.push({ name: "AddAsset", query: { type: 1 } });
}
// 列表操作方法
// 弹框
let ruleFormRef = ref();
// 打开弹框

//获取责任人
function getCanSelectPersonByDeptFn(info) {
  // getCanSelectPersonByDept({ deptId: info.id }).then(({ data }) => {
  //   let selectPersonItem = formList.find((item) => item.prop == "assetsPerson");
  //   if (selectPersonItem) {
  //     selectPersonItem.options = data.userList;
  //   }
  // });
}
// 获取
// function basicSoftwareCategory(val) {
//   // selectSoftType().then((res) => {
//   //
//   //
//   // });
// }
defineExpose({
  getListData: search,
});
</script>
<style scoped lang="scss">
.button {
  margin-right: 10px;
}
</style>
