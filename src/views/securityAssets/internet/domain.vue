<template>
  <el-card>
    <el-row class="sime-layout-wrapper">
      <el-col :span="7">
        <div class="searchTab">
          <el-input :prefix-icon="Search" v-model="searchInfo" placeholder="请输入域名组查询条件" @keyup.enter="getIpGroupData({ name: searchInfo })">
            <template #prefix>
              <el-icon class="el-input__icon"><search /></el-icon> </template
          ></el-input>
        </div>
        <el-tree :data="domain_data" :props="defaultProps" :highlight-current="true" @node-click="handleNodeClick" />
      </el-col>
      <el-col :span="1" class="tree-wrapper"> </el-col>
      <el-col :span="15">
        <common-search
          v-model="searchState.data"
          :two="$globalWindowSize != 'L'"
          :menu-data="searchState.menuData"
          :form-list="searchState.formList"
          @search="search"
          @reset="reset"
        >
        </common-search>
        <xel-table ref="tableRef" resKey="rows" :columns="columns" :load-data="getTable">
          <template #time="scope"> {{ scope.row.beginTime }}--{{ scope.row.endTime }}</template>
        </xel-table>
      </el-col>
    </el-row>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
onActivated(() => {
  search(false);
});
import { ElMessage } from "element-plus";
import { selectDomainGroups as getIpGroup, selectDomainByGroup as getTable } from "@/api/securityAssets/internet";
getIpGroupData();
// 获取ip数组
function getIpGroupData(data) {
  getIpGroup(data)
    .then((res) => {
      state.domain_data = res.data;
    })
    .catch(() => {});
}
// 点击域名，请求列表,
function handleNodeClick(row) {
  searchState.data.groupId = row.id;
  if (row.name == "未分组") {
    searchState.data.groupId = "notGroup";
  }
  tableRef.value.reload({ ...searchState.data });
}
// 树的配置
let defaultProps = reactive({
  children: "children",
  label: "name",
  id: "id",
});
let tableRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    groupId: "",
    domain: "",
    domainName: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "domain",
      label: "域名",
    },
    {
      formType: "input",
      prop: "domainName",
      label: "网站名",
    },
  ],
});
function search(initPageNum = true) {
  nextTick(() => {
    tableRef.value.reload(searchState.data, initPageNum);
  });
}
function reset() {
  searchState.data = {
    groupId: searchState.data.groupId,
    domain: "",
    domainName: "",
  };
  tableRef.value.reload(searchState.data);
}
let columns = [
  {
    prop: "domain",
    label: "域名",
  },
  {
    prop: "domainName",
    label: "网站名",
  },
  {
    label: "监控时间",
    slotName: "time",
  },
];
let state = reactive({
  searchInfo: "",
  domain_data: [],
});

let { domain_data, searchInfo } = toRefs(state);
</script>

<style lang="scss" scoped>
.sime-layout-wrapper {
  height: max-content;
}
.tree-wrapper {
  position: relative;
  min-height: 100%;
  &::after {
    content: "";
    display: block;
    width: 1px;
    height: 100%;
    position: absolute;
    right: 50%;
    top: 0;
    bottom: 0;
    background: #ededed;
  }
}
.collapse-title {
  flex: 1 0 90%;
  order: 1;
}

.el-collapse-item__header {
  flex: 1 0 auto;
  order: -1;
}
.searchTab {
  width: 100%;
  margin-bottom: 20px;
}
</style>
