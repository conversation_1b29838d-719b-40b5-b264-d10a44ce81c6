<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <statistics :border="true" :list="[{ num: state.count, text: '互联网暴露资产总数' }]"></statistics>
    <div class="clearfix"></div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="互联网暴露面 IP 资产" name="first">
        <IP :key="activeName"></IP>
      </el-tab-pane>
      <el-tab-pane label="互联网域名资产" name="second">
        <Domain :key="activeName"></Domain>
      </el-tab-pane>
    </el-tabs>
    <xel-dialog @addItem="$emit('addNode')"> </xel-dialog>
  </el-card>
</template>
<script>
export default {
  name: "Internet",
};
</script>
<script setup>
import IP from "./ip.vue";
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import Domain from "./domain.vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { selectAssetCount } from "@/api/securityAssets/internet";
const route = useRoute();
let activeName = ref("first");
activeName.value = route.query.tabName ? route.query.tabName : "first";
function handleClick(val) {
  activeName.value = val.paneName;
}
let state = reactive({
  count: 0,
});
// 获取互联网暴露资产总数
function getAllCount() {
  selectAssetCount().then((res) => {
    let data = res.data;
    state.count = parseInt(data.domainCount) + parseInt(data.ipCount) + parseInt(data.portCount);
  });
}
getAllCount();
</script>

<style lang="scss" scoped></style>
