<template>
  <el-card>
    <el-row class="sime-layout-wrapper">
      <el-col :span="7">
        <div class="searchTab">
          <el-select v-model="searchType" placeholder="查询条件" @change="changeType">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-input
            clearable
            :prefix-icon="Search"
            v-model="searchInfo"
            placeholder="请输入查询条件"
            @keyup.enter="searchType == '1' ? getIpGroupData({ name: searchInfo }) : getIpsData({ ipStr: searchInfo })"
          >
            <template #prefix>
              <el-icon class="el-input__icon"><search /></el-icon> </template
          ></el-input>
        </div>
        <div v-if="searchType == '1'">
          <el-collapse v-model="activeNames" :key="item.id" v-for="item in ip_group_data" @change="handleChange(item)">
            <el-collapse-item :name="item.id">
              <template v-slot:title>
                <span class="collapse-title">{{ item.name }}</span>
              </template>
              <xel-table
                :defaultParams="{ groupId: item.id, ipStr: searchType == '2' ? searchInfo : '' }"
                resKey="rows"
                ref="treeTableRef"
                :show-header="false"
                :columns="getIpColumns(item.name)"
                :load-data="getTableTree"
                normalPage="true"
                :highlight-current-row="true"
              >
              </xel-table>
            </el-collapse-item>
          </el-collapse>
        </div>

        <ul v-else>
          <li
            v-for="(item, index) in ip_group_data"
            :key="index"
            class="ips"
            :class="{ active: item.ipStr == searchState.data.ipStr }"
            @click="ipList(item.ipStr)"
          >
            {{ item.ipStr }}
          </li>
        </ul>
      </el-col>
      <el-col :span="1" class="tree-wrapper"> </el-col>
      <el-col :span="15">
        <common-search
          v-model="searchState.data"
          :two="$globalWindowSize != 'L'"
          :menu-data="searchState.menuData"
          :form-list="searchState.formList"
          @search="search"
          @reset="reset"
        >
        </common-search>
        <xel-table ref="tableRef" resKey="rows" :columns="columns" :load-data="getTable"> </xel-table>
      </el-col>
    </el-row>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
onActivated(() => {
  search(false);
});

import { ElMessage } from "element-plus";

import {
  selectIpGroups as getIpGroup,
  selectAllIps as getAllIps,
  selectIpByGroup as getTableTree,
  selectPortByIp as getTable,
} from "@/api/securityAssets/internet";
getIpGroupData();
// 获取ip数组
function getIpGroupData(data) {
  getIpGroup(data)
    .then((res) => {
      state.ip_group_data = res.data;
      search();
    })
    .catch(() => {});
}
// 需要后台加一个接口
function getIpsData(data) {
  let params = { ...data };
  getAllIps(params)
    .then((res) => {
      state.ip_group_data = res.data.rows;
      console.log(state);
      search();
    })
    .catch(() => {});
}
// 改变条件时
function changeType() {
  searchState.data.ipStr = "";
  if (state.searchType == "2") {
    getIpsData();
  } else {
    getIpGroupData();
  }
}
// 点击ip组,请求对应ip
let treeTableRef = ref();
function handleChange(row) {
  let data = {
    groupId: row ? row.id : "",
    ipStr: state.searchType === "2" ? state.searchInfo : "",
  };
}
function ipList(val) {
  searchState.data.ipStr = val;
  tableRef.value.reload(searchState.data);
}

function getIpColumns(groupName) {
  let ipColumns = [
    {
      prop: "ipStr",
      label: "ipStr",
      click: (val) => {
        document.querySelectorAll(".el-table__row.current-row").forEach((item) => {
          item.classList.remove("current-row");
        });
        searchState.data.ipStr = val.row.ipStr;
        searchState.data.groupId = groupName == "未分组" ? "notGroup" : "";
        //未分组传groupId：notGroup
        tableRef.value.reload({ ...searchState.data });
      },
    },
  ];

  return ipColumns;
}

let tableRef = ref();
//搜索相关
let searchState = reactive({
  data: {
    groupId: "",
    ipStr: "",
    port: "",
    dev: "",
    os: "",
    module: "",
    riskFlag: "",
    diffType: "",
  },
  menuData: [
    {
      lable: "风险等级：",
      prop: "riskFlag",
      options: [],
      dictName: "risk_level",
    },
    {
      lable: "状态：",
      prop: "diffType",
      options: [],
      dictName: "diff_type ",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "port",
      label: "端口",
    },
    {
      formType: "input",
      prop: "os",
      label: "操作系统",
    },
    {
      formType: "input",
      prop: "module",
      label: "组件",
    },
    {
      formType: "input",
      prop: "dev",
      label: "设备",
    },
  ],
});
function search(initPageNum = true) {
  nextTick(() => {
    tableRef.value.reload(searchState.data, initPageNum);
  });
}
function reset() {
  searchState.data = {
    ipStr: searchState.data.ipStr,
    port: "",
    dev: "",
    os: "",
    module: "",
    riskFlag: "",
    diffType: "",
    groupId: searchState.data.groupId,
  };
  tableRef.value.reload(searchState.data);
}
let columns = [
  {
    prop: "port",
    label: "端口",
  },
  {
    prop: "service",
    label: "服务类型",
  },
  {
    prop: "dev",
    label: "设备",
  },
  {
    prop: "os",
    label: "操作系统",
  },
  {
    prop: "module",
    label: "组件",
  },
  {
    prop: "riskFlagStr",
    label: "风险等级",
  },
  {
    prop: "updateTime",
    label: "更新时间",
  },
  {
    prop: "diffTypeStr",
    label: "状态",
  },
];
let state = reactive({
  searchType: "1", //查询条件
  searchInfo: "",
  ip_group_data: [],
  second_data: [],
  options: [
    { value: "1", label: "组" },
    { value: "2", label: "ip" },
  ],
});

let { ip_group_data, ip_data, options, searchType, searchInfo } = toRefs(state);
</script>

<style lang="scss" scoped>
.sime-layout-wrapper {
  height: max-content;
}
.tree-wrapper {
  position: relative;
  min-height: 100%;
  &::after {
    content: "";
    display: block;
    width: 1px;
    height: 100%;
    position: absolute;
    right: 50%;
    top: 0;
    bottom: 0;
    background: #ededed;
  }
}
.collapse-title {
  flex: 1 0 90%;
  order: 1;
}

.el-collapse-item__header {
  flex: 1 0 auto;
  order: -1;
}
.searchTab {
  width: 100%;
  display: flex;
  margin-bottom: 20px;
  div:first-child {
    margin-right: 8px;
    width: 160px;
  }
}
.ips {
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  border-bottom: 1px solid #ebedf1;
  &.active {
    background: #f5f7fa;
  }
}
</style>
