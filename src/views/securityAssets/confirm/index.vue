<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <statistics :border="true" :list="[{ num: count, text: '待确认资产总数' }]"></statistics>
    <div class="clearfix"></div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="域名URL待确认资产" name="first"><confirmedUrl :key="activeName" @updateTotal="getCount"></confirmedUrl></el-tab-pane>
      <el-tab-pane label="IP类待确认资产" name="second"> <confirmedIP :key="activeName" @updateTotal="getCount"></confirmedIP></el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "Confirm",
};
</script>
<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import ConfirmedIP from "./recognized/confirmedIP.vue";
import ConfirmedUrl from "./recognized/confirmedUrl.vue";
import { selectNotConfirmCount } from "@/api/securityAssets/confirm";
let activeName = ref("first");
let count = ref(0);
function getCount() {
  selectNotConfirmCount().then((res) => {
    count.value = res.data;
  });
}
getCount();
// 获取统计数
</script>
<style scoped lang="scss"></style>
