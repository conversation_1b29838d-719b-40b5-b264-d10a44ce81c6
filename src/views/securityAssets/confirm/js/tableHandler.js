export default function (payload, isDialog) {
  let actionBtns = {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnsWidth: !isDialog ? "" : 64,
    btnList: [
      {
        hide: isDialog,
        hasPermi: "workflow:notConfirm",
        icon: "circleClose",
        title: "忽略",
        onClick({ row }) {
          payload.ipAssets(row.id);
          payload.ignore();
        },
      },
      {
        hide: isDialog,
        hasPermi: "workflow:notConfirm",
        isFont: "icon-hebingdanyuange",
        title: "与现有资产合并",
        onClick({ row }) {
          payload.ipAssets(row.id);
          payload.merge();
        },
      },
      {
        hide: isDialog,
        hasPermi: "workflow:notConfirm",
        icon: "circlePlus",
        title: "新增资产",
        onClick({ row }) {
          payload.add(row.id);
        },
      },
      {
        hide: !isDialog,
        icon: "Connection",
        title: "关联",
        onClick({ row }) {
          payload.connect(row.id, row.ip ? row.ip : "");
        },
      },
    ],
  };

  return { actionBtns };
}
