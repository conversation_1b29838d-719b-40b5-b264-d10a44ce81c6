<template>
  <common-search
    v-model="searchState.data"
    :menu-data="searchState.menuData"
    :form-list="searchState.formList"
    :hide-options="props.isDialog"
    @search="search"
    @reset="reset"
    label-width="120px"
    hideOptions="true"
  >
  </common-search>
  <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange" width="80%"> </xel-table>
  <!-- 待确认资产 -->
  <xel-dialog title="待确认资产" ref="dialogRef" @submit="submitForm" @close="closeDialog" width="80%" buttonDetermine="变更资产">
    <el-row :gutter="70">
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>新增信息</p>
        </div>
        <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini" class="addInfo">
          <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"> </xel-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>摘要</p>
        </div>
        <el-row class="onname">
          <el-col :span="12">
            <p>资产对象类型：终端资产对象</p>
          </el-col>
          <el-col :span="12">
            <p>资产对象名称：{{ store.terminal.name }}</p>
          </el-col>
          <el-col :span="12">
            <p>等级保护级别：{{ store.terminal.levelName }}</p>
          </el-col>
          <el-col :span="12">
            <p>责任主体：{{ store.terminal.deptName }}</p>
          </el-col>
          <el-col :span="12">
            <p>主机名：{{ store.terminal.hostName }}</p>
          </el-col>
          <el-col :span="12">
            <p>责任人：{{ userNames }}</p>
          </el-col>
        </el-row>
        <div class="title-bottom-line">
          <p>局域网IP</p>
        </div>
        <ip-port :list="store.terminalIps" :is-detail="false" :show-type="false" :showPort="false" width="100%" class="magin-auto"></ip-port>
      </el-col>
    </el-row>
  </xel-dialog>
</template>
<script setup>
import { getTerminallist as getTableData, getTerminalDetail } from "@/api/securityAssets/terminal";
import { ElMessageBox, ElMessage } from "element-plus";
import { openIpNotConfirm, changeIpNotConfirmTerminalAssets, ignoreIpNotConfirmAsset } from "@/api/securityAssets/confirm";
import { ref, reactive, toRefs, nextTick, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
const $globalWindowSize = window.$globalWindowSize;

let props = defineProps({
  isDialog: {
    type: Boolean,
    default: false,
  },
  currentId: {
    type: String,
    default: "",
  },
});

let idKey = "roleId";
let tableRef = ref();
let timelineRef = ref();
let dialogRef = ref();
let ruleFormRef = ref();
let emit = defineEmits(["close"]);

//搜索相关
let searchState = reactive({
  data: {
    name: "",
    hostName: "",
    createName: "",
    createTime: [],
    ips: "",
    deptId: "",
    assetsPerson: [],
  },
  menuData: [
    {
      lable: "等级保护级别：",
      prop: "level",
      options: [],
      dictName: "grade_protection_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "终端资产名称",
    },
    {
      formType: "input",
      prop: "hostName",
      label: "主机名称",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "date",
      type: "datetimerange",
      prop: "createTime",
      label: "创建日期",
    },
    {
      formType: "input",
      prop: "ips",
      label: "IP",
    },

    {
      formType: "deptTree",
      prop: "deptId",
      label: "责任主体",
      multiple: false,
      // onCurrentChange(val) {
      //   getCanSelectPersonByDeptFn(val);
      // },
    },

    {
      formType: "input",
      prop: "assetsPerson",
      label: "责任人",
    },
  ],
});
//搜索按钮方法
function search(initPageNum = true) {
  let prams = {
    ...searchState.data,
    beginTimeStr: searchState.data.createTime && searchState.data.createTime.length > 0 ? searchState.data.createTime[0] : "",
    endTimeStr: searchState.data.createTime && searchState.data.createTime.length > 0 ? searchState.data.createTime[1] : "",
  };
  delete prams.createTime;
  tableRef.value.reload(prams, initPageNum);
}
// 重置按钮方法
function reset() {
  searchState.data = {
    name: "",
    hostName: "",
    createName: "",
    createTime: [],
    ips: "",
    deptId: "",
    assetsPerson: [],
  };
  search();
}

// 列表配置项
const columns = [
  {
    prop: "name",
    label: "终端资产名称",
  },
  {
    prop: "hostName",
    label: "主机名称",
    sortable: "custom",
  },
  {
    prop: "ips",
    label: "IP地址",
  },
  {
    prop: "relevantEventCount",
    label: "相关待处置安全事件",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        isFont: "icon-hebingdanyuange",
        title: "合并",
        onClick(scope) {
          emits(scope.row.id);
        },
      },
    ],
  },
];
let store = reactive({
  terminal: {},
  terminalIps: [],
});
// 添加资产按钮
let onid = ref("");

function emits(val) {
  onid.value = val;
  openIpNotConfirm({ id: props.currentId }).then((res) => {
    formData.ip = res.data.ip;
  });
  // 查看详情
  getTerminalDetail({ id: val }).then((res) => {
    store.terminal = res.data.terminal;
    store.terminalIps = res.data.terminal.terminalIps;
  });
  dialogRef.value.open();
}
let userNames = computed(() => {
  return store.terminal.personList ? store.terminal.personList.map((item) => item.userName).join() : "";
});
// 弹框列表
let formList = reactive([
  {
    formType: "input",
    prop: "ip",
    label: "IP地址",
    disabled: true,
  },
  {
    formType: "input",
    prop: "internetIp",
    label: "主机名称",
    disabled: true,
  },
  {
    formType: "input",
    prop: "lanIp",
    label: "操作系统",
    disabled: true,
  },
  {
    formType: "input",
    prop: "lanIp",
    label: "端口服务",
    disabled: true,
  },
]);
let formData = reactive({
  ip: "",
  internetIp: "",
  domain: "",
});
// 变更资产按钮
function submitForm() {
  ElMessageBox.confirm("确定要变更资产吗？", "信息", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    changeIpNotConfirmTerminalAssets({ id: onid.value, spare1: props.currentId }).then(() => {
      ElMessage({
        message: "操作成功",
        type: "success",
      });
      emit("close");
      dialogRef.value.close();
    });
  });
}
</script>
<style scoped lang="scss">
.button {
  margin-right: 10px;
}
.addInfo {
  :deep .el-form-item__content {
    display: flex;
  }
  .btn {
    margin-left: 20px;
  }
}
.onname {
  p {
    margin: 20px;
  }
}
.magin-auto {
  margin-left: 18px;
}
</style>
