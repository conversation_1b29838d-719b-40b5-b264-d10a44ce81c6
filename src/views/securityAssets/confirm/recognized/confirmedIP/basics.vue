<template>
  <common-search
    v-model="searchState.data"
    :menu-data="searchState.menuData"
    :form-list="searchState.formList"
    :hide-options="props.isDialog"
    @search="search"
    @reset="reset"
    label-width="140px"
    hideOptions="true"
  >
    <template v-if="searchState.data.softwareType" #form>
      <xel-form-item prop="softwareValue" label="基础资源软件" form-type="no">
        <load-select
          :load-data="selectSoftListByPage"
          :params="{ parentId: searchState.data.softwareType }"
          v-model="searchState.data.softwareValue"
          placeholder="请输入基础资源软件"
          style="width: 100%"
        ></load-select>
      </xel-form-item>
      <xel-form-item v-model="searchState.data.softwareEdition" formType="input" prop="softwareEdition" label="基础软件版本"></xel-form-item>
    </template>
  </common-search>
  <xel-table ref="tableRef" :columns="columns" :load-data="getTableData" @selection-change="handleSelectionChange"> </xel-table>
  <!--待确认资产弹框  -->
  <xel-dialog title="待确认资产" ref="dialogRef" @submit="submitForm" @close="closeDialog" width="80%" buttonDetermine="变更资产">
    <el-row :gutter="70">
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>新增信息</p>
        </div>
        <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini" class="addInfo">
          <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item">
            <el-button class="btn" size="small" @click="change(item)" v-if="formData[item.prop]">
              <el-icon :size="12"> <ArrowRightBold /></el-icon>
            </el-button>
          </xel-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>摘要</p>
        </div>
        <el-row class="onname">
          <el-col :span="12">
            <p>资产对象类型：计算设备资产</p>
          </el-col>
          <el-col :span="12">
            <p>资产对象名称：{{ store.abstract.name }}</p>
          </el-col>
          <el-col :span="12">
            <p>等级保护级别：{{ store.abstract.levelName }}</p>
          </el-col>
          <el-col :span="12">
            <p>责任主体：{{ store.abstract.deptName }}</p>
          </el-col>
          <el-col :span="12">
            <p>责任人：{{ userNames }}</p>
          </el-col>
        </el-row>
        <div class="title-bottom-line">
          <p>局域网IP、端口与服务组</p>
        </div>
        <ip-port :list="store.resourceServers" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
        <div class="title-bottom-line">
          <p>基础资源软件</p>
        </div>

        <ul class="soft-ul">
          <li v-for="item in store.resourceSoftwares" :key="item.id" class="inp">
            <div><span class="item-label">类型：</span>{{ item.softwareType }}</div>
            <div><span class="item-label">软件：</span>{{ item.softwareValue }}</div>
            <div><span class="item-label">版本：</span>{{ item.softwareEdition }}</div>
          </li>
        </ul>
        <div class="title-bottom-line">
          <p>其他应用组件</p>
        </div>
        <el-tag v-for="item in store.abstract.resourceAssemblys" class="tade-tag" :key="item.assemblyId">{{ item.assemblyName }}</el-tag>
      </el-col>
    </el-row>
  </xel-dialog>
  <!-- 添加端口号级服务协议弹框 -->
  <xel-dialog title="添加端口号及服务协议" ref="addtoRef" @submit="AddForm" @close="closeDialog" size="mini">
    <dynamic ref="dynamicIpRef" label-width="6em" :formList="formListIp" :data-rows="formAdd" v-if="dynamicIpstatus"></dynamic>
  </xel-dialog>
</template>
<script setup>
import {
  getAssetsResourcesList as getTableData,
  selectSoftTypeListByPage,
  selectSoftListByPage,
  assetsDetail,
} from "@/api/securityAssets/assetsList";
import { openIpNotConfirm, changeIpNotConfirmAssets } from "@/api/securityAssets/confirm";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
const $globalWindowSize = window.$globalWindowSize;

let props = defineProps({
  isDialog: {
    type: Boolean,
    default: false,
  },
  currentId: {
    type: String,
    default: "",
  },
});
let emit = defineEmits(["close"]);
let idKey = "roleId";
let tableRef = ref();
let timelineRef = ref();
let dialogRef = ref();
let ruleFormRef = ref();
let addtoRef = ref();
let ruleForm = ref();
let dynamicIpRef = ref();
let dynamicIpstatus = ref(false);

//搜索相关
let searchState = reactive({
  data: {
    level: "",
    name: "",
    hostName: "",
    createTimeStr: "",
    createName: "",
    ips: "",
    deptId: "",
    ports: "",
    assetsPerson: "",
    softwareEdition: "",
    softwareValue: "",
    assembly: "",
    softwareType: "", //软件类别id
  },
  menuData: [
    {
      lable: "等级保护级别：",
      prop: "level",
      options: [],
      dictName: "grade_protection_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "计算设备对象名称",
    },
    {
      formType: "input",
      prop: "hostName",
      label: "主机名称",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "date",
      type: "datetimerange",
      prop: "createTimeStr",
      label: "创建日期",
    },
    {
      formType: "input",
      prop: "ips",
      label: "IP",
    },
    {
      formType: "deptTree",
      prop: "deptId",
      label: "责任主体",
      multiple: false,
      // onCurrentChange(val) {
      //   getCanSelectPersonByDeptFn(val);
      // },
    },
    {
      formType: "select",
      prop: "portService",
      label: "服务协议",
      filterable: true,

      dictName: "service_agreement",
      onChangeOptions(list) {
        typeOptions = list;
      },
    },
    {
      formType: "input",
      prop: "ports",
      label: "端口号",
    },
    {
      formType: "input",
      prop: "assetsPerson",
      label: "责任人",
    },
    {
      formType: "input",
      prop: "assembly",
      label: "基础软件组件",
    },
    {
      formType: "select",
      prop: "softwareType",
      label: "基础软件类别",
      multiple: false,
      filterable: true,
      // 调字典接口
      seleteCode: {
        code: selectSoftTypeListByPage,
        resKey: "softTypeList",
        // 传递取值的字段名
        label: "name",
        value: "id",
        params: {},
      },
      onChange(val) {
        searchState.formList[9].isshow = true;
        // basicSoftwareCategory(val);
      },
    },
  ],
});
//搜索按钮方法
function search(initPageNum = true) {
  let params = { ...searchState.data, beginTimeStr: "", endTimeStr: "" };
  if (searchState.data.createTimeStr && searchState.data.createTimeStr.length > 0) {
    params.beginTimeStr = searchState.data.createTimeStr[0];
    params.endTimeStr = searchState.data.createTimeStr[1];
  }
  delete params.createTimeStr;
  tableRef.value.reload(params, initPageNum);
}
// 重置按钮方法
function reset() {
  searchState.data = {
    level: "",
    name: "",
    hostName: "",
    createTimeStr: "",
    createName: "",
    ips: "",
    deptId: "",
    ports: "",
    assetsPerson: "",
    softwareEdition: "",
    softwareValue: "",
    assembly: "",
    softwareType: "", //软件类别id
  };
  search();
}
// 列表配置项
const columns = [
  {
    prop: "name",
    label: "计算设备资产名称",
  },
  {
    prop: "hostName",
    label: "主机名称",
    sortable: "custom",
  },
  {
    prop: "ips",
    label: "IP地址",
  },
  {
    prop: "relevantBusinessCount",
    label: "相关业务系统对象",
  },
  {
    prop: "relevantVulnCount",
    label: "相关待整改中高危漏洞",
  },
  {
    prop: "relevantEventCount",
    label: "相关待处置安全事件",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        isFont: "icon-hebingdanyuange",
        title: "合并",
        onClick(scope) {
          emits(scope.row.id);
        },
      },
    ],
  },
];
let store = reactive({
  abstract: {},
  resourceServers: [],
  resourceSoftwares: [],
  spare1: [],
});
// 打开弹框
let uId = ref("");
function emits(val) {
  uId.value = val;
  openIpNotConfirm({ id: props.currentId }).then((res) => {
    formData.ip = res.data.ip;
  });
  // 查看详情
  assetsDetail({ id: val }).then((res) => {
    store.abstract = res.data.resource;
    store.resourceServers = res.data.resource.resourceServers;
    store.resourceSoftwares = res.data.resource.resourceSoftwares;
    store.resourceServers.forEach((item) => {
      item.type = item.serviceAgreementName;
    });
  });
  store.spare1 = [];
  dialogRef.value.open();
}
let userNames = computed(() => {
  return store.abstract.personList ? store.abstract.personList.map((item) => item.userName).join() : "";
});
// 弹框列表
let formData = reactive({
  ip: "",
  internetIp: "",
  domain: "",
});
let formAdd = reactive({
  number: "",
  serverAgreement: "",
});
// 弹框内容
let formListIp = reactive([
  {
    formType: "number",
    prop: "port",
    label: "端口",
    itemWidth: "calc((97% - 70px) / 2 )",
    value: "",
    min: 0,
    max: 65535,
    precision: "0",
  },
  {
    formType: "select",
    prop: "serverAgreement",
    label: "服务协议",

    dictName: "service_agreement",
    filterable: true,
    itemWidth: "calc((97% - 70px) / 2 )",
    value: "",
  },
]);
let formList = reactive([
  {
    formType: "input",
    prop: "ip",
    label: "IP地址",
    disabled: true,
  },
  {
    formType: "input",
    prop: "internetIp",
    label: "主机名称",
    disabled: true,
  },
  {
    formType: "input",
    prop: "lanIp",
    label: "操作系统",
    disabled: true,
  },
  {
    formType: "input",
    prop: "lanIp",
    label: "端口服务",
    disabled: true,
  },
]);
// 打开添加弹框
function change() {
  dynamicIpstatus.value = true;
  addtoRef.value.open();
}
//
let typeOptions = [];
function AddForm() {
  let flag = dynamicIpRef.value.list.every((item) => {
    // console.log(item[0].value,item[1].value);
    return item[0].value != undefined && item[1].value != "" && item[0].value != "";
  });
  if (flag) {
    dynamicIpRef.value.list.forEach((item) => {
      if (item[0].value != "" && item[1].value != "") {
        store.resourceServers.push({ ip: formData.ip, port: item[0].value, type: getDictText(item[1].value) });
        store.spare1.push([item[0].value, item[1].value]);
        formAdd.serverAgreement = "";
        formAdd.number = "";
        dynamicIpstatus.value = false;
      }
    });
    ElMessage({
      message: "操作成功",
      type: "success",
    });
    addtoRef.value.close();
  } else {
    ElMessage.error("请添加端口号及服务协议");
  }
}

function getDictText(value) {
  let selecteditem = typeOptions.find((item) => {
    return item.value == value;
  });
  return selecteditem.label;
}
// 变更资产
function submitForm() {
  let val = {
    id: uId.value,
    spare1: props.currentId,
    spare2: store.spare1
      .map((item) => {
        return item.join("#_ipServer_#");
      })
      .join("#_ssp_#"),
    ip: formData.ip,
  };

  if (val.spare2 !== "") {
    ElMessageBox.confirm("确定要变更资产吗？", "信息", {
      distinguishCancelAndClose: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      changeIpNotConfirmAssets(val).then(() => {
        ElMessage({
          message: "操作成功",
          type: "success",
        });
        emit("close");
        dialogRef.value.close();
        addtoRef.value.close();
      });
    });
  } else {
    ElMessage({
      type: "info",
      message: "请先添加端口/服务！",
    });
  }
}
</script>
<style scoped lang="scss">
.button {
  margin-right: 10px;
}
.addInfo {
  :deep .el-form-item__content {
    display: flex;
  }
  .btn {
    margin-left: 20px;
  }
}
.onname {
  p {
    margin: 20px;
  }
}
.input-nane {
  margin-top: 20px;
}
.inp {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf1;
  margin-left: 20px;
}
.magin-auto {
  margin-left: 15px;
}
.tade-tag {
  margin-right: 5px;
}
.soft-ul {
  > li {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid #ebedf1;
    > div {
      flex: 1;
    }
  }
}
</style>
