<!--待确认资产-->
<template>
  <!-- 新增查询 -->
  <common-search
    v-model="searchStateIP.data"
    :menu-data="searchStateIP.menuData"
    :form-list="searchStateIP.formList"
    @search="searchIP"
    @reset="resetIP"
  />

  <xel-table ref="tableRef" :columns="columnsIP" :load-data="getAssetsIpConfirmedList" :defaultParams="offQuery"> </xel-table>
  <!-- 资产列表弹框 -->
  <xel-dialog title="基础资源资产列表" ref="assetsRef" @submit="submitForm" width="85%">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="计算设备对象" name="onfirst"><basics :currentId="uid" :key="activeName" @close="close"></basics></el-tab-pane>
      <el-tab-pane label="终端资产对象" name="onsecond"> <terminal :currentId="uid" :key="activeName" @close="close"></terminal></el-tab-pane>
    </el-tabs>
    <template #button>
      <el-button @click="closeDialog" class="search-button"> 关闭 </el-button>
    </template>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, onActivated, watch } from "vue";
onActivated(() => {
  search(false);
});
import { ElMessageBox, ElMessage } from "element-plus";
import Terminal from "./confirmedIP/terminal.vue";
import Basics from "./confirmedIP/basics.vue";

import { useRouter, useRoute } from "vue-router";
let props = defineProps({
  isDialog: {
    type: Boolean,
    default: false,
  },
  assetsInfos: {
    type: String,
    default: "",
  },
  addEvent: {
    type: String,
    default: "",
  },
});
const router = useRouter();
const route = useRoute();

/* 新增参数 - 划分综合服务 */
/* { spare1: addEvent !== '' ? '' : route.params.id, assetsInfos: assetsInfos } */
let offQuery =
  route.name == "OffAddEvent" || route.name == "OffEventDetail"
    ? { spare2: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos }
    : { spare1: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos };

import tableHandler from "../js/tableHandler";
const { actionBtns } = tableHandler(
  {
    ignore,
    merge,
    add: addAsset,
    connect,
    ipAssets,
  },
  props.isDialog
);

import { getAssetsIpConfirmedList, openIpNotConfirm, ignoreIpNotConfirmAsset } from "@/api/securityAssets/confirm";
let emit = defineEmits(["connect", "updateTotal"]);
let tableRef = ref();
let dialogRef = ref("");
let ruleFormRef = ref("");
let assetsRef = ref("");
let activeName = ref("onfirst");
function handleClick(tab, event) {
  console.log(tab, event);
}

/* URL增加查询 */
let searchStateIP = reactive({
  data: {
    name: "",
    ip: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "资产名称",
    },
    {
      formType: "input",
      prop: "ip",
      label: "IP",
    },
  ],
});

/* URL 查询 */
function searchIP(initPageNum = true) {
  tableRef.value.reload(searchStateIP.data, initPageNum);
}
/* URL 重置 */
function resetIP() {
  searchStateIP.data = {
    name: "",
    ip: "",
  };
  searchIP();
}

// IP列表配置项
const columnsIP = [
  {
    prop: "name",
    label: "资产名称",
  },
  {
    prop: "ip",
    label: "IP",
  },
  {
    prop: "port",
    label: "端口",
  },
  {
    prop: "createName",
    label: "	创建人",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  actionBtns,
];
let uid = ref("");
// 弹框打开
function ipAssets(val) {
  uid.value = val;
  openIpNotConfirm({ id: val }).then((res) => {
    formData.name = res.data.ip;
  });
  // dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "IP地址",
    disabled: true,
  },
]);
let formData = reactive({
  name: "",
});
// 忽略方法
function ignore() {
  ElMessageBox.confirm("是否确认忽略该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ignoreIpNotConfirmAsset({ id: uid.value }).then(() => {
      ElMessage({
        message: "操作成功",
        type: "success",
      });
      emit("updateTotal");
      tableRef.value.reload();
    });
  });
}
//
function merge() {
  assetsRef.value.open();
}
// 关联
function connect(id, ip) {
  let data = {
    id: id,
    ip: ip,
    type: "ip",
  };
  emit("connect", data);
}
//跳转到新建资产
function addAsset(id) {
  router.push({ name: "ConfirmAdd", params: { type: 1, confirmId: id } });
}
//
function close() {
  assetsRef.value.close();
  emit("updateTotal");
  tableRef.value.reload();
}
// 搜索方法
const globalSearchObj = ref({
  globalSearch: null,
}); //全局搜索
function search(data = {}, isGlobalSearch = true) {
  if (!tableRef.value) return;
  if (isGlobalSearch) {
    if (data.globalSearch) {
      globalSearchObj.value = data;
    }
  } else {
    globalSearchObj.value = {};
  }
  let params = { assetsInfos: props.assetsInfos, ...globalSearchObj.value };
  emit("updateTotal");

  return tableRef.value.reload(params);
}
function closeDialog() {
  assetsRef.value.close();
}
defineExpose({
  getListData: search,
});
</script>

<style lang="scss" scoped></style>
