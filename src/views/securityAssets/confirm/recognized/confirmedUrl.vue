<!--待确认资产-->
<template>
  <!-- 新增查询 -->
  <common-search
    v-model="searchStateURL.data"
    :menu-data="searchStateURL.menuData"
    :form-list="searchStateURL.formList"
    @search="searchURL"
    @reset="resetURL"
  />

  <xel-table ref="tableRef" :columns="columns" :load-data="getAssetsConfirmedList" :defaultParams="offQuery"> </xel-table>

  <!--  -->
  <xel-dialog title="业务系统资产列表" ref="assetsRef" width="80%">
    <common-search
      v-model="searchState.data"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
      hideOptions="true"
    >
    </common-search>
    <xel-table ref="businessRef" :columns="busineColumns" :load-data="getTableData"> </xel-table>
    <template #button>
      <el-button @click="closeDialog" class="search-button"> 关闭 </el-button>
    </template>
  </xel-dialog>
  <!-- 待确认资产弹框 -->
  <xel-dialog title="待确认资产" ref="handleRef" @submit="assetsDialog" width="80%" @close="oncloseDialog" buttonDetermine="变更资产">
    <el-row :gutter="70">
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>新增信息</p>
        </div>
        <el-form :key="formList" :model="formData" ref="ruleFormRef" label-width="120px" size="mini" class="addInfo">
          <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item">
            <el-button class="btn" size="small" @click="change(item)" v-if="formData[item.prop]">
              <el-icon :size="12"> <ArrowRightBold /></el-icon>
            </el-button>
          </xel-form-item>
        </el-form>
      </el-col>
      <!-- 摘要 -->
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>摘要</p>
        </div>
        <el-row class="onname">
          <el-col :span="12">
            <p>资产对象类型：业务系统资产</p>
          </el-col>
          <el-col :span="12">
            <p>资产对象名称：{{ store.abstract.name }}</p>
          </el-col>
          <el-col :span="12">
            <p>等级保护级别：{{ store.abstract.levelName }}</p>
          </el-col>
          <el-col :span="12">
            <p>责任主体：{{ store.abstract.deptName }}</p>
          </el-col>
          <el-col :span="12">
            <p>责任人：{{ userNames }}</p>
          </el-col>
        </el-row>
        <div class="title-bottom-line">
          <p>系统入口</p>
        </div>
        <p v-for="item in store.domainList" :key="item.id" class="inp">{{ item.domain }}</p>
        <div class="title-bottom-line">
          <p>互联网IP、端口与服务组</p>
        </div>
        <ip-port :list="store.portsServiceList" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
        <div class="title-bottom-line">
          <p>局域网IP、端口与服务组</p>
        </div>
        <ip-port :list="store.portsName" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
      </el-col>
    </el-row>
    <xel-dialog title="添加端口号及服务协议" ref="URLRef" @submit="addNumber" @close="closeDialog">
      <dynamic ref="dynamicIpRef" label-width="6em" :formList="formListIp" :data-rows="formAdd" v-if="dynamicIpstatus"></dynamic>
    </xel-dialog>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, onActivated, watch } from "vue";
onActivated(() => {
  search(false);
  tableRef.value.reload();
});
import { ElMessageBox, ElMessage } from "element-plus";
import {
  getAssetsConfirmedList,
  openDomainNotConfirm,
  ignoreDomainNotConfirmAsset,
  changeDomainNotConfirmAssets,
} from "@/api/securityAssets/confirm";
import { queryList as getTableData, getdetail } from "@/api/securityAssets/business";
import { useRouter, useRoute } from "vue-router";
const $globalWindowSize = window.$globalWindowSize;

let emit = defineEmits(["connect", "updateTotal"]);
let props = defineProps({
  isDialog: {
    type: Boolean,
    default: false,
  },
  assetsInfos: {
    type: String,
    default: "",
  },
  addEvent: {
    type: String,
    default: "",
  },
});

const router = useRouter();
const route = useRoute();

/* 新增参数 - 划分综合服务 */
/* { spare1: addEvent !== '' ? '' : route.params.id, assetsInfos: assetsInfos } */
let offQuery =
  route.name == "OffAddEvent" || route.name == "OffEventDetail"
    ? { spare2: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos }
    : { spare1: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos };

let assdin = ref(true);
import tableHandler from "../js/tableHandler";
const { actionBtns } = tableHandler(
  {
    ignore,
    merge,
    add: addAsset,
    ipAssets,
    connect: connectAsset,
  },
  props.isDialog
);

let tableRef = ref("");
let dialogRef = ref("");
let assetsRef = ref("");
let businessRef = ref("");
let handleRef = ref("");
let URLRef = ref("");
let dynamicIpRef = ref();
let dynamicIpstatus = ref(false);

/* URL增加查询 */
let searchStateURL = reactive({
  data: {
    name: "",
    internetIp: "",
    lanIp: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "资产名称",
    },
    {
      formType: "input",
      prop: "internetIp",
      label: "互联网IP地址",
    },
    {
      formType: "input",
      prop: "lanIp",
      label: "局域网IP地址",
    },
  ],
});

/* URL 查询 */
const globalSearchObj = ref({}); //全局搜索
function searchURL(initPageNum = true, data = {}, isGlobalSearch = true) {
  if (isGlobalSearch) {
    if (data.globalSearch) {
      globalSearchObj.value = data;
    }
  } else {
    globalSearchObj.value = {};
  }
  tableRef.value.reload({ ...searchStateURL.data, ...globalSearchObj.value }, initPageNum);
}
/* URL 重置 */
function resetURL() {
  searchStateURL.data = {
    name: "",
    internetIp: "",
    lanIp: "",
  };
  searchURL();
}

// 域名列表配置项
const columns = [
  {
    prop: "name",
    label: "资产名称",
  },
  {
    prop: "domain",
    label: "新增URL/子域名",
  },
  {
    prop: "internetIp",
    label: "互联网IP地址",
  },
  {
    prop: "lanIp",
    label: "局域网IP地址",
  },
  {
    prop: "createName",
    label: "	创建人",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  actionBtns,
];

let uid = ref("");

// 弹框打开
let ondata = ref({});
function ipAssets(val) {
  uid.value = val;
  openDomainNotConfirm({ id: val }).then((res) => {
    ondata.value = res.data;
    formData.domain = res.data.domain;
    formData.internetIp = res.data.internetIp;
    formData.lanIp = res.data.lanIp;
  });
  // dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "domain",
    label: "URL",
    disabled: true,
    isShow: true,
  },
  {
    formType: "input",
    prop: "internetIp",
    label: "互联网IP",
    disabled: true,
    isShow: true,
  },
  {
    formType: "input",
    prop: "lanIp",
    label: "局域网IP",
    disabled: true,
    isShow: true,
  },
]);
let formData = reactive({
  lanIp: "",
  internetIp: "",
  domain: "",
});
// 与现有资产合并
function merge() {
  assetsRef.value.open();
  reset();
}
// 关联
function connectAsset(id) {
  let data = {
    id: id,
    type: "domain",
  };
  emit("connect", data);
  emit("updateTotal");
}
// 处理资产弹框里的列表配置项
const busineColumns = [
  {
    prop: "name",
    label: "业务系统名称",
    sortable: "custom",
  },
  {
    prop: "domains",
    label: "系统入口",
    sortable: "custom",
  },
  {
    prop: "relevantResourcesCount",
    label: "相关计算设备对象",
  },
  {
    prop: "relevantVulnCount",
    label: "相关待整改中高危漏洞",
  },
  {
    prop: "relevantEventCount",
    label: "相关待处置安全事件",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        isFont: "icon-hebingdanyuange",
        title: "合并",
        onClick(scope) {
          emits(scope.row.id);
        },
      },
    ],
  },
];
//搜索相关
let typeOptions = [];
let searchState = reactive({
  data: {
    level: "",
    name: "",
    domain: "",
    createName: "",
    createTimeStr: "",
    portService: "",
    ips: "",
    ports: "",
    assetsPerson: "",
    deptId: "",
  },
  menuData: [
    {
      lable: "等级保护级别",
      prop: "level",
      options: [],
      dictName: "grade_protection_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "业务系统名称",
    },
    {
      formType: "input",
      prop: "domain",
      label: "系统入口",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "date",
      type: "datetimerange",
      prop: "createTimeStr",
      label: "创建日期",
    },
    {
      formType: "select",
      prop: "portService",
      label: "服务协议",
      filterable: true,
      dictName: "service_agreement",
      onChangeOptions(list) {
        typeOptions = list;
      },
    },
    {
      formType: "input",
      prop: "ips",
      label: "IP",
    },
    {
      formType: "input",
      prop: "ports",
      label: "端口号",
    },
    {
      formType: "input",
      prop: "assetsPerson",
      label: "责任人",
    },
    {
      formType: "deptTree",
      prop: "deptId",
      label: "责任主体",
      multiple: false,
    },
  ],
});

let activeIPType = "";
let domain = ref("");
function change(data) {
  if (data.label == "URL") {
    formList[0].isShow = false;
    domain.value = formData.domain;
    store.domainList.push({
      domain: formData.domain,
    });
    ElMessage({
      message: "操作成功",
      type: "success",
    });
  } else {
    activeIPType = data.prop;
    URLRef.value.open();
    dynamicIpstatus.value = true;
  }
}
// 搜索方法
function search(initPageNum = true) {
  if (!businessRef.value) return;
  let params = { ...searchState.data, beginTimeStr: "", endTimeStr: "" };
  if (searchState.data.createTimeStr && searchState.data.createTimeStr.length > 0) {
    params.beginTimeStr = searchState.data.createTimeStr[0];
    params.endTimeStr = searchState.data.createTimeStr[1];
  }
  delete params.createTimeStr;

  businessRef.value.reload(params, initPageNum);
}
// 重置方法
function reset() {
  searchState.data = {
    level: "",
    name: "",
    domain: "",
    createName: "",
    createTimeStr: "",
    portService: "",
    ips: "",
    ports: "",
    assetsPerson: "",
    deptId: "",
  };
  businessRef.value.reload(searchState.data);
}
// 忽略方法
function ignore() {
  ElMessageBox.confirm("是否确认忽略该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ignoreDomainNotConfirmAsset({ id: uid.value }).then(() => {
      ElMessage({
        message: "操作成功",
        type: "success",
      });
      emit("updateTotal");
      tableRef.value.reload();
    });
  });
}
let store = reactive({
  abstract: {},
  domainList: [],
  portsServiceList: [],
  portsName: [],
  spare1: [],
  spare2: [],
});
// 合并按钮
let valId = ref("");
function emits(val, data) {
  valId.value = val;
  formList[0].isShow = true;
  formList[1].isShow = true;
  formList[2].isShow = true;
  getdetail({ id: val }).then((res) => {
    store.abstract = res.data.business;
    store.domainList = res.data.business.domainList;
    let arr = res.data.business.portsServiceList;
    arr.forEach((item) => {
      item.type = item.serviceAgreementName;
    });
    arr.forEach((item) => {
      if (item.networkType === "1") {
        // 互联网
        store.portsServiceList.push(item);
      } else {
        // 局域网
        store.portsName.push(item);
      }
    });
  });
  handleRef.value.open();
}
let userNames = computed(() => {
  return store.abstract.auList ? store.abstract.auList.map((item) => item.userName).join() : "";
});
// 添加资产跳转
function addAsset(id) {
  router.push({ name: "ConfirmAdd", params: { type: 0, confirmId: id } });
}
// 弹框内容
let formListIp = reactive([
  {
    formType: "number",
    prop: "port",
    label: "端口",
    itemWidth: "calc((97% - 70px) / 2 )",
    value: "",
    min: 0,
    max: 65535,
    precision: "0",
  },
  {
    formType: "select",
    prop: "serverAgreement",
    label: "服务协议",
    dictName: "service_agreement",
    filterable: true,
    itemWidth: "calc((97% - 70px) / 2 )",
    value: "",
  },
]);
//
let formAdd = reactive([]);
function addNumber() {
  if (activeIPType == "internetIp") {
    dynamicIpRef.value.list.forEach((item) => {
      store.portsServiceList.push({ ip: formData.internetIp, port: item[0].value, type: getDictText(item[1].value) });
      store.spare1.push([formData.internetIp, item[0].value, item[1].value]);
      URLRef.value.close();
      dynamicIpstatus.value = false;
      formList[1].isShow = false;
    });
    ElMessage.success({
      message: "操作成功",
      type: "success",
    });
  } else {
    let flag = dynamicIpRef.value.list.every((item) => {
      return item[0].value != "" && item[1].value != "" && item[0].value != undefined;
    });
    if (flag) {
      dynamicIpRef.value.list.forEach((item) => {
        if (item[0].value != "" && item[1].value != "") {
          store.portsName.push({ ip: formData.lanIp, port: item[0].value, type: getDictText(item[1].value) });
          store.spare2.push([formData.lanIp, item[0].value, item[1].value]);
          dynamicIpstatus.value = false;
          formList[2].isShow = false;
        }
      });
      ElMessage({
        message: "操作成功",
        type: "success",
      });
      URLRef.value.close();
    } else {
      ElMessage.error("请添加端口号及服务协议");
    }
  }
}

function getDictText(value) {
  let selecteditem = typeOptions.find((item) => {
    return item.value == value;
  });
  return selecteditem.label;
}

// 变更资产
function assetsDialog() {
  let val = {
    id: valId.value,
    spare1: store.spare1
      .map((item) => {
        return item.join("#_portServer_#");
      })
      .join("#_ssp_#"),
    spare2: store.spare2
      .map((item) => {
        return item.join("#_portServer_#");
      })
      .join("#_ssp_#"),
    domain: domain.value,
    spare3: uid.value,
  };
  if (val.spare1 !== "" || val.domain !== "" || val.spare2 !== "") {
    ElMessageBox.confirm("确定要变更资产吗？", "信息", {
      distinguishCancelAndClose: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      changeDomainNotConfirmAssets(val).then(() => {
        ElMessage({
          message: "操作成功",
          type: "success",
        });
        assetsRef.value.close();
        handleRef.value.close();
        emit("updateTotal");
        tableRef.value.reload(searchState.data, true);
      });
    });
  } else {
    ElMessage({
      type: "info",
      message: "请先添加合并项！",
    });
  }
}
function searchTable() {
  let params = { assetsInfos: props.assetsInfos, ...globalSearchObj.value };
  emit("updateTotal");

  return tableRef.value.reload(params);
}
defineExpose({
  getListData: searchTable,
  getSearch: searchURL,
});
function closeDialog() {
  assetsRef.value.close();
}
function oncloseDialog() {
  store.portsServiceList = [];
  store.portsName = [];
}
</script>
<style lang="scss" scoped>
.onname {
  p {
    margin: 20px;
  }
}
.inp {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf1;
  margin-left: 20px;
}
.addInfo {
  :deep .el-form-item__content {
    display: flex;
  }
  .btn {
    margin-left: 20px;
  }
}
.magin-auto {
  margin-left: 20px;
}
.formWrapper {
  display: flex;
}
</style>
