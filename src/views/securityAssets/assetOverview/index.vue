<template>
  <ul class="top asset-top-ul">
    <li>
      <el-row :gutter="24">
        <el-col :span="7">
          <div class="tap-tubiao">
            <icon n="icon-IP1" :size="30"></icon></div
        ></el-col>
        <el-col :span="17">
          <p class="tap-title">互联网暴露面IP数量</p>
          <p class="tap-number">{{ ip }}</p></el-col
        >
      </el-row>
    </li>
    <li>
      <el-row :gutter="24">
        <el-col :span="7">
          <div class="tap-tubiao">
            <icon n="icon-yuming" :size="30"></icon>
          </div>
        </el-col>
        <el-col :span="17">
          <p class="tap-title">互联网暴露面域名数量</p>
          <p class="tap-number">{{ url }}</p></el-col
        >
      </el-row>
    </li>
    <li>
      <el-row :gutter="24">
        <el-col :span="7">
          <div class="tap-tubiao">
            <icon n="icon-xitong" :size="30"></icon>
          </div>
        </el-col>
        <el-col :span="17">
          <p class="tap-title">业务系统资产数量</p>
          <p class="tap-number">{{ usiness }}</p></el-col
        >
      </el-row>
    </li>
    <li>
      <el-row :gutter="24">
        <el-col :span="7">
          <div class="tap-tubiao">
            <icon n="icon-wodeshebei" :size="30"></icon></div
        ></el-col>
        <el-col :span="17">
          <p class="tap-title">计算设备资产数量</p>
          <p class="tap-number">{{ calculation }}</p></el-col
        >
      </el-row>
    </li>
    <li>
      <el-row :gutter="24">
        <el-col :span="7">
          <div class="tap-tubiao">
            <icon n="icon-anfengxiantongji" :size="30"></icon></div
        ></el-col>
        <el-col :span="17">
          <p class="tap-title">存在风险的资产数量</p>
          <p class="tap-number">{{ risk }}</p></el-col
        >
      </el-row>
    </li>
  </ul>
  <el-row :gutter="24">
    <el-col :span="12">
      <el-card class="card ondicvParent">
        <p class="name">IP资产风险汇总</p>
        <div class="inDiv" v-if="total > 0">
          <div class="onDiv">
            <p class="onp">{{ rows.ipStr }}</p>
            <p class="onp">{{ rows.updateTime }}</p>
          </div>
          <div class="divIon">{{ total }}</div>
        </div>
        <xel-table
          v-if="total > 0"
          class="table"
          ref="tableRef"
          :pageSizes="[5, 10, 20, 50]"
          :columns="columns"
          :load-data="selectIpAssetRiskList"
          :getDataList="getDataList"
          :pageSize="5"
        >
        </xel-table>
        <div v-else class="ondicv">暂无数据</div>
      </el-card>
    </el-col>
    <el-col :span="12">
      <el-card class="card">
        <p class="name">
          风险资产分布
          <echart-edit-btn echartId="assetOverviewEchart1" :dataset="pieEchartOption.dataset"></echart-edit-btn>
          <span class="clearfix"></span>
        </p>
        <echart-component
          echartType="2"
          echartId="assetOverviewEchart1"
          :options="pieEchartOption"
          @updateEchart="updateEchart"
          style="width: 100%; height: 420px"
          class="autc"
        ></echart-component>
      </el-card>
    </el-col>
  </el-row>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import {
  selectIpAssetNum,
  selectDomainAssetNum,
  selectExistRiskAssetNum,
  selectComputingEquipmentAssetNum,
  selectBusinessSystemAssetNum,
  selectIpAssetRiskList,
  selectIpAssetRiskPieChart,
} from "@/api/securityAssets/assetOverview.js";
let tableRef = ref();
let pieRef = ref();
// 列表配置项
const columns = [
  {
    prop: "ipStr",
    label: "测试对象IP",
  },
  {
    prop: "port",
    label: "端口",
  },
  {
    prop: "service",
    label: "协议",
  },
  {
    prop: "updateTime",
    label: "更新时间",
    width: "150px",
  },
  {
    prop: "diffType",
    label: "状态",
  },
];
let pieEchartOption = ref({});
pieEchartOption.value = {
  tooltip: {
    trigger: "item",
    position: ["40%", "60%"],
  },
  legend: {
    top: "5%",
    left: "center",
  },
  dataset: {
    source: [],
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "70%"],
      center: ["50%", "60%"],

      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      label: {
        show: false,
        position: "center",
      },
      emphasis: {
        label: {
          show: false,
          fontSize: "20",
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: false,
      },
    },
  ],
};

let activities = reactive([]);
let ip = ref("");
let url = ref("");
let usiness = ref("");
let calculation = ref("");
let risk = ref("");
let total = ref("");
let rows = ref({});
// 获取数量
function getAssetOverview() {
  // 互联网IP资产数量
  selectIpAssetNum().then((res) => {
    ip.value = res.data;
  });
  //互联网域名资产数量
  selectDomainAssetNum().then((res) => {
    url.value = res.data;
  });
  //业务系统资产数量
  selectBusinessSystemAssetNum().then((res) => {
    usiness.value = res.data;
  });
  //计算设备资产
  selectComputingEquipmentAssetNum().then((res) => {
    calculation.value = res.data;
  });
  //存在风险的资产
  selectExistRiskAssetNum().then((res) => {
    risk.value = res.data;
  });
  // 风险资产分布
  selectIpAssetRiskPieChart().then((res) => {
    for (var i in res.data) {
      activities.push([res.data[i].service, res.data[i].num]);
    }
    pieEchartOption.value.dataset.source = activities;
  });
  selectIpAssetRiskList().then((res) => {
    total.value = res.data.total;
    rows.value = res.data.rows[0];
  });
}
getAssetOverview();
// echart修改后更新内容
function updateEchart(echartId, option) {}
</script>
<style scoped lang="scss">
.top {
  width: 100%;
  // height: 112px;
  display: flex;
  margin-bottom: 32px;
  li {
    margin-right: 30px;
    display: inline-block;
    width: 20%;
    background: #ffffff;
    border-radius: $radiusL;
    padding: 15px;
  }
  li:last-child {
    margin: 0;
  }
}
@media screen and (max-width: 1600px) {
  .top {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    &::after {
      content: "";
      display: block;
      width: 30%;
    }
    li {
      width: 25%;
      margin-bottom: 20px;
    }
  }
}
.name {
  font-weight: 400;
  color: $fontColor;
  font-size: 16px;
  margin-bottom: 10px;
}
.divIon {
  margin-top: 10px;
  width: 99px;
  height: 99px;
  background-color: #dae6ff;
  color: #fff;
  border-radius: $raduisM;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #28334f;
  font-weight: 600;
}
.inDiv {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.onDiv {
  border-left: 5px solid $color;
  height: 35px;
  border-radius: 2px;
  margin-top: 30px;
}
.autc {
  margin: 0 auto;
}
.onp {
  margin-left: 10px;
}
.card {
  height: 545px;
  border: 0;
  overflow: auto;
  max-height: 545px;
}
.ondicvParent {
  position: relative;
  .ondicv {
    position: absolute;
    color: $fontColorSoft;
    font-weight: normal;
    font-size: 16px;
    left: 50%;
    top: 50%;
  }
}
</style>
