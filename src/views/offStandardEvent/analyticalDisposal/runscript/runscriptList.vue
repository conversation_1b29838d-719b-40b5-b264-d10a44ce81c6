<template>
  <!-- 综合服务管理 - 综合服务脚本 - 列表 -->
  <el-card>
    <h3 v-if="!compType" class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
      <template v-if="!compType">
        <el-button @click="newlyAdded" class="search-button" v-hasPermi="'eventTemplate:edit'">
          <el-icon :size="12">
            <plus />
          </el-icon>
          创建新模板
        </el-button>
        <xel-upload-dialog
          v-hasPermi="'eventTemplate:import'"
          class="upload-button"
          templateName="综合服务脚本模板"
          btnName="导入综合模板"
          size="70px"
          exportUrl="/system/osEventTemplate/downloadEventTemplate"
          importUrl="/system/osEventTemplate/importEventTemplate"
          @updateData="search"
        ></xel-upload-dialog>
        <el-button v-hasPermi="'eventTemplate:export'" @click="exportTemplate()" class="search-button">
          <el-icon :size="12"><Download /></el-icon>
          导出综合模板
        </el-button>
      </template>
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="getTableData">
      <template #radio="scope" v-if="compType">
        <el-radio v-model="templateId" :label="scope.row.id"> <span></span></el-radio>
      </template>
      <template #level="scope">
        <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
      </template>
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "OffRunscriptList",
};
</script>
<script setup>
import { selectPage as getTableData, initTask, deleteEventTemplate as delItem } from "@/api/offStandardEvent/runscript";
import { getEventTemplateTagList } from "@/api/getOsEventTagList";

import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, onActivated } from "vue";
onActivated(() => {
  search(false);
  getEventTemplateTagListFun();
});
import { Level_Data } from "@/config/constant";
import { useRouter } from "vue-router";
import { download } from "@/plugins/request";
import { useStore } from "vuex";
const store = useStore();
const router = useRouter();

let props = defineProps({
  //是否是组件，changeEvent 事件使用
  compType: {
    type: String,
    default: "",
  },
});

let tableRef = ref();
let dialogTitle = ref("");
let searchState = reactive({
  data: {
    name: "",
    levelId: "",
    description: "",
    searchTag: "",
    taskTitle: "",
    taskDetail: "",
  },
  menuData: [
    {
      lable: "工作优先级：",
      prop: "levelId",
      options: [],
      dictName: "event_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "综合服务项名称",
      labelWidth: "120px",
    },
    {
      formType: "input",
      prop: "description",
      label: "描述",
      labelWidth: "120px",
    },
    {
      formType: "select",
      prop: "searchTag",
      label: "标签",
      labelWidth: "120px",
      multiple: false,
      filterable: true,
      options: [],
      /*seleteCode: {
        code: getEventTemplateTagList,
        resKey: "tagList",
        // 传递取值的字段名
        label: "name",
        value: "id",
        params: {},
      },*/
    },
    {
      formType: "input",
      prop: "taskTitle",
      label: "任务标题",
      labelWidth: "120px",
    },
    {
      formType: "input",
      prop: "taskDetail",
      label: "任务要求",
      labelWidth: "120px",
    },
  ],
});
let state = reactive({
  menuData: [
    {
      lable: "状态",
      parameter: "status",
      menuBtnShow: true,
      options: [],
      dictName: "sys_normal_disable",
    },
  ],
  levelData: Level_Data,
});
let { formData } = toRefs(state);
let { levelData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    resource: "",
  };
}
// 搜索按钮
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}

function reset() {
  searchState.data = {
    name: "",
    levelId: "",
    description: "",
    searchTag: "",
    taskTitle: "",
    taskDetail: "",
  };
  search();
}

const getEventTemplateTagListFun = () => {
  getEventTemplateTagList().then((res) => {
    searchState.formList.find((item) => item.prop === "searchTag").options = res.data.tagList.map((v) => {
      return {
        value: v.id,
        label: v.name,
      };
    });
  });
};

// 列表配置项
const columns = [
  {
    hide: !props.compType,
    prop: "id",
    label: "",
    slotName: "radio",
    width: "80px",
  },
  {
    prop: "name",
    label: "综合服务项名称",
  },
  {
    prop: "titlePrefix",
    label: "标题前缀",
  },
  {
    prop: "levelId",
    label: "工作默认优先级",
    slotName: "level",
  },
  {
    prop: "description",
    label: "描述",
  },
  {
    hide: props.compType,
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "view",
        title: "详情",
        onClick(scope) {
          router.push({
            name: "OffDetailTemplate",
            params: {
              id: scope.row.id,
            },
          }); //路由跳转
        },
      },
      {
        icon: "edit",
        title: "修改",
        hasPermi: "eventTemplate:edit",
        onClick(scope) {
          router.push({
            name: "OffEditTemplate",
            params: {
              id: scope.row.id,
            },
          }); //路由跳转
        },
      },
      {
        icon: "delete",
        hasPermi: "eventTemplate:delete",

        title: "删除",
        onClick(scope) {
          batchDelete(scope.row);
        },
      },
    ],
  },
];

// 列表操作方法
// 新增按钮
function newlyAdded() {
  router.push({
    name: "AddOffEditTemplate",
    params: {
      id: 0,
    },
  }); //路由跳转
}

// 事件级别比对
let level = reactive;

// 批量删除
function batchDelete(data) {
  ElMessageBox.confirm("确认删除该数据项？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delItem({ id: data.id }).then(() => {
      ElMessage.success({
        message: "删除成功",
      });
      //删除打开的tab编辑页面
      store.commit("closeTabById", data.id);
      console.log(data.id, "id");
      tableRef.value.table.clearSelection();

      search(false);
    });
  });
}
// 导出事件模板
function exportTemplate() {
  let url = "/system/osEventTemplate/exportEventTemplate";
  download(url, "事件模板.xlsx", searchState.data, "post");
}
// 初始化任务要求
function initRequire() {
  initTask({})
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success("操作成功");
      }
    })
    .catch(() => {});
}

//单选
let templateId = ref("");
defineExpose({
  templateId: computed(() => {
    return templateId.value;
  }),
  templateInfo: computed(() => {
    if (tableRef.value) {
      return tableRef.value.data.find((item) => {
        return item.id == templateId.value;
      });
    } else {
      return {};
    }
  }),
});
</script>
<style scoped lang="scss">
.upload-button {
  margin-right: 10px;
}
</style>
