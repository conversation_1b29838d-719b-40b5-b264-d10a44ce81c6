<template>
  <xel-dialog
    :associateType="associateType"
    :dialogVisible="dialogVisible"
    :title="dialogTitle"
    ref="dialogRef"
    size="large"
    @close="$emit('closeDialog')"
  >
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="search" @reset="reset"> </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :defaultParams="{ id: templateId, type: type }"
      :load-data="getTableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-for="item in columns" :key="item.id" v-bind="item"></el-table-column>
      <template #after="scope">
        <el-button
          v-if="(props.associateType == '1' && scope.row.type == '1') || props.associateType == '0'"
          @click="modifyButton(scope.row, 'after')"
        >
          下一级关联</el-button
        >
      </template>
      <template #before="scope">
        <el-button
          v-if="(props.associateType == '0' && scope.row.type == '0') || props.associateType == '1'"
          @click="modifyButton(scope.row, 'before')"
          >上一级关联</el-button
        >
      </template>
      <template #level="scope">
        <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
      </template>
    </xel-table>
  </xel-dialog>
</template>
<script setup>
import { getRelTemplateList as getTableData, relManageTemplate } from "@/api/offStandardEvent/runscript";
import { ref, reactive, onMounted, watch, nextTick, toRefs } from "vue";
import { Level_Data } from "@/config/constant";
import { ElMessageBox, ElMessage } from "element-plus";
const emits = defineEmits(["close", "closeDialog"]);
let state = reactive({
  levelData: Level_Data,
});
// 查询重置
let searchState = reactive({
  data: {
    name: "",
    titlePrefix: "",
  },
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "模板名称",
    },
    {
      formType: "input",
      prop: "titlePrefix",
      label: "模板前缀",
    },
  ],
});
let { levelData } = toRefs(state);
//定义props属性
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  templateId: {
    type: String,
    default: "",
  },
  associateType: {
    type: String,
    default: "",
  },

  /* Playbook类型 */
  type: {
    type: String,
    default: "",
  },
});
let dialogRef = ref();
watch(
  () => props.dialogVisible,
  (val) => {
    if (val) {
      popupBox();
    }
  },
  { immediate: true }
);
function search(initPage = true) {
  tableRef.value && tableRef.value.reload(searchState.data, initPage);
}
function reset(initPage = true) {
  searchState.data = {
    name: "",
    titlePrefix: "",
  };
  search();
}
// 打开弹框
function popupBox() {
  nextTick(() => {
    dialogRef.value.open();
  });
}
// 列表配置项
const columns = [
  {
    prop: "name",
    label: "模板名称",
  },
  {
    prop: "titlePrefix",
    label: "模板前缀",
  },
  {
    prop: "type",
    label: "Playbook类型",
    formatter(scope) {
      let arr = { 1: "定性Playbook", 0: "基础Playbook" };
      return arr[scope.type];
    },
  },
  {
    prop: "levelId",
    label: "级别",
    slotName: "level",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "after",
  },
  {
    label: "",
    fixed: "right",
    slotName: "before",
  },
];
let dialogTitle = "关联模板";
let tableRef = ref();
// 关联操作
function modifyButton(data, type) {
  let params = {
    templateId: props.templateId,
    relTemplateId: data.id,
    relType: type,
  };
  relManageTemplate(params)
    .then((res) => {
      if (res.code == 200) {
        tableRef.value.reload();
        ElMessage.success({
          message: "关联成功",
          type: "success",
        });
      }
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped></style>
