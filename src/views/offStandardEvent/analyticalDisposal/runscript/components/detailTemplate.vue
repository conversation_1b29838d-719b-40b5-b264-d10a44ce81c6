<!-- 综合服务管理 - 综合服务脚本 - 详情 -->
<template>
  <el-card>
    <div class="top">
      <h3 class="conH3Tit">综合服务脚本详情</h3>
      <backbutton text="综合服务脚本" name="OffRunscriptList"></backbutton>
    </div>
    <el-row :gutter="40" class="eventInfo" :class="{ 'no-padding': isChangeEvent }">
      <el-col :span="12">
        <!-- 基础信息 -->
        <div class="base-info">
          <h4>综合服务模板基础信息</h4>
          <ul class="formWrapper base-info-ul">
            <li><span class="label">综合服务项名称:</span>{{ basicInfo.name }}</li>
            <li><span class="label">标题前缀:</span>{{ basicInfo.titlePrefix }}</li>
            <li>
              <span class="label"> 工作优先级:</span>
              <div>
                <el-tag :type="basicInfo.tag">{{ basicInfo.levelName }}</el-tag>
              </div>
            </li>
            <li>
              <span class="label"> 标签: </span>
              <div>
                <el-tag v-for="item in basicInfo.tagList" :key="item.id">{{ item.name }}</el-tag>
              </div>
            </li>
            <li><span class="label">描述:</span>{{ basicInfo.description }}</li>
          </ul>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="task-info">
          <div class="aTit taskTit">
            <h4>任务</h4>
          </div>
          <div class="taskWrapper">
            <TaskList :dialogVisible="showTask" :disabled="true" :comp-type="compType" :templateId="templateId" @closeDialog="showTask = false" />
          </div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup>
import {
  getTemplateById as getList,
  getRelTemplate as getTableData,
  createTemplateId,
  delRelManageTemplate as delItem,
  checkTemplateName,
  checkTemplateById,
  saveEventTemplate,
  importEventTemplate,
} from "@/api/offStandardEvent/runscript";

//变更事件使用
import { saveEventSuperTemplate, getSuperTemplateAndTasks } from "@/api/event/detail";

import TaskList from "./taskList.vue";
import { ref, reactive, toRefs, onMounted, nextTick, defineComponent, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Level_Data } from "@/config/constant";
import { batchDelete } from "@/utils/delete";
import { ElMessageBox, ElMessage } from "element-plus";
const router = useRouter();
const route = useRoute();

let props = defineProps({
  compType: {
    type: String,
    default: "",
  }, //'changeEvent'事件升级使用
  compEditId: {
    type: String,
    default: "0",
  },
});

let isChangeEvent = props.compType == "changeEvent";

let basicInfo = ref({
  name: "",
  titlePrefix: "",
  tagList: [],
  tagJson: "",
  description: "",
  type: "",
});
let typeOptions = [
  { label: "基础Playbook", value: "0" },
  { label: "定性Playbook", value: "1" },
];
getBasicData(route.params.id);
function getBasicData(id) {
  let getBasicDataApi = getList;

  getBasicDataApi({ id })
    .then((res) => {
      let data = res.data;

      if (!data) return;
      basicInfo.value = data;
      basicInfo.value.tagList = data.tagList.map((tag) => {
        return {
          id: tag.tagId,
          name: tag.tagName,
        };
      });

      if (basicInfo.value.type == 0 || basicInfo.value.type == 1) {
        basicInfo.value.type = typeOptions.find((item) => item.value == basicInfo.value.type).label;
      }
    })
    .catch(() => {});
}

let state = reactive({
  dialogTit: "",
  editId: isChangeEvent ? props.compEditId : route.params.id,
  templateId: "",
  // 基础信息
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "模板名称",
      required: true,
      type: "text",
      onChange(val) {
        if (isChangeEvent) return;
        let data = {
          id: state.templateId,
          name: val,
        };
        checkTemplateName(data)
          .then((cres) => {
            if (!cres.data.result) {
              ElMessage.warning("名称重复不可以添加");
            }
          })
          .catch(() => {});
      },
    },
    {
      formType: "input",
      prop: "titlePrefix",
      label: "模板前缀",
      required: true,
      type: "text",
    },
    {
      formType: "level",
      prop: "levelId",
      label: "事件级别",
      required: true,
      options: [], //字典自定义
      // 字典关键字
      dictName: "event_level",
    },
    {
      formType: "tag",
      prop: "tagList",
      label: "标签",
    },
    {
      formType: "input",
      prop: "description",
      label: "描述",
      required: true,
      type: "textarea",
    },
    {
      isShow: !isChangeEvent,
      formType: "radio",
      prop: "type",
      label: "Playbook类型",
      required: true,
      options: [
        { label: "基础Playbook", value: "0" },
        { label: "定性Playbook", value: "1" },
      ],
    },
  ],

  afterTableData: [],
  afterTotalCount: 0,
  beforeTableData: [],
  beforeTotalCount: 0,
  beforePageNum: 1,
  beforePageSize: 5,
  afterPageNum: 1,
  afterPageSize: 5,
  // 个数选择器（可修改）
  afterPageSizes: [1, 2, 3, 4, 5],
  beforePageSizes: [1, 2, 3, 4, 5],
  levelData: Level_Data,
});
let isDownFold = ref(false);
let isUpFold = ref(false);
// 向下关联是否折叠 向上关联是否折叠
function changeFold(type) {
  if (type == "down") {
    isDownFold.value = !isDownFold.value;
  } else {
    isUpFold.value = !isUpFold.value;
  }
}
// 下一级
// 每页显示的条数
function handleAfterSizeChange(val) {
  // 改变每页显示的条数
  state.afterPageSize = val;
  // 注意：在改变每页显示的条数时，要将页码显示到第一页
  state.afterPageNum = 1;
}

// 显示第几页
function changeAfterPagination(val) {
  // 改变默认的页数
  state.afterPageNum = val.pageNum;
}

// 上一级
// 每页显示的条数
function handleBeforeSizeChange(val) {
  // 改变每页显示的条数
  state.beforePageSize = val;
  // 注意：在改变每页显示的条数时，要将页码显示到第一页
  state.beforePageNum = 1;
}

// 显示第几页
function changeBeforePagination(val) {
  // 改变默认的页数
  state.beforePageNum = val.pageNum;
}

// 显示任务
let showTask = ref(false);
function addTask() {
  if (templateId.value) {
    showTask.value = true;
  } else {
    saveBasic(() => {
      showTask.value = true;
    });
  }
}

// 显示关联模板
let showAssociate = ref(false);
function associate() {
  showAssociate.value = true;
}
// 关闭关联模板
function closeDialog() {
  showAssociate.value = false;
  getAssociateData(state.templateId);
}

// 获取模板id
getTemplateInfo();

function getTemplateInfo() {
  if (state.editId == "0") {
    // 新增获取模板id
    if (!isChangeEvent) {
      createTemplateId({})
        .then((res) => {
          state.templateId = res.data.id;
          getAssociateData(state.templateId);
        })
        .catch(() => {});
    }
  } else {
    state.templateId = state.editId;
    getBasicData(state.templateId);

    if (!isChangeEvent) {
      getAssociateData(state.templateId);
    }
  }
}

// //  根据模板id查询信息 (关联数据 任务列表)
function getAssociateData(id) {
  //上、 下一级关联
  getTableData({ id: id })
    .then((res) => {
      // 下一级关联
      state.afterTableData = res.data.afterTemplateList;
      // 将数据的长度赋值给totalCount
      state.afterTotalCount = res.data.afterCount;
      state.beforeTableData = res.data.beforeTemplateList;
      state.beforeTotalCount = res.data.beforeCount;
      state.dialogTit = state.editId == "0" ? "事件模板" : "修改事件模板：" + state.basicInfo.name;
    })
    .catch(() => {});
}
// 删除关联关系
function handleDelete(rows) {
  batchDelete().then(() => {
    delItem({ id: rows.id }).then(() => {
      ElMessage.success("删除成功");
      getAssociateData(state.templateId);
    });
  });
}
// 保存基本信息
let ruleFormRef = ref();
async function saveBasic(callback) {
  await ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      let tagIds = state.basicInfo.tagList.map((x) => {
        return x.id;
      });
      let params = {
        ...state.basicInfo,
        id: state.templateId,
        tagJson: tagIds.toString(),
      };
      let cparams = {
        id: state.templateId,
        name: state.basicInfo.name,
      };

      if (isChangeEvent) {
        if (state.editId == "0") {
          delete params.id;
        }
        let res = await saveEventSuperTemplate(params);

        templateId.value = res.data.id;
        state.editId = isChangeEvent ? res.data.id : route.params.id;
        if (callback) {
          callback(res.data.id);
        } else {
          ElMessage.success("操作成功");
        }
      } else {
        let cres = await checkTemplateById(cparams);
        if (cres.code == 200) {
          let submitFn = saveEventTemplate;
          let res = await submitFn(params);
          ElMessage.success("操作成功");
        }
      }
    }
  });
}

let {
  templateId,
  formList,
  afterTableData,
  afterTotalCount,
  beforeTableData,
  beforeTotalCount,
  beforePageNum,
  beforePageSize,
  beforePageSizes,
  afterPageNum,
  afterPageSize,
  afterPageSizes,
  levelData,
} = toRefs(state);
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  justify-content: space-between;
}
.tit {
  font-size: 16px;
  color: rgba(40, 51, 79, 1);
  line-height: 32px;
}
.eventInfo {
  padding: 30px 0px;
  &.no-padding {
    padding: 0;
  }
  h4 {
    font-size: 14px;
    color: $fontColor;
    line-height: 36px;
  }
  .formWrapper {
    margin-top: 16px;
    padding: 24px 0;
    border-top: 2px solid #ebedf1;
    :deep .el-form-item__label {
      text-align: right;
    }
  }
  .taskWrapper {
    border-top: 2px solid #ebedf1;
    margin-top: 16px;
    :deep .el-table tr {
      height: 56px;
    }
  }
  // 关联模板
  .aTit {
    margin-top: 28px;
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
  .taskTit {
    margin: 0;
  }
  .associate-info {
    .tabTop {
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      border-bottom: 2px solid #ebedf1;
    }
    .tableTit {
      font-size: 14px;
      font-family: PingFangSC-Light, PingFang SC;
      font-weight: 300;
      color: $fontColor;
      padding: 14px 0;

      .el-icon {
        margin-right: 8px;
      }
    }
    .num {
      width: 30px;
      height: 30px;
      line-height: 1px;
      text-align: center;
      border: 1px solid #ebedf1;
    }
  }
  .btn {
    border-top: 1px solid #ebedf1;
    div:first-child {
      margin-top: 24px;
      float: right;
    }
  }
  .saveBtn {
    background-color: $color;
    color: $bgColor;
  }
  .saveBasic {
    float: right;
  }

  :deep .el-form-item__label {
    color: #999;
  }
}
.base-info-ul {
  li {
    display: grid;
    grid-template-columns: 120px auto;
    margin-bottom: 15px;
    word-break: break-all;
    .label {
      color: #666;
    }
    .el-tag {
      margin-bottom: 5px;
      margin-right: 5px;
    }
  }
}
</style>
