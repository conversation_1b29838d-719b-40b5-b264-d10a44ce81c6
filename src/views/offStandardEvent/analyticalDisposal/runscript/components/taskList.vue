<template>
  <div class="listWrapper">
    <el-table :data="tableData" :show-header="false" ref="tableRef">
      <el-table-column type="expand">
        <template #default="scope">
          <p class="detailInfo" v-html="scope.row.detail"></p>
        </template>
      </el-table-column>
      <el-table-column label="任务名称">
        <template #default="scope">
          <!--          <p>【{{ scope.row.taskGroupText }}】{{ scope.row.title }}</p>-->
          <p>{{ scope.row.title }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="!disabled" align="right" width="120px">
        <template #default="scope">
          <ul class="action-btns-ul">
            <li @click="handleUpdate(scope.row)">
              <el-tooltip content="修改" placement="top" effect="light">
                <el-icon>
                  <edit />
                </el-icon>
              </el-tooltip>
            </li>
            <li @click="handleDelete(scope.row)">
              <el-tooltip content="删除" placement="top" effect="light">
                <el-icon>
                  <delete />
                </el-icon>
              </el-tooltip>
            </li>
          </ul>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 弹窗内容 -->
  <xel-dialog :dialogVisible="dialogVisible" :title="dialogTitle" ref="dialogRef" @submit="submitForm" @close="closeTask">
    <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xel-dialog>
</template>
<script setup>
import {
  selectTaskListByTemplateId as getTaskList,
  getTempTaskParam,
  saveEventTemplateTask as addItem,
  delEventTemplateTaskById as delItem,
} from "@/api/offStandardEvent/runscript";
import { getAssigneeRoles, selectUserTree } from "@/api/event/eventList";
//事件升级使用的接口
import { selectSuperTemplateTaskList, saveSuperEventTemplateTask, delSuperTemplateTaskById, selectSuperTemplateTask } from "@/api/event/detail";

import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, watch } from "vue";
import { useRouter } from "vue-router";
import { getDicts } from "@/api/system/dict/data";
import { batchDelete } from "@/utils/delete";
import { DataBoard } from "@element-plus/icons";
const emit = defineEmits(["close", "closeDialog"]);
const router = useRouter();
//定义props属性
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  templateId: {
    type: String,
    default: "",
  },
  compType: {
    type: String,
    default: "",
  }, //'changeEvent'事件升级使用
});

let isChangeEvent = props.compType == "changeEvent";
watch(
  () => props.dialogVisible,
  (val) => {
    if (val) {
      nextTick(() => {
        popupBox();
      });
    }
  },
  { immediate: true }
);
let tableRef = ref();

let state = reactive({
  formData: {
    title: "",
    taskGroup: "",
    assignee: [],
    assigneeRole: [],
    detail: "",
    templateId: props.templateId,
    id: "",
    templateTaskId: "",
  },
  multipleSelection: [],
  menuData: [],
  tableData: [],
});
let { formData, tableData } = toRefs(state);
//表单重置
function resetFormData() {
  state.formData = {
    title: "",
    taskGroup: "",
    assignee: [],
    assigneeRole: [],
    detail: "",
    templateId: props.templateId,
    id: "",
    templateTaskId: "",
  };
  formList[1].editorData = "";
  formList[1].isClear = true;
}
function resetList() {
  getTaskData(props.templateId);
}

let editId = ref("");

// 弹框
let dialogTitle = ref("添加任务");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox(taskId) {
  dialogRef.value.open();
  dialogTitle.value = "添加任务";
  resetFormData();
  nextTick(() => {
    ruleFormRef.value.resetFields();
  });
  if (isChangeEvent) {
    getTempEventTaskInfo(taskId);
  } else {
    getTempTaskInfo(taskId);
  }
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "title",
    label: "任务名称",
    required: true,
  },
  /*{
    formType: "select",
    prop: "taskGroup",
    label: "任务阶段",
    required: true,
    options: [],
  },*/
  {
    formType: "editor",
    prop: "detail",
    label: "任务要求",
    editorClass: "formEditor", //多个编辑器时，命名不同的名字
    editorData: "",
    isClear: false,
    onEditorValue(val) {
      state.formData.detail = val;
    },
  },
  {
    formType: "tree",
    prop: "assignee",
    label: "执行人",
    multiple: true,
    treeData: [],
    disabledKey: "value",
  },
  {
    formType: "select",
    prop: "assigneeRole",
    label: "执行角色",
    multiple: true,
    options: [],
  },
]);
// 弹框确定按钮

// 提交
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let params = {
        ...state.formData,
        assignee: state.formData.assignee != null && state.formData.assignee.length > 0 ? state.formData.assignee.toString() : "",
        assigneeRole: state.formData.assigneeRole != null && state.formData.assigneeRole.length > 0 ? state.formData.assigneeRole.toString() : "",
      };
      let apiFn = isChangeEvent ? saveSuperEventTemplateTask : addItem;

      apiFn(params)
        .then((res) => {
          resetList();
          close();
          ElMessage.success("操作成功");
          emit("closeDialog");
        })
        .catch(() => {
          close(false);
        });
    } else {
      return false;
    }
  });
}
// 关闭任务
function closeTask() {
  formList[1].isClear = false;
  emit("closeDialog");
}
// 删除任务
function handleDelete(rows) {
  batchDelete().then(() => {
    let apiFn = isChangeEvent ? delSuperTemplateTaskById : delItem;

    apiFn({ id: rows.id }).then(() => {
      ElMessage.success("删除成功");
      resetList();
    });
  });
}
// 修改任务
function handleUpdate(rows) {
  popupBox(rows.id);
  dialogTitle.value = "修改任务";
}
// 获取任务列表
getTaskData(props.templateId);
function getTaskData(id) {
  let templateId = id == 0 ? "" : id;
  // 获取任务
  let apiFn = isChangeEvent ? selectSuperTemplateTaskList : getTaskList;
  apiFn({ templateId: templateId })
    .then((res) => {
      state.tableData = res.data;
    })
    .catch(() => {});
}
// 获取任务详情信息
function getTempTaskInfo(templateTaskId) {
  // 获取任务
  getTempTaskParam({ templateTaskId: templateTaskId })
    .then((res) => {
      if (templateTaskId) {
        state.formData = res.data.bean;
        state.formData.assignee = state.formData.spare3 ? state.formData.spare3.split(",") : "";
        state.formData.assigneeRole = res.data.bean.assigneeRole ? res.data.bean.assigneeRole.split(",") : "";
        formList[1].editorData = state.formData.detail;
      }
    })
    .catch(() => {});
}

function getTempEventTaskInfo(id) {
  if (!id) return;
  selectSuperTemplateTask({ id }).then((res) => {
    state.formData = res.data;

    state.formData.assignee = state.formData.spare3 ? state.formData.spare3.split(",") : "";

    state.formData.assigneeRole = state.formData.assigneeRole ? state.formData.assigneeRole.split(",") : "";

    formList[1].editorData = state.formData.detail;
  });
}

// 获取任务阶段
/*getTaskStage();
async function getTaskStage() {
  let res1 = await getDicts("task_group");
  let res2 = await getDicts("manage_task_group");
  res1.data.forEach((item) => {
    formList[1].options.push({
      label: item.dictLabel,
      value: item.dictValue,
    });
  });
  res2.data.forEach((item) => {
    formList[1].options.push({
      label: item.dictLabel,
      value: item.dictValue,
    });
  });
}*/
getUserTree();
//获取角色和执行人
function getUserTree(params) {
  selectUserTree({}).then((res) => {
    formList[2].treeData = res.data;
  });
}
getRoleTree();
function getRoleTree(params) {
  getAssigneeRoles({}).then((res) => {
    formList[3].options = res.data.map((item) => {
      return {
        value: item.roleId,
        label: item.roleName,
      };
    });
  });
}
</script>
<style scoped lang="scss">
.detailInfo {
  padding: 24px 24px 24px 60px;
  line-height: 24px;
  background: #f9f9f9;
  word-break: break-word;
}
.el-table__expand-icon {
  width: 24px;
}
// :deep .el-table .cell {
//   padding: 0 !important;
// }
</style>
