import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

import { startTask, closeTask, submitTask, redoTask, deleteTask, getLogList, saveAuditEvent } from "@/api/offStandardEvent/event/task";

export default function () {
  async function handlerTask(type, row, isManageTask, isListTab, store) {
    if (type == "del" || type == "close") {
      ElMessageBox.confirm(`确认${type == "del" ? "删除" : "关闭"}任务？`, "警告", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        _api(type, row, isManageTask, isListTab, store);
      });
    } else if (type == "submit" && isListTab) {
      let { data } = await getLogList({ eventTaskId: row.id });

      if (data.rows.length > 0) {
        _api(type, row, isManageTask, isListTab, store);
      } else {
        ElMessage.warning("请先添加日志");

        _goDetail(type, row, isManageTask, isListTab, store);
      }
    } else {
      _api(type, row, isManageTask, isListTab, store);
    }
  }

  function saveAuditFn(params, store, callBack) {
    saveAuditEvent(params).then(() => {
      ElMessage.success("操作成功");
      if (params.status != 2) {
        _update(params.eventTaskId, store); // 误报后删除，不需要刷新
      }

      callBack && callBack();
    });
  }

  return {
    handlerTask,
    saveAuditFn,
  };
}

function _api(type, row, isManageTask, isListTab, store) {
  let apiObj = {
    start: startTask,
    close: closeTask,
    submit: submitTask,
    del: deleteTask,
    redo: redoTask,
  };
  apiObj[type]({
    id: row.id,
  }).then((res) => {
    ElMessage.success("操作成功");
    if (type == "start" && isListTab) {
      _goDetail(type, row, isManageTask, isListTab, store);
    } else if (type == "del") {
      store.commit("offdelTabByName", row.id);
      _update("", store);
    } else if (type == "submit") {
      store.dispatch("offUpdateEventLogList");

      _update(row.id, store);
    } else {
      _update(row.id, store);
    }
  });
}

function _goDetail(type, row, isManageTask, isListTab, store) {
  store.commit("offPushEventTab", {
    name: row.id,
    label: row.title,
    type: "taskDetail",
    isManageTask: isManageTask,
  });
}

function _update(id, store) {
  store.commit("offupdateTaskById", id);
  store.dispatch("offUpdateEventFlag");
}
