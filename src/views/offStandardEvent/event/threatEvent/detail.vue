<template>
  <detail-content v-if="pageStatus"></detail-content>
</template>
<script>
export default {
  name: "OffEventDetail",
  beforeRouteEnter(to, from, next) {
    let update = sessionStorage.getItem("eventId") != to.params.id;

    next((vm) => {
      //漏洞详情返回定位到漏洞tab
      if (update) {
        vm.$store.commit("offInitEventDetail");
      }

      if (to.query.taskId) {
        setTimeout(() => {
          vm.$store.commit("offPushEventTab", {
            name: to.query.taskId,
            label: to.query.taskName,
            type: "taskDetail",
            isManageTask: to.query.isManageTask,
          });
        }, 100);
      }

      if (from.name == "ChangeEvent") {
        vm.$store.commit("offReload");
      }
    });
  },

  beforeRouteLeave(to, form) {
    sessionStorage.setItem("eventId", form.params.id);
  },
};
</script>
<script setup>
import { computed } from "vue";
import detailContent from "./components/detailContent.vue";
import { useStore } from "vuex";
const store = useStore();
let pageStatus = computed(() => {
  return store.state.offEventDetail.pageStatus;
});
setTimeout(() => {
  store.commit("offChangeInToEventPage");
}, 2000);
</script>
<style lang="scss" scoped></style>
