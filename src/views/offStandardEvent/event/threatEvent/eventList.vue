<template>
  <statistics v-if="!isComp" :border-line="isComp" :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '综合服务' }]"></statistics>
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button @click="newlyAdded" class="search-button" v-if="!isComp" v-hasPermi="'event:add'">
      <el-icon :size="12">
        <plus />
      </el-icon>
      添加综合服务
    </el-button>
    <el-button @click="exportList" v-if="!isComp" v-hasPermi="'event:export'">
      <el-icon :size="12">
        <Download />
      </el-icon>
      导出
    </el-button>
  </common-search>
  <xel-table class="event-table" ref="tableRef" :columns="columns" :load-data="getTableData" :default-params="defaultParams">
    <template #title="{ row }">
      <div :class="!isComp ? 'xel-clickable' : ''" @click="toDetail(row)">
        Case #{{ row.eventNo }}-{{ row.titlePrefix }} &nbsp;&nbsp;{{ row.title }}
        <p class="event-tag-box">
          <icon n="icon-a-2" :size="20"></icon>
          <span v-for="tag in row.eventTagList" :key="tag.id">{{ tag.tagName }}</span>
        </p>
      </div></template
    >
    <template #radio="scope" v-if="isComp">
      <el-radio v-model="eventId" :label="scope.row.id"> <span></span></el-radio>
    </template>
    <template #level="scope">
      <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
    </template>
    <template #taskProgress="{ row }">
      <el-progress v-if="row.groupCountTotle != 0" :percentage="(row.groupCount1 / row.groupCountTotle) * 100" :stroke-width="8" status="success">
        <span style="font-size: 12px; color: #555" text>{{ row.groupCount1 }}/{{ row.groupCountTotle }}</span>
      </el-progress>
    </template>
  </xel-table>
</template>
<script setup>
import { initTask, deleteEventTemplate as delItem } from "@/api/offStandardEvent/runscript";

import { getEventList, exportEvent } from "@/api/offStandardEvent/event/eventList";
import { getTagList, getSelectList } from "@/api/getOsEventTagList";
import { getMergeList } from "@/api/workSpace/alert";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, computed, onActivated, onMounted } from "vue";
let firstIn = true;
onMounted(() => {
  setTimeout(() => {
    firstIn = false;
  }, 100);
});
onActivated(() => {
  if (!firstIn) {
    search(false);
  }
});
import { Level_Data } from "@/config/constant";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
import { download } from "@/plugins/request";
let props = defineProps({
  //是否是组件引用
  isComp: {
    type: Boolean,
    default: false,
  },
  //组件引用类型
  compType: {
    type: [String],
    default: "",
  },
});
let getTableData = ref(null);
if (props.isComp) {
  if (props.compType == "workAlert" || props.compType == "mergeEvent") {
    getTableData.value = getMergeList;
  }
} else {
  getTableData.value = getEventList;
}
//可合并事件列表参数
let defaultParams = {};
if (props.compType == "mergeEvent") {
  defaultParams.id = route.params.id;
  defaultParams.spare5 = 1;
}
if (route.query.priority) {
  defaultParams.levelId = route.query.priority * 1;
  /*defaultParams.findTerm = 6;*/
}

let eventId = ref("");
const router = useRouter();
let tableRef = ref();
let dialogTitle = ref("");
let searchState = reactive({
  data: {
    createTimeStr: "",
    templateId: "",
    tagId: "",
    deptName: "",

    levelId: "",
    isClose: "",
    title: "",
  },
  menuData: [
    {
      lable: "工作优先级",
      prop: "levelId",
      options: [],
      dictName: "event_level",
    },
    {
      lable: "服务状态",
      prop: "isClose",
      options: [],
      dictName: "event_close",
    },
  ],
  formList: [
    {
      formType: "daterange",
      type: "datetimerange",
      prop: "applyTimeList",
      label: "创建时间",
      labelWidth: "120px",
      day: false,
      onChange(val) {
        searchState.data.createTimeStr = val[0] + " - " + val[1];
      },
    },

    {
      formType: "select",
      prop: "templateId",
      label: "综合服务项",
      labelWidth: "120px",
      multiple: false,
      filterable: true,
      placeholder: "请选择模板名称",
      seleteCode: {
        code: getSelectList,
        resKey: "list",
        label: "name",
        value: "id",
        params: {},
      },
    },
    {
      formType: "select",
      prop: "tagId",
      label: "标签",
      labelWidth: "120px",
      multiple: false,
      filterable: true,
      seleteCode: {
        code: getTagList,
        resKey: "tagList",
        // 传递取值的字段名
        label: "name",
        value: "id",
        params: {},
      },
    },

    {
      formType: "input",
      prop: "deptName",
      label: "包括的单位",
      placeholder: "请输入单位名称",
      labelWidth: "120px",
    },
    {
      formType: "input",
      prop: "title",
      label: "关键字",
      labelWidth: "120px",
      placeholder: "请输入工作名称、附件名称、报告名称",
    },
  ],
});
getQueryPriority();
//获取路由中的query参数
function getQueryPriority() {
  if (!searchState.data.levelId && route.query.priority) {
    searchState.data.levelId = route.query.priority;
    /*searchState.data.findTerm = 6;*/
    let _location = window.location.href.split("?")[0];
    window.history.replaceState({}, "", _location);
  }
}
let state = reactive({
  levelData: Level_Data,
});
let { formData } = toRefs(state);
let { levelData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    resource: "",
  };
}
// 搜索按钮
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}

function reset() {
  searchState.data = {
    createTimeStr: "",
    templateId: "",
    tagId: "",
    deptName: "",

    levelId: "",
    isClose: "",
    title: "",
  };
  search();
}
// 列表配置项
const columns = [
  {
    hide: !props.isComp,
    prop: "id",
    label: "",
    slotName: "radio",
    width: "80px",
  },
  {
    prop: "title",
    label: "工作名称",
    slotName: "title",
    width: !props.isComp ? ($globalWindowSize == "S" ? 250 : 350) : "",
  },

  {
    prop: "levelId",
    label: "工作优先级",
    slotName: "level",
    width: 100,
  },
  {
    prop: "levelId",
    label: "任务",
    slotName: "taskProgress",

    width: !props.isComp ? ($globalWindowSize == "S" ? 180 : 200) : "",
  },

  {
    prop: "deptName",
    label: "工作开展对象",
  },
  {
    prop: "createName",
    label: "创建人",
    width: 150,
  },
  {
    prop: "createTime",
    label: "创建时间",
    width: 150,
  },
  {
    prop: "groupCount2",
    label: "报告数量",
    width: 90,
  },
  {
    prop: "closeStr",
    label: "服务状态",
    width: 90,
  },
  /*{
    prop: "createName",
    label: "分析师",
    width: 90,
  },*/
  /*{
    prop: "assetsName",
    label: "受影响资产",
    width: !props.isComp ? ($globalWindowSize == "S" ? 90 : "") : "",
  },*/
  /*{
    prop: "spare1",
    label: "可疑对象",
  },*/
];
function toDetail(row) {
  if (!props.isComp) {
    router.push({
      name: "OffEventDetail",
      params: {
        id: row.id,
      },
    }); //路由跳转
  }
}

// 列表操作方法
// 新增按钮
function newlyAdded() {
  router.push({
    name: "OffAddEvent",
    // params: {},
  }); //路由跳转
}

//导出
function exportList() {
  let params = {};
  for (let key in searchState.data) {
    if (searchState.data[key] !== "") {
      params[key] = searchState.data[key];
    }
  }
  download("/system/osEvent/exportEvent", "威胁事件.xlsx", {}, "post", params);
}

defineExpose({
  eventId: computed(() => {
    return eventId.value;
  }),
  eventInfo: computed(() => {
    if (tableRef.value) {
      return tableRef.value.data.find((item) => {
        return item.id == eventId.value;
      });
    } else {
      return {};
    }
  }),
  search,
});
</script>
<style scoped lang="scss">
.upload-button {
  margin-right: 10px;
}
.event-tag-box {
  color: #63729d;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 6px;
  span {
    min-width: 46px;
    min-height: 24px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background: #eef5ff;
    color: #63729d;
    padding-left: 12px;
    padding-right: 12px;
    margin-left: 10px;
    border-radius: $radiusSM;
    transition: all 0.3s;
    margin-bottom: 5px;
  }
}
</style>
<style>
.event-table.el-table tr:hover .event-tag-box span {
  background: #dbe5f5;
}
</style>
