<template>
  <statistics :border-line="false" :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '驳回事件数' }]"></statistics>
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
  </common-search>
  <xel-table ref="tableRef" :columns="columns" :load-data="getTableData"> </xel-table>
  <!-- 弹窗内容 -->
  <xel-dialog :title="查看告警驳回原因" ref="dialogRef" size="small" @close="closeDialog" buttonCancel="关闭" :showSubmit="false">
    <el-form :model="formData" ref="ruleFormRef" label-width="100px" size="mini">
      <el-form-item label="驳回原因:">{{ formData.name }}</el-form-item>
    </el-form>
  </xel-dialog>
</template>
<script setup>
import { initTask, deleteEventTemplate as delItem } from "@/api/analyticalDisposal/runscript";
import { getRejectEventPage as getTableData } from "@/api/event/eventList";
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, onActivated } from "vue";
import { Level_Data } from "@/config/constant";
import { useRouter } from "vue-router";
import { download } from "@/plugins/request";
onActivated(() => {
  search(false);
});
const router = useRouter();
let tableRef = ref();
let searchState = reactive({
  data: {
    name: "",
    levelId: "",
    description: "",
    searchTag: "",
    taskTitle: "",
    taskDetail: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "事件名称",
    },
    {
      formType: "input",
      prop: "rejectName",
      label: "审核人",
    },
    {
      formType: "input",
      prop: "createName",
      label: "创建人",
    },
    {
      formType: "input",
      prop: "rejectReason",
      label: "驳回原因",
    },
    {
      formType: "input",
      prop: "assetsName",
      label: "受影响资产",
    },
  ],
});
let state = reactive({
  levelData: Level_Data,
  formData: {
    name: "",
  },
});
let { formData } = toRefs(state);
let { levelData } = toRefs(state);
function resetFormData() {
  state.formData = {
    name: "",
    resource: "",
  };
}
// 搜索按钮
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
// 弹框
let dialogTitle = ref("");
let dialogRef = ref();
let ruleFormRef = ref();
// 打开弹框
function popupBox() {
  dialogRef.value.open();
}
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "驳回原因",
    required: true,
    disabeld: true,
  },
]);
// 弹框关闭
function closeDialog() {}

// 重置
function reset() {
  searchState.data = {
    name: "",
    levelId: "",
    description: "",
    searchTag: "",
    taskTitle: "",
    taskDetail: "",
  };
  search();
}
// 列表配置项
const columns = [
  {
    prop: "title",
    label: "事件名称",
    click(scope) {
      router.push({
        name: "EventDetail",
        params: {
          id: scope.row.id,
        },
      }); //路由跳转
    },
  },
  {
    prop: "createName",
    label: "创建人",
  },
  {
    prop: "rejectName",
    label: "审核人",
  },
  {
    prop: "rejectTime",
    label: "审核时间",
  },
  {
    prop: "rejectReason",
    label: "驳回原因",
  },
  {
    label: "操作",
    // fixed: "right",
    btnsWidth: "100px",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "view",
        title: "查看",
        onClick(scope) {
          popupBox();
          formData.value.name = scope.row.rejectReason;
        },
      },
    ],
  },
];
defineExpose({
  search,
});
</script>
<style scoped lang="scss">
.upload-button {
  margin-right: 10px;
}
</style>
