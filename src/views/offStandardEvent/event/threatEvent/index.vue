<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <Event ref="eventRef" />
    <!--    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="事件" name="first">
        <Event ref="eventRef" :key="'first'" />
      </el-tab-pane>
      <el-tab-pane label="驳回事件" name="second">
        <RejectList ref="rejectListRef" :key="'second'" />
      </el-tab-pane>
    </el-tabs>-->
  </el-card>
</template>
<script>
export default {
  name: "OffThreatEvent",
};
</script>
<script setup>
import Event from "./eventList.vue";
import RejectList from "./rejectList.vue";
import { ref, reactive, toRefs, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
const route = useRoute();
const router = useRouter();
let activeName = ref("first");
activeName.value = route.query.tabName ? route.query.tabName : "first";

let eventRef = ref();
let rejectListRef = ref();
function handleClick(val) {
  activeName.value = val.paneName;
  if (val.paneName == "first") {
    eventRef.value.search();
  } else {
    rejectListRef.value.search();
  }
}
</script>

<style lang="scss" scoped></style>
