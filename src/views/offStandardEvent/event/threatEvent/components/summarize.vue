<template>
  <mouse-display :playName="'摘要'" @submit="saveBaseInfo" :show-submit="true" :disaboy="!eventBtns.eventUpdate">
    <div class="margin-top20">
      <editComponent ref="baseInfoRef" :edit-info="event"></editComponent>
    </div>
    <template #display>
      <el-form ref="form" label-width="120px" label-position="left" class="base-info-form base-info-box">
        <el-row :gutter="70">
          <el-col :span="12">
            <el-form-item label="标题：">{{ event.title }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作优先级 ：">
              <el-tag :type="Level_Data[event.levelId]">{{ event.levelName }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建人：">{{ event.createName }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作开展时间：">{{ event.beaginTime }} - {{ event.endTime }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签："
              ><el-tag v-for="item in event.eventTagList" :key="item.id"
                ><span class="dobule">{{ item.tagName }}</span></el-tag
              ></el-form-item
            >
          </el-col>
          <el-col :span="12">
            <el-form-item label="参与分析师：">{{ event.analyst }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作开展对象："
              ><span v-for="item in event.eventDeptList" :key="item.deptId" class="title-add">{{ item.deptName }} </span></el-form-item
            >
          </el-col>
        </el-row>
      </el-form>
    </template>
  </mouse-display>
  <mouse-display :playName="'服务描述'" @submit="saveBaseInfo1" :show-submit="true" :disaboy="!eventBtns.eventDescription">
    <div class="margin-top20">
      <el-form :model="consciouData" ref="consciouRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in consciousness" :key="index" v-model="consciouData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </div>
    <template #display>
      <p v-html="event.detail"></p>
    </template>
  </mouse-display>
  <!--  <mouse-display :playName="'事件结论'" @submit="saveBaseInfo2" @close="eventClosed" :show-submit="true" :disaboy="!eventBtns.eventDescription">
    <div class="margin-top20">
      <el-form :model="eventData" ref="consciouRef" label-width="120px" size="mini">
        <xel-form-item v-for="(item, index) in eventsness" :key="index" v-model="eventData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </div>
    <template #display>
      <p class="onp">{{ store.state.offEventDetail.eventDetail.event && store.state.offEventDetail.eventDetail.event.eventDesc ?
          store.state.offEventDetail.eventDetail.event.eventDesc : "" }}</p>
    </template>
  </mouse-display>-->
  <template v-if="store.state.offEventDetail.eventDetail.event && store.state.offEventDetail.eventDetail.event.isClose == 1">
    <div class="title-bottom-line">
      <p>关闭原因</p>
    </div>
    <p class="onp">{{ store.state.offEventDetail.eventDetail.event.closeReason }}</p>
  </template>

  <!-- 关联事件 -->
  <!--  <div class="title-bottom-line">
    <p>关联事件</p>
  </div>
  <xel-table ref="tableRef12" :columns="listColumns" :data="associated" @selection-change="handleSelectionChange" :pagination="false">
    &lt;!&ndash; <template #title="{ row }"> Case #{{ row.eventNo }}-{{ row.titlePrefix }} &nbsp;&nbsp;{{ row.title }} </template> &ndash;&gt;
  </xel-table>
  <xel-pagination ref="paginationRef" :init="false" class="xel-table-pagination" :total="total" @change="changePagination"></xel-pagination>-->

  <!-- 工作要求附件 -->

  <mouse-display :playName="'工作要求附件'" @submit="saveFileFun" :show-submit="true" :disaboy="!eventBtns.eventUpdate">
    <div class="margin-top20">
      <el-form :model="fileData" ref="fileDataRef" label-width="120px" size="mini">
        <xel-form-item
          form-type="upload"
          prop="file"
          label="工作要求附件"
          accept=".txt, .doc, .docx, .pdf, .xlsx"
          @fileList="testResult"
          :required="true"
        />
      </el-form>
    </div>
    <template #display>
      <div style="margin-bottom: 20px">
        <p v-if="event.fileId" style="padding-left: 20px">
          <el-link :underline="false" @click="delFn(event)">
            {{ event.fileName }}
          </el-link>

          <el-icon class="pointer del-file" @click.stop="fileDeleteFun"><delete /></el-icon>
        </p>
        <p style="text-align: center" v-else>
          <span class="no-data" style="font-size: 12px"> 暂无数据 </span>
        </p>
      </div>
    </template>
  </mouse-display>

  <!-- 附件 -->
  <div class="title-bottom-line">
    <p>工作过程附件</p>
  </div>
  <xel-table
    ref="tableRef"
    :columns="columns"
    :load-data="getEventFilePage"
    @selection-change="handleSelectionChange"
    :pageSize="5"
    :default-params="{
      id: route.params.id,
    }"
    :pagination="false"
  >
  </xel-table>
  <div class="title-bot"></div>
  <div class="title-bottom-line">
    <p>服务报告</p>
    <!-- <el-button type="primary" :icon="Edit"></el-button> -->
  </div>
  <xel-table
    ref="tableRef1"
    :columns="onColumns"
    :load-data="selectAlarmReport"
    @selection-change="handleSelectionChange"
    :pageSize="5"
    :default-params="{
      eventId: route.params.id,
    }"
    :pagination="false"
  ></xel-table>

  <!-- 关联事件弹框 -->
  <xel-dialog title="关联要素" ref="dialogRef" size="small" width="1200px" @submit="submitForm" @close="closeDialog">
    <div v-if="assetsList.length > 0 && assetsList[0] !== null">
      <div class="title-bottom-line">
        <p>受影响资产</p>
      </div>
      <xel-table ref="tableRef6" :columns="loopholeColumns" :data="assetsList" :pagination="false"></xel-table>
    </div>
    <div v-if="objectList.length > 0 && objectList[0] !== null">
      <div class="title-bottom-line">
        <p>可疑对象</p>
      </div>
      <xel-table ref="tableRef6" :columns="eventColumns" :data="objectList" :pagination="false"></xel-table>
    </div>
    <div v-if="vulnList.length > 0 && vulnList[0] !== null">
      <div class="title-bottom-line">
        <p>漏洞</p>
      </div>
      <xel-table ref="tableRef7" :columns="earlyColumns" :data="vulnList" :pagination="false"></xel-table>
    </div>
    <template #button>
      <el-button @click="closeValue">关闭</el-button>
    </template>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { download } from "@/plugins/request";
import { downloadUrl } from "@/api/system/download.js";
import {
  getEventDetail,
  saveEditEvent,
  getEventFilePage,
  selectAlarmReport,
  getEventRelevanceList,
  getRelativeList,
  updateEventFile,
} from "@/api/offStandardEvent/event/detail";
import { useRouter, useRoute } from "vue-router";
import mouseDisplay from "@/views/securityAssets/businessAssets/components/mouseDisplay.vue"; //鼠标移入编辑组件
import EditComponent from "./editComponent.vue";
import { loopholeColumns, eventColumns, earlyColumns } from "./relatedElements";
const router = useRouter();
const route = useRoute();
import { ElMessageBox, ElMessage } from "element-plus";
import { Level_Data } from "@/config/constant";

let event = ref({});
let baseInfoRef = ref();
let changeProcess = ref("");
let oneventDesc = ref("");
let tableRef1 = ref();
let tableRef = ref();
let associated = ref([]);
let dialogRef = ref();
import { useStore } from "vuex";
const store = useStore();
let total = ref("");
import updateByTabName from "../mixins/updateByTabName";
updateByTabName(0, () => {
  tableValue();
  eventDetail();
  search();
});
let eventBtns = computed(() => {
  return store.state.offEventDetail.overviewBtns;
});
// watch(
//   () => eventBtns.value.status,
//   () => {
//     getList();
//     eventDetail();
//     search();
//   }
// );
let ass = ref({});
//获取事件详情
function eventDetail() {
  if (!router.name == "OffEventDetail") return;
  getEventDetail({ id: route.params.id }).then((res) => {
    store.commit("offrevisionOverview", {
      eventUpdate: res.data.eventUpdate == "Y", //摘要,
      eventDescription: res.data.eventDescription == "Y", //描述和结论
    });
    store.dispatch("offUpdateEventFlag");

    oneventDesc.value = res.data.eventDesc;
    changeProcess.value = res.data.changeProcess;
    consciousness[0].editorData = consciouData.value.emailDetail = res.data.event.detail;
    // formList[2].editorData = state.formData.detail;

    event.value = res.data.event;
    eventData.value.eventDesc = res.data.event.eventDesc;
    ass.value = {
      id: route.params.id,
      isClose: res.data.event.isClose,
      pageNum: 1,
      pageSize: 10,
    };

    getList(ass.value);

    // getEventRelevanceList(ass.value).then((res) => {
    //   associated.value = res.data.rows;
    //   total.value = res.data.total;
    // });
  });
}
function getList(data) {
  getEventRelevanceList(data).then((res) => {
    associated.value = res.data.rows;
    total.value = res.data.total;
  });
}
// eventDetail(false);
let consciousness = reactive([
  {
    formType: "editor",
    prop: "emailDetail",
    label: "服务描述",
    editorClass: "formEditor", //多个编辑器时，命名不同的名字
    editorData: "",
    isClear: false,
    onEditorValue(val) {
      consciouData.value.emailDetail = val;
    },
  },
]);
let consciouData = ref({
  emailDetail: "",
});
let eventData = ref({
  eventDesc: "",
});
let eventsness = reactive([
  {
    formType: "input",
    label: "事件结论",
    size: "mini",
    // required: true,
    prop: "eventDesc",
    type: "textarea",
    // placeholder:"请输入事件结论"
  },
]);
// 修改
function saveBaseInfo(a, b) {
  let ass = [];
  baseInfoRef.value.formData.eventTagList.forEach((item) => {
    ass.push(item.id);
  });
  let val = {
    title: baseInfoRef.value.formData.title,
    levelId: baseInfoRef.value.formData.levelId,
    beginTimeStr: baseInfoRef.value.formData.createTime[0],
    endTimeStr: baseInfoRef.value.formData.createTime[1],
    deptId: baseInfoRef.value.formData.eventDeptList.toString(),
    tagId: ass.toString(),
    id: route.params.id,
  };
  b();

  let formData = new FormData();
  for (let i in val) {
    let d = typeof val[i] === "object" ? JSON.stringify(val[i]) : val[i];
    if (i === "file") {
      formData.append(i, val[i].raw);
    } else {
      formData.append(i, d === "null" ? "" : d);
    }
  }
  saveEditEvent(formData)
    .then((res) => {
      ElMessage.success("保存成功");
      eventDetail();
      a();
    })
    .catch(() => {
      a(false);
    });
}

/* 附件修改 */
let fileDataRef = ref();
let fileData = ref({});

/* 工作要求附件 */
function testResult(list) {
  if (list[0]) {
    fileData.value.file = list[0];
  } else {
    fileData.value.file = "";
  }
}
function saveFileFun(a, b) {
  fileDataRef.value.validate((valid) => {
    if (valid) {
      b();
      let formData = new FormData();
      let add = [];
      event.value.eventTagList.forEach((item) => {
        add.push(item.id);
      });
      let val = {
        id: route.params.id,
        // 标题
        title: event.value.title,
        // 等级ID
        levelId: event.value.levelId,
        // 开始时间
        beginTimeStr: event.value.beaginTime,
        // 结束时间
        endTimeStr: event.value.createTime,
        // 部门id
        deptId: event.value.deptId,
        // 标签id
        tagId: add.toString(),
      };
      for (let i in val) {
        let d = typeof val[i] === "object" ? JSON.stringify(val[i]) : val[i];
        formData.append(i, d === "null" ? "" : d);
      }
      formData.append("file", fileData.value.file.raw);
      saveEditEvent(formData)
        .then((res) => {
          ElMessage.success("保存成功");
          eventDetail();
          a();
        })
        .catch(() => {
          a(false);
        });
    }
  });
}

const columns = [
  {
    prop: "fileName",
    label: "文件名",
  },
  {
    prop: "createTime",
    label: "时间",
  },
  {
    prop: "type",
    label: "文件类型",
  },
  {
    prop: "size",
    label: "文件大小",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Download",
        title: "下载",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
];
const onColumns = [
  {
    prop: "name",
    label: "报告名称",
  },
  {
    prop: "fileName",
    label: "文件名",
  },
  {
    prop: "createTime",
    label: "提交时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Download",
        title: "下载",
        onClick(scope) {
          delFn(scope.row);
        },
      },
    ],
  },
];
const listColumns = [
  {
    prop: "titlePrefix",
    label: "事件类型",
  },
  {
    prop: "titlePrefix",
    label: "事件编号与名称",
    formatter(row, column) {
      return row.eventNo + "-" + row.title;
    },
  },
  {
    prop: "closeStr",
    label: "分析进度",
  },
  {
    prop: "relevance",
    label: "关联要素",
    click(scope) {
      relatedElements(scope.row.id);
    },
  },
  {
    prop: "createTime",
    label: "时间",
  },
];
//搜索相关
let searchState = reactive({
  data: {
    fileName: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "fileName",
      label: "文件名",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function tableValue() {
  tableRef1.value.reload(true);
}
function reset() {
  searchState.data = {
    fileName: "",
  };
  search();
}
//附件下载方法
function delFn(val) {
  download(downloadUrl + val.fileId, val.fileName, {}, "get");
}

/* 删除文件 */
function fileDeleteFun() {
  ElMessageBox.confirm(`确定删除该工作要求附件？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      updateEventFile(route.params.id).then((res) => {
        ElMessage.success("操作成功");
        eventDetail();
      });
    })
    .catch(() => {});
}
// 修改描述
function saveBaseInfo1(a, b) {
  let add = [];
  event.value.eventTagList.forEach((item) => {
    add.push(item.id);
  });
  let ass = {
    id: route.params.id,
    detail: consciouData.value.emailDetail,
    // 标题
    title: event.value.title,
    // 等级ID
    levelId: event.value.levelId,
    // 开始时间
    beginTimeStr: event.value.beaginTime,
    // 结束时间
    endTimeStr: event.value.createTime,
    // 部门id
    deptId: event.value.deptId,
    // 标签id
    tagId: add.toString(),
  };
  b();

  let formData = new FormData();
  for (let i in ass) {
    let d = typeof ass[i] === "object" ? JSON.stringify(ass[i]) : ass[i];
    if (i === "file") {
      formData.append(i, ass[i].raw);
    } else {
      formData.append(i, d === "null" ? "" : d);
    }
  }

  saveEditEvent(formData)
    .then((res) => {
      ElMessage.success("保存成功");
      eventDetail();
      a();
    })
    .catch(() => {
      a(false);
    });
}
// 修改结论
function saveBaseInfo2(a, b) {
  let add = [];
  event.value.eventTagList.forEach((item) => {
    add.push(item.id);
  });
  let ass = {
    id: route.params.id,
    eventDesc: eventData.value.eventDesc,
    // 标题
    title: event.value.title,
    // 等级ID
    levelId: event.value.levelId,
    // 开始时间
    beginTimeStr: event.value.beaginTime,
    // 结束时间
    endTimeStr: event.value.createTime,
    // 部门id
    deptId: event.value.deptId,
    // 标签id
    tagId: add.toString(),
  };
  b();
  saveEditEvent(ass)
    .then((res) => {
      ElMessage.success("保存成功");
      eventDetail();

      a();
    })
    .catch(() => {
      a(false);
    });
}
// 关联要素
let assetsList = ref([]);
let objectList = ref([]);
let vulnList = ref([]);
function relatedElements(val) {
  let ass = {
    id: val,
    eventId: route.params.id,
  };
  getRelativeList(ass).then((res) => {
    assetsList.value = res.data.assetsList;
    objectList.value = res.data.objectList;
    vulnList.value = res.data.vulnList;
  });
  dialogRef.value.open();
}
// 分页
function changePagination(val) {
  ass.value.pageNum = val.pageNum;
  getList(ass.value);
}
// 关闭按钮
function closeValue() {
  dialogRef.value.close();
}
// 关闭方法
// function closeDialog(){
//     assetsList.value = [];
//     objectList.value = [];
//     vulnList.value = [];
// }
function eventClosed() {
  if (event.value.eventDesc) {
    eventData.value.eventDesc = event.value.eventDesc;
  } else {
    eventData.value.eventDesc = "";
  }
}
</script>

<style lang="scss" scoped>
.del-file {
  position: relative;
  top: 3px;
  margin-left: 30px;
}
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    margin-bottom: 10px;
    padding-bottom: 10px;
  }
}
.el-tag {
  margin-right: 10px;
}
.onp {
  margin: 15px 0 40px 0;
}
.title-bottom-line {
  display: flex;
  justify-content: space-between;
}
.dobule {
  display: inline-block;
  max-width: 300px;
  white-space: normal;
}
.title-bot {
  margin: 20px 0 20px 0;
}
.title-add {
  margin-right: 5px;
}
</style>
