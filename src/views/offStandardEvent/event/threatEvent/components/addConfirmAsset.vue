<!-- 添加待确认资产 -->
<template>
  <xel-dialog title="添加待确认资产" ref="dialogRef" @submit="submit" @close="$emit('close')">
    <el-form ref="assetFormRef" :model="assetFormData" label-width="10em">
      <xel-form-item v-for="item in assetFormList" :key="item.prop" v-model="assetFormData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xel-dialog>
</template>
<script setup>
import { ref, computed, watch, nextTick, onMounted } from "vue";
import { selectPage } from "@/api/securityAssets/assetGroup";
import { saveDomainNotConfirm, saveIpNotConfirm } from "@/api/event/eventList";
import { ElMessage } from "element-plus";

let emits = defineEmits(["sure", "close"]);

onMounted(() => {
  openAssetDialog();
});
//添加，编辑
let dialogRef = ref();
let assetFormRef = ref();
let assetFormData = ref({
  assetType: "1",
  name: "",
  domain: "",
  internetIp: "",
  lanIp: "",
  ip: "",
  group: "",
  reason: "",
  reason2: "",
});
let assetFormList = ref([
  {
    formType: "select",
    prop: "assetType",
    label: "待确认资产类型",
    required: true,
    options: [
      {
        label: "域名URL待确认资产",
        value: "1",
      },
      {
        label: "IP类待确认资产",
        value: "2",
      },
    ],
    onChange(val) {
      assetFormList.value

        .filter((item) => item.itemType)
        .forEach((item) => {
          if (item.itemType == val) {
            item.isShow = true;
          } else {
            item.isShow = false;
          }
        });
    },
  },
  {
    prop: "name",
    label: "资产名称",
    required: true,
  },
  {
    prop: "domain",
    label: "新增域名/子域名",
    itemType: 1,
    required: true,
    vxRule: "URL",
  },
  {
    prop: "internetIp",
    label: "互联网IP地址",
    itemType: 1,
    vxRule: "IP",
  },
  {
    prop: "lanIp",
    label: "局域网IP地址",
    itemType: 1,
    vxRule: "IP",
  },
  {
    isShow: false,
    prop: "ip",
    label: "IP",
    itemType: 2,
    required: true,
    vxRule: "IP",
    onInput(val) {
      assetFormData.value.reason2 = val;
    },
  },
  {
    formType: "select",
    prop: "group",
    label: "资产组",
    filterable: "true",
    options: [],
    multiple: true,
  },
  {
    prop: "reason",
    label: "关联因素",
    required: true,
    itemType: 1,
  },
  {
    isShow: false,
    prop: "reason2",
    label: "关联因素",
    required: true,
    disabled: true,
    itemType: 2,
  },
]);
function openAssetDialog() {
  dialogRef.value.open();
}
function submit(close, load) {
  assetFormRef.value.validate((valid) => {
    if (valid) {
      load();

      let api = null;
      let params = {};
      if (assetFormData.value.assetType == 1) {
        api = saveDomainNotConfirm;
        params = {
          name: assetFormData.value.name,
          internetIp: assetFormData.value.internetIp,
          lanIp: assetFormData.value.lanIp,
          domain: assetFormData.value.domain,
          domainGroupIds: assetFormData.value.group.join(),
        };
      } else {
        api = saveIpNotConfirm;
        params = {
          name: assetFormData.value.name,
          ip: assetFormData.value.ip,
          ipGroupIds: assetFormData.value.group.join(),
        };
      }
      api(params)
        .then(({ data }) => {
          ElMessage.success("操作成功");
          let submitData = {
            assetTypeCode: assetFormData.value.assetType == 1 ? 3 : 4,
            id: data.assetsId,
            name: assetFormData.value.name,
            reason: assetFormData.value.assetType == 1 ? assetFormData.value.reason : assetFormData.value.reason2,
            assetsGroupName: data.groupName,
            assetType: "待确认资产",
            domain: assetFormData.value.domain,
            ips:
              assetFormData.value.assetType == 1
                ? [assetFormData.value.internetIp, assetFormData.value.lanIp].filter((item) => item).join("，")
                : assetFormData.value.ip,
          };
          emits("sure", "addConfirm", submitData);

          close();
        })
        .catch(() => {
          close(false);
        });
    }
  });
}
//获取资产组
selectPage({ pageNum: 1, pageSize: 100 }).then((res) => {
  assetFormList.value[6].options = res.data.rows.map((item) => {
    return {
      value: item.id,
      label: item.groupName,
    };
  });
});
</script>

<style lang="scss" scoped></style>
