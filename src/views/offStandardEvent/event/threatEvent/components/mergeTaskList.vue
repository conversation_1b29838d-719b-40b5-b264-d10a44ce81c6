<template>
  <ul class="event-task-list">
    <li v-for="(item, index) in taskList" :key="index">
      <p class="title-bottom-line" v-if="taskList.length > 1">
        <span class="color-rev">事件{{ index == 0 ? "A" : "B" }}：</span>
        Case #{{ item.eventNo }}-{{ item.titlePrefix }} &nbsp;&nbsp; {{ item.title }}
      </p>
      <div class="event-task-title">已完成任务携带</div>
      <div v-for="(task, _index) in item.taskGroupList.filter((t) => t.spare3 == 'Y')" :key="_index" class="event-task-item">
        【{{ task.taskGroupText }}】 {{ task.title }}
      </div>
      <div class="event-task-title">未完成任务携带选择</div>
      <el-checkbox-group v-model="item.selected">
        <el-checkbox
          v-for="(task, _index) in item.taskGroupList.filter((t) => t.spare3 == 'N')"
          :key="_index"
          :label="task.id"
          class="event-task-item"
        >
          【{{ task.taskGroupText }}】{{ task.title }}</el-checkbox
        >
      </el-checkbox-group>
    </li>
  </ul>
</template>
<script setup>
import { ref, computed } from "vue";
import { getMergeEventData, initChangeEvent } from "@/api/event/detail.js";

let props = defineProps({
  type: {
    type: String,
    default: "",
  }, // merge 事件合并  change事件升级
});

//任务
let taskList = ref([]);
function getTaskList(id) {
  taskList.value = [];
  if (props.type == "merge") {
    getMergeEventData({
      eventId: id,
    }).then(({ data }) => {
      taskList.value.push({
        title: data.eventTitle1,
        eventNo: data.eventNo1,
        titlePrefix: data.titlePrefix1,
        taskGroupList: data.taskGroupList1,
        selected: [],
      });
      taskList.value.push({
        title: data.eventTitle2,
        eventNo: data.eventNo2,
        titlePrefix: data.titlePrefix2,
        taskGroupList: data.taskGroupList2,
        selected: [],
      });
    });
  } else if (props.type == "change") {
    initChangeEvent({ eventId: id }).then(({ data }) => {
      taskList.value.push({
        taskGroupList: data.taskGroupList,
        selected: [],
      });
    });
  }
}

defineExpose({
  getTaskList,
  taskList: computed(() => {
    return taskList.value;
  }),
});
</script>

<style lang="scss" scoped>
.event-task-list {
  margin-bottom: 10px;
}
</style>
