<template>
  <el-form :model="formData" ref="ruleFormRef" label-width="120px" size="mini" class="form-wrapper">
    <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    <div class="footer" v-if="!hideBtn">
      <el-button type="primary" :loading="loading" @click="submitForm">创建任务</el-button>
      <el-button round @click="resetFormData">取消</el-button>
    </div>
  </el-form>
</template>
<script setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import { getAssigneeRoles, selectUserTree } from "@/api/offStandardEvent/event/eventList";
import { useStore } from "vuex";
import { v4 as uuidv4 } from "uuid";

const store = useStore();
let props = defineProps({
  // 每个页面需要的额外参数
  otherParams: {
    type: Object,
    defaultefault: () => {
      return {};
    },
  },
  //获取数据的方法
  addItem: {
    type: Function,
    required: true,
  },
  getTaskInfo: {
    type: Function,
  },
  dictName: {
    type: String,
    default: "task_group",
  },
  hideBtn: {
    type: Boolean,
    default: false,
  },
});
let emit = defineEmits(["close"]);
let state = reactive({
  formData: {
    title: "",
    taskGroup: "",
    assignee: [],
    assigneeRole: [],
    detail: "",
  },
});
let { formData } = toRefs(state);
let ruleFormRef = ref();

// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "title",
    label: "任务名称",
    required: true,
  },
  /*{
    formType: "select",
    prop: "taskGroup",
    label: "任务阶段",
    required: true,
    dictName: props.dictName,
  },*/
  {
    formType: "editor",
    prop: "detail",
    label: "任务要求",
    required: true,
    editorClass: "formEditor" + uuidv4(), //多个编辑器时，命名不同的名字
    editorData: "",
    isClear: false,
    onEditorValue(val) {
      state.formData.detail = val;
    },
  },
  {
    isShow: !store.state.roles.includes("permission:one"),
    formType: "tree",
    prop: "assignee",
    label: "执行人",
    multiple: true,
    treeData: [],
    disabledKey: "value",
  },
  /*{
    isShow: !store.state.roles.includes("permission:one"),
    formType: "tree",
    prop: "assigneeRole",
    label: "执行角色",
    multiple: true,
    treeData: [],
  },*/
]);
onMounted(() => {
  //   获取分析人，分析角色树
  getUserTree();
  /*getRoleTree();*/
});
function getUserTree(params) {
  selectUserTree({}).then((res) => {
    formList[2].treeData = res.data;
  });
}
function getRoleTree(params) {
  getAssigneeRoles({}).then((res) => {
    formList[3].treeData = res.data.map((item) => {
      return {
        id: item.roleId,
        label: item.roleName,
      };
    });
  });
}
//表单重置
function resetFormData() {
  state.formData = {
    title: "",
    taskGroup: "",
    assignee: [],
    assigneeRole: [],
    detail: "",
  };
  formList[1].editorData = "";
  formList[1].isClear = true;
  emit("close", "task");
}
let loading = ref(false);
// 新增，更新
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      let params = {
        ...state.formData,
        assignee: state.formData.assignee != null && state.formData.assignee.length > 0 ? state.formData.assignee.toString() : "",
        assigneeRole: state.formData.assigneeRole != null && state.formData.assigneeRole.length > 0 ? state.formData.assigneeRole.toString() : "",
        ...props.otherParams,
      };
      props
        .addItem(params)
        .then((res) => {
          resetFormData();
          emit("close", null, true);
          loading.value = false;
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
}
// 获取任务详情信息
function getTempTaskInfo(templateTaskId) {
  // 获取任务
  props
    .getTaskInfo({ templateTaskId: templateTaskId })
    .then((res) => {
      //   formList[3].treeData = res.data.userTree;
      //   formList[4].treeData = res.data.roles.map((res) => {
      //     return {
      //       id: res.roleId,
      //       label: res.roleName,
      //     };
      //   });
      if (templateTaskId) {
        state.formData = res.data.bean;
        state.formData.assignee = state.formData.spare3;
        formList[1].editorData = state.formData.detail;
      }
    })
    .catch(() => {});
}

defineExpose({
  submitForm,
});
</script>

<style lang="scss" scoped>
.footer {
  margin-bottom: 20px;
  float: right;
}
</style>
