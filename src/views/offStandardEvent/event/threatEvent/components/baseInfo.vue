<template>
  <div class="margin-top30" :class="{ ishidden: eventBtns.isReject }">
    <event-name>
      <el-button size="mini" v-if="eventBtns.closeFlag" v-hasPermi="'event:close'" @click="handleEvent('close')">
        <el-icon><close-bold /></el-icon>
        关闭</el-button
      >
      <!--      <el-button size="mini" v-if="eventBtns.rejectFlag" v-hasPermi="'event:reject'" @click="handleEvent('reject')">
        <icon n="icon-fanhui"></icon>
        驳回</el-button
      >-->
      <el-button size="mini" v-if="eventBtns.eventDelete" v-hasPermi="'alarmReport:delete'" @click="handleEvent('delete')">
        <el-icon><delete /></el-icon>
        删除</el-button
      >
      <el-button size="mini" v-if="eventBtns.flg" v-hasPermi="'event:audit'" @click="handleEvent('verify')">
        <el-icon><circle-check /></el-icon>
        审核
      </el-button>
      <!--      <el-button size="mini" v-if="eventBtns.mergeFlg" v-hasPermi="'event:merge'" @click="handleEvent('merge')">
        <el-icon><connection /></el-icon>
        综合服务合并
      </el-button>-->
      <!--      <el-button size="mini" v-if="eventBtns.changeFlg" v-hasPermi="'event:change'" @click="handleEvent('change')">
        <el-icon><refresh /></el-icon>
        综合服务升级
      </el-button>-->

      <!-- 新增 - 综合服务变更 -->
      <!--      <el-button size="mini" v-if="eventBtns.changeFlg" v-hasPermi="'event:change'" @click="handleEvent('alteration')">
        <el-icon><refresh /></el-icon>
        综合服务变更
      </el-button>-->
    </event-name>
  </div>
  <!-- 驳回弹框 -->
  <xel-dialog :title="titleName" ref="dialogRef" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleFormRef" label-width="90px" size="mini">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xel-dialog>
  <!--审核综合服务  -->
  <xel-dialog title="审核综合服务" ref="examineRef" size="small" @submit="examineForm" @close="examineDialog">
    <el-form :model="state.forMexamine" ref="rulexamine" label-width="120px" size="mini">
      <xel-form-item
        v-for="(item, index) in mexaminemList"
        :key="index"
        v-model="state.forMexamine[item.prop]"
        v-bind="item"
        @input="(val) => changeReward(val, item.prop)"
      ></xel-form-item>
    </el-form>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { postRejectEvent, postCloseEvent, saveEventAudit, deleteEvent } from "@/api/offStandardEvent/event/detail";
import eventName from "./eventName.vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";

const store = useStore();
let dialogRef = ref();
let ruleFormRef = ref();
let examineRef = ref();
let rulexamine = ref();
let titleName = ref("驳回综合服务");

let eventBtns = computed(() => {
  return store.state.offEventDetail.eventBtns;
});

store.dispatch("offUpdateEventFlag");

function handleEvent(type) {
  switch (type) {
    case "close":
      closeEvent();
      break;
    case "reject":
      rejectEvent();
      break;
    case "verify":
      verifyEvent();
      break;
    case "change":
      toChange(type);
      break;
    case "merge":
      toMerge();
      break;
    case "delete":
      tobreak();
      break;
    case "alteration":
      toChange(type);
      break;
  }
}
// 更新状态
function _update(id, store) {
  store.commit("offUpdateTaskById", id);
  store.dispatch("offUpdateEventFlag");
}

function closeEvent() {
  titleName.value = "关闭综合服务";
  formList[0].label = "关闭原因";
  /*formList[1].isShow = true;*/
  dialogRef.value.open();
}

function rejectEvent() {
  titleName.value = "驳回综合服务";
  formList[0].label = "驳回原因";
  dialogRef.value.open();
}

function verifyEvent() {
  examineRef.value.open();
}

function toMerge() {
  router.push({
    name: "MergeEvent",
    params: {
      id: route.params.id,
    },
  });
}

/* 综合服务变更与升级 - 按钮综合服务 */
function toChange(type) {
  /*新增 -  原综合服务升级 */
  if (type === "change") {
    router.push({
      name: "UpgradesEvent",
      params: {
        id: route.params.id,
      },
    });
  } else {
    router.push({
      name: "AlterationEvent",
      params: {
        id: route.params.id,
      },
    });
  }
}
// 删除按钮
function tobreak() {
  ElMessageBox.confirm("确认删除该综合服务吗？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteEvent({ id: route.params.id }).then(() => {
      _update(route.params.id, store);
      store.commit("closeCurrentTab");
      router.push({ name: "OffThreatEvent" });
      ElMessage({
        message: "删除成功",
        type: "success",
      });
    });
  });
}
//新打开，关闭页签示例
// setTimeout(() => {
//   store.commit("pushEventTab", {
//     name: 222,
//     label: "新页签",
//     type: "suspiciousDetail",
//   });
// }, 1000);
// setTimeout(() => {
//   store.commit("delTabByName", 111);
// }, 2000);
let state = reactive({
  formData: {
    rejectReason: "",
    eventDesc: "",
  }, //新增编辑表单
  forMexamine: {
    reward: "",
  },
});
let formList = reactive([
  {
    formType: "input",
    size: "mini",
    required: true,
    prop: "rejectReason",
    label: "",
    type: "textarea",
    rows: 6,
    maxLength: "500",
  },

  /*{
    formType: "input",
    size: "mini",
    required: true,
    prop: "eventDesc",
    label: "综合服务结论",
    type: "textarea",
    rows: 6,
    isShow: false,
    maxLength: "500",
  },*/
]);
let mexaminemList = reactive([
  {
    formType: "number",
    size: "mini",
    required: true,
    prop: "reward",
    label: "评分",
    min: "-10000",
    max: "10000",
  },
]);
let { formData } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  /*formList[1].isShow = false;*/
  state.formData = {
    rejectReason: "",
    eventDesc: "",
  };
  state.forMexamine = {
    reward: "",
  };
}
function closeDialog() {
  resetFormData();
  ruleFormRef.value.resetFields();
}
function examineDialog() {
  resetFormData();
  rulexamine.value.resetFields();
}
// 驳回弹框提交
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (titleName.value == "驳回综合服务") {
        let ass = {
          id: route.params.id,
          rejectReason: state.formData.rejectReason,
        };
        postRejectEvent(ass).then((res) => {
          _update(route.params.id, store);
          store.commit("closeCurrentTab");
          router.push({ name: "OffThreatEvent" });
          ElMessage({
            message: "操作成功",
            type: "success",
          });
        });
      } else {
        let ass = {
          id: route.params.id,
          closeReason: state.formData.rejectReason,
          eventDesc: state.formData.eventDesc,
        };
        postCloseEvent(ass).then((res) => {
          dialogRef.value.close();
          _update(route.params.id, store);
        });
      }
    }
  });
}
// 审核综合服务提交
function examineForm() {
  rulexamine.value.validate((valid) => {
    if (valid) {
      let val = {
        id: route.params.id,
        reward: state.forMexamine.reward,
      };
      saveEventAudit(val).then((res) => {
        _update(route.params.id, store);
        examineRef.value.close();
      });
    }
  });
}

function changeReward(value, id) {
  if (value > 10000 || value < -10000) {
    state.forMexamine[id] = null;
    ElMessage.warning("请输入-10000 到 10000之间的整数");
  }
  if (value) {
    nextTick(() => {
      state.forMexamine[id] = parseInt(value);
    });
  }
}
</script>

<style lang="scss" scoped></style>
