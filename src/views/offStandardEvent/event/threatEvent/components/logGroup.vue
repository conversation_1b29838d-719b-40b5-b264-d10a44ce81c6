<!-- 日志详情 -->
<template>
  <el-collapse v-if="list.length > 0" :modelValue="list.map((item) => item.id)">
    <el-collapse-item v-for="item in list" :key="item.id" :name="item.id">
      <template #title>
        <p class="logItem">
          <span class="titleName">
            <span>
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-chuangjianzhe"></use>
              </svg>
            </span>
            <span>执行者：</span>
            <span>{{ item.createName }}</span>
          </span>
          <span class="titleTime">
            <span>
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-date"></use>
              </svg>
            </span>
            <span>执行日期：</span>
            <span>{{ item.createTime }}</span>
          </span>
          <span class="pull-right log-btns" v-if="canEdit(item)">
            <el-icon class="pointer" @click.stop="openEditLog(item)"><Edit /></el-icon>
            <el-icon class="pointer" @click.stop="delLog(item)"><Delete /></el-icon>
          </span>
        </p>
      </template>
      <div v-html="item.detail" style="word-break: break-all"></div>
      <div v-if="item.fileList.length > 0">
        <div style="font-weight: bold; margin-top: 10px">文件</div>
        <p class="fileItem" v-for="file in item.fileList" :key="file.fileId">
          <a class="pointer" @click="downLoadFile(file.fileId, file.fileName)">{{ file.fileName }}</a>
          <el-icon v-if="canEdit(item)" class="pointer del-file" @click.stop="delFile(file)"><delete /></el-icon>
        </p>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>
<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { useStore } from "vuex";
const store = useStore();
import { downloadUrl } from "@/api/system/download.js";
import { download } from "@/plugins/request";

let props = defineProps({
  list: {
    type: Array,
    default() {
      return [];
    },
  },
  showEdit: {
    type: Boolean,
    default: false,
  },
  canUpdateLog: {
    type: Boolean,
    default: false,
  },
});

let emits = defineEmits(["delFile", "openEditLog", "delLog"]);

//可编辑，删除权限  canUpdateLog == “y" && 日志创建人是登录用户
function canEdit(item) {
  return props.showEdit && props.canUpdateLog && store.state.userInfo && store.state.userInfo.userId == item.createBy;
}

// 文件下载
function downLoadFile(fileId, fileName) {
  download(downloadUrl + fileId, fileName, {}, "get");
}

function delFile(item) {
  emits("delFile", item);
}
function openEditLog(item) {
  emits("openEditLog", item);
}

function delLog(item) {
  emits("delLog", item);
}
</script>

<style lang="scss" scoped>
:deep(.el-collapse-item__header) {
  display: block;
  border-bottom: 1px solid #ebeef5;
  background: $bgColor;
  .el-collapse-item__arrow {
    float: left;
    width: 30px;
    margin-top: 16px;
    text-align: center;
    &::after {
      clear: both;
      content: "";
    }
  }
  .logItem {
    float: right;
    text-align: left;
    width: calc(100% - 40px);
    & > span {
      font-size: 14px;
      display: inline-block;
      line-height: 48px;
      margin-right: 20px;
      color: $fontColorSoft;
      & > span:nth-child(1) {
        font-size: 20px;
        line-height: 20px;
        float: left;
        margin-top: 13px;
      }
      & > span:nth-child(3) {
        color: #559cf6;
        &.reward {
          padding: 2px 7px;
          border-radius: 22px;
          background-color: #3498db;
          border-color: #3498db;
          color: #ffffff;
        }
      }
      & > span {
        margin-right: 5px;
      }
    }
  }
}
:deep(.el-collapse-item__wrap) {
  width: 100%;
}
:deep(.el-collapse-item__content) {
  padding: 10px 20px;
  .auditDetail {
    > span {
      float: left;
      &:nth-child(1) {
        width: 80px;
      }
      &:nth-child(2) {
        width: calc(100% - 80px);
      }
    }
  }
}
.log-btns {
  padding-right: 20px;
  .pointer {
    margin-left: 15px;
    font-size: 16px;
    color: #504e4e;
    transition: all 0.1s;
    opacity: 0.6;
    &:hover {
      opacity: 1;
    }
  }
}
.del-file {
  margin-left: 20px;
  transform: translateY(3px);
  opacity: 0.6;
  &:hover {
    opacity: 1;
  }
}
</style>
