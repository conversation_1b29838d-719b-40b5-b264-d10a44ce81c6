<template>
  <el-card class="add-event-wrapper">
    <div v-show="taskLength0">
      <h3 class="conH3Tit" v-if="addType !== 'WorkbenchAlert'">{{ "" || ($route.meta && $route.meta.title) }}</h3>
      <AddBasicEvent
        ref="assetBasicRef"
        :addType="addType"
        :interface="getTemplateById"
        @parentFn="getChoseTemplateId"
        @returnTempList="resetTemplateId"
      ></AddBasicEvent>
      <!-- 受影响资产 -->
      <div class="baseInfo assetInfo" v-if="templateId != '未选'">
        <div class="title">
          <p>工作开展资产</p>
          <div class="btn">
            <el-button @click="associateAsset" class="search-button">
              <el-icon :size="12">
                <plus />
              </el-icon>
              关联其它已有资产
            </el-button>
            <el-button @click="openAddConfirmAsset" class="search-button">
              <el-icon :size="12">
                <plus />
              </el-icon>
              添加待确认资产
            </el-button>
          </div>
        </div>

        <xel-table ref="assetTableRef" :columns="assetColumns" :data="assetList" :pagination="false"> </xel-table>
      </div>
      <!-- 可疑对象 -->
      <!--      <div class="baseInfo assetInfo" v-if="templateId != '未选'">
        <div class="title">
          <p>可疑对象</p>
          <div class="btn">
            <el-button @click="addSuspectObj" class="search-button">
              <el-icon :size="12">
                <plus />
              </el-icon>
              添加其它可疑对象
            </el-button>
          </div>
        </div>
        <div v-if="addType === 'WorkbenchAlert'">
          <div class="addOtherObj text-right" v-if="alertAddEvent.cdstip !== '' && cdstip">
            <p class="pull-left">
              <span>
                <el-icon color="#fe992f"><question-filled /></el-icon>
              </span>
              <span
                >当前告警目的IP (<span style="color: #9ba3bc">{{ alertAddEvent.cdstip }}</span
                >) 是否添加为可疑对象？
              </span>
            </p>
            <el-button type="primary" @click="addSuspiciousObject(alertAddEvent.cdstip, 'cdstip')">添加</el-button>
            <el-button @click="cdstip = false">关闭</el-button>
          </div>
          <div class="addOtherObj text-right" v-if="alertAddEvent.csrcip !== '' && csrcip">
            <p class="pull-left">
              <span>
                <el-icon color="#fe992f"><question-filled /></el-icon>
              </span>
              <span
                >当前告警源IP (<span style="color: #9ba3bc">{{ alertAddEvent.csrcip }}</span
                >) 是否添加为可疑对象？
              </span>
            </p>
            <el-button type="primary" @click="addSuspiciousObject(alertAddEvent.csrcip, 'csrcip')">添加</el-button>
            <el-button @click="csrcip = false">关闭</el-button>
          </div>
        </div>
        <xel-table ref="objRef" :columns="objColumns" :data="objList" :pagination="false"> </xel-table>
      </div>-->
      <div class="footer" v-if="templateId != '未选'">
        <el-button v-if="addType !== 'WorkbenchAlert' || templateTaskId !== ''" type="primary" round @click="saveEvent" :loading="createLoading"
          >创建</el-button
        >
        <el-button v-if="addType === 'WorkbenchAlert' && templateTaskId == ''" type="primary" round @click="alertEventAddTask">下一步</el-button>
        <el-button round @click="cancelEvent">取消</el-button>
      </div>
      <!-- 任务基础信息 -->
      <div class="baseInfo assetInfo" v-if="showTask">
        <div class="title">
          <p>任务基础信息</p>
        </div>
        <AddTask @close="closeComponent"></AddTask>
      </div>
      <!-- 关联资产 -->
      <AddAsset
        ref="addAssetRef"
        v-if="showAsset"
        @close="closeComponent"
        @closeDialog="showAsset = false"
        addEvent="addEvent"
        :assetList="assetList"
      ></AddAsset>
      <!-- 添加待确认资产 -->
      <addComfirmAsset v-if="showConfirmAsset" @sure="closeComponent" @close="showConfirmAsset = false"></addComfirmAsset>
      <!-- 可疑对象 -->
      <AddObj v-if="showObj" @close="closeComponent" :allEvents="state.objList"></AddObj>
    </div>
    <!-- 模板无任务时 下一步添加任务 -->
    <div v-show="!taskLength0">
      <add-event-task v-if="alertAddTask" ref="addEventTask"></add-event-task>
      <div class="text-right">
        <el-button @click="taskLength0 = true">上一步</el-button>
        <el-button type="primary" :loading="loading" @click="saveEvnetTask">创建</el-button>
        <el-button @click="cancelEvent">取消</el-button>
      </div>
    </div>
  </el-card>
</template>
<script>
export default {
  name: "OffAddEvent",
};
</script>
<script setup>
import { getTemplateById } from "@/api/offStandardEvent/runscript";
import AddBasicEvent from "./addBasicEvent.vue";
import AddAsset from "./addAsset.vue";
import AddTask from "./addTask.vue";
import AddObj from "./addObj.vue";
import { ref, reactive, onMounted, toRefs, nextTick, watch } from "vue";
import { saveEvent as addItem } from "@/api/offStandardEvent/event/eventList";
import { Level_Data } from "@/config/constant";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { parseTime } from "@/utils/ruoyi";
// 获取模板任务数
import { getEventTemplateTaskSize } from "@/api/workSpace/event";
// 工作台告警提报事件模板有任务的保存方法
import { alertsaveEvent, saveEventAndTask } from "@/api/workSpace/event";
// 告警提报事件  创建任务
import AddEventTask from "@/views/workSpace/components/addEventTask.vue";
//添加待确认资产
import addComfirmAsset from "./addConfirmAsset.vue";
import { useStore } from "vuex";

const store = useStore();
const route = useRoute();
const router = useRouter();
let addAssetRef = ref();
let props = defineProps({
  type: {
    type: [String],
    default: "",
  },
  addType: {
    type: String,
    default: "",
  },
  // 工作台告警提报事件
  alertAddEvent: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let assetBasicRef = ref();
let objRef = ref();
let ruleFormRef = ref();
let assetTableRef = ref();
let loading = ref(false);
let state = reactive({
  templateId: "未选",
  levelData: Level_Data,
  formData: {
    templateId: "",
    titlePrefix: "",
    title: "",
    tagList: [],
    levelId: null,
    detail: "",
    thatTime: [],
    endTimeStr: "",
    beginTimeStr: "",
    assetsInfos: "",
    suspiciousInfos: "",
    deptId: "",
  },
  taskFormData: {}, //任务
  assetList: [], //资产数据
  objList: [],
});
let showAsset = ref(false);
let showTask = ref(false);
let showObj = ref(false);
let { templateId, taskFormData, assetList, objList } = toRefs(state);
// 受影响资产
const assetColumns = [
  {
    prop: "name",
    label: "资产名称",
  },
  {
    prop: "assetsGroupName",
    label: "所属资产组",
  },
  {
    prop: "assetType",
    label: "资产类型",
  },
  {
    prop: "domain",
    label: "系统入口",
    formatter(val) {
      return val.domain ? val.domain : "-";
    },
  },
  {
    prop: "ips",
    label: "IP",
  },
  {
    prop: "reason",
    label: "关联原因",
    formatter(scope) {
      return scope.reason.replaceAll("#_ssp_#", ",");
    },
  },

  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Delete",
        title: "删除",
        onClick(scope) {
          state.assetList.splice(scope.$index, 1);
        },
      },
    ],
  },
];
// 可疑对象
const objColumns = [
  {
    prop: "type",
    label: "类型",
  },
  {
    prop: "content",
    label: "值/文件名",
  },
  {
    prop: "createTimeStr",
    label: "时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "Delete",
        title: "删除",
        onClick(scope) {
          state.objList.splice(scope.$index, 1);
          if (scope.row.suspicious) {
            if (scope.row.suspicious === "cdstip") {
              cdstip.value = true;
            } else if (scope.row.suspicious === "csrcip") {
              csrcip.value = true;
            }
          }
        },
      },
    ],
  },
];

// 关联其它资产
function associateAsset() {
  showAsset.value = true;
}

// 添加可疑对象
function addSuspectObj() {
  showObj.value = true;
}
// 创建事件
let createLoading = ref(false);
function saveEvent() {
  let baseInfo = {};
  // 基础信息
  assetBasicRef.value
    .getBasicInfo()
    .then((data) => {
      if (data) {
        /* 工作开展时间 */
        if (!data.endTimeStr || !data.beginTimeStr) {
          ElMessage.info("工作开展时间不能为空");
          return false;
        }
        /* 工作开展资产 */
        /*if(state.assetList.length === 0) {
          ElMessage.info("请至少关联一个资产");
          return false;
        }*/

        createLoading.value = true;
        state.formData = data;

        // 可疑对象
        state.formData.suspiciousInfos = state.objList.map((item) => item["infoStr"]).join("@_assets_@");
        //   受影响资产
        // 1、多个受影响资产之间使用@_assets_@分割
        // 2、资产内部：资产id、资产类型、关联要素使用@_ssp_@分割
        // 3、资产内部多个关联要素使用#_ssp_#分割
        let assetsArr = [];
        state.assetList.forEach((dv, di) => {
          assetsArr.push(dv.id + "@_ssp_@" + dv.assetTypeCode + "@_ssp_@" + dv.reason);
        });
        state.formData.assetsInfos = assetsArr.join("@_assets_@");
        let params = {
          ...state.formData,
          templateId: state.templateId ? state.templateId : "white",
        };
        params.thatTime = params.thatTime[0] + " - " + params.thatTime[1];

        if (props.addType === "WorkbenchAlert") {
          params.firstTaskId = templateTaskId.value;
          params.alertId = props.alertAddEvent.alertId;
          params.sourceId = props.alertAddEvent.sourceId;
          params.eventTaskCreateId = props.alertAddEvent.firstTaskId;
          params.samplingRecordId = props.alertAddEvent.samplingRecordId;
          params.handleFrom = props.alertAddEvent.handleFrom;
          delete params.id;

          alertsaveEvent(params)
            .then((res) => {
              ElMessage.success("操作成功");
              store.commit("closeCurrentTab");
              store.commit("offInitEventDetail");
              router.push({
                name: "OffEventDetail",
                params: {
                  id: res.eventId,
                },
                query: {
                  isReset: true,
                },
              });
            })
            .finally(() => {
              createLoading.value = false;
            });
        } else {
          delete params.id;
          /*delete params.tagList;*/
          delete params.params;

          let formData = new FormData();
          for (let i in params) {
            let d = typeof params[i] === "object" ? JSON.stringify(params[i]) : params[i];
            if (i === "file") {
              formData.append(i, params[i].raw);
            } else {
              formData.append(i, d === "null" ? "" : d);
            }
          }
          addItem(formData)
            .then((res) => {
              ElMessage.success("操作成功");
              cancelEvent();
              store.commit("closeCurrentTab");
            })
            .finally(() => {
              createLoading.value = false;
            });
        }
      }
    })
    .catch(() => {
      scrollToUnValid();
    });
}
function scrollToUnValid() {
  setTimeout(() => {
    let errorItem = document.querySelector(".add-event-wrapper .el-form-item.is-error");
    if (errorItem) {
      // ElMessage.warning("表单验证未通过");
      errorItem.scrollIntoView({ behavior: "smooth" });
    }
  }, 500);
}
// 取消添加事件
let alertAddTask = ref(true);
function cancelEvent(params) {
  if (props.addType === "WorkbenchAlert") {
    assetBasicRef.value.returnList();
    taskLength0.value = true;
    alertAddTask.value = false;
    setTimeout(() => {
      alertAddTask.value = true;
    }, 500);
    emits("toParentDialogTItle", "选择事件模板");
  } else {
    assetBasicRef.value.returnList();
    router.push({
      name: "OffThreatEvent",
    }); //路由跳转
  }
}
// 关闭子组件
function closeComponent(childType, data) {
  //
  if (childType == "obj") {
    showObj.value = false;
    if (data && data.length > 0) {
      data.forEach((item) => {
        state.objList.push({ ...item });
      });
    }
  } else if (childType == "task") {
    showTask.value = false;
  } else if (childType === "asset") {
    if (data) {
      state.assetList.push(data);
    }
    nextTick(() => {
      // // 刷新各关联列表
      addAssetRef.value.businessRef ? addAssetRef.value.businessRef.getListData() : null;
      addAssetRef.value.basicRef ? addAssetRef.value.basicRef.getListData() : null;
      addAssetRef.value.terminalRef ? addAssetRef.value.terminalRef.getListData() : null;
      addAssetRef.value.confirmRef ? (addAssetRef.value.confirmRef.urlRef ? addAssetRef.value.confirmRef.urlRef.getListData() : null) : null;
      addAssetRef.value.confirmRef ? (addAssetRef.value.confirmRef.ipRef ? addAssetRef.value.confirmRef.ipRef.getListData() : null) : null;
    });
  } else if (childType === "addConfirm") {
    showConfirmAsset.value = false;
    if (data) {
      state.assetList.push(data);
    }
    nextTick(() => {
      // 刷新各关联列表
      addAssetRef.value.businessRef ? addAssetRef.value.businessRef.getListData() : null;
      addAssetRef.value.basicRef ? addAssetRef.value.basicRef.getListData() : null;
      addAssetRef.value.terminalRef ? addAssetRef.value.terminalRef.getListData() : null;
      addAssetRef.value.confirmRef ? (addAssetRef.value.confirmRef.urlRef ? addAssetRef.value.confirmRef.urlRef.getListData() : null) : null;
      addAssetRef.value.confirmRef ? (addAssetRef.value.confirmRef.ipRef ? addAssetRef.value.confirmRef.ipRef.getListData() : null) : null;
    });
  }
}
// 返回选择模板界面，清空值
function resetTemplateId() {
  emits("toParentDialogTItle", "选择事件模板");
  templateId.value = "未选";
  cdstip.value = true;
  csrcip.value = true;
  objList.value = [];
  assetList.value = [];
  // 重置新增任务的内容
  addEventTask.value.resetForm();
  alertAddTask.value = false;
  setTimeout(() => {
    alertAddTask.value = true;
  }, 500);
}
// 选择模板后传值
// 向前传值
let emits = defineEmits(["toParentDialogTItle", "close"]);
function getChoseTemplateId(id) {
  templateId.value = id;
  if (props.addType === "WorkbenchAlert") {
    emits("toParentDialogTItle", "提报事件");
    getEventTemplateTaskSize(templateId.value).then((res) => {
      templateTaskId.value = res.data.taskId;
    });
  }
}
// 任务个数
let templateTaskId = ref("");
// 告警提报为事件新增
// 获取事件
let cdstip = ref(true);
let csrcip = ref(true);
// 添加从告警带来的IP为可疑对象
function addSuspiciousObject(ipStr, type) {
  if (type === "cdstip") {
    cdstip.value = false;
  } else if (type === "csrcip") {
    csrcip.value = false;
  }
  let date = new Date();
  let y = date.getFullYear();
  let MM = date.getMonth() + 1;
  MM = MM < 10 ? "0" + MM : MM;
  let d = date.getDate();
  d = d < 10 ? "0" + d : d;
  let h = date.getHours();
  h = h < 10 ? "0" + h : h;
  let m = date.getMinutes();
  m = m < 10 ? "0" + m : m;
  let s = date.getSeconds();
  s = s < 10 ? "0" + s : s;
  let nowtime = y + "年" + MM + "月" + d + "日 " + h + ":" + m + ":" + s;
  let suspiciousInfo = {
    createTimeStr: nowtime,
    type: "IP",
    content: ipStr,
    suspicious: type,
    typeName: "IP",
    tagId: "",
    description: "",
    infoStr: "0@_ssp_@" + ipStr + "@_ssp_@" + "" + "@_ssp_@" + "",
  };
  state.objList.push(suspiciousInfo);
}
// 告警提报事件时  无任务 添加任务
let taskLength0 = ref(true);
let addEventTask = ref();
function alertEventAddTask() {
  assetBasicRef.value.getBasicInfo().then((data) => {
    if (state.assetList.length === 0) {
      ElMessage.warning("请至少添加一条关联资产");
      return false;
    }
    if (state.objList.length === 0) {
      ElMessage.warning("请至少添加一条可疑对象");
      return false;
    }
    taskLength0.value = false;
  });
}
// 保存事件任务
function saveEvnetTask() {
  assetBasicRef.value
    .getBasicInfo()
    .then((result) => {
      if (result) {
        state.formData = result;
        addEventTask.value.ruleFormRef.validate((valid) => {
          if (valid) {
            loading.value = true;
            let data = addEventTask.value.formData;
            state.formData.suspiciousInfos = state.objList.map((item) => item["infoStr"]).join("@_assets_@");
            //   受影响资产
            // 1、多个受影响资产之间使用@_assets_@分割
            // 2、资产内部：资产id、资产类型、关联要素使用@_ssp_@分割
            // 3、资产内部多个关联要素使用#_ssp_#分割
            let assetsArr = [];
            state.assetList.forEach((dv, di) => {
              assetsArr.push(dv.id + "@_ssp_@" + dv.assetTypeCode + "@_ssp_@" + dv.reason);
            });
            state.formData.assetsInfos = assetsArr.join("@_assets_@");
            let params = {
              ...state.formData,
              templateId: state.templateId ? state.templateId : "white",
            };
            params.thatTime = params.thatTime[0] + " - " + params.thatTime[1];
            params.alertId = props.alertAddEvent.alertId;
            params.sourceId = props.alertAddEvent.sourceId;
            params.samplingRecordId = props.alertAddEvent.samplingRecordId;
            params.eventTaskCreateId = props.alertAddEvent.firstTaskId;
            params.handleFrom = props.alertAddEvent.handleFrom;
            params.eventTaskList = [];
            params.eventTaskList.push(data);
            saveEventAndTask(params)
              .then((res) => {
                ElMessage.success("操作成功");
                store.commit("closeCurrentTab");
                store.commit("offInitEventDetail");
                emits("close", "");
                router.push({
                  name: "OffEventDetail",
                  params: {
                    id: res.eventId,
                  },
                  query: {
                    taskId: res.taskId,
                    taskName: res.title,
                    isManageTask: "0",
                    isReset: true,
                  },
                });
              })
              .finally(() => {
                loading.value = false;
              });
          }
        });
      }
    })
    .catch(() => {
      scrollToUnValid();
    });
}

//添加待确认资产
let showConfirmAsset = ref(false);
// 添加待确认
function openAddConfirmAsset() {
  showConfirmAsset.value = true;
}
</script>

<style lang="scss" scoped>
.baseInfo {
  margin: 40px 0;
  width: 100%;
  border-top: 1px dashed #ededed;
  .form-wrapper {
    margin-top: 24px;
  }
  .title {
    padding: 26px 0px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #ededed;
    margin-bottom: 22px;
    p {
      line-height: 32px;
    }
  }
  .tit-wrapper {
    display: flex;
    width: 100%;
    .el-input:first-child {
      width: 40%;
      margin-right: 10px;
    }
  }
}
.assetInfo {
  border-top: none;
}
.footer {
  margin-bottom: 20px;
  float: right;
}
.addOtherObj {
  > p {
    line-height: 35px;
  }
  padding: 15px 10px;
  border-radius: $radiusL;
  background: $bgColor;
  box-shadow: 0px 2px 6px 1px rgba(0, 0, 0, 0.07);
  margin-bottom: 20px;
}
</style>
