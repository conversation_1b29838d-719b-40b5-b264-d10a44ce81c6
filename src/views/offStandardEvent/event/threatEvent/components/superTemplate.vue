<template>
  <xel-dialog ref="dialogRef" title="通用模板" size="large" @submit="submit" @close="cancel" buttonDetermine="使用模板">
    <add-edit-template v-if="status" :comp-edit-id="editId" ref="addEditTemplateRef" comp-type="changeEvent"></add-edit-template>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, nextTick } from "vue";

import { ElMessageBox, ElMessage } from "element-plus";

import addEditTemplate from "@/views/analyticalDisposal/runscript/components/addEditTemplate.vue";
let props = defineProps({
  editId: {
    type: String,
  },
});

let emits = defineEmits(["cancel", "submit"]);

let status = ref(true);

let dialogRef = ref();
function open() {
  dialogRef.value.open();
  status.value = true;
}
function cancel() {
  status.value = false;

  emits("cancel");
}
function close() {
  dialogRef.value.close();

  status.value = false;
}

let addEditTemplateRef = ref();
function submit() {
  addEditTemplateRef.value.saveBasic((id) => {
    emits("submit", id);
  });
}

defineExpose({
  open,
  close,
  submit,
});
</script>

<style lang="scss" scoped>
.add-btn {
  transform: scale(0.9);
  transform-origin: right;
}
</style>
