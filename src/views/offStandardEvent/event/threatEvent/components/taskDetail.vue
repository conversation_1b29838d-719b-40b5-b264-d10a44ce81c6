<template>
  <div v-if="pageStatus">
    <div class="handler-btns" v-if="btnList.filter((item) => !item.hide).length > 0">
      <el-button v-for="btn in btnList.filter((item) => !item.hide)" :key="btn.icon" size="mini" @click="btn.onClick">
        <el-icon v-if="btn.icon">
          <component :is="btn.icon" />
        </el-icon>
        <el-icon v-else>
          <svg class="icon" aria-hidden="true">
            <use :xlink:href="'#' + btn.isFont"></use>
          </svg>
        </el-icon>
        {{ btn.title }}
      </el-button>
    </div>
    <mouse-display
      :playName="'基础信息'"
      @close="echoDataBase(taskDetailState.eventTask)"
      @submit="saveBaseInfo"
      :show-submit="true"
      :disaboy="!isEventTab || taskDetail.canUpdate != 'Y'"
    >
      <el-form ref="baseFormRef" :model="baseForm" label-width="120px" label-position="left" class="base-info-form">
        <xel-form-item v-for="(item, index) in baseFormList" v-model="baseForm[item.prop]" :key="index" v-bind="item"></xel-form-item>
      </el-form>
      <template #display>
        <el-form label-width="120px" label-position="left" class="base-info-form">
          <el-row>
            <el-col :span="12">
              <el-form-item label="标题："> {{ taskDetailState.eventTask.title }}</el-form-item>
              <!--              <el-form-item label="阶段：">{{ taskDetailState.eventTask.taskGroupText }} </el-form-item>-->
              <el-form-item label="分析师：">{{ taskDetailState.eventTask.assigneeName }} </el-form-item>
              <!--              <el-form-item label="分析角色：" v-if="isEventTab && Number(eventTask.auditStatus) === 0"
                >{{ taskDetailState.eventTask.assigneeRoleName }}
              </el-form-item>-->
            </el-col>
            <el-col :span="12">
              <el-form-item label="执行时间：">{{ taskDetailState.eventTask.startTimeStr }} </el-form-item>
              <el-form-item label="完成时间：">{{ taskDetailState.eventTask.endTimeStr }} </el-form-item>
              <!--              <el-form-item label="历时：">{{ minToDate(taskDetailState.eventTask.takeUpTime) }} </el-form-item>-->
            </el-col>
          </el-row>
        </el-form>
      </template>
    </mouse-display>
    <mouse-display
      :playName="'任务描述'"
      @submit="saveDetail"
      @close="closedesc"
      :show-submit="true"
      :disaboy="!isEventTab || taskDetail.canUpdate != 'Y'"
    >
      <xel-edit class="margin-bottom10" editorClass="detailEditor" :desc="detailEdit" @change="detailEdit = $event"></xel-edit>
      <template #display>
        <div class="evetTaskD" v-html="taskDetailState.eventTask.detail"></div>
      </template>
    </mouse-display>

    <div>
      <div class="title-bottom-line margin-top20">
        <p>执行日志</p>
      </div>
      <div class="taskLogList">
        <!-- 添加日志 -->
        <el-button type="primary" style="margin-bottom: 10px" v-if="taskDetail.canUpdateLog == 'Y'" @click="openLogDialog">添加日志</el-button>
        <log-group
          :list="taskDetailState.logList"
          :showEdit="isEventTab"
          :canUpdateLog="isEventTab && taskDetail.canUpdateLog == 'Y'"
          @delFile="delFile"
          @openEditLog="openEditLog"
          @delLog="delLog"
        ></log-group>
      </div>
    </div>
    <div v-if="taskDetailState.auditList.length > 0">
      <div class="title-bottom-line margin-top20">
        <p>审核结果</p>
      </div>
      <div class="taskLogList">
        <el-collapse accordion>
          <el-collapse-item v-for="item in taskDetailState.auditList" :key="item.id" :name="item.id">
            <template #title>
              <p class="logItem">
                <span class="titleName">
                  <span>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-chuangjianzhe"></use>
                    </svg>
                  </span>
                  <span>审核者：</span>
                  <span>{{ item.auditName }}</span>
                </span>
                <span class="titleTime">
                  <span>
                    <svg class="icon" aria-hidden="true">
                      <use xlink:href="#icon-date"></use>
                    </svg>
                  </span>
                  <span>审核日期：</span>
                  <span>{{ item.auditTime }}</span>
                </span>
                <span>
                  <span></span>
                  <span>激励加分：</span>
                  <span class="reward">{{ item.reward }}</span>
                </span>
              </p>
            </template>
            <div>
              <p>
                <span v-if="item.status === '1'" style="color: green">审核通过</span>
                <span v-else style="color: red">驳回</span>
              </p>
              <p class="auditDetail">
                <span>审核意见：</span>
                <span>{{ item.comment }}</span>
              </p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div class="margin-top20 audit" v-if="(!isEventTab && samplingStatus.samplingStatus == '0') || auditShowStatus">
      <div class="title-bottom-line">
        <p>审核</p>
      </div>
      <div>
        <el-form ref="ruleFormRef" label-width="120px" size="mini">
          <el-form-item label="审核结果：">
            <el-radio-group v-model="auditState.data.status" @change="chageAduitStatus">
              <el-radio label="1" style="margin-top: 12px">审核通过</el-radio>
              <!-- <el-radio v-if="" v-model="auditState.data.status" label="0">任务质量驳回</el-radio> -->
              <el-radio label="0" style="margin-top: 12px">{{ taskDetailState.isAlert === "Y" ? "任务质量驳回" : "驳回" }}</el-radio>
              <el-radio v-if="taskDetailState.isAlert === 'Y'" label="2">误报</el-radio>
              <el-radio v-if="taskDetailState.isAlert === 'Y'" label="3">提报新事件</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="激励加分：" v-if="auditState.data.status == '1'">
            <el-input v-model="auditState.data.reward" type="number" placeholder="请输入分数" style="width: 200px" @input="changeReward"></el-input>
          </el-form-item>
          <el-form-item label="审核意见：">
            <el-input v-model="auditState.data.comment" type="textarea" placeholder="请输入审核意见" rows="5" maxlength="300"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="text-center margin-top30">
      <el-button type="button" v-if="!isEventTab" @click="closeAudit">取消</el-button>
      <el-button type="primary" v-if="(!isEventTab && samplingStatus.samplingStatus == '0') || auditShowStatus" @click="saveAudit">确定</el-button>
    </div>
    <!-- 添加，编辑日志弹框 -->
    <xel-dialog ref="logDialogRef" :title="`${activeLog.id ? '编辑' : '添加'}日志`" @submit="submitLog" @close="closeLogDialog">
      <el-form ref="logFormRef" :model="logFormData">
        <xel-form-item v-for="item in logFormList" :key="item.prop" v-model="logFormData[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xel-dialog>
    <!-- 提报事件 -->
    <xel-dialog title="选择事件模板" ref="choseEventTemplate" :ishiddenDialog="true" width="60%" @close="clsoeCreateEventDialog">
      <add-event v-if="addEventShow" :alertAddEvent="createEventState.data" addType="WorkbenchAlert"></add-event>
    </xel-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, watch, computed, nextTick } from "vue";
// 三线工作台  抽检事件的任务详情
import { selectOpenSamplingTask, saveSamplingTaskAudit } from "@/api/workSpace/workbench";
import { minToDate } from "@/utils/minToDate";
import { getDicts } from "@/api/system/dict/data";

// 执行日志 审核结果列表
import {
  getLogList,
  getAuditList,
  viewEventTaskDetail,
  updateEventTask,
  saveTaskLog,
  deleteTaskLog,
  removeFile,
} from "@/api/offStandardEvent/event/task";
import { getAssigneeRoles, selectUserTree } from "@/api/offStandardEvent/event/eventList";
import AddEvent from "@/views/event/threatEvent/components/addEvent.vue";
import mouseDisplay from "@/views/securityAssets/businessAssets/components/mouseDisplay.vue"; //鼠标移入编辑组件
import logGroup from "./logGroup.vue";

import { ElMessage, ElMessageBox } from "element-plus";

import taskHandle from "../mixins/offTaskHandle";

let { handlerTask, saveAuditFn } = taskHandle();

import { useStore } from "vuex";
const store = useStore();

import { useRoute } from "vue-router";

const route = useRoute();

//更新页面
let taskUpdate = computed(() => {
  return store.state.offEventDetail.taskUpdate;
});
let pageStatus = ref(true);
watch(
  () => taskUpdate.value.status,
  () => {
    if (taskUpdate.value.id == taskDetailState.eventTask.id || taskUpdate.value.id == route.params.id) {
      pageStatus.value = false;
      getTaskDetail();
      setTimeout(() => {
        pageStatus.value = true;
      }, 200);
    }
  }
);

let props = defineProps({
  //是否是事件详情中的任务详情tab
  isEventTab: {
    type: Boolean,
    default: false,
  },
  samplingStatus: {
    type: Object,
    default: () => {
      return {};
    },
  },
  isManageTask: {
    type: [String, Number],
    default: "",
  }, //0 执行 1处置
  audit: {
    type: Boolean,
    default: false,
  },
  reaudit: {
    type: Boolean,
    default: false,
  },
});

let taskDetailState = reactive({
  taskDetail: {},
  eventTask: {},
  isAlert: "",
  eventAlert: {},
  logList: [],
  auditList: [],
});

function chageAduitStatus(val) {
  auditState.data.comment = "";
  auditState.data.reward = 0;
}
let btnList = ref([]);
let isManageTask = false;
// 获取抽检事件中任务详情
function getTaskDetail(echoType) {
  let getDetailApi = props.isEventTab ? viewEventTaskDetail : selectOpenSamplingTask;
  getDetailApi(props.samplingStatus.taskId).then((res) => {
    let resData = res.data;
    taskDetailState.taskDetail = res.data;
    if (!echoType) {
      taskDetailState.eventTask = resData.eventTask;
      //回显表单信息 审核状态==0的时候有执行人和执行角色的编辑权限
      echoDataBase(resData.eventTask);
      echoDataDetail(resData.eventTask);

      taskDetailState.isAlert = resData.isAlert;
      taskDetailState.eventAlert = resData.eventAlert;

      btnList.value = getBtnList(resData.eventTask);
    } else {
      switch (echoType) {
        case "base":
          echoDataBase(resData.eventTask);
          break;
        case "detail":
          echoDataDetail(resData.eventTask);
          break;
      }
    }
    isManageTask = resData.eventTask.isManageTask;

    if ("isManageTask" in resData.eventTask) {
      /*getDicts(resData.eventTask.isManageTask == 0 ? "task_group" : "manage_task_group").then((res) => {
        baseFormList.value[1].options = res.data.map((item) => {
          return {
            value: item.dictValue,
            label: item.dictLabel,
          };
        });
      });*/
    }
  });
  getLogListFn();
  // 获取审核结果列表
  getAuditList(props.samplingStatus.taskId).then((res) => {
    //
    taskDetailState.auditList = res.data;
  });
}
function getLogListFn() {
  // 获取执行日志
  getLogList({ eventTaskId: props.samplingStatus.taskId, limit: 0 }).then((res) => {
    //
    taskDetailState.logList = res.data.rows;
  });
}
getTaskDetail();

// 任务审核
let auditState = reactive({
  data: {
    eventTaskId: props.samplingStatus.taskId,
    status: "1",
    reward: null,
    comment: "",
    samplingRecordId: props.samplingStatus.id,
  },
});
function changeReward(value) {
  auditState.data.reward = parseInt(value);
  if (value > 10000 || value < -10000) {
    ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
    auditState.data.reward = null;
  }
}
// 三线工作台 抽检事件审核任务
let emits = defineEmits(["close"]);
// 取消
function closeAudit() {
  emits("close");
}
// 保存审核内容
// 提报事件传值内容
let createEventState = reactive({
  data: {},
});
let choseEventTemplate = ref();
let addEventShow = ref(false);
function clsoeCreateEventDialog() {
  addEventShow.value = false;
  createEventState.data = {};
}
function saveAudit() {
  const status = auditState.data.status;

  if (status === "1") {
    if (!auditState.data.reward && auditState.data.reward != 0) {
      ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
      return false;
    }
  }
  if (status === "3") {
    createEventState.data = {
      alertId: taskDetailState.eventAlert.alertId,
      sourceId: taskDetailState.eventAlert.sourceId,
      firstTaskId: taskDetailState.eventTask.id,
      samplingRecordId: props.samplingStatus.id,
      csrcip: taskDetailState.eventAlert.csrcip,
      cdstip: taskDetailState.eventAlert.cdstip,
      handleFrom: "audit",
    };
    addEventShow.value = true;
    choseEventTemplate.value.open();
  } else {
    if (status === "2" && (auditState.data.comment === "" || auditState.data.comment == null)) {
      ElMessage.warning("请输入审核意见");
      return false;
    }
    if (props.isEventTab) {
      saveAuditFn(
        {
          eventTaskId: props.samplingStatus.taskId,
          status: auditState.data.status,
          reward: auditState.data.reward,
          comment: auditState.data.comment,
          auditType: auditType,
        },
        store,
        () => {
          auditShowStatus.value = false;
          // 误报 关闭任务详情
          if (auditState.data.status == 2) {
            store.commit("offdelTabByName", props.samplingStatus.taskId);
          } else {
            store.commit("offupdateEventActiveTab", { audit: false, reaudit: false });
          }
        }
      );
    } else {
      saveSamplingTaskAudit(auditState.data).then((res) => {
        ElMessage.success("操作成功");
        emits("close");
      });
    }
  }
}

//事件任务
//保存基本信息
let baseFormRef = ref();
let baseForm = ref({
  title: "",
  taskGroup: "",
  assignee: "",
  assigneeRole: [],
});

let roleTreeData = [];
let userTreeData = [];
getUserTree();
getRoleTree();

let baseFormList = ref([
  {
    formType: "input",
    prop: "title",
    label: "标题",
    required: true,
  },
  /*{
    formType: "select",
    prop: "taskGroup",
    label: "阶段",
    required: true,
    options: [],
    // dictName: isManageTask ? "manage_task_group" : "task_group",
  },*/
  {
    formType: "tree",
    prop: "assignee",
    label: "执行人",
    multiple: true,
    treeData: userTreeData,
    disabledKey: "value",
  },
  {
    formType: "tree",
    prop: "assigneeRole",
    label: "执行角色",
    multiple: true,
    treeData: roleTreeData,
  },
]);

let userPerm = false;
function echoDataBase(data) {
  userPerm = Number(data.auditStatus) === 0;
  baseFormList.value[1].isShow = baseFormList.value[2].isShow = userPerm;
  if (store.state.roles.includes("permission:one")) {
    baseFormList.value[1].isShow = baseFormList.value[2].isShow = false;
  }
  baseForm.value.title = data.title;
  baseForm.value.taskGroup = data.taskGroup;
  //任务未开始前
  if (data.auditStatus == 0) {
    baseForm.value.assignee = data.spare3 ? data.spare3.split(",") : [];
  } else {
    baseForm.value.assignee = data.assignee ? data.assignee.split(",") : [];
  }

  baseForm.value.assigneeRole = data.assigneeRole ? data.assigneeRole.split(",") : [];

  ["title", "taskGroupText", "assigneeName", "assigneeRoleName", "startTimeStr", "endTimeStr", "takeUpTime"].forEach((key) => {
    taskDetailState.eventTask[key] = data[key];
  });
}

function getUserTree(params) {
  selectUserTree({}).then((res) => {
    baseFormList.value[1].treeData = res.data;
  });
}
function getRoleTree(params) {
  getAssigneeRoles({}).then((res) => {
    baseFormList.value[2].treeData = res.data.map((item) => {
      return {
        id: item.roleId,
        label: item.roleName,
      };
    });
  });
}
function saveBaseInfo(close, showLoad) {
  baseFormRef.value.validate((valid) => {
    if (valid) {
      let params = {
        ...taskDetailState.eventTask,
        ...baseForm.value,
        assignee: baseForm.value.assignee.join(),
        assigneeRole: baseForm.value.assigneeRole.join(),
      };
      //不可编辑执行人的任务不传assignee

      if (!userPerm) {
        delete params.assignee;
      }
      delete params.detail;
      saveBaseInfoFn(params, "base", close, showLoad);
    }
  });
}
function saveBaseInfoFn(params, type, close, showLoad) {
  showLoad();

  updateEventTask(params)
    .then(() => {
      ElMessage.success("操作成功");
      getTaskDetail(type);
      close();
      store.commit("offupdateTaskById", props.samplingStatus.taskId);
      store.commit("offupdateEventActiveTab", { label: params.title });
    })
    .catch(() => {
      close(false);
    });
}

//任务要求
let detailEdit = ref("");
function saveDetail(close, showLoad) {
  if (!detailEdit.value) {
    ElMessage.warning("请输入任务描述");
    return;
  }
  let params = {
    ...taskDetailState.eventTask,

    detail: detailEdit.value,
  };

  delete params.assignee;

  saveBaseInfoFn(params, "detail", close, showLoad);
}

function closedesc() {
  detailEdit.value = taskDetailState.eventTask.detail;
}

function echoDataDetail(data) {
  detailEdit.value = data.detail;
  taskDetailState.eventTask.detail = data.detail;
}

//添加，编辑日志
let logDialogRef = ref();
let logFormRef = ref();
let logFormData = ref({
  detail: "",
  file_upload: "",
});
let logFileList = ref([]);
let logDialogStatus = ref(false);

let logFormList = ref([
  {
    formType: "editor",
    prop: "detail",
    label: "日志内容",
    required: true,
    editorData: "",
    editorClass: "logFormEditor", //多个编辑器时，命名不同的名字
    isClear: true,
    onEditorValue(val) {
      logFormData.value.detail = val;
    },
  },
  {
    isShow: true,
    formType: "upload",
    prop: "file_upload",
    label: "上传文件",
    fileListFa: [],
    limit: 100,
    multiple: true,
    accept: ".txt,  .doc,  .docx,  .pdf,   .xlsx , .xls, .rar,  .7Z,  .zip,  .tar ",
    onFileList(list) {
      logFileList.value = list;
    },
  },
]);
let activeLog = ref({});

function openLogDialog() {
  activeLog.value = {};
  logFormData.value = {
    detail: "",
  };

  logFileList.value = [];

  logDialogRef.value.open(() => {
    nextTick(() => {
      logFormList.value[0].isClear = !logFormList.value[0].isClear;
      logFormList.value[0].editorData = "";
    });
  });
}
function openEditLog(log) {
  activeLog.value = JSON.parse(JSON.stringify(log));
  logFormData.value = {
    detail: log.detail,
  };
  logFileList.value = [];

  logDialogRef.value.open(() => {
    nextTick(() => {
      logFormList.value[0].editorData = log.detail;
    });
  });
}
function closeLogDialog() {
  logFormList.value[1].fileListFa = [];
}
function submitLog(close, load) {
  logFormRef.value.validate((valid) => {
    if (valid) {
      load();
      let params = new FormData();
      params.append("eventTaskId", props.samplingStatus.taskId);

      if (activeLog.value.id) {
        params.append("id", activeLog.value.id);
      }
      params.append("detail", logFormData.value.detail);
      if (logFileList.value.length > 0) {
        for (let item of logFileList.value) {
          params.append("file_upload", item.raw);
        }
      } else {
        params.append("file_upload", new File([], ""));
      }

      saveTaskLog(params)
        .then(() => {
          ElMessage.success("操作成功");
          getLogListFn();
          close();
          logDialogStatus.value = false;
        })
        .catch(() => {
          close(false);
          logDialogStatus.value = false;
        });
    }
  });
}

function delLog(item) {
  ElMessageBox.confirm(`确认删除日志？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteTaskLog({
      id: item.id,
      eventTaskId: props.samplingStatus.taskId,
    }).then(() => {
      ElMessage.success("删除成功");
      getLogListFn();
    });
  });
}

//删除日志文件
function delFile(item) {
  removeFile({
    eventTaskId: props.samplingStatus.taskId,
    fileId: item.fileId,
    fileName: item.fileName,
  }).then((res) => {
    ElMessage.success("删除成功");
    getLogListFn();
  });
}

//操作按钮
function getBtnList(row) {
  return [
    {
      hide: row.startFlag != "Y",
      icon: "VideoPlay",
      title: "开始",
      onClick(scope) {
        handlerTask("start", row, isManageTask, false, store);
      },
    },
    {
      hide: row.submitFlag != "Y",

      icon: "Upload",
      title: "提报",
      onClick(scope) {
        handlerTask("submit", row, isManageTask, false, store);
      },
    },
    {
      hide: row.deleteFalg != "Y",
      icon: "Delete",
      title: "删除",
      onClick(scope) {
        handlerTask("del", row, isManageTask, false, store);
      },
    },
    {
      hide: row.reauditFlag != "Y",
      icon: "RefreshRight",
      title: "重审",
      onClick(scope) {
        showAudit("reaudit");
      },
    },
    {
      hide: row.closeFlag != "Y",
      icon: "CircleClose",
      title: "关闭",
      onClick(scope) {
        handlerTask("close", row, isManageTask, false, store);
      },
    },
    {
      hide: row.redoFlag != "Y",
      isFont: "icon-fanhui",
      title: "重做",
      onClick(scope) {
        handlerTask("redo", row, isManageTask, false, store);
      },
    },
    {
      width: "300px",
      hide: row.auditFlag != "Y",
      icon: "Checked",
      title: "审核",
      onClick(scope) {
        showAudit("audit");
      },
    },
  ];
}
let auditShowStatus = ref(false);
function showAudit(type) {
  auditState.data.status = "1";
  auditState.data.reward = 0;
  auditState.data.comment = "";
  auditShowStatus.value = true;
  auditType = type;
}
let auditType = "";
//是否展示审核
if (props.audit || props.reaudit) {
  showAudit();
  auditType = props.reaudit ? "reaudit" : "audit";
}

let { eventTask, isAlert, eventAlert, logList, auditList, taskDetail } = toRefs(taskDetailState);
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    padding-bottom: 10px;
    margin: 0px 20px 10px 20px;
  }
}
.evetTaskD {
  padding: 20px;
}
:deep(.el-collapse-item__header) {
  display: block;
  border-bottom: 1px solid #ebeef5;
  background: $bgColor;
  .el-collapse-item__arrow {
    float: left;
    width: 30px;
    margin-top: 16px;
    text-align: center;
    &::after {
      clear: both;
      content: "";
    }
  }
  .logItem {
    float: right;
    text-align: left;
    width: calc(100% - 40px);
    & > span {
      font-size: 14px;
      display: inline-block;
      line-height: 48px;
      margin-right: 20px;
      color: $fontColorSoft;
      & > span:nth-child(1) {
        font-size: 20px;
        line-height: 20px;
        float: left;
        margin-top: 13px;
      }
      & > span:nth-child(3) {
        color: #559cf6;
        &.reward {
          padding: 2px 7px;
          border-radius: 22px;
          background-color: #3498db;
          border-color: #3498db;
          color: #ffffff;
        }
      }
      & > span {
        margin-right: 5px;
      }
    }
  }
}
:deep(.el-collapse-item__wrap) {
  width: 100%;
}
:deep(.el-collapse-item__content) {
  padding: 10px 20px;
  .auditDetail {
    > span {
      float: left;
      &:nth-child(1) {
        width: 80px;
      }
      &:nth-child(2) {
        width: calc(100% - 80px);
      }
    }
  }
}
.audit {
  :deep(.el-radio.el-radio--mini) {
    margin-top: 0px !important             ;
  }
}
.handler-btns {
  text-align: right;
  padding-bottom: 10px;
  border-bottom: 1px dashed rgb(224, 221, 221);
  margin-bottom: 20px;
}
</style>
