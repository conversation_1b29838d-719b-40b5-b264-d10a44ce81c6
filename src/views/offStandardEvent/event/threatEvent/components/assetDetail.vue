<template>
  <xelDialog title="资产详情" ref="dialogRef" @close="$emit('closeDialog')" size="large" :showSubmit="false" :showCancel="false">
    <!-- 摘要 -->
    <el-row>
      <el-col>
        <div class="title-bottom-line text">
          <p>摘要</p>
          <div>
            <el-button @click="viewInfo" class="search-button">
              <el-icon> <View /> </el-icon>查看最新资产信息
            </el-button>
            <!-- <el-button @click="cancelConnect" class="search-button">
              <el-icon> <Connection /> </el-icon>取消关联
            </el-button> -->
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row class="onname">
      <el-col :span="12">
        <p>资产对象类型：{{ detailData.typeName }}</p>
      </el-col>
      <el-col :span="12">
        <p>等级保护级别：{{ detailData.levelName }}</p>
      </el-col>
      <el-col :span="12">
        <p>资产对象名称：{{ detailData.name }}</p>
      </el-col>
      <el-col :span="12">
        <p>资产组：{{ detailData.assetsGroupName }}</p>
      </el-col>
      <el-col :span="12">
        <p>责任主体：{{ detailData.deptName }}</p>
      </el-col>
      <el-col :span="12">
        <p>责任人：{{ detailData.assetsPerson }}</p>
      </el-col>
      <el-col :span="12">
        <p>更新时间：{{ detailData.updateTime }}</p>
      </el-col>
    </el-row>
    <!-- 域名 -->
    <div class="title-bottom-line text" v-if="assetData.type != '1' && assetData.type != '2'">
      <p>系统入口</p>
    </div>
    <el-row class="onname" v-if="assetData.type != '2'">
      <el-col :span="12" v-for="(dItem, index) in detailData.domainList" :key="index">
        <p>{{ dItem.domain }}</p>
      </el-col>
    </el-row>
    <!-- 互联网 -->
    <div class="title-bottom-line" v-if="assetData.type == '0'">
      <p>互联网IP、端口与服务组</p>
    </div>
    <el-row class="onname">
      <el-col :span="12" v-for="(nItem, index) in networkList" :key="index">
        <ip-port :list="[nItem]" :is-detail="true" :show-type="true" width="100%" class="magin-auto"></ip-port>
      </el-col>
    </el-row>
    <!-- 局域网 -->
    <div class="title-bottom-line text">
      <p v-if="assetData.type != '2'">局域网IP、端口与服务组</p>
    </div>
    <el-row class="onname">
      <el-col :span="12" v-for="(sItem, index) in serviceList" :key="index">
        <ip-port :list="[sItem]" :is-detail="true" :show-type="true" width="100%" class="magin-auto" :showPort="assetData.type != 2"></ip-port>
      </el-col>
    </el-row>
    <!-- 基础资源软件 -->
    <div class="title-bottom-line text" v-if="assetData.type == '1'">
      <p>基础资源软件</p>
    </div>
    <div class="margin-left20 margin-rigth20">
      <el-row class="onname" v-if="assetData.type == '1'">
        <el-col :span="12" v-for="(eItem, index) in detailData.resourceSoftwares" :key="index">
          <div><span class="item-label">类型：</span>{{ eItem.softwareType }}</div>
          <div><span class="item-label">软件：</span>{{ eItem.softwareValue }}</div>
          <div><span class="item-label">版本：</span>{{ eItem.softwareEdition }}</div>
        </el-col>
      </el-row>
    </div>

    <div class="margin-top20 margin-bottom20 margin-left20 flex" v-if="assetData.type == '1'">
      <span class="item-label">其他应用组件：</span>
      <span class="no-data" v-if="detailData.resourceAssemblys == null || detailData.resourceAssemblys.length == 0">暂无</span>
      <ul v-else class="flex resourceAssemblys">
        <li v-for="item in detailData.resourceAssemblys" :key="item.id" class="margin-right20 margin-bottom5">
          <el-tag>{{ item.assemblyName }}</el-tag>
        </li>
      </ul>
    </div>
    <!-- 关联要素 -->
    <el-row>
      <el-col>
        <div class="title-bottom-line text">
          <p>关联要素</p>
          <el-button v-if="detailData.editFlag" @click="addAsset" class="search-button">
            <el-icon :size="12">
              <plus />
            </el-icon>
            添加关联要素
          </el-button>
        </div>
      </el-col>
    </el-row>
    <el-row class="onname margin-bottom20">
      <el-col :span="12" v-for="dItem in detailData.relContentList" :key="dItem.id">
        <div v-if="!dItem.isEdit" class="contentBtn" @mouseover="mouseover(dItem)" @mouseout="mouseout(dItem)">
          <span>{{ dItem.content }}</span>
          <div class="btn" v-show="dItem.isshowbtn">
            <el-button @click="editDItem(dItem)" class="search-button" v-if="detailData.spare5 === 'Y'">
              <el-icon :size="12" title="编辑">
                <Edit />
              </el-icon>
            </el-button>
            <el-button @click="delConnect(dItem)" class="search-button" v-if="detailData.spare4 === 'Y'">
              <el-icon :size="12" title="删除">
                <Delete />
              </el-icon>
            </el-button>
          </div>
        </div>
        <div v-else class="contentBtn">
          <el-input v-if="props.assetData.type == '0' || props.assetData.type == '4'" v-model="dItemContent" placeholder="请输入关联要素" clearable />
          <el-select v-model="dItemContent" v-else>
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <div class="text magin-auto">
            <el-button @click="submitConnect(dItem)" class="search-button">
              <el-icon :size="12" title="确定">
                <Finished />
              </el-icon>
            </el-button>
            <el-button @click="cancelEdit(dItem)" class="search-button">
              <el-icon :size="12" title="取消">
                <Close />
              </el-icon>
            </el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </xelDialog>
  <associate-reason ref="singelReason" v-if="isshow" @close="closeReason" :reasonInfo="reasonInfo" :isDetailEdit="true"></associate-reason>
  <multi-reason ref="multiRef" v-if="isMultiShow" @close="closeReason" :reasonInfo="reasonInfo"></multi-reason>
</template>
<script setup>
import { ref, reactive, onMounted } from "vue";
import {
  selectBusinessDetail,
  getRelResourceDetail,
  selectTerminalDetail,
  addRelevancy,
  addResourceRelContent,
  updateRelContent,
  delRelContent,
  getContentList,
} from "@/api/offStandardEvent/event/asset.js";
import { getRelBasicAssetById, selectAssetsTerminalById } from "@/api/offStandardEvent/event/eventList";
import { useRouter, useRoute } from "vue-router";
import AssetDetail from "./assetDetail.vue";
import AssociateReason from "./associateReason.vue";
import MultiReason from "./multiReason.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { batchDelete } from "@/utils/delete";
const router = useRouter();
const route = useRoute();
let props = defineProps({
  //受影响资产类型
  // type: {
  //   type: String,
  //   default: "",
  // },
  // id: {
  //   type: String,
  //   default: "",
  // },
  assetData: {
    type: Object,
    default: (val) => {
      return {};
    },
  },
});
let emit = defineEmits(["search"]);
let options = ref([]);
let isshow = ref(false);
let isMultiShow = ref(false);
let reasonInfo = reactive({});
// 获取基础和终端关联要素

getAssetData();
function getAssetData() {
  console.log(props.assetData.resourceId, "resourceId");
  if (props.assetData.type == "1" || props.assetData.type == "2") {
    let portItem = props.assetData.type == "1" ? getRelBasicAssetById : selectAssetsTerminalById;
    portItem({ id: props.assetData.resourceId })
      .then((res) => {
        if (props.assetData.type == "1") {
          const resultArray = new Set([...res.data.chajian, ...res.data.ips, ...res.data.soft]);
          options.value = Array.from(resultArray).map((rv) => {
            return {
              label: rv,
              value: rv,
            };
          });
        } else {
          const resultArray = new Set([...res.data.ips]);
          options.value = Array.from(resultArray).map((rv) => {
            return {
              label: rv,
              value: rv,
            };
          });
        }
      })
      .catch(() => {});
  }
}
// 添加并关闭关联要素弹框
function closeReason(type, data) {
  if (type == "submit") {
    let params = {
      relId: detailData.value.resourceRelId,
      assetsType: props.assetData.type,
      contents: data.reason,
      assetId: props.assetData.id,
      eventId: route.params.id,
      assetsName: props.assetData.name,
    };
    addRelevancy(params)
      .then((res) => {
        getDetailInfo();
      })
      .catch((res) => {});
  }
  isshow.value = false;
  isMultiShow.value = false;
}
let ass = ref(false);
let buttonDisplay = ref(false);
function mouseover(dItem) {
  dItem.isshowbtn = true;
}
function mouseout(dItem) {
  dItem.isshowbtn = false;
}
function edit() {
  buttonDisplay.value = true;
}
function addAsset() {
  if (props.assetData.type != "3" || props.assetData.type != "4") {
    if (props.assetData.type == "0") {
      isshow.value = true;
    } else {
      isMultiShow.value = true;
    }
    let type = props.assetData.type == "0" ? "bussiness" : props.assetData.type == "1" ? "basic" : "terminal";
    reasonInfo.value = { type: type, id: props.assetData.id };
  }
}
//
import { useStore } from "vuex";
const store = useStore();
// 查看
function viewInfo() {
  router.push({
    name: "AssetDetails",
    params: {
      id:
        (store.state.offEventDetail.eventDetail.event.isClose == "0" && store.state.offEventDetail.eventDetail.event.isReopen == "1") ||
        store.state.offEventDetail.eventDetail.event.isClose == "1"
          ? props.assetData.resourceId
          : props.assetData.id,
      // resourseId
      type: props.assetData.type,
    },
  });
  emit("search");
}
// 删除
function delConnect(data) {
  let query = {
    relId: detailData.value.resourceRelId,
    id: data.id,
    assetsId: detailData.value.resourceId,
    assetType: props.assetData.type,
  };
  batchDelete().then(() => {
    delRelContent(query).then(() => {
      ElMessage({
        type: "success",
        message: "删除成功",
      });
      let index = detailData.value.relContentList.findIndex((item) => item.id == data.id);
      detailData.value.relContentList.splice(index, 1);
    });
  });
}
// 修改关联
function submitConnect(data) {
  let content = dItemContent.value;
  content = content.trim();
  if (!content) {
    ElMessage.warning("请输入关联要素");
    return;
  }
  data.content = content;
  updateRelContent({
    id: detailData.value.resourceRelId,
    content: data.content,
    assetType: props.assetData.type,
    assetsId: detailData.value.resourceId,
    spare3: data.id,
  })
    .then(({ data }) => {
      ElMessage.success("操作成功");
      data.isEdit = false;
    })
    .catch(() => {
      getDetailInfo();
    });
}
//取消修改关联
function cancelEdit(dItem) {
  dItem.isEdit = false;
}
let detailData = ref({
  typeName: "",
});
let dialogRef = ref();
let networkList = ref([]);
let serviceList = ref([]);
onMounted(() => {
  dialogRef.value.open();
  getDetailInfo();
});

function getDetailInfo() {
  let ports = props.assetData.type == "0" ? selectBusinessDetail : props.assetData.type == "1" ? getRelResourceDetail : selectTerminalDetail;
  ports({ eventId: route.params.id, id: props.assetData.id }).then(({ data }) => {
    detailData.value = data;
    detailData.value.typeName = props.assetData.type == "0" ? "业务系统资产" : props.assetData.type == "1" ? "计算设备资产" : "终端资产";
    if (props.assetData.type == "0") {
      networkList.value = data.portsServiceList
        .filter((item) => item.networkType == "1")
        .map((item) => {
          return {
            ...item,
            type: item.serviceAgreementName,
          };
        });
      serviceList.value = data.portsServiceList
        .filter((item) => item.networkType == "0")
        .map((item) => {
          return {
            ...item,
            type: item.serviceAgreementName,
          };
        });
    }
    if (props.assetData.type == "1" && data.resourceServers != null) {
      serviceList.value = data.resourceServers.map((item) => {
        return {
          ...item,
          type: item.serviceAgreementName,
        };
      });
    }
    if (props.assetData.type == "2" && data.ipList != null) {
      serviceList.value = data.ipList.map((item) => {
        return {
          ...item,
          type: item.serverAgreement,
        };
      });
    }

    // 关联要素
    detailData.value.relContentList = data.relContentList.map((dItem) => {
      return {
        ...dItem,
        isEdit: false,
        isshowbtn: false,
        spare3: dItem.id,
      };
    });
  });
}

let dItemContent = ref("");
function editDItem(dItem) {
  dItem.isEdit = true;
  dItemContent.value = dItem.content;
}
</script>

<style lang="scss" scoped>
.button {
  margin-right: 10px;
}
.text {
  display: flex;
  justify-content: space-between;
  .btn {
    cursor: pointer;
    margin-left: 20px;
  }
}
.onname {
  p {
    margin: 20px;
  }
}
.input-nane {
  margin-top: 20px;
}
.inp {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebedf1;
  margin-left: 20px;
}
.magin-auto {
  margin-left: 15px;
}
:deep .ip-port-list.detail > li {
  border: none;
}
.contentBtn {
  // margin: 6px 0;
  display: flex;
  justify-content: space-between;
  span {
    margin: 0 20px;
    line-height: 32px;
  }
}
:deep .action-btns-ul li {
  margin-left: 10px;
}
:deep .ip-port-list.detail > li .ip-label {
  width: 60px !important;
}

:deep .ip-port-list.detail > li .ip-span {
  width: 140px !important;
}
.resourceAssemblys {
  width: calc(100% - 8em);
  flex-wrap: wrap;
}
:deep .title-bottom-line {
  line-height: 32px;
}
span.item-label {
  float: left;
  display: inline-block;
  width: 120px;
  color: $fontColorSoft;
}
:deep .el-select {
  width: 100%;
}
</style>
