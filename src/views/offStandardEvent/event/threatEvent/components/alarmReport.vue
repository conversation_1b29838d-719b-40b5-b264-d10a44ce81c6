<template>
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button v-if="eventAlarmReportAdd" @click="newlyAdded" class="search-button">
      <el-icon :size="12">
        <plus />
      </el-icon>
      新增
    </el-button>
  </common-search>
  <xel-table
    ref="tableRef"
    :columns="columns"
    :load-data="selectAlarmReportPage"
    :default-params="{
      eventId: route.params.id,
    }"
    @selection-change="handleSelectionChange"
  >
  </xel-table>
  <!-- 告警 -->
  <!--  <div class="xeltable"></div>
  <common-search v-model="alarmSearch.data" :menu-data="alarmSearch.menuData" :form-list="alarmSearch.formList" @search="onsearch" @reset="onreset">
  </common-search>
  <xel-table
    ref="onRef"
    :columns="oncolumns"
    :load-data="selectEventAlertPage"
    :default-params="{
      eventId: route.params.id,
    }"
    @selection-change="handleSelectionChange"
  >
    <template #level="scope">
      <el-tag :type="levelData[scope.row.priority]">{{ scope.row.priorityStr }}</el-tag>
    </template>
  </xel-table>-->
  <!-- 导入文件弹框 -->
  <xel-dialog title="添加报告" ref="dialogRef" @submit="submitForm" @close="closeTask">
    <el-form :model="state.formfile" ref="ruleFormRef" label-width="140px" size="mini">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="state.formfile[item.prop]" v-bind="item"></xel-form-item>
      <xel-form-item
        :fileListFa="fileListFa"
        form-type="upload"
        prop="file_upload"
        label="选择文件"
        accept=".txt, .doc, .docx, .pdf, .xlsx"
        @fileList="changeFileList"
        :required="true"
      ></xel-form-item>
    </el-form>
  </xel-dialog>

  <!-- 告警查看详情 -->
  <xel-dialog title="告警信息" ref="detailsRef" width="1200px" @submit="submitForm">
    <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
      <el-row :gutter="70">
        <el-col :span="24">
          <el-form-item label="告警名称">{{ alarmDetails.title }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <!-- <el-form-item label="告警等级">{{ alarmDetails.priorityStr }}</el-form-item> -->
          <el-form-item label="告警级别：">
            <el-tag :type="levelData[alarmDetails.priority]">{{ alarmDetails.priorityStr }}</el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="告警发生设备">{{ alarmDetails.devName }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="设备地址">{{ alarmDetails.devIp }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="设备类型">{{ alarmDetails.devType }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="告警内容描述">{{ alarmDetails.description }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="首轮告警发生时间" v-if="alarmDetails.stage == '1' || !alarmDetails.stage">{{ alarmDetails.createTime }}</el-form-item>
          <el-form-item label="二轮告警发生时间" v-else-if="alarmDetails.stage == '2'">{{ alarmDetails.createTime2 }}</el-form-item>
          <el-form-item label="三轮告警发生时间" v-else-if="alarmDetails.stage == '3'">{{ alarmDetails.createTime3 }}</el-form-item>
          <el-form-item label="四轮告警发生时间" v-else-if="alarmDetails.stage == '4'">{{ alarmDetails.createTime4 }}</el-form-item>
          <el-form-item v-else :label="alarmDetails?.stageLabel">{{ alarmDetails?.stageTime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="首条日志源地址">{{ alarmDetails.csrcip }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="首条日志目的地址">{{ alarmDetails.cdstip }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="title-bottom-line ondata">
      <p>辅助分析</p>
    </div>

    <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
      <el-row :gutter="70">
        <el-col :span="24">
          <el-form-item label="绑定域名">{{ alarmDetails.reverseDomains }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情报类型">{{ alarmDetails.categories }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="情报置信度">{{ alarmDetails.threatScore }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="位置">{{ alarmDetails.location }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="可疑度">{{ alarmDetails.score }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="推断原因">{{ alarmDetails.reason }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #button>
      <el-button @click="closeDialog" class="search-button"> 关闭 </el-button>
    </template>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  selectAlarmReportPage,
  saveAlarm,
  deleteAlarm,
  selectEventAlertPage,
  selectarningAwaitingConfirmatio,
} from "@/api/offStandardEvent/event/alarmReport";
import { convertToChinese } from "@/utils/convertToChinese";
import { downloadUrl } from "@/api/system/download.js";
import { download } from "@/plugins/request";
import { useStore } from "vuex";
import { ElMessageBox, ElMessage } from "element-plus";
import { Level_priority } from "@/config/constant";
const store = useStore();
const route = useRoute();

let eventAlarmReportAdd = computed(() => {
  return store.state.offEventDetail.eventDetail.eventAlarmReportAdd == "Y";
});
let eventAlarmReportDel = computed(() => {
  return store.state.offEventDetail.eventDetail.eventAlarmReportDel == "Y";
});

import updateByTabName from "../mixins/updateByTabName";
updateByTabName(6, () => {
  search(false);
  onsearch(false);
});

let state = reactive({
  formfile: {
    file_upload: "",
    name: "",
    eventId: route.params.id,
  },
  levelData: Level_priority,
});
let { levelData } = toRefs(state);
let tableRef = ref();
let dialogRef = ref();
let ruleFormRef = ref();
let onRef = ref();
let detailsRef = ref();
let alarmDetails = ref({});
let fileListFa = ref([]);
const columns = ref([
  {
    prop: "name",
    label: "报告名称",
  },
  {
    prop: "fileName",
    label: "文件名",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "delete",
        title: "删除",
        onClick(scope) {
          delFn(scope.row);
        },
      },
      {
        icon: "download",
        title: "下载",
        onClick(scope) {
          oNwnload(scope.row);
        },
      },
    ],
  },
]);

watch(
  () => eventAlarmReportDel.value,
  (val) => {
    columns.value[3].btnList[0].hide = !val;
  },
  { immediate: true }
);

let formList = ref([
  {
    formType: "input",
    prop: "name",
    label: "报告名称",
    required: true,
  },
]);
const oncolumns = [
  {
    prop: "title",
    label: "告警名称",
  },
  {
    prop: "devIp",
    label: "设备地址",
  },
  {
    prop: "priorityStr",
    label: "告警等级",
    slotName: "level",
  },
  {
    prop: "description",
    label: "告警描述",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "right",
        title: "查看详情",
        onClick(scope) {
          wnload(scope.row.id);
        },
      },
    ],
  },
];

//搜索相关
let alarmSearch = reactive({
  data: {
    title: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "告警名称",
    },
  ],
});
let formon = reactive([
  {
    formType: "input",
    prop: "title",
    label: "告警名称",
    required: true,
  },
]);
let searchState = reactive({
  data: {
    name: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "报告名称",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    name: "",
  };
  search();
}
function onsearch() {
  /*onRef.value.reload(alarmSearch.data, true);*/
}
function onreset() {
  alarmSearch.data = {
    title: "",
  };
  onsearch();
}
function changeFileList(list) {
  if (list[0]) {
    state.formfile.file_upload = list[0];
  } else {
    state.formfile.file_upload = "";
  }
}
function newlyAdded() {
  dialogRef.value.open();
}
// 导入按钮
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let formData = new FormData();
      formData.append("name", state.formfile.name);
      formData.append("eventId", state.formfile.eventId);
      formData.append("file_upload", state.formfile.file_upload.raw);
      saveAlarm(formData).then((res) => {
        dialogRef.value.close();
        tableRef.value.reload(searchState.data, true);
        setTimeout(() => {
          store.commit("offchangeTabNumsByName", {
            name: 6,
            /*num: tableRef.value.resData.total + onRef.value.resData.total,*/
            num: tableRef.value.resData.total,
          });
        }, 500);
        ElMessage({
          message: res.msg,
          type: "success",
        });
      });
    }
  });
}

// 导入关闭方法
function closeTask() {
  state.formfile.name = "";
  fileListFa.value = [];
  state.formfile.eventId = route.params.id;
}
// 删除报告
function delFn(val) {
  let ass = {
    id: val.id,
    eventId: val.eventId,
  };
  deleteAlarm(ass).then((res) => {
    tableRef.value.reload(searchState.data, true);
    setTimeout(() => {
      store.commit("offchangeTabNumsByName", {
        name: 6,
        /*num: tableRef.value.resData.total + onRef.value.resData.total,*/
        num: tableRef.value.resData.total,
      });
    }, 500);
    ElMessage({
      message: res.msg,
      type: "success",
    });
  });
}
// 下载文件
function oNwnload(val) {
  download(downloadUrl + val.fileId, val.fileName, {}, "get");
}
// 告警查看详情
function wnload(id) {
  selectarningAwaitingConfirmatio({ id: id }).then((res) => {
    alarmDetails.value = res.data.record;
    if (Number(res.data.record.stage) > 4 && res.data.record.space1) {
      const space1Array = res.data.record.space1.split(";");
      const stageLabel = convertToChinese(res.data.record.stage);
      alarmDetails.value["stageTime"] = space1Array[space1Array.length - 1];
      alarmDetails.value["stageLabel"] = stageLabel + "轮告警发生时间";
    }
  });
  detailsRef.value.open();
}
// 关闭
function closeDialog() {
  detailsRef.value.close();
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    // margin-left: 20px;
  }
}
.ondata {
  margin: 10px 0 10px 0;
}
.xeltable {
  margin: 80px 0 80px 0;
}
</style>
