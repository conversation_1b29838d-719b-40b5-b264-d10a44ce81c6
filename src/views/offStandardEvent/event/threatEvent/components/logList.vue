<template>
  <section class="task-log" v-for="(item, index) in taskList" :key="index">
    <div class="title">
      <span class="color-soft">任务标题：</span>{{ item.title }}
      <!--      <span class="margin-left20"> <span class="color-soft">任务阶段：</span>{{ item.taskGroupText }} </span>
      <span class="margin-left20 num">{{ item.eventTaskLog.length }}</span>-->
    </div>
    <log-group :list="item.eventTaskLog"></log-group>
  </section>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import { initEventTaskLogList } from "@/api/offStandardEvent/event/task";

import logGroup from "./logGroup.vue";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

let props = defineProps({
  tabName: {
    type: [String, Number],
    default: 0,
  },
  isManageTask: {
    type: [String, Number],
    default: "",
  }, //0 执行 1处置
});
watch(
  () => props.tabName,
  (val) => {
    if (val == "logList") {
      getListFn();
    }
  }
);
let taskList = ref([]);
getListFn();
function getListFn() {
  initEventTaskLogList({ eventId: route.params.id, isManageTask: props.isManageTask }).then(({ data }) => {
    taskList.value = data.tasks;
  });
}
</script>

<style lang="scss" scoped>
.task-log {
  margin-top: 40px;
  &:first-of-type {
    margin-top: 10px;
  }
  .title {
    margin-bottom: 10px;
    color: #000;
    .num {
      display: inline-flex;
      height: 20px;
      border: 1px solid #e4e4e4;
      padding: 4px;
      border-radius: 2px;
      justify-content: center;
      align-items: center;
      min-width: 20px;
    }
  }
}
</style>
