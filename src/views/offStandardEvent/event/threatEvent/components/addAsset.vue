<template>
  <xelDialog title="添加关联资产" ref="dialogRef" @close="$emit('closeDialog', 'asset')" size="large" :ishiddenDialog="true">
    <el-tabs v-model="activeName" @tab-click="changeTab(activeName)">
      <el-tab-pane :lazy="true" label="业务系统对象" name="first">
        <BusinessAssets
          ref="businessRef"
          :isDialog="true"
          isDialogType="associated"
          @connect="connectReason"
          :assetsInfos="assetsInfos"
          :addEvent="addEvent"
        ></BusinessAssets>
      </el-tab-pane>
      <el-tab-pane :lazy="true" label="计算设备对象" name="second">
        <BasicAssets
          ref="basicRef"
          :isDialog="true"
          :addEvent="addEvent"
          isDialogType="associated"
          @connect="connectMultiReason"
          :assetsInfos="assetsInfos"
        ></BasicAssets>
      </el-tab-pane>
      <el-tab-pane :lazy="true" label="终端资产对象" name="third">
        <Terminal
          ref="terminalRef"
          :isDialog="true"
          :addEvent="addEvent"
          isDialogType="associated"
          @connect="connectMultiReason"
          :assetsInfos="assetsInfos"
        ></Terminal>
      </el-tab-pane>
      <el-tab-pane :lazy="true" label="待确认资产" name="fourth">
        <Confirm ref="confirmRef" @connect="connectReason" :addEvent="addEvent" :assetsInfos="assetsInfos"></Confirm>
      </el-tab-pane>
    </el-tabs>
  </xelDialog>
  <associate-reason ref="singelReason" v-if="isshow" @close="closeReason" :reasonInfo="reasonInfo"></associate-reason>
  <multi-reason ref="multiRef" v-if="isMultiShow" @close="closeMultiReason" :reasonInfo="reasonInfo"></multi-reason>
</template>
<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import BusinessAssets from "@/views/securityAssets/businessAssets/index.vue";
import BasicAssets from "@/views/securityAssets/underlying/index.vue";
import Terminal from "@/views/securityAssets/terminal/index.vue";
import AssociateReason from "./associateReason.vue";
import MultiReason from "./multiReason.vue";
import Confirm from "./confirm.vue";
let emit = defineEmits(["close", "connect", "closeDialog"]);
let multiRef = ref();
let singelReason = ref();
let dialogRef = ref();
let businessRef = ref();
let basicRef = ref();
let terminalRef = ref();
let confirmRef = ref();
let isshow = ref(false);
let isMultiShow = ref(false);
let activeName = ref("first");
let reasonInfo = reactive({});
let assetAllData = reactive({});
let assetsInfos = ref("");
onMounted(() => {
  dialogRef.value.open();
});
let props = defineProps({
  assetList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  addEvent: {
    type: String,
    default: "",
  },
});
watch(
  () => props.assetList.length,
  (val) => {
    assetsInfosFn();
  },
  {
    immediate: true,
  }
);
function assetsInfosFn() {
  let assetsArr = [];
  props.assetList.forEach((dv, di) => {
    assetsArr.push(dv.id + "@_ssp_@" + dv.assetTypeCode + "@_ssp_@" + dv.reason);
  });
  assetsInfos.value = assetsArr.join("@_assets_@");
}
function changeTab(name) {
  activeName.value = name;
}
// 关联要素
function connectReason(data) {
  isshow.value = true;
  reasonInfo.value = data;
}
// 关联多个要素
function connectMultiReason(data) {
  console.info(data);
  isMultiShow.value = true;
  reasonInfo.value = data;
}
// 关闭关联要素
function closeReason(type, data) {
  if (type == "submit") {
    emit("close", "asset", data);
  }
  isshow.value = false;
}
//
function closeMultiReason(type, data) {
  isMultiShow.value = false;
  if (type == "submit") {
    emit("close", "asset", data);
  }
}
defineExpose({
  businessRef: computed(() => businessRef.value),
  basicRef: computed(() => basicRef.value),
  terminalRef: computed(() => terminalRef.value),
  confirmRef: computed(() => confirmRef.value),
});
</script>

<style lang="scss" scoped></style>
