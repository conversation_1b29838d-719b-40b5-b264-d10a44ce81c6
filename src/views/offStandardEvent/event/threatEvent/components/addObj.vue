<template>
  <xelDialog title="添加其它可疑对象" ref="dialogRef" @submit="submitForm" @close="$emit('close', 'obj')">
    <el-form :model="objFormData" ref="ruleFormRef" label-width="120px" size="mini" class="form-wrapper">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="objFormData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xelDialog>
</template>
<script setup>
import { ref, reactive, onMounted, toRefs } from "vue";
import { checkSuspiciousForm } from "@/api/event/eventList";
import { ElMessageBox, ElMessage } from "element-plus";

let props = defineProps({
  allEvents: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
console.log(props.allEvents, 999);
let emit = defineEmits(["close"]);
let state = reactive({
  objFormData: {
    type: "",
    tagList: [],
    tagJson: "",
    content: "",
    description: "",
  },
});
let dialogRef = ref();
let { objFormData } = toRefs(state);
let ruleFormRef = ref();
onMounted(() => {
  dialogRef.value.open();
});
function submitForm(params) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let tagIds = state.objFormData.tagList.map((x) => {
        return x.id;
      });
      let params = {
        ...state.objFormData,
        tagJson: tagIds.toString(),
      };
      if (props.allEvents.length > 0) {
        let flag = props.allEvents.every((item) => {
          return item.content != params.content || item.infoStr.substr(0, 1) != params.type;
        });
        if (flag) {
          checkSuspiciousForm(params)
            .then((res) => {
              let data = res.data.suspiciousObjects;
              ElMessage.success("操作成功");
              emit("close", "obj", data);
            })
            .catch(() => {});
        } else {
          ElMessage.error("可疑对象指标重复");
        }
      } else {
        checkSuspiciousForm(params)
          .then((res) => {
            let data = res.data.suspiciousObjects;
            ElMessage.success("操作成功");
            emit("close", "obj", data);
          })
          .catch(() => {});
      }
    }
  });
}
// 弹框内容
let formList = reactive([
  {
    formType: "select",
    prop: "type",
    label: "可疑对象类型",
    required: true,
    options: [],
    dictName: "suspicious_type",
  },
  {
    formType: "input",
    type: "textarea",
    prop: "content",
    label: "可疑对象指标",
    required: true,
    maxlength: "100",
  },
  {
    formType: "tag",
    prop: "tagList",
    label: "标签",
  },
  {
    formType: "input",
    type: "textarea",
    prop: "description",
    label: "描述",
    required: true,
    maxlength: "500",
  },
]);
</script>

<style lang="scss" scoped></style>
