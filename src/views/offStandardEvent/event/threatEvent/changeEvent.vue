<template>
  <el-card ref="scrollRef">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <!-- 事件信息 -->
    <event-name></event-name>

    <section class="margin-top30">
      <div class="">选择事件类型</div>
      <section v-show="templateInfo.name && step == 2" class="template-info margin-top10">
        <h3 class="event-title break">
          当前事件类型： {{ (templateType == 1 ? "" : "通用模板") + templateInfo.name }}

          <!-- 通用模板编辑和删除 -->
          <span v-if="isSuperTemp" class="margin-left20">
            <el-button size="mini" @click="editSuperTemp">
              <el-icon size="mini"><edit /></el-icon>
            </el-button>
            <el-button size="mini" @click="returnStep(true)">
              <el-icon size="mini"><delete /></el-icon>
            </el-button>
          </span>
        </h3>
      </section>
      <!-- 选择模板 -->
      <add-basic-event
        ref="addBasicRef"
        :interface="getTemplateAndTasks"
        param-key="templateId"
        res-key="template"
        :info="eventInfo"
        :changeTempInfo="changeTempInfo"
        :template-info="templateInfo"
        :show-dept="false"
        :addSuperTag="addSuperTag"
        @parentFn="changetemplateId"
        @returnTempList="returnStep(true)"
      ></add-basic-event>
    </section>

    <section v-if="step == 2">
      <!-- 事件的任务 -->
      <merge-task-list ref="mergeTaskListRef" type="change"></merge-task-list>
      <div class="event-task-title">{{ $route.name === "AlterationEvent" ? "变更" : "升级" }}后任务选择</div>
      <el-checkbox-group v-if="templateTaskList.filter((item) => item.spare3 == 0).length > 0" v-model="selectedTaskIds">
        <el-checkbox
          v-for="(task, _index) in templateTaskList.filter((item) => item.spare3 == 0)"
          :key="_index"
          :label="task.id"
          class="event-task-item"
        >
          【{{ task.taskGroupText }}】{{ task.title }}</el-checkbox
        >
      </el-checkbox-group>
      <div class="event-task-title">{{ $route.name === "AlterationEvent" ? "变更" : "升级" }}后必完成任务</div>

      <div v-for="(task, _index) in templateTaskList.filter((item) => item.spare3 == 1)" :key="_index" class="event-task-item">
        【{{ task.taskGroupText }}】 {{ task.title }}
      </div>

      <div class="text-right margin-top20">
        <el-button type="primary" :loading="loading" @click="submitChange">事件{{ $route.name === "AlterationEvent" ? "变更" : "升级" }}</el-button>
      </div>
    </section>
    <!-- 超级模板新增，编辑，任务 -->
    <super-template ref="superTemplateRef" :edit-id="templateId" @cancel="returnStep" @submit="getTemplateInfo"></super-template>
  </el-card>
</template>
<script>
export default {
  name: "ChangeEvent",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, nextTick, computed } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { scrollRef, scrollToUnValid } from "@/utils/scrollToUnValid";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";
const store = useStore();

import eventName from "./components/eventName.vue";
import AddBasicEvent from "./components/addBasicEvent.vue";
import mergeTaskList from "./components/mergeTaskList.vue";
import superTemplate from "./components/superTemplate.vue";

import {
  getTemplateAndTasks,
  initChangeEvent,
  saveEventChange,
  getSuperTemplateAndTasks,
  initAlterationEvent,
  saveEventAlteration,
} from "@/api/event/detail.js";

//查询变更事件基本信息
let eventInfo = ref({});

/* 新增 - 事件变更与升级共用 AlterationEvent - 事件变更 */
if (route.name === "AlterationEvent") {
  initAlterationEvent({ eventId: route.params.id }).then(({ data }) => {
    eventInfo.value = data.event;
  });
} else {
  initChangeEvent({ eventId: route.params.id }).then(({ data }) => {
    eventInfo.value = data.event;
  });
}

let step = ref(1);
let mergeTaskListRef = ref();
let loading = ref(false);
function selectTemplate() {
  if (templateId.value != "0") {
    getTemplateAndTasksFn();
    step.value = 2;
    nextTick(() => {
      mergeTaskListRef.value.getTaskList(route.params.id);
    });
  } else {
    superTemplateRef.value.open();
  }
}

function returnStep(notValid = false) {
  //超级模板保存后，再关闭弹框不需要回退至选模板
  if ((templateType == 2 && templateId.value == "0") || notValid) {
    //重置页面数据
    step.value = 1;
    addBasicRef.value.returnList(false); //选择模板组件回到模板列表
    templateInfo.value = {}; //清空模板信息
    templateType = 1;
    templateId.value = "";
    mergeTaskListRef.value = [];
    templateTaskList.value = [];
  }
}

let templateType = 1; //1正常模板 2超级模板
let templateId = ref(""); //模板id
let templateTaskList = ref([]); //模板包含的任务
let templateInfo = ref({}); //模板信息
let selectedTaskIds = ref([]);
function getTemplateAndTasksFn() {
  if (!templateId.value) return;
  getTemplateAndTasks({
    templateId: templateId.value,
  }).then(({ data }) => {
    templateTaskList.value = data.taskList;
    templateInfo.value = data.template;
  });
}

let isSuperTemp = ref(false);
let selectedTemplate = ref(null);
function changetemplateId(id, data) {
  changeTempInfo.value = false;
  isSuperTemp.value = id ? false : true;
  templateType = id ? 1 : 2;
  templateId.value = id ? id : "0";
  selectedTemplate.value = data;
  selectTemplate();
}
let addBasicRef = ref();

function submitChange() {
  let taskListArr = mergeTaskListRef.value.taskList.map((item) => item.selected);
  let baseInfo = {};
  addBasicRef.value
    .getBasicInfo()
    .then((data) => {
      baseInfo = data;
      baseInfo.tagList = baseInfo.tagList || [];
      let params = {
        id: route.params.id,
        templateType: templateType,
        eventTasks: taskListArr[0],
        title: baseInfo.title,
        levelId: baseInfo.levelId,
        beginTimeStr: baseInfo.beginTimeStr,
        endTimeStr: baseInfo.endTimeStr,
        detail: baseInfo.detail,
        tagId: baseInfo.tagList.map((item) => item.id).join(),
        templateTasks: selectedTaskIds.value,
        templateId: templateId.value,
        titlePrefix: selectedTemplate.value ? selectedTemplate.value.titlePrefix : "",
      };
      loading.value = true;

      let API = route.name === "AlterationEvent" ? saveEventAlteration : saveEventChange;
      API(params)
        .then(() => {
          ElMessage.success("操作成功");
          router.push({
            name: "EventDetail",
            params: {
              id: route.params.id,
            },
          });
          store.commit("closeCurrentTab");
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(() => {
      scrollToUnValid();
    });
}

let addSuperTag = ref(false); //添加通用模板标签
let superTemplateRef = ref(); //通用模板编辑弹框
let changeTempInfo = ref(false); //通知子组件回显模板信息
//保存模板后子组件回显
function getTemplateInfo(id) {
  changeTempInfo.value = false;
  getSuperTemplateAndTasks({
    templateId: id,
  }).then(({ data }) => {
    templateTaskList.value = data.taskList;
    templateInfo.value = data.template;

    superTemplateRef.value.close(); //关闭超级模板弹框
    nextTick(() => {
      changeTempInfo.value = true; //通知子组件刷新数据
    });
    templateId.value = data.template.id; //当前编辑模板的id
    step.value = 2; //显示任务列表
    nextTick(() => {
      mergeTaskListRef.value.getTaskList(route.params.id); //获取事件自带任务
    });
    addSuperTag.value = true; //新增通用模板标签
    setTimeout(() => {
      addSuperTag.value = false;
    }, 1000);
  });
}

function editSuperTemp() {
  templateId.value = templateInfo.value.id;
  superTemplateRef.value.open();
}
</script>

<style lang="scss" scoped>
.event-title {
  padding: 12px 24px;
  background: $bgColor;
  color: $fontColor;
  border-radius: $radiusS;
}
</style>
