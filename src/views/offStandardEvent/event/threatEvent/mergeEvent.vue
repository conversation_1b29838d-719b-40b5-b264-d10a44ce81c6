<template>
  <el-card ref="scrollRef">
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <!-- 事件信息 -->
    <event-name></event-name>
    <!-- 选中的事件 -->
    <div class="margin-top20">
      <event-name v-if="!eventListShow" :id="eventListRef.eventId"></event-name>
    </div>
    <!-- 选择合并事件 -->
    <div v-show="!eventListShow" class="text-right margin-top10">
      <el-button @click="showEventList">
        <el-icon><search /></el-icon>
        选择合并事件</el-button
      >
    </div>
    <section v-show="eventListShow" class="margin-top30">
      <div class="title-bottom-line">选择合并事件</div>
      <event-list ref="eventListRef" :is-comp="true" comp-type="mergeEvent"></event-list>
      <div class="text-right margin-top20">
        <el-button type="primary" @click="selectEvent" :disabled="eventListRef && !eventListRef.eventId">确定</el-button>
      </div>
    </section>

    <section v-if="step == 2">
      <!-- 选择模板 -->
      <add-basic-event
        ref="addBasicRef"
        :interface="getTemplateAndTasks"
        param-key="templateId"
        res-key="template"
        :showDept="false"
        @parentFn="changeTemplateId"
      ></add-basic-event>
      <!-- 两个事件的任务 -->
      <merge-task-list ref="mergeTaskListRef" type="merge" class="margin-bottom10"></merge-task-list>
      <div class="event-task-title">升级后任务携带</div>
      <div v-for="(task, _index) in templateTaskList" :key="_index" class="event-task-item">【{{ task.taskGroupText }}】 {{ task.title }}</div>
      <div class="text-right margin-top20">
        <el-button type="primary" :loading="loading" @click="submitMerge">事件合并</el-button>
      </div>
    </section>
  </el-card>
</template>
<script>
export default {
  name: "MergeEvent",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, nextTick } from "vue";
import { getTemplateAndTasks, saveEventMerge } from "@/api/event/detail.js";
import { ElMessageBox, ElMessage } from "element-plus";
import { scrollRef, scrollToUnValid } from "@/utils/scrollToUnValid";

import eventName from "./components/eventName.vue";
import eventList from "./eventList.vue";
import AddBasicEvent from "./components/addBasicEvent.vue";

import mergeTaskList from "./components/mergeTaskList.vue";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
import { useStore } from "vuex";
const store = useStore();

let step = ref(1);

let eventListRef = ref();
let selectedEvent = ref({});
let eventListShow = ref(true);
let mergeTaskListRef = ref();
function selectEvent() {
  if (eventListRef.value.eventId) {
    selectedEvent.value = eventListRef.value.eventInfo;
    eventListShow.value = false;
    nextTick(() => {
      mergeTaskListRef.value.getTaskList([route.params.id, eventListRef.value.eventId].join());
    });

    getTemplateAndTasksFn();
    step.value = 2;
  }
}

function showEventList() {
  ElMessageBox.confirm(`更换事件会清空当前信息，确认重新选择事件？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    eventListShow.value = true;
    step.value = 1;
  });
}

let templateId = "";
let templateTaskList = ref();
let templateInfo = ref({});
function getTemplateAndTasksFn() {
  if (!templateId) return;
  getTemplateAndTasks({
    templateId,
  }).then(({ data }) => {
    templateTaskList.value = data.taskList;
    templateInfo.value = data.template;
  });
}
let selectedtemplate = null;
function changeTemplateId(id, data) {
  selectedtemplate = data;

  templateId = id;
  getTemplateAndTasksFn();
}
let addBasicRef = ref();
let loading = ref(false);
function submitMerge() {
  if (!addBasicRef.value.selectedtTemplateId) {
    ElMessage.warning("请先选择模板");
    return;
  }
  let taskListArr = mergeTaskListRef.value.taskList.map((item) => item.selected);
  let baseInfo = {};
  // 基础信息
  addBasicRef.value
    .getBasicInfo()
    .then((data) => {
      baseInfo = data;

      let params = {
        id: [route.params.id, eventListRef.value.eventId].join(),
        templateId: templateId,
        eventTasks: taskListArr[0].concat(taskListArr[1]),
        title: baseInfo.title,
        levelId: baseInfo.levelId,
        beginTimeStr: baseInfo.beginTimeStr,
        endTimeStr: baseInfo.endTimeStr,
        detail: baseInfo.detail,
        tagId: baseInfo.tagList.map((item) => item.id).join(),
      };
      loading.value = true;
      saveEventMerge(params)
        .then(() => {
          ElMessage.success("合并成功");
          router.push({
            name: "ThreatEvent",
          });
          store.commit("closeCurrentTab");
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(() => {
      scrollToUnValid();
    });
}
</script>

<style lang="scss" scoped></style>
