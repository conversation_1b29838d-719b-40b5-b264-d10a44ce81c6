<template>
  <div style="height: 100%" v-loading="dataLoading">
    <div>
      <p class="pull-left fieldTitle">数据信息</p>
      <p class="pull-left change" @click="changeShowOrHide">
        <span>
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-yincang"></use>
          </svg>
        </span>
        <span v-show="fieldisnull">显示空字段</span>
        <span v-show="!fieldisnull">隐藏空字段</span>
      </p>
      <div class="clearfix"></div>
    </div>
    <div class="margin-top20">
      <el-tree
        :data="state.treeList"
        node-key="cuid"
        :props="defaultProps"
        :load="loadNode"
        lazy
        v-resize="DomResize"
        :highlight-current="true"
        :expand-on-click-node="false"
        @node-click="getNode"
      />
    </div>
    <div class="margin-top20" ref="table_content">
      <el-table :data="state.detail" stripe :show-header="false" border height="100%">
        <el-table-column label="字段名" prop="fieldName"></el-table-column>
        <el-table-column label="值" prop="value">
          <template #default="scope">
            <div style="white-space: pre-wrap" v-text="$globalShowOriginStr(scope.row.value)"></div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  //添加逻辑运算符 直接选中
  directives: {
    resize: {
      // 指令的名称
      mounted(el, binding) {
        // el为绑定的元素，binding为绑定给指令的对象
        let width = "",
          height = "";
        function isReize() {
          const style = document.defaultView.getComputedStyle(el);
          if (width !== style.width || height !== style.height) {
            binding.value({ width: style.width, height: style.height });
          }
          width = style.width;
          height = style.height;
        }
        el.__vueSetInterval__ = setInterval(isReize, 300);
      },
      updated() {},
      unmounted(el) {
        clearInterval(el.__vueSetInterval__);
      },
    },
  },
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, watch } from "vue";
import { filterIndexIdNames } from "@/api/sime/search/filter";
import { getRuleInfo } from "@/api/sime/config/batchEngine";
import { alertTrace } from "@/api/workSpace/alert";
import { search_trace } from "@/api/sime/search/search";
let props = defineProps({
  traceData: {
    type: Object,
    default() {
      return {};
    },
  },
});
let dataLoading = ref(false);
let defaultProps = {
  label: "ceventname",
  children: "children",
  isLeaf: "leaf",
};
let state = reactive({
  detail: [],
  treeList: [],
  oldData: {},
  filterNames: [],
});
let indexId = ref("");
let fieldisnull = ref(true);

function changeShowOrHide() {
  fieldisnull.value = !fieldisnull.value;
  getLogDetail(state.oldData);
}
// 通过cruleId 获取indexId
function getRuleInfoAll() {
  dataLoading.value = true;
  getRuleInfo(props.traceData.cruleid).then((res) => {
    indexId.value = res.data.indexId;
    //获取names
    filterIndexIdNames(indexId.value).then((resNames) => {
      state.filterNames = resNames.data;
    });
    alertTrace({ indexId: indexId.value, alertId: props.traceData.alertId, alertTime: props.traceData.alertTime }).then((trace) => {
      state.treeList = trace.data;
      getLogDetail(state.treeList[0]);
      dataLoading.value = false;
    });
  });
}
function getLogDetail(data) {
  state.oldData = data;
  let arr = state.filterNames;
  state.detail = [];
  arr.forEach((item) => {
    for (let key in data) {
      let str = key.split("time");
      if (item.field === key) {
        if (data[key] && fieldisnull.value === true) {
          state.detail.push({
            fieldName: item.alias,
            value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
          });
        } else if (fieldisnull.value === false) {
          state.detail.push({
            fieldName: item.alias,
            value: str.length > 1 ? data[key + "Text"] : data[key + "Text"] ? data[key + "Text"] : data[key],
          });
        }
      }
    }
  });
}
async function loadNode(node, resolve) {
  if (node.level >= 1) {
    if (node.data.idatacategory === 3 || node.data.idatacategory === 1) {
      let res2 = await search_trace({ indexId: indexId.value, coriginlogids: node.data.coriginlogids });
      let data = res2.data;
      data.forEach((item) => {
        if (item.idatacategory == "0") {
          item.leaf = true;
        }
      });
      return resolve(data);
    } else {
      return resolve([]);
    }
  }
}
// 点击获取node内容
function getNode(node, key) {
  getLogDetail(node);
}
let table_content = ref();
function DomResize(data) {
  let ss = data.height.split("px");
  let height = 0;
  if (parseFloat(ss[0]) > 0) {
    height = parseFloat(ss[0]) + 100;
  } else {
    height = 100;
  }
  table_content.value.style.height = "calc(100% - " + height + "px)";
}
onMounted(() => {
  getRuleInfoAll();
  // state.treeList.push(props.logDetail);
  // getLogDetail(props.logDetail);
});
</script>

<style lang="scss" scoped>
.fieldTitle {
  font-weight: 600;
  font-size: 18px;
}
.change {
  color: #127dca;
  border: 1px solid #127dca;
  padding: 3px 5px;
  border-radius: 3px;
  margin-left: 10px;
}
</style>
