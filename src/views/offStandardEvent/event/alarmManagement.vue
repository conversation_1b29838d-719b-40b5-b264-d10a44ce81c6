<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <statistics :border="true" :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: pageTitle }]"></statistics>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table
      ref="tableRef"
      :default-params="$route.query.priority ? { priority: $route.query.priority, timeWindow: 6 } : {}"
      :columns="columns"
      :load-data="getworkbenchList"
      @selection-change="handleSelectionChange"
    >
      <template #level="scope">
        <el-tag :type="levelData[scope.row.priority]">{{ scope.row.priorityStr }}</el-tag>
      </template>
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "AlarmManagement",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, onActivated } from "vue";
import { getworkbenchList } from "@/api/workSpace/home.js";
import { useRouter, useRoute } from "vue-router";
import { Level_priority } from "@/config/constant";
const router = useRouter();
const route = useRoute();
let tableRef = ref();

let firstIn = true;
onMounted(() => {
  setTimeout(() => {
    firstIn = false;
  }, 100);
});
onActivated(() => {
  if (!firstIn) {
    search(false);
  }
});

let pageTitle = ref("告警总数");
let state = reactive({
  levelData: Level_priority,
});
let { levelData } = toRefs(state);
// 列表配置项
const columns = [
  {
    prop: "title",
    label: "告警名称",
    click(scope) {
      router.push({
        name: "AlarmDetails",
        params: { id: scope.row.id },
      });
    },
  },

  {
    prop: "priority",
    label: "告警等级",
    slotName: "level",
    sortable: true,
  },
  {
    prop: "csrcip",
    label: "告警源地址",
  },
  {
    prop: "description",
    label: "告警详情",
  },
  {
    prop: "categories",
    label: "情报碰撞结果",
  },
  {
    prop: "dealStatusStr",
    label: "处置状态",
  },
  {
    prop: "eventName",
    label: "事件信息",
  },
  {
    prop: "createTime",
    label: "首轮告警发生时间",
    sortable: true,
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "View",
        title: "查看",
        onClick(scope) {
          router.push({
            name: "AlarmDetails",
            params: { id: scope.row.id },
          });
        },
      },
    ],
  },
];
//搜索相关
let searchState = reactive({
  data: {
    priority: "",
    dealStatus: "",
    timeWindow: "",
    title: "",
    csrcip: "",
  },
  menuData: [
    // {
    //   lable: "告警等级：",
    //   prop: "priority",
    //   options: [
    //     { value: "1", label: "信息" },
    //     { value: "2", label: "低危" },
    //     { value: "3", label: "中危" },
    //     { value: "4", label: "高危" },
    //     { value: "5", label: "紧急" },
    //   ],
    // },
    {
      lable: "处置状态：",
      prop: "dealStatus",
      options: [
        { value: "1", label: "未处理" },
        { value: "5", label: "已处理" },
        { value: "2", label: "已误报判断" },
        { value: "3", label: "已生成事件" },
        { value: "4", label: "提报告警驳回" },
      ],
    },
    {
      lable: "告警时间：",
      prop: "timeWindow",
      options: [],
      dictName: "vuln_findTime",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "告警名称",
    },
    {
      formType: "input",
      prop: "space1",
      label: "IP",
      placeholder: "请输入源地址或目的地址或设备地址",
    },
    {
      formType: "select",
      prop: "priority",
      filterable: true,
      label: "告警等级",
      dictName: "alert_level",
    },
  ],
});
getQueryPriority();
//获取路由中的query参数
function getQueryPriority() {
  if (!searchState.data.priority && route.query.priority) {
    searchState.data.priority = route.query.priority;
    searchState.data.timeWindow = 6;
    searchState.data.dealStatus = 5;
    let _location = window.location.href.split("?")[0];
    window.history.replaceState({}, "", _location);
  }
}

function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    priority: "",
    dealStatus: "",
    timeWindow: "",
    title: "",
    csrcip: "",
  };
  search();
}
</script>

<style lang="scss" scoped></style>
