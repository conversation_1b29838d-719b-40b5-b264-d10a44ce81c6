<template>
  <statistics :border-line="false" :borderLine="false" :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '未通过事件数' }]"></statistics>
  <common-search
    v-model="searchState.data"
    :two="true"
    :menu-data="searchState.menuData"
    :form-list="searchState.formList"
    @search="search"
    @reset="reset"
  >
    <!-- <el-button @click="newlyAdded" class="search-button" v-hasPermi="'event:add'">
      <el-icon :size="12">
        <plus />
      </el-icon>
      添加事件
    </el-button> -->
  </common-search>
  <xel-table class="event-table" ref="tableRef" :load-data="getTableData" :columns="columns" :default-params="defaultParams">
    <template #title="{ row }">
      <div class="xel-clickable" @click="toDetail(row)">
        Case #{{ row.eventNo }}-{{ row.titlePrefix }} &nbsp;&nbsp;{{ row.title }}
        <p class="event-tag-box">
          <icon n="icon-a-2" :size="20"></icon>
          <span v-for="tag in row.eventTagList" :key="tag.id">{{ tag.tagName }}</span>
        </p>
      </div></template
    >
    <template #radio="scope" v-if="isComp">
      <el-radio v-model="eventId" :label="scope.row.id"> <span></span></el-radio>
    </template>
    <template #level="scope">
      <el-tag :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
    </template>
    <template #taskProgress="{ row }">
      <el-progress v-if="row.groupCountTotle != 0" :percentage="(row.groupCount1 / row.groupCountTotle) * 100" :stroke-width="8" status="success">
        <span style="font-size: 12px; color: #555" text>{{ row.groupCount1 }}/{{ row.groupCountTotle }}</span>
      </el-progress>
    </template>
  </xel-table>
</template>
<script setup>
import { getUnauditList } from "@/api/event/eventList";
import { ref, reactive, toRefs, computed, onActivated } from "vue";
let firstIn = true;
onMounted(() => {
  setTimeout(() => {
    firstIn = false;
  }, 100);
});
onActivated(() => {
  if (!firstIn) {
    search(false);
  }
});
import { Level_Data } from "@/config/constant";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();

let getTableData = ref(null);

getTableData.value = getUnauditList;
//可合并事件列表参数
let defaultParams = {
  /* 事件引用 - 默认查询未关闭事件 */
};
if (route.query.priority) {
  defaultParams.levelId = route.query.priority * 1;
  defaultParams.findTerm = 6;
}

let eventId = ref("");
const router = useRouter();
let tableRef = ref();
let searchState = reactive({
  data: {
    levelId: "",
    findTerm: "",
    title: "",
  },
  menuData: [
    {
      lable: "事件级别",
      prop: "levelId",
      options: [],
      dictName: "event_level",
    },
    {
      lable: "事件状态",
      prop: "isClose",
      options: [],
      dictName: "event_close",
    },
    {
      lable: "创建时间",
      prop: "findTerm",
      options: [],
      dictName: "vuln_findTime",
    },
    {
      lable: "审核状态",
      prop: "auditResultStatus",
      options: [],
      dictName: "event_auditStatus",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "关键字",
      width: "400px",
      placeholder: "请输入事件名称、受影响资产名称、可疑对象关键字、告警IP",
    },
  ],
});
getQueryPriority();
//获取路由中的query参数
function getQueryPriority() {
  if (!searchState.data.levelId && route.query.priority) {
    searchState.data.levelId = route.query.priority;
    searchState.data.findTerm = 6;
    let _location = window.location.href.split("?")[0];
    window.history.replaceState({}, "", _location);
  }
}
let state = reactive({
  levelData: Level_Data,
});
let { levelData } = toRefs(state);

// 搜索按钮
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}

function reset() {
  searchState.data = {
    levelId: "",
    findTerm: "",
    title: "",
  };
  search();
}
// 列表配置项
const columns = [
  {
    prop: "id",
    label: "",
    slotName: "radio",
    width: "80px",
  },
  {
    prop: "title",
    label: "事件名称",
    slotName: "title",
  },

  {
    prop: "levelId",
    label: "事件级别",
    slotName: "level",
    width: 100,
  },
  {
    prop: "levelId",
    label: "任务",
    slotName: "taskProgress",
  },
  {
    prop: "closeStr",
    label: "事件状态",
    width: 90,
  },
  {
    prop: "auditResultStatusStr",
    label: "审核状态",
    width: 90,
  },
  {
    prop: "createName",
    label: "分析师",
    width: 90,
  },
  {
    prop: "assetsName",
    label: "受影响资产",
  },
  {
    prop: "spare1",
    label: "可疑对象",
  },

  {
    prop: "createTime",
    label: "创建时间",
    width: 150,
  },
];

let emits = defineEmits(["getEventId"]);
function toDetail(row) {
  /* 新增 - 事件引用查询分析 */
  router.push({
    name: "EventDetail",
    params: {
      id: row.id,
    },
    query: {
      auditResultId: row.auditResultId,
    },
  }); //路由跳转
}

// 列表操作方法
// 新增按钮
// function newlyAdded() {
//   router.push({
//     name: "AddEvent",
//   }); //路由跳转
// }

defineExpose({
  eventId: computed(() => {
    return eventId.value;
  }),
  eventInfo: computed(() => {
    if (tableRef.value) {
      return tableRef.value.data.find((item) => {
        return item.id == eventId.value;
      });
    } else {
      return {};
    }
  }),
  search,
});
</script>
<style scoped lang="scss">
.upload-button {
  margin-right: 10px;
}
.event-tag-box {
  color: #63729d;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 6px;
  span {
    min-width: 46px;
    min-height: 24px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background: #eef5ff;
    color: #63729d;
    padding-left: 12px;
    padding-right: 12px;
    margin-left: 10px;
    border-radius: $radiusSM;
    transition: all 0.3s;
    margin-bottom: 5px;
  }
}
</style>
<style>
.event-table.el-table tr:hover .event-tag-box span {
  background: #dbe5f5;
}
</style>
