<template>
  <xelDialog title="添加关联资产" ref="dialogRef" @close="$emit('closeDialog', 'asset')" size="large" :ishiddenDialog="true">
    <section>
      <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset" />
    </section>
    <el-tabs v-model="activeName" @tab-click="changeTab(activeName)">
      <el-tab-pane :lazy="isLazy" label="业务系统对象" name="first">
        <BusinessAssets
          ref="businessRef"
          :isDialog="true"
          isDialogType="associated"
          @connect="connectReason"
          :assetsInfos="assetsInfos"
          :addEvent="addEvent"
        ></BusinessAssets>
      </el-tab-pane>
      <el-tab-pane :lazy="isLazy" label="计算设备对象" name="second">
        <BasicAssets
          ref="basicRef"
          :isDialog="true"
          :addEvent="addEvent"
          isDialogType="associated"
          @connect="connectMultiReason"
          :assetsInfos="assetsInfos"
        ></BasicAssets>
      </el-tab-pane>
      <el-tab-pane :lazy="isLazy" label="终端资产对象" name="third">
        <Terminal
          ref="terminalRef"
          :isDialog="true"
          :addEvent="addEvent"
          isDialogType="associated"
          @connect="connectMultiReason"
          :assetsInfos="assetsInfos"
        ></Terminal>
      </el-tab-pane>
      <el-tab-pane :lazy="isLazy" label="待确认资产" name="fourth">
        <Confirm ref="confirmRef" @connect="connectReason" :addEvent="addEvent" :assetsInfos="assetsInfos"></Confirm>
      </el-tab-pane>
    </el-tabs>
  </xelDialog>
  <associate-reason ref="singelReason" v-if="isshow" @close="closeReason" :reasonInfo="reasonInfo"></associate-reason>
  <multi-reason ref="multiRef" v-if="isMultiShow" @close="closeMultiReason" :reasonInfo="reasonInfo"></multi-reason>
</template>
<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import BusinessAssets from "@/views/securityAssets/businessAssets/index.vue";
import BasicAssets from "@/views/securityAssets/underlying/index.vue";
import Terminal from "@/views/securityAssets/terminal/index.vue";
import AssociateReason from "./associateReason.vue";
import MultiReason from "./multiReason.vue";
import Confirm from "./confirm.vue";
let emit = defineEmits(["close", "connect", "closeDialog"]);
let multiRef = ref();
let singelReason = ref();
let dialogRef = ref();
let businessRef = ref();
let basicRef = ref();
let terminalRef = ref();
let confirmRef = ref();
let isshow = ref(false);
let isMultiShow = ref(false);
let activeName = ref("first");
let reasonInfo = reactive({});
let assetAllData = reactive({});
let assetsInfos = ref("");
onMounted(() => {
  dialogRef.value.open();
});
let props = defineProps({
  assetList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  addEvent: {
    type: String,
    default: "",
  },
});
watch(
  () => props.assetList.length,
  (val) => {
    assetsInfosFn();
  },
  {
    immediate: true,
  }
);
let searchState = reactive({
  data: {
    globalSearch: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "globalSearch",
      label: "关键字",
      width: "350px",
      labelWidth: "60px",
    },
  ],
});
const isLazy = ref(false); //是否懒加载
// 搜索
function search() {
  setTimeout(() => {
    businessRef.value?.getListData(true, searchState.data);
    basicRef.value?.getListData(true, searchState.data);
    terminalRef.value?.getListData(true, searchState.data);
    confirmRef.value?.getExpandRow(searchState.data, true);
  }, 500);
}
//重置
function reset() {
  searchState.data = {
    globalSearch: "",
  };
  businessRef.value?.getListData(true, {}, false);
  basicRef.value?.getListData(true, {}, false);
  terminalRef.value?.getListData(true, {}, false);
  confirmRef.value.getExpandRow({}, false);
}
function assetsInfosFn() {
  let assetsArr = [];
  props.assetList.forEach((dv, di) => {
    assetsArr.push(dv.id + "@_ssp_@" + dv.assetTypeCode + "@_ssp_@" + dv.reason);
  });
  assetsInfos.value = assetsArr.join("@_assets_@");
}
function changeTab(name) {
  activeName.value = name;
}
// 关联要素
function connectReason(data) {
  isshow.value = true;
  reasonInfo.value = data;
}
// 关联多个要素
function connectMultiReason(data) {
  console.info(data);
  isMultiShow.value = true;
  reasonInfo.value = data;
}
// 关闭关联要素
function closeReason(type, data) {
  if (type == "submit") {
    emit("close", "asset", data);
  }
  isshow.value = false;
}
//
function closeMultiReason(type, data) {
  isMultiShow.value = false;
  if (type == "submit") {
    emit("close", "asset", data);
  }
}
defineExpose({
  businessRef: computed(() => businessRef.value),
  basicRef: computed(() => basicRef.value),
  terminalRef: computed(() => terminalRef.value),
  confirmRef: computed(() => confirmRef.value),
});
</script>

<style lang="scss" scoped></style>
