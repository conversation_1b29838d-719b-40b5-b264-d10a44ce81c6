<!-- 相关漏洞 -->
<template>
  <el-tabs v-model="activeName">
    <el-tab-pane :lazy="true" label="业务系统漏洞" name="first">
      <vuln-list ref="vulnListRef1" :is-event="true" type="business" :key="first" :relation="relation" @checkIds="checkIds"></vuln-list>
    </el-tab-pane>
    <el-tab-pane :lazy="true" label="基础资源漏洞" name="second">
      <vuln-list ref="vulnListRef2" :is-event="true" type="basic" :key="second" :relation="relation" @checkIds="checkIds"></vuln-list>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";

import vulnList from "@/views/vuln/business/index.vue";
import updateByTabName from "../mixins/updateByTabName";
updateByTabName(4, () => {
  vulnListRef1.value && vulnListRef1.value.search();
  vulnListRef2.value && vulnListRef2.value.search();
});
let props = defineProps({
  //是否与事件关联
  relation: {
    type: String,
    default: "relation",
  },
});

let emits = defineEmits("checkIds");

function checkIds(a, b, c) {
  emits("checkIds", a, b, c);
}

let activeName = ref("first");

let vulnListRef1 = ref();
let vulnListRef2 = ref();

defineExpose({
  update: () => {
    vulnListRef1.value && vulnListRef1.value.search();
    vulnListRef2.value && vulnListRef2.value.search();
  },
});
</script>

<style lang="scss" scoped></style>
