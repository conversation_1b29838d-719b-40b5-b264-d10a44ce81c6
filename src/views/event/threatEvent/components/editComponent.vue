<!-- 资产基本信息表单 -->
<template>
  <el-form v-loading="!(status && groupStatus)" :model="formData" ref="ruleFormRef" label-width="120px" size="mini">
    <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
  </el-form>
</template>
<script setup>
import { ref, reactive, toRefs, computed, watch } from "vue";
import { selectPage } from "@/api/securityAssets/assetGroup";
import { getCanSelectPersonByDept } from "@/api/securityAssets/business";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { assetTypeList } from "@/config/constant";

let assetTypeListSelf = assetTypeList;
//待确认资产新增
if (route.params.confirmId) {
  if (route.params.type == 0) {
    assetTypeListSelf = assetTypeList.filter((assetType) => assetType.value == 0);
  } else {
    assetTypeListSelf = assetTypeList.filter((assetType) => assetType.value != 0);
  }
}
// 工作台新增资产
if (route.params.assetId) {
  assetTypeListSelf = assetTypeList.filter((assetType) => assetType.value != 0);
}
let props = defineProps({
  editInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
//是否是变更资产页面
let isChangeAsset = computed(() => {
  return route.name == "ChangeAsset";
});
let isEdit = computed(() => {
  return !!props.editInfo.id;
});
let status = ref(isEdit.value ? false : true);
let groupStatus = ref(isEdit.value ? false : true);

let state = reactive({
  formData: {
    title: "",
    createTime: [],
    eventTagList: [],
    eventDeptList: [],
    levelId: [],
  },
});

let { formData } = toRefs(state);

let formList = reactive([
  {
    formType: "input",
    prop: "title",
    label: "标题",
    required: true,
  },
  {
    formType: "level",
    prop: "levelId",
    label: "事件级别",
    required: true,
    options: [], //字典自定义
    // 字典关键字
    dictName: "event_level",
  },
  {
    isShow: false,
    formType: "input",
    prop: "createName",
    label: "创建人",
    required: true,
  },
  {
    formType: "daterange",
    type: "datetimerange",
    prop: "createTime",
    label: "推断发生时间",
    required: true,
    onChange: (val) => {
      state.formData.createTime = val;
    },
  },

  {
    formType: "tag",
    prop: "eventTagList",
    label: "标签",
  },
  {
    formType: "deptTree",
    prop: "eventDeptList",
    label: "部门",
    multiple: true,
    onCurrentChange(val) {
      // getCanSelectPersonByDeptFn(val);
    },
  },
]);

//获取资产组
selectPage({ pageNum: 1, pageSize: 100 }).then((res) => {
  formList[2].options = res.data.rows.map((item) => {
    return {
      value: item.id,
      label: item.groupName,
    };
  });
  groupStatus.value = true;
});

//编辑  回显信息
function echoData() {
  state.formData.title = props.editInfo.title;
  state.formData.createName = props.editInfo.createName;

  state.formData.createTime[0] = props.editInfo.beaginTime;

  state.formData.createTime[1] = props.editInfo.endTime;
  state.formData.eventTagList = props.editInfo.eventTagList.map((item) => {
    return {
      id: item.id,
      name: item.tagName,
    };
  });
  state.formData.eventDeptList = props.editInfo.eventDeptList.map((item) => {
    return item.deptId;
  });
  state.formData.levelId = props.editInfo.levelId + "";
  status.value = true;
}
echoData();
let wacher = watch(
  () => props.editInfo,
  () => {
    echoData();
    wacher();
  }
);

let ruleFormRef = ref();

defineExpose({
  formData: state.formData,
});
</script>

<style lang="scss" scoped></style>
