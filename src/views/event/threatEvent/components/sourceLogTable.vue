<template>
  <div style="width: 100%; margin-bottom: 20px">
    <common-search v-model="searchState.data" :form-list="searchState.formList" @search="getList" @reset="reset" label-width="0px">
      <el-button @click="handLog" class="search-button"> 相关日志检索 </el-button>
    </common-search>
    <div v-if="data.length">
      <el-table :data="dataFun(data[0].esLogList)" style="width: 100%">
        <el-table-column v-for="fitem in data[0].showFields" :key="fitem" :prop="fitem.value" :label="fitem.name">
          <template #default="scope">
            {{ scope.row[fitem.value + "Text"] || scope.row[fitem.value] }}
          </template>
        </el-table-column>
        <el-table-column width="60px" label="操作" align="center">
          <template #default="scope">
            <span class="clickable" @click="toMessageDetail(scope.row)">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-chakan"></use>
              </svg>
              查看</span
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页  -->
      <xel-pagination
        ref="paginationRef"
        class="xel-table-pagination"
        :total="total"
        :page-size="10"
        :normalPage="false"
        @change="changePagination"
      />
    </div>

    <div v-else class="text-center">
      {{ loading ? "加载中..." : "暂无数据" }}
    </div>
  </div>

  <el-drawer v-model="logDetailShow">
    <log-detail v-if="logDetailShow" ref="log_detail" :logDetail="logDetail" formType="event" :type_list="data[0].allFields"></log-detail>
  </el-drawer>
</template>

<script setup>
import LogDetail from "@/views/sime/assetdetect/spaceAsset/components/syslogDetail.vue";
import { getEventTaskEsLog } from "@/api/event/task";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();
import { useStore } from "vuex";
const store = useStore();
let props = defineProps({
  /* 日志id */
  id: {
    type: String || Number,
    default: () => {
      return "";
    },
  },
  /* 事件任务id */
  eventTaskId: {
    type: String || Number,
    default: () => {
      return "";
    },
  },
  /* 事件任务id */
  esLogList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  /* 事件任务id */
  showFields: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

/* 查询相关 */
let searchState = reactive({
  data: {
    searchValue: "",
    pageNum: 1,
    pageSize: 10,
  },
  formList: [
    {
      formType: "input",
      prop: "searchValue",
      label: "",
      placeholder: "请输入查询条件",
      itemWidth: "300px",
    },
  ],
});

let loading = ref(false);
let paginationRef = ref();

/* 获取数据 */
let data = ref([]);
let total = ref(0);
const getList = () => {
  loading.value = true;
  getEventTaskEsLog({
    id: props.id,
    eventTaskId: props.eventTaskId,
    ...searchState.data,
  })
    .then((res) => {
      data.value = res.data.rows;
      total.value = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
};
getList();

let logDetailShow = ref(false);
let logDetail = ref({});
/* 跳转详情 */
const toMessageDetail = (row) => {
  logDetail.value = row;
  logDetailShow.value = true;
};

/* 相关日志检索 */
let saveData = ref(JSON.parse(JSON.stringify(store.state.siem.saveData)));
const handLog = () => {
  /* 数据组装 */
  saveData.value.filtersType = data.value[0].filtersType;
  saveData.value.startTime = data.value[0].startTime;
  saveData.value.endTime = data.value[0].endTime;
  saveData.value.timeWindow = data.value[0].timeWindow;
  saveData.value.filterId = data.value[0].queryFilterId;
  saveData.value.filters = JSON.parse(data.value[0].filters);

  /* 将数据data存储到sessionStorage中*/
  sessionStorage.setItem("alertForm", JSON.stringify(saveData.value));
  store.commit("setTabType", "btn");
  if (import.meta.env.VITE_IS_OUTPOST) {
    router.push("/simeSearch/analysis/alert");
  } else {
    window.open("/simeSearch/analysis/alert", "_blank");
  }
};

/* 数据格式调整， 将data中的logJson转成对象 */
const dataFun = (data) => {
  let newData = [];
  data.forEach((item) => {
    newData.push(JSON.parse(item.logJson));
  });
  return newData;
};

/* 分页 */
const changePagination = (params) => {
  searchState.data.pageNum = params.pageNum;
  searchState.data.pageSize = params.pageSize;
  getList();
};

/* 重置 */
const reset = () => {
  searchState.data = {
    searchValue: "",
    pageNum: 1,
    pageSize: 10,
  };
  paginationRef.value && paginationRef.value.resetPageNum();
  getList();
};
</script>

<style scoped lang="scss">
.clickable {
  color: $color;
  cursor: pointer;
}
.text-center {
  color: #848484;
}
</style>
