<template>
  <!-- 操作按钮 -->
  <div class="btns-box text-right">
    <el-button size="mini" @click="toLogList" v-if="eventTaskButs.eventTaskButShow">
      <el-icon> <Tickets /></el-icon>
      任务日志汇总</el-button
    >
    <el-button v-if="isManageTask == 0 && eventTaskFlag1 ? eventTaskButs.eventTaskButShow : false" size="mini" @click="openadd">
      <el-icon><plus /></el-icon>
      添加任务</el-button
    >
    <el-button v-if="isManageTask == 1 && eventTaskFlag2 ? eventTaskButs.eventTaskButShow : false" size="mini" @click="openadd">
      <el-icon><plus /></el-icon>
      添加任务</el-button
    >
    <el-button size="mini" v-if="bachAuditFlag ? eventTaskButs.auditResultStatus == 4 : false" @click="batchCheck">
      <el-icon> <Checked /></el-icon>
      批量审核</el-button
    >
  </div>
  <section v-for="(item, index) in taskGroupList" :key="index">
    <p class="title">{{ item.group }}({{ item.totle }}项任务)</p>
    <xel-table :pagination="false" :data="item.taskList || []" :columns="columns">
      <template #expand="{ row }">
        <div class="info-box">
          <p>
            <span class="label">任务要求：</span>
            <span v-html="row.detail" style="word-break: break-all"></span>
          </p>
          <p>
            <span class="label">审核状态：</span>
            <span class="margin-left5" v-html="getAuditComment(row)"></span>
          </p>

          <!-- 新增 - 迁移执行日志 -->
          <div>
            <span class="label">执行日志：</span>
            <div style="width: 100%">
              <log-group :list="row.logList" v-if="row.logList && row.logList.length" />
              <span v-else class="margin-left5" style="font-size: 12px">暂无数据</span>
            </div>
          </div>

          <!-- 新增 - 相关原始日志 -->
          <RelatedSourceLog :eventTaskId="row.id" />
        </div>
      </template>
      <template #auditStatus="{ row }">
        <audit-status :status="row.auditStatus"></audit-status>
      </template>
      <template #taskName="{ row }">
        <div :class="{ 'xel-clickable': $store.state.eventDetail.eventInfo.status != 0 }" class="task-name" @click="toDetail(row)">
          {{ row.title }}
        </div>
      </template>
      <template #btns="scope">
        <xel-handle-btns :btnList="getBtnList(scope.row)" :scope="scope"></xel-handle-btns>
      </template>
    </xel-table>
  </section>

  <!-- 任务审核弹框 -->
  <xel-dialog title="批量审核" ref="auditDialogRef" @submit="batchCheckTask">
    <el-form ref="checkFormRef" style="padding: 0 10px" :model="checkForm">
      <el-row class="margin-bottom10" v-for="(item, index) in checkList" :key="index" :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务标题："> {{ item.title }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <xel-form-item
            label="激励加分"
            :prop="item.id"
            v-model="checkForm[item.id]"
            form-type="number"
            @input="(val) => changeReward(val, item.id)"
          ></xel-form-item>
        </el-col>
      </el-row>
    </el-form>
  </xel-dialog>

  <!-- 添加任务 -->
  <xel-dialog ref="addDialogRef" title="添加任务" @submit="addTaskFn">
    <add-task
      v-if="addFormStatus"
      :hideBtn="true"
      ref="addTaskRef"
      :add-item="saveEventTask"
      :dictName="props.isManageTask ? 'manage_task_group' : 'task_group'"
      :otherParams="{ eventId: $route.params.id, isManageTask: isManageTask }"
      @close="closeAdd"
    ></add-task>
  </xel-dialog>
</template>
<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";

import AddTask from "./addTask.vue";
import taskHandle from "../mixins/taskHandle";
import { getEventDetail } from "@/api/event/detail";
import RelatedSourceLog from "./relatedSourceLog.vue";
import { getLogList, initTaskList, saveEventTaskAudit, saveEventTask, selectNotAuditedTask, getEventTaskEsLog } from "@/api/event/task";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";
const store = useStore();

/* 新增 - 迁移执行日志 */
import logGroup from "./logGroup.vue";

//更新页面
let taskUpdate = computed(() => {
  return store.state.eventDetail.taskUpdate;
});
watch(
  () => taskUpdate.value.status,
  () => {
    getList();
  }
);

let { handlerTask } = taskHandle();

let props = defineProps({
  isManageTask: {
    type: String,
    default: "",
  }, //0 分析 1处置
  tabName: {
    type: [String, Number],
    default: 0,
  },
  selfName: {
    type: String,
    default: "",
  },
});

watch(
  () => props.tabName,
  (val) => {
    if (val == props.selfName) {
      getList();
    }
  }
);
//事件状态标识
const eventTaskButs = computed(() => {
  return store.state.eventDetail.eventTaskButs;
});
let bachAuditFlag = computed(() => {
  //从工作台和未通过事件列表进入的事件详情：当auditResultPassShow.value == 4时才能审核任务
  if (eventTaskButs.value.auditResultStatus == 4) {
    return store.state.eventDetail.eventDetail.bachAuditFlag == "Y";
  } else {
    return false;
  }
});
//事件分析页 添加任务权限
let eventTaskFlag1 = computed(() => {
  return store.state.eventDetail.eventDetail.taskAdd == "Y";
});
let eventTaskFlag2 = computed(() => {
  return store.state.eventDetail.eventDetail.manageAdd == "Y";
});

let taskGroupList = ref([]);
getList();
function getList() {
  initTaskList({ eventId: route.params.id, isManageTask: props.isManageTask }).then(({ data }) => {
    /*taskGroupList.value = data.taskGroupList.filter((item) => item.totle);*/
    let list = data.taskGroupList.filter((item) => item.totle);
    list.forEach((item1) => {
      item1.taskList.forEach((item2) => {
        item2.logList = [];
        /* 新增 - 执行日志 */
        getLogList({ eventTaskId: item2.id, limit: 0 }).then((res) => {
          item2.logList = res.data.rows;
        });
      });
    });
    taskGroupList.value = list;
    store.commit("changeTabNumsByName", {
      name: props.isManageTask == 0 ? 1 : 2,
      num: data.taskGroupList[0].taskCount,
    });
  });
}

let columns = ref([
  {
    type: "expand",
    slotName: "expand",
  },
  { prop: "taskGroupText", label: "阶段", width: "100px" },
  {
    prop: "title",
    label: "任务",
    slotName: "taskName",
  },
  { prop: "createTime", label: "创建时间" },
  { prop: "assigneeName", label: "分析师" },
  { prop: "auditStatus", label: "审核", slotName: "auditStatus" },
  { prop: "auditName", label: "审核人" },
  {
    label: "操作",
    /*fixed: "right",*/
    slotName: "btns",
    width: "185px",
  },
]);

function getAuditComment(row) {
  let str = "";
  let auditStatus = Number(row.auditStatus);
  if ([0, 1, 2].includes(auditStatus)) {
    str = "未审核";
  } else {
    if (row.auditName) {
      if ([3, 4].includes(auditStatus)) {
        str = `<span class="margin-left10">审核人：${row.auditName}</span><span class="margin-left10">审核意见：${row.audiComment}</span>`;
        if (auditStatus == 4) {
          str = `审核不通过 ` + str;
        } else if (auditStatus == 3) {
          str = `审核通过 ` + str;
        }
        // else if (auditStatus == 2) {
        //   str = `上次审核结果：<span class="margin-left10">审核通过</span>` + str;
        // }
      }
    } else {
      if (auditStatus == 3) {
        str = "未抽检";
      }
    }
  }
  return str;
}

function getBtnList(row) {
  // 任务信息操作：
  // hide： eventTaskButs.value.auditResultStatus !=4（eventTaskButs.value.auditResultStatus ==4--审核通过--时显示）
  // 开始  删除  关闭 重做 填日志  添加任务 任务日志汇总 及信息编辑 检索日志 条件： event_task_editable_before_adopt=1 且 audit_resule_status!=4
  // 任务审核相关操作：
  // 重审  审核  批量审核 只控 eventTaskButs.value.auditResultStatus ！=4，
  // 提报   event_task_editable_before_adopt=1，直接可以提报，=0时，eventTaskButs.value.auditResultStatus !=4才可以
  return [
    {
      hide: row.startFlag != "Y" ? true : !eventTaskButs.value.eventTaskButShow,
      icon: "VideoPlay",
      title: "开始",
      onClick(scope) {
        handlerTask("start", scope.row, props.isManageTask, true, store);
      },
    },
    {
      hide: row.submitFlag != "Y" ? true : eventTaskButs.value.editable ? false : eventTaskButs.value.auditResultStatus != 4,
      icon: "Upload",
      title: "提报",
      onClick(scope) {
        handlerTask("submit", scope.row, props.isManageTask, true, store);
      },
    },
    {
      hide: row.deleteFalg != "Y" ? true : !eventTaskButs.value.eventTaskButShow,
      icon: "Delete",
      title: "删除",
      onClick(scope) {
        handlerTask("del", scope.row, props.isManageTask, true, store);
      },
    },
    {
      hide: row.reauditFlag != "Y" ? true : eventTaskButs.value.auditResultStatus != 4,
      icon: "RefreshRight",
      title: "重审",
      onClick(scope) {
        toDetail(scope.row, { reaudit: true });
      },
    },
    {
      hide: row.closeFlag != "Y" ? true : !eventTaskButs.value.eventTaskButShow,
      icon: "CircleClose",
      title: "关闭",
      onClick(scope) {
        handlerTask("close", scope.row, props.isManageTask, true, store);
      },
    },
    {
      hide: row.redoFlag != "Y" ? true : !eventTaskButs.value.eventTaskButShow,
      isFont: "icon-fanhui",
      title: "重做",
      onClick(scope) {
        handlerTask("redo", scope.row, props.isManageTask, true, store);
      },
    },
    {
      width: "300px",
      hide: row.auditFlag != "Y" ? true : eventTaskButs.value.auditResultStatus != 4,
      icon: "Checked",
      title: "审核",
      onClick(scope) {
        toDetail(scope.row, { audit: true });
      },
    },
  ];
}

//批量审核
let checkList = ref([]);
let auditDialogRef = ref();
let checkFormRef = ref();
let checkForm = ref({});
async function batchCheck() {
  let { data } = await selectNotAuditedTask({ eventId: route.params.id, isManageTask: props.isManageTask });
  checkList.value = data.taskList;
  if (checkList.value.length == 0) {
    ElMessage.warning("没有可审核的任务");
    return;
  }
  checkForm.value = {};
  for (let item of checkList.value) {
    checkForm.value[item.id] = 0;
  }
  auditDialogRef.value.open();
}

function batchCheckTask() {
  checkFormRef.value.validate((valid) => {
    if (valid) {
      let eventTaskList = [];

      for (let id in checkForm.value) {
        eventTaskList.push({
          id,
          reward: checkForm.value[id],
        });
      }

      for (let id in checkForm.value) {
        if (!checkForm.value[id] && checkForm.value[id] != 0) {
          ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
          return;
        }
      }

      saveEventTaskAudit({ eventTaskList, id: route.params.id }).then(() => {
        ElMessage.success("审核成功!");
        getList(); //刷新

        auditDialogRef.value.close();
      });
    }
  });
}

//任务日志汇总
function toLogList() {
  store.commit("pushEventTab", {
    name: "logList" + props.isManageTask, //任务id或者其他唯一值
    label: "任务日志汇总",
    type: "logList", //确定组件
    isManageTask: props.isManageTask,
  });
}

//添加任务
let addDialogRef = ref();
let addFormStatus = ref(false);
function openadd() {
  if (store.state.eventDetail.eventInfo.isLock == 1) {
    ElMessage.warning("事件被抽检中，请抽检完毕后再新增或重做该事件的任务");
    return;
  }
  addFormStatus.value = false;
  addDialogRef.value.open();
  nextTick(() => {
    addFormStatus.value = true;
  });
}
function closeAdd(a, update) {
  addDialogRef.value.close();
  if (update) {
    getList(); //刷新
  }
}
let addTaskRef = ref();
function addTaskFn() {
  addTaskRef.value.submitForm();
}

//打开详情tab
function toDetail(row, params = {}) {
  if (store.state.eventDetail.eventInfo.status != 0) {
    store.commit("pushEventTab", {
      name: row.id,
      label: row.title,
      type: "taskDetail",
      isManageTask: props.isManageTask,
      ...params,
    });
  }
}

function changeReward(value, id) {
  if (value > 10000 || value < -10000 || (!value && value != 0)) {
    checkForm.value[id] = null;
    ElMessage.warning("激励加分请输入-10000 到 10000之间的整数");
  }
  if (value) {
    nextTick(() => {
      checkForm.value[id] = parseInt(value);
    });
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-table--small .el-table__cell) {
  padding: 0;
}
:deep(.el-table .cell) {
  padding: 8px 0;
}
.btns-box {
  border-bottom: 2px dashed #ededed;
  padding: 10px 0 20px;
}
.title {
  margin: 20px 0;
}
.info-box {
  background: #f9f9f9;

  p,
  > div {
    padding: 10px 20px;
    line-height: 30px;
    display: flex;
    border-bottom: 1px solid #ebedf1;
    .label {
      color: $fontColorSoft;
      width: 7em;
      flex-shrink: 0;
    }
  }
}
.task-name {
  padding-right: 5px;
}
</style>
