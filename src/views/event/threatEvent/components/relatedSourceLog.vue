<template>
  <div class="relatedSourceLogDiv">
    <div v-if="data.length" class="logLeft">
      <div style="width: 100%" v-for="(item, index) in data" :key="item">
        <div class="logTit">
          相关原始日志（{{ numToChinese(index + 1) }}）：
          <el-link :underline="false" @click="handleDelete(item)" v-if="canUpdateLog">
            <el-icon>
              <Delete />
            </el-icon>
          </el-link>
        </div>
        <div style="width: 100%">
          <SourceLogTable :eventTaskId="eventTaskId" :id="item" />
        </div>
      </div>
    </div>
    <div v-else class="logRight">
      <span class="label">相关原始日志：</span>
      <span class="margin-left5" style="font-size: 12px">{{ loading ? "正在加载中..." : "暂无数据" }}</span>
    </div>
  </div>
</template>

<script setup>
import SourceLogTable from "./sourceLogTable.vue";
import { deleteEventTaskEsLog, getEventTaskEsLogGroupIdList } from "@/api/event/task";
import { batchDelete } from "@/utils/delete";
import { numToChinese } from "@/utils/public";
import { ref } from "vue";
import { ElMessage } from "element-plus";

let props = defineProps({
  /* 事件任务id */
  eventTaskId: {
    type: String || Number,
    default: () => {
      return "";
    },
  },
  /* 权限标识, Y 表示可以删除 */
  canUpdateLog: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
});

let loading = ref(false);

/* 获取数据 */
let data = ref([]);
const getEventTaskList = () => {
  loading.value = true;
  getEventTaskEsLogGroupIdList({ eventTaskId: props.eventTaskId })
    .then((res) => {
      data.value = res.data.data;
    })
    .finally(() => {
      loading.value = false;
    });
};
getEventTaskList();

/* 删除 */
const handleDelete = (id) => {
  batchDelete().then(() => {
    deleteEventTaskEsLog(id).then(() => {
      ElMessage.success("删除成功");
      getEventTaskList();
    });
  });
};
</script>

<style scoped lang="scss">
.relatedSourceLogDiv {
  width: 100%;
  .logRight,
  .logTit {
    color: #848484;
  }
  .logLeft {
    width: 100%;
    .logTit {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      border-bottom: 1px solid #ebedf1;
      padding-bottom: 10px;
    }
  }
  .margin-left5 {
    color: var(--el-table-font-color);
  }
}
</style>
