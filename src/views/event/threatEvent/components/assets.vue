<template>
  <el-row>
    <el-col :offset="22" :span="1">
      <el-button v-if="eventAssetAdd" @click="showAsset = true" class="search-button">
        <el-icon> <Connection /> </el-icon>关联
      </el-button>
    </el-col>
  </el-row>

  <section v-for="(item, index) in assetGroupList" :key="index">
    <div v-if="item.total > 0">
      <p class="title">{{ item.assetTitle }}</p>
      <el-table :data="item.assetList" style="width: 100%">
        <el-table-column prop="name" label="名称" v-if="item.type == '0' || item.type == '1' || item.type == '2'">
          <template #default="scope">
            <p @click="(assetData = scope.row), (isshow = true)" class="clickable">{{ scope.row.name }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" v-else> </el-table-column>
        <el-table-column prop="assetsGroupName" label="资产组"></el-table-column>
        <el-table-column prop="ip" label="IP" v-if="item.type != '3'">
          <template #ip="scope">
            <p v-if="scope.row.ip">{{ scope.row.ip }}</p>
          </template>
        </el-table-column>

        <el-table-column prop="ports" label="端口" v-if="item.type == '0' || item.type == '1'">
          <template #ports="scope">
            <p v-if="scope.row.ports">{{ scope.row.ports }}</p>
          </template>
        </el-table-column>
        <el-table-column label="系统入口" prop="domains" v-if="item.type == '0' || item.type == '3'">
          <template #domains="scope">
            <p v-if="scope.row.domains">{{ scope.row.domains }}</p>
          </template>
        </el-table-column>
        <el-table-column label="关联要素" prop="content">
          <template #content="scope">
            <p v-if="scope.row.content">{{ scope.row.content }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <ul class="action-btns-ul">
              <li v-if="scope.row.cancelFlag == 'Y'" @click="cancelConnect(scope.row)" title="取消关联">
                <el-icon> <Remove /> </el-icon>
              </li>
            </ul>
          </template>
        </el-table-column>
      </el-table>
      <xel-pagination
        ref="paginationRef"
        class="xel-table-pagination"
        :total="item.total"
        :init="false"
        @change="changePagination(item.type, $event)"
      ></xel-pagination>
    </div>
  </section>
  <asset-detail v-if="isshow" @search="closeValue" :assetData="assetData" @closeDialog="isshow = false"></asset-detail>
  <!-- 关联资产 -->
  <add-asset ref="addAssetRef" v-if="showAsset" @close="closeComponent" @closeDialog="(showAsset = false), getList()"></add-asset>
</template>
<script setup>
import { ref, reactive, toRefs, computed } from "vue";
import {
  selectBussiness,
  getRelResourceList,
  selectEventAssetsTerminal,
  getDomainConfirmList,
  selectIpConfirm,
  businessDisassociate,
  cancelEventResourceRel,
  terminalDisassociate,
  saveBusinessRelevancy,
  saveConnectResource,
  saveEventAssetsTerminal,
  saveConnectDomainConfirm,
  saveEventIpConfirmRel,
  deleteEventIpConfirmRel,
  cancelEventDomainConfirmRel,
} from "@/api/event/asset.js";
import { useRouter, useRoute } from "vue-router";
import AssetDetail from "./assetDetail.vue";
import AddAsset from "./addAsset.vue";
import { useStore } from "vuex";
const store = useStore();
const router = useRouter();
const route = useRoute();
import updateByTabName from "../mixins/updateByTabName";
updateByTabName(3, getList);
//受影响资产权限
let eventAssetAdd = computed(() => {
  return store.state.eventDetail.eventDetail.eventAssetAdd == "Y";
});

let tableRef = ref();
let addAssetRef = ref();
let state = reactive({
  assetGroupList: [
    { type: "0", assetTitle: "业务系统对象", total: 0, assetList: [] },
    { type: "1", assetTitle: "计算设备对象", total: 0, assetList: [] },
    { type: "2", assetTitle: "终端资产", total: 0, assetList: [] },
    { type: "3", assetTitle: "域名待确认资产", total: 0, assetList: [] },
    { type: "4", assetTitle: "IP待确认资产", total: 0, assetList: [] },
  ],
});
let { assetGroupList } = toRefs(state);
let showAsset = ref(false);
let paginationRef = ref();
let columns = ref([
  {
    prop: "name",
    label: "名称",
    click({ row }) {
      isshow.value = true;
      assetData.value = row;
    },
  },
  { prop: "assetsGroupName", label: "资产组" },
  { prop: "ip", label: "IP" },
  { prop: "ports", label: "端口", slotName: "ports" },
  { prop: "domains", label: "系统入口", slotName: "domains" },
  { prop: "content", label: "关联要素", slotName: "content" },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "remove",
        title: "取消关联",
        onClick(scope) {
          cancelConnect(scope.row);
        },
      },
    ],
  },
]);
let isshow = ref(false);
let assetType = ref("");
let assetId = ref("");
let assetData = ref({});
function changePagination(type, pageParams) {
  switch (type) {
    case "0":
      getBussinessList(pageParams);
      break;
    case "1":
      getResourceData(pageParams);
      break;
    case "2":
      getTerminalData(pageParams);
      break;
    case "3":
      getDomainList(pageParams);
      break;
    case "4":
      getIpData(pageParams);
      break;
  }
}
getList();
//// 业务系统对象
function getBussinessList(pageParams) {
  selectBussiness({
    eventId: route.params.id,
    pageNum: pageParams ? pageParams.pageNum : 1,
    pageSize: pageParams ? pageParams.pageSize : 10,
  }).then(({ data }) => {
    let bussinessList = [];
    if (data.total > 0) {
      bussinessList = data.rows.map((rItem) => {
        return {
          id: rItem.id,
          name: rItem.name,
          assetsGroupName: rItem.spare1,
          ip: rItem.spare3,
          ports: rItem.spare4,
          domains: rItem.spare2,
          content: rItem.spare5,
          type: "0",
          resourceId: rItem.resourceId,
          createBy: rItem.createBy,
          cancelFlag: rItem.cancelFlag,
        };
      });
    }
    state.assetGroupList.forEach((item) => {
      if (item.type == "0") {
        item.total = data.total;
        item.assetList = bussinessList;
      }
    });
  });
}
// 计算设备对象
function getResourceData(pageParams) {
  getRelResourceList({
    eventId: route.params.id,
    pageNum: pageParams ? pageParams.pageNum : 1,
    pageSize: pageParams ? pageParams.pageSize : 10,
  }).then(({ data }) => {
    let basicList = [];
    if (data.total > 0) {
      basicList = data.rows.map((rItem) => {
        return {
          id: rItem.id,
          name: rItem.name,
          assetsGroupName: rItem.assetsGroupName,
          ip: rItem.ips,
          ports: rItem.ports,
          content: rItem.content,
          type: "1",
          resourceId: rItem.resourceId,
          createBy: rItem.createBy,
          cancelFlag: rItem.cancelFlag,
        };
      });
    }
    state.assetGroupList.forEach((item) => {
      if (item.type == "1") {
        item.total = data.total;
        item.assetList = basicList;
      }
    });
  });
}
//   终端资产对象 --- 列表
function getTerminalData(pageParams) {
  selectEventAssetsTerminal({
    eventId: route.params.id,
    pageNum: pageParams ? pageParams.pageNum : 1,
    pageSize: pageParams ? pageParams.pageSize : 10,
  }).then(({ data }) => {
    let terminalList = [];
    if (data.total > 0) {
      terminalList = data.rows.map((rItem) => {
        return {
          id: rItem.id,
          name: rItem.name,
          assetsGroupName: rItem.assetsGroupName,
          ip: rItem.spare1,
          content: rItem.spare2,
          type: "2",
          resourceId: rItem.resourceId,
          createBy: rItem.createBy,
          cancelFlag: rItem.cancelFlag,
        };
      });
    }
    state.assetGroupList.forEach((item) => {
      if (item.type == "2") {
        item.total = data.total;
        item.assetList = terminalList;
      }
    });
  });
}
//   域名待确认资产 --- 列表
function getDomainList(pageParams) {
  getDomainConfirmList({
    eventId: route.params.id,
    pageNum: pageParams ? pageParams.pageNum : 1,
    pageSize: pageParams ? pageParams.pageSize : 10,
  }).then(({ data }) => {
    let domainList = [];
    if (data.total > 0) {
      domainList = data.rows.map((rItem) => {
        return {
          id: rItem.id,
          name: rItem.name,
          assetsGroupName: rItem.assetsGroupName,
          domains: rItem.domains,
          content: rItem.content,
          type: "3",
          resourceId: rItem.resourceId,
          createBy: rItem.createBy,
          cancelFlag: rItem.cancelFlag,
        };
      });
    }
    state.assetGroupList.forEach((item) => {
      if (item.type == "3") {
        item.total = data.total;
        item.assetList = domainList;
      }
    });
  });
}
// ip
function getIpData(pageParams) {
  selectIpConfirm({ eventId: route.params.id, pageNum: pageParams ? pageParams.pageNum : 1, pageSize: pageParams ? pageParams.pageSize : 10 }).then(
    ({ data }) => {
      let ipList = [];
      if (data.total > 0) {
        ipList = data.rows.map((rItem) => {
          return {
            id: rItem.id,
            name: rItem.name,
            assetsGroupName: rItem.assetsGroupName,
            ip: rItem.ip,
            content: rItem.spare2,
            type: "4",
            resourceId: rItem.resourceId,
            createBy: rItem.createBy,
            cancelFlag: rItem.cancelFlag,
          };
        });
      }
      state.assetGroupList.forEach((item) => {
        if (item.type == "4") {
          item.total = data.total;
          item.assetList = ipList;
        }
      });
    }
  );
}
function getList() {
  // 业务系统对象
  getBussinessList();
  // 计算设备对象
  getResourceData();
  //   终端资产对象 --- 列表
  getTerminalData();
  //   域名待确认资产 --- 列表
  getDomainList();
  //   ip待确认资产 --- 列表
  getIpData();
}
// 取消关联
function cancelConnect(data) {
  let cancelPorts = null;
  switch (data.type) {
    case "0":
      cancelPorts = businessDisassociate;
      break;
    case "1":
      cancelPorts = cancelEventResourceRel;
      break;
    case "2":
      cancelPorts = terminalDisassociate;
      break;
    case "3":
      cancelPorts = cancelEventDomainConfirmRel;
      break;
    case "4":
      cancelPorts = deleteEventIpConfirmRel;
      break;
  }
  cancelPorts({
    eventId: route.params.id,
    assetId: data.resourceId,
    createBy: data.createBy,
  }).then((data) => {
    store.commit("changeTabNumsByName", {
      name: 3,
      num: data.newTotal,
    });
    getList();
  });
}
// 关联要素
function closeComponent(childType, data) {
  if (data) {
    let connectPorts = null;

    switch (data.assetTypeCode) {
      case 0:
        connectPorts = saveBusinessRelevancy;
        break;
      case 1:
        connectPorts = saveConnectResource;
        break;
      case 2:
        connectPorts = saveEventAssetsTerminal;
        break;
      case 3:
        connectPorts = saveConnectDomainConfirm;
        break;
      case 4:
        connectPorts = saveEventIpConfirmRel;
        break;
    }
    connectPorts({
      eventId: route.params.id,
      assetId: data.id,
      contents: data.reason,
    }).then((res) => {
      // 刷新各关联列表
      addAssetRef.value.businessRef ? addAssetRef.value.businessRef.getListData() : null;
      addAssetRef.value.basicRef ? addAssetRef.value.basicRef.getListData() : null;
      addAssetRef.value.terminalRef ? addAssetRef.value.terminalRef.getListData() : null;
      addAssetRef.value.confirmRef ? (addAssetRef.value.confirmRef.urlRef ? addAssetRef.value.confirmRef.urlRef.getListData() : null) : null;
      addAssetRef.value.confirmRef ? (addAssetRef.value.confirmRef.ipRef ? addAssetRef.value.confirmRef.ipRef.getListData() : null) : null;
      store.commit("changeTabNumsByName", {
        name: 3,
        num: res.newTotal,
      });
    });
  }
}
// 关闭
function closeValue() {
  isshow.value = false;
}
</script>

<style lang="scss" scoped>
.title {
  margin: 20px 0;
}
.clickable {
  display: inline-block;
  color: $color;
  cursor: pointer;
}
</style>
