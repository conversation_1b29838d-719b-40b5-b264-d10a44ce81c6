import router from "@/router";
let loopholeColumns = [
  {
    prop: "name",
    label: "资产名称",
  },
  {
    prop: "internetIp",
    label: "IP",
  },
  {
    prop: "domain",
    label: "系统入口",
  },
];
let eventColumns = [
  {
    prop: "type",
    label: "类型",
  },
  {
    prop: "content",
    label: "值/文件名",
  },
];
let earlyColumns = [
  {
    prop: "title",
    label: "漏洞标题",
  },
  {
    prop: "type",
    label: "漏洞类型",
  },
  {
    prop: "levelName",
    label: "漏洞等级",
  },
  {
    prop: "findTime",
    label: "漏洞时间",
  },
];

export { loopholeColumns, eventColumns, earlyColumns };
