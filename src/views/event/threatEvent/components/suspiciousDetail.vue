<template>
  <mouse-display :playName="content" @submit="saveBaseInfo" :show-submit="true" :disaboy="!$store.state.eventDetail.eventSuspiciousUpdate">
    <div class="margin-top20">
      <suspiciousObject ref="baseInfoRef" :edit-info="tabList"></suspiciousObject>
    </div>
    <template #display>
      <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
        <el-row :gutter="70">
          <el-col :span="24">
            <el-form-item label="时间：">{{ tabList.createTime }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="标签：">
              <el-tag v-for="item in tabList.tagRelList" :key="item.tagId" class="tag">{{ item.tagName }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否IOC：" v-if="tabList.ioc == '1'">是</el-form-item>
            <el-form-item label="是否IOC：" v-else>否</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述：">{{ tabList.description }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>
  </mouse-display>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import { useStore } from "vuex";
import { getDetail } from "@/api/event/suspicious.js";
import mouseDisplay from "@/views/securityAssets/businessAssets/components/mouseDisplay.vue"; //鼠标移入编辑组件
import SuspiciousObject from "./suspiciousObject.vue";
import { saveEventSuspicious } from "@/api/event/suspicious.js";
import { ElMessageBox, ElMessage } from "element-plus";
const store = useStore();
let content = ref("");
let tabList = ref({});
let baseInfoRef = ref();
function detail() {
  getDetail({ id: store.state.eventDetail.activeTab }).then((res) => {
    tabList.value = res.data.eventSuspicious;
    content.value = res.data.eventSuspicious.content;
  });
}
detail();
function saveBaseInfo(a, b) {
  let add = [];
  baseInfoRef.value.formData.tagJson.forEach((item) => {
    add.push(item.id);
  });
  let ass = {
    id: store.state.eventDetail.activeTab,
    description: baseInfoRef.value.formData.description,
    ioc: baseInfoRef.value.formData.ioc,
    tagJson: add.toString(),
  };
  b();
  saveEventSuspicious(ass)
    .then((res) => {
      detail();
      ElMessage.success("保存成功");
      a();
    })
    .catch(() => {
      a(false);
    });
}
</script>

<style lang="scss" scoped>
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    // margin-left: 20px;
  }
}
.tag {
  margin-right: 10px;
}
</style>
