<template>
  <xelDialog title="关联要素" ref="dialogRef" @submit="submitForm" @close="$emit('close')">
    <el-form :model="dynamicForm" ref="ruleFormRef" label-width="120px" size="mini" class="form-wrapper">
      <div v-for="(item, index) in dynamicForm.formList" :key="index">
        <el-row :gutter="20">
          <el-col :span="20">
            <xel-form-item
              :key="index"
              v-model="item.value"
              v-bind="item"
              :prop="'formList.' + index + '.value'"
              @click="clickFn(index)"
            ></xel-form-item>
          </el-col>
          <el-col :span="4">
            <el-button v-if="index == 0" icon="el-icon-plus" @click.prevent="addDomain(domain)" title="添加"></el-button>
            <el-button icon="el-icon-delete" v-else @click.prevent="removeDomain(index)" title="删除"></el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </xelDialog>
</template>
<script setup>
import { getRelBasicAssetById, selectAssetsTerminalById } from "@/api/event/eventList";
import { ref, reactive, onMounted, toRefs } from "vue";
let emit = defineEmits(["close"]);
let singelAllData = reactive({});
let partData = reactive({});
let dialogRef = ref();
let options = reactive([]);
let props = defineProps({
  //id
  reasonInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
onMounted(() => {
  resetFormData();
  getAssetData();
  dialogRef.value.open();
});
function getAssetData() {
  let portItem = props.reasonInfo.value.type == "basic" ? getRelBasicAssetById : selectAssetsTerminalById;
  portItem({ id: props.reasonInfo.value.id })
    .then((res) => {
      partData = {
        ...res.data.assets,
        type: props.reasonInfo.value.type,
        assetType: props.reasonInfo.value.type == "basic" ? "基础资源资产" : "终端资产",
        assetTypeCode: props.reasonInfo.value.type == "basic" ? 1 : 2,
        domain: "-",
      };
      if (props.reasonInfo.value.type == "basic") {
        const resultArray = new Set([...res.data.chajian, ...res.data.ips, ...res.data.soft]);
        options = Array.from(resultArray).map((rv) => {
          return {
            label: rv,
            value: rv,
          };
        });
        dynamicForm.value.formList[0].options = options;
      } else {
        const resultArray = new Set([...res.data.ips]);
        options = Array.from(resultArray).map((rv) => {
          return {
            label: rv,
            value: rv,
          };
        });
        dynamicForm.value.formList[0].options = options;
      }
    })
    .catch(() => {});
}

let ruleFormRef = ref();
let formData = reactive({});
// 弹框内容
let state = reactive({
  dynamicForm: {
    formList: [
      {
        formType: "select",
        label: "关联要素",
        required: true,
        options: options,
        // onSelectClick() {
        //   console.log(111);
        // },
        // onChange: (val) => {
        //   let index = options.findIndex((item) => item.value == val);
        //   // options.splice(index, 1);
        //   // options = options.splice(0, 3);
        //   state.dynamicForm.formList.forEach((item) => item.options.shift());
        // },
      },
    ],
  },
});
let { dynamicForm } = toRefs(state);
function addDomain() {
  state.dynamicForm.formList.push({
    formType: "select",
    label: "关联要素",
    required: true,
    options: options,
    // onSelectClick() {
    //   console.log(111);
    // },
    value: "",
    onChange: (val) => {
      console.log(val, 333);
      // let index = options.findIndex((item) => item.value == val);
      // options.splice(index, 1);
    },
  });
}
function removeDomain(index) {
  state.dynamicForm.formList.splice(index, 1);
}
function submitForm(close, loading) {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let arr = "";
      arr = state.dynamicForm.formList.map((item) => item["value"]).join("#_ssp_#");
      singelAllData = {
        ...partData,
        reason: arr,
      };
      emit("close", "submit", singelAllData);
    } else {
      return false;
    }
  });
}

//父组件可以调用的方法
defineExpose({
  data: singelAllData,
});
function resetFormData() {
  state.dynamicForm = {
    formList: [
      {
        formType: "select",
        label: "关联要素",
        maxlength: "250",
        required: true,
        value: "",
        options: options,
        onChange: (val) => {},
      },
    ],
  };
}

function clickFn(index) {
  console.log(222);
  let valueList = state.dynamicForm.formList.map((item, _index) => index != _index && item["value"]);
  state.dynamicForm.formList[index].options = options.filter((item) => !valueList.includes(item.value));
}
</script>

<style lang="scss" scoped></style>
