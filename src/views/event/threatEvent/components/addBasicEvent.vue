<template>
  <div ref="basicRef" class="templateInfo" v-if="templateId == '未选'">
    <statistics
      v-if="addType !== 'WorkbenchAlert'"
      :border="true"
      :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: '事件模板' }]"
    ></statistics>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="
        $route.name === 'AlterationEvent' ? selectAlterationTemplates : $route.name != 'UpgradesEvent' ? getTableData : selectChangeTemplates
      "
      :default-params="$route.name == 'UpgradesEvent' ? { eventId: $route.params.id } : { spare1: 'merge' }"
      :page-size="5"
      :pageSizes="[5, 10, 20, 50, 100]"
    >
      <template #radio="scope">
        <el-radio v-model="templateId" :label="scope.row.id" @change="getBasicData(templateId)"> <span></span></el-radio>
      </template>

      <template #level="scope">
        <el-tag v-if="scope.row.levelId != 0" :type="levelData[scope.row.levelId]">{{ scope.row.levelName }}</el-tag>
      </template>
    </xel-table>
  </div>
  <div class="baseInfo" v-if="templateId != '未选'">
    <div class="title">
      <p>事件基础信息</p>
      <el-button @click="returnList" class="search-button">
        <el-icon :size="12">
          <plus />
        </el-icon>
        选择模板
      </el-button>
    </div>
    <el-form :model="basicData" ref="ruleFormRef" label-width="120px" label-position="left" size="mini" class="form-wrapper" :rules="rules">
      <el-row :gutter="addType !== 'WorkbenchAlert' ? 120 : 40">
        <el-col :span="12">
          <el-form-item label="事件标题:" prop="title">
            <div class="tit-wrapper">
              <el-tooltip class="box-item" v-if="basicData.titlePrefix" effect="dark" :content="basicData.titlePrefix" placement="top-start">
                <el-input v-model="basicData.titlePrefix" disabled> </el-input>
              </el-tooltip>
              <el-input v-model="basicData.titlePrefix" disabled v-else> </el-input>
              <el-input v-model="basicData.title" placeholder="请输入事件标题"> </el-input>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <xel-form-item label="标签" form-type="tag" :addSuperTag="addSuperTag" v-model="basicData.tagList"></xel-form-item>
        </el-col>
      </el-row>
      <xel-form-item
        v-for="(item, index) in formList"
        :key="index"
        :class="addType !== 'WorkbenchAlert' ? '' : 'workbenchClass'"
        v-model="basicData[item.prop]"
        v-bind="item"
      ></xel-form-item>
      <el-row :gutter="addType !== 'WorkbenchAlert' ? 120 : 40">
        <el-col :span="12" v-if="showDept">
          <xel-form-item label="部门" v-model="basicData.deptId" v-bind="formList1[0]"></xel-form-item>
        </el-col>
        <el-col :span="12">
          <xel-form-item label="推断时间" v-model="basicData.thatTime" v-bind="formList1[1]"></xel-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup>
import { getEventTemplate as getTableData } from "@/api/event/eventList";
import { selectChangeTemplates, selectAlterationTemplates } from "@/api/event/detail";
import { getCanSelectPersonByDept } from "@/api/securityAssets/business";
import { Level_Data } from "@/config/constant"; /*  */
import { ref, reactive, onMounted, toRefs, watch, computed } from "vue";

let emit = defineEmits(["parenFn", "returnTempList"]);
let props = defineProps({
  interface: {
    type: Function,
  },
  paramKey: {
    type: String,
    default: "id",
  },
  resKey: {
    type: String,
    default: "",
  },
  //事件升级使用，回显模板和事件信息
  info: {
    type: [null, Object],
    default: null,
  },
  changeTempInfo: {
    type: Boolean,
    default: false,
  },
  templateInfo: {
    type: [Object, null],
    default: null,
  },
  showDept: {
    type: Boolean,
    default: true,
  },
  //添加通用超级模板
  addSuperTag: {
    type: Boolean,
    default: false,
  },
  // 工作台提报事件判断
  addType: {
    type: String,
    default: "",
  },
});

watch(
  () => props.changeTempInfo,
  (val) => {
    if (val) {
      echoBaiscInfo(props.templateInfo);
    }
  }
);

let ruleFormRef = ref();
let basicRef = ref();
let tableRef = ref();
let searchState = reactive({
  data: {
    name: "",
    description: "",
  },
  menuData: [
    {
      lable: "事件级别",
      prop: "levelId",
      options: [],
      dictName: "event_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "name",
      label: "模板名称",
    },
    {
      formType: "input",
      prop: "description",
      label: "描述",
    },
  ],
});
// 列表配置项
const columns = [
  {
    prop: "id",
    label: "",
    slotName: "radio",
    width: "80px",
  },
  {
    prop: "name",
    label: "模板名称",
  },
  {
    prop: "titlePrefix",
    label: "标题前缀",
  },
  {
    prop: "levelId",
    label: "事件级别",
    slotName: "level",
  },

  {
    prop: "description",
    label: "描述",
    html: true,
  },
];
let state = reactive({
  templateId: "未选",
  levelData: Level_Data,
  basicData: {
    templateId: "",
    titlePrefix: "",
    title: "",
    tagList: [],
    levelId: null,
    detail: "",
    thatTime: [],
    endTimeStr: "",
    beginTimeStr: "",
    assetsInfos: "",
    suspiciousInfos: "",
    deptId: "",
  },
});

let rules = reactive({
  title: [
    {
      required: true,
      message: "请输入事件标题",
      trigger: "blur",
    },
  ],
});
// 事件基础信息
let formList = [
  {
    formType: "level",
    prop: "levelId",
    label: "事件级别",
    required: true,
    options: [], //字典自定义
    // 字典关键字
    dictName: "event_level",
  },
  {
    formType: "editor",
    prop: "detail",
    label: "描述",
    editorClass: "eventEditor", //多个编辑器时，命名不同的名字
    isClear: false,
    editorData: "",
    onEditorValue(val) {
      state.basicData.detail = val;
    },
  },
];
let formList1 = reactive([
  {
    isShow: props.showDept,
    formType: "deptTree",
    prop: "deptId",
    label: "部门",
    multiple: true,
    onChange(val) {
      getCanSelectPersonByDeptFn(val);
    },
  },
  {
    formType: "daterange",
    type: "datetimerange",
    prop: "thatTime",
    label: "推断发生时间",
    required: true,
    onChange: (val) => {
      state.basicData.thatTime = val;
      state.basicData.endTimeStr = val[1];
      state.basicData.beginTimeStr = val[0];
    },
  },
]);
let { basicData, templateId, levelData } = toRefs(state);
// / 重置
function reset() {
  searchState.data = {
    name: "",
    description: "",
  };
  search();
}
// 基础信息
function getBasicData(templateId) {
  // 基础信息
  if (templateId) {
    props
      .interface({ [props.paramKey]: templateId })
      .then((res) => {
        let data = res.data;
        if (props.resKey) {
          data = data[props.resKey];
        }
        echoBaiscInfo(data);
        emit("parentFn", templateId, data);
      })
      .catch(() => {});
  } else {
    emit("parentFn", templateId);
  }
  state.basicData = {
    templateId: templateId ? templateId : "white",
    titlePrefix: "",
    title: "",
    tagList: [],
    levelId: null,
    detail: "",
    thatTime: [],
    endTimeStr: "",
    beginTimeStr: "",
    assetsInfos: "",
    suspiciousInfos: "",
    deptId: "",
  };
}

//回显基础信息
function echoBaiscInfo(data) {
  state.basicData = { ...data, templateId: data.id };
  let info = {};
  if (data) {
    info = { ...data, ...props.info };
    if (data.description) {
      info.detail = data.description;
    }
  }
  if (data) {
    state.basicData.title = info.title;
    state.basicData.detail = info.detail || info.description;

    formList[1].editorData = info.detail;
    state.basicData.beginTimeStr = info.beaginTime;
    state.basicData.endTimeStr = info.endTime;
    state.basicData.thatTime = [state.basicData.beginTimeStr, state.basicData.endTimeStr];
  }

  state.basicData.tagList = data.tagList.map((tag) => {
    return {
      id: tag.tagId,
      name: tag.tagName,
    };
  });
  state.basicData.deptId = data.deptId ? data.deptId : "";
}

//获取责任人
function getCanSelectPersonByDeptFn(info) {
  getCanSelectPersonByDept({ deptId: info.id }).then(({ data }) => {
    let selectPersonItem = formList.find((item) => item.prop == "assetsPerson");
    if (selectPersonItem) {
      selectPersonItem.options = data.userList;
    }
  });
}
function search(initPageNum) {
  tableRef.value.reload(searchState.data, initPageNum);
}
async function getBasicInfo() {
  let arr = null;
  await ruleFormRef.value.validate((valid) => {
    if (valid) {
      let tagIds = state.basicData.tagList.map((x) => {
        return x.id;
      });
      state.basicData.tagId = tagIds.toString();
      arr = state.basicData;
      if (arr.deptId) {
        arr.deptId = arr.deptId.join();
      }
    }
  });

  if (arr) {
    return Promise.resolve(arr);
  } else {
    return Promise.reject(arr);
  }
}

function returnList(emits = true) {
  templateId.value = "未选";
  state.basicData.templateId = "";
  state.basicData.levelId = null;
  if (emits) {
    emit("returnTempList");
  }
}

defineExpose({
  data: state.basicData,
  templateId: state.templateId,
  getBasicInfo: getBasicInfo,
  returnList,
  selectedtTemplateId: computed(() => {
    return state.basicData.templateId;
  }),
});
</script>

<style lang="scss" scoped>
.baseInfo {
  margin: 40px 0;
  width: 100%;
  border-top: 1px dashed #ededed;
  .form-wrapper {
    margin-top: 24px;
  }
  .title {
    padding: 26px 0px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #ededed;
    margin-bottom: 22px;
    p {
      line-height: 32px;
    }
  }
  .tit-wrapper {
    display: flex;
    width: 100%;
    .el-input:first-child {
      width: 40%;
      margin-right: 10px;
    }
  }
}
.workbenchClass {
  padding-left: 20px;
}
:deep(.el-tag) {
  margin-left: 10px;
}
</style>
