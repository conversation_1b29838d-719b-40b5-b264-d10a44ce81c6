<template>
  <xelDialog title="关联要素" ref="dialogRef" @submit="submitForm" @close="$emit('close')">
    <el-form :model="dynamicForm" ref="ruleFormRef" label-width="120px" size="mini" class="form-wrapper" action="javascript:;">
      <div v-for="(item, index) in dynamicForm.formList" :key="index">
        <el-row :gutter="20">
          <el-col :span="20">
            <xel-form-item
              v-if="reasonInfo.value.type != 'ip'"
              :key="index"
              v-model="item.value"
              v-bind="item"
              :prop="'formList.' + index + '.value'"
              @blur="clickFn(item.value, index)"
            ></xel-form-item>

            <el-form-item v-else label="关联要素">
              <el-input clearable v-model="ipReason" :disabled="true" :placeholder="'请输入关联要素'"></el-input
            ></el-form-item>
          </el-col>
          <el-col :span="4" v-if="reasonInfo.value.type != 'ip'">
            <el-button v-if="index == 0" icon="el-icon-plus" @click.prevent="addDomain(domain)" title="添加"></el-button>
            <el-button icon="el-icon-delete" v-else @click.prevent="removeDomain(index)" title="删除"></el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </xelDialog>
</template>
<script setup>
import { getRelBusinessAssetById, selectDomainNotConfirmById, selectIpNotConfirmById } from "@/api/event/eventList";
import { ref, reactive, onMounted, toRefs } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
let emit = defineEmits(["close"]);
let singelAllData = reactive({});
let partData = reactive({});
let dialogRef = ref();
let props = defineProps({
  //id
  reasonInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
  // 是否详情页面
  isDetailEdit: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
});
onMounted(() => {
  getAssetData();
  dialogRef.value.open();
  resetFormData();
  ipReason.value = props.reasonInfo.value.ip;
});
let ipReason = ref("");
// 新增事件，关联要素获取相关数据
function getAssetData() {
  if (props.isDetailEdit && props.reasonInfo.value.type == "bussiness") {
    return;
  }
  let portItem =
    props.reasonInfo.value.type == "bussiness"
      ? getRelBusinessAssetById
      : props.reasonInfo.value.type == "ip"
      ? selectIpNotConfirmById
      : selectDomainNotConfirmById;
  portItem({ id: props.reasonInfo.value.id })
    .then((res) => {
      console.log("res: ", res);
      partData = {
        ...res.data.assets,
        type: props.reasonInfo.value.type,
        assetType: props.reasonInfo.value.type == "bussiness" ? "业务系统资产" : "待确认资产",
        assetTypeCode: props.reasonInfo.value.type == "bussiness" ? 0 : props.reasonInfo.value.type == "ip" ? 4 : 3,
        assetsGroupName: res.data.assets.assetsGroupName,
        ips:
          props.reasonInfo.value.type == "bussiness"
            ? res.data.assets.ips
            : props.reasonInfo.value.type == "ip"
            ? res.data.assets.ip
            : res.data.assets.internetIp,
      };
    })
    .catch(() => {});
}

let ruleFormRef = ref();
let formData = reactive({});
// 弹框内容
let state = reactive({
  dynamicForm: {
    formList: [
      {
        formType: "input",
        label: "关联要素",
        required: true,
        value: "",
        maxlength: "500",
      },
    ],
  },
});
let { dynamicForm } = toRefs(state);
function addDomain() {
  state.dynamicForm.formList.push({
    formType: "input",
    label: "关联要素",
    required: true,
    value: "",
    maxlength: "500",
  });
}
function removeDomain(index) {
  state.dynamicForm.formList.splice(index, 1);
}

function submitForm(close, loading) {
  // if (props.reasonInfo.value.type != "ip") {
  //   let valueList = state.dynamicForm.formList.map((item, _index) => item["value"]);
  //   if (new Set(valueList).size !== valueList.length) {
  //     ElMessage({
  //       type: "warning",
  //       message: "关联要素存在重复",
  //     });
  //     return;
  //   }
  // }

  ruleFormRef.value.validate((valid) => {
    if (valid) {
      loading();
      let arr = "";
      if (props.reasonInfo.value.type == "ip") {
        arr = props.reasonInfo.value.ip;
      } else {
        arr = state.dynamicForm.formList.map((item) => item["value"]).join("#_ssp_#");
      }

      singelAllData = {
        ...partData,
        reason: arr,
      };

      emit("close", "submit", singelAllData);

      // close();
    } else {
      return false;
    }
  });
}

//父组件可以调用的方法
defineExpose({
  data: singelAllData,
});
function resetFormData() {
  state.dynamicForm = {
    formList: [
      {
        formType: "input",
        label: "关联要素",
        required: true,
        value: "",
        maxlength: "500",
      },
    ],
  };
}
function clickFn(val, curIndex) {
  let valueList = state.dynamicForm.formList.map((item, _index) => item["value"]);
  var result = valueList.some((item, index) => {
    return index !== curIndex && item === valueList[curIndex];
  });
  if (result) {
    ElMessage({
      type: "warning",
      message: "关联要素存在重复,请重新填写",
    });
    state.dynamicForm.formList[curIndex].value = "";
  }
}
</script>

<style lang="scss" scoped></style>
