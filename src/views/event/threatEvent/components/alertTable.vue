<template>
  <div>
    <common-search v-model="alarmSearch.data" :menu-data="alarmSearch.menuData" :form-list="alarmSearch.formList" @search="onsearch" @reset="onreset">
    </common-search>

    <xel-table
      ref="onRef"
      :columns="oncolumns"
      :load-data="selectEventAlertPage"
      :default-params="{
        eventId: route.params.id,
      }"
      @selection-change="handleSelectionChange"
    >
      <template #level="scope">
        <el-tag :type="levelData[scope.row.priority]">{{ scope.row.priorityStr }}</el-tag>
      </template>
    </xel-table>

    <!-- 告警查看详情 -->
    <xel-dialog title="告警信息" ref="detailsRef" width="1200px" @submit="submitForm">
      <div class="AlertSearchBtn">
        <AlertSearchBtn :detailData="alarmDetails" />
      </div>
      <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
        <el-row :gutter="70">
          <el-col :span="24">
            <el-form-item label="告警名称">{{ alarmDetails.title }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <!-- <el-form-item label="告警等级">{{ alarmDetails.priorityStr }}</el-form-item> -->
            <el-form-item label="告警级别：">
              <el-tag :type="levelData[alarmDetails.priority]">{{ alarmDetails.priorityStr }}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="告警发生设备">{{ alarmDetails.devName }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备地址">{{ alarmDetails.devIp }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备类型">{{ alarmDetails.devType }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="告警内容描述">{{ alarmDetails.description }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="首轮告警发生时间" v-if="alarmDetails.stage == '1' || !alarmDetails.stage">{{
              alarmDetails.createTime
            }}</el-form-item>
            <el-form-item label="二轮告警发生时间" v-else-if="alarmDetails.stage == '2'">{{ alarmDetails.createTime2 }}</el-form-item>
            <el-form-item label="三轮告警发生时间" v-else-if="alarmDetails.stage == '3'">{{ alarmDetails.createTime3 }}</el-form-item>
            <el-form-item label="四轮告警发生时间" v-else-if="alarmDetails.stage == '4'">{{ alarmDetails.createTime4 }}</el-form-item>
            <el-form-item v-else :label="alarmDetails?.stageLabel">{{ alarmDetails?.stageTime }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="首条日志源地址">{{ alarmDetails.csrcip }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="首条日志目的地址">{{ alarmDetails.cdstip }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="title-bottom-line ondata">
        <p>辅助分析</p>
      </div>

      <el-form ref="form" label-width="140px" label-position="left" class="base-info-form">
        <el-row :gutter="70">
          <el-col :span="24">
            <el-form-item label="绑定域名">{{ alarmDetails.reverseDomains }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="情报类型">{{ alarmDetails.categories }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="情报置信度">{{ alarmDetails.threatScore }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="位置">{{ alarmDetails.location }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="可疑度">{{ alarmDetails.score }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="推断原因">{{ alarmDetails.reason }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #button>
        <el-button @click="closeDialog" class="search-button"> 关闭 </el-button>
      </template>
    </xel-dialog>
  </div>
</template>

<script>
export default {
  name: "alertTable",
};
</script>

<script setup>
import { reactive, ref, toRefs } from "vue";
import { Level_priority } from "@/config/constant";
import { selectAlarmReportPage, saveAlarm, deleteAlarm, selectEventAlertPage, selectarningAwaitingConfirmatio } from "@/api/event/alarmReport";
import { useRoute } from "vue-router";
import { convertToChinese } from "@/utils/convertToChinese";
import AlertSearchBtn from "@/views/workSpace/components/alertSearchBtn.vue";

const route = useRoute();
let detailsRef = ref();
let alarmDetails = ref({});

let state = reactive({
  formfile: {
    file_upload: "",
    name: "",
    eventId: route.params.id,
  },
  levelData: Level_priority,
});
let { levelData } = toRefs(state);

const oncolumns = [
  {
    prop: "title",
    label: "告警名称",
  },
  {
    prop: "devIp",
    label: "设备地址",
  },
  {
    prop: "priorityStr",
    label: "告警等级",
    slotName: "level",
  },
  {
    prop: "description",
    label: "告警描述",
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "right",
        title: "查看详情",
        onClick(scope) {
          wnload(scope.row.id);
        },
      },
    ],
  },
];

// 告警查看详情
function wnload(id) {
  selectarningAwaitingConfirmatio({ id: id }).then((res) => {
    alarmDetails.value = res.data.record;
    if (Number(res.data.record.stage) > 4 && res.data.record.space1) {
      const space1Array = res.data.record.space1.split(";");
      const stageTime = convertToChinese(res.data.record.stage);
      alarmDetails.value["stageTime"] = space1Array[space1Array.length - 1];
      alarmDetails.value["stageLabel"] = stageTime + "轮告警发生时间";
    }
  });
  detailsRef.value.open();
}

//搜索相关
let alarmSearch = reactive({
  data: {
    title: "",
  },
  menuData: [],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "告警名称",
    },
  ],
});

let onRef = ref();
function onsearch() {
  onRef.value.reload(alarmSearch.data, true);
}
function onreset() {
  alarmSearch.data = {
    title: "",
  };
  onsearch();
}

// 关闭
function closeDialog() {
  detailsRef.value.close();
}
</script>
<style scoped>
.AlertSearchBtn {
  text-align: right;
}
</style>
