<template>
  <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    <el-button v-if="eventSuspiciousAdd" @click="newlyAdded" class="search-button">
      <el-icon :size="12">
        <plus />
      </el-icon>
      新增
    </el-button>

    <el-button v-if="eventSuspiciousDel" @click="delFn(state.multipleSelection, 1)" :disabled="multiples" class="search-button">
      <el-icon :size="12">
        <delete />
      </el-icon>
      批量删除
    </el-button>
    <el-button v-if="eventSuspiciousUpdate" @click="delFn(state.multipleSelection, 2)" :disabled="multiples" class="search-button">
      <el-icon :size="12">
        <open />
      </el-icon>
      批量打开
    </el-button>
    <el-button v-if="eventSuspiciousUpdate" @click="delFn(state.multipleSelection, 3)" :disabled="multiples" class="search-button">
      <el-icon :size="12">
        <close />
      </el-icon>
      批量关闭
    </el-button>
  </common-search>
  <xel-table
    ref="tableRef"
    :columns="columns"
    :load-data="getSuspiciousList"
    @selection-change="handleSelectionChange"
    :default-params="{
      eventId: route.params.id,
    }"
    :checkbox="true"
    row-key="id"
  >
    <template #status="scope">
      <el-switch
        v-if="eventSuspiciousUpdate"
        v-model="scope.row.isRelated"
        active-value="1"
        inactive-value="0"
        @change="changeTimes(scope.row)"
      ></el-switch>
    </template>
    <template #level="scope">
      <el-tag v-for="item in scope.row.tagRelList" :key="item.tagId" class="tagid">{{ item.tagName }}</el-tag>
    </template>
  </xel-table>
  <!-- 弹框 -->
  <xelDialog title="新增可疑对象" ref="dialogRef" @submit="submitForm" @close="emit">
    <el-form :model="state.objFormData" ref="ruleFormRef" label-width="120px" size="mini" class="form-wrapper">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="state.objFormData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xelDialog>
</template>
<script setup>
import { ref, reactive, toRefs, computed } from "vue";
import {
  getSuspiciousList,
  saveSuspiciousForm,
  deleteEventSuspicious,
  closeSuspicious,
  openSuspicious,
  saveEventSuspicious,
} from "@/api/event/suspicious.js";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { useStore } from "vuex";
const store = useStore();
const route = useRoute();

import updateByTabName from "../mixins/updateByTabName";
updateByTabName(5, search);

//可疑对象权限
let eventSuspiciousAdd = computed(() => {
  return store.state.eventDetail.eventDetail.eventSuspiciousAdd == "Y";
});
let eventSuspiciousUpdate = computed(() => {
  return store.state.eventDetail.eventDetail.eventSuspiciousUpdate == "Y";
});
let eventSuspiciousDel = computed(() => {
  return store.state.eventDetail.eventDetail.eventSuspiciousDel == "Y";
});

let tableRef = ref();
let dialogRef = ref();
let ruleFormRef = ref();
let state = reactive({
  objFormData: {
    type: "",
    tagList: [],
    tagJson: "",
    content: "",
    description: "",
    eventId: route.params.id,
  },
  multipleSelection: [],
});
// 列表配置项
const columns = [
  {
    prop: "status",
    label: "",
    slotName: "status",
    width: "100px",
  },
  {
    prop: "typeStr",
    label: "类型",
  },
  {
    prop: "level",
    label: "标签",
    slotName: "level",
  },
  {
    prop: "content",
    label: "值/文件名",
    click(scope) {
      goDetail(scope.row);
    },
  },
  {
    prop: "createTime",
    label: "创建时间",
  },
];
//搜索相关
let searchState = reactive({
  data: {
    type: "",
    description: "",
    content: "",
    searchTag: "",
  },
  menuData: [
    {
      lable: "类型：",
      prop: "type",
      options: [],
      dictName: "suspicious_type",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "description",
      label: "描述",
    },
    {
      formType: "input",
      prop: "content",
      label: "指标",
    },
    {
      formType: "input",
      prop: "searchTag",
      label: "标签",
    },
  ],
});
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    type: "",
    description: "",
    content: "",
    searchTag: "",
  };
  search();
}
//
// 弹框内容
let formList = reactive([
  {
    formType: "select",
    prop: "type",
    label: "可疑对象类型",
    required: true,
    options: [],
    dictName: "suspicious_type",
  },
  {
    formType: "input",
    type: "textarea",
    prop: "content",
    label: "可疑对象指标",
    required: true,
  },
  {
    formType: "tag",
    prop: "tagList",
    label: "标签",
  },
  {
    formType: "input",
    type: "textarea",
    prop: "description",
    label: "描述",
    required: true,
  },
]);

// 新增弹框按钮
function newlyAdded() {
  dialogRef.value.open();
}
// 资产列表确定按钮
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let tagIds = state.objFormData.tagList.map((x) => {
        return x.id;
      });
      let params = {
        ...state.objFormData,
        tagJson: tagIds.toString(),
      };
      saveSuspiciousForm(params).then((res) => {
        emit();
        dialogRef.value.close();
        tableRef.value.reload(searchState.data, true);
        setTimeout(() => {
          store.commit("changeTabNumsByName", {
            name: 5,
            num: tableRef.value.resData.total,
          });
        }, 500);
        ElMessage.success("操作成功");
      });
    }
  });
}
// 弹框重置
function emit() {
  state.objFormData = {
    type: "",
    tagList: [],
    tagJson: "",
    content: "",
    description: "",
    eventId: route.params.id,
  };
}
// 列表多选
let multiples = ref(true);

function handleSelectionChange(val) {
  state.multipleSelection = val;
  multiples.value = !val.length;
}
// 批量删除
function delFn(rows, val) {
  let ids = [];
  rows.forEach((item) => {
    ids.push(item.id);
  });
  if (val == 1) {
    ElMessageBox.confirm("确认要删除吗？", "警告", {
      distinguishCancelAndClose: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      deleteEventSuspicious({ id: ids.join() }).then((res) => {
        tableRef.value.reload(searchState.data, true);
        setTimeout(() => {
          store.commit("changeTabNumsByName", {
            name: 5,
            num: tableRef.value.resData.total,
          });
        }, 500);
        tableRef.value.table.clearSelection();
        ElMessage.success("操作成功");
      });
    });
  } else if (val == 2) {
    openSuspicious({ id: ids.join() }).then((res) => {
      tableRef.value.reload(searchState.data, true);
      setTimeout(() => {
        store.commit("changeTabNumsByName", {
          name: 5,
          num: tableRef.value.resData.total,
        });
      }, 500);
      ElMessage.success("操作成功");
    });
  } else {
    closeSuspicious({ id: ids.join() }).then((res) => {
      tableRef.value.reload(searchState.data, true);
      setTimeout(() => {
        store.commit("changeTabNumsByName", {
          name: 5,
          num: tableRef.value.resData.total,
        });
      }, 500);
      ElMessage.success("操作成功");
    });
  }
}
// 批量打开
function changeTimes(lis) {
  saveEventSuspicious({ id: lis.id, isRelated: lis.isRelated }).then((res) => {
    tableRef.value.reload(searchState.data, true);
    ElMessage.success("操作成功");
  });
}
// 对象详情
function goDetail(val) {
  store.commit("pushEventTab", {
    name: val.id,
    label: val.content,
    type: "suspiciousDetail",
  });
}
</script>

<style lang="scss" scoped>
.tagid {
  margin: 5px;
  white-space: normal;
  height: auto;
  // max-width: 100px;
  // word-wrap: break-word;
}
</style>
