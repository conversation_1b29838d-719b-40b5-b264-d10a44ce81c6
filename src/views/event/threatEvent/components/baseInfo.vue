<template>
  <div class="margin-top30" :class="{ ishidden: eventBtns.isReject }">
    <event-name>
      <el-button size="mini" v-if="eventBtns.closeFlag" v-hasPermi="'event:close'" @click="handleEvent('close')">
        <el-icon><close-bold /></el-icon>
        关闭</el-button
      >
      <!-- 提审 -->
      <el-button size="mini" v-if="eventAudit.audit" v-hasPermi="'eventAudit:commit'" @click="handleEvent('audit')">
        <el-icon><circle-check /></el-icon>
        提审
      </el-button>
      <el-button size="mini" v-if="eventAudit.findings" @click="handleEvent('findings')">
        <el-icon><Tickets /></el-icon>
        提审记录</el-button
      >
      <el-button size="mini" v-if="eventAudit.submitFindings" v-hasPermi="'eventAudit:audit'" @click="handleEvent('submitFindings')">
        <el-icon><circle-check /></el-icon>
        提审审核
      </el-button>
      <!-- 提审审核 -->
      <el-button size="mini" v-if="eventBtns.rejectFlag" v-hasPermi="'event:reject'" @click="handleEvent('reject')">
        <icon n="icon-fanhui"></icon>
        驳回</el-button
      >
      <el-button size="mini" v-if="eventBtns.eventDelete" v-hasPermi="'alarmReport:delete'" @click="handleEvent('delete')">
        <el-icon><delete /></el-icon>
        删除</el-button
      >
      <el-button size="mini" v-if="eventTaskButs.auditResultStatus == 4 && eventBtns.flg" v-hasPermi="'event:audit'" @click="handleEvent('verify')">
        <el-icon><circle-check /></el-icon>
        审核
      </el-button>
      <el-button size="mini" v-if="eventBtns.mergeFlg" v-hasPermi="'event:merge'" @click="handleEvent('merge')">
        <el-icon><connection /></el-icon>
        事件合并
      </el-button>
      <el-button
        size="mini"
        v-if="eventBtns.changeFlg && eventTaskButs.auditResultStatus == 4"
        v-hasPermi="'event:change'"
        @click="handleEvent('change')"
      >
        <el-icon><refresh /></el-icon>
        事件升级
      </el-button>

      <!-- 新增 - 事件变更 -->
      <el-button
        size="mini"
        v-if="eventBtns.changeFlg && eventTaskButs.auditResultStatus == 4"
        v-hasPermi="'event:change'"
        @click="handleEvent('alteration')"
      >
        <el-icon><refresh /></el-icon>
        事件变更
      </el-button>
    </event-name>
  </div>
  <!-- 驳回弹框 -->
  <xel-dialog :title="titleName" ref="dialogRef" @submit="submitForm" @close="closeDialog">
    <el-form :model="formData" ref="ruleFormRef" label-width="90px" size="mini">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
  </xel-dialog>
  <!--审核事件  -->
  <xel-dialog title="审核事件" ref="examineRef" size="small" @submit="examineForm" @close="examineDialog">
    <el-form :model="state.forMexamine" ref="rulexamine" label-width="120px" size="mini">
      <xel-form-item
        v-for="(item, index) in mexaminemList"
        :key="index"
        v-model="state.forMexamine[item.prop]"
        v-bind="item"
        @input="(val) => changeReward(val, item.prop)"
      ></xel-form-item>
    </el-form>
  </xel-dialog>
  <!-- 提审记录 提审审核   -->
  <xel-dialog :title="auditTitleDialog" ref="findingsRef" :width="auditSize" :showSubmit="showSubmit" @submit="submitAuditForm">
    <el-form v-if="showSubmit" :model="formFindingsData" ref="findingsFormRef" label-width="140px" size="mini">
      <xel-form-item v-for="(item, index) in findingsFormItems" :key="index" v-model="formFindingsData[item.prop]" v-bind="item"></xel-form-item>
    </el-form>
    <xel-table v-else ref="tableRef" :data="eventFindings" :columns="columnsFindings" :pagination="false">
      <template #commentSlot="{ row }">
        {{ row.comment }}
      </template>
    </xel-table>
  </xel-dialog>
</template>
<script setup>
import { submitAudit, postAudit } from "@/api/event/eventList";
import { ref, reactive, toRefs, onMounted, computed, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { postRejectEvent, postCloseEvent, saveEventAudit, deleteEvent } from "@/api/event/detail";
import eventName from "./eventName.vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";

const store = useStore();
let dialogRef = ref();
let ruleFormRef = ref();
let examineRef = ref();
let rulexamine = ref();
let titleName = ref("驳回事件");
const findingsRef = ref(); //审核结果s
let eventBtns = computed(() => {
  return store.state.eventDetail.eventBtns;
});
let eventAudit = computed(() => {
  return store.state.eventDetail.eventAuditBtns;
});
let eventFindings = computed(() => {
  return store.state.eventDetail.eventFindings;
});
let eventTaskButs = computed(() => {
  return store.state.eventDetail.eventTaskButs;
});
store.dispatch("updateEventFlag");

function handleEvent(type) {
  switch (type) {
    case "close":
      closeEvent();
      break;
    case "reject":
      rejectEvent();
      break;
    case "verify":
      verifyEvent();
      break;
    case "change":
      toChange(type);
      break;
    case "merge":
      toMerge();
      break;
    case "delete":
      tobreak();
      break;
    case "alteration":
      toChange(type);
      break;
    case "audit":
      getAudit(type);
      break;
    case "findings":
      getFindings(type);
      break;
    case "submitFindings":
      getSubmitFindings(type);
      break;
  }
}
//审核记录 表格
const columnsFindings = [
  {
    prop: "submitUserName",
    label: "提报人",
  },
  {
    prop: "submitTime",
    label: "提报时间",
  },
  {
    prop: "auditResultNum",
    label: "提报次数",
  },
  {
    prop: "assignTime",
    label: "分派时间",
  },
  {
    prop: "auditUserName",
    label: "审核人",
  },
  {
    prop: "auditTime",
    label: "审核时间",
  },
  {
    prop: "auditResultStatusStr",
    label: "审核状态",
  },
  {
    prop: "comment",
    label: "审核意见",
    slotName: "commentSlot",
  },
];
//提报审核
function getAudit() {
  submitAudit(route.params.id).then((res) => {
    ElMessage.success("提审成功");
    _update(route.params.id, store);
  });
}
//提审记录
const auditTitleDialog = ref("提审记录");
const showSubmit = ref(false);
const findingsFormRef = ref(null);
const auditSize = ref("557px");
function getFormFindingsData() {
  return {
    auditResultStatus: undefined,
    comment: "",
  };
}
const formFindingsData = ref(getFormFindingsData());
function getFindings() {
  auditTitleDialog.value = "提审记录";
  showSubmit.value = false;
  auditSize.value = "1257px";
  nextTick(() => {
    findingsRef.value.open();
  });
}
//提审审核
function getSubmitFindings() {
  auditTitleDialog.value = "提审审核";
  showSubmit.value = true;
  auditSize.value = "557px";
  formFindingsData.value = getFormFindingsData();
  nextTick(() => {
    findingsRef.value.open();
  });
}
function submitAuditForm() {
  findingsFormRef.value.validate().then(() => {
    const data = {
      ...formFindingsData.value,
      eventId: route.params.id,
      id: route.query.auditResultId,
    };
    postAudit(data).then(() => {
      ElMessage.success("提审成功");
      findingsRef.value.close();
      formFindingsData.value = getFormFindingsData();
    });
  });
}
// 更新状态
function _update(id, store) {
  console.log("更新状态: ");
  store.commit("updateTaskById", id);
  store.dispatch("updateEventFlag");
}

function closeEvent() {
  titleName.value = "关闭事件";
  formList[0].label = "关闭原因";
  /*formList[1].isShow = true;*/
  dialogRef.value.open();
}

function rejectEvent() {
  titleName.value = "驳回事件";
  formList[0].label = "驳回原因";
  dialogRef.value.open();
}

function verifyEvent() {
  examineRef.value.open();
}

function toMerge() {
  router.push({
    name: "MergeEvent",
    params: {
      id: route.params.id,
    },
  });
}

/* 事件变更与升级 - 按钮事件 */
function toChange(type) {
  /*新增 -  原事件升级 */
  if (type === "change") {
    router.push({
      name: "UpgradesEvent",
      params: {
        id: route.params.id,
      },
    });
  } else {
    router.push({
      name: "AlterationEvent",
      params: {
        id: route.params.id,
      },
    });
  }
}
// 删除按钮
function tobreak() {
  ElMessageBox.confirm("确认删除该事件吗？", "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteEvent({ id: route.params.id }).then(() => {
      _update(route.params.id, store);
      store.commit("closeCurrentTab");
      router.push({ name: "ThreatEvent" });
      ElMessage({
        message: "删除成功",
        type: "success",
      });
    });
  });
}
//新打开，关闭页签示例
// setTimeout(() => {
//   store.commit("pushEventTab", {
//     name: 222,
//     label: "新页签",
//     type: "suspiciousDetail",
//   });
// }, 1000);
// setTimeout(() => {
//   store.commit("delTabByName", 111);
// }, 2000);
let state = reactive({
  formData: {
    rejectReason: "",
    eventDesc: "",
  }, //新增编辑表单
  forMexamine: {
    reward: "",
  },
});
let formList = reactive([
  {
    formType: "input",
    size: "mini",
    required: true,
    prop: "rejectReason",
    label: "",
    type: "textarea",
    rows: 6,
    maxLength: "500",
  },

  /*{
    formType: "input",
    size: "mini",
    required: true,
    prop: "eventDesc",
    label: "事件结论",
    type: "textarea",
    rows: 6,
    isShow: false,
    maxLength: "500",
  },*/
]);
let findingsFormItems = reactive([
  {
    formType: "radio",
    required: true,
    prop: "auditResultStatus",
    label: "是否通过",
    options: [
      { label: "通过", value: "4" },
      { label: "不通过", value: "3" },
    ],
  },
  {
    formType: "input",
    required: true,
    prop: "comment",
    label: "填报审核意见",
    type: "textarea",
    rows: 6,
    maxLength: "500",
  },
]);

let mexaminemList = reactive([
  {
    formType: "number",
    size: "mini",
    required: true,
    prop: "reward",
    label: "评分",
    min: "-10000",
    max: "10000",
  },
]);
let { formData } = toRefs(state);
//重置新增编辑表单
function resetFormData() {
  /*formList[1].isShow = false;*/
  state.formData = {
    rejectReason: "",
    eventDesc: "",
  };
  state.forMexamine = {
    reward: "",
  };
}
function closeDialog() {
  resetFormData();
  ruleFormRef.value.resetFields();
}
function examineDialog() {
  resetFormData();
  rulexamine.value.resetFields();
}
// 驳回弹框提交
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      if (titleName.value == "驳回事件") {
        let ass = {
          id: route.params.id,
          rejectReason: state.formData.rejectReason,
        };
        postRejectEvent(ass).then((res) => {
          _update(route.params.id, store);
          store.commit("closeCurrentTab");
          router.push({ name: "ThreatEvent" });
          ElMessage({
            message: "操作成功",
            type: "success",
          });
        });
      } else {
        let ass = {
          id: route.params.id,
          closeReason: state.formData.rejectReason,
          eventDesc: state.formData.eventDesc,
        };
        postCloseEvent(ass).then((res) => {
          dialogRef.value.close();
          _update(route.params.id, store);
        });
      }
    }
  });
}
// 审核事件提交
function examineForm() {
  rulexamine.value.validate((valid) => {
    if (valid) {
      let val = {
        id: route.params.id,
        reward: state.forMexamine.reward,
      };
      saveEventAudit(val).then((res) => {
        _update(route.params.id, store);
        examineRef.value.close();
      });
    }
  });
}

function changeReward(value, id) {
  if (value > 10000 || value < -10000) {
    state.forMexamine[id] = null;
    ElMessage.warning("请输入-10000 到 10000之间的整数");
  }
  if (value) {
    nextTick(() => {
      state.forMexamine[id] = parseInt(value);
    });
  }
}
</script>

<style lang="scss" scoped></style>
