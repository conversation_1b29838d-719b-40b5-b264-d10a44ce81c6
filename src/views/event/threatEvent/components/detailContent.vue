<template>
  <!-- 事件详情 - Tab -->
  <div style="display: flex">
    <div :style="{ width: timeDisplay ? '80%' : '100%' }" class="detail-wrapper">
      <el-card class="margin-bottom20">
        <el-button @click="showTimeline(timeDisplay)" class="pull-right search-button">
          <el-icon :size="12">
            <DArrowRight v-if="timeDisplay" />
            <d-arrow-left v-else
          /></el-icon>
          状态流
        </el-button>
        <el-button @click="backList" class="pull-right search-button" style="margin-right: 10px">
          <icon n="icon-fanhui"></icon>
          返回事件列表
        </el-button>
        <h3 class="conH3Tit margin-bottom20">{{ "" || ($route.meta && $route.meta.title) }}</h3>
        <base-info></base-info>
      </el-card>
      <el-card>
        <!-- tab -->
        <el-tabs v-model="activeTabSelf" @tab-remove="removeTabByName" :class="{ ishidden: isReject }">
          <!-- 固定tabs -->
          <el-tab-pane v-for="(item, index) in staticTabs" :key="index" :name="item.name + ''" :lazy="item.name == 0 ? false : true">
            <template #label>
              {{ item.label }}
              <span v-if="item.name in tabNums" class="tab-num">{{ tabNums[item.name] || 0 }}</span>
            </template>
            <!-- 综述 -->
            <summarize v-if="item.name == 0"></summarize>
            <!-- 事件分析 -->
            <task-list v-if="item.name == 1" :isManageTask="0" :tab-name="activeTabSelf" self-name="1"></task-list>
            <!-- 事件处置 -->
            <task-list v-if="item.name == 2" :isManageTask="1" :tab-name="activeTabSelf" self-name="2"></task-list>
            <!-- 受影响资产 -->
            <assets v-if="item.name == 3"></assets>
            <!-- 相关漏洞 -->
            <vulns v-if="item.name == 4"></vulns>
            <!-- 可疑对象 -->
            <suspicious v-if="item.name == 5"></suspicious>
            <!-- 告警与报告 -->
            <alarm-report v-if="item.name == 6"></alarm-report>
          </el-tab-pane>
          <!-- 动态添加tabs,任务详情的name 等于taskId -->
          <el-tab-pane
            v-for="(item, index) in trendsTabList"
            :key="index.name"
            :name="item.name + ''"
            :lazy="true"
            :label="item.label"
            :closable="true"
          >
            <!-- 任务详情 -->
            <task-detail
              v-if="item.type == 'taskDetail'"
              :isEventTab="true"
              :samplingStatus="{ id: '', taskId: item.name, samplingStatus: 0 }"
              :isManageTask="item.isManageTask"
              :audit="item.audit"
              :reaudit="item.reaudit"
            ></task-detail>
            <!-- 任务日志汇总 -->
            <log-list v-else-if="item.type == 'logList'" :isManageTask="item.isManageTask" :tab-name="activeTabSelf"></log-list>
            <!-- 可疑对象详情 -->
            <suspicious-detail v-else-if="item.type == 'suspiciousDetail'"></suspicious-detail>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
    <!-- 状态流 -->
    <timeline v-if="timeDisplay" :activities="eventLogList" ref="timelineRef"></timeline>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from "vuex";

export default {
  computed: mapState({
    pageStatus: (state) => state.eventDetail.pageStatus,
    tabNums: (state) => state.eventDetail.tabNums,
    eventLogList: (state) => state.eventDetail.eventLogList,
    trendsTabList: (state) => state.eventDetail.trendsTabList,
    isReject: (state) => state.eventDetail.eventBtns.isReject,
  }),
  data() {
    return {
      // activeTabSelf: this.activeTab,
    };
  },
  methods: {
    ...mapMutations(["delEventTab"]),
    ...mapActions(["updateEventLogList"]),
    removeTabByName(name) {
      let index = this.trendsTabList.findIndex((item) => item.name == name);
      this.delEventTab(index);
    },
  },
  created() {
    this.updateEventLogList();
  },
};
</script>
<script setup>
import { ref, watch, computed, provide, onActivated, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { timeDisplay, showTimeline, closeTimeline } from "@/utils/timelineDisplay.js";

import { useStore } from "vuex";
const store = useStore();

import baseInfo from "./baseInfo.vue"; //事件信息，按钮

//事件相关的固定tabs
import summarize from "./summarize.vue"; //事件综述
import taskList from "./taskList.vue"; //事件分析和事件处置
import assets from "./assets.vue"; //受影响资产
import vulns from "./vulns.vue"; //相关漏洞
import suspicious from "./suspicious.vue"; //可疑对象
import alarmReport from "./alarmReport.vue"; //告警与报告

//动态添加的tabs
import taskDetail from "./taskDetail.vue"; //任务详情
import attachment from "./attachment.vue"; //附件
import suspiciousDetail from "./suspiciousDetail.vue"; //可疑对象
import logList from "./logList.vue";

const router = useRouter();
const route = useRoute();

let id = route.params.id; //事件id

onActivated(() => {
  if (route.name == "EventDetail") {
    sessionStorage.setItem("eventId", id);
  }

  let tab = activeTabSelf.value;
  activeTabSelf.value = 0;
  nextTick(() => {
    activeTabSelf.value = tab;
  });
});

let activeTabSelf = ref("0");
provide(
  "activeTabSelf",
  computed(() => {
    return activeTabSelf.value;
  })
);
let activeTab = computed(() => {
  return store.state.eventDetail.activeTab;
});

watch(
  () => activeTabSelf.value,
  (val) => {
    store.commit("changeActiveTab", val);
  }
);

watch(
  () => activeTab.value,
  (val) => {
    activeTabSelf.value = val;
  }
);

//事件状态流
showTimeline();

//固定tabs
let staticTabs = [
  {
    name: 0,
    label: "事件综述",
  },
  {
    name: 1,
    label: "事件分析",
  },
  {
    name: 2,
    label: "事件处置",
  },
  {
    name: 3,
    label: "受影响资产",
  },
  {
    name: 4,
    label: "相关漏洞",
  },
  {
    name: 5,
    label: "可疑对象",
  },
  {
    name: 6,
    label: "告警与报告",
  },
];

function backList() {
  store.commit("closeCurrentTab");
  router.push({ name: "ThreatEvent" });
}
</script>

<style lang="scss" scoped>
.detail-wrapper {
  max-height: calc(100vh - 140px);
  border-radius: $radiusL;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 0;
  }
}
.tab-num {
  display: inline-flex;
  min-width: 22px;
  height: 22px;
  border: 1px solid $borderColor;
  font-size: 13px;
  padding: 2px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  margin-left: 10px;
}
:deep(.timeline-wrapper) {
  width: 20%;
  margin-left: 30px;
}
.isActive {
}
</style>
