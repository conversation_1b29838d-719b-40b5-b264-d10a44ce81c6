<template>
  <div style="padding-left: 30px">
    <statistics :border-line="false" :list="[{ num: total, text: '待确认资产总数' }]"></statistics>
  </div>
  <el-table :show-header="false" :data="tableData" :expandRowKeys="expandRowKeys" row-key="value" default-expand-all style="width: 100%">
    <el-table-column type="expand">
      <template #default="{ row }">
        <div class="gray-table">
          <ConfirmedUrl
            ref="urlRef"
            v-if="row.value == 1"
            :isDialog="true"
            @connect="$emit('connect', $event)"
            @updateTotal="getTotalTime"
            :assetsInfos="assetsInfos"
            :addEvent="addEvent"
          ></ConfirmedUrl>
          <ConfirmedIP
            ref="ipRef"
            v-if="row.value == 2"
            :isDialog="true"
            @connect="$emit('connect', $event)"
            @updateTotal="getTotalTime"
            :assetsInfos="assetsInfos"
            :addEvent="addEvent"
          ></ConfirmedIP>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="name" prop="name" />
  </el-table>
</template>
<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import ConfirmedIP from "@/views/securityAssets/confirm/recognized/confirmedIP.vue";
import ConfirmedUrl from "@/views/securityAssets/confirm/recognized/confirmedUrl.vue";
import { getAssetsIpConfirmedList, getAssetsConfirmedList } from "@/api/securityAssets/confirm";

import { useRoute } from "vue-router";

const route = useRoute();

let emit = defineEmits(["connect"]);
let tableData = reactive([
  {
    name: "域名URL待确认资产",
    value: 1,
  },
  {
    name: "IP类待确认资产",
    value: 2,
  },
]);
const expandRowKeys = ref([]); // 展开行id
// 展开行
function getExpandRow(data, isSearch) {
  expandRowKeys.value = [1, 2];
  setTimeout(() => {
    urlRef.value?.getSearch(true, data, isSearch);
    ipRef.value?.getListData(data, isSearch);
  }, 800);
}
let props = defineProps({
  assetsInfos: {
    type: String,
    default: "",
  },
  addEvent: {
    type: String,
    default: "",
  },
});
let urlRef = ref();
let ipRef = ref();

let total = ref(0);
getTotal();
function getTotal() {
  let params = {};
  if (route.name == "AddEvent") {
    params = { spare1: props.addEvent !== "" ? "" : route.params.id, assetsInfos: props.assetsInfos };
  } else {
    params = { assetsInfos: props.assetsInfos, spare1: props.addEvent !== "" ? "" : route.params.id };
  }

  Promise.all([getAssetsIpConfirmedList(params), getAssetsConfirmedList(params)]).then(([res1, res2]) => {
    total.value = res1.data.total + res2.data.total;
  });
}

function getTotalTime() {
  setTimeout(() => {
    getTotal();
  }, 500);
}

defineExpose({
  urlRef: computed(() => urlRef.value),
  ipRef: computed(() => ipRef.value),
  getExpandRow,
});
</script>

<style lang="scss" scoped>
.gray-table {
  padding: 20px;
}
</style>
