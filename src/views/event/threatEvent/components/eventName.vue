<template>
  <!-- 事件标题 -->
  <h3 class="event-title break">
    <el-tag class="margin-right20" :type="Level_Data[eventInfo.levelId]">{{ eventInfo.levelName }}</el-tag>
    Case #{{ eventInfo.eventNo }}-{{ eventInfo.titlePrefix }} &nbsp;&nbsp; {{ eventInfo.title }}
  </h3>
  <div class="flex-between margin-top20 info-wrapper">
    <ul class="flex create-data">
      <li>
        <el-icon size="14"><user /></el-icon>
        创建者:
        <span>{{ eventInfo.createName }}</span>
      </li>
      <li>
        <el-icon size="14"><calendar /></el-icon>
        创建日期:
        <span>{{ eventInfo.createTime }}</span>
      </li>
    </ul>
    <section class="margin-top10">
      <slot></slot>
    </section>
  </div>
</template>
<script setup>
import { ref, computed } from "vue";
import { Level_Data } from "@/config/constant";
import { getEventDetail } from "@/api/event/detail";
import { useStore } from "vuex";

const store = useStore();

let props = defineProps({
  id: {
    type: String,
    default: "",
  },
});

let eventInfoById = ref({});

let eventInfo = computed(() => {
  if (!props.id) {
    return store.state.eventDetail.eventInfo;
  } else {
    return eventInfoById.value;
  }
});
if (props.id) {
  getEventDetail({ id: props.id }).then(({ data }) => {
    eventInfoById.value = data.event;
  });
}
</script>

<style lang="scss" scoped>
.event-title {
  padding: 12px 24px;
  background: $bgColor;
  color: $fontColor;
  border-radius: $radiusS;
}
.info-wrapper {
  flex-wrap: wrap;
  & > section {
    margin-left: auto;
  }
}
.create-data {
  color: $fontColorSoft;
  li {
    margin-right: 64px;
  }
  :deep(.el-icon) {
    margin-right: 10px;
    transform: translateY(2px);
  }
  span {
    color: $colorRev;
  }
}
</style>
