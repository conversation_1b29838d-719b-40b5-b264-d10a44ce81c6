<template>
  <el-form v-loading="!editInfo" :model="formData" ref="ruleFormRef" label-width="6em" size="mini">
    <xel-form-item v-for="(item, index) in formList" :key="index" v-model="formData[item.prop]" v-bind="item"></xel-form-item>
  </el-form>
</template>
<script setup>
import { ref, reactive, toRefs, computed, watch } from "vue";
import { selectPage } from "@/api/securityAssets/assetGroup";
import { getCanSelectPersonByDept } from "@/api/securityAssets/business";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
let props = defineProps({
  editInfo: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
let state = reactive({
  formData: {
    createTime: "",
    ioc: "",
    description: "",
    tagJson: [],
  },
});

let { formData } = toRefs(state);

let formList = reactive([
  {
    formType: "input",
    prop: "createTime",
    label: "时间",
    required: true,
    disabled: true,
  },
  {
    formType: "tag",
    prop: "tagJson",
    label: "标签",
    required: true,
  },
  {
    formType: "select",
    prop: "ioc",
    label: "是否IOC",
    size: "mini",
    required: true,
    options: [
      { value: "1", label: "是" },
      { value: "0", label: "否" },
    ],
  },
  {
    formType: "input",
    required: true,
    prop: "description",
    label: "描述",
    type: "textarea",
  },
]);
const { editInfo } = toRefs(props);
console.log(editInfo);
state.formData.createTime = editInfo.value.createTime;
state.formData.description = editInfo.value.description;
state.formData.ioc = editInfo.value.ioc;
state.formData.tagJson = editInfo.value.tagRelList.map((item) => {
  return {
    id: item.tagId,
    name: item.tagName,
  };
});
defineExpose({
  formData: state.formData,
});
</script>

<style lang="scss" scoped></style>
