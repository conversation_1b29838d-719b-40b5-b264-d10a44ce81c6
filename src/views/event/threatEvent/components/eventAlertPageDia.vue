<template>
  <xel-dialog title="告警信息" ref="detailsRef" width="1200px" :show-submit="false" buttonCancel="关闭">
    <AlertTable />
  </xel-dialog>
</template>

<script>
export default {
  name: "eventAlertPageDia",
};
</script>

<script setup>
import { ref } from "vue";
import AlertTable from "@/views/event/threatEvent/components/alertTable.vue";
let detailsRef = ref();
const open = () => {
  detailsRef.value.open();
};
defineExpose({
  open,
});
</script>
<style scoped></style>
