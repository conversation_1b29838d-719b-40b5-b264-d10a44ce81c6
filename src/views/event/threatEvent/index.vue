<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="事件" name="first">
        <Event ref="eventRef" :key="'first'" />
      </el-tab-pane>
      <el-tab-pane label="待审核事件" name="second">
        <UnconfirmedEvent ref="unconfirmedEvent" :key="'second'" />
      </el-tab-pane>
      <el-tab-pane label="驳回事件" name="third">
        <RejectList ref="rejectListRef" :key="'third'" />
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "ThreatEvent",
};
</script>
<script setup>
import Event from "./eventList.vue";
import UnconfirmedEvent from "./unconfirmedEvent.vue";
import RejectList from "./rejectList.vue";
import { ref, reactive, toRefs, onMounted, watchEffect, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
const route = useRoute();
const router = useRouter();
let activeName = ref("first");
let eventRef = ref();
let unconfirmedEvent = ref();
let rejectListRef = ref();
function handleClick(val) {
  activeName.value = val.paneName;
  if (val.paneName == "first") {
    eventRef.value.search();
  } else if (val.paneName == "second") {
    unconfirmedEvent.value.search();
  } else {
    rejectListRef.value.search();
  }
}
// watchEffect(() => {
//   activeName.value = route.query.tabName ? route.query.tabName : "first";
// });
// watch(
//   () => route.query.tabName,
//   (val) => {
//     if (val) {
//       activeName.value = val;
//     } else {
//       activeName.value = "first";
//     }
//     console.log("传入的tab--activeName.value： ", val, activeName.value);
//   },
//   {
//     immediate: true,
//   }
// );
</script>

<style lang="scss" scoped></style>
