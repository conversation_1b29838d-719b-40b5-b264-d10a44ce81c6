<template>
  <el-card>
    <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="selectAlreadyDelAlert" @selection-change="handleSelectionChange">
      <template #level="scope">
        <el-tag :type="levelData[scope.row.priority]">{{ scope.row.priorityStr }}</el-tag>
      </template>
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "AlarmDeleted",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, onActivated } from "vue";
import { useRouter, useRoute } from "vue-router";
import { selectAlreadyDelAlert } from "@/api/event/alarmReport.js";
import { useStore } from "vuex";
import { Level_priority } from "@/config/constant";
const store = useStore();
const route = useRoute();

onActivated(() => {
  search(false);
});
let state = reactive({
  levelData: Level_priority,
});
let { levelData } = toRefs(state);
let tableRef = ref();
const columns = [
  {
    prop: "title",
    label: "告警名称",
  },
  {
    prop: "priorityStr",
    label: "告警等级",
    slotName: "level",
  },
  {
    prop: "devIp",
    label: "设备IP",
  },
  {
    prop: "devName",
    label: "设备名称",
  },
  {
    prop: "devType",
    label: "设备类型",
  },
  {
    prop: "userName",
    label: "申请人",
  },
  {
    prop: "applyTimeStr",
    label: "申请时间",
  },
  {
    prop: "applyReason",
    label: "申请原因",
  },
];
//搜索相关
let searchState = reactive({
  data: {
    title: "",
    priority: "",
    applyUser: "",
    createTimeStr: [],
  },
  menuData: [
    {
      lable: "告警等级：",
      prop: "priority",
      dictName: "alert_level",
    },
  ],
  formList: [
    {
      formType: "input",
      prop: "title",
      label: "告警名称",
    },
    {
      formType: "input",
      prop: "applyUser",
      label: "申请人",
    },
    // {
    //   formType: "date",
    //   type: "daterange",
    //   prop: "createTimeStr",
    //   label: "申请时间",
    // },
    {
      formType: "daterange",
      type: "datetimerange",
      prop: "createTimeStr",
      label: "申请时间",
      // required: true,
      onChange: (val) => {
        searchState.data.createTimeStr = val;
        // state.basicData.endTimeStr = val[1];
        // state.basicData.beginTimeStr = val[0];
      },
    },
  ],
});
function search(initPageNum = true) {
  let params = { ...searchState.data, applyTimeBegin: "", applyTimeEnd: "" };
  if (searchState.data.createTimeStr && searchState.data.createTimeStr.length > 0) {
    params.applyTimeBegin = searchState.data.createTimeStr[0];
    params.applyTimeEnd = searchState.data.createTimeStr[1];
  }
  delete params.createTimeStr;
  tableRef.value.reload(params, initPageNum);
}
function reset() {
  searchState.data = {
    title: "",
    priority: "",
    applyUser: "",
    createTimeStr: [],
  };
  search();
}
</script>

<style lang="scss" scoped></style>
