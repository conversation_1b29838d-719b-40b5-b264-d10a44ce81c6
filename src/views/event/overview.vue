<template>
  <div class="home-event">
    <el-row :gutter="20" class="inevent">
      <el-col :span="6"
        ><el-card class="wid">
          <el-row :gutter="24">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-minganxinxileixingshu" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title">已处理告警</p>
              <p class="tap-number">{{ processedAlarm.count }}</p></el-col
            >
          </el-row>
          <ul class="level-data">
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.urgent }"></span><span>{{ processedAlarm[5] }}</span>
            </li>
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.hight }"></span><span>{{ processedAlarm[4] }}</span>
            </li>
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.middle }"></span><span>{{ processedAlarm[3] }}</span>
            </li>
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.low }"></span><span>{{ processedAlarm[2] }}</span>
            </li>
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.ash }"></span><span>{{ processedAlarm[1] }}</span>
            </li>
          </ul>
          <el-tag>最近15天</el-tag>
        </el-card></el-col
      >
      <el-col :span="6"
        ><el-card class="wid">
          <el-row :gutter="24">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-redianshijian" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title">已分析安全事件</p>
              <p class="tap-number">{{ analyzed.count }}</p></el-col
            >
          </el-row>
          <ul class="level-data">
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.urgent }"></span><span>{{ analyzed[4] }}</span>
            </li>
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.hight }"></span><span>{{ analyzed[3] }}</span>
            </li>
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.middle }"></span><span>{{ analyzed[2] }}</span>
            </li>
            <li @click="urgent(1)">
              <span class="level-color" :style="{ background: Level_Color.low }"></span><span>{{ analyzed[1] }}</span>
            </li>
          </ul>
          <el-tag>最近15天</el-tag>
        </el-card>
      </el-col>
      <el-col :span="6"
        ><el-card class="wid">
          <el-row :gutter="24" class="tter">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-xitong" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title">业务系统资产事件数量</p>
              <p class="tap-number">{{ businessSystem }}</p></el-col
            >
          </el-row>
          <el-tag>服务期内</el-tag>
        </el-card></el-col
      >
      <el-col :span="6"
        ><el-card class="wid">
          <el-row :gutter="24" class="tter">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-wodeshebei" :size="30"></icon></div
            ></el-col>
            <el-col :span="17" class="number">
              <p class="tap-title">计算设备资产事件数量</p>
              <p class="tap-number">{{ computing }}</p></el-col
            >
          </el-row>
          <el-tag>服务期内</el-tag>
        </el-card></el-col
      >
    </el-row>
    <ul class="middle">
      <li>
        <el-card>
          <div class="Div">
            <p class="name">
              事件级别分布
              <echart-edit-btn echartId="eventOverviewEchart1" :dataset="eventLevelOption.dataset" :color="eventLevelOption.color"></echart-edit-btn>
            </p>
            <el-radio-group v-model="radio1" @change="alertTypeCount(radio1)">
              <el-radio-button label="1">周</el-radio-button>
              <el-radio-button label="2">月</el-radio-button>
              <el-radio-button label="3">年</el-radio-button>
            </el-radio-group>
          </div>
          <echart-component
            echartType="2"
            echartId="eventOverviewEchart1"
            class="autc"
            :options="eventLevelOption"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
          ></echart-component>
          <!-- <pie v-if="activities.length > 0" ref="pieRef" :inData="activities" class="autc"></pie> -->
        </el-card>
      </li>
      <li>
        <el-card>
          <div class="Div">
            <p class="name">
              攻击事件类型
              <echart-edit-btn echartId="eventOverviewEchart2" :dataset="indexsituationOption.dataset"></echart-edit-btn>
            </p>
            <el-radio-group v-model="radio2" @change="attackEvents(radio2)">
              <el-radio-button label="1">周</el-radio-button>
              <el-radio-button label="2">月</el-radio-button>
              <el-radio-button label="3">年</el-radio-button>
            </el-radio-group>
          </div>
          <echart-component
            echartType="2"
            echartId="eventOverviewEchart2"
            class="autc"
            :options="indexsituationOption"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
          ></echart-component>
          <!-- <div ref="indexsituation" class="autc"></div> -->
        </el-card>
      </li>
      <li>
        <el-card>
          <p class="name">
            告警次数
            <echart-edit-btn echartId="eventOverviewEchart3" :dataset="yesituation.dataset" :color="yesituation.color"></echart-edit-btn>
          </p>
          <echart-component
            echartType="0"
            echartId="eventOverviewEchart3"
            class="autc"
            :options="yesituation"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
          ></echart-component>
        </el-card>
      </li>
    </ul>
    <el-row :gutter="20" class="bottom">
      <el-col :span="14"
        ><el-card class="heg">
          <p class="name">
            安全事件
            <echart-edit-btn echartId="eventOverviewEchart4" :dataset="sjsituation.dataset"></echart-edit-btn>
          </p>
          <ul class="security">
            <li>
              <p class="quantity">年度事件总量</p>
              <p class="number">{{ event.count }}</p>
            </li>
            <li>
              <p class="quantity">全年事件处置率</p>
              <p class="number">{{ event.closeRate }}</p>
            </li>
            <li>
              <p class="quantity">本月新增事件</p>
              <p class="number">{{ event.currentCount }}</p>
            </li>
            <li>
              <p class="quantity">未处置完毕事件</p>
              <p class="number">{{ event.uncloseCount }}</p>
            </li>
          </ul>
          <echart-component
            echartType="1"
            echartId="eventOverviewEchart4"
            class="autc"
            :options="sjsituation"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
          ></echart-component>
          <!-- <div ref="securityIncidents" class="autc"></div>  -->
        </el-card></el-col
      >
      <el-col :span="10"
        ><el-card class="heg">
          <div class="Div">
            <p class="name">
              告警源生成告警量
              <echart-edit-btn echartId="eventOverviewEchart5" :dataset="jingsituation.dataset"></echart-edit-btn>
            </p>
            <el-radio-group v-model="radio3" @change="alarmSource(radio3)">
              <el-radio-button label="1">周</el-radio-button>
              <el-radio-button label="2">月</el-radio-button>
              <el-radio-button label="3">年</el-radio-button>
            </el-radio-group>
          </div>
          <echart-component
            echartType="0"
            echartId="eventOverviewEchart5"
            class="autc"
            :options="jingsituation"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
          ></echart-component>
          <!-- <div ref="alarmQuantity" class="autc"></div>  -->
        </el-card></el-col
      >
    </el-row>
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import {
  getEventLevelCount,
  getEventTypeCount,
  getAlertLevelCount,
  getEventCount,
  getAlertDevCount,
  getAlertDealCount,
  getEventAnalysisCount,
  getEventAssetCount,
} from "@/api/event/overview.js";
import * as echarts from "echarts";
import { Level_Color } from "@/config/constant";
let activities = ref([]);
// 事件等级分布
let eventLevelOption = ref({});
eventLevelOption.value = {
  tooltip: {
    trigger: "item",
    position: ["40%", "60%"],
  },
  color: ["#fe804d", "#eeca5f", "#4c70f4", "#5bce8f"],
  legend: {
    top: "5%",
    left: "center",
  },
  dataset: {
    source: [],
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "70%"],
      center: ["50%", "60%"],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      label: {
        show: false,
        position: "center",
      },
      emphasis: {
        label: {
          show: false,
          fontSize: "20",
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: false,
      },
    },
  ],
};
let radio1 = ref("3");
let radio2 = ref("3");
let radio3 = ref("3");
let indexsituationOption = ref({});
indexsituationOption.value = {
  legend: {
    top: "top",
  },
  grid: {
    bottom: 30,
    top: 50,
  },
  dataset: {
    source: [],
  },
  series: [
    {
      type: "pie",
      radius: [40, window.innerWidth < 1550 ? 120 : 150],
      center: ["50%", "60%"],
      roseType: "area",
      itemStyle: {
        borderRadius: 8,
      },
    },
  ],
};
let alarmTimes = ref();
let securityIncidents = ref();
let processedAlarm = ref({});
let event = ref({});
let analyzed = ref({});
let businessSystem = ref("");
let computing = ref("");
let noData = reactive({
  data: {
    title: {
      text: "暂无数据",
      x: "center",
      y: "center",
      textStyle: {
        color: "#848484",
        fontWeight: "normal",
        fontSize: 16,
      },
    },
  },
});
// 告警次数
let yesituation = ref({});
yesituation.value = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      // Use axis to trigger tooltip
      type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
    },
  },
  color: ["#4c70f4", "#eeca5f", "#fe804d"],
  legend: {},
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
  },
  yAxis: {
    type: "category",
  },
  dataset: {
    source: [],
  },
  series: [
    {
      name: "中危",
      type: "bar",
      stack: "total",
      label: {
        show: true,
      },
      emphasis: {
        focus: "series",
      },
    },
    {
      name: "高危",
      type: "bar",
      stack: "total",
      label: {
        show: true,
      },
      emphasis: {
        focus: "series",
      },
    },
    {
      name: "紧急",
      type: "bar",
      stack: "total",
      label: {
        show: true,
      },
      emphasis: {
        focus: "series",
      },
    },
  ],
};
// 安全事件
let sjsituation = ref({});
sjsituation.value = {
  tooltip: {
    trigger: "axis",
  },
  legend: {},
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
  },
  yAxis: {
    type: "value",
  },
  dataset: {
    source: [],
  },
  series: [
    {
      name: "未处置事件",
      type: "line",
      stack: "Total",
    },
    {
      name: "当月事件总量",
      type: "line",
      stack: "Total",
    },
  ],
};
function evelCount() {
  getAlertLevelCount().then((res) => {
    console.log(res);
    let source = [];
    for (var i in res.data) {
      source.push([i, res.data[i][3], res.data[i][4], res.data[i][5]]);
    }
    yesituation.value.dataset.source = source;
  });

  let source = [];
  source.push(["product", "未处置事件", "当月事件总量"]);
  getEventCount().then((res) => {
    event.value = res.data;

    for (var i in res.data.list) {
      source.push([res.data.list[i].time, res.data.list[i].count, res.data.list[i].uncloseCount]);
    }
    sjsituation.value.dataset.source = source;
  });

  // 查询已处置告警
  let ass = {
    dayCount: 15,
  };
  getAlertDealCount(ass).then((res) => {
    processedAlarm.value = res.data;
  });
  // 查询已分析安全事件数
  getEventAnalysisCount(ass).then((res) => {
    analyzed.value = res.data;
  });
  //业务系统
  getEventAssetCount({ type: 1 }).then((res) => {
    businessSystem.value = res.data.count;
  });
  // 计算设备
  getEventAssetCount({ type: 2 }).then((res) => {
    computing.value = res.data.count;
  });
}
evelCount();
// 事件级别分布图
function alertTypeCount(val) {
  activities.value = [];
  getEventLevelCount({ timeType: val }).then((res) => {
    activities.value = [
      ["紧急", res.data[4]],
      ["高危", res.data[3]],
      ["中危", res.data[2]],
      ["低危", res.data[1]],
    ];
    eventLevelOption.value.dataset.source = activities.value;
  });
}
alertTypeCount(3);
// 攻击事件类型图
function attackEvents(item) {
  getEventTypeCount({ timeType: item }).then((res) => {
    let ass = [];
    res.data.names.forEach((item, index) => {
      ass.push([item, res.data.counts[index]]);
    });

    indexsituationOption.value.dataset.source = ass;
  });
}
// 告警源图
let jingsituation = ref({});
jingsituation.value = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      // Use axis to trigger tooltip
      type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
    },
  },
  legend: {},
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
  },
  yAxis: {
    type: "category",
  },
  dataset: {
    source: [["product", "告警量"]],
  },
  series: [
    {
      type: "bar",
      stack: "total",
      label: {
        show: true,
      },
      emphasis: {
        focus: "series",
      },
    },
  ],
};
function alarmSource(on) {
  getAlertDevCount({ timeType: on }).then((res) => {
    jingsituation.value.dataset.source = [["product", "告警量"]];
    for (var i in res.data) {
      jingsituation.value.dataset.source.push([i, res.data[i]]);
    }
  });
}
alarmSource(3);
// 告警源
let gaosituation = null;
// 告警类型占比
let mysituation = null;
let opsituation = reactive({});
attackEvents(3);
// 告警次数
let onsituation = null;
// 安全事件
let aqsituation = null;
// echarts修改后传值修改
function updateEchart(echartId, option) {}
</script>

<style lang="scss" scoped>
ul {
  display: flex;
  margin: 20px 0 20px 0;
  li {
    width: 33.33%;
    margin-right: 20px;
  }
  li:last-child {
    margin: 0;
  }
}
.name {
  font-weight: 400;
  color: $fontColor;
  font-size: 16px;
  margin-bottom: 10px;
  cursor: pointer;
}
.Div {
  display: flex;
  justify-content: space-between;
}
.security {
  display: flex;
  li {
    width: 25%;
    margin-right: 20px;
  }
  li:last-child {
    margin: 0;
  }
}
.quantity {
  font-size: 14px;
  font-weight: 300;
  color: $fontColorSoft;
  margin-bottom: 10px;
}
.number {
  font-size: 24px;
  font-weight: 600;
  color: #28334f;
}
.level-color {
  margin-right: 5px;
}
.wid {
  height: 207px;
}
.heg {
  height: 634px;
}
.autc {
  width: 100%;
  height: 450px;
}
.el-tag {
  border-radius: 15px;
}
.tter {
  margin-bottom: 50px;
}
.level-data {
  margin: 15px 0;
}
</style>
