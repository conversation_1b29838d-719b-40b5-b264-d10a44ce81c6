<template>
  <!-- 威胁运营态势 - 主页 -->
  <ThreatOperationOld v-if="showType == '0' && pathname === 'threatOperation'" />
  <ThreatOperationNew v-else-if="showType == '1' && pathname === 'threatOperation'" />

  <!-- 脆弱性运营态势 - 主页 -->
  <VulnerabilityOld v-else-if="showType == '0' && pathname === 'vulnerability'" />
  <VulnerabilityNew v-else-if="showType == '1' && pathname === 'vulnerability'" />

  <!-- 运营质量态势 - 主页 -->
  <StatePersonnelOld v-else-if="showType == '0' && pathname === 'statePersonnel'" />
  <StatePersonnelNew v-else-if="showType == '1' && pathname === 'statePersonnel'" />

  <!-- 运营基础设施架构 - 主页 -->
  <InfrastructureOld v-else-if="showType == '0' && pathname === 'infrastructure'" />
  <InfrastructureNew v-else-if="showType == '1' && pathname === 'infrastructure'" />
</template>

<script>
export default {
  name: "index",
};
</script>

<script setup>
import { onMounted, ref } from "vue";
/* 威胁运营态势 */
import ThreatOperationOld from "./threatOperation/index.vue";
import ThreatOperationNew from "./new/threatOperation/index.vue";
/* 脆弱性运营态势 */
import VulnerabilityOld from "./vulnerability/index.vue";
import VulnerabilityNew from "./new/vulnearbility/index.vue";
/* 运营质量态势 */
import StatePersonnelOld from "./statePersonnel/index.vue";
import StatePersonnelNew from "./new/statePersonnel/index.vue";
/* 运营基础设施架构 */
import InfrastructureOld from "./infrastructure/index.vue";
import InfrastructureNew from "./new/infrastructure/index.vue";

import { getScreenParam } from "@/api/screen/login";

/*  0:表示旧大屏，1:表示新大屏 */
let showType = ref("");
let pathname = ref("");
onMounted(() => {
  if (window.location.pathname.indexOf("threatOperation") > -1) {
    pathname.value = "threatOperation";
  } else if (window.location.pathname.indexOf("vulnerability") > -1) {
    pathname.value = "vulnerability";
  } else if (window.location.pathname.indexOf("statePersonnel") > -1) {
    pathname.value = "statePersonnel";
  } else if (window.location.pathname.indexOf("infrastructure") > -1) {
    pathname.value = "infrastructure";
  }

  /* 获取大屏参数 0:表示旧大屏，1:表示新大屏 */
  getScreenParam().then((res) => {
    showType.value = res.data.data;
  });
});
</script>

<style scoped></style>
