<template>
  <!-- 脆弱性运营态势 - new -->
  <scaleWrapper>
    <div class="wrapper">
      <starsoPage2 titleText="脆弱性运营态势" borderType="page3">
        <div class="content-wrapper">
          <starso-border1 class="left-wrapper">
            <starso-title1 class="title">安全漏洞</starso-title1>
            <div class="left_top">
              <div class="testClass">
                <div>
                  <div>
                    <p>业务系统</p>
                    <starso-title4 typeName="修复率"></starso-title4>
                  </div>
                  <!--                  <circularProcess :key="updateCount" :count="busCorrectRate"></circularProcess>-->
                  <CharGauge :size="100" :pieData="busCorrectRate" />
                </div>
                <starso-num2 title="漏洞总数" :count="busCount"></starso-num2>
              </div>
              <div class="testClass">
                <div>
                  <div>
                    <p>基础资源</p>
                    <starso-title4 typeName="修复率"></starso-title4>
                  </div>
                  <!--                  <circularProcess :key="updateCount" :count="basCorrectRate"></circularProcess>-->
                  <CharGauge :size="100" :pieData="basCorrectRate" />
                </div>
                <starso-num2 title="漏洞总数" :count="basCount"></starso-num2>
              </div>
            </div>
            <starso-title1 class="title">资产漏洞</starso-title1>
            <div class="left_bottom">
              <starso-legend :list="legendList" align="right"></starso-legend>
              <starsoRank1
                :data="assetsVulnList"
                :colorList="colorList"
                :limitScrollNum="6"
                :listHeight="565"
                type="rotation"
                ref="starsoRank"
                @getChoseNum="getChoseNum"
              >
                <starso-border-6>
                  <starso-title-5 title="漏洞整改率" :count="vulnReate"></starso-title-5>
                  <starso-pie-and-custom-legend :data="pieData" :colorList="pieColorList" :pieTotal="pieTotal"></starso-pie-and-custom-legend>
                </starso-border-6>
              </starsoRank1>
            </div>
          </starso-border1>
          <div class="right-wrapper">
            <div class="right-wrapper-top">
              <div class="right-wrapper-top-left">
                <div class="vuln-count">漏洞总数</div>
                <starso-num-3 :num="vulnTotal"></starso-num-3>
                <div class="right-wrapper-top-left-bottom">
                  <starso-border-7 title="应用漏洞" :num="resourceVulnTotal" bgType="2"></starso-border-7>
                  <starso-border-7 title="系统漏洞" :num="businessVulnTotal" bgType="1"></starso-border-7>
                  <starso-border-7 title="高危端口" :num="heghRiskPort" bgType="2"></starso-border-7>
                  <starso-border-7 title="弱口令" :num="ruoKoulin" bgType="1"></starso-border-7>
                </div>
              </div>

              <starso-border1>
                <starso-title1 class="title">漏洞类型TOP5</starso-title1>
                <starso-pie-and-custom-legend :data="vulnTop5Data" :pieTotal="vulnTypeCount"></starso-pie-and-custom-legend>
                <starso-title1 class="title">
                  <div class="vuln_top_5">
                    <div>紧急漏洞TOP5</div>
                    <starso-num1 class="num1" title="紧急漏洞" :num="vulnCount"></starso-num1>
                  </div>
                </starso-title1>
                <starsoScrollTable1
                  :columns="rightTableColumns"
                  :list="urgentVulnTop5"
                  :listHeight="200"
                  :stripe="true"
                  :showIndex="true"
                  :limitScrollNum="6"
                ></starsoScrollTable1>
              </starso-border1>
            </div>
            <div class="right-wrapper-bottom">
              <starso-border1 class="">
                <starso-title1 class="title">漏洞实时监测</starso-title1>
                <div class="clearfix"></div>
                <starsoScrollTable1
                  style="margin-top: 12px"
                  :columns="bootomTableColumns"
                  :list="rightTableList"
                  :listHeight="223"
                  :stripe="true"
                  :showIndex="false"
                  :legend="false"
                  :limitScrollNum="6"
                ></starsoScrollTable1>
              </starso-border1>
            </div>
          </div>
        </div>
      </starsoPage2>
    </div>
  </scaleWrapper>
</template>

<script setup>
import CharGauge from "../components/charGauge.vue";
import scaleWrapper from "starso/scaleWrapper";
import starsoBorder1 from "starso/starsoBorder1";
import starsoBorder2 from "starso/starsoBorder2";
import starsoBorder4 from "starso/starsoBorder4";

import starsoRank1 from "starso/starsoRank1";
import starsoNum1 from "starso/starsoNum1";

import starsoScrollTable1 from "starso/starsoScrollTable1";
import starsoScrollTable2 from "starso/starsoScrollTable2";

import starsoSwiper1 from "starso/starsoSwiper1";
import customWorkOrder from "starso/customWorkOrder";
import customLightCircle from "starso/customLightCircle";
import customRadar from "starso/customRadar";
import customBox1 from "starso/customBox1";

import starsoTitle2 from "starso/starsoTitle2";
import starsoTitle1 from "starso/starsoTitle1";
import starsoPage2 from "starso/starsoPage2";
import circularProcess from "starso/circularProcess";
import starsoTitle4 from "starso/starsoTitle4";
import starsoNum2 from "starso/starsoNum2";
import starsoLegend from "starso/starsoLegend";
import starsoBorder6 from "starso/starsoBorder6";
import starsoTitle5 from "starso/starsoTitle5";
import starsoPieAndCustomLegend from "starso/starsoPieAndCustomLegend";
import starsoNum3 from "starso/starsoNum3";
import starsoBorder7 from "starso/starsoBorder7";
// 接口调用
import {
  selectAssetVulnDistributionAnalysisList,
  selectSafetyVuln,
  selectRectificationRateList,
  selectAssetsVulnConstituteList,
  selectBusinessVulnRealTimeStatusList,
  selectVulnCategoryCount,
  selectVulnTypeRankingList,
  selectEmergencyVulnList,
  selectEmergencyVulnCount,
} from "@/api/screen/new/newVulnearbility.js";

import { ref, reactive, toRefs, onMounted } from "vue";
let busCount = ref(0);
let busCorrectRate = ref(0);
let basCount = ref(0);
let basCorrectRate = ref(0);
let updateCount = ref(0);
function getVulnRepairRateAndVulnSum() {
  selectSafetyVuln().then((res) => {
    if (res.data) {
      let result = res.data;
      busCount.value = result.businessCount;
      busCorrectRate.value = parseFloat(result.businessRate.replace("%", ""));
      basCount.value = result.resourceCount;
      basCorrectRate.value = parseFloat(result.resourceRate.replace("%", ""));
      updateCount.value++;
    }
  });
}
getVulnRepairRateAndVulnSum();

let legendList = ref([
  {
    level: "1",
    color: "#f33826",
    name: "紧急",
  },
  {
    level: "2",
    color: "#fab025",
    name: "高危",
  },
  {
    level: "3",
    color: "#0f9ee2",
    name: "中危",
  },
]);
let assetsVulnList = ref([]);
let colorList = ref([
  { color: "#f33826", field: "emergecy" },
  { color: "#fab025", field: "high" },
  { color: "#0f9ee2", field: "middle" },
]);
let pieColorList = ref([]);
let pieData = ref([]);
// 漏洞总数
let pieTotal = ref(0);
let newChoseNum = ref(0);
let fieldList = [];
// 查询中高紧急漏洞资产关系
function getAssetsVulnRelationData() {
  selectAssetVulnDistributionAnalysisList().then((res) => {
    assetsVulnList.value = res.data;
    assetsVulnList.value.forEach((item) => {
      item.name = item.assetsName;
    });
    pieTotal.value = assetsVulnList.value[0].total;
    getAssetsVulnConstituteData(assetsVulnList.value[0].assetsId);
  });
}
getAssetsVulnRelationData();
// 漏洞整改率及漏洞类型分布
let vulnReate = ref("");
function getAssetsVulnConstituteData(assetsId) {
  selectRectificationRateList({ businessId: assetsId }).then((res) => {
    let data = res.data;
    vulnReate.value = data.rate;
  });
  selectAssetsVulnConstituteList({ businessId: assetsId }).then((res) => {
    if (res.data) {
      let data = res.data;
      pieData.value = data.map((item) => {
        return {
          vulnType: item.category,
          count: item.sum,
          rate: item.rate.replace("%", ""),
        };
      });
    }
  });
}

function getChoseNum(data, total) {
  // 传递轮播值
  newChoseNum.value = data;
  pieTotal.value = total;
  getAssetsVulnConstituteData(assetsVulnList.value[newChoseNum.value].assetsId);
}
// 应用漏洞个数，系统漏洞个数
let vulnTotal = ref(0);
let businessVulnTotal = ref(0);
let resourceVulnTotal = ref(0);
let ruoKoulin = ref(0);
let heghRiskPort = ref(0);
function getStatisticsVulnData() {
  selectVulnCategoryCount().then((item) => {
    if (item.data) {
      let data = item.data;
      vulnTotal.value = data.total;
      businessVulnTotal.value = data.businessCount;
      resourceVulnTotal.value = data.resourceCount;
      heghRiskPort.value = data.portCount;
      ruoKoulin.value = data.total - data.businessCount - data.resourceCount;
    }
  });
}
getStatisticsVulnData();
// 漏洞实时监测
let rightTableList = ref([]);
let bootomTableColumns = ref([
  {
    label: "受影响资产",
    prop: "assetsName",
    width: "300",
  },
  {
    label: "漏洞标题",
    prop: "name",
    width: "300",
  },

  {
    label: "漏洞来源",
    prop: "typeName",
    width: "150",
  },
  {
    label: "更新时间",
    prop: "firstSubmitTime",
    width: "200",
  },
  {
    label: "处置状态",
    prop: "disposalStatus",
    width: "200",
    type: "tag",
  },
]);
function getVulnRealTimeMonitoring() {
  selectBusinessVulnRealTimeStatusList().then((res) => {
    rightTableList.value = res.data;
  });
}
getVulnRealTimeMonitoring();
// 漏洞类型top5
let vulnTop5Data = ref([]);
let vulnTypeCount = ref(0);
function getVulnTypeTop5() {
  selectVulnTypeRankingList().then((res) => {
    vulnTop5Data.value = res.data.map((item) => {
      return {
        vulnType: item.type,
        count: item.vulnTotal,
        rate: item.rate.replace("%", ""),
      };
    });
    vulnTypeCount.value = res.data[0].total;
  });
}
getVulnTypeTop5();
// 紧急漏洞top5
let urgentVulnTop5 = ref([]);
function getUrgentVulnTop5() {
  selectEmergencyVulnList().then((res) => {
    urgentVulnTop5.value = res.data;
  });
}
getUrgentVulnTop5();
let rightTableColumns = ref([
  {
    label: "漏洞名",
    prop: "title",
    width: "200",
  },
  {
    label: "资产数",
    prop: "count",
    width: "60",
  },
  {
    label: "资产TOP3",
    prop: "assetName",
    width: "120",
  },
]);
// 近30天告警
let vulnCount = ref(0);
function getUrgentVulnCount() {
  selectEmergencyVulnCount().then((res) => {
    vulnCount.value = res.data.emergencyVulnCount;
  });
}
getUrgentVulnCount();
let timer = ref(null);
if (timer.value) {
  clearInterval(timer.value);
} else {
  timer.value = setInterval(() => {
    getStatisticsVulnData();
    getVulnRealTimeMonitoring();
    getAssetsVulnRelationData();
    getVulnRepairRateAndVulnSum();
    getVulnTypeTop5();
    getUrgentVulnCount();
    getUrgentVulnTop5();
  }, 6000000);
}
</script>

<style scoped lang="scss">
@import "@/assets/style/newScreen.css";
$color: #0fbed2;
.yejing {
  font-family: "yejing";
}
.wrapper {
  /*width: 100vw;
  height: 100vh;*/
  height: 100%;
  /*background: #051931;*/
  color: #fff;
  background: url(@/assets/imgs/screen/vulnerability/vulnBg.jpg) no-repeat;
}
.content-wrapper {
  // padding: 85px 80px 57px;
  padding: 25px 25px;
  display: flex;
}
.left-wrapper {
  .rotatewrapper {
    margin-left: 15px;
  }
  padding: 28px 10px;
  flex: 479;
  .left_top {
    display: flex;

    > div {
      flex: 1;
      & {
        padding: 10px 0px;
      }
      &:nth-child(1) {
        margin-right: 40px;
      }
    }
    .testClass {
      & > div {
        display: flex;
        > div > p:nth-child(1) {
          text-align: center;
          font-size: 18px;
          font-weight: 400;
          color: #ffffff;
          opacity: 0.7;
        }
      }
    }
  }
  .left_bottom {
    .asset-sum {
      line-height: 61px;
      color: rgba(255, 255, 255, 0.7);
    }
    :deep(.bar-box) {
      margin-top: 5px;
    }
  }
  :deep(.starso-rank1) {
    // font-size: 14px;
    .right_li_bottom {
      min-height: 40px;
      height: auto;
      > .starso-border-2 {
        height: 40px;
      }
      > .starso-border-6 {
        height: 328px;
        padding-top: 40px;
        padding-left: 20px;
      }
    }
    .wrapper {
      background: rgba(39, 206, 225, 0.2);
    }
    div[class^="attackType"] {
      // margin-right: 18px;
    }
  }
  .title {
    margin-bottom: 30px;
  }
  .title2 {
    margin-top: 43px;
  }
  .num1 {
    font-size: 28px;
    width: 322px;
    height: 58px;
    margin-top: 26px;
    margin-bottom: 30px;
  }
  .scroll-table-box {
    padding: 25px 20px 20px;
    margin-top: 30px;
  }
  :deep(.starso-scroll-table1 .data-list li) {
    height: 40px;
    line-height: 40px;
  }
}
.right-wrapper {
  flex: 1440;
  display: grid;
  grid-template-rows: 596px 337px;
  // padding: 10px 0px;
  .right-wrapper-top {
    // padding: 0px 10px;
    display: flex;
    & > div {
      &:nth-child(1) {
        flex: 918;
      }
      &:nth-child(2) {
        flex: 463;
      }
    }
    :deep(.customLegend) {
      height: 200px;
      & > ul {
        width: calc(100% - 200px);
      }
    }

    .vuln_top_5 {
      width: 100%;
      display: grid;
      margin-top: 50px;
      grid-template-columns: 2fr 3fr;
      :deep(.starso-border-3) {
        margin-top: -4px;
      }
    }
    .starso-scroll-table1 {
      margin-top: 40px;
    }
    .starso-border-1 {
      padding: 10px;
    }
    .right-wrapper-top-left {
      .starso-num-3 {
        font-family: yejing;
      }

      margin-top: 30px;
      .right-wrapper-top-left-bottom {
        height: 583px;
        width: 798px;
        background: url(@/assets/imgs/screen/zjbg.png) no-repeat;
        top: -50px;
        left: 40px;
        position: relative;
        & > .starso_border_7 {
          width: 227px;
          height: 198px;
          position: absolute;
        }
        & > .starso_border_7:nth-child(1) {
          top: 193px;
          left: 85px;
        }
        & > .starso_border_7:nth-child(2) {
          top: 100px;
          left: 290px;
        }
        & > .starso_border_7:nth-child(3) {
          top: 193px;
          left: 492px;
        }
        & > .starso_border_7:nth-child(4) {
          top: 304px;
          left: 290px;
        }
      }
    }
  }
  .right-wrapper-bottom {
    margin-top: 10px;
    padding: 10px 0px 0px 10px;
    & > .starso-border-1 {
      padding: 10px;
    }
    .starso-scroll-table1 {
      margin-top: -18px;
    }
  }
}
.center-wrapper {
  position: relative;

  .swiper-box {
    position: absolute;
    bottom: 220px;
    left: 0;
    height: 303px;
    left: 20px;
  }

  .work-orders,
  .line-chart-box {
    bottom: 0px;
  }
  :deep(.custom-work-orders) {
    width: 580px;
  }
  .work-orders {
    position: absolute;
    left: 500px;
  }
  .line-chart-box {
    width: 463px;
    height: 192px;
    position: absolute;
    left: 20px;

    padding: 18px 13px 0;

    .title {
      color: #fff;
      margin-bottom: 5px;
    }
  }
  .light-circle {
    position: absolute;
    color: #fff;
    bottom: 280px;
    left: 490px;
    transform-origin: center bottom;
  }
  .circle-text {
    font-size: 17px;
    position: absolute;
    bottom: 235px;
    left: 490px;
  }
}
:deep(.customLegend > ul) {
  width: calc(100% - 240px);
}
:deep(.starso-scroll-table1 .tag) {
  width: auto !important;
}
:deep(.starso-num-3) {
  margin-top: 20px;
  & > span {
    &:last-child {
      margin-right: 0px;
    }
  }
}
.vuln-count {
  height: 17px;
  font-size: 18px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #ffffff;
  opacity: 0.7;
  text-align: center;
}
</style>
