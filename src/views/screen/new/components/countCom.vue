<template>
  <div class="countComDiv" v-if="styleType === 'block'">
    <p class="cTitle">{{ title }}</p>
    <p class="cNum yejing">{{ count }}</p>
  </div>

  <div class="botNumDiv" v-else-if="styleType === 'long'">
    <span>{{ title }}</span>
    <span class="yejing">{{ count }}</span>
  </div>

  <div class="mttCountDiv" v-else-if="styleType === 'mtt'">
    <span class="titleSpan">{{ title }}</span>
    <span class="conSpan">
      <span class="count yejing">{{ count }}</span>
      <span class="unitTit yejing">{{ unitTit }}</span>
    </span>
  </div>

  <div class="userNumComDiv" v-else-if="styleType === 'user'">
    <div>{{ count }} <span class="ge">个</span></div>
    <div class="desDiv">{{ title }}</div>
  </div>
</template>

<script>
export default {
  name: "countCom",
};
</script>

<script setup>
/* 数据项 - 组件 */
defineProps({
  /* 样式 默认 block
   * block = 块状
   * long = 长方形
   * mtt = 单位
   * user = 上下
   * */
  styleType: {
    type: String,
    default() {
      return "block";
    },
  },
  /* 数据名称 */
  title: {
    type: String,
    default() {
      return "未知";
    },
  },
  /* 数量 */
  count: {
    type: String || Number,
    default() {
      return 0;
    },
  },
  /* mtt 数据单位 */
  unitTit: {
    type: String,
    default() {
      return "MIN";
    },
  },
});
</script>

<style scoped lang="scss">
.yejing {
  font-family: yejing;
}
/* Style1 - block */
.countComDiv {
  display: inline-block;
  width: 201px;
  height: 91px;
  background-image: url("@/assets/imgs/screen/infrastructure/countBg.png");
  background-size: 100% 100%;
  text-align: center;
  font-size: 16px;
  .cTitle {
    margin-top: 19px;
    margin-bottom: 10px;
  }
  .cNum {
    font-size: 26px;
    color: #44d9f1;
  }
}
/* Style2 - long */
.botNumDiv {
  display: inline-block;
  height: 50px;
  background-image: url("@/assets/imgs/screen/statePersonnel/countBg1.png");
  background-size: 100% 100%;
  line-height: 50px;
  padding: 0 15px;
  span:first-child {
    opacity: 0.7;
    margin-right: 38px;
    position: relative;
    top: -2px;
  }
  span:last-child {
    font-size: 24px;
    color: #44b5ce;
    margin-right: 20px;
  }
}

/* Style3 - mtt */
.mttCountDiv {
  height: 67px;
  line-height: 67px;
  background-image: url("@/assets/imgs/screen/statePersonnel/assetBg.png");
  background-size: 100% 100%;
  position: relative;
  .titleSpan {
    opacity: 0.7;
    margin-left: 23px;
    font-size: 16px;
  }
  .conSpan {
    position: absolute;
    right: 29px;
    .count {
      font-size: 26px;
      color: #44d9f1;
    }
    .unitTit {
      color: #44d9f1;
      margin-left: 10px;
    }
  }
}
/* Style4 - user */
.userNumComDiv {
  text-align: center;
  font-size: 24px;
  font-weight: 500;
  color: #2df7cc;
  .ge {
    font-size: 16px;
  }
  .desDiv {
    margin-top: 10px;
    font-size: 14px;
    opacity: 0.7;
  }
}
</style>
