<template>
  <!-- tooltip 组件 -->
  <el-tooltip effect="light" :content="title" placement="top" :enterable="false" :hide-after="0" popper-class="maxTooltipWidth">
    <slot>
      {{ title }}
    </slot>
  </el-tooltip>
</template>

<script setup>
/*
 * 基于 element 配置 tooltip 组件
 * 用于统一配置 tooltip 参数
 * slot 自定义插槽 默认 title 文字
 * title 显示的文字 与 content
 * */
defineProps({
  title: {
    type: String,
    default() {
      return "";
    },
  },
});
</script>

<style lang="scss">
.maxTooltipWidth {
  max-width: 1000px !important;
}
</style>
