<template>
  <ul>
    <li v-for="(item, index) in data" :key="index" class="list-item">
      <TooltipCom :title="item.name">
        <div>
          <span class="indexSpan" :class="{ noOneSpan: index !== 0 }">{{ index + 1 }}</span>
          {{ item.name }}
        </div>
      </TooltipCom>
      <div>
        <div
          class="true"
          :style="{
            width: item.value > 0 && maxNum > 0 ? (item.value / maxNum) * 100 + '%' : '0%',
          }"
        ></div>
        <div
          class="false"
          :style="{
            width: item.value > 0 && maxNum > 0 ? (1 - item.value / maxNum) * 100 + '%' : '100%',
          }"
        ></div>
      </div>
      <div class="yj">{{ item.value }}<span>%</span></div>
    </li>
  </ul>
</template>

<script>
export default {
  name: "columnChaCom",
};
</script>

<script setup>
import TooltipCom from "./tooltipCom.vue";
defineProps({
  data: {
    type: Array,
    default() {
      return [
        { name: "测试1", value: 83 },
        { name: "测试2", value: 75 },
        { name: "测试3", value: 65 },
      ];
    },
  },
  maxNum: {
    type: Number,
    default() {
      return 100;
    },
  },
});
</script>

<style scoped lang="scss">
.indexSpan {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: rgba(148, 35, 38, 0.6);
  border: 1px solid #942326;
  font-size: 14px;
  color: #942326;
  margin-right: 5px;
}
.noOneSpan {
  background: rgba(211, 152, 31, 0.22);
  border: 1px solid rgba(211, 152, 31, 0.63);
  font-size: 14px;
  color: #d3981f;
}

.list-item {
  &:not(:first-child) {
    margin-top: 21px;
  }
  &:last-child {
    margin-bottom: 10px;
  }
  background: url("@/assets/imgs/screen/statePersonnel/listBg4.png") no-repeat;
  background-size: 100% 100%;
  height: 49px;
  display: flex;
  justify-content: space-between;
  & > div {
    line-height: 49px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:nth-child(1) {
      width: 130px;
      padding: 0px 14px;
      font-size: 14px;
      font-weight: 400;
      color: #fff;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &:nth-child(2) {
      width: 231px;
      margin: 0px 15px;
      display: flex;
      margin-top: 20px;
      & > div {
        height: 11px;
        &.true {
          background: #52ebfd;
        }
        &.false {
          background: #9e272b;
        }
      }
    }
    &:nth-child(3) {
      width: 70px;
      text-align: center;
      font-size: 18px;
      font-weight: 400;
      color: #35bed1;
    }
  }
  .yj {
    font-family: yejing;
    span {
      font-size: 12px;
      margin-left: 5px;
    }
  }
}
</style>
