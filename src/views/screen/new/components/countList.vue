<template>
  <div class="count-item" :class="{ clickable: item.screenUrl }" @click="toNavScreen">
    <TooltipCom :title="item.title">
      <div class="cTitleDiv">
        <span class="indexSpan" :class="{ noOneSpan: itemIndex !== 0 }">{{ itemIndex + 1 }}</span>
        {{ item.title }}
      </div>
    </TooltipCom>

    <TooltipCom :title="contentFun()">
      <div>
        <div
          v-for="(count, index) in item.countList"
          :key="index"
          :style="{
            width: item.total > 0 ? (count / item.total) * 100 + '%' : '0%',
            background: colorsArr[index],
          }"
        >
          <div v-if="index == 0 && item.total == 0" class="itemCom" />
        </div>
      </div>
    </TooltipCom>

    <div class="yj">{{ item.total }}<span>%</span></div>
  </div>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default() {
        return {
          title: "测试",
          countList: [25, 25, 25, 25],
          total: 100,
          screenUrl: "",
        };
      },
    },
    colors: {
      type: Array,
      default: () => {
        /*return ["#8C2125", "#CE961D", "#004395", "#00CEFF"];*/
        /*return ["#be0102", "#d8510f", "#ffc951", "#059e5a", "#2b8efd"];*/
        return [];
      },
    },

    /* index 序号*/
    itemIndex: {
      type: String || Number,
      default() {
        return 0;
      },
    },
  },

  computed: {
    /* 判断使用等级 */
    colorsArr() {
      /* 是否组件传递 */
      if (this.colors.length) return this.colors;
      /* 判断使用默认颜色等级 */
      if (this.item.countList.length === 5) {
        return ["#be0102", "#d8510f", "#ffc951", "#059e5a", "#2b8efd"];
      } else if (this.item.countList.length === 3) {
        return ["#48E8FD", "#D3981F", "#932326"];
      } else {
        return ["#be0102", "#ffc951", "#059e5a", "#2b8efd"];
      }
    },
  },

  methods: {
    toNavScreen() {
      if (this.item.screenUrl) {
        this.changeNav(this.item.screenUrl, this.item.params);
      }
    },
    contentFun() {
      const countList = this.item.countList;
      if (this.item.isSeim) {
        return `严重:${countList[0]}
        重要:${countList[1]}
        一般:${countList[2]}
        轻微:${countList[3]}
        信息:${countList[4]}`;
      } else {
        if (countList.length === 5) {
          return `紧急:${countList[0]}
        高:${countList[1]}
        中:${countList[2]}
        低:${countList[3]}
        信息:${countList[4]}`;
        } else if (countList.length === 3) {
          return `及时完成:${countList[0]}%
        超时50%以内:${countList[1]}%
        超时100%以内:${countList[2]}%`;
        } else {
          return `紧急:${countList[0]}
        高:${countList[1]}
        中:${countList[2]}
        低:${countList[3]}`;
        }
      }
    },
  },
};
</script>
<script setup>
import TooltipCom from "./tooltipCom.vue";
/*interface Props {
  item: object;
}

const props = withDefaults(defineProps<Props>(), {
  item: () => {
    return {};
  },
});

const changeNav: any = inject("changeNav");
function toNavScreen() {
  if (props.item.screenUrl) {
    changeNav(props.item.screenUrl, props.item.params);
  }
}*/
</script>
<style scoped lang="scss">
.indexSpan {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: rgba(148, 35, 38, 0.6);
  border: 1px solid #942326;
  font-size: 14px;
  color: #942326;
  margin-right: 5px;
}
.noOneSpan {
  background: rgba(211, 152, 31, 0.22);
  border: 1px solid rgba(211, 152, 31, 0.63);
  font-size: 14px;
  color: #d3981f;
}
.count-item {
  .cTitleDiv {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  margin-bottom: 24px;
  display: flex;
  height: 40px;
  background: url("@/assets/imgs/screen/statePersonnel/listBg4.png");
  background-size: 100% 100%;
  & > div {
    line-height: 40px;
    &:nth-child(1) {
      width: 130px;
      padding-left: 20px;
      font-size: 15px;
      font-weight: 400;
      color: #ffffff;
      opacity: 0.8;
    }
    &:nth-child(2) {
      position: relative;
      width: calc(100% - 200px);
      margin-left: 10px;
      margin-top: 15px;
      display: flex;
      margin-right: 35px;
      & > div {
        height: 11px;
      }
    }
    /*&:nth-child(3) {
      height: 31px;
      width: 1px;
      background-color: #32cbe8;
      margin: 22px 30px 0px 30px;
      opacity: 0.4;
    }*/
    &:nth-child(3) {
      width: 50px;
      font-size: 19px;
      font-weight: 400;
      color: #2fb6cb;
    }
  }
  .itemCom {
    box-sizing: border-box;
    border: 1px solid #2fb6cb;
    height: 11px;
    border-radius: 50px;
    position: absolute;
    width: 100%;
  }
  .yj {
    font-family: yejing;
    span {
      font-size: 12px;
      margin-left: 5px;
    }
  }
}
</style>
