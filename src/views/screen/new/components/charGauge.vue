<template>
  <!-- 环形进度图表 -->
  <div ref="unitPiePanel" class="rotatewrapper" :style="{ width: props.size + 'px', height: props.size + 'px' }"></div>

  <!--  <div style="position: relative;
    top: -51px;
    left: 61px;font-size: 12px">
    环量增比
  </div>-->
</template>
<script setup>
/*
 * 环形进度图表 - 组件
 * pieData  圆环数据百分比
 * isShowArrow 是否有箭头指向
 * arrowPointTo   外部计算箭头指向  默认 up(上)   down(下)
 * */
import * as echarts from "echarts";
import { ref, computed, onMounted, watch } from "vue";
const unitPiePanel = ref(null);
const props = defineProps({
  /* 组件 - 宽高值 - 限定宽高一样 */
  size: {
    type: Number,
    default() {
      return 150;
    },
  },

  /* 环形进度值 */
  pieData: {
    type: Number,
    default: 0,
  },

  /* 类型 - 是否显示箭头 */
  isShowArrow: {
    type: Boolean,
    default() {
      return false;
    },
  },

  /* 箭头指向 默认 up(上)   down(下) */
  arrowPointTo: {
    type: String,
    default() {
      return "";
    },
  },

  /* 小标题 */
  miniTit: {
    type: String,
    default() {
      return "";
    },
  },
});

/* 计算 - 箭头指向 */
const arrowPoint = computed(() => {
  if (props.arrowPointTo) return props.arrowPointTo;
  return props.pieData > 0 ? "up" : props.pieData < 0 ? "down" : "";
});

/* 生成图表 */
const initEcharts = () => {
  const unitPeiOption = {
    title: {
      text: `{a|${props.isShowArrow ? (arrowPoint.value === "up" ? "↑" : arrowPoint.value === "down" ? "↓" : "") : ""}${props.pieData}}
      {b| %}
      {c| ${props.miniTit}}`,
      x: "center",
      y: "center",
      textStyle: {
        rich: {
          a: {
            fontSize: props.size > 100 ? 28 : 16,
            color: "#37CAB7",
            fontFamily: "yejing",
            padding: [32, 0, 0, -10],
          },
          b: {
            fontSize: props.size > 100 ? 12 : 10,
            color: "#37CAB7",
            fontFamily: "yejing",
            padding: props.size > 100 ? [-20, 0, 10, 21] : [-20, 0, 10, 1],
          },
          c: {
            fontSize: 12,
            color: "#37CAB7",
            fontFamily: "yejing",
            padding: [10, 38, 0, 0],
          },
        },
      },
    },
    series: [
      {
        type: "gauge",
        radius: "81%",
        z: 1,
        clockwise: false, //逆时针
        startAngle: -269.99,
        endAngle: 90,
        splitNumber: 24,
        splitLine: {
          show: false,
        },
        detail: {
          //显示详情数据
          show: false,
        },
        // 仪表盘的线，颜色值为一个数组
        axisLine: {
          show: true,
          lineStyle: {
            width: 8,
            opacity: 1,
            color: [
              [
                props.pieData / 100,
                {
                  x: 1,
                  y: 1,
                  x1: 0,
                  y1: 0,
                  colorStops: [
                    {
                      offset: 1,
                      color: "#49FFFE",
                    },
                    {
                      offset: 0,
                      color: "#49FFFE",
                    },
                  ],
                },
              ],
              [1, "rgba(255,255,255,0.1)"],
            ],
          },
        },
        axisLabel: {
          show: false,
        },
        //指针
        pointer: {
          show: false,
        },
        axisTick: {
          show: true,
          splitNumber: 1,
          distance: -9,
          lineStyle: {
            color: "#365D8F",
            width: props.size > 100 ? 5 : 3,
          },
          length: 10,
        }, //刻度样式
        data: [{ value: props.pieData }],
      },
    ],
  };

  /* 等字体加载完成后 绘制图表 */
  document.fonts.ready.then(() => {
    const echartContent = echarts.init(unitPiePanel.value);
    echartContent.setOption(unitPeiOption);
  });
};

onMounted(() => {
  initEcharts();
});
watch(
  () => props.pieData,
  () => {
    initEcharts();
  }
);
</script>
<style scoped lang="scss">
.rotatewrapper {
  position: relative;
  &:after {
    position: absolute;
    top: 0;
    content: "";
    width: 100%;
    height: 100%;
    background: url("@/assets/imgs/screen/statePersonnel/progressBg.png") no-repeat;
    background-size: 100% 100%;
    animation: rotate 5s linear infinite;
  }
}
</style>
