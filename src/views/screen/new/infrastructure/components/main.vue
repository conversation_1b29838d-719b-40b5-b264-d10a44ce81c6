<template>
  <div class="container">
    <div class="map-panel">
      <div class="left-bottom wrapper">
        <div class="business-system" v-for="(item, index) in source1" :key="index" :style="{ marginTop: (1200 + index * 700) / baseFontSize + 'px' }">
          <div class="text">{{ item.name }}</div>
          <div class="nums">
            <span class="num1">{{ item.num1 || 0 }}</span>
            <span class="separate">/</span>
            <span class="num2">{{ item.num2 || 0 }}</span>
          </div>
          <div class="desc wrapper" :style="{ opacity: item.showDesc ? 1 : 0 }">
            <div class="text">{{ item.desc }}</div>
          </div>
        </div>
      </div>
      <div class="right-top wrapper">
        <div class="server-item" :style="{ marginTop: 87 / baseFontSize + 'rem' }">
          <div class="server-tip">资源监测引擎</div>
          <div class="time-desc">
            上一轮同步时间 <br />
            {{ weekdetail.ziChanYingQingTongBuShiJian || "未知" }}
          </div>
        </div>
        <div class="server-item" :style="{ marginTop: (87 + 67) / baseFontSize + 'rem' }">
          <div class="server-tip">互联网监测引擎</div>
          <div class="time-desc">
            上一轮同步时间 <br />
            {{ weekdetail.huLianWangJianCeYinQing || "未知" }}
          </div>
        </div>
      </div>
      <div class="center wrapper">
        <div class="line wrapper">
          <div class="total-count">
            <div class="detail-item">
              <div class="title">本周事件提报数</div>
              <div class="num">{{ weekdetail.benZhouTiBaoShiJianShu || 0 }}</div>
            </div>
            <div class="detail-item">
              <div class="title">事件完成审核</div>
              <div class="num">{{ weekdetail.shiJianWanChengShenHe || 0 }}</div>
            </div>
            <div class="detail-item">
              <div class="title">事件待审核</div>
              <div class="num">{{ weekdetail.shiJianDaiShenHe || 0 }}</div>
            </div>
            <div class="detail-item">
              <div class="title">本周发出复测安排</div>
              <div class="num">{{ weekdetail.benZhouFaChuFuCeAnPai || 0 }}</div>
            </div>
            <div class="detail-item">
              <div class="title">本周完成复测</div>
              <div class="num">{{ weekdetail.benZhouWanChengFuCe || 0 }}</div>
            </div>
            <div class="detail-item">
              <div class="title">待执行复测</div>
              <div class="num">{{ weekdetail.daiZhiXingFuCe || 0 }}</div>
            </div>
          </div>
        </div>
        <div class="notebook wrapper">
          <div class="main-tip wrapper">告警外发</div>
        </div>
        <div class="interfaces">
          <div class="interface-item">微信接口<span class="succesStatus" :class="{ errorStatus: weekdetail.WeiXinJieKou == '0' }"></span></div>
          <div class="interface-item">短信接口<span class="succesStatus" :class="{ errorStatus: weekdetail.duanXingJieKou == '0' }"></span></div>
          <div class="interface-item">邮件接口<span class="succesStatus" :class="{ errorStatus: weekdetail.youJianJieKou == '0' }"></span></div>
        </div>

        <!-- <div class="notebook wrapper">
          <div class="main-tip wrapper">系统漏扫引擎</div>
          <div class="time-desc">上一轮同步时间 <br/> 2021-04-22 12:00:00</div>
        </div> -->
        <div class="siem wrapper">
          <div class="main-tip wrapper">SIEM</div>
          <div class="nums">
            <span class="num1">{{ siemData[0] || 0 }}</span>
            <span class="separate">/</span>
            <span class="num2">{{ siemData[1] || 0 }}</span>
          </div>
        </div>
        <!-- <div class="alarm wrapper">
          <div class="main-tip wrapper">告警外发</div>
        </div> -->
        <div class="main wrapper">
          <div class="main-tip wrapper yyzzClass">运营支撑系统</div>
          <!-- <div class="nums">
            <span class="num1">0</span>
            <span class="separate">/</span>
            <span class="num2">4</span>
          </div> -->
        </div>
        <!-- <div class="interfaces">
          <div class="interface-item">微信接口<span class="succesStatus" :class="{errorStatus:(weekdetail.WeiXinJieKou)=='0'}"></span></div>
          <div class="interface-item">短信接口<span class="succesStatus" :class="{errorStatus:(weekdetail.WeiXinJieKou)=='0'}"></span></div>
          <div class="interface-item">邮件接口<span class="succesStatus" :class="{errorStatus:(weekdetail.WeiXinJieKou)=='0'}"></span></div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import {
  getSelectDataList as selectDataList,
  getSelectEngineEquipmentRunStatus as selectEngineEquipmentRunStatus,
  getSelectEngineEquipmentYiChangeRunStatus as deviceStatus,
  getSelectSiemTotalNumList as selectSiemTotalNumList,
} from "@/api/screen/new/infrastructure";

let tabLoopTimer = null;
let scrollTimer = null;
export default {
  data() {
    return {
      baseFontSize: 16,
      source1: [
        {
          name: "FW/WAF",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "IDS/IPS",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "蜜罐",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "EDR",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "其他",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
      ],
      weekdetail: {},
      siemData: [],

      times: null,
    };
  },
  mounted() {
    this.startScroll();
    this.getWeekData();
    this.getSystemData();
    this.getSiemData();

    /* 定时器 */
    if (this.times) {
      clearInterval(this.times);
    }
    this.times = setInterval(() => {
      this.getWeekData();
      this.getSystemData();
      this.getSiemData();
    }, 30000);
  },
  methods: {
    startScroll() {
      scrollTimer = setInterval(() => {
        this.source1.forEach((item) => {
          if (item.descArr.length <= 1) return;
          item.desc = null;
          item.showDesc = false; //渐隐消失
          setTimeout(() => {
            item.desc = item.descArr[item.currentIndex];
            item.showDesc = true; //一定时间后重新出现
            item.currentIndex++;
            if (item.currentIndex >= item.descArr.length) {
              item.currentIndex = 0;
            }
          }, 1000);
        });
      }, 3000);
    },
    // 本周提报事件挨着的多条数据
    async getWeekData() {
      const res = await selectDataList();
      res.data.forEach((item) => {
        this.weekdetail[item.type] = item.param_value;
      });
    },
    // SIEM
    async getSiemData() {
      const res = await selectSiemTotalNumList();
      this.siemData = res.data.split("/");
    },
    // 查询fw、waf、ids、ips、蜜罐、edr、其他类型运行情况
    async getSystemData() {
      const res = await selectEngineEquipmentRunStatus();
      this.source1 = await Promise.all(
        res.data.map(async (item) => {
          let deviceType = item.type;
          if (item.type == "其他") {
            deviceType = "other";
          } else if (item.type == "蜜罐") {
            deviceType = "mg";
          }
          let deviceName = [];
          if (item.error === "0") {
            deviceName = await this.getDevice(deviceType);
          }
          return {
            name: item.type.toUpperCase(),
            desc: deviceName[0],
            currentIndex: 0,
            descArr: deviceName,
            num1: item.runTotal,
            num2: item.enableTotal,
            showDesc: deviceName[0] ? true : false,
          };
        })
      );
    },
    // 获取异常设备
    async getDevice(type) {
      const res = await deviceStatus({ type: type.toLowerCase() });
      return res.data.map((item) => item.name);
    },
    // 轮播
    startLoop() {
      tabLoopTimer = setInterval(() => {
        this.getSystemData();
      }, 720000);
    },
  },
  beforeUnmount() {
    clearInterval(tabLoopTimer);
  },
};
</script>

<style scoped lang="scss">
$base-font-size: 16;
%center-panel {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.map-panel {
  height: 800px;
  width: 100%;
  position: relative;
  .wrapper {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    > .nums {
      position: absolute;
      bottom: -25px;
      left: 31px;
    }
  }

  .right-top {
    width: 349px;
    height: 293px;
    background-image: url("@/assets/imgs/screen/infrastructure/lktTopBg.png");
    display: flex;
    justify-content: center;
    position: absolute;
  }

  .left-bottom {
    width: 522px;
    height: 422px;
    background-image: url("@/assets/imgs/screen/infrastructure/lktBotBg.png");
    display: flex;
    justify-content: center;
    position: absolute;
  }
  .left-bottom {
    // 左下部分
    left: 20px;
    bottom: -112px;
  }
  .right-top {
    // 右上部分
    right: 51px;
    top: 17px;
  }
  .nums {
    letter-spacing: 3px;
    line-height: 1.4rem;
    font-size: 14px;
    text-align: center;
    > .num1 {
      color: #fff;
    }
    > .separate {
      color: #3a70b4;
    }
    > .num2 {
      color: #cb9338;
    }
  }
  .center {
    width: 763px;
    height: 522px;
    background-image: url("@/assets/imgs/screen/infrastructure/lktCenterBg.png");
    position: relative;
    top: 95px;
    left: 73px;

    > .line {
      width: 596px;
      height: 309px;
      background-image: url("@/assets/imgs/screen/line.png");
      position: relative;
      top: 157px;
      left: 135px;
    }
    > .notebook {
      width: 110px;
      height: 123px;
      background-image: url("@/assets/imgs/screen/dn.png");
      position: absolute;
      left: 175px;
      top: 98px;
    }
    > .siem,
    > .alarm {
      position: absolute;
      width: 88px;
      height: 90px;
      background-image: url("@/assets/imgs/screen/server.png");
    }
    > .siem {
      left: 164px;
      top: 358px;
    }
    > .alarm {
      left: 780px;
      top: 260px;
    }
    > .main {
      position: absolute;
      width: 150px;
      height: 201px;
      background-image: url("@/assets/imgs/screen/main.png");
      left: 369px;
      top: 175px;
    }
    .main-tip {
      font-size: 18px;
      width: 137px;
      height: 58px;
      color: #fff;
      opacity: 0.8;
      position: absolute;
      background-image: url("@/assets/imgs/screen/infrastructure/main_tip.png");
      text-align: center;
      top: -60px;
      left: -24px;
      line-height: 48px;
    }
    .yyzzClass {
      top: -45px;
    }
    .total-count {
      font-size: 14px;
      text-align: center;
      position: absolute;
      right: 17px;
      bottom: 16px;
      .detail-item {
        margin-top: 14px;
      }
      .title,
      .num {
        display: inline-block;
        line-height: 21px;
      }
      .title {
        width: 133px;
        background: url("@/assets/imgs/screen/detail_title.png") no-repeat;
        color: rgba(255, 255, 255, 0.8);
      }
      .num {
        width: 44px;
        /*background: url("@/assets/imgs/screen/detail_num.png") no-repeat;*/
        background: linear-gradient(0deg, #0093a7, #1fe4ff);
        border-radius: 12px;
        color: #fff;
        margin-left: 4px;
        background-size: 100%;
      }
    }
    .interfaces {
      position: absolute;
      color: #45e3ff;
      font-size: 14px;
      text-align: center;
      // right: 10px;
      // bottom: 160px;
      left: 33px;
      top: 111px;
      .interface-item {
        background: rgba(8, 119, 189, 0.2);
        color: rgba(255, 255, 255, 0.8);
        border: 1px solid #237398;
        border-radius: 11px;
        width: 112px;
        line-height: 21px;
        margin-top: 5px;
        position: relative;
      }
      .succesStatus::after {
        content: "";
        width: 18px;
        height: 18px;
        display: block;
        background-color: #0fa74a;
        border-radius: 50%;
        position: absolute;
        right: -25px;
        top: 3px;
      }
      .errorStatus::after {
        content: "";
        width: 18px;
        height: 18px;
        display: block;
        background-color: red;
        border-radius: 50%;
        position: absolute;
        right: -25px;
        top: 3px;
      }
    }
  }
  .business-system {
    background-image: url("@/assets/imgs/screen/business_system.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    width: 68px;
    position: relative;
    &:not(:first-child) {
      margin-left: 9px;
    }
    > .text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      height: 94px;
      text-align: center;
    }
    > .desc {
      background-image: url("@/assets/imgs/screen/desc.png");
      position: absolute;
      min-width: 123px;
      height: 52px;
      top: -55px;
      left: 50%;
      transform: translateX(-50%);
      color: #dd7e2a;
      text-align: center;
      line-height: 49px;
      font-size: 14px;
      background-size: 100% 100%;
      white-space: nowrap;
      transition: opacity 0.5s;
      animation-name: DESC_ANI;
      animation-duration: 0.5s;
      animation-timing-function: ease-in-out;
      animation-iteration-count: infinite;
      animation-direction: alternate;
      animation-play-state: running;
      .text {
        padding: 0 16px;
      }
    }
  }
  .server-item {
    width: 80px;
    height: 82px;
    background-image: url("@/assets/imgs/screen/server.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    position: relative;
    &:not(:first-child) {
      margin-left: 34px;
    }
    .server-tip {
      position: absolute;
      top: -20px;
      left: 0;
      width: 120px;
      color: #fff;
      opacity: 0.7;
    }
  }
  .time-desc {
    position: absolute;
    color: #d7f9fe;
    font-size: 13px;
    width: 151px;
    bottom: -46px;
    left: -50px;
    line-height: 21px;
    &::before {
      content: "";
      background-color: #dd9b2a;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: block;
      position: absolute;
      left: -20px;
      top: 5px;
    }
  }
}
@keyframes DESC_ANI {
  from {
    top: -55px;
  }
  to {
    top: -60px;
  }
}
</style>
