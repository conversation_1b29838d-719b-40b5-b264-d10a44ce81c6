<template>
  <!-- 运营基础设施架构 - new -->
  <scaleWrapper>
    <starsoPage2 titleText="运营基础设施架构" borderType="page3">
      <div class="infrastructureDiv">
        <!-- 左侧部分  -->
        <div class="infrastructureLeft">
          <starso-title1 class="title">CPU性能监控</starso-title1>
          <echart-line-base :options="cpuOptions" width="420px" height="185px"></echart-line-base>

          <starso-title1 class="title mt20">内存性能监控</starso-title1>
          <echart-line-base :options="memoryOptions" width="420px" height="185px"></echart-line-base>

          <starso-title1 class="title mt20">存储资源用量监控</starso-title1>
          <div class="infLeftBotCon">
            <div class="infLbLeft">
              <div class="infLbLeftTit">存储资源剩余</div>
              <div class="infLbLeftNum">
                {{ storageData.syNum }}
                <span>G</span>
              </div>
            </div>
            <div class="infLbRight">
              <starso-title4 typeName="资源占用"></starso-title4>
              <!--              <circularProcess :count="storageData.zyzyNum"></circularProcess>-->
              <CharGauge :size="100" :pieData="storageData.zyzyNum" />
            </div>
          </div>
          <echart-line-base :options="storageOptions" width="420px" height="185px"></echart-line-base>
        </div>

        <!-- 中间部分  -->
        <div class="infrastructureCenter">
          <MainLayout />
        </div>

        <!-- 右侧部分  -->
        <div class="infrastructureRight">
          <!-- 右上 -->
          <div class="rightTop">
            <div class="alarmDiv">
              <p class="number">{{ storageData.dayCount }}</p>
              <p class="title">近24小时告警量</p>
            </div>
            <div class="engineDiv">
              <p class="number">{{ storageData.yqStatus }}</p>
              <p class="title">引擎运行情况</p>
              <p class="title1">/安全设备</p>
            </div>
          </div>

          <div class="rightBot">
            <starso-title1 class="title">日志源状态</starso-title1>
            <div class="rightBotTop">
              <CountCom title="30天日均安全日志数量" :count="storageData.monthCount" />
              <CountCom title="近24小时日志总量" :count="storageData.jDayCount" />
            </div>

            <starsoScrollTable1 :columns="tableColumns" :list="tableData" :listHeight="190" :stripe="true" :limitScrollNum="5"></starsoScrollTable1>
            <starso-title1 class="title mt20">日志入库状态监控</starso-title1>
            <echart-line-base :options="logWareOptions" width="420px" height="180px"></echart-line-base>
          </div>
        </div>
      </div>
    </starsoPage2>
  </scaleWrapper>
</template>

<script>
export default {
  name: "index",
};
</script>

<script setup>
import { ref } from "vue";
import scaleWrapper from "starso/scaleWrapper";
import starsoPage2 from "starso/starsoPage2";
import starsoTitle1 from "starso/starsoTitle1";
import echartLineBase from "starso/echartLineBase";
import starsoScrollTable1 from "starso/starsoScrollTable1";
import circularProcess from "starso/circularProcess";
import starsoTitle4 from "starso/starsoTitle4";
import CountCom from "../components/countCom.vue";
import MainLayout from "./components/main.vue";
import CharGauge from "../components/charGauge.vue";

import {
  getSelectCpuPerformanceList,
  getSelectMemoryPerformanceList,
  getSelectStorageResourcesList,
  getSelectTwentyFourAlertNum,
  getSelectEngineOperation,
  getSelectThirtyDayRiJunLogNum,
  getSelectTwentyFourLogNum,
  getSelectLogWareHousingStatusList,
  getSelectLogWarehousingStatusMonitoringList,
} from "@/api/screen/new/infrastructure";

let leftLineOptions = ref({
  xAxis: { type: "category", data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"] },
  yAxis: { type: "value" },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [{ type: "line", itemStyle: { color: "#6BC1D0" }, data: [820, 932, 901, 934, 1290, 1330, 1320] }],
});

/* CPU性能监控 */
let cpuOptions = ref({
  legend: {
    icon: "rect",
    right: "0",
    top: "-5px",
    textStyle: {
      color: "#fff",
    },
    data: ["CPU"],
  },
  grid: {
    top: "15%",
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    /*data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']*/
    data: [],
  },
  yAxis: { type: "value" },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [
    {
      name: "CPU",
      type: "line",
      itemStyle: { color: "#6BC1D0" },
      /*data: [820, 932, 901, 934, 1290, 1330, 1320],*/
      data: [],
    },
  ],
});
const getSelectCpuPerformanceListFun = () => {
  getSelectCpuPerformanceList().then((res) => {
    let xAxisData = [];
    let yAxisData = [];
    res.data.forEach((item) => {
      xAxisData.push(item.time);
      yAxisData.push(item.status);
    });
    cpuOptions.value.xAxis.data = xAxisData;
    cpuOptions.value.series[0].data = yAxisData;
  });
};

/* 内存性能监控 */
let memoryOptions = ref({
  legend: {
    icon: "rect",
    right: "0",
    top: "-5px",
    textStyle: {
      color: "#fff",
    },
    data: ["内存"],
  },
  grid: {
    top: "15%",
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    /*data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']*/
    data: [],
  },
  yAxis: { type: "value" },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [
    {
      name: "内存",
      type: "line",
      itemStyle: { color: "#6BC1D0" },
      /*data: [820, 932, 901, 934, 1290, 1330, 1320],*/
      data: [],
    },
  ],
});
const getSelectMemoryPerformanceListFun = () => {
  getSelectMemoryPerformanceList().then((res) => {
    let xAxisData = [];
    let yAxisData = [];
    res.data.forEach((item) => {
      xAxisData.push(item.time);
      yAxisData.push(item.status);
    });
    memoryOptions.value.xAxis.data = xAxisData;
    memoryOptions.value.series[0].data = yAxisData;
  });
};

let storageData = ref({
  syNum: 0,
  zyzyNum: 0,
  dayCount: "0",
  yqStatus: "0/0",
  monthCount: 0,
  jDayCount: 0,
});

/* 存储资源用量监控 */
let storageOptions = ref({
  legend: {
    icon: "rect",
    right: "0",
    top: "-5px",
    textStyle: {
      color: "#fff",
    },
    data: ["存储资源"],
  },
  grid: {
    top: "15%",
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    /*data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']*/
    data: [],
  },
  yAxis: { type: "value" },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [
    {
      name: "存储资源",
      type: "line",
      itemStyle: { color: "#6BC1D0" },
      /*data: [820, 932, 901, 934, 1290, 1330, 1320],*/
      data: [],
    },
  ],
});
const getSelectStorageResourcesListFun = () => {
  getSelectStorageResourcesList().then((res) => {
    let xAxisData = [];
    let yAxisData = [];
    res.data.forEach((item, index) => {
      if (index === 0) {
        storageData.value.syNum = item.a;
        storageData.value.zyzyNum = parseInt(item.b);
      }
      xAxisData.push(item.time);
      yAxisData.push(item.b);
    });
    storageOptions.value.xAxis.data = xAxisData;
    storageOptions.value.series[0].data = yAxisData;
  });
};

/* 24小时告警量 */
const getSelectTwentyFourAlertNumFun = () => {
  getSelectTwentyFourAlertNum().then((res) => {
    storageData.value.dayCount = res.data;
  });
};

/* 安全设备引擎/引擎运行情况 */
const getSelectEngineOperationFun = () => {
  getSelectEngineOperation().then((res) => {
    storageData.value.yqStatus = res.data;
  });
};

/* 30天日均安全日志数量 */
const getSelectThirtyDayRiJunLogNumFun = () => {
  getSelectThirtyDayRiJunLogNum().then((res) => {
    storageData.value.monthCount = res.data;
  });
};

/* 近24小时日志总量 */
const getSelectTwentyFourLogNumFun = () => {
  getSelectTwentyFourLogNum().then((res) => {
    storageData.value.jDayCount = res.data;
  });
};

/* 日志源状态  表格 */
let tableColumns = ref([
  {
    label: "设备地址",
    prop: "cdev_ip",
    width: "120",
  },
  {
    label: "设备类型",
    prop: "cdev_type",
    width: "90",
  },
  {
    label: "近24h数量",
    prop: "log_num_twenty_four",
    width: "80",
  },
  {
    label: "上24h环比",
    prop: "ringRatio",
    width: "80",
  },
]);
let tableData = ref([]);
const getSelectLogWareHousingStatusListFun = () => {
  getSelectLogWareHousingStatusList().then((res) => {
    tableData.value = res.data;
  });
};

/* 日志入库状态监控 */
let logWareOptions = ref({
  legend: {
    icon: "rect",
    right: "0",
    top: "-5px",
    textStyle: {
      color: "#fff",
    },
    data: ["入库数据量"],
  },
  grid: {
    top: "15%",
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    /*data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']*/
    data: [],
  },
  yAxis: { type: "value" },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [
    {
      name: "入库数据量",
      type: "line",
      itemStyle: { color: "#6BC1D0" },
      /*data: [820, 932, 901, 934, 1290, 1330, 1320],*/
      data: [],
    },
  ],
});
const getSelectLogWarehousingStatusMonitoringListFun = () => {
  getSelectLogWarehousingStatusMonitoringList().then((res) => {
    let xAxisData = [];
    let yAxisData = [];
    res.data.forEach((item, index) => {
      xAxisData.push(item.h);
      yAxisData.push(item.amount);
    });
    logWareOptions.value.xAxis.data = xAxisData;
    logWareOptions.value.series[0].data = yAxisData;
  });
};

/* 公共获取数据 */
const getList = () => {
  getSelectCpuPerformanceListFun();
  getSelectMemoryPerformanceListFun();
  getSelectStorageResourcesListFun();
  getSelectTwentyFourAlertNumFun();
  getSelectEngineOperationFun();
  getSelectThirtyDayRiJunLogNumFun();
  getSelectTwentyFourLogNumFun();
  getSelectLogWareHousingStatusListFun();
  getSelectLogWarehousingStatusMonitoringListFun();
};
getList();

/* 定时器 */
let times = ref(null);
const getDataTimer = (time = 30000) => {
  if (times.value) {
    clearInterval(times.value);
  }
  times.value = setInterval(() => {
    getList();
  }, time);
};
getDataTimer();
</script>

<style scoped lang="scss">
@import "@/assets/style/newScreen.css";
::v-deep .scale-content {
  background: url(@/assets/imgs/screen/vulnerability/vulnBg.jpg) no-repeat;
}
.infrastructureDiv {
  .mt20 {
    margin-top: 20px;
  }
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 463px auto 463px;
  padding: 22px 25px;
  color: #fff;
  margin-top: 20px;
  .title {
    margin-bottom: 20px;
  }

  /* 左侧部分 */
  .infrastructureLeft {
    height: 926px;
    background-image: url("@/assets/imgs/screen/infrastructure/lefttop.png");
    background-size: 100% 100%;
    padding: 22px 25px;

    .infLeftBotCon {
      width: 420px;
      height: 128px;
      margin-top: 20px;
      margin-bottom: 13px;
      background-image: url("@/assets/imgs/screen/infrastructure/infLeftBotBg.png");
      background-size: 100% 100%;
      display: grid;
      grid-template-columns: 185px auto;
      .infLbLeft {
        text-align: center;
        padding-left: 5px;
        .infLbLeftTit {
          width: 170px;
          height: 42px;
          line-height: 42px;
          display: inline-block;
          background-image: url("@/assets/imgs/screen/infrastructure/inflbTit.png");
          background-size: 100% 100%;
          font-size: 14px;
          color: rgba(69, 223, 245, 0.7);
          margin-top: 23px;
        }
        .infLbLeftNum {
          font-family: yejing;
          font-size: 30px;
          color: #44d9f1;
          span {
            font-size: 18px;
          }
        }
      }
      .infLbRight {
        display: flex;
        align-items: center;
        .xfl {
          margin-top: 0;
          margin-left: 15px;
          margin-right: 10px;
        }
      }
    }
  }

  .infrastructureCenter {
    padding-top: 10px;
    padding-left: 25px;
    /*background-image: url('@/assets/imgs/screen/infrastructure/ceshi.png');
    background-size: 100% 100%;
    background-color: rgba(0, 0, 0, 0.5);*/
  }

  /* 右侧部分 */
  .infrastructureRight {
    height: 926px;
    .rightTop {
      display: flex;
      justify-content: space-between;
      text-align: center;
      /* 近24小时 告警量 */
      .alarmDiv {
        width: 195px;
        height: 195px;
        background-image: url("@/assets/imgs/screen/infrastructure/rightTopLn.png");
        background-size: 100% 100%;
        position: relative;
        &:after {
          content: "";
          display: inline-block;
          width: 157px;
          height: 157px;
          background-image: url("@/assets/imgs/screen/infrastructure/rightTopLw.png");
          background-size: 100% 100%;
          position: absolute;
          top: 18px;
          left: 14px;
        }
        .number {
          position: relative;
          top: 65px;
        }
        .title {
          position: relative;
          top: 65px;
        }
      }
      /* 引擎运行情况 /安全设备 */
      .engineDiv {
        width: 277px;
        height: 276px;
        background-image: url("@/assets/imgs/screen/infrastructure/rightTopRw.png");
        background-size: 100% 100%;
        position: relative;
        top: -37px;
        color: rgba(255, 255, 255, 0.7);
        &:after {
          content: "";
          display: inline-block;
          width: 151px;
          height: 151px;
          background-image: url("@/assets/imgs/screen/infrastructure/rightTopRn.png");
          background-size: 100% 100%;
          position: absolute;
          top: 62px;
          left: 61px;
        }
        .number {
          position: relative;
          top: 105px;
        }
        .title {
          position: relative;
          top: 105px;
        }
        .title1 {
          position: relative;
          top: 85px;
        }
      }

      .number {
        font-family: yejing;
        font-size: 28px;
        color: #4cf8ff;
      }
      .title {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
      }
    }
    .rightBot {
      padding: 27px 22px;
      height: 693px;
      background-image: url("@/assets/imgs/screen/infrastructure/lefttop.png");
      background-size: 100% 100%;
      position: relative;
      top: -42px;

      .rightBotTop {
        margin-top: 28px;
        margin-bottom: 22px;
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>
