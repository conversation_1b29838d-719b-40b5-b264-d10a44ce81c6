<!---->
<template>
  <scaleWrapper>
    <starsoPage2 class="page-m-wrapper" titleText="安全运营中心" borderType="page3">
      <!-- 内容 -->
      <div class="content-wrapper">
        <starso-border1 class="left-wrapper">
          <!-- 切换按钮 -->
          <ToggleBtn showType="M" @handToggles="handToggles" />
          <!-- <el-select v-model="viewAlertNum" class="viewAlertNum-select" @change="getAlarmRankingToday">
            <el-option v-for="item in viewAlertNumList" :key="item" :value="item" :label="'top' + item"></el-option>
          </el-select> -->
          <starso-title1 class="title">本日告警排名1</starso-title1>
          <starso-rank1 :data="todayAlarmData" :listHeight="282"></starso-rank1>
          <starso-title1 class="title2">告警监测</starso-title1>

          <starso-num1 class="num1" :num="alarmQuantityData"></starso-num1>
          <echart-line-base :options="leftLineOptions" width="322px" height="150px"></echart-line-base>
          <starso-border4 class="scroll-table-box">
            <starsoScrollTable1
              :columns="leftTableColumns"
              :list="alarmTableToBeAnalyzedData"
              :listHeight="130"
              :stripe="true"
              :limitScrollNum="2"
            ></starsoScrollTable1>
          </starso-border4>
        </starso-border1>
        <div class="center-wrapper">
          <projectionMap
            class="projection-map-box"
            :scale="0.48"
            :updateStatus="mapAttackedData.updateStatus"
            :targetPoints="mapAttackedData.targetPoints"
            :attackPoints="mapAttackedData.attackPoints"
            :lines="mapAttackedData.lines"
            :targetNormalPoints="mapAttackedData.targetNormalPoints"
          ></projectionMap>
           <starsoSwiper1 class="swiper-box" :rotationData="customerEventsNewestData"></starsoSwiper1>

          <custom-work-order
            class="work-orders"
            :leftCount="eventQuantityData"
            :leftRate="eventHandlingRateData"
            :list="eventsNewestData"
          ></custom-work-order>
          <starsoBorder5 class="line-chart-box">
            <p class="title">24H攻击流量趋势图</p>
            <echart-line-base :options="centerLineOptions" width="420px" height="135px"></echart-line-base>
          </starsoBorder5>
          <customLightCircle class="light-circle" :scale="1.5">安全</customLightCircle>
          <p class="circle-text">网络安全指数</p>
        </div>
        <div class="right-wrapper">
          <starso-border1 class="right-top-box">
            <div class="attack-box">
              <customRadar class="custom-radar"></customRadar>
              <div class="text-box">
                <p>攻击次数</p>
                <p class="yejing">{{ numToStr(attackNewData.num0) }}</p>
              </div>
              <div class="right-box">
                <customBox1 :type="1" title="攻击者" :count="attackNewData.num1" class="yejing"></customBox1>
                <customBox1 :type="2" title="受攻击服务器" :count="attackNewData.num2" class="yejing"></customBox1>
              </div>
            </div>
          </starso-border1>
          <starso-border1 class="right-bottom-box">
            <section>
              <starso-title3>攻击类型TOP5</starso-title3>
              <starso-rank1 :oneColor="true" :data="getCeventTypeTopData"></starso-rank1>
            </section>
            <section>
              <starso-title3>受影响业务系统TOP5</starso-title3>
              <starso-rank1 :oneColor="true" :indexType="2" :data="rankingOfAffectedData"></starso-rank1>
            </section>
          </starso-border1>
        </div>
      </div>
    </starsoPage2>
  </scaleWrapper>
</template>

<script setup>
import getData from "./getData";
import ToggleBtn from "./components/toggleBtn.vue";
import numToStr from "@/utils/numToStr";
import scaleWrapper from "starso/scaleWrapper";
import starsoBorder1 from "starso/starsoBorder1";
import starsoTitle1 from "starso/starsoTitle1";
import starsoTitle3 from "starso/starsoTitle3";

import starsoRank1 from "starso/starsoRank1";
import starsoNum1 from "starso/starsoNum1";
import echartLineBase from "starso/echartLineBase";
import starsoBorder4 from "starso/starsoBorder4";
import starsoBorder5 from "starso/starsoBorder5";
import starsoScrollTable1 from "starso/starsoScrollTable1";
import starsoScrollTable2 from "starso/starsoScrollTable2";
import starsoSwiper1 from "starso/starsoSwiper1";
import customWorkOrder from "starso/customWorkOrder";
import customLightCircle from "starso/customLightCircle";
import customRadar from "starso/customRadar";
import customBox1 from "starso/customBox1";
import starsoPage2 from "starso/starsoPage2";
import projectionMap from "starso/projectionMap";

let {
  mapAttackedData,
  todayAlarmData,
  alarmQuantityData,
  larmMonitoringTrendChartData,
  customerEventsNewestData,
  alarmTableToBeAnalyzedData,
  eventQuantityData,
  eventHandlingRateData,
  eventsNewestData,
  sttackedEsCountData,
  rankingOfAffectedData,
  getCeventTypeTopData,
  attackNewData,
} = getData();

/* 切换 按钮 */
const emits = defineEmits(["handToggle"]);
const handToggles = (data) => {
  emits("handToggle", data);
};

let leftLineOptions = ref({
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: { type: "category" },
  yAxis: {},
  dataset: {
    /* dimensions: ["name", "数量"],*/
    source: [],
  },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [{ name: "数量", type: "line", itemStyle: { color: "#6BC1D0" } }],
});

watch(
  () => larmMonitoringTrendChartData.value,
  (val) => {
    leftLineOptions.value.dataset.source = val;
  }
);

//大小屏有差别
let leftTableColumns = ref([
  {
    label: "告警名称",
    prop: "title",
    width: "80",
  },
  {
    label: "目的IP",
    prop: "cdstip",
    width: "80",
  },
  {
    label: "时间",
    prop: "get_time",
    width: "80",
  },
]);

let centerLineOptions = ref({
  color: ["#E25039"],
  xAxis: { type: "category" },
  yAxis: {
    axisLabel: {
      formatter: (value) => {
        return numToStr(value);
      },
    },
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  dataset: {
    /*dimensions: ["name", "value"],*/
    source: [],
  },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [{ name: "数量", type: "line" }],
});
watch(
  () => sttackedEsCountData.value,
  (val) => {
    centerLineOptions.value.dataset.source = val;
  },
  {
    immediate: true,
  }
);
</script>

<style scoped lang="scss">
@import "@/assets/style/newScreen.css";
$color: #0fbed2;
:deep(.starso-rank1) {
  .bar-box {
    margin-top: 5px;
  }
}
.yejing {
  font-family: "yejing";
}
.page-m-wrapper {
  background-image: url(@/assets/imgs/screen/centerBg.jpg);
  color: #fff;
  font-size: 12px;
}

.content-wrapper {
  width: 100%;
  height: 100%;
  // padding: 85px 80px 57px;
  display: grid;
  grid-template-columns: 372px auto 371px;
  padding: 22px 20px;
}
.left-wrapper {
  padding: 14px 24px;
  position: relative;
  .viewAlertNum-select {
    position: absolute;
    width: 90px;
    left: 270px;

    // background: ;
    :deep(.el-input__inner) {
      background: transparent;
      color: #fff;
      border-color: #333;
    }
  }
  :deep(.starso-rank1) {
    // font-size: 14px;
    .right_li_bottom {
      height: 49px;
    }
    .wrapper {
      background: rgba(39, 206, 225, 0.2);
    }
    div[class^="attackType"] {
      // margin-right: 18px;
    }
  }
  .title {
    margin-bottom: 30px;
  }
  .title2 {
    margin-top: 43px;
  }
  .num1 {
    font-size: 28px;
    width: 322px;
    height: 58px;
    margin-top: 26px;
    margin-bottom: 30px;
  }
  .scroll-table-box {
    padding: 25px 20px 20px;
    margin-top: 30px;
  }
  :deep(.starso-scroll-table1 .data-list li) {
    height: 40px;
    line-height: 40px;
  }
}
.center-wrapper {
  position: relative;
  .projection-map-box {
    position: absolute;
    left: 10px;
    top: 70px;
    // transform: scale(0.48);
    transform-origin: left top;
  }
  .swiper-box {
    position: absolute;
    bottom: 220px;
    left: 0;
    height: 303px;
    left: 20px;
  }

  .work-orders,
  .line-chart-box {
    bottom: 0px;
  }
  :deep(.custom-work-orders) {
    width: 580px;
  }
  .work-orders {
    position: absolute;
    left: 500px;
  }
  .line-chart-box {
    width: 463px;
    height: 200px;
    position: absolute;
    left: 20px;

    padding: 18px 13px 0;

    .title {
      color: #fff;
      margin-bottom: 5px;
    }
  }
  .light-circle {
    position: absolute;
    color: #fff;
    bottom: 280px;
    left: 490px;
    transform-origin: center bottom;
  }
  .circle-text {
    font-size: 17px;
    position: absolute;
    bottom: 235px;
    left: 490px;
  }
}
.right-wrapper {
  .right-top-box {
    height: 289px;
    padding: 23px 38px;

    .detail-content {
      display: flex;
      padding: 30px;
      .yejing {
        color: $color;
        font-size: 22px;
      }
      .left {
        width: 140px;
        margin-right: 30px;
        border-right: 1px solid rgba(52, 152, 169, 0.2);
        margin-top: 20px;
        margin-bottom: 10px;
      }
    }
  }
  .attack-box {
    height: 110px;
    display: grid;
    grid-template-columns: 93px 180px;
    grid-template-rows: 93px 113px;
    gap: 20px;
    font-size: 14px;
    .yejing {
      color: $color;
    }

    :deep(.custom-radar) {
      transform: scale(0.58);
      transform-origin: left top;
    }
    .text-box {
      padding-left: 25px;
      display: flex;
      align-items: center;
      .yejing {
        font-size: 24px;
        margin-left: 10px;
      }
    }
    .right-box {
      display: grid;
      grid-template-rows: 56px 56px;
      gap: 13px;
      :deep(.custom-box1) {
        height: 56px;
        width: 284px;
        .content .text {
          width: 50%;
        }
      }
    }
  }
  .right-bottom-box {
    width: 372px;
    height: 643px;
    margin-top: 19px;
    display: grid;
    grid-template-rows: calc(50% - 10px) 1fr;
    padding: 0 35px;
    gap: 20px;
    .list-box {
      width: 303px;
    }
  }
}
</style>
