<!---->
<template>
  <scaleWrapper :width="3840">
    <starsoPage1>
      <img src="@/assets/imgs/screen/title1.png" class="page-title" />
      <!-- 内容 -->
      <div class="content-wrapper">
        <starso-border1 class="left-wrapper">
          <!-- 切换按钮 -->
          <ToggleBtn @handToggles="handToggles" />
          <starso-title1 class="title">本日告警排名</starso-title1>
          <starso-rank1 :data="todayAlarmData"></starso-rank1>
          <starso-title1 class="title2">告警监测</starso-title1>
          <starso-num1 class="num1" :num="alarmQuantityData"></starso-num1>
          <echart-line-base :options="leftLineOptions" width="585px" height="150px"></echart-line-base>
          <starso-border4 class="scroll-table-box">
            <starsoScrollTable1
              :columns="leftTableColumns"
              :list="alarmTableToBeAnalyzedData"
              :listHeight="80"
              :stripe="true"
              :limitScrollNum="2"
            ></starsoScrollTable1>
          </starso-border4>
        </starso-border1>
        <div class="center-wrapper">
          <projectionMap
            class="projection-map-box"
            :updateStatus="mapAttackedData.updateStatus"
            :targetPoints="mapAttackedData.targetPoints"
            :attackPoints="mapAttackedData.attackPoints"
            :lines="mapAttackedData.lines"
            :targetNormalPoints="mapAttackedData.targetNormalPoints"
          ></projectionMap>
          <starsoSwiper1 class="swiper-box" :rotationData="customerEventsNewestData"></starsoSwiper1>
          <custom-work-order
            class="work-orders"
            :leftCount="eventQuantityData"
            :leftRate="eventHandlingRateData"
            :list="eventsNewestData"
          ></custom-work-order>
          <starsoBorder5 class="line-chart-box">
            <p class="title">24H攻击流量趋势图</p>
            <echart-line-base :options="centerLineOptions" width="380px" height="135px"></echart-line-base>
          </starsoBorder5>
          <customLightCircle class="light-circle" :scale="1.5">安全</customLightCircle>
        </div>
        <div class="right-wrapper">
          <starso-border1 class="right-top-box">
            <div class="attack-box">
              <customRadar class="custom-radar"></customRadar>
              <div class="text-box">
                <p>攻击次数</p>
                <p class="yejing">{{ numToStr(attackNewData.num0) }}</p>
              </div>
              <div class="right-box">
                <customBox1 :type="1" title="攻击者" :count="attackNewData.num1"></customBox1>
                <customBox1 :type="2" title="受攻击服务器" :count="attackNewData.num2"></customBox1>
              </div>
            </div>

            <starsoScrollTable2 :showIndex="true" :columns="columns" :list="attackLogList" @change="changeAttackLine">
              <template #default="{ item }">
                <div class="detail-content">
                  <div class="left">
                    攻击次数
                    <p class="yejing">{{ item.amount }}</p>
                  </div>
                  <echart-line-base :options="attackLineOptions" width="460px" height="130px"></echart-line-base>
                </div>
              </template>
            </starsoScrollTable2>
          </starso-border1>

          <starso-border1 class="right-bottom-box">
            <section>
              <starso-title2>攻击类型TOP5</starso-title2>
              <starso-rank1 :oneColor="true" :data="getCeventTypeTopData"></starso-rank1>
            </section>
            <section>
              <starso-title2>受影响业务系统TOP5</starso-title2>
              <starso-rank1 :oneColor="true" :indexType="2" :data="rankingOfAffectedData"></starso-rank1>
            </section>
          </starso-border1>
        </div>
      </div>
    </starsoPage1>
  </scaleWrapper>
</template>

<script setup>
import getData from "./getData";
import ToggleBtn from "./components/toggleBtn.vue";
import numToStr from "@/utils/numToStr";
import starsoBorder1 from "starso/starsoBorder1";
import starsoTitle1 from "starso/starsoTitle1";
import starsoTitle2 from "starso/starsoTitle2";

import starsoRank1 from "starso/starsoRank1";
import starsoNum1 from "starso/starsoNum1";
import echartLineBase from "starso/echartLineBase";
import starsoBorder4 from "starso/starsoBorder4";
import starsoBorder5 from "starso/starsoBorder5";
import starsoScrollTable1 from "starso/starsoScrollTable1";
import starsoScrollTable2 from "starso/starsoScrollTable2";
import starsoSwiper1 from "starso/starsoSwiper1";
import customWorkOrder from "starso/customWorkOrder";
import customLightCircle from "starso/customLightCircle";
import customRadar from "starso/customRadar";
import customBox1 from "starso/customBox1";
import starsoPage1 from "starso/starsoPage1";
import projectionMap from "starso/projectionMap";
import scaleWrapper from "starso/scaleWrapper";
import { getSeimEsNameTop, getSeimTimeCount } from "@/api/screen/new/screen";
import { onMounted } from "vue";
let {
  mapAttackedData,
  todayAlarmData,
  alarmQuantityData,
  larmMonitoringTrendChartData,
  customerEventsNewestData,
  alarmTableToBeAnalyzedData,
  eventQuantityData,
  eventHandlingRateData,
  eventsNewestData,
  sttackedEsCountData,
  rankingOfAffectedData,
  getCeventTypeTopData,
  attackNewData,
} = getData();

/* 切换 按钮 */
const emits = defineEmits(["handToggle"]);
const handToggles = (data) => {
  emits("handToggle", data);
};

let leftLineOptions = ref({
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: { type: "category" },
  yAxis: {},
  dataset: {
    dimensions: ["name", "value"],
    source: [
      { name: "8-1", value: 10 },
      { name: "8-2", value: 20 },
      { name: "8-3", value: 50 },
      { name: "8-4", value: 70 },
      { name: "8-5", value: 40 },
    ],
  },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [{ name: "数量", type: "line", itemStyle: { color: "#b8322a" } }],
});
watch(
  () => larmMonitoringTrendChartData.value,
  (val) => {
    leftLineOptions.value.dataset.source = val;
  }
);

let leftTableColumns = ref([
  {
    label: "告警名称",
    prop: "title",
    width: "200",
  },
  {
    label: "目的IP",
    prop: "cdstip",
    width: "150",
  },
  {
    label: "时间",
    prop: "get_time",
    width: "160",
  },
]);

let centerLineOptions = ref({
  color: ["#b8322a"],
  xAxis: { type: "category" },
  yAxis: {},
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  dataset: {
    /*dimensions: ["name", "value"],*/
    source: [
      { name: "8-1", value: 10 },
      { name: "8-2", value: 20 },
      { name: "8-3", value: 50 },
      { name: "8-4", value: 70 },
      { name: "8-5", value: 40 },
    ],
  },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [{ name: "数量", type: "line" }],
});
watch(
  () => sttackedEsCountData.value,
  (val) => {
    centerLineOptions.value.dataset.source = val;
  },
  {
    immediate: true,
  }
);
onMounted(() => {
  centerLineOptions.value.color = ["#28ACC1"];
});
// 近30天日志类型列表（攻击日志列表）
let attackLogList = ref([]);
let columns = [
  {
    label: "日志类型",
    prop: "content",
    width: "520",
  },
  {
    label: "数量TOP5（30天）",
    prop: "amount",
    width: "120",
  },
];
// 攻击内容
let attackLineOptions = ref({
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  color: ["#28ACC1"],
  xAxis: { type: "category" },
  yAxis: {},
  dataset: {
    /*dimensions: ["name", "value"],*/
    source: [
      { name: "8-1", value: 10 },
      { name: "8-2", value: 20 },
      { name: "8-3", value: 50 },
      { name: "8-4", value: 70 },
      { name: "8-5", value: 40 },
    ],
  },
  tooltip: {
    trigger: "axis",
    show: true,
  },
  series: [{ name: "数量", type: "line" }],
});
function getGetSeimEsNameTop() {
  getSeimEsNameTop().then(({ data }) => {
    attackLogList.value = data;
    getGetSeimTimeCount(data[0].content);
  });
}
//根据天数查询日志数量（攻击曲线图）
function getGetSeimTimeCount(content) {
  getSeimTimeCount({ content: content }).then(({ data }) => {
    attackLineOptions.value.dataset.source = data.map((item) => {
      return {
        name: item.time,
        value: item.count,
      };
    });
  });
}
onMounted(() => {
  getGetSeimEsNameTop();
});
// 监听变化
function changeAttackLine(data) {
  getGetSeimTimeCount(data.content);
}
</script>

<style scoped lang="scss">
::v-deep .scale-content {
  width: 3840px;
  height: 1080px;
}
$color: #0fbed2;
.yejing {
  font-family: "yejing";
}
:deep(.starso-rank1 .right_li_bottom:not(:last-child)) {
  margin-bottom: 16px;
}
.page-title {
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
}

.content-wrapper {
  width: 100%;
  height: 100%;
  padding: 85px 80px 57px;
  display: grid;
  grid-template-columns: 643px auto 756px;
}
.left-wrapper {
  position: relative;
  padding: 14px 24px;
  :deep(.starso-rank1) {
    font-size: 14px;
    .right_li_bottom {
      height: 49px;
    }
    .wrapper {
      background: rgba(39, 206, 225, 0.2);
    }
    div[class^="attackType"] {
      margin-right: 18px;
    }
  }
  .title {
    margin-bottom: 30px;
  }
  .title2 {
    margin-top: 43px;
  }
  .num1 {
    font-size: 28px;
    width: 586px;
    height: 58px;
    margin-top: 26px;
    margin-bottom: 30px;
  }
  .scroll-table-box {
    padding: 25px 20px 20px;
    margin-top: 30px;
  }
  :deep(.starso-scroll-table1 .data-list li) {
    height: 40px;
    line-height: 40px;
  }
  :deep(.starso-rank1 .right_li_bottom > div .wrapper) {
    padding: 0 18px;
  }
}
.center-wrapper {
  position: relative;
  .projection-map-box {
    position: absolute;
    left: 10px;
    top: -20px;
    // transform: scale(0.9);
    transform-origin: left top;
    :deep(.china-panel-border) {
      display: none;
    }
  }

  .swiper-box {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 303px;
    left: 50px;
  }
  .work-orders,
  .line-chart-box,
  .light-circle {
    bottom: 20px;
  }
  .work-orders {
    position: absolute;

    left: 565px;
  }
  .line-chart-box {
    width: 397px;
    height: 192px;
    position: absolute;
    left: 1190px;

    padding: 18px 13px 0;

    .title {
      color: #fff;
      margin-bottom: 5px;
    }
  }
  .light-circle {
    position: absolute;

    color: #fff;
    right: 130px;
    transform-origin: center bottom;
  }
}
.right-wrapper {
  .right-top-box {
    height: 571px;
    padding: 23px 38px;
    :deep(.starso-scroll-table2) {
      .item-li {
        .span:nth-child(3) {
          color: $color;
        }
      }
      .item-li.active {
        .detail-box {
          height: 173px;
        }
      }
    }
    .detail-content {
      display: flex;
      padding: 30px;
      .yejing {
        color: $color;
        font-size: 22px;
      }
      .left {
        width: 140px;
        margin-right: 30px;
        border-right: 1px solid rgba(52, 152, 169, 0.2);
        margin-top: 20px;
        margin-bottom: 10px;
      }
    }
  }
  .attack-box {
    height: 110px;
    display: grid;
    grid-template-columns: 93px 180px auto;
    .yejing {
      color: $color;
    }

    :deep(.custom-radar) {
      transform: scale(0.58);
      transform-origin: left top;
    }
    .text-box {
      padding-top: 26px;
      padding-left: 25px;
      display: flex;
      flex-direction: column;
      .yejing {
        margin-top: 4px;
        font-size: 20px;
      }
    }
    .right-box {
      display: grid;
      grid-template-rows: 41px 41px;
      gap: 12px;
    }
  }
  .right-bottom-box {
    width: 756px;
    height: 348px;
    margin-top: 19px;
    display: grid;
    grid-template-columns: calc(50% - 10px) 1fr;
    padding: 20px 35px;
    gap: 20px;
    :deep(.starso-title-2) {
      margin-bottom: 18px;
    }
  }
}
</style>
