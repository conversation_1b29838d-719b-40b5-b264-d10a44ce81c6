import {
  selectAlertByTitleCount,
  selectNearlyThirtyDaysAlert,
  selectAlertTrend,
  selectAnalyzedAlert,
  selectNearlyTenAlert,
  selectNearlyThirtyDaysEvent,
  selectEventHandlingRate,
  selectEventHandDetail,
  getAttackedEsCount,
  getSeimEsCount,
  getThreatOperationMapAttacked,
  getCdstIpCountTop,
  getCeventNameCountTop,
  getSeimEsNameTop,
} from "@/api/screen/new/screen";
import numToStr from "@/utils/numToStr";

export default function getData() {
  // getViewAlertNumFn();

  // //获取下拉列表
  // let viewAlertNum = ref("10");
  // let viewAlertNumList = ref([]);
  // function getViewAlertNumFn() {
  //   getViewAlertNum().then(({ data }) => {
  //     viewAlertNumList.value = data;
  //   });
  // }
  init();

  setInterval(() => {
    init();
  }, 1000 * 15);

  //本日告警排名
  let todayAlarmData = ref([]);
  function getAlarmRankingToday() {
    selectAlertByTitleCount().then(({ data }) => {
      // console.log(data, "本日告警排名");
      todayAlarmData.value = data.map((item) => {
        return {
          ...item,
          name: item.title,
        };
      });
    });
  }

  //近30天告警
  let alarmQuantityData = ref(0);
  function getAlarmQuantity() {
    selectNearlyThirtyDaysAlert().then(({ data }) => {
      // console.log(data, "近30天告警");
      alarmQuantityData.value = data.alertNum;
    });
  }
  //告警检测折线图
  let larmMonitoringTrendChartData = ref([]);
  function getLarmMonitoringTrendChart() {
    selectAlertTrend().then(({ data }) => {
      // console.log(data, "告警检测折线图");
      larmMonitoringTrendChartData.value = data.map((item) => {
        return {
          name: item.date_hour,
          value: item.count,
        };
      });
    });
  }
  //告警表格
  let alarmTableToBeAnalyzedData = ref([]);
  function getAlarmTableToBeAnalyzed() {
    selectAnalyzedAlert().then(({ data }) => {
      alarmTableToBeAnalyzedData.value = data;
    });
  }

  //地图数据
  let mapAttackedData = ref({ updateStatus: false, targetPoints: [], attackPoints: [], lines: [], targetNormalPoints: [] });
  function getThreatOperationMapAttackedFn() {
    getThreatOperationMapAttacked().then(({ data }) => {
      if (data.length == 0) return;
      // console.log(data, "地图数据");
      let targetPoints = [
        {
          value: data[0].customerCoordinate,
          name: data[0].customerCity,
        },
      ];
      mapAttackedData.value.targetPoints = targetPoints;
      let attackPoints = [];
      let lines = [];
      for (let item of data) {
        let color = item.count > 10 ? (item.count > 100 ? "#e10007" : "#F8E60C") : "#00eaff";
        attackPoints.push({ name: item.name, value: item.coordinate, color });
        lines.push({ from: item.coordinate, to: item.customerCoordinate, color });
      }
      mapAttackedData.value.attackPoints = attackPoints;
      mapAttackedData.value.lines = lines;
      mapAttackedData.value.updateStatus = !mapAttackedData.updateStatus;
    });
  }

  //攻防对抗
  let customerEventsNewestData = ref([]);
  function getCustomerEventsNewestDataFn() {
    selectNearlyTenAlert().then(({ data }) => {
      // console.log(data, "攻防对抗");
      customerEventsNewestData.value = data.map((item) => {
        return {
          ...item,
          source: item.district,
          time: item.getTime,
          ip: item.csrcip,
          server: item.cdstip,
          level: item.priority,
        };
      });
    });
  }

  //近30天安全事件总数
  let eventQuantityData = ref(0);
  function getEventQuantity() {
    selectNearlyThirtyDaysEvent().then(({ data }) => {
      // console.log(data, "近30天安全事件总数");
      eventQuantityData.value = data.eventNum;
    });
  }
  //事件处置率
  let eventHandlingRateData = ref("");
  function getEventHandlingRate() {
    selectEventHandlingRate().then(({ data }) => {
      // console.log(data, "事件处置率");
      eventHandlingRateData.value = data.eventHandLingRate;
    });
  }
  //事件相关信息
  let eventsNewestData = ref([]);
  function getEventsNewestDataFn() {
    selectEventHandDetail().then(({ data }) => {
      // console.log(data, "事件相关信息");
      eventsNewestData.value = data.eventHandDetailMap.map((item) => {
        return {
          ...item,
          name: item.eventName,
          time: item.createTime,
          username: item.createBy,
          status: item.status,
          level: item.levelId,
          levelName: item.levelName,
        };
      });
    });
  }

  //24h攻击流量趋势图
  let sttackedEsCountData = ref([]);
  function getAttackedEsCountFn() {
    getAttackedEsCount().then(({ data }) => {
      // console.log(data, "24h攻击流量趋势图");
      sttackedEsCountData.value = data.map((item) => {
        return {
          name: item.time,
          value: item.count,
        };
      });
    });
  }

  //攻击相关数据
  let attackNewData = ref({
    num0: 0,
    num1: 0,
    num2: 0,
  });
  function getAttackNewDataFn() {
    getSeimEsCount().then(({ data }) => {
      attackNewData.value = {
        num0: data.count,
        num1: data.csCount,
        num2: data.cdCount,
      };
    });
  }

  //受影响业务系统TOP5
  let rankingOfAffectedData = ref([]);
  function getRankingOfAffected() {
    getCdstIpCountTop().then(({ data }) => {
      // console.log(data, "受影响业务系统TOP5");
      rankingOfAffectedData.value = data.map((item) => {
        return {
          ...item,
          name: item.assetName,
        };
      });
    });
  }

  //攻击类型排名
  let getCeventTypeTopData = ref([]);
  function getCeventTypeTopFn() {
    getCeventNameCountTop().then(({ data }) => {
      // console.log(data, " 攻击类型排名");
      getCeventTypeTopData.value = data.map((item) => {
        return {
          name: item.content,
          count: item.count,
        };
      });
    });
  }

  function init() {
    getAlarmRankingToday();
    getAlarmQuantity();
    getLarmMonitoringTrendChart();
    getThreatOperationMapAttackedFn();
    getCustomerEventsNewestDataFn();
    getAlarmTableToBeAnalyzed();
    getEventQuantity();
    getEventHandlingRate();
    getEventsNewestDataFn();
    getAttackedEsCountFn();
    getRankingOfAffected();
    getCeventTypeTopFn();
    getAttackNewDataFn();
  }

  return {
    todayAlarmData,
    alarmQuantityData,
    mapAttackedData,
    larmMonitoringTrendChartData,
    customerEventsNewestData,
    alarmTableToBeAnalyzedData,
    eventQuantityData,
    eventHandlingRateData,
    eventsNewestData,
    sttackedEsCountData,
    rankingOfAffectedData,
    getCeventTypeTopData,
    attackNewData,
    // viewAlertNumList,
    // viewAlertNum,
    getAlarmRankingToday,
  };
}
