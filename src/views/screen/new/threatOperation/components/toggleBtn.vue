<template>
  <div class="toggleDiv" :class="showType === 'M' ? 'isM' : ''" @click="handBtn" />
</template>

<script>
export default {
  name: "toggleBtn",
};
</script>

<script setup>
defineProps({
  /* 显示位置 */
  showType: {
    type: String,
    default() {
      return "L";
    },
  },
});

const emits = defineEmits(["handToggles"]);
const handBtn = () => {
  let showType = sessionStorage.getItem("showType");
  if (showType === "L") {
    sessionStorage.setItem("showType", "M");
    emits("handToggles", "M");
  } else if (showType === "M") {
    sessionStorage.setItem("showType", "L");
    emits("handToggles", "L");
  }
};
</script>

<style scoped lang="scss">
.toggleDiv {
  width: 50px;
  height: 50px;
  background: url("@/assets/imgs/screen/toggleBg.png") no-repeat;
  position: absolute;
  overflow: hidden;
  right: -60px;
  top: 0;
  z-index: 999;
  cursor: pointer;
  background-position: 0 -50px;
  opacity: 0.4;
  transition: all 0.3s ease-in-out;
  &:hover {
    opacity: 1;
  }
}
.isM {
  background-position: 0 0;
}
</style>
