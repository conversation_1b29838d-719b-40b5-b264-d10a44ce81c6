<!---->
<template>
  <!-- Long -->
  <page-l v-if="pageType == 'L'" @handToggle="handToggle" />
  <page-m v-else @handToggle="handToggle" />
</template>

<script setup>
import pageL from "./pageL.vue";
import pageM from "./pageM.vue";
import { ref } from "vue";
let pageType = ref("M");
const getType = () => {
  let showType = sessionStorage.getItem("showType");
  if (showType) {
    pageType.value = showType;
  } else {
    pageType.value = "M";
    sessionStorage.setItem("showType", "M");
  }
};
getType();
const handToggle = (data) => {
  pageType.value = data;
};
</script>

<style scoped lang="scss"></style>
