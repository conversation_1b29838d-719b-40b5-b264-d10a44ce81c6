<template>
  <!-- 运营质量态势 -->
  <scaleWrapper>
    <starsoPage2 titleText="运营质量态势" borderType="page3">
      <div class="statePersonnelDiv">
        <!-- 左侧部分  -->
        <div class="statePersonnelLeft">
          <starso-title1 class="title">事件处置率</starso-title1>

          <div class="leftToCzlDiv">
            <p>事件处置率</p>
            <div>
              <!--              <circularProcess :count="100"></circularProcess>-->
              <CharGauge :pieData="stateData.shiJianChuZiLv" />
            </div>
          </div>

          <div class="rightBotTop">
            <CountCom title="已完成处置事件" :count="stateData.yiWanChengCuZiShiJian" />
            <CountCom title="处置中事件" :count="stateData.cuZhiZhongShiJian" />
          </div>

          <starso-title1 class="rwzxTitle">任务执行及时率</starso-title1>
          <CountCom class="rwzxCountCom" title="团队执行及时率" :count="stateData.renWuZhiXingJiSiLv" unitTit="%" styleType="mtt" />

          <div class="columnChaComDiv">
            <vue3-seamless-scroll :list="columnChaComData" :step="0.3" :hover="true" :limitScrollNum="3" v-if="columnChaComData.length">
              <ColumnChaCom :data="columnChaComData" />
            </vue3-seamless-scroll>
            <NoData v-else />
          </div>

          <starso-title1 class="zqlTitle">准确率统计</starso-title1>
          <div class="left_top">
            <div class="testClass">
              <div>
                <div>
                  <p>事件提报</p>
                  <starso-title4 typeName="准确率"></starso-title4>
                </div>
                <!--                <circularProcess :count="stateData.shiJianTiBaoZhunQueLv"></circularProcess>-->
                <CharGauge :size="100" :pieData="stateData.shiJianTiBaoZhunQueLv" />
              </div>
            </div>
            <div class="testClass">
              <div>
                <div>
                  <p>告警判断</p>
                  <starso-title4 typeName="准确率"></starso-title4>
                </div>
                <!--                <circularProcess :count="stateData.gaoJingPanDuanZhunQueLv"></circularProcess>-->
                <CharGauge :size="100" :pieData="stateData.gaoJingPanDuanZhunQueLv" />
              </div>
            </div>
          </div>
        </div>

        <!-- 中间部分  -->
        <div class="statePersonnelCenter">
          <div class="sCenterTop">
            <!-- 分析师数据 -->
            <div class="sCUserDiv">
              <div class="userImgDiv">
                <div class="userImg">
                  <img v-if="userActive.type === 'two'" :src="erxian" style="width: 100%" />
                  <img v-else :src="yixian" style="width: 100%" />
                </div>
                <div class="userNameDiv">{{ userActive.nickName }}</div>
                <div class="userLevel">
                  <span v-if="userActive.type === 'one'">一线安全分析师</span>
                  <span v-else-if="userActive.type === 'two'">二线安全分析师</span>
                </div>
              </div>

              <div class="userCenter">
                <userInfoCom comTitle="漏洞验证数量" :count="userData.varifyCount" />
                <userInfoCom comTitle="安全测试成果数量" :count="userData.testResultCount" />
              </div>

              <div class="userRigth">
                <UserRatioCom comTitle="处置告警数量" :count="userData.alertCount" :rateCon="userData.alertRate" />
                <UserRatioCom
                  :comTitle="userActive.type === 'two' ? '处置事件数量' : '提报事件数量'"
                  :count="userData.eventCount"
                  :rateCon="userData.eventRate"
                />
              </div>
            </div>
            <!-- 分析师头像 -->
            <div class="sCImgDiv" @mouseenter="handIn('into')" @mouseleave="handIn('out')">
              <span class="btnSpan leftBtn" v-if="isBtnShow && pageSize > 1" @click="handSpanBtn('l')">
                <i class="el-icon-caret-left" />
              </span>
              <div
                :class="item.nickName === userActive.nickName ? 'userActive' : ''"
                v-for="(item, index) in analystsList"
                :key="item"
                @click="hangUserImg(item, index)"
              >
                <img v-if="item.type === 'two'" :src="erxian" style="width: 100%" />
                <img v-else :src="yixian" style="width: 100%" />
              </div>
              <span class="btnSpan rightBtn" v-if="isBtnShow && pageSize > 1" @click="handSpanBtn('r')">
                <i class="el-icon-caret-right" />
              </span>
            </div>
          </div>

          <div class="sCenterBot">
            <starso-title1 class="title">运营质量雷达图</starso-title1>
            <div class="ldtL" />
            <div class="ldtR" />
            <div class="CountComDiv">
              <CountCom styleType="long" title="日均告警处理数" :count="stateData.riJunGaoJingChuLiShu" />
              <CountCom styleType="long" title="日均事件处理数" :count="stateData.riJunShiJianTiBaoShu" />
            </div>
            <StateRadar :radarData="radarData" />
          </div>
        </div>

        <!-- 右侧部分  -->
        <div class="statePersonnelRight">
          <!--  按钮   -->
          <div class="btnDiv">
            <span @click="handBtn('year')" :class="intervalTime === 'year' ? 'btnActive' : ''">年</span>
            <span @click="handBtn('month')" :class="intervalTime === 'month' ? 'btnActive' : ''">月</span>
            <span @click="handBtn('week')" :class="intervalTime === 'week' ? 'btnActive' : ''">周</span>
          </div>

          <StateRightCom comType="MTTA" :count="stateData.pinJunXiangYingShJian" :listData="mttaData" style="margin-bottom: 10px" />
          <StateRightCom comType="MTTD" :listData="mttdData" :count="stateData.pinJunJianCeShJian" />
        </div>
      </div>
    </starsoPage2>
  </scaleWrapper>
</template>

<script>
export default {
  name: "index",
};
</script>

<script setup>
import scaleWrapper from "starso/scaleWrapper";
import starsoPage2 from "starso/starsoPage2";
import starsoTitle1 from "starso/starsoTitle1";
import circularProcess from "starso/circularProcess";
import starsoNum2 from "starso/starsoNum2";
import starsoTitle4 from "starso/starsoTitle4";
import starsoTitle5 from "starso/starsoTitle5";
import CountCom from "../components/countCom.vue";
import StateRadar from "./components/stateRadar.vue";
import StateRightCom from "./components/stateRightCom.vue";
import UserInfoCom from "./components/userInfoCom.vue";
import UserRatioCom from "./components/userRatioCom.vue";
import ColumnChaCom from "../components/columnChaCom.vue";
import CharGauge from "../components/charGauge.vue";
import yixian from "@/assets/imgs/screen/statePersonnel/yixian.png";
import erxian from "@/assets/imgs/screen/statePersonnel/erxian.png";
import NoData from "../components/noData.vue";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";

import {
  getSelectLeiDaTuData,
  getSelectTaskExecutionRate,
  getSelectAnalystsMttaStatistics,
  getSelectAnalystsMttdStatistics,
  getSelectAnalysts,
  getSelectAnalystsData,
} from "@/api/screen/new/statePersonnel";

import { ref } from "vue";

/* 时间周期 */
let intervalTime = ref("year");

/* 查询雷达图 */
let stateData = ref({});
let radarData = ref({});
const getSelectLeiDaTuDataFun = (data) => {
  getSelectLeiDaTuData(data).then((res) => {
    radarData.value = res.data;
    res.data.forEach((item) => {
      if (
        item.type === "shiJianChuZiLv" ||
        item.type === "renWuZhiXingJiSiLv" ||
        item.type === "shiJianTiBaoZhunQueLv" ||
        item.type === "gaoJingPanDuanZhunQueLv"
      ) {
        item.param_value = item.param_value.split("%")[0];
      }
      stateData.value[item.type] = item.param_value;
    });
  });
};

/* 分析师任务执行及时率列表 */
let columnChaComData = ref([]);
const getSelectTaskExecutionRateFun = (data) => {
  getSelectTaskExecutionRate(data).then((res) => {
    columnChaComData.value = res.data.map((item) => {
      return {
        name: item.name,
        value: item.rate,
      };
    });
  });
};

/* 分析师MTTA统计 */
let mttaData = ref([]);
const getSelectAnalystsMttaStatisticsFun = (data) => {
  getSelectAnalystsMttaStatistics(data).then((res) => {
    mttaData.value = res.data.map((item) => {
      item.blue = parseInt(item.blue);
      item.yellow = parseInt(item.yellow);
      item.red = parseInt(item.red);
      return {
        title: item.name,
        countList: [item.blue, item.yellow, item.red],
        total: item.blue + item.yellow + item.red,
      };
    });
  });
};

/* 分析师MTTD统计 */
let mttdData = ref([]);
const getSelectAnalystsMttdStatisticsFun = (data) => {
  getSelectAnalystsMttdStatistics(data).then((res) => {
    mttdData.value = res.data.map((item) => {
      item.blue = parseInt(item.blue);
      item.yellow = parseInt(item.yellow);
      item.red = parseInt(item.red);
      return {
        title: item.name,
        countList: [item.blue, item.yellow, item.red],
        total: item.blue + item.yellow + item.red,
      };
    });
  });
};

/* 查询一二线分析师及服务该客户的其他分析师 */
let analystsList = ref([]);
let analystsListAll = ref([]);
let userActive = ref({});

/* 分页总数 */
let pageNum = ref(1);
let pageSize = ref(0);

const getSelectAnalystsFun = () => {
  getSelectAnalysts().then((res) => {
    /*analystsList.value = res.data;*/
    analystsListAll.value = res.data;
    pageSize.value = Math.ceil(res.data.length / 6);
    if (!userActive.value.nickName) {
      userActive.value = res.data[0];
      analystsListFun(1);
      timerFun();
    }
    getSelectAnalystsDataFun({
      intervalTime: intervalTime.value,
      nickName: res.data[0].nickName,
      type: res.data[0].type,
    });
  });
};

let timer = ref();
let userActiveNum = ref(1);

/* 定时轮播 */
const timerFun = () => {
  timer.value = setInterval(() => {
    userActive.value = analystsList.value[userActiveNum.value - 1];
    hangUserImg(userActive.value);
    if (userActiveNum.value === analystsList.value.length) {
      if (pageNum.value === pageSize.value) {
        pageNum.value = 1;
      } else {
        pageNum.value++;
      }
      setTimeout(() => {
        analystsListFun(pageNum.value);
      }, 9800);
      userActiveNum.value = 1;
    } else {
      userActiveNum.value++;
    }
  }, 10000);
};

/* 限定分页 */
const analystsListFun = (pageNum) => {
  analystsList.value = analystsListAll.value.slice((pageNum - 1) * 6, pageNum * 6);
};

/* 鼠标移入清除定时器 */
let isBtnShow = ref(false);
const handIn = (type) => {
  if (type === "into") {
    isBtnShow.value = true;
    clearInterval(timer.value);
  } else {
    isBtnShow.value = false;
    timerFun();
  }
};

/* span分頁按钮 */
const handSpanBtn = (type) => {
  if (type === "l") {
    pageNum.value--;
    if (pageNum.value < 1) {
      pageNum.value = pageSize.value;
    }
  } else {
    pageNum.value++;
    if (pageNum.value > pageSize.value) {
      pageNum.value = 1;
    }
  }
  userActiveNum.value = 1;
  analystsListFun(pageNum.value);
  userActive.value = analystsList.value[0];
  hangUserImg(userActive.value);
};

/* 查询分析师数据 */
let userData = ref({});
const getSelectAnalystsDataFun = (data) => {
  getSelectAnalystsData(data).then((res) => {
    res.data[0].alertRate = res.data[0].alertRate.split("%")[0];
    res.data[0].eventRate = res.data[0].eventRate.split("%")[0];
    userData.value = res.data[0];
  });
};

/* 按钮组点击 */
const handBtn = (data) => {
  intervalTime.value = data;
  getList({ intervalTime: data });
};
/* 分析师头像点击 */
const hangUserImg = (data, index) => {
  /* 如果是点击，设置索引为当前index */
  if (index !== undefined) {
    userActiveNum.value = index + 1;
  }
  userActive.value = data;
  getSelectAnalystsDataFun({
    intervalTime: intervalTime.value,
    nickName: data.nickName,
    type: data.type,
  });
};

/* 获取全部数据 */
const getList = (data) => {
  getSelectLeiDaTuDataFun(data);
  getSelectTaskExecutionRateFun(data);
  getSelectAnalystsMttdStatisticsFun(data);
  getSelectAnalystsMttaStatisticsFun(data);
  getSelectAnalystsFun();
};

getList({ intervalTime: intervalTime.value });

/* 定时器 */
let times = ref(null);
const getDataTimer = (time = 30000) => {
  if (times.value) {
    clearInterval(times.value);
  }
  times.value = setInterval(() => {
    let data = "year";
    intervalTime.value === "year" ? (data = "month") : intervalTime.value === "month" ? (data = "week") : (data = "year");
    handBtn(data);
    /*getList({intervalTime: intervalTime.value });*/
  }, time);
};
getDataTimer();
</script>

<style scoped lang="scss">
@import "@/assets/style/newScreen.css";
::v-deep .scale-content {
  background: url(@/assets/imgs/screen/vulnerability/vulnBg.jpg) no-repeat;
}
.statePersonnelDiv {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 463px auto 463px;
  padding: 30px 25px;
  color: #fff;
  margin-top: 20px;

  /* 左侧 右侧部分 */
  .statePersonnelLeft,
  .statePersonnelRight {
    height: 926px;
    background-image: url("@/assets/imgs/screen/infrastructure/lefttop.png");
    background-size: 100% 100%;
    padding: 30px 25px;
  }

  .statePersonnelLeft {
    .leftToCzlDiv {
      width: 423px;
      height: 170px;
      background-image: url("@/assets/imgs/screen/statePersonnel/czlBg.png");
      background-size: 100% 100%;
      margin-top: 20px;
      margin-bottom: 30px;

      /*display: flex;
      justify-content: space-between;*/
      align-items: center;
      display: grid;
      grid-template-columns: 230px auto;
      > p {
        font-size: 18px;
        text-align: center;
        opacity: 0.7;
      }
    }
    .rightBotTop {
      display: flex;
      justify-content: space-between;
      .countComDiv {
        width: 172px;
      }
    }
    .rwzxTitle {
      margin-top: 40px;
      margin-bottom: 26px;
    }
    .rwzxCountCom {
      margin-bottom: 15px;
    }

    /* 任务执行及时率 - 滚动 */
    .columnChaComDiv {
      height: 190px;
      overflow: hidden;
      position: relative;
    }

    .zqlTitle {
      margin-top: 10px;
      margin-bottom: 15px;
    }
    .left_top {
      display: flex;
      .xfl {
        margin-right: 15px;
      }

      > div {
        flex: 1;
        & {
          padding: 10px 0px;
        }
        &:nth-child(1) {
          margin-right: 40px;
        }
      }
      .testClass {
        & > div {
          display: flex;
          > div > p:nth-child(1) {
            text-align: center;
            font-size: 18px;
            font-weight: 400;
            color: #ffffff;
            opacity: 0.7;
          }
        }
      }
    }
  }

  /* 中间部分 */
  .statePersonnelCenter {
    margin: 0 24px;
    .sCenterTop,
    .sCenterBot {
      padding: 22px 25px;
    }
    .sCenterTop {
      height: 365px;
      background-image: url("@/assets/imgs/screen/statePersonnel/sCenterTop.png");
      background-size: 100% 100%;

      /* 分析师数据 */
      .sCUserDiv {
        display: flex;
        justify-content: space-between;

        .userImgDiv {
          width: 186px;
          height: 226px;
          background-image: url("@/assets/imgs/screen/statePersonnel/cTopUser.png");
          background-size: 100% 100%;
          text-align: center;
          font-size: 16px;

          .userImg {
            display: inline-block;
            /*border: 1px solid #fff;*/
            border-radius: 50px;
            width: 66px;
            height: 66px;
            margin-top: 40px;
          }
          .userNameDiv {
            margin-top: 24px;
            margin-bottom: 10px;
          }
          .userLevel {
            opacity: 0.35;
          }
        }
      }

      /* 分析师头像 */
      .sCImgDiv {
        position: relative;
        .btnSpan {
          color: #4beaea;
          display: inline-block;
          cursor: pointer;
          width: 30px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          border-radius: 20px;
          border: 1px solid #1e4d5b;
        }
        .leftBtn {
          position: absolute;
          top: 13px;
        }
        .rightBtn {
          position: absolute;
          top: 13px;
          right: 0;
        }

        margin-top: 21px;
        .userActive {
          box-shadow: 0 0 11px #52fefd;
        }
        > div {
          cursor: pointer;
          /*border: 1px solid #fff;*/
          border-radius: 50px;
          width: 52px;
          height: 52px;
          display: inline-block;
          margin-left: 68px;
        }
      }
    }
    .sCenterBot {
      height: 537px;
      margin-top: 24px;
      background-image: url("@/assets/imgs/screen/statePersonnel/sCenterBot.png");
      background-size: 100% 100%;
      position: relative;
      .CountComDiv {
        position: absolute;
        right: 20px;
        top: 20px;
      }

      .ldtL,
      .ldtR {
        width: 32px;
        height: 394px;
        background-image: url("@/assets/imgs/screen/statePersonnel/ldtLink.png");
        background-size: 100% 100%;
        position: absolute;
        bottom: 40px;
      }
      .ldtL {
        left: 35px;
        transform: rotate(180deg);
      }
      .ldtR {
        position: absolute;
        right: 35px;
      }
    }
  }

  /* 右侧部分 */
  .statePersonnelRight {
    position: relative;
    .btnDiv {
      position: absolute;
      top: -45px;
      right: 10px;
      span {
        display: inline-block;
        width: 62px;
        height: 39px;
        line-height: 39px;
        text-align: center;
        cursor: pointer;
      }
      .btnActive {
        background-image: url("@/assets/imgs/screen/statePersonnel/btnActiveBg.png");
        background-size: 100% 100%;
      }
    }
  }
}
</style>
