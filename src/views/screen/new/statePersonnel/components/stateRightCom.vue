<template>
  <!-- MTTA or MTTD  -->
  <div class="StateRightComDiv">
    <starso-title1 class="title">{{ comType }}</starso-title1>

    <CountCom styleType="mtt" :title="'团队' + comType" :count="count" />

    <!-- 信息描述 -->
    <div class="desDiv">
      {{ comType }}标准值={{ comType === "MTTA" ? 240 : 120 }} <span class="info1" />及时完成 <span class="info2" />超时50%以内
      <span class="info3" />超时100%以内
    </div>

    <!-- 信息条 -->
    <div class="infoDiv">
      <!--      120 240-360-480
      120-180-240-->
      {{ comType === "MTTA" ? 240 : 120 }}min
      <span>{{ comType === "MTTA" ? 360 : 180 }}min</span>
      <span>{{ comType }}</span>
    </div>

    <!-- 柱状图 -->
    <div class="CountListComDiv">
      <vue3-seamless-scroll :list="listData" :step="0.3" :hover="true" :limitScrollNum="3" v-if="listData.length">
        <CountList v-for="(item, index) in listData" :key="item" :item="item" :itemIndex="index" />
      </vue3-seamless-scroll>
      <NoData v-else />
    </div>
  </div>
</template>

<script>
export default {
  name: "stateRightCom",
};
</script>

<script setup>
/* MTTA or MTTD 组件 */
import starsoTitle1 from "starso/starsoTitle1";
import CountCom from "../../components/countCom.vue";
import CountList from "../../components/countList.vue";
import NoData from "../../components/noData.vue";
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";

defineProps({
  /* 组件类型  默认 MTTA
   * MTTA
   * MTTD
   * */
  comType: {
    type: String,
    default() {
      return "MTTA";
    },
  },
  /* 响应时间 */
  count: {
    type: String || Number,
    default() {
      return 0;
    },
  },
  /* 响应时间 */
  listData: {
    type: Array,
    default() {
      return [];
    },
  },
});
</script>
<style scoped lang="scss">
.StateRightComDiv {
  .title {
    margin-bottom: 27px;
  }
  /* 信息描述 */
  .desDiv {
    font-size: 12px;
    text-align: end;
    margin-top: 37px;
    margin-bottom: 20px;
    span {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-left: 10px;
      margin-right: 2px;
    }
    .info1 {
      background: #48e8fd;
    }
    .info2 {
      background: #d3981f;
    }
    .info3 {
      background: #932326;
    }
  }

  /* 信息条 */
  .infoDiv {
    width: 418px;
    height: 33px;
    line-height: 33px;
    text-align: end;
    background: rgba(72, 232, 253, 0.2);
    border: 1px solid rgba(72, 232, 253, 0.2);
    opacity: 0.7;
    font-size: 12px;
    color: #fff;
    margin-bottom: 23px;
    span:nth-child(2) {
      margin-left: 20px;
      margin-right: 44px;
    }
    span:last-child {
      margin-right: 14px;
    }
  }

  .CountListComDiv {
    height: 180px;
    overflow: hidden;
    position: relative;
  }
}
</style>
