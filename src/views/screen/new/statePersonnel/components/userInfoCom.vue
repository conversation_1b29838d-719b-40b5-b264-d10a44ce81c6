<template>
  <div class="uCTop">
    <div class="imgDiv">
      <div class="imgBg1" />
      <div class="imgBg2" :class="comTitle === '漏洞验证数量' ? 'loudong' : 'anquan'" />
    </div>
    <div class="uCTopCon">
      <CountCom styleType="user" :title="comTitle" :count="count" />
    </div>
  </div>
</template>

<script>
export default {
  name: "userInfoCom",
};
</script>

<script setup>
/*
 * 运营质量态势 - user信息组件
 * */
import CountCom from "../../components/countCom.vue";

defineProps({
  /*
   * 标题名称类型
   * */
  comTitle: {
    type: String,
    default() {
      return "未知";
    },
  },
  /* 数量 */
  count: {
    type: String || Number,
    default() {
      return 0;
    },
  },
});
</script>

<style scoped lang="scss">
.uCTop {
  width: 277px;
  height: 100px;
  background-image: url("@/assets/imgs/screen/statePersonnel/cTopL.png");
  background-size: 100% 100%;
  margin-bottom: 12px;
  display: grid;
  grid-template-columns: 120px auto;

  .imgDiv {
    position: relative;
    .imgBg1 {
      width: 90px;
      height: 101px;
      background-image: url("@/assets/imgs/screen/statePersonnel/userLeftBg.png");
      background-size: 100% 100%;
      position: absolute;
      top: 5px;
      left: 15px;
    }
    .imgBg2 {
      width: 28px;
      height: 28px;
      background-size: 100% 100%;
      position: absolute;
      top: 34px;
      left: 46px;
    }
    .loudong {
      background-image: url("@/assets/imgs/screen/statePersonnel/loudong.png");
    }
    .anquan {
      width: 23px;
      height: 30px;
      background-image: url("@/assets/imgs/screen/statePersonnel/anquan.png");
      top: 34px;
      left: 49px;
    }
  }
  .uCTopCon {
    margin-top: 20px;
  }
}
</style>
