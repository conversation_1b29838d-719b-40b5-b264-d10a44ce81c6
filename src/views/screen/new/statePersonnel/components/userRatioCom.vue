<template>
  <div class="uRTop">
    <div class="uRTopCon">
      <CountCom styleType="user" :title="comTitle" :count="count" />
    </div>
    <div class="uRTopConRight">
      <starso-title4 typeName="准确率"></starso-title4>
      <!--      <circularProcess :count="rateCon"></circularProcess>-->
      <CharGauge :size="90" :pieData="rateCon" />
    </div>
  </div>
</template>

<script>
export default {
  name: "userRatioCom",
};
</script>

<script setup>
import CountCom from "../../components/countCom.vue";
import circularProcess from "starso/circularProcess";
import starsoTitle4 from "starso/starsoTitle4";
import CharGauge from "../../components/charGauge.vue";
defineProps({
  /*
   * 标题名称
   * */
  comTitle: {
    type: String,
    default() {
      return "未知";
    },
  },
  /* 数量 */
  count: {
    type: String || Number,
    default() {
      return 0;
    },
  },
  /* 准确率 */
  rateCon: {
    type: Number,
    default() {
      return 0;
    },
  },
});
</script>

<style scoped lang="scss">
.uRTop {
  width: 327px;
  height: 100px;
  background-image: url("@/assets/imgs/screen/statePersonnel/cTopR.png");
  background-size: 100% 100%;
  margin-bottom: 12px;
  display: grid;
  grid-template-columns: 145px auto;
  .uRTopCon {
    margin-top: 20px;
  }
  .uRTopConRight {
    display: flex;
    align-items: center;
    .xfl {
      margin-top: 0;
      margin-right: 10px;
    }
    .circularProcess {
      margin-left: 10px;
    }
  }
}
</style>
