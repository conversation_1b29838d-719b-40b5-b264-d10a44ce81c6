<template>
  <component :is="windowSize < 1500 ? screenLayout : divComponent">
    <div id="box">
      <div class="wrapper">
        <div class="header">
          <div class="left">
            <img src="@/assets/imgs/screen/logo.png" alt="" />
            <span>网络安全运营中心</span>
          </div>
          <div class="right">
            <span>{{ nowTime }}</span>
            <span>运营态势</span>
          </div>
        </div>
        <div class="content">
          <div class="top">
            <div class="box">
              <div class="title">30天日均安全日志数量</div>
              <div class="box-content">
                <div class="box-info">
                  {{ filterNum(thirtyDayNum) }}<span class="count">{{ filterCount(thirtyDayNum) }}</span>
                </div>
              </div>
            </div>
            <div class="box">
              <div class="title">近24小时日志总量</div>
              <div class="box-content">
                <div class="box-info">
                  {{ filterNum(logNum) }}<span class="count">{{ filterCount(logNum) }}</span>
                </div>
              </div>
            </div>
            <div class="box">
              <div class="title">近24小时告警量</div>
              <div class="box-content">
                {{ alertNum }}
              </div>
            </div>
            <div class="box">
              <div class="title">引擎运行情况/安全设备</div>
              <div class="box-content">
                {{ leiData }}
              </div>
            </div>
          </div>
          <div class="left">
            <div class="box">
              <div class="title">CPU性能监控</div>
              <div class="box-content">
                <MultipleLine :type="'cpu'"></MultipleLine>
              </div>
            </div>
            <div class="box">
              <div class="title">内存性能监控</div>
              <div class="box-content">
                <MultipleLine :type="'memory'"></MultipleLine>
              </div>
            </div>
            <div class="box">
              <div class="title">
                存储资源用量监控 <span class="rateNum">剩余：{{ remain }}</span>
              </div>
              <div class="box-content">
                <MultipleLine :type="'storage'"></MultipleLine>
              </div>
            </div>
          </div>
          <div class="right">
            <MainLayout></MainLayout>
          </div>
          <div class="bottom">
            <div class="box">
              <div class="title">日志源状态</div>
              <div class="box-content">
                <LogTable :type="'log'"></LogTable>
              </div>
            </div>
            <div class="box">
              <div class="title">日志入库状态监控</div>
              <div class="box-content">
                <log-line></log-line>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </component>
</template>
<script setup>
import divComponent from "../divComponent.vue";
import screenLayout from "../../screenLayout/index.vue";
const windowSize = window.innerWidth;
</script>
<script>
import { getThirtyDayNum, selecTtwentyFourLogNum, selecTtwentyFourAlertNum, selectLeiDaTuData, selectSiemTotalNumList } from "@/api/screen/basic";
import LogTable from "@/views/screen/threatOperation/components/tableShuff.vue";
import MainLayout from "./components/main.vue";
import LogLine from "./components/line.vue";
import MultipleLine from "./components/multipleLine.vue";
import bus from "@/utils/bus";
export default {
  name: "Infrastructure",
  components: {
    MainLayout,
    LogTable,
    LogLine,
    MultipleLine,
  },
  data() {
    return {
      nowTime: "",
      remain: "",
      thirtyDayNum: 0,
      logNum: 0,
      alertNum: 0,
      leiData: "",
    };
  },
  mounted() {
    // 存储资源用量监控
    let _this = this;
    this.$emitter.on("remain", (e) => {
      // console.log("eventBus1 ", e);
      _this.remain = e;
    });

    this.getThirtyDayNumFn();
    this.getLogNum();
    this.getAlertNum();
    this.getLetData();
  },
  filters: {},
  methods: {
    filterCount(value) {
      if (value > 10000) {
        return "万";
      } else {
        return "";
      }
    },
    filterNum(value) {
      if (value > 10000) {
        return (value / 10000).toFixed(2);
      } else {
        return value;
      }
    },
    // 30天日均安全日志数量
    async getThirtyDayNumFn() {
      const res = await getThirtyDayNum();
      this.thirtyDayNum = res.data;
    },
    // 近24小时日志总量
    async getLogNum() {
      const res = await selecTtwentyFourLogNum();
      this.logNum = res.data;
    },

    // 近24小时告警数量
    async getAlertNum() {
      const res = await selecTtwentyFourAlertNum();
      this.alertNum = res.data;
    },
    // 安全设备引擎/引擎运行情况
    async getLetData() {
      const res = await selectLeiDaTuData();
      this.leiData = res.msg;
    },

    // 时间
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month = new Date(timeStamp).getMonth() + 1 < 10 ? "0" + (new Date(timeStamp).getMonth() + 1) : new Date(timeStamp).getMonth() + 1;
      let date = new Date(timeStamp).getDate() < 10 ? "0" + new Date(timeStamp).getDate() : new Date(timeStamp).getDate();
      let hh = new Date(timeStamp).getHours() < 10 ? "0" + new Date(timeStamp).getHours() : new Date(timeStamp).getHours();
      let mm = new Date(timeStamp).getMinutes() < 10 ? "0" + new Date(timeStamp).getMinutes() : new Date(timeStamp).getMinutes();
      let ss = new Date(timeStamp).getSeconds() < 10 ? "0" + new Date(timeStamp).getSeconds() : new Date(timeStamp).getSeconds();
      this.nowTime = year + "年" + month + "月" + date + "日" + " " + hh + ":" + mm + ":" + ss;
    },
    nowTimes() {
      this.timeFormate(new Date());
      setInterval(this.nowTimes, 1000);
      this.clear();
    },
    clear() {
      clearInterval(this.nowTimes);
      this.nowTimes = null;
    },
  },
};
</script>
<style lang="scss">
$base-font-size: 16;
// 大屏 小于 1500px 宽度屏幕 媒体查询
@media only screen and (max-width: 1500px) {
  :root {
    --mini-layout: 1080px;
  }
}
#box {
  height: var(--mini-layout, 100vh);
}
.wrapper {
  height: var(--mini-layout, 100vh);
}
.content {
  height: calc(var(--mini-layout, 100vh) - #{79rem / $base-font-size});
}
</style>

<style lang="scss" scoped>
p,
div {
  margin: 0;
  padding: 0;
}
body,
ul {
  /*组合选择器*/
  list-style: none; /*清除列表默认样式*/
  padding: 0; /*清除padding*/
  margin: 0;
}
$base-font-size: 16;
#box {
  overflow-y: auto;
  overflow-x: hidden;
  // background: #003079;
  background: url("../../../assets/imgs/screen/layoutbg.png");
  background-position: bottom;
  background-size: 100%;
}
.wrapper {
  font-family: "SourceHanSansCN-Regular";
  font-size: 14rem / $base-font-size;
}
// 头部
.header {
  padding: 0px 17rem / $base-font-size;
  $header-height: 79rem / $base-font-size;
  font-family: "FZZYJW--GB1-0";
  color: #fff;
  height: $header-height;
  border: 2px solid #073ea0;
  background: linear-gradient(180deg, #07226b 0%, #031445 100%);
  box-sizing: border-box;
  vertical-align: middle;
  .left {
    display: inline-block;
    line-height: $header-height - 4rem / $base-font-size;
    img {
      vertical-align: middle;
      width: 34rem / $base-font-size;
    }

    span {
      font-size: 28rem / $base-font-size;
      margin: 2rem / $base-font-size 19rem / $base-font-size;
      vertical-align: middle;
    }
  }
  .right {
    float: right;
    line-height: $header-height - 4rem / $base-font-size;
    > span {
      vertical-align: top;
    }
    > span:nth-child(2) {
      display: inline-block;
      width: 211rem / $base-font-size;
      height: 100%;
      background: #44b0ff;
      margin-left: 42rem / $base-font-size;
      font-size: 32rem / $base-font-size;
      color: #dff0f8;
      text-align: center;
    }
  }
}
// 内容区
.content {
  padding: 14rem / $base-font-size 17rem / $base-font-size 8rem / $base-font-size 17rem / $base-font-size;
  width: 100%;

  position: relative;
  display: flex;
  flex-direction: column;
  // 标题
  .title {
    color: #dff0f8;
    text-align: left;
    border: 2px solid #073ea0;
    border-bottom: none;
    height: 30rem / $base-font-size;
    line-height: 28rem / $base-font-size;
    padding-left: 18rem / $base-font-size;
    vertical-align: middle;
    background: linear-gradient(181deg, #050925 0%, #152b68 100%);
    box-sizing: border-box;
  }
  // 万单位
  .count {
    font-size: 14px;
    display: inline-block;
    margin-left: 2px;
  }
  // 顶部
  .top {
    flex: 86;
    display: flex;
    z-index: 2;
    .box {
      margin-left: 34rem / $base-font-size;
      width: 226rem / $base-font-size;
      height: 100%;
      vertical-align: middle;
      .box-content {
        padding: 18rem / $base-font-size 0;
        width: 100%;
        border: 1px solid #0b64bf;
        box-sizing: border-box;
        text-align: center;
        font-size: 24rem / $base-font-size;
        font-family: Courier New;
        font-weight: bold;
        color: #dff0f8;
        vertical-align: middle;
      }
    }
    .box:first-child {
      margin-left: 0;
    }
  }
  // 左侧拆线
  .left {
    flex: 920;
    margin-top: 14rem / $base-font-size;
    width: 353rem / $base-font-size;
    display: flex;
    flex-direction: column;
    .box {
      margin-bottom: 12rem / $base-font-size;
      width: 100%;
      height: 100%;
      > .box-content {
        width: 100%;
        border: 1px solid #0b64bf;
        height: calc(100% - #{30rem / $base-font-size});
      }
    }
    .box:last-child {
      margin-top: 73rem / $base-font-size;
    }
    .rateNum {
      margin-right: 7rem / $base-font-size;
      float: right;
      color: #dd9b2a;
    }
  }
  //  系统
  .right {
    position: absolute;
    left: 392rem / $base-font-size;
    top: 30rem / $base-font-size;
  }
  // 下部分
  .bottom {
    position: absolute;
    right: 17rem / $base-font-size;
    top: 722rem / $base-font-size;
    display: flex;
    height: 258rem / $base-font-size;
    .box {
      margin-left: 12rem / $base-font-size;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      > .box-content {
        white-space: nowrap;
        padding-bottom: 6rem / $base-font-size;
        width: 353rem / $base-font-size;
        border: 1px solid #0b64bf;
        height: calc(100% - #{30rem / $base-font-size});
      }
    }
  }
}
</style>
