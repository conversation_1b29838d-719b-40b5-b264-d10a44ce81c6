<template>
  <div class="line-wrapper">
    <div class="line" v-bind:class="{ affectLine: type }" ref="intelligenceLine"></div>
  </div>
</template>
<script>
let intelligenceBarChart = null;
import { selectLogWareList } from "@/api/screen/basic";
let tabLoopTimer = null;
export default {
  props: {
    type: String,
    lineColor: String,
  },
  data() {
    return {
      xBarData: [],
      yBarData: [],
    };
  },
  mounted() {
    this.getlogData();
    // 轮播    //
    this.startLoop();
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      intelligenceBarChart.resize();
    },
    // 今日情报数量
    intelligenceLIne(xdata, ydata) {
      intelligenceBarChart = this.$echarts.init(this.$refs.intelligenceLine);
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "none" },
        },
        legend: {
          data: ["入库数据量"],
          align: "left",
          bottom: 6,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          left: "2%",
          right: "4%",
          bottom: "14%",
          top: "10%",
          containLabel: true,
        },
        // x轴溢出
        dataZoom: [
          {
            realtime: true,
            show: true,
            type: "inside",
            start: 0,
            end: 100,
            // xAxisIndex:[0,1],//表示x轴折叠
          },
        ],

        xAxis: {
          type: "category",
          boundaryGap: true, //两边是否留白
          // max: "2021-08-19 10",

          axisLabel: {
            // interval:0,
            // margin: 10,
            // rotate: -7,
            show: true,
            color: "#FFF", //更改坐标轴文字颜色
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
          data: xdata,
        },
        yAxis: {
          type: "value",
          axisLabel: {
            show: true,
            // interval:2,
            // boundaryGap: false,
            color: "#FFF", //更改坐标轴文字颜色
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
        },
        series: [
          {
            name: "入库数据量",
            data: ydata,
            type: "line",
            smooth: true, //弧度
            areaStyle: {
              normal: {
                color: "#0B64BF",
              },
            },
            symbol: "circle", //折点设定为实心点
            symbolSize: 9, //设定实心点的大小
            itemStyle: {
              normal: {
                color: "#7FFFFF", //折点颜色
                borderColor: "#0E9CFF",
                lineStyle: {
                  normal: {
                    color: "#17ABE0",
                  },
                },
              },
            },
          },
        ],
      };
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (ydata.length > 0) {
        intelligenceBarChart.setOption(option);
      } else {
        intelligenceBarChart.setOption(noData);
      }
    },
    //日志入库状态监控
    async getlogData() {
      const res = await selectLogWareList();
      const xdata = (this.xBarData = res.data.map((rItem) => {
        return rItem.h;
      }));
      const ydata = res.data.map((rItem) => {
        return rItem.amount;
      });

      this.intelligenceLIne(xdata, ydata);
    },
    // 轮播
    startLoop() {
      tabLoopTimer = setInterval(() => {
        this.getlogData();
      }, 3600000);
    },
  },
  beforeUnmount() {
    clearInterval(tabLoopTimer);
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.line-wrapper,
.line {
  height: 100%;
}
</style>
