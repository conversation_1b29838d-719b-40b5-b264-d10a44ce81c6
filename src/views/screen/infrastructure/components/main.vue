<template>
  <div class="container">
    <div class="map-panel">
      <div class="left-bottom wrapper">
        <div class="business-system" v-for="(item, index) in source1" :key="index" :style="{ marginTop: (32 + index * 42) / baseFontSize + 'rem' }">
          <div class="text">{{ item.name }}</div>
          <div class="nums">
            <span class="num1">{{ item.num1 }}</span>
            <span class="separate">/</span>
            <span class="num2">{{ item.num2 }}</span>
          </div>
          <div class="desc wrapper" :style="{ opacity: item.showDesc ? 1 : 0 }">
            <div class="text">{{ item.desc }}</div>
          </div>
        </div>
      </div>
      <div class="right-top wrapper">
        <div class="server-item" :style="{ marginTop: 87 / baseFontSize + 'rem' }">
          <div class="server-tip">资源监测引擎</div>
          <div class="time-desc">
            上一轮同步时间 <br />
            {{ weekdetail.ziChanYingQingTongBuShiJian }}
          </div>
        </div>
        <div class="server-item" :style="{ marginTop: (87 + 67) / baseFontSize + 'rem' }">
          <div class="server-tip">互联网监测引擎</div>
          <div class="time-desc">
            上一轮同步时间 <br />
            {{ weekdetail.huLianWangJianCeYinQing }}
          </div>
        </div>
      </div>
      <div class="center wrapper">
        <div class="line wrapper">
          <div class="total-count">
            <div class="detail-item">
              <div class="title">本周事件提报数</div>
              <div class="num">{{ weekdetail.benZhouTiBaoShiJianShu }}</div>
            </div>
            <div class="detail-item">
              <div class="title">事件完成审核</div>
              <div class="num">{{ weekdetail.shiJianWanChengShenHe }}</div>
            </div>
            <div class="detail-item">
              <div class="title">事件待审核</div>
              <div class="num">{{ weekdetail.shiJianDaiShenHe }}</div>
            </div>
            <div class="detail-item">
              <div class="title">本周发出复测安排</div>
              <div class="num">{{ weekdetail.benZhouFaChuFuCeAnPai }}</div>
            </div>
            <div class="detail-item">
              <div class="title">本周完成复测</div>
              <div class="num">{{ weekdetail.benZhouWanChengFuCe }}</div>
            </div>
            <div class="detail-item">
              <div class="title">待执行复测</div>
              <div class="num">{{ weekdetail.daiZhiXingFuCe }}</div>
            </div>
          </div>
        </div>
        <div class="notebook wrapper">
          <div class="main-tip wrapper">告警外发</div>
        </div>
        <div class="interfaces">
          <div class="interface-item">微信接口<span class="succesStatus" :class="{ errorStatus: weekdetail.WeiXinJieKou == '0' }"></span></div>
          <div class="interface-item">短信接口<span class="succesStatus" :class="{ errorStatus: weekdetail.duanXingJieKou == '0' }"></span></div>
          <div class="interface-item">邮件接口<span class="succesStatus" :class="{ errorStatus: weekdetail.youJianJieKou == '0' }"></span></div>
        </div>

        <!-- <div class="notebook wrapper">
          <div class="main-tip wrapper">系统漏扫引擎</div>
          <div class="time-desc">上一轮同步时间 <br/> 2021-04-22 12:00:00</div>
        </div> -->
        <div class="siem wrapper">
          <div class="main-tip wrapper">SIEM</div>
          <div class="nums">
            <span class="num1">{{ siemData[0] }}</span>
            <span class="separate">/</span>
            <span class="num2">{{ siemData[1] }}</span>
          </div>
        </div>
        <!-- <div class="alarm wrapper">
          <div class="main-tip wrapper">告警外发</div>
        </div> -->
        <div class="main wrapper">
          <div class="main-tip wrapper">运营支撑系统</div>
          <!-- <div class="nums">
            <span class="num1">0</span>
            <span class="separate">/</span>
            <span class="num2">4</span>
          </div> -->
        </div>
        <!-- <div class="interfaces">
          <div class="interface-item">微信接口<span class="succesStatus" :class="{errorStatus:(weekdetail.WeiXinJieKou)=='0'}"></span></div>
          <div class="interface-item">短信接口<span class="succesStatus" :class="{errorStatus:(weekdetail.WeiXinJieKou)=='0'}"></span></div>
          <div class="interface-item">邮件接口<span class="succesStatus" :class="{errorStatus:(weekdetail.WeiXinJieKou)=='0'}"></span></div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { selectDataList, selectEngineEquipmentRunStatus, selectSiemTotalNumList, deviceStatus } from "@/api/screen/basic";
let tabLoopTimer = null;
let scrollTimer = null;
export default {
  data() {
    return {
      baseFontSize: 16,
      source1: [
        {
          name: "FW/WAF",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "IDS/IPS",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "蜜罐",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "EDR",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
        {
          name: "其他",
          desc: "",
          descArr: [],
          currentIndex: 0,
          showDesc: false,
          num1: "",
          num2: "",
        },
      ],
      weekdetail: {},
      siemData: [],
    };
  },
  mounted() {
    this.startScroll();
    this.getWeekData();
    this.getSystemData();
    this.getSiemData();
  },
  methods: {
    startScroll() {
      scrollTimer = setInterval(() => {
        this.source1.forEach((item) => {
          if (item.descArr.length <= 1) return;
          item.desc = null;
          item.showDesc = false; //渐隐消失
          setTimeout(() => {
            item.desc = item.descArr[item.currentIndex];
            item.showDesc = true; //一定时间后重新出现
            item.currentIndex++;
            if (item.currentIndex >= item.descArr.length) {
              item.currentIndex = 0;
            }
          }, 1000);
        });
      }, 3000);
    },
    // 本周提报事件挨着的多条数据
    async getWeekData() {
      const res = await selectDataList();
      res.data.forEach((item) => {
        this.weekdetail[item.type] = item.param_value;
      });
    },
    // SIEM
    async getSiemData() {
      const res = await selectSiemTotalNumList();
      this.siemData = res.msg.split("/");
    },
    // 查询fw、waf、ids、ips、蜜罐、edr、其他类型运行情况
    async getSystemData() {
      const res = await selectEngineEquipmentRunStatus();
      this.source1 = await Promise.all(
        res.data.map(async (item) => {
          let deviceType = item.type;
          if (item.type == "其他") {
            deviceType = "other";
          } else if (item.type == "蜜罐") {
            deviceType = "mg";
          }
          let deviceName = [];
          if (item.error === "0") {
            deviceName = await this.getDevice(deviceType);
          }
          return {
            name: item.type.toUpperCase(),
            desc: deviceName[0],
            currentIndex: 0,
            descArr: deviceName,
            num1: item.runTotal,
            num2: item.enableTotal,
            showDesc: deviceName[0] ? true : false,
          };
        })
      );
    },
    // 获取异常设备
    async getDevice(type) {
      const res = await deviceStatus({ type: type.toLowerCase() });
      return res.data.map((item) => item.name);
    },
    // 轮播
    startLoop() {
      tabLoopTimer = setInterval(() => {
        this.getSystemData();
      }, 720000);
    },
  },
  beforeUnmount() {
    clearInterval(tabLoopTimer);
  },
};
</script>

<style scoped lang="scss">
$base-font-size: 16;
%center-panel {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.map-panel {
  height: 800rem / $base-font-size;
  width: 1400rem / $base-font-size;
  position: relative;
  .wrapper {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    > .nums {
      position: absolute;
      bottom: -25rem / $base-font-size;
    }
  }
  .left-bottom,
  .right-top {
    width: 567rem / $base-font-size;
    height: 354rem / $base-font-size;
    background-image: url("../../../../assets/imgs/screen/corner_panel.png");
    display: flex;
    justify-content: center;
    position: absolute;
  }
  .left-bottom {
    // 左下部分
    left: 0;
    bottom: 0;
  }
  .right-top {
    // 右上部分
    right: 65rem / $base-font-size;
    top: 0;
  }
  .nums {
    letter-spacing: 3px;
    line-height: 1.4rem;
    font-size: 14rem / $base-font-size;
    > .num1 {
      color: #fff;
    }
    > .separate {
      color: #3a70b4;
    }
    > .num2 {
      color: #cb9338;
    }
  }
  .center {
    width: 965rem / $base-font-size;
    height: 544rem / $base-font-size;
    background-image: url("../../../../assets/imgs/screen/center_panel.png");
    @extend %center-panel;
    > .line {
      width: 596rem / $base-font-size;
      height: 309rem / $base-font-size;
      background-image: url("../../../../assets/imgs/screen/line.png");
      @extend %center-panel;
    }
    > .notebook {
      width: 93rem / $base-font-size;
      height: 101rem / $base-font-size;
      background-image: url("../../../../assets/imgs/screen/dn.png");
      position: absolute;
      left: 195rem / $base-font-size;
      top: 65rem / $base-font-size;
    }
    > .siem,
    > .alarm {
      position: absolute;
      width: 88rem / $base-font-size;
      height: 90rem / $base-font-size;
      background-image: url("../../../../assets/imgs/screen/server.png");
    }
    > .siem {
      left: 220rem / $base-font-size;
      top: 330rem / $base-font-size;
    }
    > .alarm {
      left: 780rem / $base-font-size;
      top: 260rem / $base-font-size;
    }
    > .main {
      position: absolute;
      width: 150rem / $base-font-size;
      height: 201rem / $base-font-size;
      background-image: url("../../../../assets/imgs/screen/main.png");
      left: 410rem / $base-font-size;
      top: 130rem / $base-font-size;
    }
    .main-tip {
      font-size: 18rem / $base-font-size;
      width: 137rem / $base-font-size;
      height: 58rem / $base-font-size;
      color: #fff;
      position: absolute;
      background-image: url("../../../../assets/imgs/screen/main_tip.png");
      text-align: center;
      top: -60rem / $base-font-size;
      left: -24rem / $base-font-size;
      line-height: 48rem / $base-font-size;
    }
    .total-count {
      font-size: 14rem / $base-font-size;
      text-align: center;
      position: absolute;
      right: 20px;
      bottom: 33px;
      .detail-item {
        margin-top: 8px;
      }
      .title,
      .num {
        display: inline-block;
        line-height: 21rem / $base-font-size;
      }
      .title {
        width: 133rem / $base-font-size;
        background: url("../../../../assets/imgs/screen/detail_title.png") no-repeat;
        color: #45e3ff;
      }
      .num {
        width: 44rem / $base-font-size;
        background: url("../../../../assets/imgs/screen/detail_num.png") no-repeat;
        color: #fff;
        margin-left: 4px;
        background-size: 100%;
      }
    }
    .interfaces {
      position: absolute;
      color: #45e3ff;
      font-size: 14rem / $base-font-size;
      text-align: center;
      // right: 10rem / $base-font-size;
      // bottom: 160rem / $base-font-size;
      left: 41rem / $base-font-size;
      top: 73rem / $base-font-size;
      .interface-item {
        background: rgba(8, 119, 189, 0.2);
        border: 1px solid #237398;
        border-radius: 11rem / $base-font-size;
        width: 112rem / $base-font-size;
        line-height: 21rem / $base-font-size;
        margin-top: 5px;
        position: relative;
      }
      .succesStatus::after {
        content: "";
        width: 18rem / $base-font-size;
        height: 18rem / $base-font-size;
        display: block;
        background-color: #0fa74a;
        border-radius: 50%;
        position: absolute;
        right: -25rem / $base-font-size;
        top: 3rem / $base-font-size;
      }
      .errorStatus::after {
        content: "";
        width: 18rem / $base-font-size;
        height: 18rem / $base-font-size;
        display: block;
        background-color: red;
        border-radius: 50%;
        position: absolute;
        right: -25rem / $base-font-size;
        top: 3rem / $base-font-size;
      }
    }
  }
  .business-system {
    background-image: url("../../../../assets/imgs/screen/business_system.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    width: 68rem / $base-font-size;
    position: relative;
    &:not(:first-child) {
      margin-left: 9rem / $base-font-size;
    }
    > .text {
      color: #21e6fe;
      font-size: 14rem / $base-font-size;
      height: 94rem / $base-font-size;
      text-align: center;
    }
    > .desc {
      background-image: url("../../../../assets/imgs/screen/desc.png");
      position: absolute;
      min-width: 123rem / $base-font-size;
      height: 52rem / $base-font-size;
      top: -55rem / $base-font-size;
      left: 50%;
      transform: translateX(-50%);
      color: #dd7e2a;
      text-align: center;
      line-height: 49rem / $base-font-size;
      font-size: 14rem / $base-font-size;
      background-size: 100% 100%;
      white-space: nowrap;
      transition: opacity 0.5s;
      animation-name: DESC_ANI;
      animation-duration: 0.5s;
      animation-timing-function: ease-in-out;
      animation-iteration-count: infinite;
      animation-direction: alternate;
      animation-play-state: running;
      .text {
        padding: 0 16rem / $base-font-size;
      }
    }
  }
  .server-item {
    width: 80rem / $base-font-size;
    height: 82rem / $base-font-size;
    background-image: url("../../../../assets/imgs/screen/server.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    position: relative;
    &:not(:first-child) {
      margin-left: 34rem / $base-font-size;
    }
    .server-tip {
      position: absolute;
      top: -20rem / $base-font-size;
      left: 0;
      width: 120rem / $base-font-size;
      color: #45e3ff;
    }
  }
  .time-desc {
    position: absolute;
    color: #d7f9fe;
    font-size: 13rem / $base-font-size;
    width: 151rem / $base-font-size;
    bottom: -46rem / $base-font-size;
    left: -50rem / $base-font-size;
    line-height: 21rem / $base-font-size;
    &::before {
      content: "";
      background-color: #dd9b2a;
      width: 12rem / $base-font-size;
      height: 12rem / $base-font-size;
      border-radius: 50%;
      display: block;
      position: absolute;
      left: -20rem / $base-font-size;
      top: 5rem / $base-font-size;
    }
  }
}
@keyframes DESC_ANI {
  from {
    top: -55rem / $base-font-size;
  }
  to {
    top: -60rem / $base-font-size;
  }
}
</style>
