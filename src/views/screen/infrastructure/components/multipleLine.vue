<template>
  <div class="line-wrapper">
    <div class="line" ref="intelligenceLine"></div>
  </div>
</template>
<script>
import { selectCpuList, selectMemoryList, selectStorageList } from "@/api/screen/basic";
import bus from "@/utils/bus";
let tabLoopTimer = null;
let scrollTimers = [];
let cpuLine = null;
let storageLine = null;
let memoryLine = null;
const windowSize = window.innerWidth;
export default {
  props: {
    type: String,
    lineColor: String,
  },
  data() {
    return {
      xBarData: [],
      yBarData: [],
    };
  },
  watch: {
    type: {
      handler(newV, oldV) {
        if (newV === "cpu") {
          this.getCpuData();
        } else if (newV == "memory") {
          this.getMemoryData();
        } else if (newV == "storage") {
          this.getStorageData();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // 轮播    //
    this.startLoop();
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      cpuLine.resize();
      storageLine.resize();
      memoryLine.resize();
    },
    // 今日情报数量
    intelligenceLine(total, xdata, series, timeSet) {
      let self = this;
      switch (self.type) {
        case "cpu":
          cpuLine = this.$echarts.init(this.$refs.intelligenceLine);
          break;
        case "storage":
          storageLine = this.$echarts.init(this.$refs.intelligenceLine);
          break;
        default:
          memoryLine = this.$echarts.init(this.$refs.intelligenceLine);
          break;
      }
      // 控制显示最后一个图例
      // const cName = "cpu" + total;
      // const mName = "内存" + total;
      // const sName = "存储资源" + total;
      const cName = "cpu";
      const mName = "内存";
      const sName = "存储资源";
      const name = [this.type == "cpu" ? cName : this.type == "memory" ? mName : sName];

      // 滚动载取x轴时间 ---开始
      let xAxisAllData = Array.from(timeSet).sort();
      let xAxisSliceStart = 0,
        sliceLength = 5;

      let xAxisData = xAxisAllData.slice(xAxisSliceStart, xAxisSliceStart + sliceLength);

      // 滚动载取x轴时间 ---结束

      const option = {
        color: ["#066EB8", "#E6AA05", "#005BDB", "#338FF2", "#00DFE3", "#ED790C", "#E6AA05"],
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "none" },
        },
        legend: {
          data: name,
          align: "left",
          bottom: "0",
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          left: "6%",
          right: "10%",
          bottom: windowSize < 1500 ? "20%" : "10%",
          top: "6%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          axisLabel: {
            interval: 0,
            rotate: 0,
            show: true,
            color: "#FFF", //更改坐标轴文字颜色
            // formatter: function(params) {
            //     let startDate=params.substring(2,4)+'/'+params.substring(5,7)+'/'+params.substring(8,10)
            //     return startDate + '\n' + params.split(' ')[1]

            // },//
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
          data: xAxisData,
        },
        yAxis: {
          type: "value",
          axisLabel: {
            show: true,
            // interval:2,
            boundaryGap: false,
            color: "#FFF", //更改坐标轴文字颜色
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
        },
        series: series,
      };
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };

      switch (total) {
        case 0:
          self.type == "cpu" ? cpuLine.setOption(noData) : self.type == "memory" ? memoryLine.setOption(noData) : storageLine.setOption(noData);
          break;
        default:
          self.type == "cpu" ? cpuLine.setOption(option) : self.type == "memory" ? memoryLine.setOption(option) : storageLine.setOption(option);
          break;
      }
      // 多个定时器            //
      scrollTimers.push(
        setInterval(() => {
          xAxisSliceStart++;
          if (xAxisSliceStart + sliceLength > xAxisAllData.length) {
            // 已经超出右侧边界
            xAxisSliceStart = 0;
          }
          let xAxisData = xAxisAllData.slice(xAxisSliceStart, xAxisSliceStart + sliceLength);
          option.xAxis.data = xAxisData;
          self.type == "cpu" ? cpuLine.setOption(option) : self.type == "memory" ? memoryLine.setOption(option) : storageLine.setOption(option);
        }, 2000)
      );
    },

    //cpu性能监测
    async getCpuData() {
      const res = await selectCpuList();
      var ydata = [];
      var xdata = [];
      var series = [];
      var total = 0;
      const timeSet = new Set();
      res.data.forEach((element) => {
        total = res.data.length;
      });
      series = res.data.map((lineData, index) => {
        return {
          type: "line",
          name: "cpu",
          smooth: true, //弧度
          data: lineData
            .sort((item1, item2) => {
              if (item1.time > item2.time) {
                return 1;
              } else if (item1.time < item2.time) {
                return -1;
              } else {
                return 0;
              }
            })
            .map((item) => {
              // 去重操作
              timeSet.add(item.time);
              return [item.time, +item.status];
            }),
        };
      });

      this.intelligenceLine(total, xdata, series, timeSet);
    },
    //内存性能监测
    async getMemoryData() {
      const res = await selectMemoryList();
      var mydata = [];
      var mxdata = [];
      var series = [];
      var total = 0; //控制显示最后一个图例
      const timeSet = new Set();
      res.data.forEach((element, index) => {
        total = res.data.length;
      });
      series = res.data.map((lineData, index) => {
        return {
          type: "line",
          name: "内存",
          smooth: true, //弧度
          data: lineData
            .sort((item1, item2) => {
              if (item1.time > item2.time) {
                return 1;
              } else if (item1.time < item2.time) {
                return -1;
              } else {
                return 0;
              }
            })
            .map((item) => {
              // 去重操作
              timeSet.add(item.time);
              return [item.time, +item.status];
            }),
        };
      });

      this.intelligenceLine(total, mxdata, series, timeSet);
    },

    //存储资源用量监控
    async getStorageData() {
      const res = await selectStorageList();
      var sydata = [];
      var sxdata = [];
      var series = [];
      var total = 0; //控制显示最后一个图例
      const timeSet = new Set();
      res.data.forEach((element, index) => {
        total = res.data.length;
        //    剩余
        // console.log("eventBusEmit1 ", element[element.length - 1].a);

        this.$emitter.emit("remain", element[element.length - 1].a);
      });
      series = res.data.map((lineData, index) => {
        return {
          type: "line",
          name: "存储资源",
          smooth: true, //弧度
          data: lineData
            .sort((item1, item2) => {
              if (item1.time > item2.time) {
                return 1;
              } else if (item1.time < item2.time) {
                return -1;
              } else {
                return 0;
              }
            })
            .map((item) => {
              // 去重操作
              timeSet.add(item.time);
              return [item.time, +item.b];
            }),
        };
      });
      this.intelligenceLine(total, sxdata, series, timeSet);
    },
    // 轮播12分钟
    startLoop() {
      tabLoopTimer = setInterval(() => {
        // 清除动态折线滚动
        scrollTimers.forEach((timer) => {
          // 清除滚动定时器
          clearInterval(timer);
        });
        scrollTimers = [];
        if (this.type === "cpu") {
          this.getCpuData();
        } else if (this.type == "memory") {
          this.getMemoryData();
        } else if (this.type == "storage") {
          this.getStorageData();
        }
      }, 720000);
    },
  },
  beforeUnmount() {
    clearInterval(tabLoopTimer);
    // 清除滚动
    // clearInterval(scrollTimers)
    scrollTimers.forEach((timer) => {
      // 清除滚动定时器
      clearInterval(timer);
    });
    scrollTimers = [];
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.line-wrapper,
.line {
  height: 100%;
}
</style>
