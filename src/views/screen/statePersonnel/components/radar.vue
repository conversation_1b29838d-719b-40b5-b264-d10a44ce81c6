<template>
  <div class="radar-wrapper">
    <div class="bar" ref="initRadar"></div>
  </div>
</template>
<script>
let initRadar = null;
const radarData = {};
const windowSize = window.innerWidth;
import arrowDown from "@/assets/imgs/screen/arrow_down.png";
import arrowUp from "@/assets/imgs/screen/arrow_up.png";
import { selectLeiDaTuData } from "@/api/screen/person";
export default {
  name: "personBar",
  props: {
    activeName: String,
  },
  data() {
    return {
      xAxisData: [],
      yAxisData: [],
      ass: { intervalTime: "" },
    };
  },
  watch: {
    activeName: {
      handler(newV, oldV) {
        let self = this;
        if (newV === "季") {
          this.getPersonRatioData(newV);
        } else if (newV === "月") {
          this.getPersonRatioData(newV);
        } else {
          this.getPersonRatioData(newV);
        }
      },
      immediate: true,
    },
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      initRadar.resize();
    },
    async getPersonRatioData(val) {
      if (val === "季") {
        this.ass.intervalTime = "quarter";
      } else if (val === "月") {
        this.ass.intervalTime = "month";
      } else if (val === "周") {
        this.ass.intervalTime = "week";
      }
      const res = await selectLeiDaTuData(this.ass);
      if (res.code == 200) {
        // 排序
        const source = res.data;
        const sortArr = ["mttdBaiFenBi", "mttaBaiFenBi", "renWuZhiXingJiSiLv", "gaoJingPanDuanZhunQueLv", "shiJianTiBaoZhunQueLv", "shiJianChuZiLv"];
        const result = source
          .sort((item1, item2) => {
            const index1 = sortArr.findIndex((sortItem) => {
              return item1.type === sortItem;
            });
            const index2 = sortArr.findIndex((sortItem) => {
              return item2.type === sortItem;
            });
            if (index1 !== -1 && index2 !== -1) {
              if (index1 > index2) {
                return 1;
              } else if (index1 < index2) {
                return -1;
              }
            } else {
              if (index1 > index2) {
                return -1;
              } else if (index1 < index2) {
                return 1;
              }
            }
          })
          .map((item) => {
            return parseFloat(item.param_value);
          });
        res.data.forEach((rItem) => {
          if (
            rItem.type == "renWuZhiXingJiSiLv" ||
            rItem.type == "shiJianTiBaoZhunQueLv" ||
            rItem.type == "gaoJingPanDuanZhunQueLv" ||
            rItem.type == "shiJianChuZiLv"
          ) {
            radarData[rItem.type] = rItem.param_value;
          } else {
            radarData[rItem.type] = parseFloat(rItem.param_value).toFixed(2);
          }
        });
        this.initEchart(result);
      }
    },
    initEchart(dataList) {
      let self = this;
      initRadar = this.$echarts.init(this.$refs.initRadar);
      const option = {
        color: ["#67F9D8", "#FFE434", "#56A3F1", "#FF917C"],
        tooltip: {
          trigger: "item",
          confine: true,
          formatter: function (params) {
            return `
                        当前状态<br/>
                        MTTD：${radarData.pinJunJianCeShJian} 分钟<br/>
                        MTTA：${radarData.pinJunXiangYingShJian} 分钟<br/>
                        任务执行及时率: ${radarData.renWuZhiXingJiSiLv}<br/>
                        告警判断的准确率: ${radarData.gaoJingPanDuanZhunQueLv}<br/>
                        事件提报准确率: ${radarData.shiJianTiBaoZhunQueLv}<br/>
                        事件处置率: ${radarData.shiJianChuZiLv}
                       `;
          },
        },
        legend: {
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
          bottom: 0,
          data: ["当前状态"],
          show: false,
        },
        grid: {
          x: 14,
          left: "4%",
          right: "10%",
          bottom: "4%",
          top: "4%",
          containLabel: true,
        },
        radar: {
          center: ["56%", "48%"], // 外圆的位置
          radius: windowSize < 1700 ? 110 : windowSize < 1900 ? 130 : 160,
          indicator: [
            { name: "MTTD", max: 100 },
            { name: "MTTA", max: 100 },
            { name: "任务执行及时率", max: 100 },
            { name: "告警判断的准确率", max: 100 },
            { name: "事件提报准确率", max: 100 },
            { name: "事件处置率", max: 100 },
          ],

          name: {
            formatter: function (name) {
              if (name == "MTTD") {
                return `${name} {a|${radarData.pinJunJianCeShJian}} 分钟 {arrowUp|}{upText|${radarData.mttdMaxValue}}\n{arrowDown|}{upText|${radarData.mttdMinValue}}`;
              } else if (name == "MTTA") {
                return `${name} {a|${radarData.pinJunXiangYingShJian}} 分钟 {arrowUp|}{upText|${radarData.mttaMaxValue}}\n{arrowDown|}{upText|${radarData.mttaMinValue}}`;
              }
              if (name == "任务执行及时率") {
                return `${name}\n {a|${radarData.renWuZhiXingJiSiLv}}`;
              }
              if (name == "告警判断的准确率") {
                return `${name}\n {a|${radarData.gaoJingPanDuanZhunQueLv}}`;
              }
              if (name == "事件提报准确率") {
                return `${name}\n {a|${radarData.shiJianTiBaoZhunQueLv}}`;
              }
              if (name == "事件处置率") {
                return `${name}\n {a|${radarData.shiJianChuZiLv}}`;
              }
            },
            textStyle: {
              color: "#fff",
              fontSize: 14,
              lineHeight: 20,
            },
            rich: {
              a: {
                fontSize: 24,
                lineHeight: 0,
                // padding:[0,10]
              },
              arrowUp: {
                align: "right",
                height: 14,
                backgroundColor: {
                  image: arrowUp,
                },
              },
              arrowDown: {
                align: "right",
                height: 14,
                backgroundColor: {
                  image: arrowDown,
                },
              },
              upText: {
                align: "right",
                width: 26,
                fontSize: 12,
                padding: [0, 20],
              },
            },
          },
          splitArea: {
            // 坐标轴在 grid 区域中的分隔区域，默认不显示。
            show: true,
            areaStyle: {
              // 分隔区域的样式设置。
              color: ["#2E4C78", "#2E4C78", "#2E4C78", "#2E4C78", "#2E4C78"], // 画布颜色 // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
            },
          },
          splitLine: {
            lineStyle: {
              type: "solid",
              color: ["#C0C0C0", "#C0C0C0"], // 分隔线颜色
              width: 1, // 分隔线线宽
            },
          },
          itemStyle: {
            // 折线拐点标志的样式。 、
            color: "rgba(32, 232, 255, 1)",
          },
        },
        series: [
          {
            name: "当前状态",
            type: "radar",
            symbol: "circle",
            symbolSize: 10,
            color: "rgba(32, 232, 255, 1)",
            areaStyle: {
              normal: {
                color: "rgba(32, 232, 255, 1)",
              },
            },
            itemStyle: {
              // 折线拐点标志的样式。 、
              normal: {
                // 普通状态时的样式
                lineStyle: {
                  width: 1,
                },
                color: "rgba(32, 232, 255, 1)",
                // opacity:1
              },
              emphasis: {
                // 高亮时的样式
                lineStyle: {
                  width: 1,
                },
                opacity: 1,
              },
            },
            data: [
              {
                value: dataList,
                name: "",
                // 点处显示字
                label: {
                  normal: {
                    show: true,
                  },
                },
              },
            ],
          },
        ],
      };
      // / 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (dataList.length > 0) {
        initRadar.setOption(option);
      } else {
        initRadar.setOption(noData);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.radar-wrapper,
.bar {
  height: 100%;
}
</style>
