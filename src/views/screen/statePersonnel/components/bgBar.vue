<template>
  <div class="bar-wrapper">
    <div class="bar" ref="initBar"></div>
  </div>
</template>
<script>
let initBgBar = null;
import { selectTaskTimelinessRate } from "@/api/screen/person";
export default {
  name: "personBar",
  data() {
    return {
      xAxisData: [],
      yAxisData: [],
    };
  },
  mounted() {
    this.getPersonRatioData();
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      initBgBar.resize();
    },
    async getPersonRatioData() {
      const res = await selectTaskTimelinessRate();
      if (res.code == 200) {
        this.xAxisData = res.data.map((item) => {
          return item.name;
        });
        this.yAxisData = res.data.map((item) => {
          return item.rate;
        });
        this.initEchart(this.yAxisData);
      }
    },
    initEchart(dataList) {
      initBgBar = this.$echarts.init(this.$refs.initBar);
      let self = this;
      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0,0,0,0.3)",
        },
      };
      var itemStyle = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: new self.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#83bff6" },
            { offset: 1, color: "#188df0" },
          ]),
        },
        label: {
          show: true,
          position: "top",
        },
      };
      const option = {
        title: {
          text: "MTTA 构成",
          textStyle: {
            color: "#21E9FFFF",
            fontSize: 12,
            fontWeight: "500",
          },
          x: "center",
          top: 12,
          show: false,
        },
        legend: {
          data: ["及时率"],
          left: "40%",
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
          bottom: 6,
          show: true,
        },
        textStyle: {
          fontSize: 15,
          color: "#fff",
        },
        tooltip: {
          formatter: function (params) {
            let seriesName = params.seriesName ? params.seriesName + ":" : "";
            return `${params.name}<br/>${seriesName}${params.value}%`;
          },
        },
        xAxis: {
          data: self.xAxisData,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: { show: false },
          splitArea: { show: false },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            show: true,
          },
          axisTick: {
            show: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },

          splitLine: { show: false },
        },
        grid: {
          left: "10%",
          right: "18%",
          bottom: "22%",
          top: "14%",
          containLabel: false,
        },
        series: [
          {
            name: "及时率",
            type: "bar",
            barWidth: 10, //柱图宽度
            itemStyle: itemStyle,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: self.yAxisData,
            label: {
              show: true,
              position: "top",
              formatter: "{@score}%",
              textStyle: {
                color: "#03fcfe",
                fontSize: "12",
              },
            },
            // 标准线
            markLine: {
              symbol: "none",
              itemStyle: {
                normal: {
                  color: "#FFBF09",
                  label: {
                    formatter: "{c}%",
                    color: "#FFBF09",
                  },
                  lineStyle: {
                    type: "dotted",
                  },
                },
              },
              data: [{ type: "average", name: "平均值" }],
            },
            zlevel: 1,
          },
          {
            name: "背景",
            type: "bar",
            barWidth: 10,
            barGap: "-100%",
            barGategoryGap: "-100%",
            showBackground: true,
            data: [100, 100, 100],
            itemStyle: {
              normal: {
                color: "rgba(101, 115, 142, .3)",
                barBorderRadius: 10,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "rgba(101, 115, 142, .7)",
                },
              },
            },
          },
        ],
      };
      // / 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (dataList.length > 0) {
        initBgBar.setOption(option);
      } else {
        initBgBar.setOption(noData);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.bar-wrapper,
.bar {
  height: 100%;
}
</style>
