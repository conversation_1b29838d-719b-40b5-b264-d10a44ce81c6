<template>
  <div class="barWrapper">
    <div class="bar" ref="initBar"></div>
  </div>
</template>
<script>
var initTwoBar = null;
import { selectTaskExecutionRate } from "@/api/screen/person";
export default {
  name: "personBar",
  data() {
    return {
      xAxisData: [],
      yAxisData: [],
      nameData: [],
    };
  },
  mounted() {
    this.getPersonRatioData();
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      initTwoBar.resize();
    },
    async getPersonRatioData() {
      const res = await selectTaskExecutionRate();
      if (res.code == 200) {
        this.nameData = res.data.map((rItem) => {
          return rItem.name;
        });
        this.xAxisData = res.data.map((rItem) => {
          return rItem.rate;
        });
        this.yAxisData = res.data.map((rItem) => {
          return -rItem.defeatRate;
        });
        this.initEchart(this.nameData);
      }
    },
    initEchart(dataList) {
      initTwoBar = this.$echarts.init(this.$refs.initBar);
      let self = this;
      var name = ["超时完成", "及时完成"];
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "line", // 默认为直线，可选为：'line' | 'shadow'
            color: "#fff",
          },
          textStyle: {
            fontSize: 12,
          },
          formatter: function (params) {
            const dotHtml1 =
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(238, 61, 74, 1)"></span>';
            const dotHtml2 =
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:rgba(34, 100, 174, 1)"></span>';

            return `${params[0].name}<br/>${dotHtml1}${params[0].seriesName}：${Math.abs(params[0].value)}
              <br/>${dotHtml2}${params[1].seriesName}：${Math.abs(params[1].value)}`;
          },
        },
        legend: {
          left: "center",
          bottom: 0,
          show: true,
          type: "scroll",
          data: name,
          itemWidth: 20,
          itemHeight: 15,
          itemGap: 30,
          textStyle: {
            fontSize: 10,
            color: "#fff",
            fontFamily: "Microsoft YaHei",
          },
        },
        grid: [
          {
            left: "26%",
            right: "10%",
            top: "14%",
            bottom: "30%",
          },
        ],
        xAxis: [
          {
            type: "value",
            max: 100,
            min: -100,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              show: true,
              fontSize: 12,
              color: "fff",
              rotate: 0,
              interval: "auto",
              formatter: function (value) {
                return Math.abs(value);
              },
            },
            gridIndex: 0,
            // inverse: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#fff",
              },
            },
            // 网格线
            splitLine: {
              show: false,
            },
            axisTick: {
              show: true,
            },
          },
        ],
        yAxis: [
          {
            type: "category",
            offset: 20,
            axisLine: {
              show: true,
              lineStyle: {
                width: 1,
                type: "solid",
                color: "#fff",
              },
            },
            axisLabel: {
              show: true,
              fontSize: 12,
              color: "#fff",
              rotate: 0,
              interval: "auto",
            },
            data: self.nameData,
          },
        ],
        series: [
          {
            name: name[0],
            type: "bar",
            stack: "one",
            data: self.yAxisData,
            barWidth: 10,
            itemStyle: {
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new self.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: "rgba(238, 61, 74, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(177, 31, 42, 1)",
                  },
                ]),
              },
            },
            label: {
              position: "left",
              show: true,
              fontSize: 12,
              color: "rgba(233, 58, 71, 1)",
              formatter: function (param) {
                return -param.value;
              },
            },
          },
          {
            name: name[1],
            type: "bar",
            stack: "one",
            data: self.xAxisData,
            barWidth: 10,
            itemStyle: {
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: new self.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: "rgba(34, 100, 174, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(32, 232, 255, 1)",
                  },
                ]),
              },
            },
            label: {
              position: "right",
              show: true,
              fontSize: 12,
              color: "rgba(34, 176, 233, 1)",
            },
          },
        ],
      };
      // / 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (dataList.length > 0) {
        initTwoBar.setOption(option);
      } else {
        initTwoBar.setOption(noData);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.barWrapper,
.bar {
  height: 100%;
}
</style>
