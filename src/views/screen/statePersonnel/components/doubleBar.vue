<template>
  <div class="bar-wrapper">
    <div class="bar" ref="initBar"></div>
  </div>
</template>
<script>
let initBar = null;
const windowSize = window.innerWidth;
import { selectAlertAndEventAccuracy } from "@/api/screen/person";
export default {
  name: "personBar",
  data() {
    return {
      xAxisData: [],
      eventData: [],
      alarmData: [],
    };
  },
  mounted() {
    this.getPersonRatioData();
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      initBar.resize();
    },
    async getPersonRatioData() {
      const res = await selectAlertAndEventAccuracy();
      if (res.code == 200) {
        this.xAxisData = res.data.map((item) => {
          return item.name;
        });
        this.eventData = res.data.map((item) => {
          return parseFloat(item.eventRate);
        });
        this.alarmData = res.data.map((item) => {
          return parseFloat(item.alertRate);
        });
        this.initEchart(this.eventData, this.alarmData);
      }
    },
    initEchart(eventLIst, alarm) {
      initBar = this.$echarts.init(this.$refs.initBar);
      let self = this;
      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0,0,0,0.3)",
        },
      };
      var itemStyle = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: new self.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(34, 100, 174, 1)" },
            { offset: 1, color: "rgba(34, 203, 255, 1)" },
          ]),
        },
        label: {
          show: true,
          position: "top",
        },
      };
      var itemStyle2 = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: new self.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(38, 138, 117, 1)" },
            { offset: 1, color: "rgba(65, 230, 195, 1)" },
          ]),
        },
        label: {
          show: true,
          position: "top",
        },
      };
      const option = {
        title: {
          show: false,
        },
        legend: {
          data: ["告警判断准确率", "事件提报准确率"],
          left: "10%",
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
          bottom: windowSize < 1500 ? "16%" : "10%",
          show: true,
        },
        textStyle: {
          fontSize: 15,
          color: "#fff",
        },
        tooltip: {
          formatter: function (params) {
            // return params.value+'%'
            if (params.seriesName !== null) {
              return `${params.name}<br/>${params.seriesName}：${params.value}%`;
            } else {
              return `${params.name}<br/>${params.value}%`;
            }
          },
        },
        xAxis: {
          data: self.xAxisData,
          axisLabel: {
            interval: 0,
            rotate: 15,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: { show: false },
          splitArea: { show: false },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            show: true,
          },
          axisTick: {
            show: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },

          splitLine: { show: false },
        },
        grid: {
          left: "2%",
          right: "10%",
          bottom: "24%",
          top: "14%",
          containLabel: true,
        },
        series: [
          {
            name: "告警判断准确率",
            type: "bar",
            barWidth: 5, //柱图宽度
            itemStyle: itemStyle,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: self.alarmData,
            label: {
              show: false,
            },
            // 标准线
            markLine: {
              symbol: "none",
              itemStyle: {
                normal: {
                  color: "#4c9ad5",
                  label: {
                    formatter: "{c}",
                    color: "#4c9ad5",
                    padding: [-13, -20, 15, -45],
                    position: "end",
                  },
                  lineStyle: {
                    type: "dotted",
                  },
                },
              },
              data: [{ type: "average", name: "平均值" }],
            },
            zlevel: 1,
          },
          {
            name: "事件提报准确率",
            type: "bar",
            barWidth: 5, //柱图宽度
            itemStyle: itemStyle2,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: self.eventData,
            label: {
              show: false,
            },
            // 标准线
            markLine: {
              symbol: "none",
              itemStyle: {
                normal: {
                  color: "#55a48e",
                  label: {
                    formatter: "{c}",
                    color: "#55a48e",
                  },
                  lineStyle: {
                    type: "dotted",
                  },
                },
              },
              data: [{ type: "average", name: "平均值" }],
            },
            zlevel: 1,
          },
        ],
      };
      // / 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (eventLIst.length > 0 || alarmList.length > 0) {
        initBar.setOption(option);
      } else {
        initBar.setOption(noData);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.bar-wrapper,
.bar {
  height: 100%;
}
</style>
