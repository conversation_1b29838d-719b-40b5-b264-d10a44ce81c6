<template>
  <div class="bar-wrapper">
    <div class="bar" ref="initBar"></div>
  </div>
</template>
<script>
let mttaBar = null;
let mttdBar = null;
import { selectAcargeCompletionRatio, selectAvgTestimeStatistics } from "@/api/screen/person";
export default {
  data() {
    return {
      xAxisData: [],
      blueData: [],
      yellowData: [],
      redData: [],
    };
  },
  props: {
    type: String,
  },
  watch: {
    type: {
      handler(newV, oldV) {
        if (newV === "MTTD") {
          this.getSelectAvgTestimeStatistics();
        } else {
          this.getRatioData();
        }
      },
      immediate: true,
    },
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      mttaBar.resize();
      mttdBar.resize();
    },
    async getSelectAvgTestimeStatistics() {
      const res = await selectAvgTestimeStatistics();
      if (res.code == 200) {
        this.xAxisData = res.data.map((item) => {
          return item.name;
        });
        this.blueData = res.data.map((item) => {
          return parseFloat(item.blue);
        });
        this.yellowData = res.data.map((item) => {
          return parseFloat(item.yellow);
        });
        this.redData = res.data.map((item) => {
          return parseFloat(item.red);
        });
        this.initEchart(this.xAxisData);
      }
    },
    async getRatioData() {
      const res = await selectAcargeCompletionRatio();
      if (res.code == 200) {
        this.xAxisData = res.data.map((item) => {
          return item.name;
        });
        this.blueData = res.data.map((item) => {
          return parseFloat(item.blue);
        });
        this.yellowData = res.data.map((item) => {
          return parseFloat(item.yellow);
        });
        this.redData = res.data.map((item) => {
          return parseFloat(item.red);
        });
        this.initEchart(this.xAxisData);
      }
    },
    initEchart(dataList) {
      let self = this;
      if (self.type === "MTTD") {
        mttdBar = this.$echarts.init(this.$refs.initBar);
      } else {
        mttaBar = this.$echarts.init(this.$refs.initBar);
      }

      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0,0,0,0.3)",
        },
      };
      var itemStyle = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: new self.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#83bff6" },
            { offset: 1, color: "#188df0" },
          ]),
        },
      };
      var itemStyle2 = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: new self.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#FFBF09" },
            { offset: 1, color: "#F8E60C" },
          ]),
        },
      };
      var itemStyle3 = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: new self.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#ee3d4aFF" },
            { offset: 1, color: "#b11f2aFF" },
          ]),
        },
      };

      // / 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      const option = {
        title: {
          text: self.type ? "MTTD 构成" : "MTTA 构成",
          textStyle: {
            color: "#21E9FFFF",
            fontSize: 12,
            fontWeight: "500",
          },
          x: "center",
          top: 12,
        },
        legend: {
          data: ["未超时", "超时100%以内", "超时150%以内"],
          left: "0",
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
          bottom: 6,
        },
        textStyle: {
          fontSize: 15,
          color: "#fff",
        },
        tooltip: {
          formatter: function (params) {
            // return params.value+'%'
            console.info(params);
            if (params.componentType !== "markLine") {
              return `${params.name}<br/>${params.seriesName}：${params.value}%`;
            } else {
              return `平均值<br/>${params.value}%`;
            }
          },
        },
        xAxis: {
          data: self.xAxisData,
          axisLabel: {
            interval: 0,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: { show: false },
          splitArea: { show: false },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            formatter: "{value}%",
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: { show: false },
        },
        grid: {
          left: "2%",
          right: "14%",
          bottom: "14%",
          top: "14%",
          containLabel: true,
        },
        series: [
          {
            name: "未超时",
            type: "bar",
            stack: "one",
            barWidth: 10, //柱图宽度
            itemStyle: itemStyle,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: self.blueData,
          },
          {
            name: "超时100%以内",
            type: "bar",
            stack: "one",
            itemStyle: itemStyle2,
            emphasis: emphasisStyle,
            data: self.yellowData,
            markLine: {
              symbol: "none",
              itemStyle: {
                normal: {
                  color: "#FFBF09",
                  label: {
                    formatter: "{c}",
                    color: "#FFBF09",
                  },
                },
              },
              data: [{ yAxis: 100 }],
            },
          },
          {
            name: "超时150%以内",
            type: "bar",
            stack: "one",
            itemStyle: itemStyle3,
            emphasis: emphasisStyle,
            data: self.redData,
            markLine: {
              symbol: "none",
              itemStyle: {
                normal: {
                  color: "#FFBF09",
                  label: {
                    formatter: "{c}",
                    color: "#FFBF09",
                    padding: [-13, -20, 15, -45],
                    position: "end",
                  },
                },
              },
              data: [{ yAxis: 150 }],
            },
          },
        ],
      };
      switch (dataList.length) {
        case 0:
          self.type == "MTTD" ? mttdBar.setOption(noData) : mttaBar.setOption(noData);
          break;
        default:
          self.type == "MTTD" ? mttdBar.setOption(option) : mttaBar.setOption(option);
          break;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.bar-wrapper,
.bar {
  height: 100%;
}
</style>
