<template>
  <div class="bar-wrapper">
    <div class="bar" ref="initBar"></div>
  </div>
</template>
<script>
let mttaBar = null;
let mttdBar = null;
import { selectPersonalAcargeCompletionRatio, selectPersonAvgTestimeStatistics } from "@/api/screen/person";
export default {
  name: "personBar",
  data() {
    return {
      xAxisData: [],
      yAxisData: [],
    };
  },
  props: {
    type: String,
  },
  watch: {
    type: {
      handler(newV, oldV) {
        if (newV === "MTTA") {
          this.getselectPersonAvgTestimeStatistics();
        } else {
          this.getPersonRatioData();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      mttaBar.resize();
      mttdBar.resize();
    },
    async getselectPersonAvgTestimeStatistics() {
      const res = await selectPersonAvgTestimeStatistics();
      if (res.code == 200) {
        this.xAxisData = res.data.map((item) => {
          return item.name;
        });
        this.yAxisData = res.data.map((item) => {
          return item.shijian;
        });
        this.initEchart(this.yAxisData);
      }
    },
    async getPersonRatioData() {
      const res = await selectPersonalAcargeCompletionRatio();
      if (res.code == 200) {
        this.xAxisData = res.data.map((item) => {
          return item.name;
        });
        this.yAxisData = res.data.map((item) => {
          return item.val;
        });
        this.initEchart(this.yAxisData);
      }
    },
    initEchart(dataList) {
      let self = this;
      if (self.type === "MTTA") {
        mttaBar = this.$echarts.init(this.$refs.initBar);
      } else {
        mttdBar = this.$echarts.init(this.$refs.initBar);
      }

      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0,0,0,0.3)",
        },
      };
      var itemStyle = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: new self.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#83bff6" },
            { offset: 1, color: "#188df0" },
          ]),
        },
        label: {
          show: true,
          position: "top",
        },
      };
      const option = {
        title: {
          text: "MTTA 构成",
          textStyle: {
            color: "#21E9FFFF",
            fontSize: 12,
            fontWeight: "500",
          },
          x: "center",
          top: 12,
          show: false,
        },
        legend: {
          data: ["超时率"],
          left: "10%",
          textStyle: {
            color: "#fff",
          },
          bottom: 6,
          show: false,
        },
        textStyle: {
          fontSize: 15,
          color: "#fff",
        },
        tooltip: {
          // formatter:function(params){
          //     let seriesName=params.seriesName?params.seriesName+':':''
          //      return `${params.name}<br/>${seriesName}${params.value}%`
          // }
        },
        xAxis: {
          data: self.xAxisData,
          axisLabel: {
            interval: 0,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: { show: false },
          splitArea: { show: false },
        },
        yAxis: {
          type: "value",
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: "#FFF",
            },
          },

          splitLine: { show: false },
        },
        grid: {
          left: "2%",
          right: "14%",
          bottom: "14%",
          top: "14%",
          containLabel: true,
        },
        series: [
          {
            name: "超时率",
            type: "bar",
            barWidth: 10, //柱图宽度
            itemStyle: itemStyle,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: self.yAxisData,
            label: {
              show: true,
              position: "top",
              fontSize: 10,
            },
            // 标准线
            markLine: {
              symbol: "none",
              itemStyle: {
                normal: {
                  color: "#FFBF09",
                  label: {
                    formatter: "{c}",
                    color: "#FFBF09",
                  },
                  lineStyle: {
                    type: "dotted",
                  },
                },
              },
              data: [self.type ? { type: "average", name: "平均值" } : { yAxis: 240 }],
            },
            zlevel: 1,
          },
        ],
      };
      // / 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      switch (dataList.length) {
        case 0:
          self.type == "MTTA" ? mttaBar.setOption(noData) : mttdBar.setOption(noData);
          break;
        default:
          self.type == "MTTA" ? mttaBar.setOption(option) : mttdBar.setOption(option);
          break;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.bar-wrapper,
.bar {
  height: 100%;
}
</style>
