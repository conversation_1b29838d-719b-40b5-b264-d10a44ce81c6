<template>
  <component :is="windowSize < 1500 ? screenLayout : divComponent">
    <div id="box">
      <div class="wrapper">
        <div class="header">
          <div class="left">
            <img class="logo" src="@/assets/imgs/screen/logo.png" alt="" />
            <span>网络安全运营中心</span>
          </div>
          <div class="right">
            <span>{{ nowTime }}</span>
            <span>运营态势</span>
          </div>
        </div>
        <div class="content">
          <div class="con-left">
            <div class="top">
              <div class="title">运营质量雷达图</div>
              <div class="box">
                <div class="map-box">
                  <radar :activeName="activeName"></radar>
                </div>
                <div class="info">
                  <ul class="btn">
                    <li :class="activeName == '季' ? 'active' : ''" @click="changeTab('季')">季</li>
                    <li :class="activeName == '月' ? 'active' : ''" @click="changeTab('月')">月</li>
                    <li :class="activeName == '周' ? 'active' : ''" @click="changeTab('周')">周</li>
                  </ul>
                  <div class="txtInfo">
                    <div class="maxInfo">
                      <p>日均告警处理数</p>
                      <p class="rate">{{ numberOfAlarms }}</p>
                    </div>
                    <div class="maxInfo">
                      <p>日均事件提报数</p>
                      <p class="rate">{{ submitted }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="left">
                <div class="box">
                  <div class="title">事件处置率</div>
                  <ul class="box-content disposalWrapper">
                    <li>
                      <p class="eventrate">+{{ disposalRate }}<img src="@/assets/imgs/screen/arrow.png" /></p>
                      <div class="max">
                        <p>已完成处置事件</p>
                        <p class="rate">{{ disposalEvent }}</p>
                      </div>
                      <div class="max">
                        <p>处置中事件</p>
                        <p class="rate">{{ underDisposal }}</p>
                      </div>
                    </li>
                    <li>
                      <RateTable :type="'personDisposal'"></RateTable>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="right">
                <div class="box">
                  <div class="title">准确率统计</div>
                  <div class="box-content">
                    <div class="disposalWrapper eventbox">
                      <div class="max">
                        <p class="change-num-size">事件提报准确率</p>
                        <p class="rate">{{ reportingAccuracy }}</p>
                      </div>
                      <div class="max">
                        <p class="change-num-size">告警判断准确率</p>
                        <p class="rate">{{ reportiJudgment }}</p>
                      </div>
                    </div>
                    <div class="double-wrapper">
                      <DoubleBar></DoubleBar>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="con-right">
            <div class="box">
              <div class="title">MTTA</div>
              <div class="box-content">
                <div class="averageBox">
                  <Bar></Bar>
                </div>
                <div class="personBox">
                  <div class="max">
                    <p><span>MTTA</span><span style="float: right">分钟</span></p>
                    <div>
                      <span class="maxTit">{{ mttaBaiFenBi }}</span>
                      <span class="maxInfo">
                        <p>
                          <img src="@/assets/imgs/screen/arrow_up.png" /><span :title="mttaMaxValue">{{ mttaMaxValue }}</span>
                        </p>
                        <p>
                          <img src="@/assets/imgs/screen/arrow_down.png" /><span :title="mttaMinValue">{{ mttaMinValue }}</span>
                        </p>
                      </span>
                    </div>
                  </div>
                  <div class="person-wrapper">
                    <PersonBar></PersonBar>
                  </div>
                </div>
              </div>
            </div>
            <div class="box">
              <div class="title">MTTD</div>
              <div class="box-content">
                <div class="averageBox">
                  <Bar :type="'MTTD'"></Bar>
                </div>
                <div class="personBox">
                  <div class="max">
                    <p><span>MTTD</span><span style="float: right">分钟</span></p>
                    <div>
                      <span class="maxTit">{{ mttdBaiFenBi }}</span>
                      <span class="maxInfo">
                        <p><img src="@/assets/imgs/screen/arrow_up.png" />{{ mttdMaxValue }}</p>
                        <p><img src="@/assets/imgs/screen/arrow_down.png" />{{ mttdMinValue }}</p>
                      </span>
                    </div>
                  </div>
                  <PersonBar :type="'MTTA'"></PersonBar>
                </div>
              </div>
            </div>
            <div class="box">
              <div class="title">任务执行及时率</div>
              <div class="box-content">
                <div class="averageBox">
                  <BarRate :type="'MTTD'"></BarRate>
                </div>
                <div class="personBox">
                  <div class="max">
                    <p>任务执行及时率</p>
                    <p class="rate change-size">{{ implementation }}</p>
                  </div>
                  <TowBar></TowBar>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </component>
</template>
<script setup>
import divComponent from "../divComponent.vue";
import screenLayout from "../../screenLayout/index.vue";
const windowSize = window.innerWidth;
</script>
<script>
import Bar from "./components/bar.vue";
import PersonBar from "./components/personBar.vue";
import BarRate from "./components/bgBar.vue";
import TowBar from "./components/twoWayBar.vue";
import DoubleBar from "./components/doubleBar.vue";
import RateTable from "../threatOperation/components/tableShuff.vue";
import Radar from "./components/radar.vue";
import { selectLeiDaTuData } from "@/api/screen/person";
let tabLoopTimer = null;
export default {
  name: "StatePersonnel",
  components: {
    Bar,
    PersonBar,
    BarRate,
    TowBar,
    DoubleBar,
    RateTable,
    Radar,
  },
  data() {
    return {
      nowTime: "",
      activeName: "季",
      parameter: {
        intervalTime: "",
      },
      numberOfAlarms: 0,
      submitted: 0,
      disposalEvent: 0,
      underDisposal: 0,
      reportingAccuracy: 0,
      reportiJudgment: 0,
      implementation: 0,
      mttdBaiFenBi: 0,
      mttdMaxValue: 0,
      mttdMinValue: 0,
      mttaBaiFenBi: 0,
      mttaMaxValue: 0,
      mttaMinValue: 0,
      disposalRate: 0,
    };
  },
  methods: {
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month = new Date(timeStamp).getMonth() + 1 < 10 ? "0" + (new Date(timeStamp).getMonth() + 1) : new Date(timeStamp).getMonth() + 1;
      let date = new Date(timeStamp).getDate() < 10 ? "0" + new Date(timeStamp).getDate() : new Date(timeStamp).getDate();
      let hh = new Date(timeStamp).getHours() < 10 ? "0" + new Date(timeStamp).getHours() : new Date(timeStamp).getHours();
      let mm = new Date(timeStamp).getMinutes() < 10 ? "0" + new Date(timeStamp).getMinutes() : new Date(timeStamp).getMinutes();
      let ss = new Date(timeStamp).getSeconds() < 10 ? "0" + new Date(timeStamp).getSeconds() : new Date(timeStamp).getSeconds();
      this.nowTime = year + "年" + month + "月" + date + "日" + " " + hh + ":" + mm + ":" + ss;
    },
    nowTimes() {
      this.timeFormate(new Date());
      setInterval(this.nowTimes, 1000);
      this.clear();
    },
    clear() {
      clearInterval(this.nowTimes);
      this.nowTimes = null;
    },
    // 切换雷达图数据
    changeTab(val) {
      this.activeName = val;
      this.getSelectLeiDaTuData();
    },
    // 页面数据
    getSelectLeiDaTuData() {
      if (this.activeName === "季") {
        this.parameter.intervalTime = "quarter";
      } else if (this.activeName === "月") {
        this.parameter.intervalTime = "month";
      } else if (this.activeName === "周") {
        this.parameter.intervalTime = "week";
      }
      selectLeiDaTuData(this.parameter).then((res) => {
        if (res.code == 200) {
          res.data.forEach((item) => {
            if (item.type == "riJunGaoJingChuLiShu") {
              this.numberOfAlarms = item.param_value;
            } else if (item.type == "riJunShiJianTiBaoShu") {
              this.submitted = item.param_value;
            } else if (item.type == "yiWanChengCuZiShiJian") {
              this.disposalEvent = item.param_value;
            } else if (item.type == "cuZhiZhongShiJian") {
              this.underDisposal = item.param_value;
            } else if (item.type == "shiJianTiBaoZhunQueLv") {
              this.reportingAccuracy = item.param_value;
            } else if (item.type == "gaoJingPanDuanZhunQueLv") {
              this.reportiJudgment = item.param_value;
            } else if (item.type == "renWuZhiXingJiSiLv") {
              this.implementation = item.param_value;
            } else if (item.type == "pinJunJianCeShJian") {
              this.mttdBaiFenBi = item.param_value;
            } else if (item.type == "mttdMaxValue") {
              this.mttdMaxValue = item.param_value;
            } else if (item.type == "mttdMinValue") {
              this.mttdMinValue = item.param_value;
            } else if (item.type == "pinJunXiangYingShJian") {
              this.mttaBaiFenBi = item.param_value;
            } else if (item.type == "mttaMaxValue") {
              this.mttaMaxValue = parseInt(item.param_value * 100).toFixed(2) / 100;
            } else if (item.type == "mttaMinValue") {
              this.mttaMinValue = item.param_value;
            } else if (item.type == "shiJianChuZiLv") {
              this.disposalRate = item.param_value;
            }
          });
        }
      });
    },
    // 轮播
    startLoop() {
      const typeTabs = ["季", "月", "周"];
      tabLoopTimer = setInterval(() => {
        let typeTabIndex = typeTabs.indexOf(this.activeName) + 1;
        if (typeTabIndex >= typeTabs.length) {
          typeTabIndex = 0;
        }
        this.changeTab(typeTabs[typeTabIndex]);
      }, 5000);
    },
  },
  mounted() {
    this.nowTimes();
    this.getSelectLeiDaTuData();
    this.startLoop();
  },
};
</script>
<style lang="scss">
$base-font-size: 16;
// 大屏 小于 1500px 宽度屏幕 媒体查询
@media only screen and (max-width: 1500px) {
  :root {
    --mini-layout: 1080px;
  }
}
#box {
  height: var(--mini-layout, 100vh);
}
.wrapper {
  height: var(--mini-layout, 100vh);
}
.content {
  height: calc(var(--mini-layout, 100vh) - #{79rem / $base-font-size});
}
</style>
<style lang="scss" scoped>
p,
div {
  margin: 0;
  padding: 0;
}
body,
ul {
  /*组合选择器*/
  list-style: none; /*清除列表默认样式*/
  padding: 0; /*清除padding*/
  margin: 0;
}
#box {
  overflow-y: auto;
  overflow-x: hidden;
  background: #082453;
}
$base-font-size: 16;
.wrapper {
  font-family: "SourceHanSansCN-Regular";
  background: #082453;
  font-size: 14rem / $base-font-size;
}
// 头部
.header {
  padding: 0px 17rem / $base-font-size;
  $header-height: 79rem / $base-font-size;
  font-family: "FZZYJW--GB1-0";
  color: #fff;
  height: $header-height;
  border: 2px solid #073ea0;
  background: linear-gradient(180deg, #07226b 0%, #031445 100%);
  box-sizing: border-box;
  vertical-align: middle;
  .left {
    display: inline-block;
    line-height: $header-height - 4rem / $base-font-size;
    img {
      vertical-align: middle;
      width: 34rem / $base-font-size;
    }
    span {
      font-size: 28rem / $base-font-size;
      margin: 2rem / $base-font-size 19rem / $base-font-size;
      vertical-align: middle;
    }
  }
  .right {
    float: right;
    line-height: $header-height - 4rem / $base-font-size;
    > span {
      vertical-align: top;
    }
    > span:nth-child(2) {
      display: inline-block;
      width: 211rem / $base-font-size;
      height: 100%;
      background: #44b0ff;
      margin-left: 42rem / $base-font-size;
      font-size: 32rem / $base-font-size;
      color: #dff0f8;
      text-align: center;
    }
  }
}
// 内容区
.content {
  $tit-height: 30rem / $base-font-size;
  $box-width: 830rem / $base-font-size;
  $box-height: 289rem / $base-font-size;
  padding: 14rem / $base-font-size 17rem / $base-font-size;
  width: 100%;
  display: flex;
  flex: auto;
  // 标题
  .title {
    color: #dff0f8;
    text-align: left;
    border: 1px solid #0b64bf;
    border-bottom: none;
    height: 30rem / $base-font-size;
    line-height: 30rem / $base-font-size;
    padding-left: 18rem / $base-font-size;
    vertical-align: middle;
    background: linear-gradient(181deg, #050925 0%, #152b68 100%);
  }
  // 内容左侧
  > .con-left {
    flex: 1;
    margin-right: 28rem / $base-font-size;
    display: flex;
    flex-direction: column;
    height: 100%;
    > .top {
      width: 100%;
      margin-bottom: 21rem / $base-font-size;
      flex: 566;
      > .box {
        display: flex;
        width: 100%;
        height: calc(100% - #{$tit-height});
        border: 1px solid #0b64bf;
        > .map-box {
          flex: 1;
          height: 100%;
        }
        > .info {
          flex: 0 0 140rem / $base-font-size;
          margin-top: 15rem / $base-font-size;
          position: relative;
          > .btn {
            position: absolute;
            right: 20rem / $base-font-size;
            li {
              display: inline-block;
              padding: 7rem / $base-font-size 8rem / $base-font-size;
              margin-left: 6rem / $base-font-size;
              text-align: center;
              background: #7ae1ff;
              cursor: pointer;
              color: #2474fc;
              border-radius: 4px;
            }
            li:hover,
            li:active {
              color: #ffffff;
              background: #1c52ab;
            }
            .active {
              color: #ffffff;
              background: #1c52ab;
            }
          }
          > .txtInfo {
            width: 100%;
            position: absolute;
            bottom: 10rem / $base-font-size;
            right: 10rem / $base-font-size;
            .maxInfo {
              margin-top: 15rem / $base-font-size;
              padding: 6rem / $base-font-size;
              height: 64rem / $base-font-size;
              border: 1px solid #0b64bf;
              background: rgba(121, 230, 245, 0.1);
              font-size: 12rem / $base-font-size;
              color: #fff;

              .rate {
                height: 40rem / $base-font-size;
                line-height: 40rem / $base-font-size;
                font-size: 26rem / $base-font-size;
                text-align: center;
              }
            }
          }
        }
      }
    }
    > .bottom {
      $txt-height: 80rem / $base-font-size;
      display: flex;
      width: 100%;
      flex: 386;
      overflow: hidden;
      > .left {
        width: 55%;
        margin-right: 28rem / $base-font-size;
      }
      > .right {
        width: 45%;
      }
      .box {
        border: 1px solid #0b64bf;
        height: 100%;
        .box-content {
          border: 1px solid #0b64bf;
          height: calc(100% - #{$tit-height});
          // 柱状图高度
          .double-wrapper {
            $out-height: 110rem / $base-font-size;
            height: calc(100% - #{$out-height});
          }
        }

        .disposalWrapper {
          display: flex;
          width: 100%;
          .eventrate {
            margin-top: 30rem / $base-font-size;
            font-size: 30rem / $base-font-size;
            color: rgba(68, 182, 35, 1);
            text-align: center;
            height: $txt-height;
            line-height: $txt-height;
            vertical-align: middle;
            > img {
              width: 20rem / $base-font-size;
              margin-left: 10rem / $base-font-size;
            }
          }
          .max {
            margin: 30rem / $base-font-size 10% 0 10%;
            padding: 6rem / $base-font-size;
            width: 80%;
            height: $txt-height;
            border: 1px solid #0b64bf;
            background: rgba(121, 230, 245, 0.1);
            font-size: 12rem / $base-font-size;
            color: #fff;
            .maxTit {
              font-size: 24rem / $base-font-size;
              display: inline-block;
              vertical-align: middle;
            }
            .rate {
              height: 50rem / $base-font-size;
              line-height: 50rem / $base-font-size;
              font-size: 30rem / $base-font-size;
              text-align: center;
            }
          }
          li:nth-child(1) {
            display: inline-block;
            width: 40%;
            height: 100%;
          }
          li:nth-child(2) {
            display: inline-block;
            width: 60%;
            height: 100%;
          }
        }
      }
    }
  }
  // 内容右侧
  > .con-right {
    overflow: hidden;
    $box-height: 320rem / $base-font-size;
    flex: 0 0 $box-width;
    display: flex;
    flex-direction: column;
    height: 100%;
    .box {
      margin-bottom: 11rem / $base-font-size;
      flex: $box-height;
      overflow: hidden;
      .box-content {
        display: flex;
        width: 100%;
        border: 1px solid #0b64bf;
        height: calc(100% - #{30rem / $base-font-size});
        > .averageBox {
          width: 50%;
          height: 100%;
        }
        > .personBox {
          width: 50%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          > .max {
            margin-left: auto; //右对齐
            margin-right: 16rem / $base-font-size;
            margin-top: 30rem / $base-font-size;
            padding: 6rem / $base-font-size;
            min-width: 160rem / $base-font-size;
            height: 80rem / $base-font-size;
            border: 1px solid #0b64bf;
            background: rgba(121, 230, 245, 0.1);
            font-size: 12rem / $base-font-size;
            color: #fff;
            .maxTit {
              font-size: 22rem / $base-font-size;
              display: inline-block;
              vertical-align: middle;
            }
            .rate {
              height: 50rem / $base-font-size;
              line-height: 50rem / $base-font-size;
              font-size: 30rem / $base-font-size;
              text-align: center;
            }
            .maxInfo {
              padding: 6rem / $base-font-size;
              display: inline-block;
              margin-left: 16rem / $base-font-size;
              height: 50rem / $base-font-size;
              vertical-align: middle;
              font-size: 10rem / $base-font-size;

              > p {
                span {
                  display: inline-block;
                  max-width: 56rem / $base-font-size;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  vertical-align: middle;
                }
              }
              img {
                display: inline-block;
                width: 16rem / $base-font-size;
                height: 16rem / $base-font-size;
                margin-right: 10rem / $base-font-size;
                vertical-align: middle;
              }
            }
          }
          > .person-wrapper {
            height: calc(100% - #{80rem / $base-font-size});
          }
        }
      }
    }
    .box:last-child {
      margin: 0;
    }
  }
}
</style>
