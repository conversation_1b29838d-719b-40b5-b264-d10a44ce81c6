<template>
  <component :is="windowSize < 1500 ? screenLayout : divComponent">
    <div class="wrapper">
      <div class="header">
        <div class="left">
          <img class="logo" src="@/assets/imgs/screen/logo.png" alt="" />
          <span>网络安全运营中心</span>
        </div>
        <div class="right">
          <span>{{ nowTime }}</span>
          <span>运营态势</span>
        </div>
      </div>
      <div class="content">
        <div class="con-top">
          <div class="left">
            <div class="box">
              <div class="title">本日告警排名</div>
              <div class="box-content">
                <Bar :startColor="startColor" :endColor="endColor"></Bar>
              </div>
            </div>
            <div class="box">
              <div class="title">告警监测走势图</div>
              <div class="box-content">
                <FigureLine></FigureLine>
              </div>
            </div>
          </div>
          <div class="main">
            <div class="top">
              <div class="box">
                <div class="title">近30天日志量</div>
                <div class="box-info">
                  {{ filterNum(count) }}<span class="count">{{ filterCount(count) }}</span>
                </div>
              </div>
              <div class="box">
                <div class="title">近30天告警量</div>
                <div class="box-info">{{ alertNum }}</div>
              </div>
              <div class="box">
                <div class="title">近30天事件量</div>
                <div class="box-info">{{ eventNum }}</div>
              </div>
              <div class="box">
                <div class="title">事件处置率</div>
                <div class="box-info">{{ eventHandLingRate }}</div>
              </div>
            </div>
            <div class="mapBox">
              <!-- 绍兴 -->
              <img src="@/assets/imgs/screen/quality.png" alt="" />
              <Map></Map>
            </div>
          </div>
          <div class="right">
            <div class="box">
              <div class="title">攻击类型排名</div>
              <div class="box-content">
                <!-- <AffectBar  :startColor="ipStartColor" :endColor="ipEndColor"></AffectBar> -->
                <Bar :type="'affact'" :startColor="attackStartColor" :endColor="attackEndColor"></Bar>
              </div>
            </div>
            <div class="box">
              <div class="title">受影响业务系统排名</div>
              <div class="box-content">
                <Bar :type="'affect'" :startColor="affectStartColor" :endColor="affectEndColor"></Bar>
              </div>
            </div>
          </div>
        </div>
        <div class="con-bottom">
          <div class="tab-box">
            <div class="title">实时日志状态</div>
            <div class="table">
              <table-shuff :type="'journal'"></table-shuff>
            </div>
          </div>
          <div class="tab-box">
            <div class="title">待分析告警表</div>
            <!-- 绍兴 -->
            <!-- <div class="title">告警列表</div> -->
            <div class="table">
              <table-shuff :type="'analysis'"></table-shuff>
            </div>
          </div>
          <div class="tab-box">
            <div class="title">事件处置详情</div>
            <div class="table">
              <table-shuff :type="'disposal'"></table-shuff>
            </div>
          </div>
        </div>
        <div class="bottom-line">
          <div class="title">近一日受攻击态势</div>
          <div class="line-box">
            <FigureLine :type="'attack'" :lineColor="'#0E9CFF'"></FigureLine>
          </div>
        </div>
      </div>
    </div>
  </component>
</template>
<script setup>
import divComponent from "../divComponent.vue";
import screenLayout from "../../screenLayout/index.vue";
</script>
<script>
import Bar from "./components/bar.vue";
import AffectBar from "./components/affectBar.vue";
import FigureLine from "./components/line.vue";
import TableShuff from "./components/tableShuff.vue";
import Map from "./components/map.vue";
import { alarmQuantity, eventQuantity, eventHandlingRate, logVolume } from "@/api/screen/screen";
const windowSize = window.innerWidth;
export default {
  name: "ThreatOperation",
  components: {
    Bar,
    AffectBar,
    FigureLine,
    TableShuff,
    Map,
    // screenLayout,
    // divComponent,
  },
  created() {
    this.getAttackSourceIPaddressRanking();
    console.info(this.$echarts);
  },
  data() {
    return {
      startColor: "#2072D8",
      endColor: "#23D6FF",
      ipStartColor: "#006AFF",
      ipEndColor: "#00D5FF",
      affectStartColor: "#FFBF09",
      affectEndColor: "#F8E60C",
      attackStartColor: "#C85B1C",
      attackEndColor: "#FDAB00",
      nowTime: "",
      alertNum: 0,
      eventNum: 0,
      eventHandLingRate: 0,
      count: 0,
    };
  },
  filters: {},
  methods: {
    filterNum(value) {
      if (value > 10000) {
        return (value / 10000).toFixed(2);
      } else {
        return value;
      }
    },
    filterCount(value) {
      if (value > 10000) {
        return "万";
      } else {
        return "";
      }
    },
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month = new Date(timeStamp).getMonth() + 1 < 10 ? "0" + (new Date(timeStamp).getMonth() + 1) : new Date(timeStamp).getMonth() + 1;
      let date = new Date(timeStamp).getDate() < 10 ? "0" + new Date(timeStamp).getDate() : new Date(timeStamp).getDate();
      let hh = new Date(timeStamp).getHours() < 10 ? "0" + new Date(timeStamp).getHours() : new Date(timeStamp).getHours();
      let mm = new Date(timeStamp).getMinutes() < 10 ? "0" + new Date(timeStamp).getMinutes() : new Date(timeStamp).getMinutes();
      let ss = new Date(timeStamp).getSeconds() < 10 ? "0" + new Date(timeStamp).getSeconds() : new Date(timeStamp).getSeconds();
      this.nowTime = year + "年" + month + "月" + date + "日" + " " + hh + ":" + mm + ":" + ss;
    },
    nowTimes() {
      this.timeFormate(new Date());
      setInterval(this.nowTimes, 1000);
      this.clear();
    },
    clear() {
      clearInterval(this.nowTimes);
      this.nowTimes = null;
    },
    // 获取数据
    getAttackSourceIPaddressRanking() {
      // 近30天告警量
      alarmQuantity().then((res) => {
        this.alertNum = res.data.alertNum;
      });
      // 近30天事件量
      eventQuantity().then((res) => {
        this.eventNum = res.data.eventNum;
      });
      // 事件处置率
      eventHandlingRate().then((res) => {
        this.eventHandLingRate = res.data.eventHandLingRate;
      });
      // 近30天日志量
      logVolume().then((res) => {
        this.count = res.data.count;
      });
    },
  },
  mounted() {
    this.nowTimes();
  },
};
</script>
<style lang="scss">
$base-font-size: 16;
// 大屏 小于 1500px 宽度屏幕 媒体查询
@media only screen and (max-width: 1500px) {
  :root {
    --mini-layout: 1080px;
  }
}
#box {
  height: var(--mini-layout, 100vh);
}
.wrapper {
  height: var(--mini-layout, 100vh);
}
.content {
  height: calc(var(--mini-layout, 100vh) - #{80rem / $base-font-size});
}
</style>
<style lang="scss" scoped>
@import "@/assets/style/screen.scss";
p,
div {
  margin: 0;
  padding: 0;
}
$base-font-size: 16;
.wrapper {
  font-family: "SourceHanSansCN-Regular";
  background: #082453;
  font-size: 14rem / $base-font-size;
  display: flex;
  flex-direction: column;
}
// 头部
.header {
  padding: 0px 17rem / $base-font-size;
  $header-height: 80rem / $base-font-size;
  font-family: "FZZYJW--GB1-0";
  color: #fff;
  height: $header-height;
  border: 2px solid #073ea0;
  background: linear-gradient(180deg, #07226b 0%, #031445 100%);
  box-sizing: border-box;
  .left {
    display: inline-block;
    line-height: $header-height - 4rem / $base-font-size;
    img {
      vertical-align: middle;
      width: 34rem / $base-font-size;
    }
    span {
      font-size: 28rem / $base-font-size;
      margin: 2rem / $base-font-size 19rem / $base-font-size;
      vertical-align: middle;
    }
  }
  .right {
    float: right;
    line-height: $header-height - 4rem / $base-font-size;
    > span {
      vertical-align: top;
    }
    > span:nth-child(2) {
      display: inline-block;
      width: 211rem / $base-font-size;
      height: 100%;
      background: #44b0ff;
      margin-left: 42rem / $base-font-size;
      font-size: 32rem / $base-font-size;
      color: #dff0f8;
      text-align: center;
    }
  }
}
// 内容区
.content {
  $header-height: 80rem / $base-font-size;
  display: flex;
  flex-direction: column;
  padding: 0px 17rem / $base-font-size;
  // 标题
  .title {
    color: #dff0f8;
    text-align: left;
    border: 1px solid #0b64bf;
    border-bottom: none;
    height: 30rem / $base-font-size;
    line-height: 30rem / $base-font-size;
    padding-left: 18rem / $base-font-size;
    vertical-align: middle;
    background: linear-gradient(181deg, #050925 0%, #152b68 100%);
    box-sizing: border-box;
  }
  .count {
    font-size: 14px;
    display: inline-block;
    margin-left: 3px;
  }
  // 上部分
  > .con-top {
    width: 100%;
    $box-width: 353rem / $base-font-size;
    $box-height: 228rem / $base-font-size;
    display: flex;
    flex: 535;
    height: 0; //防止溢出
    padding-top: 21rem / $base-font-size;
    color: #dff0f8;
    > .left {
      flex: 0 0 $box-width;
    }
    > .left,
    > .right {
      width: 0;
      display: flex;
      flex-direction: column;
      height: 100%;
      $bar-height: 30rem / $base-font-size;
      .box {
        flex: 258;
        margin-bottom: 9rem / $base-font-size;
        .box-content {
          border: 1px solid #0b64bf;
          height: calc(100% - #{$bar-height});
        }
      }
    }

    > .right {
      flex: 0 0 $box-width;
    }
    > .main {
      $box-width: 260rem / $base-font-size;
      $box-height: 65rem / $base-font-size;
      flex: 1;
      padding: 0 23rem / $base-font-size;
      display: flex;
      flex-direction: column;
      > .top {
        // flex:
        width: 100%;
        display: flex;
        flex: 95;
        justify-content: space-around;
        .box {
          width: 25%;
          margin-left: 30rem / $base-font-size;
          display: flex;
          flex-direction: column;
          .box-info {
            font-family: Courier New;
            font-weight: bold;
            border: 1px solid #0b64bf;
            font-size: 32rem / $base-font-size;
            height: 65rem / $base-font-size;
            line-height: 65rem / $base-font-size;
            text-align: center;
            vertical-align: middle;
          }
        }
        .box:nth-child(1) {
          margin: 0;
        }
      }
      > .mapBox {
        width: 100%;
        // height: 89%;
        flex: 409;
        position: relative;
        // 绍兴
        // position: relative;
        // img {
        //   position: absolute;
        //   left: 0;
        //   top: 0;
        // }
        > img {
          position: absolute;
          top: 10rem / $base-font-size;
          display: inline-block;
          width: 305rem / $base-font-size;
          height: 61rem / $base-font-size;
        }
      }
    }
  }
  // 下部分
  > .con-bottom {
    $box-height: 228rem / $base-font-size;
    width: 100%;
    padding-top: 9rem / $base-font-size;
    height: 0;
    flex: 258;
    display: flex;
    justify-content: space-around;
    .tab-box {
      margin-left: 42rem / $base-font-size;
      width: 33.3%;
      > .table {
        padding: 0 16rem / $base-font-size;
        height: calc(100% - #{30rem / $base-font-size});
        border: 1px solid #0b64bf;
      }
    }
    .tab-box:nth-child(1) {
      margin: 0;
    }
  }
}
.bottom-line {
  $line-height: 30rem / $base-font-size;
  padding: 14rem / $base-font-size 0;
  width: 100%;
  flex: 150;
  > .line-box {
    padding: 8rem / $base-font-size;
    border: 1px solid #0b64bf;
    height: calc(100% - #{$line-height});
  }
}
</style>
