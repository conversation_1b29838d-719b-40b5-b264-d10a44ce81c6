<template>
  <div class="bar-wrapper">
    <div class="bar" ref="intelligenceBar"></div>
  </div>
</template>
<script>
let intelligenceBarChart = null;
let affectBussinessBar = null;
let affactBar = null;
import { alarmRankingToday, rankingOfAffected, attackTypeRanking } from "@/api/screen/screen";
export default {
  props: {
    startColor: String,
    endColor: String,
    type: String,
  },
  data() {
    return {
      xBarData: [],
      yBarData: [],
    };
  },
  watch: {
    type: {
      handler(newV, oldV) {
        if (newV === "affect") {
          this.getAffectData();
        } else if (newV === "affact") {
          this.getAttackTypeRanking();
        } else {
          this.getAlarmRankingToday();
        }
      },
      immediate: true,
    },
  },
  created() {
    // 本日告警排名
  },
  mounted() {
    // 监听窗口变化 - 同时刷新
    window.addEventListener("resize", function () {
      affectBussinessBar.resize();
      intelligenceBarChart.resize();
      affactBar.resize();
    });
  },
  methods: {
    // 攻击类型排名
    async getAttackTypeRanking() {
      const res = await attackTypeRanking();
      this.xBarData = res.data.map((rItem) => {
        return rItem.count;
      });
      this.yBarData = res.data.map((rItem) => {
        return rItem.content;
      });
      this.intelligenceBar(this.xBarData);
    },
    // 获取受影响业务系统排名
    async getAffectData() {
      const res = await rankingOfAffected();
      this.xBarData = res.data.map((rItem) => {
        return rItem.count;
      });
      this.yBarData = res.data.map((rItem) => {
        return rItem.assetName;
      });
      this.intelligenceBar(this.xBarData);
    },
    //本日告警排名
    async getAlarmRankingToday() {
      const res = await alarmRankingToday();
      this.xBarData = res.data.map((rItem) => {
        return rItem.count;
      });
      this.yBarData = res.data.map((rItem) => {
        return rItem.title;
      });
      this.intelligenceBar(this.xBarData);
    },
    // 加载图
    intelligenceBar(dataList) {
      let self = this;
      switch (self.type) {
        case "affect":
          affectBussinessBar = this.$echarts.init(this.$refs.intelligenceBar);
          break;
        case "affact":
          affactBar = this.$echarts.init(this.$refs.intelligenceBar);
          break;
        default:
          intelligenceBarChart = this.$echarts.init(this.$refs.intelligenceBar);
          break;
      }
      // 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["数量"],
          align: "left",
          bottom: 0,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          left: "2%",
          right: "8%",
          bottom: "14%",
          top: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],

          axisLabel: {
            margin: 10,
            show: true,
            color: "#FFF", //更改坐标轴文字颜色
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
        },
        yAxis: {
          triggerEvent: true,
          type: "category",
          inverse: true, //倒叙
          axisLabel: {
            show: true,
            fontSize: 10,
            color: "#FFF", //更改坐标轴文字颜色
            formatter: function (value) {
              if (value.length > 8) {
                value = value.slice(0, 8) + "...";
              }
              return value;
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          data: this.yBarData,
        },
        series: {
          name: "数量",
          type: "bar",
          barWidth: 10, //柱图宽度
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(
              0,
              0,
              1,
              0,
              [
                {
                  offset: 0,
                  color: this.startColor, // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: this.endColor, // 100% 处的颜色
                },
              ],
              false
            ),
          },
          data: self.xBarData,
        },
      };
      switch (dataList.length) {
        case 0:
          self.type == "affect"
            ? affectBussinessBar.setOption(noData)
            : self.type == "affact"
            ? affactBar.setOption(noData)
            : intelligenceBarChart.setOption(noData);
          break;
        default:
          self.type == "affect"
            ? affectBussinessBar.setOption(option)
            : self.type == "affact"
            ? affactBar.setOption(option)
            : intelligenceBarChart.setOption(option);
          break;
      }
      // this.extension(intelligenceBarChart);
    },
    extension(intelligenceBarChart) {
      //判断是否创建过div框,如果创建过就不再创建了
      var id = document.getElementById("extension");
      if (!id) {
        var div = "<div id = 'extension' sytle=\"display:none\"></div>";
        $("html").append(div);
      }
      intelligenceBarChart.on("mouseover", function (params) {
        if (params.componentType == "xAxis") {
          $("#extension")
            .css({
              position: "absolute",
              color: "black",
              //"border":"solid 2px white",
              "font-family": "Arial",
              "font-size": "20px",
              padding: "5px",
              display: "inline",
            })
            .text(params.value);

          $("html").mousemove(function (event) {
            var xx = event.pageX - 30;
            var yy = event.pageY + 20;
            $("#extension").css("top", yy).css("left", xx);
          });
        }
      });

      intelligenceBarChart.on("mouseout", function (params) {
        if (params.componentType == "xAxis") {
          $("#extension").css("display", "none");
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.bar-wrapper,
.bar {
  height: 100%;
}
</style>
