<template>
  <div class="shuff-wrapper">
    <div class="table-wrapper">
      <!-- 告警 -->
      <table class="table-content" v-if="this.type == 'analysis'">
        <thead class="title">
          <tr>
            <th>告警名称</th>
            <th>源IP</th>
            <th>目的IP</th>
            <th>状态</th>
            <th>时间</th>
          </tr>
        </thead>
        <tbody class="tableBody" ref="alarmBodyWrapper">
          <tr v-for="(item, index) in alarmData" :key="index" class="mouse">
            <td :title="item.title">{{ filterVal(item.title) }}</td>
            <td :title="item.csrcip">{{ filterVal(item.csrcip) }}</td>
            <td :title="item.cdstip">{{ filterVal(item.cdstip) }}</td>
            <td :title="item.status">{{ filterVal(item.status) }}</td>
            <td :title="item.get_time">{{ filterVal(item.get_time) }}</td>
          </tr>
        </tbody>
      </table>
      <!--事件  -->
      <table class="table-content" v-if="this.type == 'disposal'">
        <thead class="title">
          <tr>
            <th>事件名称</th>
            <th>响应时长</th>
            <th>定性时长</th>
            <th class="sWidth">进度</th>
            <th class="sWidth">等级</th>
            <th class="width60">事件结论</th>
            <th>时间</th>
          </tr>
        </thead>
        <tbody class="tableBody" ref="eventBodyWrapper">
          <tr v-for="(eItem, eIndex) in eventData" :key="eIndex" class="mouse">
            <td :title="eItem.eventName">{{ filterVal(eItem.eventName) }}</td>
            <td :title="eItem.mtta">{{ filterVal(eItem.mtta) }}</td>
            <td :title="eItem.mttd">{{ filterVal(eItem.mttd) }}</td>
            <td class="sWidth" :title="eItem.jindu">{{ filterVal(eItem.jindu) }}</td>
            <td class="sWidth" :title="eItem.dengji">{{ filterVal(eItem.dengji) }}</td>
            <td class="width60" :title="unescape(eItem.status)">{{ unescape(eItem.status) }}</td>
            <td :title="filterVal(eItem.createTime)">{{ filterVal(eItem.createTime) }}</td>
          </tr>
        </tbody>
      </table>
      <!-- 人员状态处置 -->
      <table class="table-content" v-if="this.type === 'personDisposal'">
        <thead class="title personTit">
          <tr>
            <th>事件名称</th>
            <th>处置进度</th>
            <th>已处置时长</th>
          </tr>
        </thead>
        <tbody class="tableBody" ref="personBodyWrapper">
          <tr v-for="(zItem, zIndex) in personDisData" :key="zIndex" class="mouse">
            <td :title="zItem.eventNo">{{ filterVal(zItem.eventNo) }}</td>
            <td :title="zItem.progress">{{ filterVal(zItem.progress) }}</td>
            <td :title="zItem.diff">{{ filterVal(zItem.diff) }}</td>
          </tr>
        </tbody>
      </table>
      <!-- 日志 -->
      <table class="table-content" v-if="this.type === 'journal'">
        <thead class="title">
          <tr>
            <th>日志名称</th>
            <th class="width60">日志类型</th>
            <th>源地址</th>
            <th>目的地址</th>
            <th>发现时间</th>
            <th class="width60">合并数量</th>
          </tr>
        </thead>
        <tbody class="tableBody" ref="realTimeLog">
          <tr v-for="(cstem, csndex) in realTime" :key="csndex" class="mouse">
            <td :title="cstem.cevent_name">{{ filterVal(cstem.cevent_name) }}</td>
            <td class="width60" :title="cstem.cevent_type">{{ filterVal(cstem.cevent_type) }}</td>
            <td :title="cstem.csrcip">{{ filterVal(cstem.csrcip) }}</td>
            <td :title="cstem.cdstip">{{ filterVal(cstem.cdstip) }}</td>
            <td :title="cstem.loccur_time">{{ cstem.loccur_time }}</td>
            <td class="width60" :title="cstem.imerge_count">{{ filterVal(cstem.imerge_count) }}</td>
          </tr>
        </tbody>
      </table>
      <!-- 业务系统漏洞实时状态 -->
      <table class="table-content" v-if="this.type === 'bussiness'">
        <thead class="title">
          <tr>
            <th>漏洞标题</th>
            <th>漏洞类别</th>
            <th>漏洞级别</th>
            <th>受影响资产</th>
            <th>处置状态</th>
            <th>更新时间</th>
          </tr>
        </thead>
        <tbody class="tableBody vulnBody" ref="businessSystem">
          <tr v-for="(qItem, bIndex) in businessData" :key="bIndex" class="mouse">
            <td :title="qItem.name">{{ filterVal(qItem.name) }}</td>
            <td :title="qItem.categoryName">{{ filterVal(qItem.categoryName) }}</td>
            <td :title="qItem.levelName">{{ filterVal(qItem.levelName) }}</td>
            <td :title="qItem.assetsName">{{ filterVal(qItem.assetsName) }}</td>
            <td :title="qItem.disposalStatus">{{ filterVal(qItem.disposalStatus) }}</td>
            <td :title="qItem.firstSubmitTime">{{ filterVal(qItem.firstSubmitTime) }}</td>
          </tr>
        </tbody>
      </table>
      <!-- 基础资源漏洞实时状态 -->
      <table class="table-content" v-if="this.type === 'basic'">
        <thead class="title">
          <tr>
            <th>漏洞标题</th>
            <th>漏洞级别</th>
            <th>受影响资产</th>
            <th>处置状态</th>
            <th>更新时间</th>
          </tr>
        </thead>
        <tbody class="tableBody vulnBody" ref="basicResources">
          <tr v-for="(bItem, bIndex) in basicData" :key="bIndex" class="mouse">
            <td :title="bItem.title">{{ filterVal(bItem.title) }}</td>
            <td :title="bItem.level">{{ filterVal(bItem.level) }}</td>
            <td :title="bItem.assetsName">{{ filterVal(bItem.assetsName) }}</td>
            <td :title="bItem.disposalStatus">{{ filterVal(bItem.disposalStatus) }}</td>
            <td :title="bItem.findTime">{{ filterVal(bItem.findTime) }}</td>
          </tr>
        </tbody>
      </table>
      <!-- 基础架构日志 -->
      <table class="table-content" v-if="this.type === 'log'">
        <thead class="title">
          <tr>
            <th>设备地址</th>
            <th>设备类型</th>
            <th>近24h数量</th>
            <th>上24h环比</th>
          </tr>
        </thead>
        <tbody class="tableBody basicBody" ref="basicLog">
          <tr v-for="(bItem, bIndex) in logData" :key="bIndex">
            <td :title="bItem.cdev_ip">{{ filterVal(bItem.cdev_ip) }}</td>
            <td :title="bItem.cdev_type">{{ filterVal(bItem.cdev_type) }}</td>
            <td :title="bItem.log_num_twenty_four">{{ filterVal(bItem.log_num_twenty_four) }}</td>
            <td :title="bItem.ringRatio">{{ filterVal(bItem.ringRatio) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script>
let scrollTimers = [];
import { alarmTableToBeAnalyzed, selectEventHandDetail, getRealTimeLogStatus } from "@/api/screen/screen";
import { selectUnCloseEvent } from "@/api/screen/person";
import { selectBusinessVulnRealTimeStatusList, selectResourceVulnRealTimeStatusList } from "@/api/screen/vuln";
import { selectLogList } from "@/api/screen/basic";
export default {
  props: {
    type: String,
  },
  data() {
    return {
      alarmData: [],
      personDisData: [],
      eventData: [],
      realTime: [],
      businessData: [],
      basicData: [],
      logData: [],
    };
  },

  watch: {
    type: {
      handler(newV, oldV) {
        if (newV === "analysis") {
          this.geTalarmTableToBeAnalyzed();
        } else if (newV === "personDisposal") {
          this.getPersonHandlingDetails();
        } else if (newV === "disposal") {
          this.getEventHandlingDetails();
        } else if (newV === "journal") {
          this.realTimeLogStatus();
        } else if (newV === "bussiness") {
          this.getbusinessData();
        } else if (newV === "basic") {
          this.getBasicResourceData();
        } else if (newV == "log") {
          this.getBasicLogData();
        }
      },
      immediate: true,
    },
  },
  mounted() {},
  unmounted() {
    scrollTimers.forEach((timer) => {
      // 清除滚动定时器
      clearInterval(timer);
    });
    scrollTimers = [];
  },
  methods: {
    filterVal(val) {
      if (!val) {
        return "--";
      } else {
        return val;
      }
    },
    unescape(html) {
      if (!html) {
        return "--";
      } else {
        let str = html
          .replace(html ? /&(?!#?\w+;)/g : /&/g, "&amp;")
          .replace(/&lt;/g, "<")
          .replace(/&gt;/g, ">")
          .replace(/&#40;/g, "(")
          .replace(/&#41;/g, ")")
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'");
        return str;
      }
    },
    moveTableContent(refName, dataKey) {
      return () => {
        // console.log('moveTableContent执行', refName)
        const bodyWrapper = this.$refs[refName];
        // 容器高度
        let leng = this[dataKey].length;
        const containerHeight = bodyWrapper.getBoundingClientRect().height;
        const trItem = bodyWrapper.children[0];
        if (trItem) {
          const totalHeight = trItem.getBoundingClientRect().height * leng;
          if (bodyWrapper.scrollTop + containerHeight >= totalHeight - 5) {
            // 已经滚动至底部
            bodyWrapper.scrollBy({
              // 回到顶部
              top: -bodyWrapper.scrollTop,
              behavior: "smooth",
            });
          } else {
            // 向下滚动一行的高度
            bodyWrapper.scrollBy({
              top: 40,
              behavior: "smooth",
            });
          }
        }
      };
    },
    //待分析告警表
    async geTalarmTableToBeAnalyzed() {
      const res = await alarmTableToBeAnalyzed();
      this.alarmData = res.data;
      scrollTimers.push(setInterval(this.moveTableContent.call(this, "alarmBodyWrapper", "alarmData"), 2000));
    },
    // 事件处置详情
    async getEventHandlingDetails() {
      const res = await selectEventHandDetail();
      this.eventData = res.data.eventHandDetailMap;
      scrollTimers.push(setInterval(this.moveTableContent.call(this, "eventBodyWrapper", "eventData"), 2000));
    },
    // 人员状态大屏
    // 事件处置详情
    async getPersonHandlingDetails() {
      const res = await selectUnCloseEvent();
      this.personDisData = res.data;
      scrollTimers.push(setInterval(this.moveTableContent.call(this, "personBodyWrapper", "personDisData"), 2000));
    },
    // 实时日志状态
    async realTimeLogStatus() {
      const res = await getRealTimeLogStatus();
      this.realTime = res.data;
      scrollTimers.push(setInterval(this.moveTableContent.call(this, "realTimeLog", "realTime"), 2000));
    },
    // 业务系统漏洞实时状态
    async getbusinessData() {
      const res = await selectBusinessVulnRealTimeStatusList();
      this.businessData = res.data.businessVulnList;
      scrollTimers.push(setInterval(this.moveTableContent.call(this, "businessSystem", "businessData"), 2000));
    },
    //
    async getBasicResourceData() {
      const res = await selectResourceVulnRealTimeStatusList();
      this.basicData = res.data.resourceVulnList;
      scrollTimers.push(setInterval(this.moveTableContent.call(this, "basicResources", "basicData"), 2000));
    },
    //
    async getBasicLogData() {
      const res = await selectLogList();
      this.logData = res.data;
      scrollTimers.push(setInterval(this.moveTableContent.call(this, "basicLog", "logData"), 2000));
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
%table-row {
  background-color: #010a41;
  color: #fff;
}
p {
  margin: 0;
  padding: 0;
}
.shuff-wrapper {
  width: 100%;
  height: 100%;
  padding-bottom: 10rem / $base-font-size;
  .table-wrapper {
    color: #fff;
    height: 100%;
    table.table-content {
      width: 100%;
      height: 100%;
      display: block;
      text-align: center;
      .title {
        color: #61c5f7;
        vertical-align: middle;
        > tr {
          height: 28rem / $base-font-size;
          line-height: 22rem / $base-font-size;
        }
      }
      .tableBody {
        display: block;
        overflow-y: hidden;
        height: calc(100% - #{30rem / $base-font-size});
        tr {
          height: 20rem / $base-font-size;
          line-height: 20rem / $base-font-size;
          table-layout: fixed;
          display: table;
        }
      }
      tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        font-size: 12rem / $base-font-size;
        td {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
      }
      .personBody {
        tr {
          font-size: 12rem / $base-font-size;
        }
      }
      .vulnBody {
        tr {
          height: 28rem / $base-font-size;
          line-height: 20rem / $base-font-size;
          font-size: 12rem / $base-font-size;
        }
      }
    }
  }
}
.mouse td {
  cursor: pointer;
}
.sWidth {
  width: 40px;
}
.width60 {
  width: 60px;
}
</style>
