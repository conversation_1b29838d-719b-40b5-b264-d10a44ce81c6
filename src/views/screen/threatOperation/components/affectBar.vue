<template>
  <div>
    <div class="bar" ref="intelligenceBar"></div>
  </div>
</template>
<script>
import { attackSourceIPaddressRanking } from "@/api/screen/screen";
export default {
  props: {
    startColor: String,
    endColor: String,
  },
  data() {
    return {
      xBarData: [],
      yBarData: [],
    };
  },
  created() {
    // 攻击源IP地址排名
    this.getAttackSourceIPaddressRanking();
  },
  mounted() {},
  methods: {
    // 今日情报数量
    intelligenceBar(dataList) {
      const intelligenceBarChart = this.$echarts.init(this.$refs.intelligenceBar);
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["数量"],
          bottom: 6,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          left: "2%",
          right: "4%",
          bottom: "14%",
          top: "3%",
          containLabel: true,
        },
        yAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
          axisLabel: {
            show: true,
            color: "#FFF", //更改坐标轴文字颜色
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
        },
        xAxis: {
          triggeEvent: true,
          type: "category",
          axisLabel: {
            show: true,
            color: "#FFF",
            rotate: 50, //更改坐标轴文字颜色
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          data: this.xBarData,
        },
        series: {
          type: "bar",
          name: "数量",
          barWidth: 10, //柱图宽度
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(
              1,
              1,
              1,
              0,
              [
                {
                  offset: 0,
                  color: this.startColor, // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: this.endColor, // 100% 处的颜色
                },
              ],
              false
            ),
          },
          data: this.yBarData,
        },
      };
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (dataList.length > 0) {
        intelligenceBarChart.setOption(option);
      } else {
        intelligenceBarChart.setOption(noData);
      }
    },
    // 攻击源IP地址排名
    getAttackSourceIPaddressRanking() {
      attackSourceIPaddressRanking().then((res) => {
        this.xBarData = res.data.map((rItem) => {
          return rItem.content;
        });
        this.yBarData = res.data.map((rItem) => {
          return rItem.count;
        });
        this.intelligenceBar(this.yBarData);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.bar-wrapper,
.bar {
  height: 100%;
}
</style>
