<template>
  <div class="line-wrapper">
    <div class="line" ref="intelligenceLine"></div>
  </div>
</template>
<script>
let alarmLine = null;
let attackLine = null;
import { larmMonitoringTrendChart, getAttackedEsCount } from "@/api/screen/screen";
const windowSize = window.innerWidth;
export default {
  props: {
    type: String,
    lineColor: String,
  },
  data() {
    return {
      xBarData: [],
      yBarData: [],
    };
  },
  watch: {
    type: {
      handler(newV, oldV) {
        if (newV === "attack") {
          this.attackedEsCount();
        } else {
          this.getLarmMonitoringTrendChart();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // 监听窗口变化 - 同时刷新重绘
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 重绘
    resizeFnc() {
      alarmLine.resize();
      attackLine.resize();
    },
    // 今日情报数量
    intelligenceLIne(dataList) {
      let self = this;
      if (self.type == "attack") {
        attackLine = this.$echarts.init(this.$refs.intelligenceLine);
      } else {
        alarmLine = this.$echarts.init(this.$refs.intelligenceLine);
      }

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "none" },
        },
        legend: {
          data: ["数量"],
          align: "left",
          bottom: 0,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          left: "2%",
          right: self.type == "attack" ? "4%" : "2%",
          bottom: self.type == "attack" ? "15%" : "15%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,

          axisLabel: {
            rotate: self.type == "attack" ? 0 : -10,
            margin: 10,
            show: true,
            color: "#FFF", //更改坐标轴文字颜色
            formatter: function (value) {
              var ret = ""; //拼接加\n返回的类目项
              var maxLength = 10; //每项显示文字个数
              var valLength = value.length; //X轴类目项的文字个数
              var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
              if (rowN > 1 && windowSize < 1500) {
                //如果类目项的文字大于10,
                for (var i = 0; i < rowN; i++) {
                  var temp = ""; //每次截取的字符串
                  var start = i * maxLength; //开始截取的位置
                  var end = start + maxLength; //结束截取的位置
                  //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                  temp = value.substring(start, end) + "\n";
                  ret += temp; //凭借最终的字符串
                }
                return ret;
              } else {
                return value;
              }
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
          data: this.xBarData,
        },
        yAxis: {
          type: "value",
          axisLabel: {
            show: true,
            // interval: 3,
            // rotate: -30,
            color: "#FFF", //更改坐标轴文字颜色
            fontSize: 8,
            formatter: (val, key) => {
              if (key % 3 == 0) {
                return val;
              }
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
        },
        series: [
          {
            name: "数量",
            data: this.yBarData,
            type: "line",
            symbol: "circle", //折点设定为实心点
            symbolSize: 9, //设定实心点的大小
            itemStyle: {
              normal: {
                color: "#7FFFFF", //折点颜色
                lineStyle: {
                  normal: {
                    color: this.lineColor ? this.lineColor : "#17ABE0",
                  },
                },
              },
            },
          },
        ],
      };
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      switch (dataList.length) {
        case 0:
          self.type == "attack" ? attackLine.setOption(noData) : alarmLine.setOption(noData);
          break;
        default:
          self.type == "attack" ? attackLine.setOption(option) : alarmLine.setOption(option);
          break;
      }
    },
    //告警监测走势图
    getLarmMonitoringTrendChart() {
      larmMonitoringTrendChart().then((res) => {
        this.xBarData = res.data.map((rItem) => {
          return rItem.date_hour;
        });
        this.yBarData = res.data.map((rItem) => {
          return rItem.count;
        });
        this.intelligenceLIne(this.yBarData);
      });
    },
    //
    attackedEsCount() {
      getAttackedEsCount().then((res) => {
        this.xBarData = res.data.map((rItem) => {
          return rItem.time;
        });
        this.yBarData = res.data.map((rItem) => {
          return rItem.count;
        });
        this.intelligenceLIne(this.yBarData);
      });
    },
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.line-wrapper,
.line {
  height: 100%;
}
</style>
