<template>
  <div class="map-wrapper">
    <div class="mapBox" ref="map"></div>
  </div>
</template>

<script>
var mapData = {
  type: "FeatureCollection",
  crs: { type: "name", properties: { name: "urn:ogc:def:crs:OGC:1.3:CRS84" } },
  features: [],
};
var dataList = []; //攻击被攻击数据
import { getThreatOperationMap, getThreatOperationMapAttacked } from "@/api/screen/screen";
let myChart = null;
export default {
  data() {
    return {
      // myChart: null,
    };
  },
  created() {
    // 获取世界地图数据
    this.getMapData();
    this.getAttackData();
  },
  mounted() {
    // 监听窗口变化 - 同时刷新
    this.$nextTick((_) => {
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    });
  },
  beforeUnmount() {
    // if (!this.myChart) {
    //   return;
    // }
    // this.myChart.dispose();
    // this.myChart = null;
  },
  methods: {
    // 获取世界地图数据
    async getMapData() {
      const res = await getThreatOperationMap();
      if (res.code == 200) {
        res.data.forEach((item) => {
          item.geometry = {
            type: item.type,
            coordinates: JSON.parse(item.coordinates),
          };
          delete item.coordinates;
          delete item.type;
          item.properties = {
            name: item.name,
            childNum: +item.childNum,
          };
          delete item.name;
          delete item.childNum;
        });
        mapData.features = res.data;
      }
      this.initMap();
    },
    // 获取攻击数据
    async getAttackData() {
      const res = await getThreatOperationMapAttacked();
      if (res.code == 200) {
        console.info(res.data);
        dataList = res.data;
      }
    },
    convertData(data) {
      var res = [];
      data.forEach((dv, di) => {
        if (dv.coordinate && dv.customerCoordinate) {
          res.push({
            coords: [dv.coordinate, dv.customerCoordinate],
            lineStyle: {
              normal: {
                color: dv.count > 10 ? (dv.count > 100 ? "#e10007" : "#F8E60C") : "#00eaff", //线的颜色
                width: 1,
                opacity: 1, //尾迹线条透明度
                curveness: 0.3, //尾迹线条曲直度
              },
            },
          });
        }
      });
      console.info(res);
      return res;
    },
    initMap() {
      let self = this;
      var series = [];
      series.push(
        {
          type: "lines",
          zlevel: 2,
          effect: {
            show: true,
            period: 4, //箭头指向速度，值越小速度越快
            trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
            symbol: "arrow", //箭头图标
            symbolSize: 5, //图标大小
          },
          data: self.convertData(dataList),
        },
        // 攻击点参数设置
        {
          type: "effectScatter",
          coordinateSystem: "geo",
          zlevel: 2,
          rippleEffect: {
            //涟漪特效
            period: 4, //动画时间，值越小速度越快
            brushType: "stroke", //波纹绘制方式 stroke, fill
            scale: 4, //波纹圆环最大限制，值越大波纹越大
          },
          label: {
            normal: {
              show: true,
              position: "right", //显示位置
              offset: [5, 0], //偏移设置
              formatter: function (params) {
                //圆环显示文字
                return params.data.name;
              },
              fontSize: 13,
            },
            emphasis: {
              show: true,
            },
          },
          symbol: "circle",
          symbolSize: 2,
          data: dataList.map(function (dataItem) {
            let lineColor = dataItem.count > 10 ? (dataItem.count > 100 ? "#e10007" : "#F8E60C") : "#00eaff";
            return {
              name: dataItem.name,
              value: dataItem.coordinate,
              itemStyle: {
                normal: {
                  show: false,
                  color: lineColor,
                },
              },
            };
          }),
        },
        //被攻击点参数设置
        {
          type: "scatter",
          coordinateSystem: "geo",
          zlevel: 2,
          rippleEffect: {
            period: 4,
            brushType: "stroke",
            scale: 4,
          },
          label: {
            normal: {
              show: true,
              position: "right",
              formatter: "{b}",
            },
            emphasis: {
              show: true,
            },
          },
          symbol: "pin",
          symbolSize: 20,
          data: dataList.map(function (dv) {
            let customColor = dv.count > 10 ? (dv.count > 100 ? "#e10007" : "#F8E60C") : "#00eaff";
            return {
              name: dv.customerCity,
              value: dv.customerCoordinate,
              itemStyle: {
                normal: {
                  show: false,
                  color: customColor,
                },
              },
            };
          }),
        }
      );

      var nameMap = "world";
      myChart = this.$echarts.init(this.$refs.map);
      /*获取地图数据*/
      this.$echarts.registerMap(nameMap, mapData);
      /*获取地图数据*/
      myChart.showLoading();
      myChart.hideLoading();
      var optionMap = {
        grid: {
          bottom: 20,
          top: 10,
          left: 0,
          right: 0,
        },
        geo: {
          map: nameMap,
          // 是否显示文字
          label: {
            emphasis: {
              show: false,
            },
          },
          roam: true, //是否允许缩放
          itemStyle: {
            // 地图相关设置
            normal: {
              borderColor: "#2980b9", //区域边框线
              borderWidth: 1,
              areaColor: "#12235c", //区域颜色
            },
            emphasis: {
              areaColor: "#FA8C16", //鼠标移上去颜色
              borderColor: "#FA8C16", //区域边框线

              borderWidth: 1,
              color: "green",
              show: false,
            },
          },
        },
        series: series,
      };
      myChart.setOption(optionMap, true);
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.map-wrapper {
  width: 100%;
  height: 100%;
  > .mapBox {
    width: 100%;
    // height:420rem/$base-font-size;
    height: 100%;
  }
}
</style>
