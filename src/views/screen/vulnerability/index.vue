<template>
  <component :is="windowSize < 1500 ? screenLayout : divComponent">
    <div id="box">
      <div class="wrapper">
        <div class="header header-height-size">
          <div class="left header-height-size">
            <img class="logo" src="@/assets/imgs/screen/logo.png" alt="" />
            <span>网络安全运营中心</span>
          </div>
          <div class="right header-height-size">
            <span>{{ nowTime }}</span>
            <span>运营态势</span>
          </div>
        </div>
        <div class="content">
          <div class="con-left">
            <div class="box">
              <div class="title">高频监测状态</div>
              <div class="box-content">
                <div class="info-wrapper firstbox">
                  <p class="info-tit">监测系统数量</p>
                  <p class="info-num">{{ monitorSysNumber }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">监测频度</p>
                  <p class="info-num">24 <span class="info-tit"> 小时</span></p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">最近监测结果时间</p>
                  <p class="info-date">{{ latestMonitoringResultTime }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">最新监测成果</p>
                  <p class="info-num">{{ latestMonitoringResultAchievement }}<span class="info-tit"> 个</span></p>
                </div>
              </div>
            </div>
            <div class="box">
              <div class="title">低频扫描状态</div>
              <div class="box-content">
                <div class="info-wrapper firstbox">
                  <p class="info-tit">监测对象数量</p>
                  <p class="info-num">{{ JianCeDuixNum }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">最近扫描时间</p>
                  <p class="info-date">{{ lastScanTime }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">下次扫描计划</p>
                  <p class="info-date">{{ lastScanPlan }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">最新扫描成果</p>
                  <p class="info-num">{{ lastScanResult }} <span class="info-tit"> 个</span></p>
                </div>
              </div>
            </div>
            <div class="box">
              <div class="title">深度测试状态</div>
              <div class="box-content">
                <div class="info-wrapper firstbox">
                  <p class="info-tit">监测系统数量</p>
                  <p class="info-num">{{ monitorsysNum }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">最近测试时间</p>
                  <p class="info-date">{{ lastTestTimeResult }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">下次测试计划</p>
                  <p class="info-date">{{ nextTestPlan }}</p>
                </div>
                <div class="info-wrapper">
                  <p class="info-tit">最新测试成果</p>
                  <p class="info-num">{{ lastTestResults }} <span class="info-tit"> 个</span></p>
                </div>
              </div>
            </div>
          </div>
          <div class="con-center">
            <div class="box-wrapper">
              <div class="box">
                <div class="title">待处置漏洞分布</div>
                <div class="box-content">
                  <div class="bar-wrapper" v-if="highData">
                    <span class="text">高频监测</span>
                    <div class="bar">
                      <!-- 每一个宽度的百分比根据数值调整  需要保证总和是100 -->
                      <div class="bar-inner color1" :style="{ width: highData.urgent + '%' }"></div>
                      <div class="bar-inner color2" :style="{ width: highData.hight + '%' }"></div>
                      <div class="bar-inner color3" :style="{ width: highData.middle + '%' }"></div>
                      <div class="bar-inner color4" :style="{ width: highData.low + '%' }"></div>
                    </div>
                    <div class="num">{{ highData.highFrequency }}</div>
                  </div>
                  <div class="bar-wrapper">
                    <span class="text">低频监测</span>
                    <div class="bar">
                      <!-- 每一个宽度的百分比根据数值调整  需要保证总和是100 -->
                      <div class="bar-inner color1" :style="{ width: lowhData.urgent + '%' }"></div>
                      <div class="bar-inner color2" :style="{ width: lowhData.hight + '%' }"></div>
                      <div class="bar-inner color3" :style="{ width: lowhData.middle + '%' }"></div>
                      <div class="bar-inner color4" :style="{ width: lowhData.low + '%' }"></div>
                    </div>
                    <div class="num">{{ lowhData.lowFrequency }}</div>
                  </div>
                  <div class="barinfo-wrapper">
                    <div class="barInfo">
                      <div class="bar-inner color1"></div>
                      <p class="txt">紧急</p>
                    </div>
                    <div class="barInfo">
                      <div class="bar-inner color2"></div>
                      <p class="txt">高危</p>
                    </div>
                    <div class="barInfo">
                      <div class="bar-inner color3"></div>
                      <p class="txt">中危</p>
                    </div>
                    <div class="barInfo">
                      <div class="bar-inner color4"></div>
                      <p class="txt">低危</p>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 实时状态表格 -->
              <div class="box">
                <div class="title">业务系统漏洞实时状态</div>
                <div class="box-content table-content">
                  <TableShuff :type="'bussiness'"></TableShuff>
                </div>
              </div>
            </div>

            <!-- 基础资源漏洞实时状态 -->
            <div class="box">
              <div class="title">基础资源漏洞实时状态</div>
              <div class="box-content table-content">
                <TableShuff :type="'basic'"></TableShuff>
              </div>
            </div>
          </div>
          <div class="con-right">
            <div class="top">
              <div class="box">
                <div class="title">漏洞数量和时间分布</div>
                <div class="box-content">
                  <ul class="btn">
                    <li :class="activeName == 'vuln0' ? 'active' : ''" @click="changeDateTab('vuln0')">全部漏洞</li>
                    <li :class="activeName == 'vuln1' ? 'active' : ''" @click="changeDateTab('vuln1')">业务系统漏洞</li>
                    <li :class="activeName == 'vuln2' ? 'active' : ''" @click="changeDateTab('vuln2')">基础资源漏洞</li>
                  </ul>
                  <DimensionBar :activeName="activeName"></DimensionBar>
                </div>
              </div>
              <div class="box">
                <div class="title">漏洞类型排名</div>
                <div class="box-content">
                  <ul class="btn">
                    <li :class="barName == 'business' ? 'active' : ''" @click="changeTypeTab('business')">业务</li>
                    <li :class="barName == 'basics' ? 'active' : ''" @click="changeTypeTab('basics')">基础</li>
                  </ul>
                  <Bar :type="barName"></Bar>
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="box">
                <div class="title">资产漏洞分布分析</div>
                <div class="box-content">
                  <div class="vulnbar">
                    <vuln-bar @assetsId="getAssetsIdDetail"></vuln-bar>
                  </div>
                  <div class="pieRate">
                    <RatePie :windowSize="windowSize" :id="childId"></RatePie>
                  </div>
                  <div class="pie">
                    <Pie :id="childId"></Pie>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </component>
</template>
<script setup>
import divComponent from "../divComponent.vue";
import screenLayout from "../../screenLayout/index.vue";
const windowSize = window.innerWidth;
</script>
<script>
import Pie from "./components/pie.vue";
import RatePie from "./components/water.vue";
import vulnBar from "./components/stackBar.vue";
import Bar from "./components/bar.vue";
import TableShuff from "@/views/screen/threatOperation/components/tableShuff.vue";
import DimensionBar from "./components/dimensionalBar.vue";
import {
  selectMonitorSysNumber,
  selectLatestMonitoringResultTime,
  selectLatestMonitoringResultAchievements,
  selectJianCeDuixNum,
  selectLastScanTime,
  selectLastScanResult,
  selectMonitorSysTemNum,
  selectLastTestTime,
  selectLastScanPlan,
  selectNextTestPlan,
  selectLastTestResults,
  selectBusinessVulnStatisticst,
  selectResourceVulnStatistics,
} from "@/api/screen/vuln";
let tabLoopTimer = null;
export default {
  name: "Vulnerability",
  components: {
    TableShuff,
    DimensionBar,
    Bar,
    vulnBar,
    RatePie,
    Pie,
  },
  created() {
    this.getSelectMonitorSysNumber();
  },
  data() {
    return {
      nowTime: "",
      activeName: "vuln2",
      barName: "business",
      monitorSysNumber: 0,
      latestMonitoringResultTime: 0,
      latestMonitoringResultAchievement: 0,
      JianCeDuixNum: 0,
      lastScanTime: 0,
      lastScanResult: 0,
      monitorsysNum: 0,
      lastTestTimeResult: 0,
      lastScanPlan: 0,
      nextTestPlan: 0,
      lastTestResults: 0,
      highData: {},
      lowhData: {},
      childId: "",
    };
  },
  methods: {
    getAssetsIdDetail(id) {
      this.childId = id;
    },
    // 时间
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month = new Date(timeStamp).getMonth() + 1 < 10 ? "0" + (new Date(timeStamp).getMonth() + 1) : new Date(timeStamp).getMonth() + 1;
      let date = new Date(timeStamp).getDate() < 10 ? "0" + new Date(timeStamp).getDate() : new Date(timeStamp).getDate();
      let hh = new Date(timeStamp).getHours() < 10 ? "0" + new Date(timeStamp).getHours() : new Date(timeStamp).getHours();
      let mm = new Date(timeStamp).getMinutes() < 10 ? "0" + new Date(timeStamp).getMinutes() : new Date(timeStamp).getMinutes();
      let ss = new Date(timeStamp).getSeconds() < 10 ? "0" + new Date(timeStamp).getSeconds() : new Date(timeStamp).getSeconds();
      this.nowTime = year + "年" + month + "月" + date + "日" + " " + hh + ":" + mm + ":" + ss;
    },
    nowTimes() {
      this.timeFormate(new Date());
      setInterval(this.nowTimes, 1000);
      this.clear();
    },
    clear() {
      clearInterval(this.nowTimes);
      this.nowTimes = null;
    },
    // 轮播
    startLoop() {
      const dateTabs = ["vuln0", "vuln1", "vuln2"];
      const typeTabs = ["business", "basics"];
      tabLoopTimer = setInterval(() => {
        let dateTabIndex = dateTabs.indexOf(this.activeName) + 1;
        if (dateTabIndex >= dateTabs.length) {
          dateTabIndex = 0;
        }
        this.changeDateTab(dateTabs[dateTabIndex]);

        let typeTabIndex = typeTabs.indexOf(this.barName) + 1;
        if (typeTabIndex >= typeTabs.length) {
          typeTabIndex = 0;
        }
        this.changeTypeTab(typeTabs[typeTabIndex]);
      }, 5000);
    },
    // 切换雷达图数据
    changeDateTab(val) {
      this.activeName = val;
    },
    // 切换类型排名
    changeTypeTab(val) {
      this.barName = val;
    },
    getSelectMonitorSysNumber() {
      //高频监测状态-监测系统数量
      selectMonitorSysNumber().then((res) => {
        this.monitorSysNumber = res.data.monitorSysNumber;
      });
      //高频监测状态-最近监测结果时间
      selectLatestMonitoringResultTime().then((res) => {
        this.latestMonitoringResultTime = res.data.latestMonitoringResultTime;
      });
      //  高频监测状态-最新监测成果
      selectLatestMonitoringResultAchievements().then((res) => {
        this.latestMonitoringResultAchievement = res.data.latestMonitoringResultAchievement;
      });
      // 低频扫描状态-监测对象数量
      selectJianCeDuixNum().then((res) => {
        this.JianCeDuixNum = res.data.JianCeDuixNum;
      });
      // 低频扫描状态-最近扫描时间
      selectLastScanTime().then((res) => {
        this.lastScanTime = res.data.lastScanTime;
      });
      // 低频扫描状态-最新扫描成果
      selectLastScanResult().then((res) => {
        this.lastScanResult = res.data.lastScanResult;
      });
      // 深度测试状态-监测系统数量
      selectMonitorSysTemNum().then((res) => {
        this.monitorsysNum = res.data.monitorsysNum;
      });
      // 深度测试状态-最新测试时间
      selectLastTestTime().then((res) => {
        this.lastTestTimeResult = res.data.lastTestTimeResult;
      });
      // 低频扫描状态-下次扫描计划
      selectLastScanPlan().then((res) => {
        this.lastScanPlan = res.data.lastScanPlan;
      });
      //深度测试状态-下次测试计划
      selectNextTestPlan().then((res) => {
        this.nextTestPlan = res.data.nextTestPlan;
      });
      // 深度测试状态-最新测试成果
      selectLastTestResults().then((res) => {
        this.lastTestResults = res.data.lastTestResults;
      });
      // 待处置漏洞分布-高频监测
      selectBusinessVulnStatisticst().then((res) => {
        const list = res.data.businessVulnStatistics;
        this.highData = {
          urgent: (list.urgent / list.total) * 100,
          hight: (list.hight / list.total) * 100,
          middle: (list.middle / list.total) * 100,
          low: (list.low / list.total) * 100,
          highFrequency: list.total,
        };
      });
      // 待处置漏洞分布-低频监测
      selectResourceVulnStatistics().then((res) => {
        const list = res.data.resourceVulnStatistics;
        this.lowhData = {
          urgent: (list.urgent / list.total) * 100,
          hight: (list.hight / list.total) * 100,
          middle: (list.middle / list.total) * 100,
          low: (list.low / list.total) * 100,
          lowFrequency: list.total,
        };
      });
    },
  },
  mounted() {
    this.nowTimes();
    this.startLoop();
  },
  beforeUnmount() {
    clearInterval(tabLoopTimer);
  },
};
</script>
<style lang="scss">
$base-font-size: 16;
// 大屏 小于 1500px 宽度屏幕 媒体查询
@media only screen and (max-width: 1500px) {
  :root {
    --mini-layout: 1080px;
  }
}
#box {
  height: var(--mini-layout, 100vh);
}
.wrapper {
  height: var(--mini-layout, 100vh);
}
.content {
  height: calc(var(--mini-layout, 100vh) - #{80rem / $base-font-size});
}
</style>
<style lang="scss" scoped>
p,
div {
  margin: 0;
  padding: 0;
}
body,
ul {
  /*组合选择器*/
  list-style: none; /*清除列表默认样式*/
  padding: 0; /*清除padding*/
  margin: 0;
}
#box {
  overflow-y: auto;
  overflow-x: hidden;
  background: #082453;
}
$base-font-size: 16;
.wrapper {
  font-family: "SourceHanSansCN-Regular";
  background: #082453;
  font-size: 14rem / $base-font-size;
  display: flex;
  flex-direction: column;
}
// 头部
.header {
  padding: 0px 17rem / $base-font-size;
  $header-height: 80rem / $base-font-size;
  font-family: "FZZYJW--GB1-0";
  color: #fff;
  height: $header-height;
  border: 2px solid #073ea0;
  background: linear-gradient(180deg, #07226b 0%, #031445 100%);
  box-sizing: border-box;
  vertical-align: middle;
  .left {
    display: inline-block;
    line-height: $header-height - 4rem / $base-font-size;
    img {
      vertical-align: middle;
      width: 34rem / $base-font-size;
    }
    span {
      font-size: 28rem / $base-font-size;
      margin: 2rem / $base-font-size 19rem / $base-font-size;
      vertical-align: middle;
    }
  }
  .right {
    float: right;
    line-height: $header-height - 4rem / $base-font-size;
    > span {
      vertical-align: top;
    }
    > span:nth-child(2) {
      display: inline-block;
      width: 211rem / $base-font-size;
      height: 100%;
      background: #44b0ff;
      margin-left: 42rem / $base-font-size;
      font-size: 32rem / $base-font-size;
      color: #dff0f8;
      text-align: center;
    }
  }
}
// 内容区
.content {
  padding: 14rem / $base-font-size 17rem / $base-font-size 10rem / $base-font-size 17rem / $base-font-size;
  width: 100%;
  display: flex;
  flex: auto;
  // 标题
  .title {
    color: #dff0f8;
    text-align: left;
    border: 2px solid #073ea0;
    border-bottom: none;
    height: 30rem / $base-font-size;
    line-height: 28rem / $base-font-size;
    padding-left: 18rem / $base-font-size;
    vertical-align: middle;
    background: linear-gradient(181deg, #050925 0%, #152b68 100%);
    box-sizing: border-box;
    // flex: 30,
  }
  > .con-left,
  > .con-center {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .box-wrapper {
      flex: 648;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      margin-bottom: 8rem / $base-font-size;
    }
    .box {
      flex: 320;
      overflow: hidden;
      &:not(:last-child) {
        margin-bottom: 8rem / $base-font-size;
      }
      .box-content {
        padding: 6rem / $base-font-size 0;
        padding-bottom: 0;
        width: 100%;
        border: 1px solid #003079;
        height: calc(100% - #{30rem / $base-font-size});
        box-sizing: border-box;
      }
    }
  }
  > .con-left {
    flex: 0 0 254rem / $base-font-size;
    .box {
      display: flex;
      flex-direction: column;
    }
    .info-wrapper {
      // margin-left: 20rem/$base-font-size;
      // margin-bottom:14rem/$base-font-size;
      margin: 0 auto 14rem / $base-font-size;
      width: 212rem / $base-font-size;
      // flex: 0 0 212rem/$base-font-size;
      height: 67rem / $base-font-size;
      // height: 61/290;
      border: 1px solid #79e6f5;
      border-radius: 2px;
      color: #fff;
      padding: 8rem / $base-font-size 10rem / $base-font-size;
      box-sizing: border-box;
    }
    .firstbox {
      padding-top: 0;
      display: flex;
      justify-content: space-between;
      border: none;
      margin-bottom: 0;
      height: 48rem / $base-font-size;
      line-height: 48rem / $base-font-size;
      box-sizing: border-box;
    }
    .info-tit {
      font-size: 12rem / $base-font-size;
    }
    .info-num {
      font-size: 28rem / $base-font-size;
      text-align: center;
      font-weight: bold;
    }
    .info-date {
      font-size: 16rem / $base-font-size;
      text-align: center;
      vertical-align: middle;
      line-height: 32rem / $base-font-size;
    }
  }

  // 中间
  > .con-center {
    $line-color-list: #d94330 #de6523 #f9af3b #047381;
    margin: 0 12rem / $base-font-size;
    flex: 0 0 560rem / $base-font-size;
    .table-content {
      height: 100%;
      padding: 0 16rem / $base-font-size !important;
    }
    .bar-wrapper {
      margin: 50rem / $base-font-size 20rem / $base-font-size;
      font-weight: bold;
      .text {
        color: #fff;
        line-height: 40rem / $base-font-size;
        display: inline-block;
        vertical-align: middle;
        font-size: 16rem / $base-font-size;
      }
      > .bar {
        display: inline-block;
        background-color: #284a74;
        border: 1px solid #48769b;
        width: 280rem / $base-font-size;
        height: 40rem / $base-font-size;
        vertical-align: middle;
        margin-left: 20rem / $base-font-size;
        padding: 12rem / $base-font-size 20rem / $base-font-size;
        > .bar-inner {
          display: inline-block;
          height: 12rem / $base-font-size;
          border-radius: 5rem / $base-font-size;
          @each $line-color in $line-color-list {
            $i: index($line-color-list, $line-color);
            &.color#{$i} {
              background-color: $line-color;
            }
          }
        }
      }
      > .num {
        float: right;
        line-height: 40rem / $base-font-size;
        vertical-align: middle;
        font-size: 28rem / $base-font-size;
        color: #fff;
      }
    }
    .barinfo-wrapper {
      margin-left: 116rem / $base-font-size;
      display: flex;
      .barInfo {
        margin-right: 6rem / $base-font-size;
        height: 14rem / $base-font-size;
        display: flex;
        .txt {
          margin-left: 4rem / $base-font-size;
          color: #fff;
          font-size: 12rem / $base-font-size;
        }
        .bar-inner {
          float: left;
          display: inline-block;
          width: 24rem / $base-font-size;
          height: 12rem / $base-font-size;
          border-radius: 4rem / $base-font-size;
          @each $line-color in $line-color-list {
            $i: index($line-color-list, $line-color);
            &.color#{$i} {
              background-color: $line-color;
            }
          }
        }
      }
    }
  }
  // 右侧
  > .con-right {
    flex: 1;
    // flex:0 0 1048rem/$base-font-size;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .box-content {
      padding-top: 18rem / $base-font-size;
      width: 100%;
      border: 1px solid #003079;
      box-sizing: border-box;
      height: calc(100% - #{30rem / $base-font-size});
    }
    .top {
      width: 100%;
      flex: 648;
      display: flex;
      overflow: hidden;
      margin-bottom: 8rem / $base-font-size;
      > .box {
        width: 49.5%;
        margin-right: 1%;
        height: 100%;
        .box-content {
          // height:612rem/$base-font-size;
          height: calc(100% - #{30rem / $base-font-size});
          position: relative;
          > div {
            height: calc(100% - #{30rem / $base-font-size});
            width: 100%;
          }
        }
      }
      > .box:last-child {
        margin: 0;
      }
      .btn {
        margin-left: 10rem / $base-font-size;
        color: #fff;
        li {
          display: inline-block;
          padding: 6rem / $base-font-size 12rem / $base-font-size;
          text-align: center;
          background: #538ded;
          cursor: pointer;
          font-size: 12rem / $base-font-size;
          cursor: pointer;
        }
        li:first-child {
          border-radius: 6rem / $base-font-size 0px 0px 6rem / $base-font-size;
        }
        li:last-child {
          border-radius: 0px 6rem / $base-font-size 6rem / $base-font-size 0px;
        }
        li:hover,
        li:active {
          background: #1974e4;
        }
        .active {
          background: #1974e4;
        }
      }
    }
    .bottom {
      flex: 320;
      overflow: hidden;
      .box {
        height: 100%;
      }
      .box-content {
        display: flex;
        width: 100%;
        height: calc(100% - #{30rem / $base-font-size});
        .vulnbar {
          width: 50%;
          > div {
            height: calc(100% - #{30rem / $base-font-size});
          }
        }
        .pieRate {
          width: 20%;
          > div {
            height: calc(100% - #{30rem / $base-font-size});
          }
        }
        .pie {
          width: 30%;
          > div {
            height: calc(100% - #{30rem / $base-font-size});
          }
        }
      }
    }
  }
}
</style>
