<template>
  <div>
    <div class="piewrapper" ref="piePanel"></div>
  </div>
</template>

<script>
let waterChart = null;
import { selectAssetsVulnConstituteList } from "@/api/screen/vuln";
import bus from "@/utils/bus";
export default {
  data() {
    return {};
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  watch: {
    id: {
      handler(newV, oldV) {
        let self = this;
        if (newV) {
          this.getRateList(newV);
        }
      },
      immediate: true,
    },
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    this.getRateList();
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    resizeFnc() {
      waterChart.resize();
    },
    // 获取构成
    async getRateList(id) {
      const res = await selectAssetsVulnConstituteList({ businessId: id });
      if (res.code == 200) {
        const result = res.data.map((item) => {
          if (parseFloat(item.rate) / 100) {
            return {
              name: item.category,
              value: parseFloat(item.rate) / 100,
            };
          }
        });
        this.initWaterPanel(result);
      }
    },
    initWaterPanel(data) {
      waterChart = this.$echarts.init(this.$refs.piePanel);
      var option = {
        title: {
          text: "资产漏洞构成",
          subtext: "",
          left: "center",
          top: "bottom",
          textStyle: {
            fontSize: 12,
            color: "#fff", // 主标题文字颜色
          },
        },
        tooltip: {
          trigger: "item",
          formatter: "{b} : ({d}%)",
        },
        grid: {
          left: "10",
          bottom: "0",
          containLabel: true,
        },
        color: ["#69F3FF", "#C9584E", "#EBA626", "#53A2A3", "#3963C8", "#27A2BB", "#F0554D"],
        labelLine: {
          normal: {
            length: 0,
          },
        },
        series: [
          {
            type: "pie",
            radius: "45%",
            // center: ['50%', '50%'],
            //  roseType: 'area',
            itemStyle: {
              borderRadius: 8,
              fontSize: 10,
            },
            data: data,
          },
        ],
      };
      let noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#848484",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (data) {
        waterChart.setOption(option);
      } else {
        waterChart.setOption(noData);
      }
    },
  },
};
</script>

<style scoped lang="scss">
$base-font-size: 16;
.piewrapper {
  height: 100%;
  width: 100%;
}
</style>
