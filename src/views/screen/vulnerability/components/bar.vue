<template>
  <div>
    <div class="bar" ref="intelligenceBar"></div>
  </div>
</template>
<script>
let barEchart = null;
import { selectBusinessTypeRankingList, selectResourceTypeRankingList } from "@/api/screen/vuln";
export default {
  props: {
    type: String,
  },
  data() {
    return {
      xBarData: [],
      yBarData: [],
    };
  },
  watch: {
    type: {
      handler(newV, oldV) {
        if (newV === "business") {
          this.getSelectBusinessTypeRankingList();
        } else if (newV === "basics") {
          this.getseLectResourceTypeRankingList();
        }
      },
      immediate: true,
    },
  },
  created() {
    // 本日告警排名
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    this.intelligenceBar();
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    resizeFnc() {
      barEchart.resize();
    },
    // 漏洞类型排名-业务漏洞
    async getSelectBusinessTypeRankingList() {
      const res = await selectBusinessTypeRankingList();
      this.xBarData = res.data.businessTypeRankingList.map((rItem) => {
        return rItem.vulnTotal;
      });
      this.yBarData = res.data.businessTypeRankingList.map((rItem) => {
        return rItem.type;
      });
      this.intelligenceBar(this.xBarData);
    },
    // 漏洞类型排名-基础漏洞
    async getseLectResourceTypeRankingList() {
      const res = await selectResourceTypeRankingList();
      this.xBarData = res.data.resourceTypeRankingList.map((rItem) => {
        return rItem.vulnTotal;
      });
      this.yBarData = res.data.resourceTypeRankingList.map((rItem) => {
        return rItem.type;
      });
      this.intelligenceBar(this.xBarData);
    },
    // 加载图
    intelligenceBar(dataList) {
      let self = this;
      barEchart = this.$echarts.init(this.$refs.intelligenceBar);
      // 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: ["数量"],
          align: "left",
          bottom: 6,
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          left: "2%",
          right: "8%",
          bottom: "14%",
          top: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],

          axisLabel: {
            show: true,
            color: "#FFF", //更改坐标轴文字颜色
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisTick: {
            // 刻度线
            show: true,
          },
        },
        yAxis: {
          type: "category",
          inverse: true, //倒叙
          axisLabel: {
            show: true,
            fontSize: 10,
            interval: 0,
            rotate: 45,
            color: "#FFF", //更改坐标轴文字颜色
            formatter: function (value) {
              var texts = value;
              if (texts.length > 10) {
                // 现实的字数
                texts = texts.substr(0, 10) + "...";
              }
              return texts;
            },
          },
          splitLine: {
            // 网格线
            show: false, //隐藏或显示
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          data: this.yBarData,
        },
        series: {
          name: "数量",
          type: "bar",
          barWidth: 10, //柱图宽度
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(
              0,
              0,
              1,
              0,
              [
                {
                  offset: 0,
                  color: "#19B2CF", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#65DCFC", // 100% 处的颜色
                },
              ],
              false
            ),
          },
          data: self.xBarData,
        },
      };
      barEchart.setOption(option);
      // if(dataList.length>0){
      //     intelligenceBarChart.setOption(option)
      // }else{
      //     intelligenceBarChart.setOption(noData)
      // }
    },
  },
};
</script>
<style lang="scss" scoped>
$base-font-size: 16;
.bar {
  width: 100%;
  height: 100%;
}
</style>
