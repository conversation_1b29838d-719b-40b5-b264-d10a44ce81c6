<template>
  <div>
    <div class="bar" ref="dimensionalBar"></div>
  </div>
</template>
<script>
const levels = ["低", "中", "高", "紧急", "信息"];
let initBarChart = null;
import { selectResourceList, selectBusinessList, selectAllList } from "@/api/screen/vuln";
export default {
  name: "dimension",
  data() {
    return {
      zBarData: [],
    };
  },
  props: {
    activeName: String,
  },
  watch: {
    activeName: {
      handler(newV, oldV) {
        let self = this;
        if (newV === "vuln2") {
          this.getBasicData();
        } else if (newV === "vuln1") {
          this.getBussinessData();
        } else {
          this.getAllData();
        }
      },
      immediate: true,
    },
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    resizeFnc() {
      initBarChart.resize();
    },
    // 获取基础数据
    async getBasicData() {
      // if(self.initBarChart){
      //     self.initBarChart.clear();
      // }
      const res = await selectResourceList();
      // 月份
      const months = [];
      // 数据
      const data = res.data.resoVulnNumAndTimeDistributionList
        .sort((item1, item2) => {
          // 先按月份执行排序
          if (item1.month > item2.month) {
            return 1;
          } else if (item1.month < item2.month) {
            return -1;
          } else {
            return 0;
          }
        })
        .map((item) => {
          if (!months.includes(item.month)) {
            months.push(item.month);
          }
          return [levels.indexOf(item.key), months.length - 1, item.num];
        });
      this.initDimensiona(months, data);
    },
    // 业务系统
    async getBussinessData() {
      const bRes = await selectBusinessList();
      // 月份
      const months = [];
      // 数据
      const data = bRes.data.businessVulnNumAndTimeDistributionList
        .sort((item1, item2) => {
          // 先按月份执行排序
          if (item1.month > item2.month) {
            return 1;
          } else if (item1.month < item2.month) {
            return -1;
          } else {
            return 0;
          }
        })
        .map((item) => {
          if (!months.includes(item.month)) {
            months.push(item.month);
          }
          return [levels.indexOf(item.key), months.length - 1, item.num];
        });
      this.initDimensiona(months, data);
    },
    // 全部漏洞
    // 业务系统
    async getAllData() {
      const aRes = await selectAllList();
      // 月份
      const months = [];
      // 数据
      const data = aRes.data.vulnNumAndTimeDistributionList
        .sort((item1, item2) => {
          // 先按月份执行排序
          if (item1.month > item2.month) {
            return 1;
          } else if (item1.month < item2.month) {
            return -1;
          } else {
            return 0;
          }
        })
        .map((item) => {
          if (!months.includes(item.month)) {
            months.push(item.month);
          }
          return [levels.indexOf(item.key), months.length - 1, item.num];
        });
      this.initDimensiona(months, data);
    },

    // 加载图
    initDimensiona(months, data) {
      initBarChart = this.$echarts.init(this.$refs.dimensionalBar);
      initBarChart.clear();
      // 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };

      const option = {
        tooltip: {
          show: true,
          formatter: (param) => {
            return `月份: ${months[param.value[1]]} <br/> 等级: ${levels[param.value[0]]} <br/> 数量: ${param.value[2]}`;
          },
        },
        xAxis3D: {
          type: "category",
          data: levels,
          name: null,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
        },
        yAxis3D: {
          type: "category",
          data: months,
          name: null,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
        },
        zAxis3D: {
          type: "value",
          name: null,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
        },
        grid3D: {
          // boxWidth: 100,
          boxDepth: 80,
          viewControl: {
            rotateSensitivity: 0, // 不能旋转
            zoomSensitivity: 0, // 不能缩放
          },
          light: {
            main: {
              intensity: 1,
              shadow: true,
            },
            ambient: {
              intensity: 0.3,
            },
          },
        },
        series: [
          {
            type: "bar3D",
            data: data.map(function (item) {
              return {
                value: [item[0], item[1], item[2]],
              };
            }),
            shading: "lambert",
            label: {
              fontSize: 26,
              borderWidth: 1,
              color: "#fff",
            },
            itemStyle: {
              color: "#1974E4",
            },
            emphasis: {
              label: {
                fontSize: 20,
                color: "#fff",
              },
              itemStyle: {
                color: "#fff",
              },
            },
          },
        ],
      };
      if (months.length > 0) {
        initBarChart.setOption(option);
      } else {
        initBarChart.setOption(noData);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
$base-font-size: 16;
.bar {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
