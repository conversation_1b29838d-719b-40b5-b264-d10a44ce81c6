<template>
  <div>
    <div class="waterPanel" ref="waterPanel"></div>
  </div>
</template>

<script>
let waterChart = null;
const windowSize = window.innerWidth;
import { selectRectificationRateList } from "@/api/screen/vuln";
import bus from "@/utils/bus";
export default {
  data() {
    return {};
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  watch: {
    id: {
      handler(newV, oldV) {
        let self = this;
        if (newV) {
          this.getRateList(newV);
        }
      },
      immediate: true,
    },
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    this.getRateList();
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    resizeFnc() {
      waterChart.resize();
    },
    // 获取整改率
    async getRateList(id) {
      const res = await selectRectificationRateList({ businessId: id });
      if (res.code == 200) {
        const source = res.data;
        // 顺序 总数，紧急，高危，中危
        const sortArr = ["zongRate", "merge", "high", "middle"];
        sortArr.forEach((item) => {
          const index = source.findIndex((sourceItem) => {
            return item in sourceItem || sourceItem.level === item;
          });
          if (index === -1) {
            source.push({
              level: item,
              rate: "0%",
            });
          }
        });
        const result = source
          .sort((item1, item2) => {
            const index1 = sortArr.findIndex((sortItem) => {
              return sortItem in item1 || item1.level === sortItem;
            });
            const index2 = sortArr.findIndex((sortItem) => {
              return sortItem in item2 || item2.level === sortItem;
            });
            if (index1 > index2) {
              return 1;
            } else if (index1 < index2) {
              return -1;
            } else {
              return 0;
            }
          })
          .map((item) => {
            return parseFloat((parseFloat(item.rate) / 100).toFixed(4));
          });
        this.initWaterPanel(result);
      }
    },
    initWaterPanel(data) {
      let self = this;
      waterChart = this.$echarts.init(this.$refs.waterPanel);
      const option = {
        // 鼠标移上去显示
        tooltip: {
          show: true,
          formatter: function (param) {
            return (param.value * 100).toFixed(2) + "%";
          },
        },
        title: {
          text: "资产漏洞整改率",
          subtext: "",
          left: "center",
          top: "bottom",
          textStyle: {
            fontSize: 12,
            color: "#fff", // 主标题文字颜色
          },
        },
        series: [
          {
            type: "liquidFill",
            data: data,
            // 文本
            label: {
              formatter: function (param) {
                return (param.value * 100).toFixed(2) + "%";
              },
              fontSize: windowSize < 1500 ? 18 : 28,
            },
            // 中心
            // center:['50%','50%'],
            // 半径
            radius: "70%",
            // 更新动画
            animationDuration: 0,
            animationDurationUpdate: 2000,
            animationEasingUpdate: "cubicOut",
            color: ["#1974E4", "#d94330", "#de6523", "#f9af3b"],
            itemStyle: {
              opacity: 0.75,
            },
            // 波浪和轮廓上有阴影
            outline: {
              borderDistance: 8,
              itemStyle: {
                borderWidth: 5,
                borderColor: "#294D99",
                // shadowBlur: 20,
                // shadowColor: 'rgba(255, 0, 0, 1)'
              },
            },
            backgroundStyle: {
              color: "#000",
            },

            emphasis: {
              itemStyle: {
                opacity: 0.8,
              },
            },
          },
        ],
      };
      waterChart.setOption(option);
    },
  },
};
</script>

<style scoped lang="scss">
$base-font-size: 16;
.waterPanel {
  height: 100%;
  width: 100%;
}
</style>
