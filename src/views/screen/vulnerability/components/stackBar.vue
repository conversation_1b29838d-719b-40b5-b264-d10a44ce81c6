<template>
  <div>
    <div class="bar" ref="initBar"></div>
    <div class="accumbar" v-if="isExistData">
      <span class="text">无主漏洞</span>

      <div class="bar-wrapper">
        <div class="showinfo">
          无主漏洞
          <p><i class="point color1"></i>紧急:{{ noCountData.emergecy }}</p>
          <p><i class="point color2"></i>高危:{{ noCountData.high }}</p>
          <p><i class="point color3"></i>中危:{{ noCountData.middle }}</p>
          合计:{{ noCountData.total }}
        </div>
        <!-- 每一个宽度的百分比根据数值调整  需要保证总和是100 -->
        <div class="bar-inner color3" :style="{ width: noVulnData.middle + '%' }"></div>
        <div class="bar-inner color2" :style="{ width: noVulnData.high + '%' }"></div>
        <div class="bar-inner color1" :style="{ width: noVulnData.emergecy + '%' }"></div>
      </div>
      <span class="text num">{{ noVulnData.total }}</span>
    </div>
    <ul class="barinfo-wrapper" v-if="isExistData">
      <li>
        <div class="bar-inner color1"></div>
        <p class="txt">紧急</p>
      </li>
      <li>
        <div class="bar-inner color2"></div>
        <p class="txt">高危</p>
      </li>
      <li>
        <div class="bar-inner color3"></div>
        <p class="txt">中危</p>
      </li>
    </ul>
  </div>
</template>
<script>
import { selectAnalysisList, selectNoPersonVuln } from "@/api/screen/vuln";
import bus from "@/utils/bus";
var yBarData = [];
var middle = [];
var high = [];
var emergecy = [];
let initBar = null;
export default {
  name: "personBar",
  data() {
    return {
      noCountData: {
        emergecy: 0,
        high: 0,
        middle: 0,
        total: 0,
      },
      noVulnData: {
        emergecy: 0,
        high: 0,
        middle: 0,
        total: 0,
      },
      isExistData: false,
    };
  },
  created() {
    this.getPersonRatioData();
    this.getnoPersonVuln();
  },
  beforeUnmount() {
    // 销毁组件
    window.removeEventListener("resize", this.resizeFnc);
  },
  mounted() {
    window.addEventListener("resize", this.resizeFnc);
  },
  methods: {
    // 屏幕变化刷新echart
    resizeFnc() {
      initBar.resize();
    },
    async getPersonRatioData() {
      const res = await selectAnalysisList();
      if (res.code == 200) {
        yBarData = res.data.assetVulnDistributionAnalysisList.map((item) => {
          return item.assetsName;
        });

        middle = res.data.assetVulnDistributionAnalysisList.map((item) => {
          return {
            value: parseFloat(item.middle),
            id: item.assetsId,
          };
        });
        high = res.data.assetVulnDistributionAnalysisList.map((item) => {
          return {
            value: parseFloat(item.high),
            id: item.assetsId,
          };
        });
        emergecy = res.data.assetVulnDistributionAnalysisList.map((item) => {
          return {
            value: parseFloat(item.emergecy),
            id: item.assetsId,
          };
        });
        const total = res.data.assetVulnDistributionAnalysisList.map((item) => {
          return item.total;
        });
        this.initEchart(middle, high, emergecy, yBarData, total);
      }
    },
    // 无主漏洞
    async getnoPersonVuln() {
      const res = await selectNoPersonVuln();
      const data = res.data.noPersonVulnDistributionAnalysisList[0] || {};
      this.noCountData = data || {};
      if (Object.keys(data).length > 0) {
        this.isExistData = true;
        this.noVulnData = {
          middle: (data.middle / data.total) * 100,
          high: (data.high / data.total) * 100,
          emergecy: (data.emergecy / data.total) * 100,
          total: data.total,
        };
      }
    },
    initEchart(middle, high, emergecy, yBarData, total) {
      let _this = this;
      initBar = this.$echarts.init(this.$refs.initBar);
      let self = this;
      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0,0,0,0.3)",
        },
      };
      var itemStyle = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: "#FFAE39",
        },
        label: {
          show: true,
          position: "top",
        },
      };
      // 中危
      var itemStyle2 = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: "#DE6521",
        },
        label: {
          show: true,
          position: "top",
        },
      };
      // 高危
      var itemStyle3 = {
        //柱形图圆角，鼠标移上去效果
        emphasis: {
          barBorderRadius: [10, 10, 10, 10],
        },

        normal: {
          //柱形图圆角，初始化效果
          barBorderRadius: [10, 10, 10, 10],
          color: "#E34531",
        },
        label: {
          show: true,
          position: "top",
        },
      };
      var num = 5;
      const option = {
        title: {
          show: false,
        },
        legend: {
          data: ["中危", "高危", "紧急"],
          left: "40%",
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
          bottom: 6,
          show: false,
        },
        textStyle: {
          fontSize: 15,
          color: "#fff",
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            const dotHtml1 =
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#f9af3b"></span>';
            const dotHtml2 =
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#de6523"></span>';
            const dotHtml3 =
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#d94330"></span>';

            return `${params[0].name}<br/>${dotHtml1}${params[0].seriesName}：${params[0].value}
                        <br/>${dotHtml2}${params[1].seriesName}：${params[1].value}
                        <br/>${dotHtml3}${params[2].seriesName}：${params[2].value}`;
          },
        },
        xAxis: {
          type: "value",
          axisLabel: {
            interval: 0,
            rotate: 0,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },
          splitLine: { show: false },
          splitArea: { show: false },
        },
        yAxis: {
          data: yBarData,
          axisLabel: {
            show: true,
          },
          axisTick: {
            show: true,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#FFF",
            },
          },

          splitLine: { show: false },
        },
        grid: {
          left: "2%",
          right: "12%",
          bottom: "0%",
          top: "2%",
          containLabel: true,
        },
        series: [
          {
            name: "中危",
            type: "bar",
            stack: "one",
            barWidth: 10, //柱图宽度
            itemStyle: itemStyle,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: middle,
            zlevel: 1,
          },
          {
            name: "高危",
            type: "bar",
            stack: "one",
            barWidth: 10, //柱图宽度
            itemStyle: itemStyle2,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: high,

            zlevel: 1,
          },
          {
            name: "紧急",
            type: "bar",
            stack: "one",
            barWidth: 1, //柱图宽度
            itemStyle: itemStyle3,
            emphasis: emphasisStyle, //鼠标移上去显示的效果
            data: emergecy,
            label: {
              show: true,
              formatter: function (params) {
                return total[params.dataIndex];
              },
              position: "right",
            },
            zlevel: 1,
          },
        ],
      };
      // / 无数据时
      const noData = {
        title: {
          text: "暂无数据",
          x: "center",
          y: "center",
          textStyle: {
            color: "#fff",
            fontWeight: "normal",
            fontSize: 16,
          },
        },
      };
      if (yBarData.length > 0) {
        initBar.setOption(option);
      } else {
        initBar.setOption(noData);
      }
      // 点击事件
      initBar.on("click", function (params) {
        _this.$emit("assetsId", params.data.id);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
p,
div {
  margin: 0;
  padding: 0;
}
$base-font-size: 16;
.bar {
  width: 100%;
  height: calc(100% - #{32rem / $base-font-size});
}
$line-color-list: #d94330 #de6523 #f9af3b;
.accumbar {
  margin-left: 10rem / $base-font-size;
  .text {
    color: #fff;
    line-height: 32rem / $base-font-size;
    display: inline-block;
    vertical-align: middle;
    font-size: 12rem / $base-font-size;
  }

  > .bar-wrapper {
    position: relative;
    cursor: pointer;
    display: inline-block;
    width: 280px;
    height: 32rem / $base-font-size;
    vertical-align: middle;
    margin-left: 20rem / $base-font-size;
    padding: 8rem / $base-font-size 20rem / $base-font-size;
    > .bar-inner {
      display: inline-block;
      height: 12rem / $base-font-size;
      border-radius: 5rem / $base-font-size;
      @each $line-color in $line-color-list {
        $i: index($line-color-list, $line-color);
        &.color#{$i} {
          background-color: $line-color;
        }
      }
    }
    .showinfo {
      display: none;
      position: absolute;
      left: 20%;
      top: -100px;
      padding: 10px;
      width: 100px;
      background: rgba(15, 15, 15, 0.7);
      font-size: 12px;
      color: #fff;
      p {
        margin: 4px;
        vertical-align: middle;
      }
      .point {
        display: inline-block;
        border-radius: 50%;
        width: 10px;
        height: 10px;
        margin-right: 6px;
        vertical-align: middle;
        @each $line-color in $line-color-list {
          $i: index($line-color-list, $line-color);
          &.color#{$i} {
            background-color: $line-color;
          }
        }
      }
    }
  }
  .bar-wrapper:hover .showinfo {
    display: block;
  }
  > .num {
    float: right;
  }
}
.barinfo-wrapper {
  margin-left: 116rem / $base-font-size;
  li {
    margin-right: 6rem / $base-font-size;
    display: inline-block;
    .txt {
      float: left;
      margin-left: 4rem / $base-font-size;
      color: #fff;
      font-size: 12rem / $base-font-size;
      line-height: 14rem / $base-font-size;
    }
    .bar-inner {
      float: left;
      display: inline-block;
      width: 24rem / $base-font-size;
      height: 14rem / $base-font-size;
      border-radius: 4rem / $base-font-size;
      @each $line-color in $line-color-list {
        $i: index($line-color-list, $line-color);
        &.color#{$i} {
          background-color: $line-color;
        }
      }
    }
  }
}
</style>
