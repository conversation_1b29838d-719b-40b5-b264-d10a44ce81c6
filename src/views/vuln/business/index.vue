<template>
  <el-card :key="isBusiness">
    <h3 v-if="!isEvent" class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <statistics v-if="!isEvent" :border="true" :list="[{ num: pageCount, text: pageTitle }]"></statistics>
    <common-search
      v-model="searchState.data"
      :hideOptions="isEvent"
      :menu-data="searchState.menuData"
      :form-list="searchState.formList"
      @search="search"
      @reset="reset"
    >
      <template v-if="isEvent && relation == 'relation'">
        <el-button v-if="eventVulnAdd" @click="openConnectionDialog">
          <el-icon><Connection /></el-icon>
          关联漏洞</el-button
        >
        <el-button v-if="eventVulnDel" @click="unConnectionSubmit" :disabled="selectedIds.length == 0">
          <el-icon><remove /></el-icon>
          取消关联</el-button
        >
      </template>
    </common-search>
    <!-- 外层表格 -->
    <el-table
      ref="tableParentRef"
      show-header="false"
      v-if="showTable"
      :data="parentList"
      row-key="index"
      :default-expand-all="isEvent && relation == 'relation'"
      :expand-row-keys="isEvent ? '' : expandRowKeys"
      @expand-change="expandChange"
    >
      <el-table-column type="expand">
        <template #default="{ row }">
          <div class="gray-table">
            <vuln-table
              v-if="showVulnTable"
              :is-event="isEvent"
              :type="type"
              :relation="relation"
              :parent="row"
              :search-data="searchState.data"
              @openAction="openActionDialog"
              @openView="openViewDialog"
              @checkIds="checkIds"
            ></vuln-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="assetsName" :label="isBusiness ? '业务系统名称' : '基础资源名称'" :formatter="getAssetsName"></el-table-column>
      <el-table-column label="漏洞分布" width="400">
        <template #default="{ row }">
          <ul class="level-data">
            <li>紧急<span class="level-color" :style="{ background: Level_Color.urgent }"></span>{{ row.urgentRiskCount }}</li>
            <li>高危<span class="level-color" :style="{ background: Level_Color.hight }"></span>{{ row.hightRiskCount }}</li>
            <li>中危<span class="level-color" :style="{ background: Level_Color.middle }"></span>{{ row.middleRiskCount }}</li>
            <li>低危<span class="level-color" :style="{ background: Level_Color.low }"></span>{{ row.lowRiskCount }}</li>
            <li>信息<span class="level-color" :style="{ background: Level_Color.info }"></span>{{ row.infoCount }}</li>
          </ul>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <xel-pagination ref="paginationRef" class="xel-table-pagination" :total="total" :pageNum="pageNum" @change="changePagination"></xel-pagination>
    <!-- 操作弹框 -->
    <xel-dialog ref="actionDialogRef" :title="actionDialogTitle" @submit="submitAction" @close="closeActionDialog">
      <el-form>
        <xel-form-item
          form-type="editor"
          :key="actionType"
          :label="(actionType == 'misreport' ? '误报' : '待办') + '证明'"
          v-model="backDetail"
          :is-clear="isClear"
          @editorValue="changeBackDetail"
        ></xel-form-item>
      </el-form>
    </xel-dialog>
    <!-- 反馈详情弹框 -->
    <xel-dialog ref="viewDialogRef" title="漏洞反馈详情" :ishiddenDialog="true">
      <ul class="view-details">
        <li v-for="(item, index) in viewDetails" :key="index">
          <el-card>
            <template #header>
              <p class="title">{{ index == viewDetails.length - 1 ? "首" : `第${item.rownumStr}` }}次反馈详情</p></template
            >
            <p class="flex-between info">
              <span><span class="color-soft">反馈状态：</span>{{ item.backStatus }}</span>
              <span
                ><span class="color-soft">反馈人：{{ item.backUserName }}</span></span
              >
              <span
                ><span class="color-soft">反馈时间：{{ item.backTime }}</span></span
              >
            </p>
            <p><span class="color-soft">反馈详情</span></p>
            <div class="detail" v-html="item.backDetail"></div>
          </el-card>
        </li>
      </ul>
    </xel-dialog>
    <!-- 关联漏洞 -->
    <xel-dialog ref="connectionDialogRef" title="关联漏洞" size="large" buttonDetermine="关联" @submit="connectionSubmit" @close="search">
      <connection-list ref="connectionRef" relation="noRelation" @checkIds="checkIdsConnect"></connection-list>
    </xel-dialog>
  </el-card>
</template>
<script>
export default {
  name: "VulnBusiness",
};
</script>
<script setup>
import { ref, reactive, computed, onMounted, nextTick, onActivated, onDeactivated } from "vue";
import { Level_Color } from "@/config/constant";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";
const store = useStore();

import { getBusinessVulnCount } from "@/api/vuln/business";
import { selectBasicResourceVulnCount } from "@/api/vuln/basic";

// 事件关联漏洞接口
import connectionList from "@/views/event/threatEvent/components/vulns.vue";
//事件关联漏洞权限
let eventVulnAdd = computed(() => {
  return store.state.eventDetail.eventDetail.eventVulnAdd == "Y";
});
let eventVulnDel = computed(() => {
  return store.state.eventDetail.eventDetail.eventVulnDel == "Y";
});

let props = defineProps({
  isEvent: {
    type: Boolean,
    default: false,
  }, //事件详情下的相关漏洞
  type: {
    type: String,
    default: "",
  },
  //是否与事件关联
  relation: {
    type: String,
    default: "relation",
  },
});

let spareStr = route.name == "VulnBusiness" || props.type == "business" ? "spare" : "space";

let emits = defineEmits("checkIds");

let isBusiness = computed(() => {
  if (!props.isEvent) {
    return route.name == "VulnBusiness";
  } else {
    return props.type == "business";
  }
}); // true 业务系统漏洞，false 基础资源漏洞
let pageTitle = ref("");
let pageCount = ref(0);
getPageCount();
function getPageCount() {
  if (!props.isEvent) {
    if (isBusiness.value) {
      pageTitle.value = "业务系统漏洞总数";
      getBusinessVulnCount().then((res) => {
        pageCount.value = res.data;
      });
    } else {
      pageTitle.value = "基础资源漏洞总数";
      selectBasicResourceVulnCount().then((res) => {
        pageCount.value = res.data;
      });
    }
  }
}

import {
  getVulnParentList_1,
  saveBatchRectified_1,
  saveBatchMisreport_1,
  saveBatchIgnore_1,
  saveBackRectified_1,
  saveBackMisreport_1,
  saveBackIgnore_1,
  selectVulnFeedBackDetail_1,
  selectVulnType_1,
} from "@/api/vuln/business";

import {
  getVulnParentList_2,
  saveBatchRectified_2,
  saveBatchMisreport_2,
  saveBatchIgnore_2,
  saveBackRectified_2,
  saveBackMisreport_2,
  saveBackIgnore_2,
  selectVulnType_2,
} from "@/api/vuln/basic";

// 事件下的漏洞接口
import { relatedVuln, cancelVuln } from "@/api/event/detail";

let getVulnParentList = isBusiness.value ? getVulnParentList_1 : getVulnParentList_2;
let saveBatchRectified = isBusiness.value ? saveBatchRectified_1 : saveBatchRectified_2;
let saveBatchMisreport = isBusiness.value ? saveBatchMisreport_1 : saveBatchMisreport_2;
let saveBatchIgnore = isBusiness.value ? saveBatchIgnore_1 : saveBatchIgnore_2;
let saveBackRectified = isBusiness.value ? saveBackRectified_1 : saveBackRectified_2;
let saveBackMisreport = isBusiness.value ? saveBackMisreport_1 : saveBackMisreport_2;
let saveBackIgnore = isBusiness.value ? saveBackIgnore_1 : saveBackIgnore_2;
let selectVulnFeedBackDetail = isBusiness.value ? selectVulnFeedBackDetail_1 : selectVulnFeedBackDetail_1;

import vulnTable from "./components/vulnTable.vue";

let showVulnTable = ref(true);

onActivated(() => {
  showVulnTable.value = true;
});
onDeactivated(() => {
  showVulnTable.value = false;
});
const pageNum = ref(1);
const pageSize = ref(10);
//搜索相关
let searchState = reactive({
  data: {
    level: "",
    result: "",
    findTerm: "",
    source: "",
    type: "",
    systemUrl: "",
    vulnUrl: "",
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  },
  menuData: [
    {
      lable: "漏洞级别：",
      prop: "level",
      dictName: "vuln_level",
    },
    {
      lable: "处置状态：",
      prop: "result",
      dictName: "vuln_result",
    },
    {
      lable: "发现时间：",
      prop: "findTerm",
      dictName: "vuln_findTime",
    },
    {
      lable: isBusiness.value ? "漏洞类别" : "漏洞类型：",
      prop: "type",
      seleteCode: {
        code: isBusiness.value ? selectVulnType_1 : selectVulnType_2,
        value: "name",
        label: "name",
      },
      menuBtnShow: true,
    },
    {
      isShow: isBusiness.value,
      lable: "漏洞来源：",
      prop: "source",
      dictName: "vuln_source",
    },
  ],
  formList: [
    {
      isShow: isBusiness.value,
      prop: "systemUrl",
      label: "系统入口",
    },
    {
      prop: "vulnUrl",
      label: "漏洞地址",
    },
    {
      isShow: isBusiness.value,
      prop: "vulnCode",
      label: "漏洞编号",
    },
    {
      prop: "title",
      label: "漏洞标题",
    },
  ],
});
let paginationRef = ref();
let tableParentRef = ref();

function search() {
  //刷新时取消选中
  idsTitles = {};
  selectedIds.value = [];
  selectedTitles.value = [];
  idsTitlesConnect = {};
  selectedIdsConnect.value = [];
  selectedTitlesConnect.value = [];

  showTable.value = false;
  let params = { ...searchState.data };
  if (props.isEvent) {
    params[spareStr + 1] = props.relation;
    params[spareStr + 2] = route.params.id;
  }
  getVulnParentList(params).then(({ data }) => {
    showTable.value = true;

    total.value = data.total;
    parentList.value = data.rows.map((item, index) => {
      return {
        ...item,
        index: index,
      };
    });
    getPageCount();

    //关闭展开行
    nextTick(() => {
      for (let item of parentList.value) {
        if (tableParentRef.value) {
          tableParentRef.value.toggleRowExpansion(item, false);
        }
      }
    });
  });
}
function reset() {
  pageNum.value = 1;
  pageSize.value = 10;
  searchState.data = {
    level: "",
    result: "",
    findTerm: "",
    source: "",
    type: "",
    systemUrl: "",
    vulnUrl: "",
    pageNum: pageNum.value,
    pageSize: pageNum.value,
  };
  paginationRef.value.resetPageNum();
  search();
}

//外层表格
let showTable = ref(false);
let parentList = ref([]);
let total = ref(0);
let expandRowKeys = ref([]);
function getAssetsName(row) {
  if (isBusiness.value) {
    return (row.assetsName || "") + " " + row.id;
  } else {
    return (row.assetsName || "") + " " + row.ips;
  }
}
function expandChange(row, rows) {
  expandRowKeys.value = rows.map((item) => item.index);
}
function changePagination(pageParams) {
  pageNum.value = pageParams.pageNum;
  pageSize.value = pageParams.pageSize;
  searchState.data.pageNum = pageParams.pageNum;
  searchState.data.pageSize = pageParams.pageSize;
  search();
}

//操作弹框
let actionDialogRef = ref();
let actionDialogTitle = ref("");
let ids = ref([]);
let actionType = ref("");
let backDetail = ref("");
let isClear = ref(false);
let activeChildTableRef = null;
let actionSingle = false;
function openActionDialog(type, dialogTitle, idList, tableRef, single) {
  actionSingle = single;
  isClear.value = true;

  actionType.value = type;
  actionDialogTitle.value = dialogTitle;
  actionDialogRef.value.open();
  ids.value = idList;
  activeChildTableRef = tableRef;
}
let actionApiObj = {
  ignore: {
    single: saveBackIgnore,
    batch: saveBatchIgnore,
  },
  rectified: {
    single: saveBackRectified,
    batch: saveBatchRectified,
  },
  misreport: {
    single: saveBackMisreport,
    batch: saveBatchMisreport,
  },
};
function submitAction(close, load) {
  if (!backDetail.value) {
    ElMessage.warning(`${(actionType.value == "misreport" ? "误报" : "待办") + "证明"}不能为空`);
    return;
  }
  let ifSingle = actionSingle; //是否是单个数据操作

  load();
  let apiFn = actionApiObj[actionType.value][ifSingle ? "single" : "batch"];
  let params = { backDetail: backDetail.value };
  if (ifSingle) {
    params.id = ids.value.join();
  } else {
    params.ids = ids.value.join();
  }

  apiFn(params)
    .then(() => {
      ElMessage.success("操作成功");
      close();

      if (ifSingle) {
        if (activeChildTableRef) {
          activeChildTableRef.reload({}, false);
          activeChildTableRef.table.clearSelection();
        }
      } else {
        activeChildTableRef.reload({}, false);
        activeChildTableRef.table.clearSelection();
      }
    })
    .catch(() => {
      close(false);
    });
}
function closeActionDialog() {
  isClear.value = false;
}
function changeBackDetail(data) {
  backDetail.value = data;
}

//反馈详情
let viewDialogRef = ref();
let viewDetails = ref([]);
function openViewDialog(id) {
  viewDialogRef.value.open();
  selectVulnFeedBackDetail({ id }).then(({ data }) => {
    viewDetails.value = data;
  });
}

//关联漏洞相关
let connectionDialogRef = ref();
function openConnectionDialog() {
  connectionDialogRef.value.open();
  if (connectionRef.value) {
    connectionRef.value.update && connectionRef.value.update();
  }
}
let selectedIds = ref([]);
let selectedTitles = ref([]);
let idsTitles = {};

function checkIds(pid, ids, titles) {
  idsTitles[pid] = {
    ids,
    titles,
  };
  let idList = [];
  let titleList = [];
  for (let key in idsTitles) {
    idList.push(...idsTitles[key].ids);
    titleList.push(...idsTitles[key].titles);
  }
  selectedIds.value = [...new Set(idList)];

  selectedTitles.value = [...new Set(titleList)];
  emits("checkIds", pid, selectedIds.value, selectedTitles.value);
}

let selectedIdsConnect = ref([]);
let selectedTitlesConnect = ref([]);
let idsTitlesConnect = {};
function checkIdsConnect(pid, ids, titles) {
  idsTitlesConnect[pid] = {
    ids,
    titles,
  };
  let idList = [];
  let titleList = [];
  for (let key in idsTitlesConnect) {
    idList.push(...idsTitlesConnect[key].ids);
    titleList.push(...idsTitlesConnect[key].titles);
  }

  selectedIdsConnect.value = [...new Set(idList)];

  selectedTitlesConnect.value = [...new Set(titleList)];

  // emits("checkIds", pid, selectedIdsConnect.value, selectedTitlesConnect.value);
}

//关联
let connectionRef = ref();
function connectionSubmit(close, load) {
  if (selectedIdsConnect.value.length == 0) {
    ElMessage.warning("请先选择漏洞");
    return;
  }
  load();
  relatedVuln({
    id: selectedIdsConnect.value.join(),
    title: selectedTitlesConnect.value.join(),
    eventId: route.params.id,
  })
    .then(({ data }) => {
      ElMessage.success("操作成功");
      connectionRef.value.update();
      selectedIdsConnect.value = [];
      store.commit("changeTabNumsByName", {
        name: 4,
        num: data.vulnCount,
      });
      search();
      close(false);
    })
    .catch(() => {
      close(false);
    });
}
function unConnectionSubmit() {
  ElMessageBox.confirm(`确认取消关联选中漏洞？`, "警告", {
    distinguishCancelAndClose: true,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    cancelVuln({
      id: selectedIds.value.join(),
      title: selectedTitles.value.join(),
      eventId: route.params.id,
    }).then(({ data }) => {
      ElMessage.success("操作成功");
      store.commit("changeTabNumsByName", {
        name: 4,
        num: data.vulnCount,
      });
      selectedIds.value = [];
      search();
    });
  });
}

defineExpose({
  search,
});
</script>

<style lang="scss" scoped>
.level-data {
  display: flex;
  flex-wrap: nowrap;

  li {
    margin-right: 20px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    .level-color {
      margin: 0 6px;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
.view-details li {
  margin-bottom: 20px;
  padding: 0 5px;
  :deep(.el-card__body) {
    padding-top: 0;
  }
  .title {
    font-size: 16px;
    padding: 10px;
  }
  .info {
    padding: 10px 0;
    border-bottom: 1px dashed #ccc;
    margin-bottom: 10px;
  }
  .detail {
    margin-top: 10px;
  }
}
</style>
