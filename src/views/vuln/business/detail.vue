<template>
  <div class="Div">
    <el-card class="card">
      <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
      <div class="margin-bottom20 text-right" style="margin-top: -30px">
        <backbutton text="漏洞列表" :name="isBusiness ? 'vulnBusiness' : 'vulnBaisc'"></backbutton>
        <el-button @click="lifecycle" class="search-button">
          <el-icon :size="12"><d-arrow-left /></el-icon>
          漏洞生命周期
        </el-button>
      </div>
      <div class="title-bottom-line">
        <p>基础信息</p>
      </div>

      <section v-if="isBusiness">
        <el-form ref="form" label-width="120px" label-position="left" class="base-info-form">
          <el-row :gutter="70">
            <el-col :span="12">
              <el-form-item label="漏洞标题：">{{ assdetData.title }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞级别 ：">
                <el-tag :type="Level_Data[assdetData.levelId]">{{ assdetData.levelName }}</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞类别：">{{ assdetData.category }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞类型：">{{ assdetData.type }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隶属单位：">{{}}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="IP地址：">{{ assdetData.ipStr }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞URL：">{{ assdetData.vulnUrl }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统入口：">{{ assdetData.systemUrl }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发现时间：">{{ assdetData.findTime }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务系统：">{{ assdetData.system }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分析师：">{{ assdetData.dealId }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处置状态：">{{ assdetData.resultName }}</el-form-item>
            </el-col>
            <el-col :span="24" v-if="!spare5Null && assdetData.retestList">
              <el-form-item label="复测状态：">
                <span v-if="assdetData.retestList.length == 0">未复测</span>
                <xel-table v-else :data="assdetData.retestList" :columns="retestListColumns" :pagination="false"></xel-table>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </section>
      <section v-else>
        <el-form ref="form" label-width="120px" label-position="left" class="base-info-form">
          <el-row :gutter="70">
            <el-col :span="12">
              <el-form-item label="漏洞标题：">{{ assdetData.title }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="漏洞级别 ：">
                <el-tag :type="Level_Data[assdetData.levelId]">{{ assdetData.levelName }}</el-tag>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="漏洞类型：">{{ assdetData.type }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务系统：">{{ assdetData.system }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="隶属单位：">{{}}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="IP地址：">{{ assdetData.vulnIpStr }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时间：">{{ assdetData.findTime }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="CNNVD编号：">{{ assdetData.cnnvd }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="CVE编号：">{{ assdetData.cve }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="CNCVE编号：">{{ assdetData.cncve }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="处置状态：">{{ assdetData.resultName }}</el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </section>
      <div class="title-bottom-line" v-if="isBusiness && assdetData.source == 2">
        <p>漏洞信息</p>
      </div>
      <section v-if="isBusiness && assdetData.source == 2">
        <el-form ref="form" label-width="120px" label-position="left" class="base-info-form">
          <el-row :gutter="70">
            <el-col :span="24">
              <el-form-item label="提交方式：">{{ assdetData.method }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="问题参数 ：">{{ assdetData.testParam }} </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="测试用例 ："><div v-html="assdetData.testRule"></div> </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="请求头消息 ："> <div v-html="assdetData.httpreq"></div></el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </section>
      <div class="title-bottom-line">
        <p>漏洞详情描述</p>
      </div>
      <section class="assde" v-if="isBusiness" v-html="assdetData.riskDescription"></section>
      <section class="assde" v-else v-html="assdetData.detail"></section>
      <template v-if="assdetData.source != 2 || !spare5Null">
        <div class="title-bottom-line" v-if="isBusiness">
          <p>漏洞复现步骤</p>
        </div>
        <section class="assde" v-if="isBusiness" v-html="assdetData.repetitionSteps"></section>
      </template>

      <div class="title-bottom-line">
        <p>漏洞修复建议</p>
      </div>
      <section class="assde" v-if="isBusiness" v-html="assdetData.restorationProposal"></section>
      <section class="assde" v-else v-html="assdetData.reinforcement"></section>
      <template v-if="isBusiness">
        <!-- 验证结果 -->
        <template v-if="assdetData.source != 2 || spare5Null">
          <div class="panel-body" v-if="assdetData.fileList && assdetData.fileList.length > 0">
            <div class="panel tasks-widget mbot0">
              <header class="title-bottom-line">验证结果</header>
              <section class="task-content mbot0 vuln_old_desc">
                <div class="input_img">
                  <p>
                    <span
                      >验证人：<span>{{ assdetData.dealId }}</span></span
                    >
                    <span style="margin-left: 60px"
                      >验证时间：<span>{{ assdetData.dealTime }}</span></span
                    >
                  </p>
                  <div>
                    <div v-for="item in assdetData.fileList" :key="item.fileId">
                      <div style="float: left" class="show_img">
                        <img
                          :src="uploadUrl + 'system/file/getFile?fileId=' + item.fileId"
                          class="original_img"
                          style="margin-top: 20px; margin-right: 10px; max-width: 150px; max-height: 120px"
                          alt=""
                          @click="showBigImg(item.fileId)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </template>
        <!-- 复测列表 -->
        <template v-if="!spare5Null">
          <div v-for="(item, index) in assdetData.retestList" :key="item.id" class="panel-body">
            <div v-if="item.result !== '' && item.result !== null" class="panel tasks-widget mbot0">
              <header class="title-bottom-line">
                {{ indexMethod(item) }}
              </header>
              <section class="assde" v-html="item.detail"></section>
            </div>
          </div>
        </template>
      </template>
    </el-card>
    <div class="cardAdd" v-if="timeDisplay"><timeline :activities="activities" ref="timelineRef"></timeline></div>
  </div>
  <!-- 放大图片 -->
  <xel-dialog ref="imgDialogRef" :append-to-body="true" title="验证结果图片" width="80%" :showSubmit="false" buttonCancel="关闭">
    <img :src="uploadUrl + 'system/file/getFile?fileId=' + imgFileId" alt="" style="max-width: 100%; display: block; margin: 0 auto" />
  </xel-dialog>
</template>
<script>
export default {
  name: "VulnBusinessDetail",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { selectVulnDetail_1 } from "@/api/vuln/business";
import { selectVulnDetail_2 } from "@/api/vuln/basic";

import { useRouter, useRoute } from "vue-router";
import { timeDisplay, showTimeline, closeTimeline } from "@/utils/timelineDisplay.js";
import { Level_Data } from "@/config/constant";

let uploadUrl = "/outpost-api/";

const route = useRoute();
const router = useRouter();
let isBusiness = computed(() => {
  return route.name == "VulnBusinessDetail";
}); // true 业务系统漏洞，false 基础资源漏洞
let selectVulnDetail = isBusiness.value ? selectVulnDetail_1 : selectVulnDetail_2;
let spare5Null = computed(() => {
  if (isBusiness.value && assdetData.value.source == 2 && assdetData.value.spare5 == null) {
    return true;
  } else {
    return false;
  }
});

let vulnId = route.params.id;
let assdetData = ref({});
function getselectVulnDetail() {
  selectVulnDetail({ id: vulnId }).then((res) => {
    activities.value = res.data.vulnLogList;
    assdetData.value = res.data;
  });
}
getselectVulnDetail();
//时间流
let activities = ref([]);
function lifecycle() {
  showTimeline();
}

//复测列表
let retestListColumns = [
  {
    prop: "rownumStr",
    label: "阶段",
    formatter(row) {
      if (row.rownumStr == "首") {
        return row.rownumStr + "次复测";
      } else {
        return "第" + row.rownumStr + "次复测";
      }
    },
  },
  {
    prop: "retestTime",
    label: "测试时间",
  },
  {
    prop: "result",
    label: "测试结果",
    formatter(r, c, cellValue) {
      return cellValue == 0 ? "未加固" : "已加固";
    },
  },
  {
    prop: "retestUser",
    label: "复测人",
  },
];
// 放大图片
let imgFileId = ref("");
let imgDialogRef = ref();
function showBigImg(id) {
  console.log("id: ", id);
  imgFileId.value = id;
  imgDialogRef.value.open();
}
function indexMethod(row) {
  if (row.rownumStr == "首") {
    return row.rownumStr + "次复测";
  } else {
    return "第" + row.rownumStr + "次复测";
  }
}
</script>

<style lang="scss" scoped>
.Div {
  display: flex;
}
.base-info-form {
  :deep(.el-form-item) {
    border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    margin-left: 20px;
  }
}
.assde {
  line-height: 160%;
  color: $fontColorSoft;
  margin-left: 20px;
}
.card {
  width: 100%;
}
.cardAdd {
  width: 25%;
  // min-width: ;
  margin-left: 30px;
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  position: sticky;
  top: 0;
}
</style>
