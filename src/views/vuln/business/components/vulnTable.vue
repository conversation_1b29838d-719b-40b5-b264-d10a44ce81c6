<template>
  <div class="relative vuln-table">
    <xel-table
      ref="tableRef"
      :load-data="getVulnList"
      :columns="columns"
      :checkbox="true"
      row-key="id"
      :default-params="{ ...defaultParams, ...searchData }"
      @selection-change="handleSelectionChange"
    >
      <template #level="scope">
        <el-tag :type="Level_Data[scope.row.level]">{{ scope.row.levelName }}</el-tag>
      </template>
      <template #space5="{ row }">
        {{ row.space5 ?? "无主漏洞" }}
      </template>
      <template #action="scope">
        <xel-handle-btns :scope="scope" class="row-action" :btn-list="getBtnListByResult(scope.row)"></xel-handle-btns>
      </template>
    </xel-table>
    <!-- 批量操作按钮 -->
    <xel-handle-btns v-if="!isEvent" class="handleBtns" :btn-list="batchBtnList"></xel-handle-btns>
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";
import { getVulnList_1, saveBatchFiltered_1 } from "@/api/vuln/business";
import { getVulnList_2, saveBatchFiltered_2 } from "@/api/vuln/basic";

import { ElMessage, ElMessageBox } from "element-plus";
import { download } from "@/plugins/request";

import { Level_Data } from "@/config/constant";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

let props = defineProps({
  parent: {
    type: Object,
    required: true,
  },

  searchData: {
    type: Object,
    required: true,
  },
  isEvent: {
    type: Boolean,
    default: false,
  }, //事件详情下的相关漏洞
  type: {
    type: String,
    default: "",
  },
  //是否与事件关联
  relation: {
    type: String,
    default: "relation",
  },
});

let spareStr = route.name == "VulnBusiness" || props.type == "business" ? "spare" : "space";

let emits = defineEmits(["openAction", "openView", "checkIds"]);

let isBusiness = computed(() => {
  if (!props.isEvent) {
    return route.name == "VulnBusiness";
  } else {
    return props.type == "business";
  }
}); // true 业务系统漏洞，false 基础资源漏洞

let getVulnList = isBusiness.value ? getVulnList_1 : getVulnList_2;
let saveBatchFiltered = isBusiness.value ? saveBatchFiltered_1 : saveBatchFiltered_2;
let handFlag = ref(false);
let exportFlag = ref(false);

let pageTitle = ref(isBusiness.value ? "业务系统漏洞" : "基础资源漏洞");

//父级有资产id的调此接口时传id；此值与spare2必传一个，但是不能两个都传
let defaultParams = {};

if (isBusiness.value) {
  if (props.parent.systemUrl) {
    defaultParams.id = String(props.parent.systemUrl);
  } else {
    defaultParams[spareStr + 2] = String(props.parent.id);
  }
} else {
  if (props.parent.assetsId) {
    defaultParams.assetsId = String(props.parent.assetsId);
  }
}

if (props.isEvent) {
  defaultParams[spareStr + 1] = String(props.relation);
  if (props.type == "business") {
    defaultParams[spareStr + 3] = route.params.id;
  } else {
    defaultParams[spareStr + 2] = route.params.id;
  }
}

let tableRef = ref();
let resData = computed(() => {
  return tableRef.value ? tableRef.value.resData : {};
});
watch(
  () => resData.value,
  (val) => {
    handFlag.value = val.handFlag == "Y";

    exportFlag.value = val.exportFlag == "Y";
    batchBtnList.value.forEach((item, index, arr) => {
      if (index == arr.length - 1) {
        item.hide = !exportFlag.value;
      } else {
        item.hide = !handFlag.value;
      }
    });
  },
  { deep: true }
);
let columns = ref([
  {
    prop: "title",
    label: "漏洞标题 ",
    width: $globalWindowSize == "S" ? 250 : $globalWindowSize == "M" ? 250 : 350,
    formatter({ row }) {
      if (isBusiness.value) {
        return "#" + row.vulnCode + " - " + row.title;
      } else {
        return row.title;
      }
    },
    click({ row }) {
      let name = isBusiness.value ? "VulnBusinessDetail" : "VulnBasicDetail";
      router.push({ name, params: { id: row.id } });
    },
  },
  {
    prop: isBusiness.value ? "type" : "topType",
    label: "漏洞类别 ",
  },
  {
    prop: "vulnUrl",
    label: "漏洞地址 ",
  },
  {
    prop: "levelName",
    label: "漏洞级别 ",
    slotName: "level",
    width: "80px",
  },
  {
    prop: "sourceName",
    label: "漏洞来源 ",
  },
  {
    prop: "updateTime",
    label: "更新时间 ",
    width: "150px",
  },
  {
    prop: "space5",
    label: "责任人",
    slotName: "space5",
  },
  {
    prop: "resultName",
    label: "处置状态 ",
  },
  {
    hide: props.isEvent,
    prop: "action",
    label: "动作 ",
    width: "190",
    slotName: "action",
  },
]);

if (!isBusiness.value) {
  columns.value.splice(4, 1);
}

let btnsTemplate = [
  {
    name: "rectified",

    title: "已整改",
    icon: "circle-check",
    onClick({ row }) {
      actionFn("rectified", row);
    },
  },
  {
    name: "ignore",
    title: "申请忽略",
    icon: "more",
    onClick({ row }) {
      actionFn("ignore", row);
    },
  },
  {
    name: "misreport",
    title: "误报",
    icon: "remove",
    onClick({ row }) {
      actionFn("misreport", row);
    },
  },
  {
    name: "view",
    title: "反馈详情",
    icon: "view",
    onClick({ row }) {
      emits("openView", row.id);
    },
  },
];
/***********************
 result = 0 :
result = 1 ：忽略、已整改、误报
result = 2 ：反馈详情
result = 3 ：反馈详情
result = 4 ：忽略、已整改、反馈详情
result = 5 ： 忽略、已整改、反馈详情
result = 6 ：反馈详情
result = 7 ：反馈详情
result = 8 ：忽略、已整改、反馈详情
result = 9 ：反馈详情
result = 10 ：反馈详情

反馈详情
1.result==11 && backStatus != 0 显示反馈详情按钮
2.result==11 && backStatus ==0 不显示按钮
 * ***************************************** */
let btnListObj = new Map([
  [0, []],
  [1, ["ignore", "rectified", "misreport"]],
  [2, ["view"]],
  [3, ["view"]],
  [4, ["ignore", "rectified", "view"]],
  [5, ["ignore", "rectified", "view"]],
  [6, ["view"]],
  [7, ["view"]],
  [8, ["ignore", "rectified", "view"]],
  [9, ["view"]],
  [10, ["view"]],
  [11, []], //无主漏洞
  [12, ["view"]], //有处理的无主漏洞
]);
function getBtnListByResult(row) {
  let result = row.result;
  if (result == 11 && row.backStatus != 0) {
    result = 12;
  }
  if (!handFlag.value) {
    result = 12;
  }
  let names = btnListObj.get(Number(result)) || [];
  return btnsTemplate.filter((item) => names.includes(item.name));
}

//批量操作
let batchBtnList = ref([
  {
    hide: !handFlag.value,
    title: "手动过滤",
    icon: "filter",
    onClick() {
      actionFn("filter");
    },
    disabled: true,
  },
  {
    hide: !handFlag.value,
    title: "已整改",
    icon: "circle-check",
    onClick() {
      actionFn("rectified");
    },
    disabled: true,
  },
  {
    hide: !handFlag.value,
    title: "申请忽略",
    icon: "more",
    onClick() {
      actionFn("ignore");
    },
    disabled: true,
  },
  {
    hide: !handFlag.value,
    title: "误报",
    icon: "remove",
    onClick() {
      actionFn("misreport");
    },
    disabled: true,
  },
  {
    hide: !exportFlag.value,
    title: "导出",
    icon: "download",
    onClick() {
      let url = isBusiness.value ? "/system/vulnBusiness/exportVuln" : "/system/vulnBasic/exportVuln";
      download(url, "漏洞列表.xls", {}, "post", { ids: ids.join() });
    },
    disabled: true,
  },
]);

let ids = [];
let selectedDatas = [];
function handleSelectionChange(list) {
  selectedDatas = list;
  batchBtnList.value.forEach((item) => (item.disabled = !list.length));
  ids = list.map((item) => item.id);
  let pid = "";
  if (isBusiness.value) {
    pid = String(props.parent.id);
  } else {
    if (props.parent.assetsId) {
      pid = String(props.parent.assetsId);
    }
  }
  emits(
    "checkIds",
    pid,
    ids,
    list.map((item) => item.title)
  );
}

let actionType = ref("");
let actionTypeTextObj = {
  filter: "手动过滤",
  ignore: "申请忽略",
  view: "反馈详情",
  rectified: "整改",
  misreport: "误报",
};
let activeData = ref({});
let isBatch = ref(false);

function actionFn(type, row) {
  actionType.value = type;
  if (row) activeData.value = row;
  isBatch.value = !row;

  let idList = row ? [row.id] : ids;

  if (!row && !validateResult(type)) {
    return;
  }

  switch (type) {
    case "filter":
      ElMessageBox.confirm(`确定要手动过滤吗？`, "警告", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        saveBatchFiltered({ ids: idList.join() })
          .then((res) => {
            ElMessage.success("操作成功");
            tableRef.value.table.clearSelection();
            tableRef.value.reload({}, false);
          })
          .catch((res) => {});
      });

      break;
    case "rectified":
    case "misreport":
    case "ignore":
      emits("openAction", actionType.value, actionDialogTitle.value, idList, tableRef.value, !!row);
      break;
  }
}
function validateResult(type) {
  let list = selectedDatas;
  let texts = [];
  let arr = [];
  switch (type) {
    case "rectified":
      arr = list.filter((item) => ![1, 4, 5, 8].includes(Number(item.result)));
      break;
    case "misreport":
      arr = list.filter((item) => ![1].includes(Number(item.result)));
      break;
    case "ignore":
      arr = list.filter((item) => ![1, 4, 5, 8].includes(Number(item.result)));
      break;
  }
  texts = arr.map((item) => item.title);
  //? 取消前台校验
  // if (texts.length > 0) {
  //   ElMessage.warning(texts.join(", ") + " 不满足条件，请取消选中!");
  //   return false;
  // }
  return true;
}

let actionDialogTitle = computed(() => {
  return pageTitle.value + (isBatch.value ? "批量" : "") + actionTypeTextObj[actionType.value];
});
</script>

<style lang="scss" scoped>
.handleBtns {
  position: absolute;
  bottom: 5px;
  left: 20px;
}
.row-action {
  transform: scale(0.8);
  transform-origin: left center;
}
.vuln-table {
  :deep(.el-pagination) {
    transform: scale(0.92) translateY(-10px);
    transform-origin: 90% center;
  }
}
</style>
