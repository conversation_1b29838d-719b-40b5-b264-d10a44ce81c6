<template>
  <security></security>
</template>
<script>
export default {
  name: "SafetyWarning",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import Security from "../leakage/security.vue";
const route = useRoute();
const router = useRouter();
</script>
<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
