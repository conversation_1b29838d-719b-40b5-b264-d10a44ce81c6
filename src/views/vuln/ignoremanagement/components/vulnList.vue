<template>
  <div>
    <div class="title-bottom-line">
      <p>{{ title }}</p>
    </div>
    <div v-hasPermi="type === 'alarm' ? 'vulnMisreport:del' : 'vulnIgnore:del'" class="margin-bottom10">
      <el-button :disabled="deleteState.deleteVals1.length <= 0" type="button" @click="deleteVulns('business')">批量删除</el-button>
    </div>
    <xel-table ref="tableRef" :columns="columns" :load-data="getdata1" :checkbox="true" row-key="id" @selection-change="handleSelectionChange1">
      <template #level="scope">
        <el-tag v-if="scope.row.level !== '' && scope.row.levelName !== ''" :type="Level_Data[scope.row.level]">{{ scope.row.levelName }}</el-tag>
      </template>
    </xel-table>
    <div class="clearfix"></div>
    <div class="title-bottom-line">
      <p>{{ title2 }}</p>
    </div>

    <div v-hasPermi="type === 'alarm' ? 'vulnMisreport:del' : 'vulnIgnore:del'" class="margin-bottom10">
      <el-button :disabled="deleteState.deleteVals2.length <= 0" @click="deleteVulns('resource')">批量删除</el-button>
    </div>
    <xel-table ref="tableRef2" :columns="columns2" :load-data="getdata2" :checkbox="true" row-key="id" @selection-change="handleSelectionChange2">
      <template #level="scope">
        <el-tag v-if="scope.row.level !== '' && scope.row.levelName !== ''" :type="Level_Data[scope.row.level]">{{ scope.row.levelName }}</el-tag>
      </template>
    </xel-table>
    <xel-dialog title="误报证明" ref="wuZm" :ishiddenDialog="true" @close="closeDialog">
      <!-- <div class="wudetail" v-html="wubaoDetail"></div> -->
      <ul>
        <li class="margin-bottom10" v-for="(item, index) in reportProve" :key="index" v-html="item.backDetail"></li>
      </ul>
    </xel-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import { batchDelete } from "@/utils/delete";
import { Level_Data } from "@/config/constant";

import {
  selectPageMisreportBusiness,
  selectPageMisreportResource,
  selectPageIgnoreBusiness,
  selectPageIgnoreResource,
  deleteIngroeVuln,
  viewMisReport,
} from "@/api/vuln/vulnFilter";
import { ElMessage } from "element-plus";
let props = defineProps({
  type: {
    type: String,
    default: "",
  },
});
let title = ref("");
let title2 = ref("");
let getdata1 = ref(null);
let getdata2 = ref(null);
// 误报证明
let wuZm = ref();
// let wubaoDetail = ref("");
let reportProve = ref([]);
if (props.type === "alarm") {
  title.value = "业务系统漏洞误报列表";
  title2.value = "基础资源漏洞误报列表";
  getdata1.value = selectPageMisreportBusiness;
  getdata2.value = selectPageMisreportResource;
} else if (props.type === "active") {
  title.value = "业务系统漏洞主动忽略列表";
  title2.value = "基础资源漏洞主动忽略列表";
  getdata1.value = selectPageIgnoreBusiness;
  getdata2.value = selectPageIgnoreResource;
}
let tableRef = ref();
let tableRef2 = ref();

let btnList = computed(() => {
  if (props.type === "alarm") {
    return [
      {
        icon: "delete",
        hasPermi: "vulnMisreport:del",

        title: "删除",
        onClick(scope) {
          batchDelete().then(() => {
            deleteIngroeVuln(scope.row.id).then((res) => {
              ElMessage.success("删除成功");
              tableRef.value.reload({});
              tableRef2.value.reload({});
            });
          });
        },
      },
      {
        isFont: "icon-chakan",
        title: "误报证明",
        onClick(scope) {
          viewMisReport(scope.row.id).then((res) => {
            wuZm.value.open();
            // wubaoDetail.value = res.reportProve;
            reportProve.value = res.reportProve;
          });
        },
      },
    ];
  } else {
    return [
      {
        icon: "delete",
        title: "删除",
        hasPermi: "vulnIgnore:del",

        onClick(scope) {
          batchDelete().then(() => {
            deleteIngroeVuln(scope.row.id).then((res) => {
              ElMessage.success("删除成功");
              tableRef.value.reload({});
              tableRef2.value.reload({});
            });
          });
        },
      },
    ];
  }
});
let columns = [
  {
    prop: "title",
    label: "漏洞标题",
  },
  {
    prop: "vulnType",
    label: "漏洞类型",
  },
  {
    prop: "level",
    label: "漏洞级别",
    slotName: "level",
  },
  {
    prop: "systemName",
    label: "业务系统",
  },
  {
    prop: "vulnUrl",
    label: "漏洞地址",
  },
  {
    prop: "createTime",
    label: "添加时间",
  },
  {
    prop: "",
    fixed: "right",
    label: "操作",
    slotName: "actionBtns",
    btnList: btnList.value,
  },
];
let columns2 = [
  {
    prop: "title",
    label: "漏洞名称",
  },
  {
    prop: "level",
    label: "漏洞级别",
    slotName: "level",
  },
  {
    prop: "hostName",
    label: "主机名",
  },
  {
    prop: "assetsIpStr",
    label: "主机地址",
  },
  {
    prop: "createTime",
    label: "添加时间",
  },
  {
    prop: "",
    fixed: "right",
    label: "操作",
    slotName: "actionBtns",
    btnList: btnList.value,
  },
];
// 关闭误报证明后操作
function closeDialog() {
  // wubaoDetail.value = "";
  reportProve.value = [];
}
// 批量删除
let deleteState = reactive({
  deleteVals1: [],
  deleteVals2: [],
});
function handleSelectionChange2(val) {
  deleteState.deleteVals2 = val;
  console.info(val);
}
function handleSelectionChange1(val) {
  console.info(val);
  deleteState.deleteVals1 = val;
}
// 删除业务系统
function deleteVulns(type) {
  batchDelete().then(() => {
    let ids = "";
    let arr = [];
    if (type === "business") {
      arr = deleteState.deleteVals1;
    } else if (type === "resource") {
      arr = deleteState.deleteVals2;
    }
    arr.forEach((item, index) => {
      if (index > 0) {
        ids = ids + "," + item.id;
      } else {
        ids = ids + item.id;
      }
    });
    deleteIngroeVuln(ids).then((res) => {
      ElMessage.success("删除成功");
      tableRef.value.reload({});
      tableRef.value.table.clearSelection();
      tableRef2.value.reload({});
      tableRef2.value.table.clearSelection();
    });
  });
}
</script>

<style scoped lang="scss">
.wudetail {
  padding: 10px;
  border: 1px solid #e4e4e4;
  border-radius: 5px;
  min-height: 50px;
}
</style>
