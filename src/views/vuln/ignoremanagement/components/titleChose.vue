<template>
  <common-search
    v-if="JSON.stringify(state.data) !== '{}'"
    v-model="state.data"
    :menu-data="state.menuData"
    :form-list="state.formList"
    @search="search"
    labelWidth="120px"
    @reset="reset"
  >
    <template #form>
      <el-form-item label="创建时间: ">
        <el-date-picker
          v-model="searchState.timeList"
          style="width: 100%"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          @change="getThatime"
        >
        </el-date-picker>
      </el-form-item>
    </template>
  </common-search>
  <xel-table ref="tableRef" :columns="columns" :load-data="getData">
    <template #level="scope">
      <el-tag v-if="scope.row.level !== '' && scope.row.levelName !== ''" :type="Level_Data[scope.row.level]">{{ scope.row.levelName }}</el-tag>
    </template>
  </xel-table>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { Level_Data } from "@/config/constant";
import { getVulnResourceList, getBusinessList } from "@/api/vuln/vulnFilter";
let props = defineProps({
  componentType: {
    type: String,
    default: "",
  },
});
// 通过类型判断使用值
let state = reactive({
  data: {},
  menuData: [],
  formList: [],
});
let searchState = reactive({
  timeList: [],
  // 漏洞
  data1: {
    level: "",
    cncve: "",
    title: "",
    cnnvd: "",
    cve: "",
    thatTime: "",
  },
  menuData1: [
    {
      lable: "漏洞级别：",
      prop: "level",
      dictName: "vuln_level",
    },
  ],
  formList1: [
    {
      prop: "title",
      label: "漏洞名称",
    },
    {
      prop: "cncve",
      label: "CNCVE编号",
    },
    {
      prop: "cnnvd",
      label: "CNNVD编号",
    },
    {
      prop: "cve",
      label: "CVE编号",
    },
  ],
  data2: {
    name: "",
    domain: "",
    createName: "",
    beginTimeStr: "",
    endTimeStr: "",
    ips: "",
    ports: "",
    portService: "",
    assetsPerson: "",
    assetsDept: "",
    level: "",
  },
  menuData2: [
    {
      lable: "等级保护级别：",
      prop: "level",
      dictName: "grade_protection_level",
    },
  ],
  formList2: [
    {
      prop: "name",
      label: "业务系统名称",
    },
    {
      prop: "domain",
      label: "系统域名",
    },
    {
      prop: "createName",
      label: "创建人",
    },
    {
      prop: "ips",
      label: "IP",
    },
    {
      prop: "ports",
      label: "端口号",
    },

    {
      formType: "select",
      prop: "portService",
      label: "服务协议",
      filterable: true,

      dictName: "service_agreement",
    },
    {
      prop: "assetsPerson",
      label: "责任人",
    },
    {
      formType: "deptTree",
      multiple: false, //是否多选
      prop: "assetsDept",
      label: "责任主体",
    },
  ],
});
let columns = computed(() => {
  if (props.componentType === "vuln") {
    return [
      {
        prop: "title",
        label: "漏洞名称",
      },
      {
        prop: "level",
        label: "漏洞级别",
        slotName: "level",
      },
      {
        prop: "cve",
        label: "CVE编号",
      },
      {
        prop: "cncve",
        label: "CNCVE编号",
      },
      {
        prop: "cnnvd",
        label: "CNNVD编号",
      },
      {
        prop: "",
        fixed: "right",
        label: "操作",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "check",
            title: "选择",
            onClick(scope) {
              emits("title", "vuln", scope.row.id, scope.row.title);
            },
          },
        ],
      },
    ];
  } else {
    return [
      {
        prop: "name",
        label: "业务系统名称",
      },
      {
        prop: "domains",
        label: "域名",
      },
      {
        prop: "ips",
        label: "服务IP",
      },
      {
        prop: "ports",
        label: "服务端口",
      },
      {
        prop: "",
        fixed: "right",
        label: "操作",
        slotName: "actionBtns",
        btnList: [
          {
            icon: "check",
            title: "选择",
            onClick(scope) {
              emits("title", "business", scope.row.id, scope.row.name);
            },
          },
        ],
      },
    ];
  }
});

// 获取数据方法
let getData = ref(null);
if (props.componentType === "vuln") {
  state.data = JSON.parse(JSON.stringify(searchState.data1));
  state.menuData = JSON.parse(JSON.stringify(searchState.menuData1));
  state.formList = JSON.parse(JSON.stringify(searchState.formList1));
  getData.value = getVulnResourceList;
} else {
  state.data = JSON.parse(JSON.stringify(searchState.data2));
  state.menuData = JSON.parse(JSON.stringify(searchState.menuData2));
  state.formList = JSON.parse(JSON.stringify(searchState.formList2));
  getData.value = getBusinessList;
}
let emits = defineEmits(["title"]);
let tableRef = ref();
function search(initPage = true) {
  tableRef.value && tableRef.value.reload({ ...state.data }, initPage);
}
// 刷新搜索条件
let searShow = ref(true);
function reset() {
  searShow.value = false;
  if (props.componentType === "vuln") {
    state.data = {
      level: "",
      cncve: "",
      title: "",
      cnnvd: "",
      cve: "",
      thatTime: "",
    };
  } else {
    state.data = {
      name: "",
      domain: "",
      createName: "",
      beginTimeStr: "",
      endTimeStr: "",
      ips: "",
      ports: "",
      portService: "",
      assetsPerson: "",
      assetsDept: "",
      level: "",
    };
  }
  searchState.timeList = [];
  setTimeout(() => {
    searShow.value = true;
  }, 200);
  tableRef.value && tableRef.value.reload({});
}
// 获取时间范围
function getThatime(val) {
  if (val) {
    if (props.componentType === "vuln") {
      state.data.thatTime = val[0] + " - " + val[1];
    } else {
      state.data.endTimeStr = val[1];
      state.data.beginTimeStr = val[0];
    }
  } else {
    if (props.componentType === "vuln") {
      state.data.thatTime = "";
    } else {
      state.data.endTimeStr = "";
      state.data.beginTimeStr = "";
    }
  }
}
</script>

<style lang="scss" scoped></style>
