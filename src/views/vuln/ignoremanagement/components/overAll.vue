<template>
  <div>
    <p class="margin-bottom20" v-hasPermi="'vulnGlobal:add'">
      <span @click="addFIlter" style="cursor: pointer; color: #57c8f2"
        ><el-icon><plus /></el-icon> 添加全局忽略规则</span
      >
    </p>
    <div v-show="isAdd">
      <el-form v-if="showAddForm" :model="state.addForm" ref="ruleFormRef" class="formWrapper" label-position="right" label-width="0px">
        <div
          style="width: 200px; position: relative; margin-right: 40px; margin-bottom: 20px"
          class="pull-left add_list"
          v-for="(item, index) in state.formList"
          :key="index"
          v-show="item.isShow"
        >
          <span class="colse_form_item" @click="delete_add_form(item.prop, index)">
            <el-icon :size="12" color="#df1e1e"><circle-close /></el-icon>
          </span>
          <span class="add" v-if="index !== state.formList.length - 1">AND</span>
          <span
            class="add"
            v-else-if="index == state.formList.length - 1 && state.formList.length !== 7"
            @click="add_add_form"
            style="cursor: pointer"
            >AND</span
          >
          <span class="add" v-else-if="state.formList.length == 7" style="cursor: pointer"></span>
          <div
            v-if="item.formType === 'chose'"
            class="chose_other"
            :title="state.addForm[item.name] || item.placeholder"
            @click="add_form(item.prop)"
            :style="state.addForm[item.name] !== '' ? 'color:#303133 ' : ''"
          >
            {{ state.addForm[item.name] || item.placeholder }}
          </div>
          <xel-form-item v-else v-model="state.addForm[item.prop]" v-bind="item"></xel-form-item>
        </div>
      </el-form>
      <div class="clearfix"></div>
      <div class="text-right margin-bottom20">
        <el-button
          type="button"
          @click="
            isAdd = false;
            reset_add_form();
          "
          >取消</el-button
        >
        <el-button type="primary" @click="addVulnFilter">添加全局忽略规则</el-button>
      </div>
    </div>
  </div>

  <xel-table ref="tableRef" :columns="columns" :load-data="getGlobalList" :show-header="false">
    <template #status="scope">
      <el-switch v-hasPermi="'vulnGlobal:edit'" v-model="scope.row.status" active-value="1" inactive-value="2" @change="update_statu(scope.row)">
      </el-switch>
    </template>
    <template #level="scope">
      <el-tag v-if="scope.row.level !== '' && scope.row.levelName !== ''" :type="Level_Data[scope.row.level]">{{ scope.row.levelName }}</el-tag>
    </template>
  </xel-table>
  <xel-dialog :title="add_title" ref="dialogRef" width="60%" :ishiddenDialog="true" @close="closeDialog">
    <title-chose v-if="component" :componentType="componentType" @title="getTitle"></title-chose>
  </xel-dialog>
  <xel-dialog title="添加规则条件" ref="add_form_list" width="60%" :ishiddenDialog="true">
    <div>
      <div class="form_item" v-for="(item, index) in state.allFormList" :key="item.prop" v-show="!item.isShow" @click="add_to_form(index)">
        {{ item.form_name }}
      </div>
    </div>
  </xel-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { getVulnTypeTree, getAssetsGroup, getGlobalList, saveGlobalList, updateGlobalList } from "@/api/vuln/vulnFilter";
import { batchDelete } from "@/utils/delete";
import { ElMessage } from "element-plus";
import { Level_Data } from "@/config/constant";
import TitleChose from "./titleChose.vue";
let state = reactive({
  addForm: {
    vulnSource: "",
    vulnType: "",
    level: "",
    title: "",
    titleName: "",
    cve: "",
    cnnvd: "",
    cncve: "",
    /** 此处暂时隐藏，因为当前漏洞在soss处过滤，但是soss处没有客户资产相关内容 */
    // dept: "",
    // groupId: "",
    // businessSystem: "",
    businessSystemName: "",
  },
  formList: [
    {
      formType: "select",
      prop: "vulnSource",
      label: "",
      placeholder: "请选择数据来源",
      options: [
        {
          value: "scan",
          label: "互联网应用脆弱性运营 ",
        },
        {
          value: "soss",
          label: "专项安全测试",
        },
        {
          value: "3",
          label: "天镜脆弱性扫描器 ",
        },
      ],
      required: false,
      itemWidth: "200px",
      onChange(val) {
        getVulnType(val);
      },
      isShow: true,
    },
    {
      formType: "tree",
      prop: "vulnType",
      label: "",
      placeholder: "请选择漏洞类型",
      multiple: false, //是否多选
      treeData: [],
      popWidth: "300px",
      treeProps: {
        id: "id",
        label: "label",
        children: "children",
      },
      itemWidth: "200px",
      disabled: false,
      isShow: true,
    },
    {
      formType: "select",
      prop: "level",
      label: "",
      placeholder: "请选择漏洞级别",
      dictName: "vuln_level",
      isShow: true,
    },
  ],
  allFormList: [
    {
      formType: "select",
      prop: "vulnSource",
      label: "",
      form_name: "数据来源",
      placeholder: "请选择数据来源",
      options: [
        {
          value: "scan",
          label: "互联网应用脆弱性运营 ",
        },
        {
          value: "soss",
          label: "专项安全测试",
        },
        {
          value: "3",
          label: "天镜脆弱性扫描器 ",
        },
      ],
      required: false,
      itemWidth: "200px",
      onChange(val) {
        getVulnTypeTree(val).then((res) => {
          state.addForm.vulnType = "";
          state.formList.forEach((item) => {
            if (item.prop == "vulnType") {
              item.treeData = res.data.deptTree;
            }
          });
        });
      },
      isShow: true,
    },
    {
      formType: "tree",
      prop: "vulnType",
      label: "",
      form_name: "漏洞类型",
      placeholder: "请选择漏洞类型",
      multiple: false, //是否多选
      treeData: [],
      popWidth: "300px",
      treeProps: {
        id: "id",
        label: "label",
        children: "children",
      },
      itemWidth: "200px",
      disabled: false,
      isShow: true,
    },
    {
      formType: "select",
      prop: "level",
      label: "",
      form_name: "漏洞级别",
      placeholder: "请选择漏洞级别",
      dictName: "vuln_level",
      isShow: true,
    },
    {
      formType: "chose",
      prop: "title",
      name: "titleName",
      form_name: "漏洞名称",
      placeholder: "请选择漏洞名称",
      isShow: false,
    },
    {
      formType: "input",
      prop: "cve",
      placeholder: "请输入CVE编号",
      form_name: "CVE编号",
      isShow: false,
    },
    {
      formType: "input",
      prop: "cnnvd",
      form_name: "CNNVD编号",
      placeholder: "请输入CNNVD编号",
      isShow: false,
    },
    {
      formType: "input",
      prop: "cncve",
      form_name: "CNCVE编号",
      placeholder: "请输入CNCVE编号",
      isShow: false,
    },
    /** 此处暂时隐藏，因为当前漏洞在soss处过滤，但是soss处没有客户资产相关内容 */
    // {
    //   formType: "deptTree",
    //   prop: "dept",
    //   label: "",
    //   placeholder: "请选择责任部门",
    //   form_name: "责任部门",
    //   multiple: false, //是否多选
    //   isShow: false,
    // },
    // {
    //   formType: "select",
    //   prop: "groupId",
    //   label: "",
    //   placeholder: "请选择资产组",
    //   filterable: true,
    //   form_name: "资产组",
    //   seleteCode: {
    //     code: getAssetsGroup,
    //     resKey: "assetsGroupList",
    //     // 传递取值的字段名
    //     label: "groupName",
    //     value: "id",
    //   },

    //   isShow: false,
    // },
    // {
    //   formType: "chose",
    //   prop: "businessSystem",
    //   name: "businessSystemName",
    //   form_name: "业务系统",
    //   placeholder: "请选择业务系统",
    //   isShow: false,
    // },
  ],
});
// 关闭新增忽略规则选项
let showAddForm = ref(true);
function delete_add_form(field, index) {
  if (state.formList.length > 1) {
    showAddForm.value = false;
    state.allFormList.forEach((item) => {
      if (item.prop === field) {
        item.isShow = false;
      }
    });
    if (field == "vulnSource") {
      state.formList.forEach((item) => {
        if (item.prop == "vulnType") {
          state.addForm[item.prop] = "";
          state.formList[1].treeData = [];
        }
      });
    }
    state.formList.splice(index, 1);
    state.addForm[field] = "";
    setTimeout(() => {
      showAddForm.value = true;
    }, 200);
    // state.formList[index].isShow = false;
  } else {
    ElMessage.warning("请至少保留一条选项");
  }
}
let add_title = ref("");
let dialogRef = ref();
let component = ref(false);
let componentType = ref("");
function add_form(prop) {
  if (prop === "title") {
    add_title.value = "选择漏洞名称";
    componentType.value = "vuln";
  } else if (prop === "businessSystem") {
    add_title.value = "选择业务系统";
    componentType.value = "business";
  }
  component.value = true;
  dialogRef.value.open();
}
// 添加忽略规则打开
let isAdd = ref(false);
function addFIlter() {
  isAdd.value = !isAdd.value;
  reset_add_form();
}
// 重置添加内容
function reset_add_form() {
  state.addForm = {
    vulnSource: "",
    vulnType: "",
    level: "",
    title: "",
    titleName: "",
    cve: "",
    cnnvd: "",
    cncve: "",
    /** 此处暂时隐藏，因为当前漏洞在soss处过滤，但是soss处没有客户资产相关内容 */
    // dept: "",
    // groupId: "",
    // businessSystem: "",
    // businessSystemName: "",
  };
  state.formList = [
    {
      formType: "select",
      prop: "vulnSource",
      label: "",
      placeholder: "请选择数据来源",
      options: [
        {
          value: "scan",
          label: "互联网应用脆弱性运营 ",
        },
        {
          value: "soss",
          label: "专项安全测试",
        },
        {
          value: "3",
          label: "天镜脆弱性扫描器 ",
        },
      ],
      required: false,
      itemWidth: "200px",
      onChange(val) {
        getVulnType(val);
      },
      isShow: true,
    },
    {
      formType: "tree",
      prop: "vulnType",
      label: "",
      placeholder: "请选择漏洞类型",
      multiple: false, //是否多选
      treeData: [],
      popWidth: "300px",
      treeProps: {
        id: "id",
        label: "label",
        children: "children",
      },
      itemWidth: "200px",
      disabled: false,
      isShow: true,
    },
    {
      formType: "select",
      prop: "level",
      label: "",
      placeholder: "请选择漏洞级别",
      dictName: "vuln_level",
      isShow: true,
    },
  ];
  state.allFormList.forEach((item) => {
    if (item.prop === "vulnSource" || item.prop === "vulnType" || item.prop === "level") {
      item.isShow = true;
    } else {
      item.isShow = false;
    }
  });
}
// 选择漏洞名称界面传来的titile
function getTitle(type, id, title) {
  if (type === "vuln") {
    state.addForm.title = id;
    state.addForm.titleName = title;
  } else if (type === "business") {
    state.addForm.businessSystem = id;
    state.addForm.businessSystemName = title;
  }

  componentType.value = "";
  closeDialog();
}
function getBusinessTitle(id, title) {}
// 关闭弹窗
function closeDialog() {
  dialogRef.value.close();
  component.value = false;
}
// 表格
let columns = [
  {
    prop: "status",
    label: "开关",
    slotName: "status",
  },
  {
    prop: "vulnSource",
    label: "漏洞标题",
  },
  {
    prop: "vulnType",
    label: "漏洞类型",
  },
  {
    prop: "level",
    label: "漏洞级别",
    slotName: "level",
  },
  {
    prop: "title",
    label: "漏洞名称",
  },
  {
    prop: "cve",
    label: "CVE",
  },
  {
    prop: "cnnvd",
    label: "CNNVD",
  },
  {
    prop: "cncve",
    label: "cncve",
  },
  /** 此处暂时隐藏，因为当前漏洞在soss处过滤，但是soss处没有客户资产相关内容 */
  // {
  //   prop: "dept",
  //   label: "责任部门",
  // },
  // {
  //   prop: "groupId",
  //   label: "资产组",
  // },
  // {
  //   prop: "businessSystem",
  //   label: "业务系统",
  // },
  {
    prop: "",
    fixed: "right",
    label: "操作",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "delete",
        title: "删除",
        hasPermi: "vulnGlobal:del",
        onClick(scope) {
          batchDelete().then(() => {
            let param = {
              id: scope.row.id,
              status: "0",
            };
            updateGlobalList(param).then(() => {
              ElMessage.success("删除成功");
              search();
            });
          });
        },
      },
    ],
  },
];
// 列表查询
let tableRef = ref();
function search(initPage = true) {
  tableRef.value && tableRef.value.reload({}, initPage);
}
// 添加全局忽略规则
function addVulnFilter() {
  saveGlobalList(state.addForm).then((res) => {
    search();
    reset_add_form();
    ElMessage.success("添加成功");
    isAdd.value = !isAdd.value;
  });
}
// 新增忽略规则条件
let add_form_list = ref();
function add_add_form() {
  add_form_list.value.open();
}
function add_to_form(index) {
  state.allFormList[index].isShow = true;
  let add = state.allFormList[index];

  state.formList.push(add);
  if (state.allFormList[index].prop === "vulnType") {
    if (state.addForm.vulnSource !== "") {
      getVulnType(state.addForm.vulnSource);
    }
  }
  add_form_list.value.close();
}
// 获取漏洞类型
function getVulnType(type) {
  getVulnTypeTree(type).then((res) => {
    state.formList.forEach((item) => {
      if (item.prop == "vulnType") {
        item.isShow = false;
        state.addForm.vulnType = "";
        item.treeData = res.data.deptTree;
        item.isShow = true;
      }
    });
  });
}
// 修改忽略规则状态
function update_statu(row) {
  let param = {
    id: row.id,
    status: row.status,
  };
  updateGlobalList(param).then((res) => {
    ElMessage.success("修改成功");
    search();
  });
}
</script>

<style lang="scss" scoped>
.colse_form_item {
  position: absolute;
  z-index: 99;
  right: -10px;
  top: -10px;
  cursor: pointer;
}
.chose_other {
  height: 32px;
  width: 100%;
  line-height: 32px;
  border: 1px solid #e4e4e4;
  border-radius: $radiusS;
  padding: 0px 15px;
  color: #c0c4cc;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.add {
  position: absolute;
  right: -35px;
  top: 8px;
  color: $color;
}
.form_item {
  float: left;
  width: 16%;
  margin: 0 2%;
  height: 30px;
  line-height: 30px;
  margin-bottom: 20px;
  border: 1px solid #e4e4e4;
  text-align: center;
  border-radius: 5px;
  padding: 0px 10px;
  cursor: pointer;
}
.formWrapper {
  .add_list {
    height: 40px;
  }
}
</style>
