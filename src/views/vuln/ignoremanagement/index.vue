<template>
  <el-card>
    <h3 class="conH3Tit">
      {{ "" || ($route.meta && $route.meta.title) }}
    </h3>
    <el-tabs v-model="activeName" @tab-click="changeTabs">
      <el-tab-pane label="全局忽略清单" name="overall">
        <over-all></over-all>
      </el-tab-pane>
      <el-tab-pane label="误报忽略清单" name="alarm">
        <vuln-list v-if="type === 'alarm'" :type="type"></vuln-list>
      </el-tab-pane>
      <el-tab-pane label="主动忽略清单" name="active">
        <vuln-list v-if="type === 'active'" :type="type"></vuln-list>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script>
export default {
  name: "Ignoremanagement",
};
</script>
<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import OverAll from "./components/overAll.vue";
import VulnList from "./components/vulnList.vue";

let activeName = ref("overall");
let type = ref("");
function changeTabs(val) {
  type.value = val.props.name;
}
</script>

<style lang="scss" scoped></style>
