import router from "@/router";
let loopholeColumns = [
  {
    prop: "name",
    label: "监测任务名称",
  },
  {
    prop: "keywordsCount",
    label: "监测关键词数量",
  },
  {
    prop: "keywordsSiteCount",
    label: "关键词站点数量",
  },
  {
    prop: "networkDiskCount",
    label: "网盘敏感信息数量",
  },
  {
    prop: "gitHubCount",
    label: "GitHub 敏感信息数量",
  },
  {
    prop: "exposeEmailCount",
    label: "互联网暴露邮箱数量",
  },
  {
    prop: "wechatCount",
    label: "公众号数量",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "View",
        title: "查看",
        onClick(scope) {
          router.push({ name: "SensitiveDetails", params: { id: scope.row.id } });
        },
      },
    ],
  },
];
let eventColumns = [
  {
    prop: "name",
    label: "评估任务名称",
  },
  {
    prop: "testEmailNum",
    label: "测试邮箱数量",
  },
  {
    prop: "actionType1",
    label: "点击附件数量",
  },
  {
    prop: "actionType2",
    label: "点击链接数量",
  },
  {
    prop: "actionType3",
    label: "提交表单数量",
  },
  {
    label: "操作",
    fixed: "right",
    slotName: "actionBtns",
    btnList: [
      {
        icon: "View",
        title: "查看",
        onClick(scope) {
          router.push({ name: "Consciousness", params: { id: scope.row.id } });
        },
      },
    ],
  },
];
// 安全预警备份
let earlyColumns = function (clickFn) {
  return [
    {
      prop: "warnTitle",
      label: "预警名称",
    },
    {
      prop: "sendTime",
      label: "创建时间",
    },
    {
      label: "操作",
      fixed: "right",
      slotName: "actionBtns",
      btnList: [
        {
          icon: "View",
          title: "查看",
          onClick(scope) {
            clickFn(scope.row);
          },
        },
      ],
    },
  ];
};
// 新安全预警-漏洞预警
let vulnColumns = function (clickFn) {
  return [
    {
      prop: "vid",
      label: "漏洞编号",
    },
    {
      prop: "title",
      label: "漏洞标题",
    },
    {
      prop: "vulnType",
      label: "漏洞类型",
    },
    {
      prop: "vulnNoticeLevel",
      label: "漏洞等级",
    },
    {
      prop: "src",
      label: "漏洞来源",
    },
    {
      prop: "url",
      label: "漏洞公告链接",
    },
    {
      prop: "intime",
      label: "发布时间",
    },
    {
      label: "操作",
      fixed: "right",
      slotName: "actionBtns",
      btnList: [
        {
          icon: "View",
          title: "查看",
          onClick(scope) {
            clickFn(scope.row);
          },
        },
      ],
    },
  ];
};
let searchState = {
  data: {
    name: "",
  },
  menuData: [],
  formList: [
    {
      prop: "name",
      label: "监测任务名称",
    },
  ],
};
let eventState = {
  data: {
    name: "",
  },
  menuData: [],
  formList: [
    {
      prop: "name",
      label: "评估任务名称",
    },
  ],
};
let warningState = {
  data: {
    warnTitle: "",
    sendTimeStr: "",
    createTime: "",
  },
  menuData: [
    {
      lable: "预警时间：",
      prop: "sendTimeStr",
      options: [
        { value: "24", label: "今日" },
        { value: "48", label: "48小时" },
        { value: "72", label: "72小时" },
        { value: "7", label: "近7日" },
        { value: "30", label: "近30日" },
        { value: "halfYear", label: "近半年" },
      ],
    },
  ],
  formList: [
    {
      prop: "warnTitle",
      label: "预警名称",
    },
    {
      formType: "daterange",
      type: "datetimerange",
      prop: "createTime",
      label: "创建时间",
      // onChange: (val) => {
      //   state.formData.createTime = val;
      // },
    },
  ],
};
// 新的安全预警
let vulnWarningState = {
  data: {
    title: "",
    sendTimeStart: "",
    sendTimeEnd: "",
    vid: "",
  },
  menuData: [
    // {
    //   lable: "预警时间：",
    //   prop: "sendTimeStr",
    //   options: [
    //     { value: "24", label: "今日" },
    //     { value: "48", label: "48小时" },
    //     { value: "72", label: "72小时" },
    //     { value: "7", label: "近7日" },
    //     { value: "30", label: "近30日" },
    //     { value: "halfYear", label: "近半年" },
    //   ],
    // },
  ],
  formList: [
    {
      prop: "vid",
      label: "漏洞编号",
    },
    {
      prop: "title",
      label: "漏洞标题",
    },
    {
      prop: "vulnNoticeLevel",
      label: "漏洞等级",
      formType: "select",
      options: [
        { value: "0", label: "低危" },
        { value: "1", label: "中危" },
        { value: "2", label: "高危" },
        { value: "3", label: "紧急" },
      ],
    },
    {
      prop: "vulnType",
      label: "漏洞类型",
    },
    {
      formType: "daterange",
      type: "datetimerange",
      prop: "sendTimeEnd",
      label: "发布时间",
      onChange: (val) => {
        vulnWarningState.data.sendTimeStart = val[0];
        vulnWarningState.data.sendTimeEnd = val[1];
      },
    },
  ],
};
export { loopholeColumns, eventColumns, earlyColumns, vulnColumns, searchState, eventState, warningState, vulnWarningState };
