<template>
  <!-- <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3> -->
  <el-card>
    <div class="title-bottom-line">
      <p>服务成功统计</p>
    </div>
    <ul class="ulname">
      <li>
        <p class="line">关键词数量</p>
        <p class="ulp">{{ rows.keywordsCount }}</p>
      </li>
      <li>
        <p class="line">关键词站点数量</p>
        <p class="ulp">{{ rows.keywordsSiteCount }}</p>
      </li>
      <li>
        <p class="line">网盘敏感信息数量</p>
        <p class="ulp">{{ rows.networkDiskCount }}</p>
      </li>
      <li>
        <p class="line">GitHub敏感信息数量</p>
        <p class="ulp">{{ rows.gitHubCount }}</p>
      </li>
      <li>
        <p class="line">互联网暴露邮箱数量</p>
        <p class="ulp">{{ rows.exposeEmailCount }}</p>
      </li>
      <li>
        <p class="line">公众号数量</p>
        <p class="ulp">{{ rows.wechatCount }}</p>
      </li>
    </ul>
  </el-card>
  <el-card>
    <div class="title-bottom-line">
      <p>测试时间范围</p>
    </div>
    <p>{{ rows.beginTime }} 至 {{ rows.endTime }}</p>
  </el-card>
  <el-card>
    <div class="title-bottom-line">
      <p>关键词清单</p>
    </div>
    <el-tag type="info" v-for="item in onData" :key="item.id">{{ item.keywords }}</el-tag>
  </el-card>
  <!-- 关键词 -->
  <el-card>
    <div class="title-bottom-line">
      <p>关键词站点</p>
    </div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="keywordsSiteDetail"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
  <!-- GitHub 敏感信息 -->
  <el-card>
    <div class="title-bottom-line">
      <p>GitHub 敏感信息</p>
    </div>
    <common-search
      v-model="gitHubState.data"
      :menu-data="gitHubState.menuData"
      :form-list="gitHubState.formList"
      @search="gitHubsearch"
      @reset="gitHubreset"
    >
    </common-search>
    <xel-table
      ref="gitHubRef"
      :columns="gitHubColumns"
      :load-data="gitHubDetail"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
  <!-- 网盘敏感信息 -->
  <el-card>
    <div class="title-bottom-line">
      <p>网盘敏感</p>
    </div>
    <common-search
      v-model="netDiskState.data"
      :menu-data="netDiskState.menuData"
      :form-list="netDiskState.formList"
      @search="netDisksearch"
      @reset="netDiskreset"
    >
    </common-search>
    <xel-table
      ref="netDiskRef"
      :columns="netDiskColumns"
      :load-data="networkDiskDetail"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
  <!-- 互联网暴露邮箱 -->
  <el-card>
    <div class="title-bottom-line">
      <p>互联网暴露邮箱</p>
    </div>
    <common-search
      v-model="internetState.data"
      :menu-data="internetState.menuData"
      :form-list="internetState.formList"
      @search="internetsearch"
      @reset="internetReset"
    >
    </common-search>
    <xel-table
      ref="internetRef"
      :columns="internetColumns"
      :load-data="exposeEmailDetail"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
      :pagination="false"
      :show-header="false"
    >
    </xel-table>
  </el-card>
  <!-- 公众账号 -->
  <el-card>
    <div class="title-bottom-line">
      <p>公众账号</p>
    </div>
    <common-search
      v-model="publicState.data"
      :menu-data="publicState.menuData"
      :form-list="publicState.formList"
      @search="publictsearch"
      @reset="publicReset"
    >
    </common-search>
    <xel-table
      ref="publictRef"
      :columns="publicColumns"
      :load-data="wechatDetail"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "SensitiveDetails",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  ParentList,
  keywordsSiteDetail,
  gitHubDetail,
  networkDiskDetail,
  exposeEmailDetail,
  wechatDetail,
  keywordsDetail,
} from "@/api/vuln/leakage.js";
const router = useRouter();
const route = useRoute();
let assetsId = route.params.id;
let tableRef = ref();
let gitHubRef = ref();
let netDiskRef = ref();
let internetRef = ref();
let publictRef = ref();
let rows = ref({});
let onData = ref([]);
function name() {
  ParentList({ id: assetsId }).then((res) => {
    rows.value = res.data.rows[0];
  });
  keywordsDetail({ taskId: assetsId }).then((res) => {
    onData.value = res.data;
  });
}
name();
//关键词相关
let searchState = reactive({
  data: {
    url: "",
    domain: "",
    title: "",
  },
  menuData: [],
  formList: [
    {
      prop: "url",
      label: "URl",
    },
    {
      prop: "domain",
      label: "域名",
    },
    {
      prop: "title",
      label: "标题",
    },
  ],
});
const columns = [
  {
    prop: "url",
    label: "URL",
  },
  {
    prop: "domain",
    label: "域名",
  },
  {
    prop: "addr",
    label: "归属地",
  },
  {
    prop: "title",
    label: "标题",
  },
];
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    url: "",
    domain: "",
    title: "",
  };
  search();
}
//GitHub 敏感信息相关
let gitHubState = reactive({
  data: {
    url: "",
  },
  menuData: [],
  formList: [
    {
      prop: "url",
      label: "URl",
    },
  ],
});
function gitHubsearch() {
  gitHubRef.value.reload(gitHubState.data, true);
}
function gitHubreset(params) {
  gitHubState.data = {
    url: "",
  };
  gitHubRef.value.reload();
}
const gitHubColumns = [
  {
    prop: "keywords",
    label: "关键词",
  },
  {
    prop: "url",
    label: "URL",
  },
];
//网盘敏感
const netDiskColumns = [
  {
    prop: "filename",
    label: "文件名",
  },
  {
    prop: "url",
    label: "URL",
  },
];
function netDisksearch() {
  netDiskRef.value.reload(netDiskState.data, true);
}
function netDiskreset(params) {
  netDiskState.data = {
    url: "",
  };
  netDiskRef.value.reload();
}
let netDiskState = reactive({
  data: {
    url: "",
  },
  menuData: [],
  formList: [
    {
      prop: "url",
      label: "URl",
    },
  ],
});
// 互联网暴露邮箱
let internetState = reactive({
  data: {
    email: "",
  },
  menuData: [],
  formList: [
    {
      prop: "email",
      label: "邮箱地址",
    },
  ],
});
const internetColumns = [
  {
    prop: "email",
    label: "邮箱",
  },
];
function internetsearch() {
  internetRef.value.reload(internetState.data, true);
}
function internetReset(params) {
  internetState.data = {
    email: "",
  };
  internetRef.value.reload();
}
//公众账号
let publicState = reactive({
  data: {
    name: "",
  },
  menuData: [],
  formList: [
    {
      prop: "name",
      label: "公众账号名称",
    },
  ],
});
const publicColumns = [
  {
    prop: "name",
    label: "公众账号名称",
  },
  {
    prop: "wechat",
    label: "微信号",
  },
];
function publictsearch() {
  publictRef.value.reload(publicState.data, true);
}
function publicReset(params) {
  publicState.data = {
    name: "",
  };
  publictRef.value.reload();
}
</script>
<style lang="scss" scoped>
.ulname {
  display: flex;
  li {
    width: 16%;
    border-left: 1px solid #ebedf1;
    padding-left: 50px;
    margin-left: 20px;
  }
}
@media screen and (max-width: 1500px) {
  .ulname {
    display: flex;
    li {
      width: 16%;
      border-left: 1px solid #ebedf1;
      padding-left: 23px;
      margin-left: 20px;
    }
  }
}
.ulp {
  color: #28334f;
  font-size: 28px;
  font-weight: 600;
  margin-top: 10px;
}
.line {
  font-size: 14px;
  color: $fontColorSoft;
  font-weight: 300;
}
.el-card {
  margin-bottom: 20px;
}
.el-tag {
  margin-right: 10px;
}
</style>
