<template>
  <el-card>
    <h3 v-if="!isDialog" class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3>
    <statistics :border="true" :list="[{ num: tableRef ? tableRef.staticTotal : 0, text: pageTitle }]"></statistics>
    <div class="clearfix margin-bottom20"></div>
    <common-search v-model="onState.data" :menu-data="onState.menuData" :form-list="onState.formList" @search="search" @reset="reset">
      <el-button icon="el-icon-upload2" @click="newlyAdded" class="search-button" v-if="route.name != 'SafetyWarning'"> 导入 </el-button>
    </common-search>
    <xel-table ref="tableRef" :columns="columns" :load-data="url" @selection-change="handleSelectionChange"> </xel-table>
  </el-card>
  <!-- 安全敏感信息泄露弹框 -->
  <xel-dialog title="导入" ref="dialogRef" @submit="submitForm" @close="closeDialog">
    <div class="title-bottom-line">
      <p>基础信息</p>
    </div>
    <el-form :model="state.formfile" ref="ruleFormRef" label-width="120px" size="mini">
      <xel-form-item v-for="(item, index) in formList" :key="index" v-model="state.formfile[item.prop]" v-bind="item"></xel-form-item>
      <xel-form-item
        form-type="upload"
        prop="file"
        label="测试结果"
        accept=".xlsx,  .xls"
        :fileListFa="fileListFa"
        @fileList="changeFileList"
        :required="true"
      ></xel-form-item>
    </el-form>
    <el-link type="primary" @click="playbook" :underline="false" class="pri">安全敏感信息泄露测试结果模板.xlsx</el-link>
  </xel-dialog>
  <!-- 安全预警弹框 -->
  <xel-dialog title="安全预警" ref="warningRef">
    <el-form ref="form" label-width="120px" label-position="left" class="base-info-form">
      <el-row :gutter="70">
        <el-col :span="24">
          <el-form-item label="预警名称：">{{ sonarning.warnTitle }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="预警发送时间：">{{ sonarning.sendTime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="预警内容描述：">{{ sonarning.warnContent }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-link type="primary" @click="early" :underline="false" class="pri">{{ earlyWarning.detailReportName }}</el-link>
    <template #button>
      <el-button @click="andd"> 关闭 </el-button>
    </template>
  </xel-dialog>
  <!-- 新安全预警--漏洞预警弹框 -->
  <xel-dialog title="安全预警" ref="vulnRef">
    <el-form ref="form" label-width="180px" label-position="left" class="base-info-form">
      <el-row :gutter="70">
        <el-col :span="24">
          <el-form-item label="漏洞编号：">{{ vulning.vid }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="漏洞标题：">{{ vulning.title }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="漏洞类型：">{{ vulning.vulnType }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="漏洞等级：">{{ vulning.vulnNoticeLevel }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="漏洞来源：">{{ vulning.src }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="漏洞公告链接：">{{ vulning.url }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="发布时间：">{{ vulning.intime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="漏洞详情：">{{ vulning.info }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="修复方案：">{{ vulning.solution }}</el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item label="详情里面的文件名：">{{ vulning.fileName }}</el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <el-link type="primary" @click="vulnDownload" :underline="false" class="pri">{{ vulning.fileName }}</el-link>
    <template #button>
      <el-button @click="closeVulnDialog"> 关闭 </el-button>
    </template>
  </xel-dialog>
  <!-- 安全意识弹框 -->
  <xel-dialog title="导入" ref="consciousnessRef" @submit="mailForm" @close="onDialog">
    <el-form :model="state.consciouData" ref="consciouRef" label-width="120px" size="mini">
      <xel-form-item label="评估任务名称" v-model="state.consciouData.name" formType="input" prop="name" :required="true"></xel-form-item>
      <div class="email-domain-list">
        <xel-form-item
          label="测试邮件域"
          v-model="state.consciouData.emailDomainList"
          formType="input"
          prop="emailDomainList"
          :required="true"
        ></xel-form-item>
        <el-form-item label="" style="margin-top: -10px">
          <el-button type="text" @click="addEmail">
            <el-icon :size="12"> <plus /> </el-icon>添加</el-button
          >
        </el-form-item>
        <div v-for="(item, index) in emailList" class="email-domain-item" :key="index">
          <el-form-item label="">
            <el-input v-model="emailList[index].email" placeholder="请输入测试邮件域"></el-input>
            <el-icon class="del-email" @click="removeEmail(index)"><remove-filled /></el-icon>
          </el-form-item>
        </div>
      </div>

      <xel-form-item v-for="(item, index) in consciousness" :key="index" v-model="state.consciouData[item.prop]" v-bind="item"></xel-form-item>
      <xel-form-item form-type="upload" prop="emailFile" label="邮件附件" @fileList="mailAttachment" :required="true"></xel-form-item>
      <xel-form-item
        form-type="upload"
        prop="emailExample"
        label="邮件样例"
        @fileList="mailSample"
        accept=".png,  .jpg"
        :required="true"
      ></xel-form-item>
      <xel-form-item
        form-type="upload"
        prop="excelFile"
        label="测试结果"
        accept=".xlsx,  .xls"
        @fileList="testResult"
        :required="true"
      ></xel-form-item>
      <el-link type="primary" @click="conbook" :underline="false" class="pri">安全意识隐患测试结果模板.xlsx</el-link>
    </el-form>
  </xel-dialog>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { download } from "@/plugins/request";
import { downloadUrl } from "@/api/system/download.js";
import { useRouter, useRoute } from "vue-router";
import { uploadLeakTask, ParentList } from "@/api/vuln/leakage.js";
import { selectSafetyTask, earlywarning, getWarningList, insertSafetyTask } from "@/api/vuln/consciousness.js";
import { loopholeColumns, eventColumns, earlyColumns, vulnColumns, searchState, eventState, warningState, vulnWarningState } from "./security";
const route = useRoute();
const router = useRouter();
let ruleFormRef = ref();
let warningRef = ref();
let vulnRef = ref();
let consciousnessRef = ref();
let consciouRef = ref();

let onName = ref("");
let name = ref("");
let url = ref();
let columns = ref();
let onState = ref();
let pageTitle = ref("");
switch (route.name) {
  case "Leakage":
    onName.value = "安全敏感信息泄露情况";
    name.value = "安全敏感信息泄露列表";
    url.value = ParentList;
    columns.value = loopholeColumns;
    onState.value = searchState;
    pageTitle.value = "监测任务总数";
    break;
  case "HiddenDanger":
    onName.value = "安全意识隐患情况";
    name.value = "安全意识隐患列表";
    url.value = selectSafetyTask;
    columns.value = eventColumns;
    onState.value = eventState;
    pageTitle.value = "评估任务总数";
    break;
  case "SafetyWarningBack":
    name.value = "安全预警列表";
    url.value = earlywarning;
    columns.value = earlyColumns(safetyWarning);
    onState.value = warningState;
    pageTitle.value = "安全预警总数";
    break;
  default:
    onName.value = "安全预警情况";
    name.value = "安全预警列表";
    url.value = getWarningList;
    columns.value = vulnColumns(vulnWarninFn);
    onState.value = vulnWarningState;
    pageTitle.value = "安全预警总数";
}
let tableRef = ref();
let dialogRef = ref();
// 全部配置
let typeObject = reactive({});
// //搜索按钮
function search() {
  tableRef.value.reload(onState.value.data, true);
}
// 重置按钮
function reset() {
  onState.value.data = {
    name: "",
  };
  tableRef.value.reload();
}

let fileListFa = ref([]);

function newlyAdded() {
  if (route.name == "HiddenDanger") {
    consciousnessRef.value.open();
  } else if (route.name == "Leakage") {
    dialogRef.value.open();
  }
}
let consciousness = reactive([
  {
    formType: "date",
    type: "datetimerange",
    prop: "createTimeStr",
    label: "测试时间范围",
    valueFormat: "YYYY-MM-DD HH:mm:ss",
    required: true,
  },
  {
    formType: "editor",
    prop: "emailDetail",
    label: "邮件正文",
    editorClass: "formEditor", //多个编辑器时，命名不同的名字
    editorData: "",
    isClear: false,
    required: true,
    onEditorValue(val) {
      state.consciouData.emailDetail = val;
    },
  },

  {
    formType: "input",
    prop: "emailSenderAddr",
    label: "发件人地址",
    required: true,
    exRule: "email",
  },
  {
    formType: "input",
    prop: "emailSenderName",
    label: "发件人名称",
    required: true,
  },
]);
// 弹框内容
let formList = reactive([
  {
    formType: "input",
    prop: "name",
    label: "监测任务名称",
    required: true,
  },
  {
    formType: "date",
    type: "daterange",
    prop: "createTimeStr",
    label: "监测时间范围",
    valueFormat: "YYYY-MM-DD",
    required: true,
  },
]);

let state = reactive({
  formfile: {
    createTimeStr: "",
    name: "",
    file: "",
  }, //新增编辑表单
  consciouData: {
    emailFile: "",
    emailExample: "",
    excelFile: "",
    name: "",
    emailDetail: "",
    emailSenderAddr: "",
    emailSenderName: "",
    emailDomainList: "",
    createTimeStr: "",
  },
  dataName: {
    warnTitle: "",
    sendTime: "",
    warnContent: "",
  },
});
// 导入按钮
function submitForm() {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      let formData = new FormData();
      formData.append("name", state.formfile.name);
      formData.append("beginTimeStr", state.formfile.createTimeStr[0]);
      formData.append("endTimeStr", state.formfile.createTimeStr[1]);
      formData.append("file", state.formfile.file.raw);
      uploadLeakTask(formData).then((res) => {
        dialogRef.value.close();
        closeDialog();
        tableRef.value.reload(onState.value.data, true);
        ElMessage({
          message: res.msg,
          type: "success",
        });
      });
    }
  });
}

function changeFileList(list) {
  if (list[0]) {
    state.formfile.file = list[0];
  } else {
    state.formfile.file = "";
  }
}
function mailAttachment(list) {
  if (list[0]) {
    state.consciouData.emailFile = list[0];
  } else {
    state.consciouData.emailFile = "";
  }
}
function mailSample(list) {
  if (list[0]) {
    state.consciouData.emailExample = list[0];
  } else {
    state.consciouData.emailExample = "";
  }
}
function testResult(list) {
  if (list[0]) {
    state.consciouData.excelFile = list[0];
  } else {
    state.consciouData.excelFile = "";
  }
}
function closeDialog() {
  state.formfile.createTimeStr = "";
  state.formfile.name = "";
  state.formfile.file = "";
  fileListFa.value = [];
  if (ruleFormRef.value) {
    ruleFormRef.value.clearValidate();
  }
}
function onDialog() {
  state.consciouData.emailFile = "";
  state.consciouData.emailExample = "";
  state.consciouData.excelFile = "";
  state.consciouData.name = "";
  state.consciouData.emailDetail = "";
  state.consciouData.emailSenderAddr = "";
  state.consciouData.emailSenderName = "";
  state.consciouData.emailDomainList = "";
  state.consciouData.createTimeStr = "";
  emailList.value = [];
  fileListFa.value = [];
  if (consciouRef.value) {
    consciouRef.value.clearValidate();
  }
}
let earlyWarning = ref({});
let sonarning = ref({});

// 安全预警弹框
function safetyWarning(row) {
  warningRef.value.open();
  earlyWarning.value = row;
  sonarning.value = row;
}

// 新安全预警

let vulnWarning = ref({});
let vulning = ref({});
// 安全预警弹框
function vulnWarninFn(row) {
  vulnRef.value.open();
  vulnWarning.value = row;
  vulning.value = row;
}
// 安全敏感信息泄露测试结果模板
function playbook() {
  download("/system/leakTask/downloadLeakageTaskTemplate");
}
// 安全预警下载
function early() {
  download(downloadUrl + earlyWarning.value.fileId, earlyWarning.value.detailReportName, {}, "get");
}
// 新安全预警--漏洞预警下载
function vulnDownload() {
  download(downloadUrl + vulning.value.fileId, vulning.value.fileName, {}, "get");
}
// 安全意识导入方法
function mailForm() {
  consciouRef.value.validate((valid) => {
    if (valid) {
      let formData = new FormData();
      formData.append("name", state.consciouData.name);
      formData.append("beginTime", state.consciouData.createTimeStr[0]);
      formData.append("endTime", state.consciouData.createTimeStr[1]);
      formData.append("emailSenderAddr", state.consciouData.emailSenderAddr);
      formData.append("emailSenderName", state.consciouData.emailSenderName);
      formData.append("emailFile", state.consciouData.emailFile.raw);
      formData.append("emailExample", state.consciouData.emailExample.raw);
      formData.append("excelFile", state.consciouData.excelFile.raw);
      formData.append("emailDetail", state.consciouData.emailDetail);

      let emailListStr = emailList.value.reduce((str, item) => {
        if (item.email) {
          return str + "," + item.email;
        } else {
          return str;
        }
      }, state.consciouData.emailDomainList);
      formData.append("domains", emailListStr);
      insertSafetyTask(formData).then((res) => {
        onDialog();
        tableRef.value.reload(onState.value.data, true);
        ElMessage({
          message: res.msg,
          type: "success",
        });
        consciousnessRef.value.close();
      });
    }
  });
}
//安全意识隐患测试结果模板下载方法
function conbook() {
  download("/system/leakTask/downloadSafeTaskTemplate");
}
function andd() {
  warningRef.value.close();
}
function closeVulnDialog() {
  vulnRef.value.close();
}
let emailList = ref([]);
let addEmail = function () {
  emailList.value.push({ email: "" });
};
function removeEmail(index) {
  emailList.value.splice(index, 1);
}
</script>
<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
.pri {
  margin-left: 45px;
  font-size: 12px;
}

.base-info-form {
  :deep(.el-form-item) {
    // border-bottom: 1px solid #ebedf1;
    // margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 10px;
    margin-left: 20px;
  }
}
.email-domain-item {
  margin-top: -10px;
  :deep .el-input {
    width: calc(100% - 30px);
    margin-right: 10px;
  }
  :deep .el-icon {
    font-size: 16px;
    color: #f56c6c;
    vertical-align: middle;
    cursor: pointer;
  }
}
</style>
