<template>
  <security></security>
</template>
<script>
export default {
  name: "HiddenDanger",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted } from "vue";

import { useRouter, useRoute } from "vue-router";
import Security from "../leakage/security.vue";
const route = useRoute();
const router = useRouter();
let tableRef = ref();
</script>
<style lang="scss" scoped>
.card {
  margin-bottom: 20px;
}
</style>
