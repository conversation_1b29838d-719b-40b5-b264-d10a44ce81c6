<template>
  <!-- <h3 class="conH3Tit">{{ "" || ($route.meta && $route.meta.title) }}</h3> -->
  <!-- 评估事件名称 -->
  <el-card>
    <div class="title-bottom-line">
      <p>评估事件名称</p>
    </div>
    <ul class="top">
      <li>
        <el-row :gutter="24">
          <el-col :span="7">
            <div class="tap-tubiao">
              <icon n="icon-nav_baogao" :size="30"></icon></div
          ></el-col>
          <el-col :span="17">
            <p class="tap-title">服务成果统计</p>
            <p class="tap-number">{{ rows.count }}</p></el-col
          >
        </el-row>
      </li>
      <li>
        <p class="tap-title">测试邮箱数量</p>
        <p class="tap-act">{{ rows.testEmailNum }}</p>
      </li>
      <li>
        <p class="tap-title">点击附件数量</p>
        <p class="tap-act">{{ rows.actionType1 }}</p>
      </li>
      <li>
        <p class="tap-title">点击链接数量</p>
        <p class="tap-act">{{ rows.actionType2 }}</p>
      </li>
      <li>
        <p class="tap-title">提交表单数量</p>
        <p class="tap-act">{{ rows.actionType3 }}</p>
      </li>
    </ul>
    <el-row :gutter="70">
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>点击提交行为时间分布</p>
        </div>
        <div ref="indexChart" style="width: 100%; height: 450px"></div>
      </el-col>
      <el-col :span="12">
        <div class="title-bottom-line">
          <p>点击提交行为占比</p>
        </div>
        <pie v-if="onData.length > 0" ref="pieRef" :inData="onData" class="autc"></pie>
      </el-col>
    </el-row>
  </el-card>
  <el-row :gutter="50">
    <el-col :span="12">
      <el-card>
        <div class="title-bottom-line">
          <p>测试时间范围</p>
        </div>
        <div class="begin">
          <el-input v-model="rows.beginTime" disabled placeholder="Please input" /><span class="assc">至</span
          ><el-input v-model="rows.endTime" disabled placeholder="Please input" />
        </div>
      </el-card>
    </el-col>
    <el-col :span="12">
      <el-card>
        <div class="title-bottom-line">
          <p>测试邮箱域</p>
        </div>
        <el-input v-for="item in onDomain" :key="item.id" disabled :placeholder="item.domain" class="assdin"></el-input>
      </el-card>
    </el-col>
  </el-row>
  <!-- 测试邮箱清单 -->
  <el-card>
    <div class="title-bottom-line">
      <p>测试邮箱清单</p>
    </div>
    <common-search v-model="searchState.data" :menu-data="searchState.menuData" :form-list="searchState.formList" @search="search" @reset="reset">
    </common-search>
    <xel-table
      ref="tableRef"
      :columns="columns"
      :load-data="selectTestEmail"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
  <!-- 伪造域选择记录 -->
  <el-card>
    <div class="title-bottom-line">
      <p>伪造域选择记录</p>
    </div>
    <common-search
      v-model="gitHubState.data"
      :menu-data="gitHubState.menuData"
      :form-list="gitHubState.formList"
      @search="gitHubsearch"
      @reset="gitHubreset"
    >
    </common-search>
    <xel-table
      ref="gitHubRef"
      :columns="gitHubColumns"
      :load-data="selectForgeDomain"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
  <!-- 测试记录 -->
  <el-card>
    <div class="title-bottom-line">
      <p>测试记录</p>
    </div>
    <xel-table
      ref="netDiskRef"
      :columns="netDiskColumns"
      :load-data="selectTestRecord"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
  <!-- 伪造邮件文案 -->
  <el-card class="onbottom">
    <div class="title-bottom-line">
      <p>伪造邮件文案</p>
    </div>
    <p class="onspan">邮件正文：</p>
    <p v-html="rows.emailDetail" class="tail"></p>
    <p>
      <span class="onspan">附件：</span>
      <el-link type="primary" @click="onconbook" :underline="false" class="pri">{{ onfileId.fileName }}</el-link>
    </p>
    <p>
      <span class="onspan">邮件样例：</span>
      <el-link type="primary" @click="conbook" :underline="false" class="">{{ ontaskId.fileName }}</el-link>
    </p>
    <p><span class="onspan">发件人名称：</span>{{ rows.emailSenderName }}</p>
    <p><span class="onspan">发件人地址：</span>{{ rows.emailSenderAddr }}</p>
  </el-card>
  <!-- 结果清单 -->
  <el-card>
    <div class="title-bottom-line">
      <p>互联网暴露邮箱</p>
    </div>
    <common-search
      v-model="internetState.data"
      :menu-data="internetState.menuData"
      :form-list="internetState.formList"
      @search="internetsearch"
      @reset="internetReset"
    >
    </common-search>
    <xel-table
      ref="internetRef"
      :columns="internetColumns"
      :load-data="selectTestResult"
      @selection-change="handleSelectionChange"
      :default-params="{ taskId: assetsId }"
    >
    </xel-table>
  </el-card>
</template>
<script>
export default {
  name: "Consciousness",
};
</script>
<script setup>
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  selectSafetyTaskById,
  selectTimeResult,
  getRightPercent,
  selectTestEmail,
  selectForgeDomain,
  selectTestRecord,
  selectTestResult,
  selectEmailDomain,
  selectTaskFile,
} from "@/api/vuln/consciousness.js";
import * as echarts from "echarts";
import { downloadUrl } from "@/api/system/download.js";
import { download } from "@/plugins/request";
const router = useRouter();
const route = useRoute();
let assetsId = route.params.id;
let tableRef = ref();
let gitHubRef = ref();
let netDiskRef = ref();
let internetRef = ref();
let publictRef = ref();
let indexChart = ref();

let rows = ref({});
let onData = ref([]);
let onName = ref([]);
let onValue = ref([]);
let onDomain = ref({});
let onfileId = ref({});
let ontaskId = ref({});
function name() {
  selectSafetyTaskById({ id: assetsId }).then((res) => {
    rows.value = res.data;
  });
  // 柱形图数据;
  selectTimeResult({ taskId: assetsId }).then((res) => {
    res.data.forEach((item) => {
      onName.value.push(item.name);
      onValue.value.push(item.value);
      myChart.setOption(option);
    });
  });
  // 饼图数据
  getRightPercent({ taskId: assetsId }).then((res) => {
    onData.value = res.data;
  });
  // 测试邮箱域数据
  selectEmailDomain({ taskId: assetsId }).then((res) => {
    onDomain.value = res.data.rows;
    console.log(onDomain.value);
  });
  // 伪造邮件文案
  selectTaskFile({ taskId: assetsId }).then((res) => {
    // 附件
    onfileId.value = res.data[0];
    // 照片
    ontaskId.value = res.data[1];
  });
}
name();
//测试邮箱清单
let searchState = reactive({
  data: {
    username: "",
    email: "",
  },
  menuData: [],
  formList: [
    {
      prop: "username",
      label: "邮箱用户名",
    },
    {
      prop: "email",
      label: "邮箱地址",
    },
  ],
});
const columns = [
  {
    prop: "username",
    label: "姓名",
  },
  {
    prop: "email",
    label: "邮箱",
  },
  {
    prop: "contactWay",
    label: "联系方式",
  },
];
function search(initPageNum = true) {
  tableRef.value.reload(searchState.data, initPageNum);
}
function reset() {
  searchState.data = {
    username: "",
    email: "",
  };
  search();
}
//伪造域选择记录相关
let gitHubState = reactive({
  data: {
    domain: "",
  },
  menuData: [],
  formList: [
    {
      prop: "domain",
      label: "域名",
    },
  ],
});
function gitHubsearch() {
  gitHubRef.value.reload(gitHubState.data, true);
}
function gitHubreset(params) {
  gitHubState.data = {
    domain: "",
  };
  gitHubRef.value.reload();
}
const gitHubColumns = [
  {
    prop: "domain",
    label: "域名",
  },
  {
    prop: "actionTypeName",
    label: "域名注册状态",
  },
  {
    prop: "remark",
    label: "备注",
  },
];
//测试记录
const netDiskColumns = [
  {
    prop: "mailfrom",
    label: "mailfrom",
  },
  {
    prop: "from",
    label: "from",
  },
  {
    prop: "name",
    label: "name",
  },
  {
    prop: "recipients",
    label: "收件人",
  },
  {
    prop: "content",
    label: "内容",
  },
  {
    prop: "actionTypeName",
    label: "是否到达",
  },
  {
    prop: "displaySituation",
    label: "显示",
  },
  {
    prop: "remark",
    label: "备注",
  },
];
// 互联网暴露邮箱
let internetState = reactive({
  data: {
    email: "",
    createTimeStr: "",
    host: "",
    macIp: "",
    mail: "",
    ip: "",
    time: "",
    actionType: "",
  },

  menuData: [
    {
      lable: "处置状态",
      prop: "actionType",
      options: [],
      dictName: "action_type",
    },
    {
      lable: "点击时间",
      prop: "time",
      options: [],
      dictName: "time",
    },
  ],
  formList: [
    {
      formType: "date",
      type: "daterange",
      prop: "createTimeStr",
      label: "点击时间",
    },
    {
      formType: "input",
      prop: "host",
      label: "主机/用户名",
    },
    {
      formType: "input",
      prop: "macIp",
      label: "Mac 地址",
    },
    {
      formType: "input",
      prop: "mail",
      label: "邮件地址",
    },
    {
      formType: "input",
      prop: "ip",
      label: "IP地址",
    },
  ],
});
const internetColumns = [
  {
    prop: "host",
    label: "主机/用户名",
  },
  {
    prop: "macIpMail",
    label: "MAC-IP/Mail",
  },
  {
    prop: "postData",
    label: "POST Data",
  },
  {
    prop: "ip",
    label: "IP",
  },
  {
    prop: "actionTypeName",
    label: "行为",
  },
  {
    prop: "actionTime",
    label: "时间",
  },
  {
    prop: "remark",
    label: "备注",
  },
];
function internetsearch() {
  let params = { ...internetState.data, beginTime: "", endTime: "" };
  if (internetState.data.createTimeStr && internetState.data.createTimeStr.length > 0) {
    params.beginTime = internetState.data.createTimeStr[0];
    params.endTime = internetState.data.createTimeStr[1];
  }
  delete params.createTimeStr;
  internetRef.value.reload(params, true);
}
function internetReset(params) {
  internetState.data = {
    email: "",
  };
  internetRef.value.reload();
}
let myChart = null;
let option = reactive({});
// 柱状图
onMounted(() => {
  myChart = echarts.init(indexChart.value);
  option = {
    xAxis: {
      type: "category",
      data: onName.value,
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        data: onValue.value,
        type: "bar",
      },
    ],
  };
});
function onconbook() {
  download(downloadUrl + onfileId.value.fileId, onfileId.value.fileName, {}, "get");
}
function conbook() {
  download(downloadUrl + ontaskId.value.fileId, ontaskId.value.fileName, {}, "get");
}
</script>
<style lang="scss" scoped>
.top {
  width: 100%;
  // height: 112px;
  display: flex;
  margin-bottom: 32px;
  li {
    margin-right: 30px;
    display: inline-block;
    width: 20%;
    background: #ffffff;
    border-radius: $radiusL;
    padding: 15px;
    border-left: 1px solid $bgColor;
  }
  li:last-child {
    margin: 0;
  }
}
.tap-act {
  font-size: 28px;
  font-weight: 600;
  color: #28334f;
}
.el-card {
  margin-bottom: 20px;
}
.assc {
  margin-top: 5px;
}

.autc {
  margin: 0 auto;
}
.begin {
  display: flex;
  .el-input {
    width: 45%;
    margin: 0 10px 0 10px;
  }
}
.assdin {
  margin-bottom: 10px;
}
.tail {
  margin: 20px 20px 20px 60px;
  font-size: 16px;
  font-weight: 400;
  color: #353d4a;
  width: 80%;
  word-wrap: break-word;
}
.tailpri {
  margin: 20px 20px 20px 60px;
}
.onspan {
  font-size: 14px;
  font-weight: 400;
  color: $fontColorSoft;
}
.onbottom {
  p {
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 1700px) {
  .tap-number {
    padding-left: 20px;
  }
  .tap-title {
    padding-left: 20px;
  }
  .tap-act {
    padding-left: 20px;
  }
}
</style>
