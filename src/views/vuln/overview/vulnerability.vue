<template>
  <p class="tap-title">{{ titleName }}</p>
  <p class="tap-number">{{ onNane.vulnTotal }}</p>
  <ul class="level-data">
    <li>
      <span class="level-color" :style="{ background: Level_Color.urgent }"></span><span>{{ onNane.urgentRiskCount }}</span>
    </li>
    <li>
      <span class="level-color" :style="{ background: Level_Color.hight }"></span><span>{{ onNane.hightRiskCount }}</span>
    </li>
    <li>
      <span class="level-color" :style="{ background: Level_Color.middle }"></span><span>{{ onNane.middleRiskCount }}</span>
    </li>
    <li>
      <span class="level-color" :style="{ background: Level_Color.low }"></span><span>{{ onNane.lowRiskCount }}</span>
    </li>
  </ul>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, resolveDynamicComponent } from "vue";
import { getVulnLevelSummary } from "@/api/vuln/overview.js";
import { Level_Color } from "@/config/constant";
let props = defineProps({
  titleName: {
    type: String,
    default: "",
  },
  dataSize: {
    type: String,
    default: "",
  },
});
let onNane = ref({});
function name() {
  getVulnLevelSummary({ vulnParam: props.dataSize }).then((res) => {
    onNane.value = res.data;
  });
}
name();
</script>
<style lang="scss" scoped>
.level-data {
  display: flex;
  li {
    margin-right: 20px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }
}
.level-color {
  margin-right: 10px;
}
.tap-number {
  margin-bottom: 10px;
}
.tap-number {
  font-size: 28px;
  font-weight: 600;
  color: #28334f;
}
</style>
