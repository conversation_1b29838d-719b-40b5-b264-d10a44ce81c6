<template>
  <div class="home-loophole">
    <el-row :gutter="20" class="head">
      <el-col :span="5" class="onnote"
        ><el-card>
          <el-row :gutter="24">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-nav_loudong" :size="30"></icon></div
            ></el-col>
            <el-col :span="17" class="loudong">
              <p class="tap-title">已发现安全漏洞</p>
              <p class="tap-number">{{ activiName.vulnTotal }}</p></el-col
            >
            <el-divider></el-divider>
            <ul class="level-data">
              <li>
                <span class="level-color" :style="{ background: Level_Color.urgent }"></span> <span class="fontstyle">紧急</span>
                <span class="fontcolo">{{ activiName.urgentRiskCount }}</span>
              </li>
              <li>
                <span class="level-color" :style="{ background: Level_Color.hight }"></span> <span class="fontstyle">高危</span>
                <span class="fontcolo">{{ activiName.hightRiskCount }}</span>
              </li>
              <li>
                <span class="level-color" :style="{ background: Level_Color.middle }"></span> <span class="fontstyle">中危</span>
                <span class="fontcolo">{{ activiName.middleRiskCount }}</span>
              </li>
              <li>
                <span class="level-color" :style="{ background: Level_Color.low }"></span> <span class="fontstyle">低危</span>
                <span class="fontcolo">{{ activiName.lowRiskCount }}</span>
              </li>
            </ul>
          </el-row>
        </el-card></el-col
      >
      <el-col :span="19" class="onnote"
        ><el-card>
          <el-row :gutter="40">
            <el-col :span="6">
              <vulnerability titleName="已发现业务系统漏洞" dataSize="businessFind"></vulnerability>
              <vulnerability titleName="已发现基础资源漏洞" dataSize="basicFind"></vulnerability>
            </el-col>
            <el-col :span="6">
              <vulnerability titleName="待修复业务系统漏洞" dataSize="businessNotRepair"></vulnerability>
              <vulnerability titleName="待修复基础资源漏洞" dataSize="basicNotRepair"></vulnerability>
            </el-col>
            <el-col :span="6">
              <vulnerability titleName="已修复业务系统漏洞" dataSize="businessRepair"></vulnerability>
              <vulnerability titleName="已修复基础资源漏洞" dataSize="basicRepair"></vulnerability>
            </el-col>
            <el-col :span="6">
              <p class="tap-title">已完成敏感信息泄露测试</p>
              <p class="tap-number">{{ sensitive }}</p>
              次
              <p class="tap-title">已完成安全意识评估</p>
              <p class="tap-number">{{ assessment }}</p>
              次
            </el-col>
          </el-row>
        </el-card></el-col
      >
    </el-row>
    <el-row :gutter="20" class="chart">
      <el-col :span="10"
        ><el-card>
          <p class="name">
            已发现漏洞类型分布
            <echart-edit-btn echartId="vulnOverViewEchart1" :dataset="vulnTypeOption.dataset" :color="vulnTypeOption.color"></echart-edit-btn>
          </p>
          <echart-component
            echartType="2"
            echartId="vulnOverViewEchart1"
            :options="vulnTypeOption"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
            class="echart-box"
          ></echart-component> </el-card
      ></el-col>
      <el-col :span="14"
        ><el-card>
          <p class="name">
            已发现漏洞（Top10）整改情况分布
            <echart-edit-btn echartId="vulnOverViewEchart2" :dataset="onChartOption.dataset"></echart-edit-btn>
          </p>
          <div style="height: 450px">
            <echart-component
              echartType="0"
              echartId="vulnOverViewEchart2"
              :options="onChartOption"
              @updateEchart="updateEchart"
              style="width: 100%; height: 450px"
              class="echart-box"
            ></echart-component>
          </div> </el-card
      ></el-col>
    </el-row>
    <el-row :gutter="20" class="pictures">
      <el-col :span="10"
        ><el-card>
          <p class="name">
            高危漏洞变化趋势（近6个月）
            <echart-edit-btn echartId="vulnOverViewEchart3" :dataset="option.dataset"></echart-edit-btn>
          </p>
          <echart-component
            echartType="1"
            echartId="vulnOverViewEchart3"
            :options="option"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
            class="echart-box"
          ></echart-component></el-card
      ></el-col>
      <el-col :span="14"
        ><el-card>
          <p class="name">
            漏洞发现/整改态势图
            <echart-edit-btn echartId="vulnOverViewEchart4" :dataset="opsituation.dataset"></echart-edit-btn>
          </p>
          <echart-component
            echartType="1"
            echartId="vulnOverViewEchart4"
            :options="opsituation"
            @updateEchart="updateEchart"
            style="width: 100%; height: 450px"
            class="echart-box"
          ></echart-component>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
import { ref, reactive, toRefs, onMounted, markRaw } from "vue";
import Vulnerability from "./vulnerability.vue";
import {
  getVulnLevelSummary,
  getVulnHighOrAboveSummary,
  getVulnFindOrCorrectSummary,
  getVulnCorrectRateSummary,
  getLeakageSummary,
  getSafeTySummary,
} from "@/api/vuln/overview.js";
import { Level_Color } from "@/config/constant";
import * as echarts from "echarts";
let activities = ref([]);
let activiName = ref({});
let onName = ref([]);
let onValue = ref([]);
let indexChart = ref();
let indexsituation = ref();
let noData = {
  title: {
    text: "暂无数据",
    x: "center",
    y: "center",
    textStyle: {
      color: "#848484",
      fontWeight: "normal",
      fontSize: 16,
    },
  },
};
// 已发现漏洞类型分布
let vulnTypeOption = ref({});
vulnTypeOption.value = {
  tooltip: {
    trigger: "item",
    position: ["40%", "60%"],
  },
  color: ["#fe804d", "#eeca5f", "#4c70f4", "#5bce8f"],
  legend: {
    top: "5%",
    left: "center",
  },
  dataset: {
    source: [],
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "70%"],
      center: ["50%", "60%"],

      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      label: {
        show: false,
        position: "center",
      },
      emphasis: {
        label: {
          show: false,
          fontSize: "20",
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: false,
      },
    },
  ],
};
// 已发现漏洞top10
let onChartOption = ref({});
let emphasisStyle = {
  itemStyle: {
    shadowBlur: 10,
    shadowColor: "rgba(0,0,0,0.3)",
  },
};
onChartOption.value = {
  legend: {},
  dataset: {
    source: [],
  },
  xAxis: {
    name: "漏洞名称",
    type: "category",
    axisLabel: {
      interval: 0,
      rotate: -20,
      formatter: function (value) {
        var texts = value;
        if (texts.length > 8) {
          // 具体显示的字数
          texts = texts.substr(0, 8) + "...";
        }
        return texts;
      },
    },
    splitLine: { show: false },
    splitArea: { show: false },
  },
  toolbox: {},
  tooltip: {},
  yAxis: { type: "value" },
  grid: {
    bottom: 50,
  },
  series: [
    {
      type: "bar",
      stack: "one",
      emphasis: emphasisStyle,
    },
    {
      type: "bar",
      stack: "one",
      emphasis: emphasisStyle,
    },
  ],
};
let rectificationName = ref([]);
let rectificationvalias = ref([]);
let rectificationvalue = ref([]);
let sensitive = ref("");
let assessment = ref("");
// 高危漏洞变化趋势
let option = ref({});
option.value = {
  tooltip: {
    formatter: function (params) {
      return `${params.name}<br/>${`漏洞量`}：${params.value}`;
    },
  },
  xAxis: {
    type: "category",
    data: onName.value,
  },
  yAxis: {
    type: "value",
  },
  dataset: {
    source: onValue.value,
  },
  series: [
    {
      type: "line",
    },
  ],
};
function summary() {
  // 饼图数据
  getVulnLevelSummary().then((res) => {
    activiName.value = res.data;
    activities.value.push(["等级", "数量"]);
    activities.value.push(["紧急", res.data.urgentRiskCount]);
    activities.value.push(["高危", res.data.hightRiskCount]);
    activities.value.push(["中危", res.data.middleRiskCount]);
    activities.value.push(["低危", res.data.lowRiskCount]);
    vulnTypeOption.value.dataset.source = activities;
  });
  // 高危漏洞变化趋势数据
  getVulnHighOrAboveSummary().then((res) => {
    res.data.vulnList.forEach((item) => {
      onName.value.push(item.name);
      onValue.value.push([item.name, item.value]);
      option.value.dataset.source = onValue.value;
    });
  });
  // 漏洞发现/整改态势图数据
  getVulnFindOrCorrectSummary().then((res) => {
    let data = res.data;
    let source = [["product", "已发现", "已整改"]];
    res.data.vulnCorrectList.forEach((item, index) => {
      source.push([data.monthList[index], data.vulnFindList[index].value, item.value]);
    });
    opsituation.value.dataset.source = source;
  });
  // 已发漏洞分布
  getVulnCorrectRateSummary().then((res) => {
    let source = [];
    source.push(["product", "已整改", "未整改"]);
    let data = res.data;
    for (let i = 0; i < data.vulnTypeList.length; i++) {
      source.push([data.vulnTypeList[i], data.vulnCorrectRateList[i], data.vulnNotCorrectRateList[i]]);
    }
    onChartOption.value.dataset.source = source;
  });
  // 已完成敏感信息泄露测试
  getLeakageSummary().then((res) => {
    sensitive.value = res.data;
  });
  // 已完成安全意识评估
  getSafeTySummary().then((res) => {
    assessment.value = res.data;
  });
}
summary();

// 高危漏洞变化图
onMounted(() => {});
let opsituation = ref({});
opsituation.value = {
  tooltip: {},
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
  },
  yAxis: {
    type: "value",
  },
  dataset: {
    source: [],
  },
  series: [
    {
      type: "line",
    },
    {
      type: "line",
    },
  ],
};

// 漏洞发现/整改态势图
function opsituationfn(source) {}

let echartType1 = ref(2);
function updateEchart(echartId, option) {}
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}
.fontstyle {
  font-size: 10px;
  font-weight: 300;
  color: $fontColorSoft;
  margin-right: 20px;
}
.level-data {
  display: flex;
  flex-wrap: wrap;
  li {
    width: 50%;
    margin-bottom: 7px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }
}
.fontcolo {
  font-size: 24px;
  font-weight: 600;
  color: #28334f;
}
.level-color {
  margin-right: 5px;
}
.name {
  font-weight: 400;
  color: $fontColor;
  font-size: 16px;
  margin-bottom: 10px;
}
.autc {
  margin: 0 auto;
  width: 100%;
  height: 450px;
}
</style>
