<template>
  <div v-if="!needChangePassword" class="home-wrapper">
    <el-row class="home-middle">
      <el-col :span="24"
        ><el-card>
          <p>服务开通情况</p>
          <ul class="situation" v-if="ServiceOpening.length > 0 || stateData.serviceSituat === '1' || stateData.serviceScore === '1'">
            <li v-for="item in ServiceOpening" :key="item.count">
              <div class="ation" v-if="item.openState == '1'">
                <p class="ion">{{ item.serviceName }}</p>
                <el-tag type="success">已开通</el-tag>
              </div>
              <div class="ation" v-else>
                <p class="ion">{{ item.serviceName }}</p>
                <el-tag type="danger">未开通</el-tag>
              </div>
              <div class="number" v-if="item.openState == '1'">
                <p class="nubur">
                  服务数量 <span>{{ item.count }}</span>
                </p>
                <p class="nubur" v-if="item.service.indexOf('次') == -1">
                  服务期 <span>{{ item.service }}</span>
                </p>
                <p class="nubur" v-if="item.service.indexOf('次') != -1">
                  服务次数 <span>{{ item.service }}</span>
                </p>
              </div>
            </li>
          </ul>
        </el-card></el-col
      >
    </el-row>
    <ul class="home-card">
      <li>
        <el-card>
          <el-row :gutter="24">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-yuming" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title" :style="$globalWindowSize == 'S' ? { 'margin-left': ' -6px', 'white-space': 'nowrap' } : {}">已发现中高危资产</p>
              <p class="tap-number">{{ calculateBooksMessage }}</p></el-col
            >
          </el-row>
          <div class="number">
            <p class="nubur pointer" @click="distribution(4)">
              业务系统资产 <span>{{ highRisk.busCount }}</span>
            </p>
            <p class="nubur pointer" @click="distribution(5)">
              计算设备资产<span>{{ highRisk.basCount }}</span>
            </p>
          </div>
          <el-tag>最近15天</el-tag>
        </el-card>
      </li>
      <li>
        <el-card>
          <el-row :gutter="24">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-anfengxiantongji" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title">已处理告警</p>
              <p class="tap-number">{{ giveAn.count }}</p></el-col
            >
          </el-row>
          <ul class="level-data">
            <li @click="urgent(1, 5)">
              <span class="level-color" :style="{ background: Level_Color.urgent }"></span><span class="pointer">{{ giveAn[5] }}</span>
            </li>
            <li @click="urgent(1, 4)">
              <span class="level-color" :style="{ background: Level_Color.hight }"></span><span class="pointer">{{ giveAn[4] }}</span>
            </li>
            <li @click="urgent(1, 3)">
              <span class="level-color" :style="{ background: Level_Color.middle }"></span><span class="pointer">{{ giveAn[3] }}</span>
            </li>
            <li @click="urgent(1, 2)">
              <span class="level-color" :style="{ background: Level_Color.low }"></span><span class="pointer">{{ giveAn[2] }}</span>
            </li>
            <li @click="urgent(1, 1)">
              <span class="level-color" :style="{ background: Level_Color.info }"></span><span class="pointer">{{ giveAn[1] }}</span>
            </li>
          </ul>
          <el-tag>最近15天</el-tag>
        </el-card>
      </li>
      <li>
        <el-card>
          <el-row :gutter="24">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-redianshijian" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title">已分析安全事件</p>
              <p class="tap-number">{{ event.count }}</p></el-col
            >
          </el-row>
          <ul class="level-data">
            <li @click="urgent(2, 4)">
              <span class="level-color" :style="{ background: Level_Color.urgent }"></span><span class="pointer">{{ event[4] }}</span>
            </li>
            <li @click="urgent(2, 3)">
              <span class="level-color" :style="{ background: Level_Color.hight }"></span><span class="pointer">{{ event[3] }}</span>
            </li>
            <li @click="urgent(2, 2)">
              <span class="level-color" :style="{ background: Level_Color.middle }"></span><span class="pointer">{{ event[2] }}</span>
            </li>
            <li @click="urgent(2, 1)">
              <span class="level-color" :style="{ background: Level_Color.low }"></span><span class="pointer">{{ event[1] }}</span>
            </li>
          </ul>
          <el-tag>最近15天</el-tag>
        </el-card>
      </li>
      <li>
        <el-card>
          <el-row :gutter="24">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-nav_loudong" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title">已发现安全漏洞</p>
              <p class="tap-number">{{ loophole.vulnTotal }}</p></el-col
            >
          </el-row>
          <ul class="level-data">
            <li>
              <span class="level-color" :style="{ background: Level_Color.urgent }"></span><span>{{ loophole.urgentRiskCount }}</span>
            </li>
            <li>
              <span class="level-color" :style="{ background: Level_Color.hight }"></span><span>{{ loophole.hightRiskCount }}</span>
            </li>
            <li>
              <span class="level-color" :style="{ background: Level_Color.middle }"></span><span>{{ loophole.middleRiskCount }}</span>
            </li>
            <li>
              <span class="level-color" :style="{ background: Level_Color.low }"></span><span>{{ loophole.lowRiskCount }}</span>
            </li>
          </ul>
          <el-tag>服务期内</el-tag></el-card
        >
      </li>
      <li>
        <el-card>
          <el-row :gutter="24" class="tter">
            <el-col :span="7">
              <div class="tap-tubiao">
                <icon n="icon-nav_baogao" :size="30"></icon></div
            ></el-col>
            <el-col :span="17">
              <p class="tap-title">已提交报告</p>
              <p class="tap-number">{{ data }}</p></el-col
            >
          </el-row>
          <el-tag>服务期内</el-tag>
        </el-card>
      </li>
    </ul>
    <el-row :gutter="20" class="spanlist">
      <el-col :span="8">
        <el-card>
          <section class="echart-container">
            <p class="name">
              高危漏洞变化趋势(近6个月)
              <echart-edit-btn echartId="homeEchart1" :dataset="lineChartOption.dataset"></echart-edit-btn>
            </p>
            <echart-component
              echartType="1"
              echartId="homeEchart1"
              :options="lineChartOption"
              @updateEchart="updateEchart"
              style="width: 100%; height: 450px"
              class="echart-box"
            ></echart-component>
            <!-- <div ref="indexChart" style="width: 100%; height: 450px" class="echart-box"></div> -->
          </section>
        </el-card>
      </el-col>
      <el-col :span="8"
        ><el-card>
          <section class="echart-container">
            <p class="name" @click="distribution(1)">
              安全事件类型分布(近6个月)
              <echart-edit-btn echartId="homeEchart2" :dataset="pieChartOption.dataset"></echart-edit-btn>
            </p>
            <echart-component
              echartType="2"
              echartId="homeEchart2"
              :options="pieChartOption"
              @updateEchart="updateEchart"
              style="width: 100%; height: 450px"
              class="echart-box"
            ></echart-component>
          </section> </el-card
      ></el-col>
      <el-col :span="8"
        ><el-card>
          <section class="echart-container">
            <div class="disply">
              <p class="name" @click="distribution(2)">
                告警类型占比
                <echart-edit-btn echartId="homeEchart3" :dataset="opsituation.dataset"></echart-edit-btn>
              </p>
              <el-radio-group v-model="radio1" @change="alertTypeCount(radio1)">
                <el-radio-button label="1">周</el-radio-button>
                <el-radio-button label="2">月</el-radio-button>
                <el-radio-button label="3">年</el-radio-button>
              </el-radio-group>
            </div>
            <echart-component
              echartType="2"
              echartId="homeEchart3"
              :options="opsituation"
              @updateEchart="updateEchart"
              style="width: 100%; height: 450px"
              class="echart-box"
            ></echart-component>
            <!-- <div ref="indexsituation" style="height: 450px" class="echart-box"></div> -->
          </section>
        </el-card>
      </el-col>
    </el-row>
  </div>
  <reset-psd v-else></reset-psd>
</template>
<script setup>
import { ref, reactive, toRefs, computed, onMounted } from "vue";
import { selectSoftListByPage } from "@/api/securityAssets/assetsList";
import resetPsd from "@/views/system/resetPsd.vue";
import * as echarts from "echarts";

import {
  listServices,
  getAlertDealCount,
  getEventAnalysisCount,
  getVulnLevelStatistics,
  getReportCount,
  getVulnHighOrAboveSummary,
  getEventTypeCount,
  selectServiceStatus,
  getAlertTypeCount,
  getIpAssetRiskNum,
} from "@/api/workSpace/home.js";
import { Level_Color } from "@/config/constant";

import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
const store = useStore();
const route = useRoute();
const router = useRouter();

let needChangePassword = computed(() => {
  return store.state.needChangePassword;
});
let ServiceOpening = ref([]);
let giveAn = ref({});
let event = ref({});
let loophole = ref({});
let data = ref("");
let stateData = ref({});
let highRisk = ref({});
let radio1 = ref("3");
let noData = reactive({
  data: {
    title: {
      text: "暂无数据",
      x: "center",
      y: "center",
      textStyle: {
        color: "#848484",
        fontWeight: "normal",
        fontSize: 16,
      },
    },
  },
});
let indexsituation = ref();
let lineEchart = {
  dom: {},
};
// 折线图
let lineChartOption = ref({});
lineChartOption.value = {
  tooltip: {
    formatter: function (params) {
      return `${params.name}<br/>${`漏洞量`}：${params.value}`;
    },
  },
  dataset: {
    source: [],
  },
  xAxis: { type: "category" },
  yAxis: {},
  series: [
    {
      type: "line",
    },
  ],
};
// 饼图
let pieChartOption = ref({});
pieChartOption.value = {
  dataset: {
    source: [],
  },
  tooltip: {
    trigger: "item",
    position: ["40%", "60%"],
  },
  legend: {
    top: "5%",
    left: "center",
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "70%"],
      center: ["50%", "60%"],

      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 2,
      },
      label: {
        show: false,
        position: "center",
      },
      emphasis: {
        label: {
          show: false,
          fontSize: "20",
          fontWeight: "bold",
        },
      },
      labelLine: {
        show: false,
      },
    },
  ],
};
let pieEchart = ref(null);
let pieRef = ref();
function getlistServices(params) {
  // 权限判断
  selectServiceStatus().then((res) => {
    if (res.data) {
      stateData.value = res.data;
    }
  });
  // 服务开通情况
  listServices().then((res) => {
    ServiceOpening.value = res.data;
  });
  // 已处理告警
  let ass = {
    dayCount: 15,
  };
  getAlertDealCount(ass).then((res) => {
    giveAn.value = res.data;
  });
  // 高危资产
  getIpAssetRiskNum(ass).then((res) => {
    highRisk.value = res.data;
  });
  // 已分析安全事件
  getEventAnalysisCount(ass).then((res) => {
    event.value = res.data;
  });
  // 已发现安全漏洞
  getVulnLevelStatistics().then((res) => {
    loophole.value = res.data;
  });
  // 已提交报告
  getReportCount().then((res) => {
    data.value = res.data;
  });
  // 折线图

  getVulnHighOrAboveSummary().then((res) => {
    let source = [];
    source.push(["name", "漏洞量"]);
    res.data.vulnList.forEach((item) => {
      source.push([item.name, item.value]);
    });
    lineChartOption.value.dataset = {
      source: source,
    };
  });
  // 饼图
  getEventTypeCount().then((res) => {
    let activities = [];
    res.data.names.forEach((item, index) => {
      activities.push([item, res.data.counts[index]]);
    });
    pieChartOption.value.dataset = {
      source: activities,
    };
    let noData = {
      title: {
        text: "暂无数据",
        x: "center",
        y: "center",
        textStyle: {
          color: "#848484",
          fontWeight: "normal",
          fontSize: 16,
        },
      },
    };
  });
}
let timeType = ref(3);
function alertTypeCount(val) {
  // 年月日饼图
  timeType.value = val;
  getAlertTypeCount({ timeType: timeType.value }).then((res) => {
    let data = res.data.list;
    let arr = [];
    for (let key in data) {
      arr.push([key, data[key]]);
    }
    opsituation.value.dataset.source = arr;
  });
}
onMounted(() => {
  getlistServices();
  alertTypeCount(3);
});

// 告警类型占比
let opsituation = ref({});
opsituation.value = {
  legend: {
    top: "top",
  },
  grid: {
    bottom: 30,
    top: 50,
  },
  dataset: {
    source: [],
  },
  series: [
    {
      type: "pie",
      radius: [40, window.innerWidth < 1550 ? 120 : 150],
      center: ["50%", "60%"],
      roseType: "area",
      itemStyle: {
        borderRadius: 8,
      },
    },
  ],
};
// 跳转
function distribution(val) {
  if (val == 1) {
    router.push({
      name: "ThreatEvent",
    });
  } else if (val == 2) {
    router.push({
      name: "AlarmManagement",
    });
  } else if (val == 3) {
    router.push({
      name: "Report",
    });
  } else if (val == 4) {
    router.push({
      name: "BusinessAssets",
      query: { priority: 1 },
    });
  } else if (val == 5) {
    router.push({
      name: "Underlying",
      query: { priority: 1 },
    });
  }
}
//
function urgent(time, level) {
  let query = { priority: level };
  if (time == 1) {
    router.push({
      name: "AlarmManagement",
      params: { id: time },
      query,
    });
  } else {
    router.push({
      name: "ThreatEvent",
      params: { id: time },
      query,
    });
  }
}
// 计算属性
let calculateBooksMessage = computed(() => {
  let a = 0;
  let b = 0;
  if (highRisk.value) {
    a = highRisk.value.basCount || 0;
    b = highRisk.value.busCount || 0;
  }
  return a + b;
});
// echarts修改图形后传值到容器界面
function updateEchart(echartId, option) {
  if (echartId == "homeEchart1") {
    // lineChartOption.value = option;
  }
}
</script>

<style lang="scss" scoped>
.disply {
  display: flex;
  justify-content: space-between;
}
.spanlist {
  margin-top: 20px;
}
.level-color {
  margin-right: 5px;
}
.home-card {
  display: flex;

  li {
    width: 20%;
    margin-right: 20px;
  }
  li:last-child {
    margin: 0;
  }
}
.el-row {
  margin-bottom: 20px;
}
.title {
  font-size: 16px;
  font-weight: 400;
  color: $fontColor;
  margin-bottom: 10px;
}
.number {
  display: flex;
  margin-top: 10px;
  margin-bottom: 5px;
  p {
    margin-right: 20px;
  }
}
.el-tag {
  border-radius: 15px;
}
.level-data {
  display: flex;
  margin-bottom: 25px;
  white-space: nowrap;
  li {
    white-space: nowrap;
    span {
      white-space: nowrap;
    }
  }
}
.situation {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  li {
    width: 20%;
    // flex: 0;
    border-left: 3px solid $color;
    padding-left: 50px;
  }
}
.ation {
  display: flex;
}
.ion {
  font-size: 14px;
  font-weight: 400;
  color: $fontColor;
  margin-right: 15px;
}
.nubur {
  font-size: 12px;
  font-weight: 300;
  color: $fontColorSoft;
  margin-bottom: 20px;
  span {
    font-size: 14px;
    font-weight: 400;
    color: $fontColor;
    margin-left: 5px;
    margin-right: 3px;
  }
}
.name {
  font-weight: 400;
  color: $fontColor;
  font-size: 16px;
  margin-bottom: 10px;
  cursor: pointer;
}
.autc {
  margin: 0 auto;
}
.tubiao {
  display: flex;
}
.tnumber {
  margin: 23px 0 0 38px;
  font-size: 36px;
  font-weight: 600;
  color: #28334f;
}
.tter {
  margin-bottom: 62px;
}
.echart-container {
  min-height: 412px;
}
</style>
