<template>
  <div class="login-wrapper">
    <img v-if="isImgTitle" class="system-title" src="@/assets/imgs/systemTitle.png" alt="" />
    <h1 v-else class="system-title">{{ systemTitle }}</h1>

    <el-form ref="formRef" :model="form" :rules="rules" class="login-form">
      <el-form-item prop="username">
        <el-input v-model.trim="form.username" type="text" auto-complete="off" placeholder="账号" id="username" name="username">
          <template #prefix>
            <el-icon><user /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model.trim="form.password" type="password" auto-complete="off" placeholder="密码" id="password" name="password">
          <template #prefix>
            <el-icon><lock /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="isCaptchaOnOff">
        <el-input v-model="form.code" auto-complete="off" placeholder="验证码" style="width: 63%" @keyup.enter="handleLogin" id="code" name="code">
          <template #prefix>
            <el-icon><finished /></el-icon
          ></template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" alt="" class="login-code-img" />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="medium" @click="handleLogin" :loading="loading"> 登 录 </el-button>
      </el-form-item>
    </el-form>
    <p class="version-box" v-if="versionNo">[{{ versionNo }},UIV1.8.1_20240716]</p>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import { getCodeImg, login, getChangeLicense, geterrorInfo } from "@/api/login";
import { encrypt, decrypt, ciiEncrypt } from "@/utils/jsencrypt";
import { TOKEN_NAME } from "@/config/constant";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { getScreenToken as getScreenTokenApi } from "@/api/screen/login";
import { SCREEN_TOKEN_NAME } from "@/config/constant";

const isImgTitle = import.meta.env.VITE_LOGIN_IMG;
const systemTitle = import.meta.env.VITE_SYSTEM_TITLE;
let versionNo = window.versionNo;
const store = useStore();
const router = useRouter();
const route = useRoute();
let formRef = ref(); //设置form的ref 以调用el-form的方法
let loading = ref(false);
//form rules
let form = reactive({
  uuid: "",
  username: "",
  password: "",
});
const rules = {
  username: [
    {
      required: true,
      trigger: "blur",
      message: "用户名不能为空",
    },
  ],
  password: [
    {
      required: true,
      trigger: "blur",
      message: "密码不能为空",
    },
  ],
  code: [
    {
      required: true,
      trigger: "change",
      message: "验证码不能为空",
    },
  ],
};
//验证码
let isCaptchaOnOff = ref(true); //是否显示验证码
let codeUrl = ref("");
function getCode() {
  getCodeImg().then((res) => {
    isCaptchaOnOff.value = res.captchaOnOff;
    codeUrl.value = "data:image/gif;base64," + res.img;
    form.uuid = res.uuid;
  });
}

//登录
function handleLogin() {
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      login({ ...form, password: ciiEncrypt(form.password) })
        .then((res) => {
          localStorage.setItem(TOKEN_NAME, res.data.access_token);

          //判断当前系统授权问题;
          // if (import.meta.env.VITE_IS_SIME) {
          //   loginSuccess();
          // } else {
          getChangeLicense().then(({ data }) => {
            if (data.changeLicense == "0") {
              loginSuccess();
            } else {
              //授权异常  判断是否有权限进行系统授权
              if (data.hasLicensePermission === true) {
                router.push({ path: "/system/license" });
              } else {
                //没有权限-提醒联系管理员
                geterrorInfo().then(({ data }) => {
                  ElMessage.info(data.licenseError);
                  loading.value = false;
                });
              }
            }
          });
          // }
        })
        .catch(() => {
          loading.value = false;
          getCode();
        });
    }
  });
}

//登录成功跳转
function loginSuccess() {
  store.dispatch("loginSuccess");

  if (route.query && route.query.redirect) {
    router.push(route.query.redirect);
  } else {
    //  siem版登录成功后跳转菜单第一个页面
    if (!import.meta.env.VITE_IS_SIME) {
      router.push("/");
    }
  }

  //更新大屏token
  if (!import.meta.env.VITE_IS_SIME) {
    getScreenTokenApi().then((tokenRes) => {
      if (tokenRes.code == 200) {
        localStorage.setItem(SCREEN_TOKEN_NAME, tokenRes.data.access_token);
      }
    });
  }
}
//页面加载时执行
onMounted(() => {
  getCode();
  store.commit("clearWebinfo");
});
</script>

<style lang="scss" scoped>
.login-form {
  width: 600px;
  margin-top: 80px;
}
.login-code {
  float: right;
  cursor: pointer;
}
:deep {
  .el-input__inner {
    border-radius: 0;
    line-height: 60px;
    height: 60px;
    background: transparent;
    border-color: transparent;
    border-bottom-color: #39437d;
    padding-left: 60px;
    color: #fff;
    &:-internal-autofill-previewed,
    &:-internal-autofill-selected {
      -webkit-text-fill-color: #ffffff !important;
      transition: background-color 5000s ease-in-out 0s !important;
    }
  }
  .el-form-item.is-error .el-input__inner,
  .el-form-item.is-error .el-input__inner:focus,
  .el-form-item.is-error .el-textarea__inner,
  .el-form-item.is-error .el-textarea__inner:focus {
    border-color: transparent;
    border-bottom-color: #39437d;
  }
  .el-form-item__error {
    padding-left: 60px;
    padding-top: 4px;
  }
  .el-icon {
    color: #36407a;
    font-size: 20px;
    vertical-align: bottom;
    transform: translateY(5px);
  }
  .el-button--medium {
    padding: 16px 20px;
    width: 160px;
    margin-top: 20px;
    border-radius: 14px;
  }
}
</style>
