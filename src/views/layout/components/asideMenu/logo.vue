<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <!-- <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/overview/main">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1 v-else class="sidebar-title" style="color: #fff">安全运营服务门户</h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/overview/main">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1 class="sidebar-title" style="color: #fff">安全运营服务门户</h1>
      </router-link> -->
      <!-- <img :src="'/outpost-api' + logo" class="sidebar-logo" @click="toHome" style="margin: 10px 0px" /> -->

      <router-link class="sidebar-logo-link" to="/">
        <template v-if="!sidebarOpened">
          <img v-if="bigLogo" :src="'/outpost-api' + bigLogo" class="sidebar-logo" @click="toHome" style="margin: 10px 0px" />
          <!-- <img v-else src="@/assets/logo.png" alt="logo" /> -->
        </template>
        <template v-if="sidebarOpened">
          <img v-if="smallLogo" :src="'/outpost-api' + smallLogo" class="sidebar-logo" @click="toHome" style="margin: 10px 0px" />
          <!-- <img v-else src="@/assets/logore.png" alt="logo" /> -->
        </template>

        <!-- <h1 class="sidebar-title" style="color: #fff">安全运营服务门户</h1> -->
      </router-link>
    </transition>
  </div>
</template>

<script>
import logoImg from "@/assets/logo.png";
import logoReImg from "@/assets/logore.png";

export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      title: "",
    };
  },
  mounted() {},
  computed: {
    sidebarOpened() {
      return this.$store.state.app.sidebar.opened;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme;
    },
    bigLogo() {
      return this.$store.state.logo.big;
    },
    smallLogo() {
      return this.$store.state.logo.small;
    },
    logo() {
      return this.$store.state.app.sidebar.opened ? (this.smallLogo ? this.smallLogo : logoReImg) : this.bigLogo ? this.bigLogo : logoImg;
    },
  },

  methods: {
    toHome() {
      if (!import.meta.env.VITE_IS_SIME) {
        this.$router.push("/");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  // width: 100%;
  // height: 100px;
  line-height: 50px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;
  background-color: $olorSup;
  border: 1px solid rgba(4, 21, 111, 0.2);
  border-radius: $radiusL $radiusL 0px $radiusL;
  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    img {
      max-width: 100%;
      max-height: 60px;
    }
    & .sidebar-logo {
      // width: 32px;
      // height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      // display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
      max-height: 33px;
    }
  }
}
</style>
