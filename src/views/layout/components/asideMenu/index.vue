<template>
  <div class="sildeMenu">
    <logo :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        active-text-color="#ffd04b"
        background-color="#2d4590"
        class="asideMenu"
        :default-active="activeMenu"
        text-color="#fff"
        :unique-opened="true"
        :collapse="isCollapse"
        :collapse-transition="false"
      >
        <template v-for="(parentMune, index) in allRouter" :key="parentMune.path">
          <el-menu-item v-if="hideChidren(parentMune)" :index="index + 1 + ''" @click="toRouter(parentMune.children[0])"
            ><icon :class="isCollapse ? 'menu-in ' : 'menu-icon'" :n="parentMune.meta.icon"></icon
            ><span>{{ parentMune.children[0].meta.title }}</span></el-menu-item
          >
          <el-sub-menu v-else-if="!parentMune.children[0].hidden" :index="index + 1 + ''">
            <template #title>
              <icon :class="isCollapse ? 'menu-in ' : 'menu-icon'" :n="parentMune.meta.icon"></icon>
              <span>{{ parentMune.meta.title }}</span>
            </template>
            <template v-for="(child, _index) in parentMune.children" :key="child.path">
              <el-menu-item v-if="!child.hidden" @click="toRouter(child)" :index="index + 1 + '-' + (_index + 1)">
                {{ child.meta.title }}<span class="itemChose"></span
              ></el-menu-item>
            </template>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-scrollbar>
    <div :class="isCollapse ? 'reLogOut pointer' : 'sliderLogOut pointer'" @click="loginout">
      <el-icon>
        <Back />
      </el-icon>
      <span v-if="!isCollapse" style="margin-left: 20px">退出登录</span>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, nextTick, computed, watch } from "vue";
import logo from "./logo.vue";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

const store = useStore();

let allRouter = computed(() => {
  return store.state.allRouters.filter((item) => !item.hidden).filter((item) => item.children);
});
//路由切换时高亮显示当前菜单
let activeMenu = ref("");
let detailParentMenu = computed(() => {
  return store.state.menu.detailParentMenu;
});
watch(
  () => route.name + allRouter.value.length,
  (val) => {
    nextTick(() => {
      let _index = 0;
      let _childIndex = 0;
      let oneChildren = false;
      allRouter.value.find(
        (item, index) =>
          item.children &&
          item.children.find((child, childIndex) => {
            //二级菜单 || 复用详情页内设置父菜单 || 路由信息内配置的父菜单
            if (child.name == route.name || child.name == detailParentMenu.value[route.name] || child.name == route.meta.parentName) {
              _index = index + 1;
              _childIndex = childIndex + 1;
              if (item.children.length == 1) {
                oneChildren = true;
                if (item.children[0].name == "Workbench") oneChildren = false; //工作台时
              }
              return true;
            }
          })
      );
      if (_index) {
        activeMenu.value = _index + (oneChildren ? "" : "-" + _childIndex);
      }
    });
  },
  { immediate: true }
);

let isCollapse = computed(() => {
  return store.getters.sidebar.opened;
});

let handleOpen = function () {};
let handleClose = function () {};

function toRouter(item) {
  if (item.meta && item.meta.link) {
    if (item.meta.link.includes("screen.html/")) {
      window.open(window.location.origin + "/screen.html/" + item.meta.link.split("screen.html/")[1]);
    } else {
      window.open(item.meta.link);
    }
  } else {
    router.push({
      name: item.name,
    });
  }
}

function loginout() {
  store.commit("loginout");
}

function hideChidren(menu) {
  return menu.children && menu.children.length == 1 && menu.children[0].hidden;
}
</script>
<style lang="scss" scoped>
:deep(.el-menu--collapse) {
  width: 80px;
  i + span {
    display: none;
  }
}
:deep(.el-menu) {
  border-right: none;
  .el-menu-item-group__title {
    padding: 0 !important;
  }
}
:deep(.asideMenu:not(.el-menu--collapse)) {
  width: 196px;

  .el-sub-menu {
    .el-menu {
      border: none;
      max-width: 164px;
      margin: 0px auto;
      background: #3d56a4;
      box-shadow: 0px 1px 2px 0px rgb(0 0 0 / 5%);
      border-radius: $radiusSM;
      overflow: hidden;
      .el-menu-item {
        padding: inherit;
        min-width: auto;
      }
      .itemChose {
        display: none;
      }
      .is-active {
        & > .itemChose {
          display: inline-block;
          width: 5px;
          height: 24px;
          background: #ffffff;
          border-radius: 4px 0px 0px 4px;
          float: right;
          margin-top: 13px;
        }
      }
    }
  }
}
.menu-icon {
  margin-right: 18px;
  font-size: 16px;
}
.menu-in {
  font-size: 18px !important;
  margin-left: 5px !important;
}
:deep() {
  .el-scrollbar {
    height: calc(100% - 170px) !important;
    margin-bottom: 20px;
    .el-scrollbar__wrap {
      overflow: scroll;
      height: 100%;
      .el-scrollbar__view {
        height: 100%;
      }
    }
  }
  .el-scrollbar__wrap--hidden-default {
    .el-scrollbar {
      height: calc(100% - 130px) !important;
    }
  }
  .sliderLogOut {
    width: 164px;
    height: 46px;
    margin: 0px auto;
    text-align: center;
    line-height: 46px;
    font-weight: 400;
    background: #3d56a4;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    border-radius: $radiusSM;
    .el-icon {
      transform: translateY(1px);
    }
  }
  // 折叠后登出
  .reLogOut {
    width: 30px;
    height: 46px;
    text-align: center;
    margin: 0px auto;
  }
}
</style>
