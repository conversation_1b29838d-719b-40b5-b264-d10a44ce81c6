<template>
  <el-breadcrumb separator-class="el-icon-arrow-right">
    <el-breadcrumb-item>{{ needChangePassword ? "密码修改" : firstName }}</el-breadcrumb-item>
    <el-breadcrumb-item v-if="secondName">{{ secondName }}</el-breadcrumb-item>
    <div class="user-avatar">
      Hi,{{ $store.state.userInfo ? $store.state.userInfo.nickName : "" }}
      <img v-if="photoId" class="avatar" :src="'/outpost-api' + photoId" alt="" />
      <img v-else class="avatar" src="@/assets/imgs/default.png" alt="头像" />
    </div>
  </el-breadcrumb>
</template>
<script setup>
import { ref, watch, computed } from "vue";
// 获取路由参数
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();

import { useStore } from "vuex";
const store = useStore();
let needChangePassword = computed(() => {
  return store.state.needChangePassword;
});
let photoId = computed(() => {
  return store.state.userInfo ? store.state.userInfo.avatar : "";
});

function toHome() {
  router.push("/");
}

let firstName = ref("");
let secondName = ref("");
watch(
  () => route.name,
  (val) => {
    changeText();
  },
  { immediate: true }
);
watch(
  () => store.state.allRouters,
  (val) => {
    changeText();
  },
  { deep: true }
);
function changeText() {
  let routers = store.state.allRouters;

  secondName.value = route.meta.title;
  firstName.value = "";
  let routeParents = routers.filter((item) => item.children && item.children.length != 0);

  let currentParent = routeParents.find((item) => item.children.find((item) => item.name == route.name));

  if (!currentParent) {
    //详情页
    currentParent = routeParents.find((item) => item.name == route.meta.breadParent);
  }
  if (!currentParent) return;

  if (currentParent.children.length > 1 || route.meta.breadParent) {
    firstName.value = currentParent.meta.title;
  } else {
    firstName.value = secondName.value;
    secondName.value = "";
  }
}
</script>
<style scoped lang="scss">
.user-avatar {
  margin-left: auto;

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 100%;
    margin-right: $mainContentPadding;
    vertical-align: middle;
    margin-left: 8px;
  }
}
:deep {
  .el-breadcrumb__item:last-of-type {
    .el-breadcrumb__separator {
      display: none;
    }
  }
}
</style>
