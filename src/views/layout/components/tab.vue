<template>
  <div class="tabs-wrapper" ref="tabs_wrapper">
    <router-link
      v-for="(item, index) in tabsList"
      :key="item.fullPath"
      :to="item.fullPath"
      :ref="setItemRef"
      exact
      tag="span"
      :class="{ active: item.name == $route.name }"
      @contextmenu.prevent.prevent="openMenu(tag, $event)"
      style="position: relative"
      >{{ needChangePassword ? "密码修改" : item.meta.title }}
      <span v-show="tabsList.length > 1" class="el-icon-close" @click.prevent.stop="closeTab(item, index)" />
    </router-link>
    <ul v-show="visible" class="contextmenu" :style="{ left: left + 'px', top: top + 'px' }">
      <li @click="refreshSelectedTag(selectedTag)">刷新页面</li>
      <li @click="closeOthers">关闭其他</li>

      <li @click="closeCurrent">关闭当前</li>
    </ul>
  </div>
</template>
<script setup>
import { ref, computed, watch, onBeforeUpdate, onUpdated, onMounted, nextTick } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";

const store = useStore();
const route = useRoute();
const router = useRouter();

let needChangePassword = computed(() => {
  return store.state.needChangePassword;
});

let tabsList = computed(() => {
  return store.state.tabsList;
});
let tabs_wrapper = ref();
let parentWidth = ref(0);
let visible = ref(false);
let left = ref(0);
let top = ref(0);

function openMenu(tag, e) {
  left.value = e.clientX;
  top.value = e.clientY;
  visible.value = true;
}
function refreshSelectedTag() {
  window.location.reload();
}
function closeTab(item, index) {
  store.commit("closeTab", index);
  if (item.fullPath == route.fullPath) {
    if (index > 0) {
      router.push(tabsList.value[index - 1]);
    } else {
      router.push(tabsList.value[index]);
    }
  }
}

function closeCurrent() {
  let index = tabsList.value.findIndex((item) => item.fullPath == route.fullPath);
  if (index > 0) {
    closeTab(route, index);
  }
}

function closeOthers() {
  store.commit("closeOtherTabs", route);
}
let itemRefs = [];
const setItemRef = (el) => {
  if (el) {
    itemRefs.push(el);
  }
};
onBeforeUpdate(() => {
  itemRefs = [];
});
onUpdated(() => {});

watch(
  () => route.fullPath,
  () => {
    nextTick(() => {
      if (!tabs_wrapper.value) return;
      let tabsDom = tabs_wrapper.value.querySelectorAll("a");
      let index = -1;
      for (let i = 0; i < tabsDom.length; i++) {
        if (tabsDom[i].className && tabsDom[i].className.includes("active")) {
          index = i;
          break;
        }
      }

      let lastIndex = index - 1;
      let nextIndex = index + 1;
      [lastIndex, index, nextIndex].forEach((item) => {
        tabsDom[item] && tabsDom[item].scrollIntoView();
      });
    }, {});
  }
);
onMounted(() => {
  document.addEventListener("click", () => {
    visible.value = false;
  });
});
//关闭事件详情页面时，重置tabs
watch(
  () => tabsList.value,
  (val) => {
    if (val) {
      let newEventDetail = val.find((item) => item.name == "EventDetail");
      if (store.state.eventDetail.trendsTabList.length > 0 && !newEventDetail) {
        store.commit("initEventDetail");
      }
    }
  },
  { deep: true }
);
</script>
<style scoped lang="scss">
.tabs-wrapper {
  overflow-x: hidden;
  // width: 200px;
  white-space: nowrap;
  padding: 10px 0px;
  position: relative;
  a {
    border-radius: 16px;
    margin-right: 16px;
    background-color: $bgColor;
    padding: 5.5px 14px 6.5px 14px;
    text-decoration: none;
    color: #28334f;
    display: inline-block;
    &.active,
    &:hover {
      background-color: $color;
      color: $bgColor;
    }
  }
}
.tabs-wrapper a:hover {
  .el-icon-close {
    display: inline-block;
    transform: scale(1.1);
  }
}
.el-icon-close {
  border-radius: 50%;
  background-color: #63729d;
  color: #ffffff;
  font-size: 10px;
  padding: 2px;
  position: absolute;
  transform: scale(0);
  margin-top: -8px;
  cursor: pointer;
  transition: all 0.2s;
}
.el-icon-close:hover {
  opacity: 0.7;
}

.contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: fixed;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    &:hover {
      background: #eee;
    }
  }
}
</style>
