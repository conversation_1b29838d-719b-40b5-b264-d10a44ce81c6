<!-- 系统布局文件 -->
<template>
  <div v-if="!$wujie" class="layout-wrapper">
    <!-- 左侧导航 -->
    <aside-menu class="aside-menu" :class="sidebar.opened ? 'retract-menu' : ''" />
    <article class="content-wrapper" :class="sidebar.opened ? 'retract-content' : ''">
      <!-- 伸缩 -->
      <hambuger
        id="hamburger-container"
        :is-active="sidebar.opened"
        class="hamburger-container"
        @toggleClick="store.dispatch('toggleSideBar')"
      ></hambuger>
      <!-- 面包区 -->
      <breadcrumb class="breadcrumb"></breadcrumb>
      <!-- 页签 -->
      <tab class="tab-wrapper" />
      <!-- 内容 -->
      <section class="main-content">
        <div class="main-content-overflow">
          <slot>
            <router-view v-if="show" v-slot="{ Component }">
              <keep-alive ref="keepAliveRef" :include="keepAliveName" :max="keepAliveMax">
                <component :is="Component" :key="$route.fullPath" />
              </keep-alive>
            </router-view>
          </slot>
        </div>
      </section>
    </article>
  </div>
  <!-- 子应用 -->
  <section v-if="$wujie" class="main-content">
    <div class="main-content-overflow">
      <router-view v-slot="{ Component }">
        <keep-alive ref="keepAliveRef" :include="keepAliveNameWujie">
          <component :is="Component" :key="$route.fullPath" />
        </keep-alive>
      </router-view>
    </div>
  </section>
</template>
<script setup>
// import { mapGetters } from "vuex";
import { useStore } from "vuex";
import firstToUpper from "@/utils/firstToUpper";

import tab from "./components/tab.vue";
import hambuger from "./components/hambuger.vue";
import asideMenu from "./components/asideMenu/index.vue";
import breadcrumb from "./components/breadcrumb.vue";
import { ref, reactive, toRefs, onMounted, computed, watch, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
const store = useStore();
const route = useRoute();

let keepAliveMax = 20;
let keepAliveName = computed(() => {
  return store.state.tabsList.map((item) => firstToUpper(item.meta.keepAliveName) || firstToUpper(item.name));
});

let includeList = [];
let sidebar = computed(() => {
  return store.getters.sidebar;
});
let show = ref(true);

/*创建一个事件对象,用于触发页面初始化事件，用于其他页面判断是否初始化完成。历史代码逻辑中强制在user请求后再刷新一次页面，因此有些页面会加载两次
这个事件用于在其他页面中判断是否在真正的页面加载完成后再进行操作。目前“查询分析”页面的“复制标签”使用了这个事件。
*/
window.$PageInitEvent = new Event("PageInitEvent");
const pageInitTimer = setTimeout(() => {
  window.dispatchEvent(window.$PageInitEvent);
}, 500);

watch(
  () => store.state.userInfo,
  (newVal, oldVal) => {
    show.value = false;
    clearTimeout(pageInitTimer);
    setTimeout(() => {
      show.value = true;
      setTimeout(() => {
        window.dispatchEvent(window.$PageInitEvent);
      }, 200);
    }, 500);
  }
);

watch(
  () => route.path,
  (val) => {
    if (route.meta.keepAlive && includeList.indexOf(route.name) === -1) {
      includeList.push(route.name);
    }
    // includeList.push(route.name);
    //
  }
);

const keepAliveNameWujie = ref([]); // 子应用只缓存不含params参数的路由
watch(
  () => route.name,
  (val) => {
    if (isEmptyObject(route.params) && !keepAliveNameWujie.value.includes(route.name)) {
      keepAliveNameWujie.value.push(route.name);
    }
  },
  {
    immediate: true,
  }
);
function isEmptyObject(obj) {
  return Object.keys(obj).length === 0;
}
</script>
<style scoped lang="scss">
$menuWidth: 196px;
$remenuWidth: 80px;
.layout-wrapper {
  background: $bgColor;
  &::before {
    content: "";
    display: block;
    height: 240px;
    width: 100vw;
    background: $bgColorSup;
    position: fixed;
    z-index: 0;
  }
}
.aside-menu {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  width: $menuWidth;
  background: $olorSup;
  border-radius: $radiusL;
  color: #fff;
}

.retract-menu {
  width: $remenuWidth;
}
.retract-content {
  margin-left: $remenuWidth !important;
}
.content-wrapper {
  margin-left: $menuWidth;
  padding: 0 $mainContentPadding 0;
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.main-content {
  overflow: hidden;
  // margin-top: 20px;
  // background: #fff;
  border-radius: $radiusL;
  height: calc(100vh - 120px);
  .main-content-overflow {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 0;
    }

    & > .el-card.is-always-shadow:first-child {
      box-shadow: none;
      border: none;
    }
  }
}
.breadcrumb {
  // margin-bottom: 20px;
  border: 1px solid #d0d0d0;
  border-bottom-left-radius: $radiusL;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
  border-top: none;
  border-left: none;
  height: 48px;
  width: calc(100% + 64px);
  display: flex;
  align-items: center;
  padding-left: 42px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.7s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.hamburger-container {
  position: absolute;
  line-height: 46px;
  // height: 100%;
  height: 46px;
  float: left;
  cursor: pointer;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    background: rgba(0, 0, 0, 0.025);
  }
}
</style>
