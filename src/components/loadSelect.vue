<!--select 滚动加载更多 -->
<template>
  <el-select
    :modelValue="modelValue"
    @update:modelValue="$emit('update:modelValue', $event)"
    ref="selectRef"
    placeholder="请选择基础资源软件"
    filterable
    remote
    :popper-class="'load-select' + popClass"
    :remote-method="remoteMethod"
    @click="clickFn"
    @change="changeSelected"
    style="width: 100%"
    :title="text"
  >
    <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
      <span style="float: left; max-width: 300px" class="ellipsis">{{ item.name }}</span>
      <span style="float: right">{{ item.parentName }}</span>
    </el-option>
  </el-select>
</template>
<script setup>
import { ref, reactive, computed, nextTick } from "vue";
let props = defineProps({
  modelValue: [Number, String],
  type: [Number, String],
  loadData: {
    type: Function,
    required: true,
  },
  params: {
    type: Object,
    default: () => {
      return {};
    },
  },
  popClass: {
    type: String,
    default: "",
  },
  //*选中的数组列表（回显数据使用，选中值对应的选项需要异步获取）
  selectedList: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let text = ref("");
const emit = defineEmits(["update:modelValue", "change", "update:type"]);

let selectRef = ref();
let options = ref([]);
let loading = ref(false);

let pageNum = 1;

//回显编辑数据
echoOptions();
function echoOptions() {
  if (props.selectedList.length > 0) {
    options.value = [...props.selectedList];
  }
}

//监听选项最后一个元素
let iobser = new IntersectionObserver((item) => {
  if (item[0] && item[0].isIntersecting) {
    loadOptions();
  }
});
function observe() {
  let dom = document.querySelector(`.load-select${props.popClass} .el-scrollbar__wrap .el-select-dropdown__item:last-of-type`);
  if (dom) {
    iobser.observe(dom);
  } else {
    name = "";
  }
}
function addListen() {
  nextTick(() => {
    setTimeout(() => {
      observe();
    }, 1500);
  });
}

let name = "";
let lastName = "";

function clickFn() {
  addListen();
  if (pageNum > 1) pageNum--; //点击
  remoteMethod();
}

const remoteMethod = (query) => {
  if (typeof query == "string") {
    if (name != query) {
      document.querySelector(`.load-select${props.popClass} .el-scrollbar__wrap`).scrollTop = 0;

      pageNum = 1;
    }
    name = query;
  }

  loadOptions();
};

function loadOptions() {
  if (loading.value) return;
  let lastLi = document.querySelector(`.load-select${props.popClass} .el-scrollbar__wrap .el-select-dropdown__item:last-of-type`);

  lastLi && iobser.unobserve(lastLi);

  loading.value = true;
  props
    .loadData({ ...props.params, pageNum: pageNum, name })
    .then(({ data }) => {
      if (options.value.length > 0 && data.length > 0 && data[data.length - 1].id == options.value[options.value.length - 1].id) {
        return;
      }
      pageNum++;
      let filterData = data.filter((item) => !props.selectedList.find((s) => s.id == item.id));
      if (name != lastName) {
        options.value = filterData;
      } else {
        options.value.push(...filterData);
      }
      lastName = name;
      nextTick(() => {
        if (lastLi) {
          observe();
        }
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

function changeSelected(val) {
  let selected = options.value.find((item) => item.id == val) || {};

  let type = selected.parentId;

  text.value = selected.name;
  emit("update:type", type);
  emit("change", val);
}
</script>

<style lang="scss" scoped></style>
