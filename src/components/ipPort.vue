<!-- ip 端口合并展示 -->
<template>
  <ul class="ip-port-list" :class="{ detail: isDetail }" :style="{ width: width }">
    <li v-for="(item, index) in listSlef" :key="index">
      <span class="ip-label color-soft">IP</span>
      <span class="ip-span"> {{ item.ip }}</span>
      <ul>
        <li v-for="(detail, _index) in item.details" :key="_index">
          <span v-if="showPort" class="ip-port">{{ detail.port }}</span>
          <span v-if="showType && detail.type" class="ip-type">{{ detail.type }}</span>
        </li>
      </ul>
    </li>
  </ul>
</template>
<script setup>
import { computed } from "vue";
let props = defineProps({
  //[{ip:************,port:'8080',type:"ftp"}]
  list: {
    type: Array,
    default() {
      return [];
    },
  },
  showType: {
    type: <PERSON>olean,
    default: false,
  },
  showPort: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: "160px",
  },
  //是否详情页展示，端口号和协议按钮展示
  isDetail: {
    type: Boolean,
    default: false,
  },
});

let listSlef = computed(() => {
  let arr = [];
  for (let item of props.list) {
    let exitItem = arr.find((i) => i.ip == item.ip);
    if (exitItem) {
      exitItem.details.push({
        port: item.port,
        type: item.type,
      });
    } else {
      arr.push({
        ip: item.ip,
        details: [
          {
            port: item.port,
            type: item.type,
          },
        ],
      });
    }
  }
  return arr;
});
</script>

<style lang="scss" scoped>
.ip-port-list {
  & > li {
    padding: 10px;
    line-height: 20px;
    margin: 10px 0;
    border: 1px solid #eee;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    // width: 200px;
  }
  ul {
    margin-left: 30px;
  }
  .ip-type {
    margin-left: 10px;
  }
}

.ip-port-list.detail {
  & > li {
    border: none;
    border-bottom: 1px solid #ebedf1;
    justify-content: flex-start;
    padding-bottom: 10px;
    &:first-child {
      margin-top: 0;
    }
    .ip-label {
      display: inline-block;
      width: 120px;
    }
    .ip-span {
      display: inline-block;
      width: 120px;
    }
    ul {
      margin-left: 60px;
      li {
        margin-bottom: 8px;
        &:last-child {
          margin-bottom: 0;
        }
        .ip-port,
        .ip-type {
          display: inline-block;
          border-radius: 4px;
          padding: 3px 8px;
        }
        .ip-port {
          background: #dae6ff;
        }
        .ip-type {
          background-color: #e4f9f0;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.el-dialog {
  .ip-port-list.detail > li ul {
    margin-left: 20px;
    .ip-type {
      max-width: 130px;
      vertical-align: top;
    }
  }
  .ip-port-list.detail > li .ip-span {
    width: 100px;
  }
}
</style>
