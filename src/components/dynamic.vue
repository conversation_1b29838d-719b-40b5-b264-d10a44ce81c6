<template>
  <p v-if="dynamicName" class="title-bottom-line">{{ dynamicName }}</p>
  <section>
    <el-form
      ref="dynamicValidateFormRef"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :class="formList.length == 3 ? 'dynamic-item-wrapper' : ''"
    >
      <!-- 每一行 -->
      <component :is="draggable ? VueDraggableNext : 'div'" v-bind="rowIndex" :list="rows">
        <div v-for="(item, rowIndex) in rows" :key="item">
          <!-- 每一行中的表单项列表 1或者3 -->
          <el-form-item class="xelItem" v-if="showIndex" label="序号:">{{ rowIndex + 1 }}</el-form-item>
          <xel-form-item v-for="(domain, index) in item" v-model="domain.value" :key="index" v-bind="domain" class="xelItem"> </xel-form-item>
          <div :class="{ 'empty-line': formList.length == 3 && $globalWindowSize == 'S' }" class="inline-line">
            <el-button v-if="!item[0].disabled" @click.prevent="removeDomain(rowIndex)" icon="el-icon-delete" class="delBtn pull-right"></el-button>
          </div>
        </div>
      </component>
    </el-form>
    <div class="dynamic-add pointer" @click="addDomain" :style="{ 'padding-left': labelWidth }">
      <el-icon :size="18"><plus /></el-icon>
      添加{{ dynamicName }}
    </div>
  </section>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { VueDraggableNext } from "vue-draggable-next";
import { ref, reactive, toRefs, nextTick, onMounted, watch } from "vue";
let props = defineProps({
  dynamicName: {
    type: String,
    default: "",
  },
  formList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  labelWidth: {
    type: String,
    default: "100px",
  },
  dataRows: {
    type: Array,
    default: () => {
      return [];
    },
  },
  showIndex: {
    type: Boolean,
    default: false,
  },
  labelPosition: {
    type: String,
    default: "left",
  },
  draggable: { type: Boolean, default: false }, //日志设置为true
});
let dynamicValidateFormRef = ref();
let listStr = JSON.stringify(props.formList);
let rows = reactive([JSON.parse(listStr)]);

//回显已有的数据

function echoIpRows() {
  if (props.dataRows.length > 0) {
    rows.splice(0, 1);
    rows.push(...props.dataRows);
  }
}
onMounted(() => {
  echoIpRows();
});
watch(
  () => props.dataRows.length,
  () => {
    echoIpRows();
  }
);

function addDomain() {
  listStr = JSON.stringify(props.formList);
  let newRow = JSON.parse(listStr);
  for (let it of newRow) {
    it.value = "";
    delete it.disabled;
    delete it.id;
  }
  rows.push(newRow);
}
function removeDomain(index) {
  rows.splice(index, 1);
}

defineExpose({
  list: rows,
});
</script>
<style lang="scss" scoped>
:deep {
  .xelItem {
    display: inline-flex;
    margin-right: 1.5%;
    &:last-of-type {
      margin-right: 0;
    }
  }
  .delBtn {
    margin-left: 10px;
  }
}
.dynamic-add {
  width: calc(100% - 70px);
}
.inline-line {
  vertical-align: middle;
  overflow: hidden;
}
</style>
