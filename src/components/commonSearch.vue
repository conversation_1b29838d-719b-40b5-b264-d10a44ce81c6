<template>
  <div class="bg-p-border-new">
    <div ref="btnsRef" :class="{ 'table-handler-btns': true, 'one-row': searchWidth }">
      <el-button class="filter-search-btn" v-if="!searchWidth" size="small" @click="toggleSearch">
        <el-icon :size="12"> <search /> </el-icon>
        {{ $wujie ? "过滤查询" : "搜索查询" }}
      </el-button>
      <slot></slot>
    </div>
    <el-form
      ref="formRef"
      v-show="searchShow"
      :model="selfSearchData"
      :label-width="labelWidth"
      :label-position="$wujie ? 'top' : 'left'"
      size="small"
      class="search-form-wrapper"
      :class="{ 'one-row-form': searchWidth }"
      :style="{ width: searchWidthSelf }"
      @submit.prevent
    >
      <!-- 选项按钮展示 -->
      <xel-query-menu ref="xelQueryMenuRef" :menuData="menuData" :searchShow="searchShow" :data="modelValue" @selectValue="selectValue" />
      <div class="search-form-item-wrapper" :class="{ 'two-item': two }">
        <!-- 正常表单项 -->
        <xel-form-item
          v-for="(item, index) in formList"
          :key="index"
          v-model="selfSearchData[item.prop]"
          v-bind="item"
          :style="{ width: item.width }"
          :label-width="item.labelWidth"
        ></xel-form-item>
        <!-- 自定义表单项 -->
        <slot name="form"></slot>
        <el-form-item ref="searchRef" label-width="0" style="width: 130px" class="common-search-btns-item">
          <el-button type="primary" @click="searchFn">{{ $wujie ? "查询" : "搜索" }}</el-button>
          <el-button @click="resetFn">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>
<script setup>
import { ref, reactive, watch, onMounted, nextTick, onActivated } from "vue";

let props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    },
  },
  menuData: {
    type: [Array],
    default: () => {
      return [];
    },
  },
  formList: {
    type: [Array],
    default: () => {
      return [];
    },
  },
  labelWidth: {
    type: String,
    default: "100px",
  },
  //初始状态不显示
  hideOptions: {
    type: Boolean,
    default: false,
  },
  //是否一行显示两个表单项
  two: {
    type: Boolean,
    default: false,
  },
});

let emit = defineEmits(["search", "reset", "update:modelValue"]);

let selfSearchData = reactive(props.modelValue);
watch(
  () => props.modelValue,
  (val) => {
    Object.keys(selfSearchData).forEach((key) => {
      if (key in val) {
        selfSearchData[key] = val[key];
      } else {
        delete selfSearchData[key];
      }
    });

    changeStatus = false;
  }
);

let changeStatus = false;
watch(
  () => selfSearchData,
  (val) => {
    emit("update:modelValue", selfSearchData);
  },
  { deep: true }
);
let searchShow = ref(true);
if (props.hideOptions) {
  searchShow.value = false;
}
function toggleSearch() {
  searchShow.value = !searchShow.value;
}

function selectValue(data) {
  if (data.id === -1) {
    selfSearchData[data.prop] = "";
  } else {
    selfSearchData[data.prop] = data.id;
  }
  setTimeout(() => {
    searchFn();
  }, 20);
}
function searchFn() {
  emit("search");
}
let xelQueryMenuRef = ref();
function resetFn() {
  xelQueryMenuRef.value.closeAll();

  emit("reset");
}

//搜索表单的宽度
let searchWidthSelf = ref("");
let formRef = ref();
let btnsRef = ref();
let searchWidth = ref(false);
let searchRef = ref();
onMounted(() => {
  let formConItems = formRef.value.$el.querySelectorAll(".formCon");
  let formItems = formRef.value.$el.querySelectorAll(".el-form-item");

  if (formConItems.length > 0) {
    setSearchStyle();
    return;
  } else if (formItems.length == 4) {
    setSearchStyle();
    return;
  }
  setOneRowStyle();
});

function setSearchStyle() {
  if (searchRef.value.$el.offsetLeft < 20) {
    searchRef.value.$el.style.marginLeft = "auto";
  }
}

function setOneRowStyle() {
  let formItems = formRef.value.$el.querySelectorAll(".el-form-item");
  if (props.two) return;
  //一个表单项
  if (formItems.length == 2) {
    searchWidth.value = true;
    formItems[0].style.width = "500px";
    formItems[0].style.maxWidth = "calc(100% - 200px)";
  } else if (formItems.length == 3) {
    searchWidth.value = true;

    formItems[0].style.width = formItems[1].style.width = "400px";
    formItems[0].style.maxWidth = formItems[1].style.maxWidth = "calc((100% - 300px)/2)";
  } else {
    searchWidth.value = false;
    return;
  }
  nextTick(() => {
    let btnWidth = btnsRef.value.clientWidth;
    searchWidthSelf.value = `calc(100% - ${btnWidth}px)`;
  });
}

//监听input的回车事件，手动触发查询
function inputEnter() {
  let inputs = formRef.value.$el.querySelectorAll(".el-input__inner");
  inputs.forEach((input) => {
    input.onkeydown = (event) => {
      if (event.keyCode == 13) {
        searchFn();
      }
    };
  });
}
onMounted(() => {
  inputEnter();
});
</script>

<style lang="scss" scoped>
/*表格上方的操作按钮*/

.search-form-item-wrapper {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  .el-form-item,
  ::v-slotted(.el-form-item) {
    width: 30%;
    margin-right: 3%;
    @for $n from 1 through 20 {
      &:nth-of-type(#{$n}) {
        order: $n + 1;
      }
    }
  }
}
.table-handler-btns.one-row {
  float: right;
}
.search-form-wrapper:not(.one-row-form) {
  border-top: 1px dashed #ededed;
  border-bottom: 1px dashed #ededed;
  padding-top: 10px;
  margin-bottom: 20px;
}
</style>
<style lang="scss">
.el-dialog .search-form-item-wrapper,
.two-item.search-form-item-wrapper {
  .el-form-item {
    width: 45%;
    margin-right: 5%;
  }
}
</style>
