<template>
  <div ref="indexChart" style="width: 100%; height: 450px"></div>
</template>
<script setup>
import * as echarts from "echarts";
import { ref, reactive, toRefs, nextTick, onMounted } from "vue";
let props = defineProps({
  inData: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let indexChart = ref();
onMounted(() => {
  let myChart = echarts.init(indexChart.value);
  let option = {
    tooltip: {
      trigger: "item",
      position: ["40%", "60%"],
    },
    legend: {
      top: "5%",
      left: "center",
    },
    series: [
      {
        type: "pie",
        radius: ["40%", "70%"],
        center: ["50%", "60%"],

        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: false,
            fontSize: "20",
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: props.inData,
      },
    ],
  };
  let noData = {
    title: {
      text: "暂无数据",
      x: "center",
      y: "center",
      textStyle: {
        color: "#848484",
        fontWeight: "normal",
        fontSize: 16,
      },
    },
  };
  if (props.inData.length > 0) {
    myChart.setOption(option);
  } else {
    myChart.setOption(noData);
  }
});
</script>

<style scoped lang="scss"></style>
