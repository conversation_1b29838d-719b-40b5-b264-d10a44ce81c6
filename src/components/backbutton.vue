<template>
  <el-button round @click="newlyAdded">
    <icon n="icon-fanhui"></icon>
    返回{{ text }}</el-button
  >
</template>
<script setup>
import { useRouter, useRoute } from "vue-router";
import firstToUpper from "@/utils/firstToUpper";
import { useStore } from "vuex";
const store = useStore();

let router = useRouter();
let props = defineProps({
  text: {
    type: String,
    default: "",
  },
  name: {
    type: [String, Object],
    default: "",
  },
});
function newlyAdded() {
  //关闭页签
  store.commit("closeCurrentTab");

  if (typeof props.name == "string") {
    router.push({ name: firstToUpper(props.name) });
  }
  // 用于多个tab 跳转到指定tab
  if (typeof props.name == "object") {
    if (props.name.tabName) {
      router.push({
        name: firstToUpper(props.name.name),
        query: {
          tabName: props.name.tabName,
        },
      }); //路由跳转
    } else {
      router.push(props.name);
    }
  }
}
</script>
