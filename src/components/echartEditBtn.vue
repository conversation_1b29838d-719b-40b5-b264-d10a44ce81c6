<template>
  <el-button
    v-hasPermi="'overview:edit'"
    @click.stop="
      $store.commit('openEditEchart', {
        echartId: echartId,
        dataSet: dataset,
        color: color,
      })
    "
    type="text"
    >编辑图表</el-button
  >
</template>

<script setup>
import { ref, reactive, toRefs, computed, onMounted } from "vue";
let props = defineProps({
  echartId: {
    type: String,
    default: "",
  },
  dataset: {
    type: Array || Object,
  },
  color: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
</script>

<style></style>
