<template>
  <section v-for="(item, rowIndex) in rows" :key="rowIndex" class="margin-bottom10 clearfix">
    <div class="pull-left el-form-item__label" style="width: 6em; text-align: left; margin-top: -5px">软件:</div>

    <div style="width: calc(100% - 110px - 6em); overflow: hidden" class="pull-left">
      <load-select
        :load-data="selectSoftListByPage"
        :pop-class="rowIndex"
        v-model="item.softwareValue"
        v-model:type="item.softwareType"
        placeholder="请输入基础资源软件"
        :selected-list="selectedList"
        @change="changeValue(item)"
        style="float: left; width: 70%"
      ></load-select>
      <el-input placeholder="请输入版本号" v-model="item.softwareEdition" style="float: right; width: 23%" @change="changeValue(item)"></el-input>
    </div>
    <div class="pull-right">
      <el-button
        v-if="isEdit && (item.change || !item.id)"
        @click.prevent="saveDomain(item, rowIndex)"
        icon="el-icon-circle-check"
        class="delBtn"
        type="success"
        plain
      ></el-button>
      <el-button
        v-if="rows.length > 1 && (!isEdit || item.id)"
        @click.prevent="removeDomain(item, rowIndex)"
        icon="el-icon-delete"
        class="delBtn"
      ></el-button>
    </div>
  </section>
  <div class="dynamic-add pointer margin-bottom10" @click="addDomain" style="width: calc(100% - 70px - 6em); margin-left: 6em">
    <el-icon :size="18"><plus /></el-icon>
    添加基础资源软件
  </div>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick } from "vue";
import { selectSoftListByPage } from "@/api/securityAssets/assetsList";
import { batchDelete } from "@/utils/delete";

import { useRouter, useRoute } from "vue-router";
const route = useRoute();
let pageType = Number(route.query.type || route.params.type || 0); //当前页面的资产类型
let assetsId = route.params.id;

let props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  dataList: {
    type: Array,
    default() {
      return [];
    },
  },
  editApi: {
    type: Function,
  },
  delApi: {
    type: Function,
  },
  dataRows: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
let formData = {
  softwareValue: "",
  softwareEdition: "",
  softwareType: "",
};
let listStr = JSON.stringify(formData);
let rows = reactive([]);

let selectedList = ref([]); //选中值

if (!props.isEdit || (props.isEdit && props.dataList.length == 0)) {
  rows.push(JSON.parse(listStr));
} else {
  let dataList = props.dataList.map((item) => {
    return {
      ...item,
      softwareValue: item.softValueId,
      softwareType: item.softTypeId,
    };
  });

  rows.push(...dataList);

  selectedList.value = props.dataList.map((item) => {
    return {
      id: item.softValueId,
      name: item.softwareValue,
      parentId: item.softTypeId,
      parentName: item.softwareType,
    };
  });
}

//添加
function addDomain() {
  rows.push(JSON.parse(listStr));
}

//编辑 保存
function saveDomain(item, index) {
  if (item.softwareValue == "") {
    ElMessage.warning("软件不能为空");
    return;
  } else if (item.softwareEdition == "") {
    ElMessage.warning("版本号不能为空");
    return;
  }
  let saveData = {
    ...item,
    assetsId: assetsId,
  };
  props.editApi(saveData).then(({ data }) => {
    ElMessage.success("保存成功");
    if (data) {
      rows[index].id = data.id;
    }
    delete item.change;
  });
}

//删除
function removeDomain(item, index) {
  if (!props.isEdit) {
    rows.splice(index, 1);
  } else {
    batchDelete().then(() => {
      props.delApi({ id: item.id, assetsId: assetsId }).then(() => {
        ElMessage.success("删除成功");
        rows.splice(index, 1);
      });
    });
  }
}

function changeValue(item) {
  item.change = true;
}

defineExpose({
  list: rows,
});
</script>
<style lang="scss" scoped>
:deep {
  .xelItem {
    display: inline-flex;
  }
  .delBtn {
    margin-left: 10px;
  }
}
</style>
