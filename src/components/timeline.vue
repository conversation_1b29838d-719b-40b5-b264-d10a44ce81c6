<template>
  <el-card class="timeline-wrapper">
    <div class="block">
      <div class="iconClose">
        <el-icon class="pointer" @click="closeTimeline"><close /></el-icon>
      </div>
      <el-timeline>
        <el-timeline-item v-for="(activity, index) in activities" :key="index" :timestamp="activity.timestamp">
          <p :title="activity.remark">{{ activity.remark }}</p>
          <p style="color: rgba(24, 144, 255, 1)">{{ activity.operationerName }}</p>
          <p style="color: rgba(132, 132, 132, 1)">{{ activity.operationTime }}</p>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-card>
</template>
<script setup>
import { ref, reactive, toRefs, nextTick } from "vue";
import { showTimeline, closeTimeline } from "@/utils/timelineDisplay.js";
let props = defineProps({
  activities: {
    type: Array,
    default: () => {
      return [];
    },
  },
});
</script>
<style scoped lang="scss">
.timeline-wrapper {
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  position: sticky;
  top: 0;
}
:deep(.el-card__body) {
  padding-top: 0px;
}
p {
  line-height: 26px;
  &:first-child {
    line-height: 140%;

    /* 文字最多五行 */
    overflow: hidden;
    display: -webkit-box;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    word-wrap: break-word;
  }
}
.block {
}
.iconClose {
  text-align: right;
  margin-bottom: 5px;
  padding-top: 10px;
  background: #fff;
  color: $fontColorSoft;
  position: sticky;
  top: 0;
  z-index: 1;
  i {
    position: relative;
    right: -20px;
    font-size: 20px;
  }
}
</style>
