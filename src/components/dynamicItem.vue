<!-- 编辑单条表单项列表 -->
<template>
  <el-form
    ref="dynamicValidateFormRef"
    :label-width="labelWidth"
    label-position="right"
    :class="formList[0].length == 3 ? 'dynamic-item-wrapper' : ''"
  >
    <!-- 每一行 -->
    <div v-for="(item, rowIndex) in rows" :key="item">
      <!-- 每一行中的表单项列表 1或者3 -->
      <xel-form-item
        v-for="(domain, index) in item"
        v-model="domain.value"
        :key="index"
        v-bind="domain"
        class="xelItem"
        @focus="changeValue(item)"
        @change="changeValue(item)"
      >
      </xel-form-item>
      <div :class="{ 'empty-line': $globalWindowSize == 'S' }" class="inline-line">
        <!-- 编辑 -->
        <el-button
          v-show="!item[0].id || item[0].change"
          @click.prevent="save(item, rowIndex)"
          type="success"
          plain
          icon="el-icon-circle-check"
          class="delBtn"
        ></el-button>

        <!-- 删除 -->
        <el-button
          v-show="item[0].id && (allowEmpty || rows.length > 1)"
          @click.prevent="removeDomain(item, rowIndex)"
          icon="el-icon-delete"
          class="delBtn"
        ></el-button>
        <!-- 转移 -->
        <el-button
          title="转移"
          v-show="props.canTransfer && item[0].id"
          @click.prevent="switchFn(item, rowIndex)"
          type="warning"
          plain
          icon="el-icon-sort"
          class="delBtn switch-btn"
        ></el-button>
      </div>
    </div>
    <div>
      <el-form-item :style="{ width: addWidth }">
        <div class="dynamic-add pointer" @click="addDomain">
          <el-icon :size="18"><plus /></el-icon>
          添加{{ dynamicName }}
        </div>
      </el-form-item>
    </div>
  </el-form>
</template>
<script setup>
import { ElMessageBox, ElMessage } from "element-plus";
import { ref, reactive, toRefs, nextTick, watch } from "vue";
import { rules } from "@/xelComponents/utils/formValidator";
import { batchDelete } from "@/utils/delete";
import { getDicts } from "@/api/system/dict/data";
import { validaDomain } from "@/utils/ruoyi";
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
let pageType = Number(route.query.type || route.params.type || 0); //当前页面的资产类型
let assetsId = route.params.id;

let props = defineProps({
  dynamicName: {
    type: String,
    default: "详情",
  },
  //已存在的数据列表或空的模板表单
  formList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  labelWidth: {
    type: String,
    default: "100px",
  },
  editApi: {
    type: Function,
  },
  delApi: {
    type: Function,
  },
  addApi: {
    type: Function,
  },
  canTransfer: {
    type: Boolean,
    default: false,
  },
  networkType: {
    type: [String, Number],
    default: 0,
  },
  type: {
    type: String,
    default: "ip",
  },
  editKey: {
    type: String,
    default: "resourceServers",
  },
  addWidth: {
    type: String,
    default: "80%",
  },
  //不必填，可为空
  allowEmpty: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "",
  },
});

let emits = defineEmits(["transfer", "update"]);

let rows = ref(JSON.parse(JSON.stringify(props.formList)));
watch(
  () => props.formList,
  () => {
    rows.value = JSON.parse(JSON.stringify(props.formList));
  }
);

let dynamicValidateFormRef = ref();

/* 新增 - 获取验证字典 */
const domainDic = ref([]);
const getDicFun = () => {
  getDicts("asset_business_domain_agreement").then((res) => {
    domainDic.value = res.data.map((item) => {
      return item.dictLabel;
    });
  });
};
getDicFun();

//保存
function save(item, index) {
  for (let f of item) {
    if (!f.value && f.value !== 0 && f.value !== "0") {
      if (f.label === "服务协议" && f.prop === "serverAgreement") {
      } else {
        ElMessage.warning(`${f.label || "系统入口"}不能为空`);
        return false;
      }
    }
    //校验ip格式
    if (f.prop == "ipStr" || f.prop == "ip") {
      let { result, errMsg } = rules.IP(f.value);
      if (!result) {
        ElMessage.warning(`${props.title}${f.label || "系统入口"}格式不正确`);
        return false;
      }
    }
    //校验ip格式
    if (f.prop == "domain") {
      let result = validaDomain(f.value, domainDic.value);
      if (!result) {
        return false;
      }
      /*let { result, errMsg } = rules.URL(f.value);
      if (!result) {
        ElMessage.warning(`${props.title}${f.label || "系统入口"}格式不正确`);
        return false;
      }*/
    }
  }

  let saveDataObj = {};

  if (props.type == "ip") {
    saveDataObj = {
      assetsId: assetsId,
      id: item[0].id,
      ip: item[0].value,
      ipStr: item[0].value,
      port: item[1].value,
      serverAgreement: item[2].value,
      networkType: props.networkType,
    };
  } else if (props.type == "domain") {
    saveDataObj = { assetsBusinessId: assetsId, domain: item[0].value, id: item[0].id };
  } else if (props.type == "terIp") {
    saveDataObj = { assetsId: assetsId, ip: item[0].value, id: item[0].id };
  }
  let saveData = {};
  if (saveDataObj.id) {
    if (props.type == "ip") {
      saveData[props.editKey] = [{ ...saveDataObj }];
    } else if (props.type == "domain") {
      saveData.domainList = [saveDataObj];
    } else if (props.type == "terIp") {
      saveData = { ...saveDataObj, ipStr: saveDataObj.ip };
    }
  } else {
    saveData = saveDataObj;
    if (props.type == "terIp") {
      saveData = { ...saveDataObj, ipStr: saveDataObj.ip };
    }
  }

  let saveFn = saveDataObj.id ? props.editApi : props.addApi;

  saveFn(saveData).then(({ data }) => {
    ElMessage.success("保存成功");

    emits("update");
    //新增 设置id
    if (data && (data.id || data.domainId || data.ipId)) {
      for (let it of item) {
        it.id = data.id || data.domainId || data.ipId;
      }
    }

    delete item[0].change;
  });
}

//添加
function addDomain() {
  let item = JSON.parse(JSON.stringify(props.formList[0]));

  for (let it of item) {
    it.value = "";
    delete it.disabled;
    delete it.id;
  }
  rows.value.push(item);
}

//转移
function switchFn(item, index) {
  let onlyOne = false;
  let text = item[0].prop == "domain" ? "系统入口" : "IP";

  let savedRows = rows.value.filter((row) => row[0].id);

  if (props.type == "domain" || props.type == "terIp") {
    if (savedRows.length == 1) {
      onlyOne = true;
    }
  } else if (props.type == "ip") {
    let ipList = savedRows.map((item) => item[0].value);

    let norepeatIps = [...new Set(ipList)];
    if (norepeatIps.length == 1) {
      onlyOne = true;
    }
  }

  if (onlyOne) {
    ElMessageBox.confirm(`此资产只有一个${text}，如果将${text}转移，资产将被删除！`, "警告", {
      distinguishCancelAndClose: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      //转移接口
      submitSwitch(item, index, true);
    });
  } else {
    submitSwitch(item, index);
  }
}

function submitSwitch(item, index, retrunList = false) {
  emits("transfer", item, index, retrunList);
}

//删除
function removeDomain(item, index) {
  batchDelete().then(() => {
    props.delApi({ id: item[0].id, assetsBusinessId: assetsId, assetsId: assetsId, networkType: props.networkType }).then(() => {
      ElMessage.success("删除成功");
      rows.value.splice(index, 1);
      emits("update");
    });
  });
}

//静态删除
function del(index) {
  rows.value.splice(index, 1);
}

//删除ip相同的项
function delByIp(index) {
  let ip = rows.value[index][0].value;
  for (let i = 0; i < rows.value.length; i++) {
    if (rows.value[i][0].value == ip) {
      rows.value.splice(i, 1);
      i--;
    }
  }
}

//修改值
function changeValue(item) {
  item[0].change = true;
}

defineExpose({
  list: rows,
  del,
  delByIp,
});
</script>
<style lang="scss" scoped>
:deep {
  .xelItem {
    display: inline-flex;
  }
  .delBtn {
    margin-left: 10px;
  }
  .switch-btn .el-icon-sort {
    transform: rotate(90deg);
  }
}
</style>
