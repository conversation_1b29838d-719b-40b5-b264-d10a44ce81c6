<template>
  <el-row class="sime-layout-wrapper mr-30-new">
    <el-col :span="5" class="mr-15-new tree-search-new">
      <el-tree
        ref="treeRef"
        node-key="id"
        :expand-on-click-node="false"
        :default-expanded-keys="expandKeys"
        v-loading="loading"
        :data="state.list"
        :props="defaultProps"
        @node-click="nodeClick"
        @node-expand="expandClick"
      >
        <template #default="scope">
          <span :title="scope.node.label" class="custom-tree-node">
            <el-icon class="folder" v-if="scope.data.isDataNode === false" :size="14"><folder-opened /></el-icon>
            <span class="node-txt ellipsis" :title="scope.node.label"> {{ scope.node.label }}</span>
            <!-- <span class="el-tree-node__label" v-if="scope.data.isDataNode === false">
              <el-icon :size="14" @click="modNode(scope.node.data, 'add')" v-if="addGroupBtn" title="添加">
                <plus />
              </el-icon>
              <el-icon :size="14" @click="nodeClick(scope.node.data)" v-if="scope.node.data.id == '0'" title="刷新">
                <Refresh />
              </el-icon>
              <el-icon :size="14" @click="modNode(scope.node.data, 'edit')" v-if="editGroupBtn && scope.node.data.id != '0'" title="编辑">
                <Edit />
              </el-icon>
              <el-icon :size="14" @click="delNode(scope.node.data)" v-if="deleteGroupBtn && scope.node.data.id != '0'" title="删除">
                <Delete />
              </el-icon>
            </span> -->
          </span>
          <span class="el-tree-node__label" v-if="scope.data.isDataNode === false">
            <el-icon :size="14" @click="modNode(scope.node.data, 'add')" v-if="addGroupBtn" title="添加">
              <plus />
            </el-icon>
            <el-icon :size="14" @click="nodeClick(scope.node.data)" v-if="scope.node.data.id == '0'" title="刷新">
              <Refresh />
            </el-icon>
            <el-icon :size="14" @click="modNode(scope.node.data, 'edit')" v-if="editGroupBtn && scope.node.data.id != '0'" title="编辑">
              <Edit />
            </el-icon>
            <el-icon :size="14" @click="delNode(scope.node.data)" v-if="deleteGroupBtn && scope.node.data.id != '0'" title="删除">
              <Delete />
            </el-icon>
          </span>
        </template>
      </el-tree>
    </el-col>
    <el-col :span="1" class="tree-wrapper"> </el-col>

    <el-col :span="18" class="ml-15-new">
      <slot
        ><table-list :moduleTyp="moduleTyp" @changeShow="changeShow" ref="tableListRef" :table_data="table_data" :groupId="state.groupId"></table-list
      ></slot>
    </el-col>
    <xelDialog v-if="showForm" :title="treeTitle" ref="dialogRef" size="small" @submit="saveNodeItem" @close="(showForm = false), colseItem">
      <el-form :model="node_form_data" ref="ruleFormRef" label-width="120px" size="small" action="javascript:;">
        <xel-form-item v-for="(item, index) in node_form_list" :key="index" v-model="node_form_data[item.prop]" v-bind="item"></xel-form-item>
      </el-form>
    </xelDialog>
  </el-row>
</template>
<script setup>
import tableList from "@/views/sime/config/logStore/components/dataSourceList.vue";
import { ref, reactive, toRefs, onMounted, nextTick, computed, watch } from "vue";
import { batchDelete } from "@/utils/delete";
import { ellipsis } from "@/xelComponents/utils/common";
import { ElMessageBox, ElMessage } from "element-plus";
import { remove } from "../api/sime/config/logAccept";
import { useRouter, useRoute, onBeforeRouteLeave } from "vue-router";
import hasPermi from "@/utils/hasPermi.js";

let router = useRouter();
let props = defineProps({
  data: {
    type: [Array, null],
    default() {
      return null;
    },
  },
  //请求的默认参数
  defaultParams: {
    type: Object,
    default() {
      return {};
    },
  },
  // 返回默认格式
  defaultProps: {
    type: Object,
    default() {
      return { children: "children", label: "name" };
    },
  },
  // 接口返回关键字
  resKey: {
    type: String,
    default: "",
  },
  //获取数据的方法
  loadData: {
    type: Function,
    required: true,
  },
  // 添加编辑树节点所有信息
  node_item_data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  table_data: {
    type: Object,
    default: () => {
      return {};
    },
  },
  tableId: {
    type: String,
    default: "",
  },
  // 模块
  moduleTyp: {
    type: String,
    default: "",
  },
});
watch(
  () => props.tableId,
  (val) => {
    if (val) {
      treeRef.value.setCurrentKey(val);
    }
  }
);
let emits = defineEmits(["changeGroup", "changeShow"]);
function changeShow() {
  emits("changeShow");
}
let state = reactive({
  list: [],
  expandKeys: [],
  node_form_data: {
    parentId: null,
  },
  node_form_list: [],
  treeTitle: "",
  groupId: null,
});

let { list, expandKeys, node_form_data, node_form_list, treeTitle } = toRefs(state);
let loading = ref(false);
let addGroupBtn = ref(false);
let editGroupBtn = ref(false);
let deleteGroupBtn = ref(false);
// 获取按钮权限
btnFn();
function btnFn() {
  // 分析规则页面
  if (props.moduleTyp == "Analysis") {
    addGroupBtn.value = hasPermi("config:analysisRuleGroup:add");
    editGroupBtn.value = hasPermi("config:analysisRuleGroup:update");
    deleteGroupBtn.value = hasPermi("config:analysisRuleGroup:delete");
  }
  //日志存储--数据源管理
  if (props.moduleTyp == "dataManage") {
    addGroupBtn.value = hasPermi("logStore:datasourceGroup:add");
    editGroupBtn.value = hasPermi("logStore:datasourceGroup:update");
    deleteGroupBtn.value = hasPermi("logStore:datasourceGroup:delete");
  }
  //日志存储--索引管理
  if (props.moduleTyp == "indexsManage") {
    addGroupBtn.value = hasPermi("logStore:indexGroup:add");
    editGroupBtn.value = hasPermi("logStore:indexGroup:update");
    deleteGroupBtn.value = hasPermi("logStore:indexGroup:delete");
  }
  // 告警转发--数据源管理
  if (props.moduleTyp == "alarmDataManage") {
    addGroupBtn.value = hasPermi("alertRelay:datasourceGroup:add");
    editGroupBtn.value = hasPermi("alertRelay:datasourceGroup:update");
    deleteGroupBtn.value = hasPermi("alertRelay:datasourceGroup:delete");
  }
  // 告警转发-数据表管理
  if (props.moduleTyp == "alarmList") {
    addGroupBtn.value = hasPermi("alertRelay:tableGroup:add");
    editGroupBtn.value = hasPermi("alertRelay:tableGroup:update");
    deleteGroupBtn.value = hasPermi("alertRelay:tableGroup:delete");
  }
  // 告警转发-消息列表
  if (props.moduleTyp == "mqList") {
    addGroupBtn.value = hasPermi("alertRelay:mqGroup:add");
    editGroupBtn.value = hasPermi("alertRelay:mqGroup:update");
    deleteGroupBtn.value = hasPermi("alertRelay:mqGroup:delete");
  }
  // 过滤器
  if (props.moduleTyp == "queryFilter") {
    addGroupBtn.value = hasPermi("queryFilter:group:add");
    editGroupBtn.value = hasPermi("queryFilter:group:update");
    deleteGroupBtn.value = hasPermi("queryFilter:group:delete");
  }

  // 解析规则
  if (props.moduleTyp == "ruleGroup") {
    addGroupBtn.value = hasPermi("config:parseRuleGroup:add");
    editGroupBtn.value = hasPermi("config:parseRuleGroup:edit");
    deleteGroupBtn.value = hasPermi("config:parseRuleGroup:delete");
  }

  /* syslog转发 */
  if (props.moduleTyp == "syslog") {
    addGroupBtn.value = hasPermi("alertRelay:syslogGroup:add");
    editGroupBtn.value = hasPermi("alertRelay:syslogGroup:update");
    deleteGroupBtn.value = hasPermi("alertRelay:syslogGroup:delete");
  }
}

onBeforeRouteLeave((to, from, next) => {
  sessionStorage.setItem("expandNode", "");
  next();
});
// 获取树数据
getData();
function getData(id, echoSelectedNode = false) {
  loading.value = true;
  props.loadData({ ...props.defaultParams }).then((data) => {
    if (props.resKey) {
      state.list = data[props.resKey];
    } else {
      state.list[0] = {
        id: "0",
        name: "全部分组",
        isDataNode: false,
        children: data.data,
      };
      // 是否已经有展开的节点
      let getexpandNode = sessionStorage.getItem("expandNode") ? JSON.parse(sessionStorage.getItem("expandNode")) : "";
      if (getexpandNode.length > 0) {
        let arr = [];
        getexpandNode.forEach((gItem, gIndex) => {
          arr.push(gItem.id);
        });
        arr.push(state.list[0].id);
        state.expandKeys = arr;
      } else {
        state.expandKeys.push(state.list[0].id);
      }
      setTimeout(() => {
        if (id) {
          //编辑节点，返回时选中刚才的分组
          if (echoSelectedNode && currentClickNode) {
            nodeClick(currentClickNode);
          } else {
            currentClickNode = null;

            treeRef.value.setCurrentKey(id);
            //根据id获取node数据
            if (echoSelectedNode) {
              let node = treeRef.value.getNode(id);
              if (node && node.data) {
                nodeClick(node.data);
              }
            } else {
              tableListRef.value ? tableListRef.value.getList() : "";
            }
          }
        }
      }, 200);
    }

    loading.value = false;
  });
}
let showForm = ref(false);
let dialogRef = ref();
// 打开弹框
function popupBox() {
  nextTick(() => {
    dialogRef.value.open();
  });
}
// 关闭弹框，重置form
function resetForm() {
  showForm.value = false;
  state.node_form_data = {};
  getData();
}
// 点击箭头方法，存取展开节点
function expandClick(node) {
  let expandNode = [];
  expandNode.push({
    id: node.id,
    name: node.name,
    parentId: node.parentId,
  });
  sessionStorage.setItem("expandNode", JSON.stringify(expandNode));
}
// 点击节点的方法
let currentClickNode = null; //保存当前点击的节点
function nodeClick(node) {
  if (node.isGroupNode) {
    currentClickNode = JSON.parse(JSON.stringify(node));
  } else {
    currentClickNode = null;
  }
  state.groupId = node.id;
  getData(node.id);
  emits("changeGroup", node);
}
// 节点操作接口
let port = null;
// 添加编辑节点
async function modNode(node, type) {
  showForm.value = true;
  popupBox();
  state.node_form_data = {};
  state.node_form_list = props.node_item_data["add_form_list"];
  if (type == "add") {
    state.treeTitle = "新增" + props.node_item_data.nodeeDialogTitle;
    state.node_form_data.parentId = node.id;
    port = props.node_item_data.addport;
  } else {
    state.treeTitle = "编辑" + props.node_item_data.nodeeDialogTitle;
    state.node_form_data = await getTreeDetailInfo(node.id);
    port = props.node_item_data.editport;
  }
}

// 获取树的详情信息

async function getTreeDetailInfo(id) {
  let port = props.node_item_data.detailport;
  return port(id)
    .then((res) => {
      return res.data;
    })
    .catch(() => {});
}
let treeRef = ref();
// 保存新增修改节点操作
let ruleFormRef = ref();
async function checkName() {
  let check = props.node_item_data.checkName;
  return check(state.node_form_data)
    .then((res) => {
      if (res.code == 200) {
        return false;
      }
      close();
    })
    .catch(() => {
      close(false);
      return true;
    });
}
function saveNodeItem(close, load) {
  ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      // let isReapt = false;
      // if (props.node_item_data.isCheckName) {
      //   isReapt = await checkName();
      // }
      // // 名称不重复
      // if (!isReapt) {
      load();
      port(state.node_form_data)
        .then((res) => {
          if (state.treeTitle.indexOf("新增") > -1) {
            let addNodeData = { ...state.node_form_data, id: res.data.id, isDataNode: false, children: [] };
            treeRef.value.append(addNodeData, state.node_form_data.parentId);
            // treeRef.value.setCurrentKey(res.data.id);
            nodeClick(addNodeData);
          } else {
            treeRef.value.currentNode.data.name = state.node_form_data.name;
            // treeRef.value.setCurrentKey(state.node_form_data.id);
            nodeClick(state.node_form_data);
          }

          showForm.value = false;
          state.node_form_data = {};
          ElMessage.success("操作成功");
          close();
        })
        .catch(() => {
          close(false);
        });
    }
    // }
  });
}

// 删除节点
function delNode(node) {
  batchDelete().then(() => {
    let delItem = props.node_item_data.delport;
    delItem(node.id).then(() => {
      treeRef.value.remove(node.id);
      treeRef.value.setCurrentKey(node.parentId);
      ElMessage.success("删除成功");
      state.groupId = node.parentId;
      getData();
    });
  });
}
let tableListRef = ref();
// 设置树节点选中
function setCheck(data) {
  treeRef.value.setCurrentKey(data);
}
//父组件可以调用的方法
defineExpose({
  setCheck,
  getData,
  tableListRef,
  getTreeData: () => {
    return state.list;
  },
});
</script>

<style lang="scss" scoped>
.sime-layout-wrapper {
  height: max-content;
}
.tree-wrapper {
  position: relative;
  min-height: 100%;
  &::after {
    content: "";
    display: block;
    width: 1px;
    height: 100%;
    position: absolute;
    right: 50%;
    top: 0;
    bottom: 0;
    background: #ededed;
  }
}
// 选中节点样式
:deep .el-tree-node__content {
  height: 32px;
  line-height: 32px;
  position: relative;
}
:deep .is-current > .el-tree-node__content {
  // width: 138px;
  background: $bgColor;
  border-radius: 0px 100px 100px 0px;
}
:deep .is-current > .el-tree-node__content,
:deep .is-current > .el-tree-node__content > .el-tree-node__expand-icon :not(.is-leaf) {
  color: $color;
}
:deep .is-current > .el-tree-node__content:hover .el-tree-node__label {
  background: rgba(255, 255, 255);
  color: $color;
}
.custom-tree-node {
  // width: calc(100% - 100px);
  width: 100%;
  display: inline-block;
  white-space: nowrap; /* 禁止文本换行 */
  overflow: hidden; /* 隐藏溢出内容 */
}
.el-tree-node__label {
  display: none;
  .el-icon {
    transform: translateY(2px);
    margin-right: 8px;
    &:hover {
      // color: $color;
    }
    &:last-child {
      margin-right: 0;
    }
  }
}
.el-tree-node__content:hover .el-tree-node__label {
  margin-top: 4px;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
  background: rgba(177, 177, 177);
  border-radius: 10px;
  color: #fff;
  position: absolute;
  top: 0px;
  right: 10px;
  display: inline-block;
}

.node-txt {
  width: 100%;
  display: inline-block;
  // width: calc(100% - 130px);
  margin-right: 8px;
  vertical-align: bottom;
}
.folder {
  margin-top: 10px;
  margin-right: 4px;
}
</style>
