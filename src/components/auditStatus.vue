<!-- 审核状态 -->
<template>
  <span v-if="Number(status) < 3" class="audit-status non-checked">未审核</span>
  <span v-else-if="status == 3" class="audit-status checked">已通过</span>
  <span v-else-if="status == 4" class="audit-status fail">未通过</span>
</template>
<script setup>
let props = defineProps({
  status: {
    type: [Number, String],
    default: "",
  },
});
</script>

<style lang="scss" scoped>
.audit-status {
  display: inline-block;
  width: 74px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: $raduisM;
}
.non-checked {
  background: #eef5ff;
  color: #63729d;
}

.fail {
  background: #fcedee;
  color: #940000;
}

.checked {
  background: #e4f9f0;
  color: #004830;
}
</style>
