<template>
  <xel-dialog :title="title" ref="dialogRef" :show-submit="false" buttonCancel="关闭" @close="$emit('close')">
    <ul class="details-ul">
      <li v-for="item in list" :style="{ width: item.width || '50%' }" :key="item.prop">
        <span class="detail-label" :style="{ width: labelWidth || labelWidthSelf }">{{ item.label }}：</span>
        <span class="detail-content" :style="{ width: `calc( 100% -   ${labelWidth || labelWidthSelf} )` }"> {{ data[item.prop] || "" }}</span>
      </li>
    </ul>
  </xel-dialog>
</template>
<script setup>
//props看注释
//打开弹框方法 Ref.open()
//弹框关闭 @close ，一般情况下不需要用
import { ref, computed } from "vue";
let props = defineProps({
  //弹框标题
  title: {
    type: String,
    default: "详情",
  },
  //详情信息
  data: {
    type: Object,
    required: true,
  },
  //详情列表：格式和表格组件绑定的columns相同
  list: {
    type: Array,
    default() {
      return [];
    },
  },
  //label宽度，可以不传，会自动计算最大宽度
  labelWidth: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["close"]);

let labelWidthSelf = computed(() => {
  return Math.max(...props.list.map((item) => item.label.length)) + 1 + "em";
});

let dialogRef = ref();
function open() {
  dialogRef.value.open();
}

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.details-ul {
  line-height: 24px;
  overflow: hidden;
  li {
    float: left;
    padding: 0 15px;
    margin-bottom: 15px;
    overflow: hidden;
    span {
      display: inline-block;
    }
  }
  .detail-label {
    // margin-right: 10px;
    float: left;
    color: $fontColorSoft;
  }
  .detail-content {
    float: left;
  }
}
</style>
