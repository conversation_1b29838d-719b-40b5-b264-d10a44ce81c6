<!-- 统计数据 -->
<template>
  <div v-if="borderLine" class="border-line"></div>
  <ul class="statistics-data pull-left">
    <li v-for="(item, index) in list" :key="index">
      <span>{{ item.num }}</span>
      {{ item.text }}
    </li>
  </ul>
</template>
<script setup>
let props = defineProps({
  list: {
    type: Array,
    default: () => {
      return [];
    },
  },
  borderLine: {
    type: Boolean,
    default: true,
  },
});
</script>

<style lang="scss" scoped>
.statistics-data {
  display: flex;
  margin-top: -10px;
  li {
    margin-right: 40px;
    color: $fontColorSoft;
    span {
      color: $color;
      font-size: 36px;
      margin-right: 14px;
    }
  }
}
.border-line {
  border-bottom: 1px solid #ededed;
  margin: 20px 0;
}
</style>
