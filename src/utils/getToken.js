import { TOKEN_NAME, SCREEN_TOKEN_NAME } from "@/config/constant";

import Cookies from "js-cookie";

const IossTokenKey = "Isoss-Admin-Token";

export default function () {
  //子应用获取token
  if (window.$wujie) {
    return Cookies.get(IossTokenKey);
  }
  if (window.location.href.includes("screen.html")) {
    return localStorage.getItem(SCREEN_TOKEN_NAME) ? "Bearer " + localStorage.getItem(SCREEN_TOKEN_NAME) : "";
  } else {
    return localStorage.getItem(TOKEN_NAME) ? "Bearer " + localStorage.getItem(TOKEN_NAME) : "";
  }
}
