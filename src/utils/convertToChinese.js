// 两位数数字转换中文
export function convertToChinese(stage) {
  // 清除之前的中文数字
  let chineseNumber = "";
  // 转换逻辑
  const chineseNumbers = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  // 确保stage是字符串，并转换为数字
  const num = Number(stage);
  const arr = stage.split("");
  // 验证输入是否为正整数
  if (num && !isNaN(num) && num >= 0 && num <= 99) {
    // 分解数字到十位和个位
    const tens = Math.floor(num / 10); // 十位数
    const ones = num % 10; // 个位数
    // 构造中文数字
    if (tens > 0) {
      if (ones === 0) {
        // 只有十位数，如10, 20, ...
        chineseNumber = (tens == 1 ? "" : chineseNumbers[tens]) + "十";
      } else {
        chineseNumber = (tens == 1 ? "" : chineseNumbers[arr[0]]) + "十" + chineseNumbers[arr[1]];
      }
    } else {
      // 只有个位数
      chineseNumber = chineseNumbers[ones];
    }
  }
  return chineseNumber;
}
