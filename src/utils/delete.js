import { ElMessageBox, ElMessage } from "element-plus";
export function batchDelete() {
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(`确认删除选中的数据项？`, "警告", {
      distinguishCancelAndClose: true,
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        resolve();
      })
      .catch((action) => {
        // ElMessage({
        //   type: "info",
        //   message: "取消删除",
        // });
      });
  });
}
