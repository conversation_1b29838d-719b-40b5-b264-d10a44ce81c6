//处理blob文件流
export default function handleBlob(res, name) {
  const blob = new Blob([res]);
  // for IE
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    console.log(11111);
    const fileName = name;
    window.navigator.msSaveOrOpenBlob(blob, fileName);
  } else {
    // for Non-IE (chrome, firefox etc.)
    const fileName = name;
    const elink = document.createElement("a");
    elink.download = fileName;
    elink.style.display = "none";
    elink.href = URL.createObjectURL(blob);
    document.body.appendChild(elink);
    elink.click();
    URL.revokeObjectURL(elink.href);
    document.body.removeChild(elink);
  }
}
