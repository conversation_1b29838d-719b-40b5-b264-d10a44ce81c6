import { useStore } from "vuex";
import { computed } from "vue";

const allPermission = "*:*:*";

export default function (hasPermi) {
  const store = useStore();
  const permissions = computed(() => {
    return store.state && store.state.permissions;
  });
  let showFlag = false;
  if (!hasPermi) {
    showFlag = true;
  } else {
    //判断权限
    showFlag = permissions.value.some((permission) => {
      return allPermission === permission || hasPermi == permission;
    });
  }
  return showFlag;
}
