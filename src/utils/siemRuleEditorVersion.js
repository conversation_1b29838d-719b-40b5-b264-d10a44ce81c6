//获取siem规则编辑器版本
import { getDicts as getDictsSime } from "@/api/sime/config/dict";
import { ref } from "vue";
export default function getSiemRuleVersion() {
  /* 1 旧版本 2新版本
   * ruleEditorVersion 分析规则条件编辑器版本（1-前哨版，2-isoss版）
   * simeSearchVersion 查询分析条件编辑器版本（1-前哨版，2-isoss版）
   * assetdetectVersion 空间资产测绘条件编辑器版本（1-前哨版，2-isoss版）
   * */
  const ruleEditorVersion = ref(1);
  const simeSearchVersion = ref(1);
  const assetdetectVersion = ref(1);
  getDictsSime("common_conditionEditor_version")
    .then((res) => {
      const versionItem = res.data.find((item) => item.dictValue == "analysisRule");
      if (versionItem) ruleEditorVersion.value = parseInt(versionItem.dictLabel);
      // if (versionItem) ruleEditorVersion.value = 1; // 测试旧版本1

      const search = res.data.find((item) => item.dictValue == "search");
      if (search) simeSearchVersion.value = parseInt(search.dictLabel);
      const asset = res.data.find((item) => item.dictValue == "assetdetect");
      if (asset) assetdetectVersion.value = parseInt(asset.dictLabel);
    })
    .catch(() => {
      ruleEditorVersion.value = 1;
      simeSearchVersion.value = 1;
      assetdetectVersion.value = 1;
    });
  return {
    ruleEditorVersion,
    simeSearchVersion,
    assetdetectVersion,
  };
}
