//确认密码验证规则
const validatePass = (rule, value, callback, canEmpty = false) => {
  if (canEmpty && value == "") {
    callback();
  } else {
    // let reg = /(?=.*[a-z])(?=.*\d)(?=.*[#@!?,:~%^$&*.+_()={}<>\\[\]\-\/|;\"'`])[a-z\d#@!?,:~%^$&*.+_()={}<>\\[\]\-\/|;\"'`]{8}/i;

    // let reg = /(?=.*[a-z])(?=.*\d)(?=.*[#@!,:~%^&*.+_()={}$<>\\-\\/])[a-z\d#@!,:~%^&*.+_()={}$<>\\-\\/\\[\\]]{8}/i;

    let reg = /^(?=.*[a-z])(?=.*\d)(?=.*[#@!?,:~%^$&*.+_()={}<>\\[\]\-\/|;\"'`])[a-z\d#@!?,:~%^$&*.+_()={}<>\\[\]\-\/|;\"'`]{8,}$/i;

    if (!reg.test(value)) {
      callback(new Error("密码中必须包含字母、数字、特殊字符，至少8个字符"));
    } else {
      callback();
    }
  }
};
const validatePass2 = (password, rule, value, callback) => {
  if (value !== password) {
    callback(new Error("两次输入密码不一致"));
  } else {
    callback();
  }
};

export { validatePass, validatePass2 };
