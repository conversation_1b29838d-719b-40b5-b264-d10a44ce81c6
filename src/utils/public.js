/* 公共处理文件 */

/* 时间格式化 - 时间戳转时间 */
export function formatDate(value) {
  let date = new Date(value);
  let y = date.getFullYear();
  let MM = date.getMonth() + 1;
  MM = MM < 10 ? "0" + MM : MM;
  let d = date.getDate();
  d = d < 10 ? "0" + d : d;
  let h = date.getHours();
  h = h < 10 ? "0" + h : h;
  let m = date.getMinutes();
  m = m < 10 ? "0" + m : m;
  let s = date.getSeconds();
  s = s < 10 ? "0" + s : s;
  return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
}

/* 将数字1,2,3,4,5,6,7,8...转成一，二，三，四,五，六，七，八...
 * 当前数字最大为100
 * 满足当前业务需求
 *  */
export function numToChinese(num) {
  let arr = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
  let str = "";
  if (num <= 10) {
    str = arr[num - 1];
  } else if (num <= 20) {
    num === 20 ? (str = "二十") : (str = "十" + arr[num - 11]);
  } else if (num <= 30) {
    num === 30 ? (str = "三十") : (str = "二十" + arr[num - 21]);
  } else if (num <= 40) {
    num === 40 ? (str = "四十") : (str = "三十" + arr[num - 31]);
  } else if (num <= 50) {
    num === 50 ? (str = "五十") : (str = "四十" + arr[num - 41]);
  } else if (num <= 60) {
    num === 60 ? (str = "六十") : (str = "五十" + arr[num - 51]);
  } else if (num <= 70) {
    num === 70 ? (str = "七十") : (str = "六十" + arr[num - 61]);
  } else if (num <= 80) {
    num === 80 ? (str = "八十") : (str = "七十" + arr[num - 71]);
  } else if (num <= 90) {
    num === 90 ? (str = "九十") : (str = "八十" + arr[num - 81]);
  } else if (num <= 100) {
    num === 100 ? (str = "一百") : (str = "九十" + arr[num - 91]);
  } else {
    str = num;
  }
  return str;
}
