import axios from "axios";
import { tansParams } from "@/utils/ruoyi";
import { getToken } from "@/utils";
let service = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  // 超时
  timeout: 10000,
});
export function download(url, params, filename) {
  return service
    .post(url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params);
        },
      ],
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: getToken(),
      },
      responseType: "blob",
    })
    .then(({ data }) => {
      const content = data;
      const blob = new Blob([content]);
      if ("download" in document.createElement("a")) {
        const elink = document.createElement("a");
        elink.download = filename;
        elink.style.display = "none";
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click();
        URL.revokeObjectURL(elink.href);
        document.body.removeChild(elink);
      } else {
        navigator.msSaveBlob(blob, filename);
      }
    })
    .catch((r) => {
      console.error(r);
    });
}
