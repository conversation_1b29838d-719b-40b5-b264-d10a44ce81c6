//滚动到未通过验证的表单项
import { ref } from "vue";
let scrollRef = ref();
import { ElMessage } from "element-plus";

function scrollToUnValid() {
  setTimeout(() => {
    if (scrollRef.value) {
      let errorItem = scrollRef.value.$el.querySelector(".el-form-item.is-error");
      console.log("errorItem: ", errorItem);
      if (errorItem) {
        // ElMessage.warning("表单验证未通过");
        errorItem.scrollIntoView({ behavior: "smooth" });
      }
    }
  }, 500);
}

export { scrollRef, scrollToUnValid };
