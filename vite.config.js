import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import { loadEnv } from "vite";
import createVitePlugins from "./vite/plugins";

// https://vitejs.dev/config/

let prodBaseUrl = "http://127.0.0.1:8080/"; //打包地址,请勿修改

let devBaseUrl = {
  target: "http://10.110.101.213:7080/", //测试服务器地址
  // target: "http://10.110.102.193:8080/",
  // target: "http://10.110.60.48:8080/",
};
export default ({ command, mode }) => {
  const __DEV__ = command == "serve";
  const env = loadEnv(mode, process.cwd());

  // 打包输出路径
  let outDir = "dist";
  if (command === "build") {
    if (mode == "sime") {
      outDir = "dist_siem";
    } else if (mode == "isossSiem") {
      outDir = "dist_isoss_siem";
    }
  }

  return defineConfig({
    plugins: createVitePlugins(env, command === "build"),
    base: loadEnv(mode, process.cwd()).VITE_BASE_URL || "/",
    server: {
      hot: true,
      open: true,
      port: 3000,
      host: "0.0.0.0",
      proxy: {
        [loadEnv(mode, process.cwd()).VITE_BASE_API]: {
          target: __DEV__ ? devBaseUrl.target : prodBaseUrl,
          changeOrigin: true,
          rewrite: (path) => path.replace(loadEnv(mode, process.cwd()).VITE_BASE_API, ""),
        },
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        starso: path.resolve(__dirname, "./node_modules", "starso-components-ui/lib/es"),
      },
    },
    // 强制预构建插件包
    optimizeDeps: {
      include: ["axios"],
    },
    //生产模式打包配置
    build: {
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, "index.html"),
          nested: path.resolve(__dirname, "screen.html"),
        },
      },
      //输出路径
      outDir,
    },

    css: {
      // css预处理器
      preprocessorOptions: {
        scss: {
          // 引入 variable.scss 这样就可以在全局中使用 variable.scss中预定义的变量了
          // 给导入的路径最后加上 ;

          additionalData: (content, loaderContext) => {
            let variableName = "variable";
            if (loadEnv(mode, process.cwd()).VITE_STYLE2) {
              variableName = "variable-style2";
            } else if (loadEnv(mode, process.cwd()).VITE_STYLE3) {
              variableName = "variable-style3";
            }
            if (!loaderContext.includes("element-variables")) {
              return `@import "./src/assets/style/${variableName}.scss";` + content;
            }
            return content;
          },
        },
      },
    },
  });
};
