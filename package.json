{"name": "vue-three", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "devSime": "vite --mode simeDev", "buildSime": "vite build --mode sime", "devStyle2": "vite  --mode style2Dev", "buildStyle2": "vite build --mode style2", "buildNewbeile": "vite build --mode newbeile", "devIsossSiem": "vite --mode isossSiemDev", "buildIsossSiem": "vite build --mode isossSiem", "buildAll": "npm run build && npm run buildSime && npm run buildIsossSiem", "serve": "vite cpreview", "staging": "vite build --mode staging", "lint": "eslint --fix --ext .js,.vue src", "prepare": "husky install", "preview": "vite preview", "format": "prettier --write src"}, "lint-staged": {"*.{vue,js}": "eslint --fix --ext "}, "dependencies": {"@element-plus/icons": "^0.0.11", "@fullcalendar/core": "^5.9.0", "@fullcalendar/daygrid": "^5.9.0", "@fullcalendar/interaction": "^5.9.0", "@fullcalendar/timegrid": "^5.9.0", "@fullcalendar/vue3": "^5.9.0", "axios": "^0.21.4", "crypto-js": "^4.1.1", "echarts": "^5.2.1", "echarts-gl": "^2.0.8", "echarts-liquidfill": "^3.1.0", "element-plus": "^1.1.0-beta.20", "file-saver": "^2.0.5", "js-cookie": "^3.0.1", "jsencrypt": "^3.2.1", "minireset.css": "^0.0.7", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "quill": "^1.3.7", "sass": "^1.42.1", "sortablejs": "^1.14.0", "three": "^0.132.2", "tiny-emitter": "^2.1.0", "uuid": "^8.3.2", "vue": "3.2.13", "vue-cropper": "^1.0.2", "vue-cropperjs": "^5.0.0", "vue-draggable-next": "^2.1.1", "vue-router": "^4.0.11", "vue3-draggable-resizable": "^1.6.5", "vue3-seamless-scroll": "^2.0.1", "vue3-treeselect": "^0.1.10", "vuex": "^4.0.2", "wangeditor": "^4.7.8", "xlsx": "^0.17.4"}, "devDependencies": {"@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "@vitejs/plugin-vue": "^1.9.0", "@vue/compiler-sfc": "3.2.22", "eslint": "^7.25.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.9.0", "husky": "^7.0.2", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^2.2.1", "script-loader": "^0.7.2", "starso-components-ui": "git+http://**************/starso/starso-components-ui.git#release_outpostWeb", "unplugin-auto-import": "0.5.3", "vite": "^2.5.10", "vite-plugin-compression": "0.3.6", "vite-plugin-svg-icons": "1.0.5", "vite-plugin-vue-setup-extend": "0.1.0"}}