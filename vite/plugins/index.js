import vue from "@vitejs/plugin-vue";

import createAutoImport from "./auto-import";
// import createSvgIcon from './svg-icon'
// import createCompression from './compression'
// import createSetupExtend from './setup-extend'

const htmlPlugin = (title) => {
  return {
    name: "html-transform",
    transformIndexHtml(html) {
      // console.log(import.meta.env);
      return html.replace(/<title>(.*?)<\/title>/, `<title>${title}</title>`);
    },
  };
};

export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];
  vitePlugins.push(createAutoImport());
  // vitePlugins.push(createSetupExtend())
  // vitePlugins.push(createSvgIcon(isBuild))
  // isBuild && vitePlugins.push(...createCompression(viteEnv))
  vitePlugins.push(htmlPlugin(viteEnv.VITE_SYSTEM_TITLE));

  return vitePlugins;
}
