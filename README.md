# Vue 3 + Vite

# 启动项目

## 前哨

npm run dev

## siem 独立版

npm run devSiem

## isoss 内的 siem

npm run devIsossSiem (vite.config.js 中的端口号需要改为 8080)

# 修复代码格式 eslint

npm run lint

# 代码格式化插件安装

安装插件 prettier  
保存文件，按 ctrl + shift + f，选择 prettier 格式化程序

# 代码提交

提交时做 eslint 代码校验，如果不能提交，查看 git 提交报错信息，根据报错修正代码，再次提交。

# 开发注意

    1. views router vuex api 按模块一一对应

    2. 添加必要的注释

    3.css 样式禁止写在行内，vue 文件内的 style 标签必须加 scoped

    4.控制子组件内的样式或覆盖element-plus样式时，使用:deep，控制插槽内的样式使用::v-slotted
    ```
    <style scoped lang="scss">
    /* deep selectors */
    :deep(.foo) {}

    /* targeting slot content */
    ::v-slotted(.foo) {}
    </style>
    ```
    5.http请求，返回数据code===200 则返回data

    6.class和组件名都使用中划线命名

# 使用 element-plus 的 Message 消息提示，MessageBox 弹框 ， Notification 通知

```
<script setup>
import {  ElMessage } from 'element-plus'
ElMessage({
  type: 'info',
  message: `action: ${action}`,
})
</script>
```

# .md 文件查看

安装插件 Markdown Preview Enhanced ，在编辑器中预览

# 项目中包含 ruoyi 框架部分内容，用于 sime 独立的系统管理

1.  request.js 中判断如果是 sime 独立项目，并且是/system/开头的接口，降返回的 data 数据处理成展开数据
2.  全局方法，组件引入
3.  views/sime/system 文件夹，以及其他位置的 sime 文件夹或者以 sime 开头的文件夹都是独立 sime 使用，包括 style,components,plusin
