# VUE3.0 基础语法

## setup 单文件组件

vue3 提供了组合式 API 语法糖，在 script 标签上添加 setup .

```
<script setup>

</script>
```

## data 的改变

1. 响应式数据直接在`<script>`标签中定义变量，简单类型的变量使用 ref 创建，数组和对象用 reactive 创建。非响应式数据直接使用。注：ref 和 reactive 需要引入。
2. 访问或修改 ref()创建的值，需要使用变量的 value 属性

```
import {ref,reactive} from 'vue
<script setup>
let str = '非数据视图绑定的变量';

let num = ref(0);//定义简单数据类型的响应式变量

num.value ++ ;// 修改num的值

let obj = reactive({
    a:1,
    b:2,
});//定义响应式的对象或数组
obj.a = 3;//修改对象
</script>
```

## methods 的改变

直接在`<script>`标签中定义函数,模板中绑定对应函数名

```
<template>
<button @click="alertFn"></button>
</template>

<script setup>
function alertFn(){
    alert(1)
}
</script>
```

## props 的改变

通过 defineProps 定义，这个 api 是全局的，不需要引入

```
<script setup>
    let props = defineProps({
        title:{
            type:String,
            default:'标题'
        }
    })
    props.title // 访问props
</script>
```

## $emit 的改变

组件内的事件传递必须通过 defineEmits 来显示声明，全局的，不需要引入

```
<script setup>
const emit = defineEmits(['change', 'delete'])
emits("change",1)//传递事件
</script>
```

## components 的改变

在 script-setup 中导入任意的组件就可以直接在 `template `中使用

```
<template>
  <Foo />
</template>
<script setup>
  import Foo from './Foo.vue'
</script>
```

## 生命周期的改变

setup 在生命周期 beforeCreate 之前执行，因此不能使用 this。  
可以通过直接导入 onX 函数来注册生命周期钩子，将要执行的代码放到钩子函数回调函数参数中。  
在生命周期钩子中，不能对数组和对象直接赋值，不会触发视图的更新，需要调用数组的方法改变原数组。


```
import {onMounted,onActivated} from 'vue'
<script setup>
let arr = reactive([])
onMounted(()=>{
    console.log('mounted挂载完成后执行')
    arr.push(...[1,2,3])
})
</script>
```

## 访问子组件$refs 的改变

因为访问不到 this,所以不能使用$refs。  
需要用 ref()显示声明 ref 的名称。

```
<template>
  <el-form ref="form" ></el-form>
</template>
<script setup>
import {ref} from 'vue'
  let form = ref();
  //调用表单组件的验证方法
  form.value.validate(valid=>{
  })
</script>
```

## computed 的改变

引入 computed 函数，在回调中定义值

```
import {  computed } from "vue";
<script setup>
let computedNum = computed(() => {
  return props.total * 1000;
});
</script>
```

## watch 的改变

引入 watch 函数，回调函数的第一个值是监听的变量，第二个参数是 handler

```
import {  watch } from "vue";
<script setup>
// 侦听一个 getter
const state = reactive({ count: 0 })
watch(
  () => state.count,
  (count, prevCount) => {
    /* ... */
  }
)

// 直接侦听一个 ref
const count = ref(0)
watch(count, (count, prevCount) => {
  /* ... */
})
</script>
```

## \$attrs 和\$listener 的改变

\$attrs 包含 class 和 style  
\$listene 废弃，事件加`on`前缀，成为\$attrs 的一部分

## 推荐设置 vue 代码块

```
{
  "Print to console": {
    "prefix": "vue",
    "body": [
      "<template>\n",
      "</template>",

      "<script setup>\n",
      "import { ref, reactive, onMounted } from 'vue';\n",
      "</script>\n",

      "<style lang='scss' scoped>",
      "</style>"
    ],
    "description": "vue"
  }
}
```

## 调用子组件属性和方法的改变

为了在 `<script setup>` 组件中明确要暴露出去的属性和方法，使用 全部变量 defineExpose 显示声明,否则父组件获取不到。

```
<script setup>
import {ref} from 'vue'
let num = ref(0)
let num2 = ref(1)
defineExpose({
  num //父组件可以获取num属性，无法获取num2
})
</script>
```

## vue-router 的使用

参考 vue-router 文档[https://next.router.vuejs.org/zh/guide/advanced/composition-api.html#%E5%9C%A8-setup-%E4%B8%AD%E8%AE%BF%E9%97%AE%E8%B7%AF%E7%94%B1%E5%92%8C%E5%BD%93%E5%89%8D%E8%B7%AF%E7%94%B1]  
使用 useRouter，useRoute 函数

```
import { useRouter, useRoute } from 'vue-router'
const router = useRouter()
router.push('/) //路由跳转

const route = useRoute()
route.params.id //访问路由参数
```

在 Vue 3 中，此类用法将不再自动创建 $ref 数组。要从单个绑定获取多个 ref，请将 ref 绑定到一个更灵活的函数上 (这是一个新特性)

```
<div v-for="item in list" :ref="setItemRef"></div>
<script setip>
  let itemRefs = []
  //生成ref数组
  const setItemRef = el => {
    if (el) {
      itemRefs.push(el)
    }
  }
  onBeforeUpdate(() => {
    itemRefs = []
  })
  onUpdated(() => {
    console.log(itemRefs)
  })
</script>

```
