let obj = {
  name: "事件",
  notetype: "and",
  children: [
    {
      name: "ireponse", //字段名 filed
      operator: "equal", //操作
      value: "5", //输入框值
      nameText: "响应结果", //filledText
      operatorText: "等于", //操作汉字
      valueText: "成功", //选择对应的文字，如果是输入 和value相同
      notetype: "condition", //条件
    },
    {
      name: "",
      notetype: "or", // and or not
      children: [
        {
          name: "filter",
          operator: "in",
          value: "1453972269910827010",
          notetype: "condition",
          nameText: "过滤器",
          operatorText: "等于",
          valueText: "原始日志",
        },
        {
          name: "level",
          operator: "equal",
          value: "5",
          notetype: "condition",
          nameText: "响应结果",
          operatorText: "等于",
          valueText: "成功",
        },
      ],
    },
    {
      name: "",
      notetype: "or",
      children: [
        {
          name: "ceventname",
          operator: "contain",
          value: "攻击",
          notetype: "condition",
          nameText: "响应结果",
          operatorText: "等于",
          valueText: "成功",
        },
        {
          name: "",
          notetype: "not",
          children: [
            {
              name: "ireponse",
              operator: "lt",
              value: "8",
              notetype: "condition",
              nameText: "响应结果",
              operatorText: "等于",
              valueText: "成功",
            },
          ],
        },
      ],
    },
  ],
};
