[
  {
    name: "homeLayout",
    path: "/",
    component: "views/layout/index.vue",
    meta: {
      icon: "icon-shouye",
      title: "概览",
    },
    children: [
      {
        path: "",
        component: "views/home/<USER>",
        name: "home",
        meta: {
          icon: "icon-shouye",
          title: "概览",
        },
      },
      {
        name: "Workbench",
        path: "/workbench",
        hidden: false,
        component: "views/workSpace/index.vue",
        meta: {
          title: "工作台",
          icon: "icon-zhuzhuangtu",
        },
      },
    ],
  },
  {
    name: "System",
    path: "/system",
    hidden: false,
    redirect: "noRedirect",
    component: "views/layout/index.vue",
    alwaysShow: true,
    meta: {
      title: "系统设置",
      icon: "icon-nav_system",
    },
    children: [
      {
        name: "UserList",
        path: "userList",
        hidden: false,
        component: "views/system/user/userList.vue",
        meta: {
          title: "账号管理",
          icon: "yong<PERSON><PERSON><PERSON><PERSON>",
        },
      },

      {
        name: "Role",
        path: "role",
        hidden: false,
        component: "views/system/role/index.vue",
        meta: {
          title: "角色管理",
          icon: "jues<PERSON>uan<PERSON>",
        },
      },
      {
        name: "customer",
        path: "customer",
        component: "views/system/customer/index.vue",
        meta: {
          title: "客户管理",
          icon: "kehuguanli",
        },
      },
      {
        name: "department",
        path: "department",
        component: "views/system/department/index.vue",
        meta: {
          title: "部门管理",
          icon: "bumenguanli",
        },
      },

      {
        name: "Menu",
        path: "menu",
        hidden: false,
        component: "views/system/menu/index.vue",
        meta: {
          title: "菜单管理",
          icon: "caidanguanli",
        },
      },
      {
        name: "Dict",
        path: "dict",
        hidden: false,
        component: "views/system/dict/index.vue",
        meta: {
          title: "字典管理",
          icon: "zidianguanli",
        },
      },
      {
        name: "Log",
        path: "log",
        hidden: false,
        component: "views/system/log/index.vue",
        alwaysShow: true,
        meta: {
          title: "日志管理",
          icon: "rizhiguanli",
        },
      },
      {
        name: "BasicSoftware",
        path: "basicSoftware",
        hidden: false,
        component: "views/system/basicSoftware/index.vue",
        meta: {
          title: "基础资源软件",
          icon: "jichuziyuanruanjian",
        },
      },
      {
        name: "License",
        path: "license",
        hidden: false,
        component: "views/system/license/submitmain.vue",
        meta: {
          title: "授权管理",
          icon: "shouquanguanli",
        },
      },
      {
        name: "SendTypemain",
        path: "sendTypemain",
        hidden: false,
        component: "views/system/sendTypemain/index.vue",
        meta: {
          title: "通知发送方式",
          icon: "tongzhifasongfangshi",
        },
      },
      {
        name: "versionInformation",
        path: "versionInformation",
        hidden: false,
        component: "views/system/versionInformation/index.vue",
        meta: {
          title: "版本信息",
          icon: "tongzhifasongfangshi",
        },
      },

      {
        name: "resetPsd",
        path: "resetPsd",
        hidden: false,
        component: "views/system/resetPsd.vue",
        meta: {
          title: "密码修改",
          icon: "yewuxitong",
        },
      },
    ],
  },
  {
    name: "screen",
    path: "/screen",
    component: "views/layout/index.vue",
    meta: {
      title: "态势大屏",
      icon: "icon-nav_tsgz",
    },
    children: [
      {
        name: "statePersonnel",
        path: "http://10.110.101.119/screen/statePersonnel",
        meta: {
          title: "人员状态大屏",
          icon: "icon-nav_quit",

          isLink: true,
        },
      },
      {
        name: "threatOperation",
        path: "http://10.110.101.119/screen/threatOperation",
        meta: {
          title: "威胁运营大屏",
          icon: "icon-nav_quit",

          isLink: true,
        },
      },
      {
        name: "vulnerability",
        path: "http://10.110.101.119/screen/vulnerability",
        meta: {
          title: "脆弱性运营",
          icon: "icon-nav_quit",

          isLink: true,
        },
      },
      {
        name: "infrastructure",
        path: "http://10.110.101.119/screen/infrastructure",
        meta: {
          title: "运营基础设施架构",
          icon: "icon-nav_quit",

          isLink: true,
        },
      },
    ],
  },
  {
    name: "SecurityAssets",
    path: "/securityAssets",
    hidden: false,
    redirect: "noRedirect",
    component: "views/layout/index.vue",
    alwaysShow: true,
    meta: {
      title: "安全资产",
      icon: "icon-nav_aqzc",
    },
    children: [
      {
        name: "assetOverview",
        path: "assetOverview",
        hidden: false,
        component: "views/securityAssets/assetOverview/index.vue",
        meta: {
          title: "资产总览",
          icon: "zichanzonglan",
        },
      },
      {
        name: "assetGroup",
        path: "assetGroup",
        hidden: false,
        component: "views/securityAssets/assetGroup/index.vue",
        meta: {
          title: "资产组",
          icon: "zichanzu",
        },
      },
      {
        name: "businessAssets",
        path: "businessAssets",
        hidden: false,
        component: "views/securityAssets/businessAssets/index.vue",
        meta: {
          title: "业务系统资产",
          icon: "yewuxitongzichan",
        },
      },
      {
        name: "underlying",
        path: "underlying",
        hidden: false,
        component: "views/securityAssets/underlying/index.vue",
        meta: {
          title: "计算设备资产",
          icon: "jichuzichanduixiang",
        },
      },
      {
        name: "terminal",
        path: "terminal",
        hidden: false,
        component: "views/securityAssets/terminal/index.vue",
        meta: {
          title: "终端资产对象",
          icon: "jichuzichanduixiang",
        },
      },
      {
        name: "internet",
        path: "internet",
        hidden: false,
        component: "views/securityAssets/internet/index.vue",
        meta: {
          title: "互联网暴露资产",
          icon: "jichuzichanduixiang",
        },
      },
      {
        name: "confirm",
        path: "confirm",
        hidden: false,
        component: "views/securityAssets/confirm/index.vue",
        meta: {
          title: "待确认资产",
          icon: "jichuzichanduixiang",
        },
      },

      {
        name: "addAsset",
        path: "addAsset",
        hidden: true,
        component: "views/securityAssets/businessAssets/addAsset.vue",
        meta: {
          title: "添加资产",
          icon: "yewuxitongzichan",

          permission: "businessAssets",
        },
      },
      {
        name: "changeAsset",
        path: "changeAsset/:terminalId",
        hidden: true,
        component: "views/securityAssets/businessAssets/addAsset.vue",
        meta: {
          title: "变更资产",
          icon: "yewuxitongzichan",

          permission: "businessAssets",
        },
      },
      //待确认资产的新建
      {
        name: "confirmAdd",
        path: "confirmAdd/:type/:confirmId",
        hidden: true,
        component: "views/securityAssets/businessAssets/addAsset.vue",
        meta: {
          title: "新建资产",
          icon: "yewuxitongzichan",

          permission: "businessAssets",
        },
      },
      //工作台待确认资产的新建
      {
        name: "WorkBenchConfirmAdd",
        path: "WorkBenchConfirmAdd/:type/:confirmId/:assetType",
        hidden: true,
        component: "views/securityAssets/businessAssets/addAsset.vue",
        meta: {
          title: "新建资产",
          icon: "yewuxitongzichan",

          permission: "businessAssets",
        },
      },
      {
        name: "newAssetGroup",
        path: "newAssetGroup/:id",
        hidden: true,
        component: "views/securityAssets/assetGroup/newAssetGroup.vue",
        meta: {
          title: "资产组详情",
          icon: "xinjianzichanzu",
          permission: "assetGroup",
        },
      },
      {
        name: "newAsset",
        path: "newAsset/:id",
        hidden: true,
        component: "views/securityAssets/assetGroup/newAssetGroup.vue",
        meta: {
          title: "新建资产组",
          icon: "xinjianzichanzu",
          permission: "assetGroup",
        },
      },
      {
        name: "assetDetails",
        path: "assetDetails/:id/:type",
        hidden: true,
        component: "views/securityAssets/businessAssets/assetDetails.vue",
        meta: {
          title: "资产详情",
          icon: "yewuxitongzican",

          permission: "businessAssets",
        },
      },
      {
        name: "jumpPage",
        //id:资产id  pageType:0 漏洞 1：事件 assetType: 0|| 1 ||2  spare1:标识 1:已处置 0未处置
        path: "jumpPage/:id/:pageType/:assetType/:spare1",
        hidden: true,
        component: "views/securityAssets/businessAssets/components/jumpPage.vue",
        meta: {
          title: "列表",
          icon: "liebiao",

          permission: "businessAssets",
        },
      },
      {
        name: "jumpService",
        path: "jumpService/:id/:assetType",
        hidden: true,
        component: "views/securityAssets/businessAssets/business/jumpService.vue",
        meta: {
          title: "列表",
          icon: "liebiao1",

          permission: "businessAssets",
        },
      },
    ],
  },
  {
    name: "vulnLayout",
    path: "/vuln",
    component: "views/layout/index.vue",
    meta: {
      title: "漏洞隐患",

      icon: "icon-nav_loudong",
    },
    children: [
      {
        name: "vulnOverview",
        path: "vulnOverview",
        component: "views/vuln/overview/index.vue",
        meta: {
          title: "漏洞总览",
        },
      },
      {
        name: "vulnBusiness",
        path: "vulnBusiness",
        component: "views/vuln/business/index.vue",
        meta: {
          title: "业务系统漏洞",
        },
      },
      {
        name: "vulnBusinessDetail",
        path: "vulnBusinessDetail/:id",
        hidden: true,
        component: "views/vuln/business/detail.vue",
        meta: {
          title: "业务系统漏洞详情",
          permission: "vulnBusiness",
        },
      },
      {
        name: "vulnBaisc",
        path: "vulnBaisc",
        component: "views/vuln/basic/index.vue",
        meta: {
          title: "基础资源漏洞",
        },
      },
      {
        name: "vulnBasicDetail",
        path: "vulnBasicDetail/:id",
        hidden: true,
        component: "views/vuln/business/detail.vue",
        meta: {
          title: "基础资源漏洞详情",
          permission: "vulnBaisc",
        },
      },
      {
        name: "leakage",
        path: "leakage",
        component: "views/vuln/leakage/index.vue",
        meta: {
          title: "安全敏感信息泄露",
        },
      },
      {
        name: "sensitiveDetails",
        path: "sensitiveDetails/:id",
        hidden: true,

        component: "views/vuln/leakage/sensitiveDetails.vue",
        meta: {
          title: "安全敏感信息漏洞详情",
          permission: "leakage",
        },
      },
      {
        name: "hiddenDanger",
        path: "hiddenDanger",
        component: "views/vuln/hiddenDanger/index.vue",
        meta: {
          title: "安全意识隐患",
        },
      },
      {
        name: "Consciousness",
        path: "consciousness/:id",
        hidden: true,
        component: "views/vuln/hiddenDanger/consciousness.vue",
        meta: {
          title: "安全意识评估详情",
          permission: "hiddenDanger",
        },
      },
      {
        name: "safetyWarning",
        path: "safetyWarning",
        component: "views/vuln/safetyWarning/index.vue",
        meta: {
          title: "安全预警",
        },
      },
      {
        name: "ignoremanagement",
        path: "ignoremanagement",
        component: "views/vuln/ignoremanagement/index.vue",
        meta: {
          title: "漏洞忽略管理",
        },
      },
    ],
  },
  {
    name: "AnalyticalDisposal",
    path: "/analyticalDisposal",

    component: "views/layout/index.vue",
    meta: {
      title: "运营脚本",
      icon: "icon-zhuzhuangtu",
    },
    children: [
      {
        name: "RunscriptList",
        path: "runscriptList",
        hidden: false,
        component: "views/analyticalDisposal/runscript/runscriptList.vue",
        meta: {
          title: "运营脚本",
          icon: "icon-zhuzhuangtu",

          permission: "AnalyticalDisposal",
        },
      },
      {
        name: "addEditTemplate",
        path: "addEditTemplate/:id",
        hidden: true,
        component: "views/analyticalDisposal/runscript/components/addEditTemplate.vue",
        meta: {
          title: "模板详情",
          icon: "gaojingpeizhi",

          permission: "RunscriptList",
        },
      },
    ],
  },
  {
    name: "OperationParam",
    path: "/operationParam",
    hidden: false,
    redirect: "noRedirect",
    component: "views/layout/index.vue",
    alwaysShow: true,
    meta: {
      title: "运营管理",
      icon: "icon-nav_yunying",
    },
    children: [
      {
        name: "config",
        path: "config",
        hidden: false,
        component: "views/operationParam/config/index.vue",
        meta: {
          title: "参数配置",
          icon: "canshupeizhi",
        },
      },
      {
        name: "samplingRate",
        path: "samplingRate",
        hidden: false,
        component: "views/operationParam/samplingRate/index.vue",
        meta: {
          title: "抽检率",
          icon: "choujianlv",
        },
      },
      {
        name: "alertConfig",
        path: "alertConfig",
        hidden: false,
        component: "views/operationParam/alertConfig/index.vue",
        meta: {
          title: "告警配置",
          icon: "gaojingpeizhi",
        },
      },
      {
        name: "dataSource",
        path: "dataSource",
        hidden: false,
        component: "views/operationParam/dataSource/sourceList.vue",
        meta: {
          title: "数据源配置",
          icon: "gaojingpeizhi",
        },
      },
      {
        name: "resource",
        path: "resource",
        hidden: false,
        component: "views/operationParam/resource/resourceList.vue",
        meta: {
          title: "资源监控管理",
          icon: "gaojingpeizhi",
        },
      },
      {
        name: "timeTask",
        path: "timeTask",
        hidden: false,
        component: "views/operationParam/timeTask/timeTaskList.vue",
        meta: {
          title: "定时任务配置",
          icon: "gaojingpeizhi",
        },
      },
      {
        name: "timeTaskLog",
        path: "timeTaskLog",
        hidden: true,
        component: "views/operationParam/timeTask/timeTaskLog.vue",
        meta: {
          title: "定时任务日志",
          icon: "gaojingpeizhi",

          permission: "timeTask",
        },
      },
      {
        name: "logoConfig",
        path: "logoConfig",
        hidden: false,
        component: "views/operationParam/logoConfig.vue",
        meta: {
          title: "LOGO配置",
          icon: "gaojingpeizhi",
        },
      },

      {
        name: "largeScreen",
        path: "largeScreen",
        hidden: false,
        component: "views/operationParam/largeScreen/screenList.vue",
        meta: {
          title: "大屏数据导入",
          icon: "gaojingpeizhi",
        },
      },
      {
        name: "timedTask",
        path: "timedTask",
        component: "views/system/timedTask/index.vue",
        meta: {
          title: "MTTD/MTTA定时",
          icon: "dingshi",
        },
      },
      {
        name: "alarmflowQuery",
        path: "alarmflowQuery",
        component: "views/operationParam/alarmflowQuery/index.vue",
        meta: {
          title: "告警流查询",
          icon: "dingshi",
        },
      },
      {
        name: "exportSsp",
        path: "exportSsp",
        hidden: false,
        component: "views/system/dataExport/index.vue",
        meta: {
          title: "导出SSP数据",
          icon: "icon-nav_quit",
        },
      },
      {
        name: "reportLayout",
        path: "/report",
        component: "views/layout/index.vue",
        meta: {
          title: "报告",
          icon: "icon-nav_baogao",
        },
        children: [
          {
            name: "report",
            path: "",
            // hidden: true,
            component: "views/system/report/index.vue",
            meta: {
              title: "报告",

              icon: "icon-nav_baogao",
            },
          },
        ],
      },
    ],
  },
  {
    name: "reportLayout",
    path: "/report",
    component: "views/layout/index.vue",
    meta: {
      title: "报告",

      icon: "icon-nav_baogao",
    },
    children: [
      {
        name: "report",
        path: "",
        // hidden: true,
        component: "views/system/report/index.vue",
        meta: {
          title: "报告",

          icon: "icon-nav_baogao",
        },
      },
    ],
  },
  {
    name: "simeBatchProcessingLayout",
    path: "/simeBatchProcessing",
    component: "views/layout/index.vue",
    meta: {
      title: "分析规则",
      icon: "icon-nav_fxgz",
    },
    children: [
      {
        name: "simeBatchProcessing",
        path: "rule",
        hidden: false,
        component: "views/sime/simeBatchProcessing/index.vue",

        meta: {
          title: "分析规则",
          icon: "icon-nav_fxgz",
        },
      },
      {
        name: "ruleVersion",
        path: "ruleVersion/:id",
        hidden: true,
        component: "views/sime/simeBatchProcessing/version.vue",

        meta: {
          title: "分析规则版本",
          permission: "simeBatchProcessing",
        },
      },
      {
        name: "ruleVersionDetail",
        path: "ruleVersionDetail/:id",
        hidden: true,
        component: "views/sime/simeBatchProcessing/versionDetail.vue",

        meta: {
          title: "版本详情",
          permission: "simeBatchProcessing",
        },
      },
    ],
  },
  {
    name: "simeConfigLayout",
    path: "/simeConfig",
    component: "views/layout/index.vue",
    meta: {
      title: "分析配置",
      icon: "icon-nav_sjy",
    },
    children: [
      {
        name: "SimeLog",
        path: "SimeLog",
        hidden: false,
        component: "views/sime/config/logStore/log.vue",
        meta: {
          title: "日志存储",
        },
      },
      {
        name: "addEditIndexes",
        path: "addEditIndexes/:id",
        hidden: true,
        component: "views/sime/config/logStore/components/addEditIndexes.vue",
        meta: {
          title: "新增索引配置",
          icon: "gaojingpeizhi",
          permission: "SimeLog",
        },
      },
      {
        name: "editIndexes",
        path: "editIndexes/:id",
        hidden: true,
        component: "views/sime/config/logStore/components/addEditIndexes.vue",
        meta: {
          title: "修改索引配置",
          icon: "gaojingpeizhi",
          permission: "SimeLog",
        },
      },
      {
        name: "Alarm",
        path: "alarm",
        hidden: false,
        component: "views/sime/config/alarm/index.vue",

        meta: {
          title: "告警输出",
        },
      },
      {
        name: "addEditAlarm",
        path: "addEditAlarm/:id",
        hidden: true,
        component: "views/sime/config/alarm/components/addEditAlarm.vue",
        meta: {
          title: "数据表详情",
          icon: "gaojingpeizhi",

          permission: "alarm",
        },
      },
      {
        name: "addEditMessage",
        path: "addEditMessage/:id",
        hidden: true,
        component: "views/sime/config/alarm/components/addEditMessage.vue",
        meta: {
          title: "消息队列详情",
          icon: "gaojingpeizhi",

          permission: "alarm",
        },
      },
      {
        name: "filter",
        path: "filter",
        hidden: false,
        component: "views/sime/config/filter/index.vue",

        meta: {
          title: "过滤器管理",
        },
      },
      {
        name: "filterVersion",
        path: "filterVersion/:id",
        hidden: true,
        component: "views/sime/config/filter/version.vue",

        meta: {
          title: "过滤器版本",
          permission: "filter",
        },
      },
      {
        name: "filterVersionDetail",
        path: "filterVersionDetail/:id",
        hidden: true,
        component: "views/sime/config/filter/versionDetail.vue",

        meta: {
          title: "版本详情",
          permission: "filter",
        },
      },
      {
        name: "logAccept",
        path: "logAccept",
        hidden: false,
        component: "views/sime/config/logAccept.vue",

        meta: {
          title: "日志接收管理",
        },
      },
      {
        name: "Management",
        path: "management/:id",
        hidden: true,
        component: "views/sime/config/logAccept/index.vue",

        meta: {
          title: "新增监听器",
          permission: "logAccept",
        },
      },
      {
        name: "editListener",
        path: "editListener/:id",
        hidden: true,
        component: "views/sime/config/logAccept/index.vue",

        meta: {
          title: "编辑监听器",
          permission: "logAccept",
        },
      },
      {
        name: "monitoring",
        path: "monitoring/:id/:name",
        hidden: true,
        component: "views/sime/config/logAccept/monitoring.vue",

        meta: {
          title: "状态监控",
          permission: "logAccept",
        },
      },
      {
        name: "simeDict",
        path: "dict",
        hidden: false,
        component: "views/system/dict/index.vue",

        meta: {
          title: "字典管理",
        },
      },
    ],
  },
  {
    name: "simeSearchLayout",
    path: "/simeSearch",
    component: "views/layout/index.vue",
    meta: {
      title: "查询分析",
      icon: "icon-nav_cxfx",
    },
    children: [
      {
        name: "simeSearch",
        path: "analysis",
        hidden: false,
        component: "views/sime/simeSearch/index.vue",

        meta: {
          title: "查询分析",
          icon: "icon-nav_cxfx",
        },
      },
    ],
  },
  {
    name: "eventLayout",
    path: "/event",
    component: "views/layout/index.vue",
    meta: {
      title: "事件",

      icon: "icon-nav_wxjcfx",
    },
    children: [
      {
        name: "eventOverview",
        path: "eventOverview",
        // hidden: true,
        component: "views/event/overview.vue",
        meta: {
          title: "总览",

          icon: "icon-nav_wxjcfx",
        },
      },
      {
        name: "AlarmDeleted",
        path: "alarmDeleted",
        // hidden: true,
        component: "views/event/alarmDeleted.vue",
        meta: {
          title: "已删除告警",

          icon: "icon-nav_wxjcfx",
        },
      },
      {
        name: "alarmManagement",
        path: "alarmManagement",
        // hidden: true,
        component: "views/event/alarmManagement.vue",
        meta: {
          title: "告警管理",

          icon: "icon-nav_wxjcfx",
        },
      },
      {
        name: "AlarmDetails",
        path: "alarmDetails/:id",
        hidden: true,
        component: "views/event/alarmDetails.vue",
        meta: {
          title: "告警详情",

          icon: "icon-nav_wxjcfx",
          permission: "alarmManagement",
        },
      },
      {
        name: "threatEvent",
        path: "threatEvent",
        // hidden: true,
        component: "views/event/threatEvent/index.vue",
        meta: {
          title: "威胁事件",

          icon: "icon-nav_wxjcfx",
        },
      },
      {
        name: "eventDetail",
        path: "eventDetail/:id",
        hidden: true,
        component: "views/event/threatEvent/detail.vue",
        meta: {
          title: "事件详情",

          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
        },
      },
      {
        name: "AddEvent",
        path: "addEvent",
        hidden: true,
        component: "views/event/threatEvent/components/addEvent.vue",
        meta: {
          title: "添加事件",

          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
        },
      },
      {
        name: "MergeEvent",
        path: "mergeEvent/:id",
        hidden: true,
        component: "views/event/threatEvent/mergeEvent.vue",
        meta: {
          title: "事件合并",

          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
        },
      },
      {
        name: "ChangeEvent",
        path: "changeEvent/:id",
        hidden: true,
        component: "views/event/threatEvent/changeEvent.vue",
        meta: {
          title: "事件升级",

          icon: "icon-nav_wxjcfx",
          permission: "threatEvent",
        },
      },
    ],
  },
];
